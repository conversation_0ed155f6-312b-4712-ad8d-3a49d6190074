{"cells": [{"cell_type": "code", "execution_count": 9, "id": "2d0c2bdf", "metadata": {}, "outputs": [], "source": ["models = [\n", "    '/data/home/<USER>/repos/anjesbench/share/models/gemma-3-1b-it',\n", "    '/data/home/<USER>/repos/anesbench/share/models/gemma-3-4b-it',\n", "    '/data/home/<USER>/repos/anesbench/share/models/gemma-3-12b-it',\n", "    '/data/home/<USER>/repos/anesbench/share/models/gemma-3-27b-it',\n", "    # '/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-0.5B-Instruct',\n", "    '/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-1.5B-Instruct',\n", "    '/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-3B-Instruct',\n", "    '/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-7B-Instruct',\n", "    '/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-14B-Instruct',\n", "    '/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-32B-Instruct',\n", "    '/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-72B-Instruct',\n", "    '/data/home/<USER>/repos/anesbench/share/models/Qwen3-0.6B',\n", "    '/data/home/<USER>/repos/anesbench/share/models/Qwen3-1.7B',\n", "    '/data/home/<USER>/repos/anesbench/share/models/Qwen3-4B',\n", "    '/data/home/<USER>/repos/anesbench/share/models/Qwen3-8B',\n", "    '/data/home/<USER>/repos/anesbench/share/models/Qwen3-14B',\n", "    '/data/home/<USER>/repos/anesbench/share/models/Qwen3-30B-A3B',\n", "    '/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Llama-8B',\n", "    # '/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Llama-70B',\n", "    '/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Qwen-1.5B',\n", "    '/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Qwen-7B',\n", "    '/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Qwen-14B',\n", "    '/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Qwen-32B',\n", "]"]}, {"cell_type": "code", "execution_count": 21, "id": "8d56262a", "metadata": {}, "outputs": [], "source": ["import json\n", "import re\n", "from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction\n", "from nltk.tokenize import word_tokenize\n", "from rouge_score import rouge_scorer\n", "import numpy as np\n", "\n", "with open('/data/home/<USER>/repos/anesbench/share/datasets/anesthesia/benchmark/fine/rebuttal/openqa/system2_en_2trans_refined.json', 'r') as f:\n", "    data = json.load(f)\n", "\n", "pp = ''\n", "for model in models:\n", "    with open(f'/data/home/<USER>/repos/anesbench/share/datasets/anesthesia/benchmark/fine/rebuttal/openqa/open-source/data/{model.split(\"/\")[-1]}.json', 'r') as f:\n", "        model_data = json.load(f)\n", "\n", "    bleu_scores = []\n", "    f1_scores = []\n", "    rouge_scores = {'rouge1': [], 'rouge2': [], 'rougeL': []}\n", "    \n", "    scorer = rouge_scorer.RougeScorer(['rouge1', 'rouge2', 'rougeL'], use_stemmer=True)\n", "    \n", "    def calculate_f1(ref, hyp):\n", "        ref_tokens = set(word_tokenize(ref.lower()))\n", "        hyp_tokens = set(word_tokenize(hyp.lower()))\n", "        \n", "        common = ref_tokens & hyp_tokens\n", "        if not common:\n", "            return 0\n", "        precision = len(common) / len(hyp_tokens)\n", "        recall = len(common) / len(ref_tokens)\n", "        return 2 * precision * recall / (precision + recall)\n", "    for res_item, data_item in zip(model_data, data):\n", "        if res_item['id'] == data_item['id']:\n", "            predict = res_item['response']\n", "            try:\n", "                predict_content = re.findall(r'<answer>(.*?)</answer>', predict, re.DOTALL)[0].strip()\n", "            except:\n", "                # print(predict)\n", "                # print('-'*100)\n", "                continue\n", "            # 计算BLEU分数\n", "            reference = word_tokenize(answer.lower())\n", "            candidate = word_tokenize(predict_content.lower())\n", "            smooth = SmoothingFunction().method1\n", "            bleu = sentence_bleu([reference], candidate, smoothing_function=smooth)\n", "            bleu_scores.append(bleu)\n", "\n", "            # 计算F1分数\n", "            f1 = calculate_f1(answer, predict_content)\n", "            f1_scores.append(f1)\n", "\n", "            # 计算ROUGE分数\n", "            scores = scorer.score(answer, predict_content)\n", "            rouge_scores['rouge1'].append(scores['rouge1'].fmeasure)\n", "            rouge_scores['rouge2'].append(scores['rouge2'].fmeasure) \n", "            rouge_scores['rougeL'].append(scores['rougeL'].fmeasure)\n", "        else:\n", "            raise ValueError(f\"id {res_item['id']} not found in data\")\n", "    # 计算均值和方差\n", "    # print(\"| 模型 | BLEU | F1 | ROUGE1 | ROUGE2 | ROUGE L |\")\n", "    # # print(\"| --- | --- | --- | --- | --- | --- |\")\n", "    # print(bleu_scores)\n", "    # print(f1_scores)\n", "    # print(rouge_scores)\n", "    pp += f\"| {model.split('/')[-1]} | {np.mean(bleu_scores):.4f} ({np.var(bleu_scores):.4f}) | {np.mean(f1_scores):.4f} ({np.var(f1_scores):.4f}) | {np.mean(rouge_scores['rouge1']):.4f} ({np.var(rouge_scores['rouge1']):.4f}) | {np.mean(rouge_scores['rouge2']):.4f} ({np.var(rouge_scores['rouge2']):.4f}) | {np.mean(rouge_scores['rougeL']):.4f} ({np.var(rouge_scores['rougeL']):.4f}) |\\n\"\n", "\n", "    "]}, {"cell_type": "code", "execution_count": 17, "id": "308fd020", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["| gemma-3-1b-it | 0.0001 (0.0000) | 0.0021 (0.0001) | 0.0066 (0.0002) | 0.0000 (0.0000) | 0.0058 (0.0001) |\n", "| gemma-3-4b-it | 0.0002 (0.0000) | 0.0017 (0.0001) | 0.0059 (0.0005) | 0.0000 (0.0000) | 0.0049 (0.0002) |\n", "| gemma-3-12b-it | 0.0011 (0.0001) | 0.0052 (0.0011) | 0.0082 (0.0007) | 0.0000 (0.0000) | 0.0082 (0.0007) |\n", "| gemma-3-27b-it | 0.0003 (0.0000) | 0.0033 (0.0002) | 0.0096 (0.0007) | 0.0000 (0.0000) | 0.0096 (0.0007) |\n", "| Qwen2.5-1.5B-Instruct | 0.0000 (0.0000) | 0.0006 (0.0000) | 0.0098 (0.0003) | 0.0000 (0.0000) | 0.0098 (0.0003) |\n", "| Qwen2.5-3B-Instruct | 0.0001 (0.0000) | 0.0017 (0.0001) | 0.0062 (0.0007) | 0.0000 (0.0000) | 0.0055 (0.0006) |\n", "| Qwen2.5-7B-Instruct | 0.0003 (0.0000) | 0.0029 (0.0002) | 0.0082 (0.0005) | 0.0000 (0.0000) | 0.0074 (0.0004) |\n", "| Qwen2.5-14B-Instruct | 0.0004 (0.0000) | 0.0030 (0.0003) | 0.0047 (0.0004) | 0.0000 (0.0000) | 0.0047 (0.0004) |\n", "| Qwen2.5-32B-Instruct | 0.0002 (0.0000) | 0.0020 (0.0001) | 0.0123 (0.0030) | 0.0000 (0.0000) | 0.0123 (0.0030) |\n", "| Qwen2.5-72B-Instruct | 0.0000 (0.0000) | 0.0000 (0.0000) | 0.0120 (0.0027) | 0.0007 (0.0000) | 0.0120 (0.0027) |\n", "| Qwen3-0.6B | 0.0006 (0.0000) | 0.0037 (0.0005) | 0.0048 (0.0005) | 0.0000 (0.0000) | 0.0048 (0.0005) |\n", "| Qwen3-1.7B | 0.0000 (0.0000) | 0.0000 (0.0000) | 0.0077 (0.0004) | 0.0000 (0.0000) | 0.0077 (0.0004) |\n", "| Qwen3-4B | 0.0011 (0.0001) | 0.0062 (0.0016) | 0.0198 (0.0031) | 0.0024 (0.0003) | 0.0198 (0.0031) |\n", "| Qwen3-8B | 0.0001 (0.0000) | 0.0016 (0.0001) | 0.0104 (0.0006) | 0.0000 (0.0000) | 0.0096 (0.0006) |\n", "| Qwen3-14B | 0.0009 (0.0000) | 0.0068 (0.0008) | 0.0193 (0.0025) | 0.0020 (0.0003) | 0.0183 (0.0019) |\n", "| Qwen3-30B-A3B | 0.0000 (0.0000) | 0.0003 (0.0000) | 0.0142 (0.0004) | 0.0000 (0.0000) | 0.0142 (0.0004) |\n", "| DeepSeek-R1-Distill-Llama-8B | 0.0001 (0.0000) | 0.0010 (0.0001) | 0.0058 (0.0007) | 0.0000 (0.0000) | 0.0058 (0.0007) |\n", "| DeepSeek-R1-Distill-Qwen-1.5B | 0.0001 (0.0000) | 0.0017 (0.0001) | 0.0029 (0.0001) | 0.0000 (0.0000) | 0.0023 (0.0001) |\n", "| DeepSeek-R1-Distill-<PERSON>wen-7B | 0.0001 (0.0000) | 0.0021 (0.0001) | 0.0073 (0.0004) | 0.0000 (0.0000) | 0.0071 (0.0004) |\n", "| DeepSeek-R1-Di<PERSON>ill-<PERSON>wen-14B | 0.0000 (0.0000) | 0.0000 (0.0000) | 0.0052 (0.0003) | 0.0000 (0.0000) | 0.0052 (0.0003) |\n", "| DeepSeek-R1-Distill-<PERSON>wen-32B | 0.0003 (0.0000) | 0.0020 (0.0002) | 0.0064 (0.0005) | 0.0000 (0.0000) | 0.0064 (0.0005) |\n", "\n"]}], "source": ["print(pp)"]}, {"cell_type": "code", "execution_count": null, "id": "d16bda67", "metadata": {}, "outputs": [], "source": ["| gemma-3-1b-it | 0.0001 (0.0000) | 0.0021 (0.0001) | 0.0066 (0.0002) | 0.0000 (0.0000) | 0.0058 (0.0001) |\n", "| gemma-3-4b-it | 0.0002 (0.0000) | 0.0017 (0.0001) | 0.0059 (0.0005) | 0.0000 (0.0000) | 0.0049 (0.0002) |\n", "| gemma-3-12b-it | 0.0011 (0.0001) | 0.0052 (0.0011) | 0.0082 (0.0007) | 0.0000 (0.0000) | 0.0082 (0.0007) |\n", "| gemma-3-27b-it | 0.0003 (0.0000) | 0.0033 (0.0002) | 0.0096 (0.0007) | 0.0000 (0.0000) | 0.0096 (0.0007) |\n", "| Qwen2.5-1.5B-Instruct | 0.0000 (0.0000) | 0.0006 (0.0000) | 0.0098 (0.0003) | 0.0000 (0.0000) | 0.0098 (0.0003) |\n", "| Qwen2.5-3B-Instruct | 0.0001 (0.0000) | 0.0017 (0.0001) | 0.0062 (0.0007) | 0.0000 (0.0000) | 0.0055 (0.0006) |\n", "| Qwen2.5-7B-Instruct | 0.0003 (0.0000) | 0.0029 (0.0002) | 0.0082 (0.0005) | 0.0000 (0.0000) | 0.0074 (0.0004) |\n", "| Qwen2.5-14B-Instruct | 0.0004 (0.0000) | 0.0030 (0.0003) | 0.0047 (0.0004) | 0.0000 (0.0000) | 0.0047 (0.0004) |\n", "| Qwen2.5-32B-Instruct | 0.0002 (0.0000) | 0.0020 (0.0001) | 0.0123 (0.0030) | 0.0000 (0.0000) | 0.0123 (0.0030) |\n", "| Qwen2.5-72B-Instruct | 0.0000 (0.0000) | 0.0000 (0.0000) | 0.0120 (0.0027) | 0.0007 (0.0000) | 0.0120 (0.0027) |\n", "| Qwen3-0.6B | 0.0006 (0.0000) | 0.0037 (0.0005) | 0.0048 (0.0005) | 0.0000 (0.0000) | 0.0048 (0.0005) |\n", "| Qwen3-1.7B | 0.0000 (0.0000) | 0.0000 (0.0000) | 0.0077 (0.0004) | 0.0000 (0.0000) | 0.0077 (0.0004) |\n", "| Qwen3-4B | 0.0011 (0.0001) | 0.0062 (0.0016) | 0.0198 (0.0031) | 0.0024 (0.0003) | 0.0198 (0.0031) |\n", "| Qwen3-8B | 0.0001 (0.0000) | 0.0016 (0.0001) | 0.0104 (0.0006) | 0.0000 (0.0000) | 0.0096 (0.0006) |\n", "| Qwen3-14B | 0.0009 (0.0000) | 0.0068 (0.0008) | 0.0193 (0.0025) | 0.0020 (0.0003) | 0.0183 (0.0019) |\n", "| Qwen3-30B-A3B | 0.0000 (0.0000) | 0.0003 (0.0000) | 0.0142 (0.0004) | 0.0000 (0.0000) | 0.0142 (0.0004) |\n", "| DeepSeek-R1-Distill-Llama-8B | 0.0001 (0.0000) | 0.0010 (0.0001) | 0.0058 (0.0007) | 0.0000 (0.0000) | 0.0058 (0.0007) |\n", "| DeepSeek-R1-Distill-Qwen-1.5B | 0.0001 (0.0000) | 0.0017 (0.0001) | 0.0029 (0.0001) | 0.0000 (0.0000) | 0.0023 (0.0001) |\n", "| DeepSeek-R1-Distill-<PERSON>wen-7B | 0.0001 (0.0000) | 0.0021 (0.0001) | 0.0073 (0.0004) | 0.0000 (0.0000) | 0.0071 (0.0004) |\n", "| DeepSeek-R1-Di<PERSON>ill-<PERSON>wen-14B | 0.0000 (0.0000) | 0.0000 (0.0000) | 0.0052 (0.0003) | 0.0000 (0.0000) | 0.0052 (0.0003) |\n", "| DeepSeek-R1-Distill-<PERSON>wen-32B | 0.0003 (0.0000) | 0.0020 (0.0002) | 0.0064 (0.0005) | 0.0000 (0.0000) | 0.0064 (0.0005) |\n"]}], "metadata": {"kernelspec": {"display_name": "genai", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}
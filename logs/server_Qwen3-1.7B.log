[2025-07-28 00:57:37] server_args=ServerArgs(model_path='/data/home/<USER>/repos/anesbench/share/models/Qwen3-1.7B', tokenizer_path='/data/home/<USER>/repos/anesbench/share/models/Qwen3-1.7B', tokenizer_mode='auto', skip_tokenizer_init=False, load_format='auto', model_loader_extra_config='{}', trust_remote_code=False, context_length=32768, is_embedding=False, enable_multimodal=None, revision=None, model_impl='auto', host='127.0.0.1', port=8004, skip_server_warmup=False, warmups=None, nccl_port=None, dtype='auto', quantization=None, quantization_param_path=None, kv_cache_dtype='auto', mem_fraction_static=0.85, max_running_requests=None, max_total_tokens=None, chunked_prefill_size=8192, max_prefill_tokens=16384, schedule_policy='fcfs', schedule_conservativeness=1.0, cpu_offload_gb=0, page_size=1, hybrid_kvcache_ratio=None, swa_full_tokens_ratio=0.8, disable_hybrid_swa_memory=False, device='cuda', tp_size=4, pp_size=1, max_micro_batch_size=None, stream_interval=1, stream_output=False, random_seed=168874892, constrained_json_whitespace_pattern=None, watchdog_timeout=300, dist_timeout=None, download_dir=None, base_gpu_id=0, gpu_id_step=1, sleep_on_idle=False, log_level='info', log_level_http=None, log_requests=False, log_requests_level=0, crash_dump_folder=None, show_time_cost=True, enable_metrics=True, enable_metrics_for_all_schedulers=False, bucket_time_to_first_token=None, bucket_inter_token_latency=None, bucket_e2e_request_latency=None, collect_tokens_histogram=False, decode_log_interval=40, enable_request_time_stats_logging=False, kv_events_config=None, api_key=None, served_model_name='/data/home/<USER>/repos/anesbench/share/models/Qwen3-1.7B', chat_template=None, completion_template=None, file_storage_path='sglang_storage', enable_cache_report=False, reasoning_parser=None, tool_call_parser=None, dp_size=2, load_balance_method='round_robin', dist_init_addr=None, nnodes=1, node_rank=0, json_model_override_args='{}', preferred_sampling_params=None, enable_lora=None, max_lora_rank=None, lora_target_modules=None, lora_paths=None, max_loras_per_batch=8, lora_backend='triton', attention_backend=None, sampling_backend='flashinfer', grammar_backend='xgrammar', mm_attention_backend=None, speculative_algorithm=None, speculative_draft_model_path=None, speculative_num_steps=None, speculative_eagle_topk=None, speculative_num_draft_tokens=None, speculative_accept_threshold_single=1.0, speculative_accept_threshold_acc=1.0, speculative_token_map=None, ep_size=1, enable_ep_moe=False, enable_deepep_moe=False, enable_flashinfer_moe=False, enable_flashinfer_allreduce_fusion=False, deepep_mode='auto', ep_num_redundant_experts=0, ep_dispatch_algorithm='static', init_expert_location='trivial', enable_eplb=False, eplb_algorithm='auto', eplb_rebalance_num_iterations=1000, eplb_rebalance_layers_per_chunk=None, expert_distribution_recorder_mode=None, expert_distribution_recorder_buffer_size=1000, enable_expert_distribution_metrics=False, deepep_config=None, moe_dense_tp_size=None, enable_hierarchical_cache=False, hicache_ratio=2.0, hicache_size=0, hicache_write_policy='write_through_selective', hicache_io_backend='', hicache_storage_backend=None, enable_double_sparsity=False, ds_channel_config_path=None, ds_heavy_channel_num=32, ds_heavy_token_num=256, ds_heavy_channel_type='qk', ds_sparse_decode_threshold=4096, disable_radix_cache=False, cuda_graph_max_bs=None, cuda_graph_bs=None, disable_cuda_graph=False, disable_cuda_graph_padding=False, enable_profile_cuda_graph=False, enable_nccl_nvls=False, enable_tokenizer_batch_encode=False, disable_outlines_disk_cache=False, disable_custom_all_reduce=False, enable_mscclpp=False, disable_overlap_schedule=False, enable_mixed_chunk=False, enable_dp_attention=False, enable_dp_lm_head=False, enable_two_batch_overlap=False, enable_torch_compile=False, torch_compile_max_bs=32, torchao_config='', enable_nan_detection=False, enable_p2p_check=False, triton_attention_reduce_in_fp32=False, triton_attention_num_kv_splits=8, num_continuous_decode_steps=1, delete_ckpt_after_loading=False, enable_memory_saver=False, allow_auto_truncate=False, enable_custom_logit_processor=False, flashinfer_mla_disable_ragged=False, disable_shared_experts_fusion=False, disable_chunked_prefix_cache=False, disable_fast_image_processor=False, enable_return_hidden_states=False, enable_triton_kernel_moe=False, debug_tensor_dump_output_folder=None, debug_tensor_dump_input_file=None, debug_tensor_dump_inject=False, debug_tensor_dump_prefill_only=False, disaggregation_mode='null', disaggregation_transfer_backend='mooncake', disaggregation_bootstrap_port=8998, disaggregation_decode_tp=None, disaggregation_decode_dp=None, disaggregation_prefill_pp=1, disaggregation_ib_device=None, num_reserved_decode_tokens=512, pdlb_url=None, custom_weight_loader=[], weight_loader_disable_mmap=False, enable_pdmux=False, sm_group_num=3)
[2025-07-28 00:57:42] Launch DP0 starting at GPU #0.
[2025-07-28 00:57:42] Launch DP1 starting at GPU #4.
[2025-07-28 00:57:50 DP0 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 00:57:50 DP0 TP0] Init torch distributed begin.
[2025-07-28 00:57:50 DP1 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 00:57:50 DP1 TP0] Init torch distributed begin.
[2025-07-28 00:57:52 DP0 TP0] sglang is using nccl==2.26.2
[2025-07-28 00:57:52 DP1 TP0] sglang is using nccl==2.26.2
[2025-07-28 00:57:53 DP0 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 00:57:53 DP1 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 00:57:53 DP0 TP0] Load weight begin. avail mem=78.21 GB

Loading safetensors checkpoint shards:   0% Completed | 0/2 [00:00<?, ?it/s]
[2025-07-28 00:57:53 DP1 TP0] Load weight begin. avail mem=78.21 GB

Loading safetensors checkpoint shards:   0% Completed | 0/2 [00:00<?, ?it/s]

Loading safetensors checkpoint shards: 100% Completed | 2/2 [00:01<00:00,  1.80it/s]

Loading safetensors checkpoint shards: 100% Completed | 2/2 [00:01<00:00,  1.61it/s]

Loading safetensors checkpoint shards: 100% Completed | 2/2 [00:01<00:00,  1.80it/s]


Loading safetensors checkpoint shards: 100% Completed | 2/2 [00:01<00:00,  1.61it/s]

[2025-07-28 00:57:55 DP0 TP0] Load weight end. type=Qwen3ForCausalLM, dtype=torch.bfloat16, avail mem=77.38 GB, mem usage=0.82 GB.
[2025-07-28 00:57:55 DP1 TP0] Load weight end. type=Qwen3ForCausalLM, dtype=torch.bfloat16, avail mem=77.38 GB, mem usage=0.82 GB.
[2025-07-28 00:57:55 DP1 TP0] KV Cache is allocated. #tokens: 2455575, K size: 32.79 GB, V size: 32.79 GB
[2025-07-28 00:57:55 DP1 TP3] KV Cache is allocated. #tokens: 2455575, K size: 32.79 GB, V size: 32.79 GB
[2025-07-28 00:57:55 DP0 TP0] KV Cache is allocated. #tokens: 2455575, K size: 32.79 GB, V size: 32.79 GB
[2025-07-28 00:57:55 DP0 TP2] KV Cache is allocated. #tokens: 2455575, K size: 32.79 GB, V size: 32.79 GB
[2025-07-28 00:57:55 DP1 TP2] KV Cache is allocated. #tokens: 2455575, K size: 32.79 GB, V size: 32.79 GB
[2025-07-28 00:57:55 DP0 TP1] KV Cache is allocated. #tokens: 2455575, K size: 32.79 GB, V size: 32.79 GB
[2025-07-28 00:57:55 DP1 TP1] KV Cache is allocated. #tokens: 2455575, K size: 32.79 GB, V size: 32.79 GB
[2025-07-28 00:57:55 DP1 TP0] Memory pool end. avail mem=11.24 GB
[2025-07-28 00:57:55 DP0 TP0] Memory pool end. avail mem=11.24 GB
[2025-07-28 00:57:55 DP0 TP3] KV Cache is allocated. #tokens: 2455575, K size: 32.79 GB, V size: 32.79 GB
[2025-07-28 00:57:55 DP0 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.62 GB
[2025-07-28 00:57:55 DP1 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.62 GB
[2025-07-28 00:57:55 DP0 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.60 GB):   0%|          | 0/23 [00:00<?, ?it/s][2025-07-28 00:57:55 DP1 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.60 GB):   0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.60 GB):   4%|▍         | 1/23 [00:01<00:25,  1.14s/it]
Capturing batches (bs=152 avail_mem=10.36 GB):   4%|▍         | 1/23 [00:01<00:25,  1.14s/it]
Capturing batches (bs=160 avail_mem=10.60 GB):   4%|▍         | 1/23 [00:01<00:24,  1.12s/it]
Capturing batches (bs=152 avail_mem=10.36 GB):   4%|▍         | 1/23 [00:01<00:24,  1.12s/it]
Capturing batches (bs=152 avail_mem=10.36 GB):   9%|▊         | 2/23 [00:01<00:14,  1.49it/s]
Capturing batches (bs=144 avail_mem=10.25 GB):   9%|▊         | 2/23 [00:01<00:14,  1.49it/s]
Capturing batches (bs=152 avail_mem=10.36 GB):   9%|▊         | 2/23 [00:01<00:13,  1.52it/s]
Capturing batches (bs=144 avail_mem=10.25 GB):   9%|▊         | 2/23 [00:01<00:13,  1.52it/s]
Capturing batches (bs=144 avail_mem=10.25 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.71it/s]
Capturing batches (bs=136 avail_mem=10.16 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.71it/s]
Capturing batches (bs=144 avail_mem=10.25 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.74it/s]
Capturing batches (bs=136 avail_mem=10.16 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.74it/s]
Capturing batches (bs=136 avail_mem=10.16 GB):  17%|█▋        | 4/23 [00:02<00:08,  2.16it/s]
Capturing batches (bs=128 avail_mem=10.06 GB):  17%|█▋        | 4/23 [00:02<00:08,  2.16it/s]
Capturing batches (bs=136 avail_mem=10.16 GB):  17%|█▋        | 4/23 [00:02<00:08,  2.18it/s]
Capturing batches (bs=128 avail_mem=10.06 GB):  17%|█▋        | 4/23 [00:02<00:08,  2.18it/s]
Capturing batches (bs=128 avail_mem=10.06 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.54it/s]
Capturing batches (bs=120 avail_mem=9.98 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.54it/s] 
Capturing batches (bs=128 avail_mem=10.06 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.54it/s]
Capturing batches (bs=120 avail_mem=9.98 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.54it/s] 
Capturing batches (bs=120 avail_mem=9.98 GB):  26%|██▌       | 6/23 [00:02<00:05,  2.84it/s]
Capturing batches (bs=112 avail_mem=9.89 GB):  26%|██▌       | 6/23 [00:02<00:05,  2.84it/s]
Capturing batches (bs=120 avail_mem=9.98 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.82it/s]
Capturing batches (bs=112 avail_mem=9.89 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.82it/s]
Capturing batches (bs=112 avail_mem=9.89 GB):  30%|███       | 7/23 [00:03<00:05,  3.06it/s]
Capturing batches (bs=104 avail_mem=9.82 GB):  30%|███       | 7/23 [00:03<00:05,  3.06it/s]
Capturing batches (bs=112 avail_mem=9.89 GB):  30%|███       | 7/23 [00:03<00:05,  3.03it/s]
Capturing batches (bs=104 avail_mem=9.82 GB):  30%|███       | 7/23 [00:03<00:05,  3.03it/s]
Capturing batches (bs=104 avail_mem=9.82 GB):  35%|███▍      | 8/23 [00:03<00:04,  3.23it/s]
Capturing batches (bs=96 avail_mem=9.73 GB):  35%|███▍      | 8/23 [00:03<00:04,  3.23it/s] 
Capturing batches (bs=104 avail_mem=9.82 GB):  35%|███▍      | 8/23 [00:03<00:04,  3.18it/s]
Capturing batches (bs=96 avail_mem=9.73 GB):  35%|███▍      | 8/23 [00:03<00:04,  3.18it/s] 
Capturing batches (bs=96 avail_mem=9.73 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.36it/s]
Capturing batches (bs=88 avail_mem=9.67 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.36it/s]
Capturing batches (bs=96 avail_mem=9.73 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.30it/s]
Capturing batches (bs=88 avail_mem=9.67 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.30it/s]
Capturing batches (bs=88 avail_mem=9.67 GB):  43%|████▎     | 10/23 [00:03<00:03,  3.44it/s]
Capturing batches (bs=80 avail_mem=9.60 GB):  43%|████▎     | 10/23 [00:03<00:03,  3.44it/s]
Capturing batches (bs=88 avail_mem=9.67 GB):  43%|████▎     | 10/23 [00:03<00:03,  3.39it/s]
Capturing batches (bs=80 avail_mem=9.60 GB):  43%|████▎     | 10/23 [00:03<00:03,  3.39it/s]
Capturing batches (bs=80 avail_mem=9.60 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.51it/s]
Capturing batches (bs=72 avail_mem=9.59 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.51it/s]
Capturing batches (bs=80 avail_mem=9.60 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.46it/s]
Capturing batches (bs=72 avail_mem=9.59 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.46it/s]
Capturing batches (bs=72 avail_mem=9.59 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.56it/s]
Capturing batches (bs=64 avail_mem=9.52 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.56it/s]
Capturing batches (bs=72 avail_mem=9.59 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.50it/s]
Capturing batches (bs=64 avail_mem=9.52 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.50it/s]
Capturing batches (bs=64 avail_mem=9.52 GB):  57%|█████▋    | 13/23 [00:04<00:02,  3.51it/s]
Capturing batches (bs=56 avail_mem=9.48 GB):  57%|█████▋    | 13/23 [00:04<00:02,  3.51it/s]
Capturing batches (bs=64 avail_mem=9.52 GB):  57%|█████▋    | 13/23 [00:04<00:02,  3.45it/s]
Capturing batches (bs=56 avail_mem=9.48 GB):  57%|█████▋    | 13/23 [00:04<00:02,  3.45it/s]
Capturing batches (bs=56 avail_mem=9.48 GB):  61%|██████    | 14/23 [00:04<00:02,  3.56it/s]
Capturing batches (bs=48 avail_mem=9.42 GB):  61%|██████    | 14/23 [00:04<00:02,  3.56it/s]
Capturing batches (bs=56 avail_mem=9.48 GB):  61%|██████    | 14/23 [00:05<00:02,  3.50it/s]
Capturing batches (bs=48 avail_mem=9.42 GB):  61%|██████    | 14/23 [00:05<00:02,  3.50it/s]
Capturing batches (bs=48 avail_mem=9.42 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.60it/s]
Capturing batches (bs=40 avail_mem=9.39 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.60it/s]
Capturing batches (bs=48 avail_mem=9.42 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.54it/s]
Capturing batches (bs=40 avail_mem=9.39 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.54it/s]
Capturing batches (bs=40 avail_mem=9.39 GB):  70%|██████▉   | 16/23 [00:05<00:01,  3.63it/s]
Capturing batches (bs=32 avail_mem=9.37 GB):  70%|██████▉   | 16/23 [00:05<00:01,  3.63it/s]
Capturing batches (bs=40 avail_mem=9.39 GB):  70%|██████▉   | 16/23 [00:05<00:01,  3.56it/s]
Capturing batches (bs=32 avail_mem=9.37 GB):  70%|██████▉   | 16/23 [00:05<00:01,  3.56it/s]
Capturing batches (bs=32 avail_mem=9.37 GB):  74%|███████▍  | 17/23 [00:05<00:01,  3.65it/s]
Capturing batches (bs=24 avail_mem=9.34 GB):  74%|███████▍  | 17/23 [00:05<00:01,  3.65it/s]
Capturing batches (bs=32 avail_mem=9.37 GB):  74%|███████▍  | 17/23 [00:05<00:01,  3.58it/s]
Capturing batches (bs=24 avail_mem=9.34 GB):  74%|███████▍  | 17/23 [00:05<00:01,  3.58it/s]
Capturing batches (bs=24 avail_mem=9.34 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.66it/s]
Capturing batches (bs=16 avail_mem=9.32 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.66it/s]
Capturing batches (bs=24 avail_mem=9.34 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.59it/s]
Capturing batches (bs=16 avail_mem=9.32 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.59it/s]
Capturing batches (bs=16 avail_mem=9.32 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.67it/s]
Capturing batches (bs=8 avail_mem=9.29 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.67it/s] 
Capturing batches (bs=16 avail_mem=9.32 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.60it/s]
Capturing batches (bs=8 avail_mem=9.29 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.60it/s] 
Capturing batches (bs=8 avail_mem=9.29 GB):  87%|████████▋ | 20/23 [00:06<00:00,  3.69it/s]
Capturing batches (bs=4 avail_mem=9.27 GB):  87%|████████▋ | 20/23 [00:06<00:00,  3.69it/s]
Capturing batches (bs=8 avail_mem=9.29 GB):  87%|████████▋ | 20/23 [00:06<00:00,  3.62it/s]
Capturing batches (bs=4 avail_mem=9.27 GB):  87%|████████▋ | 20/23 [00:06<00:00,  3.62it/s]
Capturing batches (bs=4 avail_mem=9.27 GB):  91%|█████████▏| 21/23 [00:06<00:00,  3.69it/s]
Capturing batches (bs=2 avail_mem=9.26 GB):  91%|█████████▏| 21/23 [00:06<00:00,  3.69it/s]
Capturing batches (bs=4 avail_mem=9.27 GB):  91%|█████████▏| 21/23 [00:06<00:00,  3.62it/s]
Capturing batches (bs=2 avail_mem=9.26 GB):  91%|█████████▏| 21/23 [00:06<00:00,  3.62it/s]
Capturing batches (bs=2 avail_mem=9.26 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.70it/s]
Capturing batches (bs=1 avail_mem=9.24 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.70it/s]
Capturing batches (bs=2 avail_mem=9.26 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.63it/s]
Capturing batches (bs=1 avail_mem=9.24 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.63it/s]
Capturing batches (bs=1 avail_mem=9.24 GB): 100%|██████████| 23/23 [00:07<00:00,  3.69it/s]
Capturing batches (bs=1 avail_mem=9.24 GB): 100%|██████████| 23/23 [00:07<00:00,  3.10it/s]
[2025-07-28 00:58:03 DP0 TP0] Registering 1311 cuda graph addresses
[2025-07-28 00:58:03 DP0 TP3] Registering 1311 cuda graph addresses
[2025-07-28 00:58:03 DP0 TP2] Registering 1311 cuda graph addresses
[2025-07-28 00:58:03 DP0 TP1] Registering 1311 cuda graph addresses
[2025-07-28 00:58:03 DP0 TP0] Capture cuda graph end. Time elapsed: 7.61 s. mem usage=1.38 GB. avail mem=9.23 GB.
[2025-07-28 00:58:03 DP1 TP3] Registering 1311 cuda graph addresses
[2025-07-28 00:58:03 DP1 TP1] Registering 1311 cuda graph addresses

Capturing batches (bs=1 avail_mem=9.24 GB): 100%|██████████| 23/23 [00:07<00:00,  3.61it/s]
Capturing batches (bs=1 avail_mem=9.24 GB): 100%|██████████| 23/23 [00:07<00:00,  3.07it/s]
[2025-07-28 00:58:03 DP1 TP0] Registering 1311 cuda graph addresses
[2025-07-28 00:58:03 DP1 TP2] Registering 1311 cuda graph addresses
[2025-07-28 00:58:03 DP1 TP0] Capture cuda graph end. Time elapsed: 7.73 s. mem usage=1.38 GB. avail mem=9.23 GB.
[2025-07-28 00:58:03 DP0 TP0] max_total_num_tokens=2455575, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=9.23 GB
[2025-07-28 00:58:03 DP1 TP0] max_total_num_tokens=2455575, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=9.23 GB
[2025-07-28 00:58:04] INFO:     Started server process [1801780]
[2025-07-28 00:58:04] INFO:     Waiting for application startup.
[2025-07-28 00:58:04] INFO:     Application startup complete.
[2025-07-28 00:58:04] INFO:     Uvicorn running on http://127.0.0.1:8004 (Press CTRL+C to quit)
[2025-07-28 00:58:05] INFO:     127.0.0.1:48686 - "GET /get_model_info HTTP/1.1" 200 OK
[2025-07-28 00:58:05 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:58:05 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:58:05] INFO:     127.0.0.1:48700 - "GET /health HTTP/1.1" 200 OK
[2025-07-28 00:58:06 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 194, #cached-token: 0, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:58:06 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 235, #cached-token: 0, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:58:06] INFO:     127.0.0.1:48690 - "POST /generate HTTP/1.1" 200 OK
[2025-07-28 00:58:06] The server is fired up and ready to roll!
[2025-07-28 00:58:06 DP0 TP0] Decode batch. #running-req: 1, #token: 234, token usage: 0.00, cuda graph: True, gen throughput (token/s): 15.23, #queue-req: 0, 
[2025-07-28 00:58:06 DP1 TP0] Decode batch. #running-req: 1, #token: 275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 15.54, #queue-req: 0, 
[2025-07-28 00:58:06 DP0 TP0] Decode batch. #running-req: 1, #token: 274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.13, #queue-req: 0, 
[2025-07-28 00:58:06 DP1 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.35, #queue-req: 0, 
[2025-07-28 00:58:06 DP0 TP0] Decode batch. #running-req: 1, #token: 314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.84, #queue-req: 0, 
[2025-07-28 00:58:06 DP1 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 288.89, #queue-req: 0, 
[2025-07-28 00:58:06 DP0 TP0] Decode batch. #running-req: 1, #token: 354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.92, #queue-req: 0, 
[2025-07-28 00:58:06 DP1 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 278.40, #queue-req: 0, 
[2025-07-28 00:58:07 DP0 TP0] Decode batch. #running-req: 1, #token: 394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.36, #queue-req: 0, 
[2025-07-28 00:58:07 DP1 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 281.05, #queue-req: 0, 
[2025-07-28 00:58:07 DP0 TP0] Decode batch. #running-req: 1, #token: 434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.03, #queue-req: 0, 
[2025-07-28 00:58:07 DP1 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.17, #queue-req: 0, 
[2025-07-28 00:58:07 DP0 TP0] Decode batch. #running-req: 1, #token: 474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.85, #queue-req: 0, 
[2025-07-28 00:58:07 DP1 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.71, #queue-req: 0, 
[2025-07-28 00:58:07 DP0 TP0] Decode batch. #running-req: 1, #token: 514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.43, #queue-req: 0, 
[2025-07-28 00:58:07 DP1 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.62, #queue-req: 0, 
[2025-07-28 00:58:07 DP0 TP0] Decode batch. #running-req: 1, #token: 554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.60, #queue-req: 0, 
[2025-07-28 00:58:07 DP1 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.85, #queue-req: 0, 
[2025-07-28 00:58:07 DP0 TP0] Decode batch. #running-req: 1, #token: 594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 280.38, #queue-req: 0, 
[2025-07-28 00:58:07 DP1 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.58, #queue-req: 0, 
[2025-07-28 00:58:07 DP0 TP0] Decode batch. #running-req: 1, #token: 634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.48, #queue-req: 0, 
[2025-07-28 00:58:07 DP1 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.86, #queue-req: 0, 
[2025-07-28 00:58:08 DP0 TP0] Decode batch. #running-req: 1, #token: 674, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.79, #queue-req: 0, 
[2025-07-28 00:58:08 DP1 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.02, #queue-req: 0, 
[2025-07-28 00:58:08 DP0 TP0] Decode batch. #running-req: 1, #token: 714, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.90, #queue-req: 0, 
[2025-07-28 00:58:08 DP1 TP0] Decode batch. #running-req: 1, #token: 755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.48, #queue-req: 0, 
[2025-07-28 00:58:08 DP0 TP0] Decode batch. #running-req: 1, #token: 754, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.83, #queue-req: 0, 
[2025-07-28 00:58:08 DP1 TP0] Decode batch. #running-req: 1, #token: 795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.35, #queue-req: 0, 
[2025-07-28 00:58:08 DP0 TP0] Decode batch. #running-req: 1, #token: 794, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.75, #queue-req: 0, 
[2025-07-28 00:58:08 DP1 TP0] Decode batch. #running-req: 1, #token: 835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.39, #queue-req: 0, 
[2025-07-28 00:58:08 DP0 TP0] Decode batch. #running-req: 1, #token: 834, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.63, #queue-req: 0, 
[2025-07-28 00:58:08 DP1 TP0] Decode batch. #running-req: 1, #token: 875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.36, #queue-req: 0, 
[2025-07-28 00:58:08 DP0 TP0] Decode batch. #running-req: 1, #token: 874, token usage: 0.00, cuda graph: True, gen throughput (token/s): 283.42, #queue-req: 0, 
[2025-07-28 00:58:08 DP1 TP0] Decode batch. #running-req: 1, #token: 915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.05, #queue-req: 0, 
[2025-07-28 00:58:08 DP0 TP0] Decode batch. #running-req: 1, #token: 914, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.22, #queue-req: 0, 
[2025-07-28 00:58:08 DP1 TP0] Decode batch. #running-req: 1, #token: 955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.31, #queue-req: 0, 
[2025-07-28 00:58:08] INFO:     127.0.0.1:48714 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:58:09 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:58:09 DP1 TP0] Decode batch. #running-req: 1, #token: 995, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.27, #queue-req: 0, 
[2025-07-28 00:58:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.45, #queue-req: 0, 
[2025-07-28 00:58:09 DP0 TP0] Decode batch. #running-req: 1, #token: 174, token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.33, #queue-req: 0, 
[2025-07-28 00:58:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1075, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.02, #queue-req: 0, 
[2025-07-28 00:58:09 DP0 TP0] Decode batch. #running-req: 1, #token: 214, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.53, #queue-req: 0, 
[2025-07-28 00:58:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.98, #queue-req: 0, 
[2025-07-28 00:58:09 DP0 TP0] Decode batch. #running-req: 1, #token: 254, token usage: 0.00, cuda graph: True, gen throughput (token/s): 288.41, #queue-req: 0, 
[2025-07-28 00:58:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1155, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.98, #queue-req: 0, 
[2025-07-28 00:58:09 DP0 TP0] Decode batch. #running-req: 1, #token: 294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 282.49, #queue-req: 0, 
[2025-07-28 00:58:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.55, #queue-req: 0, 
[2025-07-28 00:58:09 DP0 TP0] Decode batch. #running-req: 1, #token: 334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 282.29, #queue-req: 0, 
[2025-07-28 00:58:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.21, #queue-req: 0, 
[2025-07-28 00:58:09 DP0 TP0] Decode batch. #running-req: 1, #token: 374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 272.59, #queue-req: 0, 
[2025-07-28 00:58:09] INFO:     127.0.0.1:48720 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:58:09 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 207, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:58:10 DP0 TP0] Decode batch. #running-req: 1, #token: 414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.56, #queue-req: 0, 
[2025-07-28 00:58:10 DP0 TP0] Decode batch. #running-req: 1, #token: 454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.63, #queue-req: 0, 
[2025-07-28 00:58:10 DP1 TP0] Decode batch. #running-req: 1, #token: 299, token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.60, #queue-req: 0, 
[2025-07-28 00:58:10 DP0 TP0] Decode batch. #running-req: 1, #token: 494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.57, #queue-req: 0, 
[2025-07-28 00:58:10 DP1 TP0] Decode batch. #running-req: 1, #token: 339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.82, #queue-req: 0, 
[2025-07-28 00:58:10 DP1 TP0] Decode batch. #running-req: 1, #token: 379, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.99, #queue-req: 0, 
[2025-07-28 00:58:10 DP0 TP0] Decode batch. #running-req: 1, #token: 534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.50, #queue-req: 0, 
[2025-07-28 00:58:10 DP1 TP0] Decode batch. #running-req: 1, #token: 419, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.62, #queue-req: 0, 
[2025-07-28 00:58:10 DP0 TP0] Decode batch. #running-req: 1, #token: 574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 280.47, #queue-req: 0, 
[2025-07-28 00:58:10 DP1 TP0] Decode batch. #running-req: 1, #token: 459, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.42, #queue-req: 0, 
[2025-07-28 00:58:10 DP0 TP0] Decode batch. #running-req: 1, #token: 614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.22, #queue-req: 0, 
[2025-07-28 00:58:10 DP1 TP0] Decode batch. #running-req: 1, #token: 499, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.27, #queue-req: 0, 
[2025-07-28 00:58:10 DP0 TP0] Decode batch. #running-req: 1, #token: 654, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.71, #queue-req: 0, 
[2025-07-28 00:58:11 DP1 TP0] Decode batch. #running-req: 1, #token: 539, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.96, #queue-req: 0, 
[2025-07-28 00:58:11 DP0 TP0] Decode batch. #running-req: 1, #token: 694, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.04, #queue-req: 0, 
[2025-07-28 00:58:11 DP1 TP0] Decode batch. #running-req: 1, #token: 579, token usage: 0.00, cuda graph: True, gen throughput (token/s): 260.44, #queue-req: 0, 
[2025-07-28 00:58:11 DP0 TP0] Decode batch. #running-req: 1, #token: 734, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.93, #queue-req: 0, 
[2025-07-28 00:58:11 DP1 TP0] Decode batch. #running-req: 1, #token: 619, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.69, #queue-req: 0, 
[2025-07-28 00:58:11 DP0 TP0] Decode batch. #running-req: 1, #token: 774, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.82, #queue-req: 0, 
[2025-07-28 00:58:11 DP1 TP0] Decode batch. #running-req: 1, #token: 659, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.13, #queue-req: 0, 
[2025-07-28 00:58:11 DP0 TP0] Decode batch. #running-req: 1, #token: 814, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.43, #queue-req: 0, 
[2025-07-28 00:58:11 DP1 TP0] Decode batch. #running-req: 1, #token: 699, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.39, #queue-req: 0, 
[2025-07-28 00:58:11 DP0 TP0] Decode batch. #running-req: 1, #token: 854, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.05, #queue-req: 0, 
[2025-07-28 00:58:11 DP1 TP0] Decode batch. #running-req: 1, #token: 739, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.98, #queue-req: 0, 
[2025-07-28 00:58:11 DP0 TP0] Decode batch. #running-req: 1, #token: 894, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.35, #queue-req: 0, 
[2025-07-28 00:58:11 DP1 TP0] Decode batch. #running-req: 1, #token: 779, token usage: 0.00, cuda graph: True, gen throughput (token/s): 281.64, #queue-req: 0, 
[2025-07-28 00:58:11 DP0 TP0] Decode batch. #running-req: 1, #token: 934, token usage: 0.00, cuda graph: True, gen throughput (token/s): 220.92, #queue-req: 0, 
[2025-07-28 00:58:11] INFO:     127.0.0.1:48728 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:58:11 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:58:11 DP1 TP0] Decode batch. #running-req: 1, #token: 819, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.54, #queue-req: 0, 
[2025-07-28 00:58:12 DP1 TP0] Decode batch. #running-req: 1, #token: 859, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.42, #queue-req: 0, 
[2025-07-28 00:58:12 DP0 TP0] Decode batch. #running-req: 1, #token: 244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 189.65, #queue-req: 0, 
[2025-07-28 00:58:12 DP0 TP0] Decode batch. #running-req: 1, #token: 284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 324.41, #queue-req: 0, 
[2025-07-28 00:58:12 DP1 TP0] Decode batch. #running-req: 1, #token: 899, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.61, #queue-req: 0, 
[2025-07-28 00:58:12 DP0 TP0] Decode batch. #running-req: 1, #token: 324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.83, #queue-req: 0, 
[2025-07-28 00:58:12 DP1 TP0] Decode batch. #running-req: 1, #token: 939, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.90, #queue-req: 0, 
[2025-07-28 00:58:12 DP1 TP0] Decode batch. #running-req: 1, #token: 979, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.21, #queue-req: 0, 
[2025-07-28 00:58:12 DP0 TP0] Decode batch. #running-req: 1, #token: 364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.68, #queue-req: 0, 
[2025-07-28 00:58:12 DP1 TP0] Decode batch. #running-req: 1, #token: 1019, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.83, #queue-req: 0, 
[2025-07-28 00:58:12 DP0 TP0] Decode batch. #running-req: 1, #token: 404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.89, #queue-req: 0, 
[2025-07-28 00:58:12 DP1 TP0] Decode batch. #running-req: 1, #token: 1059, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.85, #queue-req: 0, 
[2025-07-28 00:58:12 DP0 TP0] Decode batch. #running-req: 1, #token: 444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.71, #queue-req: 0, 
[2025-07-28 00:58:12 DP1 TP0] Decode batch. #running-req: 1, #token: 1099, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.87, #queue-req: 0, 
[2025-07-28 00:58:12 DP0 TP0] Decode batch. #running-req: 1, #token: 484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.46, #queue-req: 0, 
[2025-07-28 00:58:13 DP1 TP0] Decode batch. #running-req: 1, #token: 1139, token usage: 0.00, cuda graph: True, gen throughput (token/s): 278.73, #queue-req: 0, 
[2025-07-28 00:58:13 DP0 TP0] Decode batch. #running-req: 1, #token: 524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 282.62, #queue-req: 0, 
[2025-07-28 00:58:13 DP1 TP0] Decode batch. #running-req: 1, #token: 1179, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.40, #queue-req: 0, 
[2025-07-28 00:58:13 DP0 TP0] Decode batch. #running-req: 1, #token: 564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.22, #queue-req: 0, 
[2025-07-28 00:58:13 DP0 TP0] Decode batch. #running-req: 1, #token: 604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.47, #queue-req: 0, 
[2025-07-28 00:58:13 DP1 TP0] Decode batch. #running-req: 1, #token: 1219, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.72, #queue-req: 0, 
[2025-07-28 00:58:13 DP0 TP0] Decode batch. #running-req: 1, #token: 644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.15, #queue-req: 0, 
[2025-07-28 00:58:13 DP1 TP0] Decode batch. #running-req: 1, #token: 1259, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.34, #queue-req: 0, 
[2025-07-28 00:58:13 DP1 TP0] Decode batch. #running-req: 1, #token: 1299, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.58, #queue-req: 0, 
[2025-07-28 00:58:13 DP0 TP0] Decode batch. #running-req: 1, #token: 684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.39, #queue-req: 0, 
[2025-07-28 00:58:13 DP1 TP0] Decode batch. #running-req: 1, #token: 1339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.29, #queue-req: 0, 
[2025-07-28 00:58:13 DP0 TP0] Decode batch. #running-req: 1, #token: 724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.49, #queue-req: 0, 
[2025-07-28 00:58:13 DP1 TP0] Decode batch. #running-req: 1, #token: 1379, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.25, #queue-req: 0, 
[2025-07-28 00:58:13 DP0 TP0] Decode batch. #running-req: 1, #token: 764, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.69, #queue-req: 0, 
[2025-07-28 00:58:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1419, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.39, #queue-req: 0, 
[2025-07-28 00:58:14 DP0 TP0] Decode batch. #running-req: 1, #token: 804, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.87, #queue-req: 0, 
[2025-07-28 00:58:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1459, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.21, #queue-req: 0, 
[2025-07-28 00:58:14 DP0 TP0] Decode batch. #running-req: 1, #token: 844, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.64, #queue-req: 0, 
[2025-07-28 00:58:14 DP0 TP0] Decode batch. #running-req: 1, #token: 884, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.92, #queue-req: 0, 
[2025-07-28 00:58:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1499, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.23, #queue-req: 0, 
[2025-07-28 00:58:14 DP0 TP0] Decode batch. #running-req: 1, #token: 924, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.08, #queue-req: 0, 
[2025-07-28 00:58:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1539, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.19, #queue-req: 0, 
[2025-07-28 00:58:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1579, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.46, #queue-req: 0, 
[2025-07-28 00:58:14 DP0 TP0] Decode batch. #running-req: 1, #token: 964, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.77, #queue-req: 0, 
[2025-07-28 00:58:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1619, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.92, #queue-req: 0, 
[2025-07-28 00:58:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1004, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.96, #queue-req: 0, 
[2025-07-28 00:58:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1659, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.83, #queue-req: 0, 
[2025-07-28 00:58:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1044, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.85, #queue-req: 0, 
[2025-07-28 00:58:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1084, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.48, #queue-req: 0, 
[2025-07-28 00:58:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1699, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.54, #queue-req: 0, 
[2025-07-28 00:58:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1124, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.87, #queue-req: 0, 
[2025-07-28 00:58:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1739, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.85, #queue-req: 0, 
[2025-07-28 00:58:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1164, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.60, #queue-req: 0, 
[2025-07-28 00:58:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1779, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.28, #queue-req: 0, 
[2025-07-28 00:58:15] INFO:     127.0.0.1:48732 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:58:15 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 133, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:58:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1204, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.05, #queue-req: 0, 
[2025-07-28 00:58:15 DP1 TP0] Decode batch. #running-req: 1, #token: 228, token usage: 0.00, cuda graph: True, gen throughput (token/s): 184.70, #queue-req: 0, 
[2025-07-28 00:58:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.32, #queue-req: 0, 
[2025-07-28 00:58:15 DP1 TP0] Decode batch. #running-req: 1, #token: 268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 337.85, #queue-req: 0, 
[2025-07-28 00:58:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.37, #queue-req: 0, 
[2025-07-28 00:58:15 DP1 TP0] Decode batch. #running-req: 1, #token: 308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 325.87, #queue-req: 0, 
[2025-07-28 00:58:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.05, #queue-req: 0, 
[2025-07-28 00:58:15 DP1 TP0] Decode batch. #running-req: 1, #token: 348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 334.51, #queue-req: 0, 
[2025-07-28 00:58:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.04, #queue-req: 0, 
[2025-07-28 00:58:15 DP1 TP0] Decode batch. #running-req: 1, #token: 388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 329.88, #queue-req: 0, 
[2025-07-28 00:58:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.07, #queue-req: 0, 
[2025-07-28 00:58:16 DP1 TP0] Decode batch. #running-req: 1, #token: 428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 320.09, #queue-req: 0, 
[2025-07-28 00:58:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.88, #queue-req: 0, 
[2025-07-28 00:58:16 DP1 TP0] Decode batch. #running-req: 1, #token: 468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.59, #queue-req: 0, 
[2025-07-28 00:58:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.36, #queue-req: 0, 
[2025-07-28 00:58:16 DP1 TP0] Decode batch. #running-req: 1, #token: 508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.42, #queue-req: 0, 
[2025-07-28 00:58:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.17, #queue-req: 0, 
[2025-07-28 00:58:16 DP1 TP0] Decode batch. #running-req: 1, #token: 548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.43, #queue-req: 0, 
[2025-07-28 00:58:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 279.42, #queue-req: 0, 
[2025-07-28 00:58:16 DP1 TP0] Decode batch. #running-req: 1, #token: 588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.45, #queue-req: 0, 
[2025-07-28 00:58:16 DP1 TP0] Decode batch. #running-req: 1, #token: 628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.98, #queue-req: 0, 
[2025-07-28 00:58:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.49, #queue-req: 0, 
[2025-07-28 00:58:16 DP1 TP0] Decode batch. #running-req: 1, #token: 668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.05, #queue-req: 0, 
[2025-07-28 00:58:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.32, #queue-req: 0, 
[2025-07-28 00:58:16 DP1 TP0] Decode batch. #running-req: 1, #token: 708, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.24, #queue-req: 0, 
[2025-07-28 00:58:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.03, #queue-req: 0, 
[2025-07-28 00:58:17 DP1 TP0] Decode batch. #running-req: 1, #token: 748, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.24, #queue-req: 0, 
[2025-07-28 00:58:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 278.02, #queue-req: 0, 
[2025-07-28 00:58:17 DP1 TP0] Decode batch. #running-req: 1, #token: 788, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.59, #queue-req: 0, 
[2025-07-28 00:58:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1764, token usage: 0.00, cuda graph: True, gen throughput (token/s): 283.95, #queue-req: 0, 
[2025-07-28 00:58:17 DP1 TP0] Decode batch. #running-req: 1, #token: 828, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.29, #queue-req: 0, 
[2025-07-28 00:58:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1804, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.63, #queue-req: 0, 
[2025-07-28 00:58:17 DP1 TP0] Decode batch. #running-req: 1, #token: 868, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.66, #queue-req: 0, 
[2025-07-28 00:58:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1844, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.21, #queue-req: 0, 
[2025-07-28 00:58:17 DP1 TP0] Decode batch. #running-req: 1, #token: 908, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.97, #queue-req: 0, 
[2025-07-28 00:58:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1884, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.07, #queue-req: 0, 
[2025-07-28 00:58:17 DP1 TP0] Decode batch. #running-req: 1, #token: 948, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.46, #queue-req: 0, 
[2025-07-28 00:58:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1924, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.62, #queue-req: 0, 
[2025-07-28 00:58:17 DP1 TP0] Decode batch. #running-req: 1, #token: 988, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.37, #queue-req: 0, 
[2025-07-28 00:58:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1964, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.74, #queue-req: 0, 
[2025-07-28 00:58:18 DP1 TP0] Decode batch. #running-req: 1, #token: 1028, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.57, #queue-req: 0, 
[2025-07-28 00:58:18 DP0 TP0] Decode batch. #running-req: 1, #token: 2004, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.51, #queue-req: 0, 
[2025-07-28 00:58:18 DP1 TP0] Decode batch. #running-req: 1, #token: 1068, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.15, #queue-req: 0, 
[2025-07-28 00:58:18 DP0 TP0] Decode batch. #running-req: 1, #token: 2044, token usage: 0.00, cuda graph: True, gen throughput (token/s): 259.09, #queue-req: 0, 
[2025-07-28 00:58:18 DP1 TP0] Decode batch. #running-req: 1, #token: 1108, token usage: 0.00, cuda graph: True, gen throughput (token/s): 282.29, #queue-req: 0, 
[2025-07-28 00:58:18 DP0 TP0] Decode batch. #running-req: 1, #token: 2084, token usage: 0.00, cuda graph: True, gen throughput (token/s): 273.04, #queue-req: 0, 
[2025-07-28 00:58:18 DP1 TP0] Decode batch. #running-req: 1, #token: 1148, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.95, #queue-req: 0, 
[2025-07-28 00:58:18 DP0 TP0] Decode batch. #running-req: 1, #token: 2124, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.68, #queue-req: 0, 
[2025-07-28 00:58:18 DP1 TP0] Decode batch. #running-req: 1, #token: 1188, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.08, #queue-req: 0, 
[2025-07-28 00:58:18 DP0 TP0] Decode batch. #running-req: 1, #token: 2164, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.70, #queue-req: 0, 
[2025-07-28 00:58:18 DP1 TP0] Decode batch. #running-req: 1, #token: 1228, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.50, #queue-req: 0, 
[2025-07-28 00:58:18 DP0 TP0] Decode batch. #running-req: 1, #token: 2204, token usage: 0.00, cuda graph: True, gen throughput (token/s): 282.06, #queue-req: 0, 
[2025-07-28 00:58:18 DP1 TP0] Decode batch. #running-req: 1, #token: 1268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.25, #queue-req: 0, 
[2025-07-28 00:58:18 DP0 TP0] Decode batch. #running-req: 1, #token: 2244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.71, #queue-req: 0, 
[2025-07-28 00:58:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.88, #queue-req: 0, 
[2025-07-28 00:58:19] INFO:     127.0.0.1:44762 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:58:19 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 186, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:58:19 DP0 TP0] Decode batch. #running-req: 1, #token: 2469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.69, #queue-req: 0, 
[2025-07-28 00:58:19 DP0 TP0] Decode batch. #running-req: 2, #token: 2551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 554.95, #queue-req: 0, 
[2025-07-28 00:58:19 DP0 TP0] Decode batch. #running-req: 2, #token: 2631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 534.12, #queue-req: 0, 
[2025-07-28 00:58:19 DP0 TP0] Decode batch. #running-req: 2, #token: 2711, token usage: 0.00, cuda graph: True, gen throughput (token/s): 538.84, #queue-req: 0, 
[2025-07-28 00:58:19 DP0 TP0] Decode batch. #running-req: 2, #token: 2791, token usage: 0.00, cuda graph: True, gen throughput (token/s): 554.18, #queue-req: 0, 
[2025-07-28 00:58:19] INFO:     127.0.0.1:48742 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:58:19 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 173, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:58:19 DP0 TP0] Decode batch. #running-req: 1, #token: 472, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.12, #queue-req: 0, 
[2025-07-28 00:58:19 DP1 TP0] Decode batch. #running-req: 1, #token: 282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 43.08, #queue-req: 0, 
[2025-07-28 00:58:20 DP0 TP0] Decode batch. #running-req: 1, #token: 512, token usage: 0.00, cuda graph: True, gen throughput (token/s): 273.07, #queue-req: 0, 
[2025-07-28 00:58:20 DP1 TP0] Decode batch. #running-req: 1, #token: 322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.94, #queue-req: 0, 
[2025-07-28 00:58:20 DP0 TP0] Decode batch. #running-req: 1, #token: 552, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.95, #queue-req: 0, 
[2025-07-28 00:58:20 DP1 TP0] Decode batch. #running-req: 1, #token: 362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 226.68, #queue-req: 0, 
[2025-07-28 00:58:20 DP0 TP0] Decode batch. #running-req: 1, #token: 592, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.80, #queue-req: 0, 
[2025-07-28 00:58:20 DP1 TP0] Decode batch. #running-req: 1, #token: 402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 282.32, #queue-req: 0, 
[2025-07-28 00:58:20 DP0 TP0] Decode batch. #running-req: 1, #token: 632, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.05, #queue-req: 0, 
[2025-07-28 00:58:20 DP1 TP0] Decode batch. #running-req: 1, #token: 442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.87, #queue-req: 0, 
[2025-07-28 00:58:20 DP0 TP0] Decode batch. #running-req: 1, #token: 672, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.83, #queue-req: 0, 
[2025-07-28 00:58:20 DP1 TP0] Decode batch. #running-req: 1, #token: 482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.15, #queue-req: 0, 
[2025-07-28 00:58:20 DP0 TP0] Decode batch. #running-req: 1, #token: 712, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.68, #queue-req: 0, 
[2025-07-28 00:58:20 DP1 TP0] Decode batch. #running-req: 1, #token: 522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.70, #queue-req: 0, 
[2025-07-28 00:58:20 DP0 TP0] Decode batch. #running-req: 1, #token: 752, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.20, #queue-req: 0, 
[2025-07-28 00:58:20 DP1 TP0] Decode batch. #running-req: 1, #token: 562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.69, #queue-req: 0, 
[2025-07-28 00:58:20 DP0 TP0] Decode batch. #running-req: 1, #token: 792, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.17, #queue-req: 0, 
[2025-07-28 00:58:21 DP1 TP0] Decode batch. #running-req: 1, #token: 602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.69, #queue-req: 0, 
[2025-07-28 00:58:21 DP0 TP0] Decode batch. #running-req: 1, #token: 832, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.92, #queue-req: 0, 
[2025-07-28 00:58:21 DP1 TP0] Decode batch. #running-req: 1, #token: 642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.77, #queue-req: 0, 
[2025-07-28 00:58:21 DP0 TP0] Decode batch. #running-req: 1, #token: 872, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.70, #queue-req: 0, 
[2025-07-28 00:58:21 DP1 TP0] Decode batch. #running-req: 1, #token: 682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.55, #queue-req: 0, 
[2025-07-28 00:58:21 DP0 TP0] Decode batch. #running-req: 1, #token: 912, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.03, #queue-req: 0, 
[2025-07-28 00:58:21 DP1 TP0] Decode batch. #running-req: 1, #token: 722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.97, #queue-req: 0, 
[2025-07-28 00:58:21 DP0 TP0] Decode batch. #running-req: 1, #token: 952, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.43, #queue-req: 0, 
[2025-07-28 00:58:21 DP1 TP0] Decode batch. #running-req: 1, #token: 762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.28, #queue-req: 0, 
[2025-07-28 00:58:21 DP0 TP0] Decode batch. #running-req: 1, #token: 992, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.95, #queue-req: 0, 
[2025-07-28 00:58:21] INFO:     127.0.0.1:44772 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:58:21 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 226, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:58:21 DP1 TP0] Decode batch. #running-req: 1, #token: 802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.01, #queue-req: 0, 
[2025-07-28 00:58:21 DP1 TP0] Decode batch. #running-req: 1, #token: 842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.06, #queue-req: 0, 
[2025-07-28 00:58:21 DP0 TP0] Decode batch. #running-req: 1, #token: 346, token usage: 0.00, cuda graph: True, gen throughput (token/s): 187.67, #queue-req: 0, 
[2025-07-28 00:58:22 DP0 TP0] Decode batch. #running-req: 1, #token: 386, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.41, #queue-req: 0, 
[2025-07-28 00:58:22 DP1 TP0] Decode batch. #running-req: 1, #token: 882, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.62, #queue-req: 0, 
[2025-07-28 00:58:22 DP0 TP0] Decode batch. #running-req: 1, #token: 426, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.34, #queue-req: 0, 
[2025-07-28 00:58:22 DP1 TP0] Decode batch. #running-req: 1, #token: 922, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.62, #queue-req: 0, 
[2025-07-28 00:58:22 DP0 TP0] Decode batch. #running-req: 1, #token: 466, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.22, #queue-req: 0, 
[2025-07-28 00:58:22 DP1 TP0] Decode batch. #running-req: 1, #token: 962, token usage: 0.00, cuda graph: True, gen throughput (token/s): 252.93, #queue-req: 0, 
[2025-07-28 00:58:22 DP0 TP0] Decode batch. #running-req: 1, #token: 506, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.85, #queue-req: 0, 
[2025-07-28 00:58:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1002, token usage: 0.00, cuda graph: True, gen throughput (token/s): 276.35, #queue-req: 0, 
[2025-07-28 00:58:22 DP0 TP0] Decode batch. #running-req: 1, #token: 546, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.41, #queue-req: 0, 
[2025-07-28 00:58:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1042, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.11, #queue-req: 0, 
[2025-07-28 00:58:22 DP0 TP0] Decode batch. #running-req: 1, #token: 586, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.88, #queue-req: 0, 
[2025-07-28 00:58:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1082, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.77, #queue-req: 0, 
[2025-07-28 00:58:22 DP0 TP0] Decode batch. #running-req: 1, #token: 626, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.70, #queue-req: 0, 
[2025-07-28 00:58:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1122, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.91, #queue-req: 0, 
[2025-07-28 00:58:22 DP0 TP0] Decode batch. #running-req: 1, #token: 666, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.47, #queue-req: 0, 
[2025-07-28 00:58:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1162, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.80, #queue-req: 0, 
[2025-07-28 00:58:23 DP0 TP0] Decode batch. #running-req: 1, #token: 706, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.32, #queue-req: 0, 
[2025-07-28 00:58:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1202, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.95, #queue-req: 0, 
[2025-07-28 00:58:23 DP0 TP0] Decode batch. #running-req: 1, #token: 746, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.76, #queue-req: 0, 
[2025-07-28 00:58:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 282.88, #queue-req: 0, 
[2025-07-28 00:58:23 DP0 TP0] Decode batch. #running-req: 1, #token: 786, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.83, #queue-req: 0, 
[2025-07-28 00:58:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.45, #queue-req: 0, 
[2025-07-28 00:58:23 DP0 TP0] Decode batch. #running-req: 1, #token: 826, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.64, #queue-req: 0, 
[2025-07-28 00:58:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 281.72, #queue-req: 0, 
[2025-07-28 00:58:23 DP0 TP0] Decode batch. #running-req: 1, #token: 866, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.96, #queue-req: 0, 
[2025-07-28 00:58:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 276.08, #queue-req: 0, 
[2025-07-28 00:58:23 DP0 TP0] Decode batch. #running-req: 1, #token: 906, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.39, #queue-req: 0, 
[2025-07-28 00:58:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 278.90, #queue-req: 0, 
[2025-07-28 00:58:23 DP0 TP0] Decode batch. #running-req: 1, #token: 946, token usage: 0.00, cuda graph: True, gen throughput (token/s): 271.50, #queue-req: 0, 
[2025-07-28 00:58:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 271.70, #queue-req: 0, 
[2025-07-28 00:58:24 DP0 TP0] Decode batch. #running-req: 1, #token: 986, token usage: 0.00, cuda graph: True, gen throughput (token/s): 278.56, #queue-req: 0, 
[2025-07-28 00:58:24 DP1 TP0] Decode batch. #running-req: 1, #token: 1482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 278.30, #queue-req: 0, 
[2025-07-28 00:58:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1026, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.78, #queue-req: 0, 
[2025-07-28 00:58:24 DP1 TP0] Decode batch. #running-req: 1, #token: 1522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 280.21, #queue-req: 0, 
[2025-07-28 00:58:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1066, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.38, #queue-req: 0, 
[2025-07-28 00:58:24 DP1 TP0] Decode batch. #running-req: 1, #token: 1562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.00, #queue-req: 0, 
[2025-07-28 00:58:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1106, token usage: 0.00, cuda graph: True, gen throughput (token/s): 280.36, #queue-req: 0, 
[2025-07-28 00:58:24 DP1 TP0] Decode batch. #running-req: 1, #token: 1602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.38, #queue-req: 0, 
[2025-07-28 00:58:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1146, token usage: 0.00, cuda graph: True, gen throughput (token/s): 282.27, #queue-req: 0, 
[2025-07-28 00:58:24 DP1 TP0] Decode batch. #running-req: 1, #token: 1642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.33, #queue-req: 0, 
[2025-07-28 00:58:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1186, token usage: 0.00, cuda graph: True, gen throughput (token/s): 279.56, #queue-req: 0, 
[2025-07-28 00:58:24 DP1 TP0] Decode batch. #running-req: 1, #token: 1682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.97, #queue-req: 0, 
[2025-07-28 00:58:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1226, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.34, #queue-req: 0, 
[2025-07-28 00:58:24] INFO:     127.0.0.1:44792 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:58:24 DP1 TP0] Decode batch. #running-req: 1, #token: 1722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.79, #queue-req: 0, 
[2025-07-28 00:58:24 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 168, #cached-token: 88, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:58:25 DP1 TP0] Decode batch. #running-req: 2, #token: 1971, token usage: 0.00, cuda graph: True, gen throughput (token/s): 390.10, #queue-req: 0, 
[2025-07-28 00:58:25 DP1 TP0] Decode batch. #running-req: 2, #token: 2051, token usage: 0.00, cuda graph: True, gen throughput (token/s): 587.39, #queue-req: 0, 
[2025-07-28 00:58:25 DP1 TP0] Decode batch. #running-req: 2, #token: 2131, token usage: 0.00, cuda graph: True, gen throughput (token/s): 602.72, #queue-req: 0, 
[2025-07-28 00:58:25 DP1 TP0] Decode batch. #running-req: 2, #token: 2211, token usage: 0.00, cuda graph: True, gen throughput (token/s): 580.22, #queue-req: 0, 
[2025-07-28 00:58:25 DP1 TP0] Decode batch. #running-req: 2, #token: 2291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 595.36, #queue-req: 0, 
[2025-07-28 00:58:25 DP1 TP0] Decode batch. #running-req: 2, #token: 2371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 600.42, #queue-req: 0, 
[2025-07-28 00:58:25 DP1 TP0] Decode batch. #running-req: 2, #token: 2451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 588.03, #queue-req: 0, 
[2025-07-28 00:58:26 DP1 TP0] Decode batch. #running-req: 2, #token: 2531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 586.42, #queue-req: 0, 
[2025-07-28 00:58:26 DP1 TP0] Decode batch. #running-req: 2, #token: 2611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 591.67, #queue-req: 0, 
[2025-07-28 00:58:26 DP1 TP0] Decode batch. #running-req: 2, #token: 2691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 596.53, #queue-req: 0, 
[2025-07-28 00:58:26 DP1 TP0] Decode batch. #running-req: 2, #token: 2771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 593.30, #queue-req: 0, 
[2025-07-28 00:58:26 DP1 TP0] Decode batch. #running-req: 2, #token: 2851, token usage: 0.00, cuda graph: True, gen throughput (token/s): 599.33, #queue-req: 0, 
[2025-07-28 00:58:26 DP1 TP0] Decode batch. #running-req: 2, #token: 2931, token usage: 0.00, cuda graph: True, gen throughput (token/s): 529.24, #queue-req: 0, 
[2025-07-28 00:58:26 DP1 TP0] Decode batch. #running-req: 2, #token: 3011, token usage: 0.00, cuda graph: True, gen throughput (token/s): 579.85, #queue-req: 0, 
[2025-07-28 00:58:27 DP1 TP0] Decode batch. #running-req: 2, #token: 3091, token usage: 0.00, cuda graph: True, gen throughput (token/s): 563.79, #queue-req: 0, 
[2025-07-28 00:58:27 DP1 TP0] Decode batch. #running-req: 2, #token: 3171, token usage: 0.00, cuda graph: True, gen throughput (token/s): 582.80, #queue-req: 0, 
[2025-07-28 00:58:27 DP1 TP0] Decode batch. #running-req: 2, #token: 3251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 578.71, #queue-req: 0, 
[2025-07-28 00:58:27] INFO:     127.0.0.1:54978 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:58:27 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 154, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:58:27 DP1 TP0] Decode batch. #running-req: 1, #token: 2442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.78, #queue-req: 0, 
[2025-07-28 00:58:27 DP0 TP0] Decode batch. #running-req: 1, #token: 274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 15.07, #queue-req: 0, 
[2025-07-28 00:58:27 DP1 TP0] Decode batch. #running-req: 1, #token: 2482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.45, #queue-req: 0, 
[2025-07-28 00:58:27 DP0 TP0] Decode batch. #running-req: 1, #token: 314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.31, #queue-req: 0, 
[2025-07-28 00:58:27 DP1 TP0] Decode batch. #running-req: 1, #token: 2522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.73, #queue-req: 0, 
[2025-07-28 00:58:27 DP0 TP0] Decode batch. #running-req: 1, #token: 354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.00, #queue-req: 0, 
[2025-07-28 00:58:27 DP1 TP0] Decode batch. #running-req: 1, #token: 2562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.69, #queue-req: 0, 
[2025-07-28 00:58:27 DP0 TP0] Decode batch. #running-req: 1, #token: 394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 274.11, #queue-req: 0, 
[2025-07-28 00:58:27 DP1 TP0] Decode batch. #running-req: 1, #token: 2602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.18, #queue-req: 0, 
[2025-07-28 00:58:28 DP0 TP0] Decode batch. #running-req: 1, #token: 434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 261.16, #queue-req: 0, 
[2025-07-28 00:58:28 DP1 TP0] Decode batch. #running-req: 1, #token: 2642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.45, #queue-req: 0, 
[2025-07-28 00:58:28 DP1 TP0] Decode batch. #running-req: 1, #token: 2682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.41, #queue-req: 0, 
[2025-07-28 00:58:28 DP0 TP0] Decode batch. #running-req: 1, #token: 474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.80, #queue-req: 0, 
[2025-07-28 00:58:28 DP1 TP0] Decode batch. #running-req: 1, #token: 2722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.51, #queue-req: 0, 
[2025-07-28 00:58:28 DP0 TP0] Decode batch. #running-req: 1, #token: 514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.04, #queue-req: 0, 
[2025-07-28 00:58:28 DP1 TP0] Decode batch. #running-req: 1, #token: 2762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.83, #queue-req: 0, 
[2025-07-28 00:58:28 DP0 TP0] Decode batch. #running-req: 1, #token: 554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 283.69, #queue-req: 0, 
[2025-07-28 00:58:28 DP1 TP0] Decode batch. #running-req: 1, #token: 2802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.47, #queue-req: 0, 
[2025-07-28 00:58:28 DP0 TP0] Decode batch. #running-req: 1, #token: 594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 278.27, #queue-req: 0, 
[2025-07-28 00:58:28 DP1 TP0] Decode batch. #running-req: 1, #token: 2842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.53, #queue-req: 0, 
[2025-07-28 00:58:28 DP0 TP0] Decode batch. #running-req: 1, #token: 634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 281.15, #queue-req: 0, 
[2025-07-28 00:58:28 DP1 TP0] Decode batch. #running-req: 1, #token: 2882, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.11, #queue-req: 0, 
[2025-07-28 00:58:28 DP0 TP0] Decode batch. #running-req: 1, #token: 674, token usage: 0.00, cuda graph: True, gen throughput (token/s): 281.07, #queue-req: 0, 
[2025-07-28 00:58:29 DP1 TP0] Decode batch. #running-req: 1, #token: 2922, token usage: 0.00, cuda graph: True, gen throughput (token/s): 271.66, #queue-req: 0, 
[2025-07-28 00:58:29 DP0 TP0] Decode batch. #running-req: 1, #token: 714, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.50, #queue-req: 0, 
[2025-07-28 00:58:29 DP1 TP0] Decode batch. #running-req: 1, #token: 2962, token usage: 0.00, cuda graph: True, gen throughput (token/s): 288.81, #queue-req: 0, 
[2025-07-28 00:58:29 DP0 TP0] Decode batch. #running-req: 1, #token: 754, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.63, #queue-req: 0, 
[2025-07-28 00:58:29 DP1 TP0] Decode batch. #running-req: 1, #token: 3002, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.46, #queue-req: 0, 
[2025-07-28 00:58:29 DP0 TP0] Decode batch. #running-req: 1, #token: 794, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.51, #queue-req: 0, 
[2025-07-28 00:58:29 DP1 TP0] Decode batch. #running-req: 1, #token: 3042, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.32, #queue-req: 0, 
[2025-07-28 00:58:29 DP0 TP0] Decode batch. #running-req: 1, #token: 834, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.12, #queue-req: 0, 
[2025-07-28 00:58:29 DP1 TP0] Decode batch. #running-req: 1, #token: 3082, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.44, #queue-req: 0, 
[2025-07-28 00:58:29 DP0 TP0] Decode batch. #running-req: 1, #token: 874, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.38, #queue-req: 0, 
[2025-07-28 00:58:29 DP1 TP0] Decode batch. #running-req: 1, #token: 3122, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.45, #queue-req: 0, 
[2025-07-28 00:58:29 DP0 TP0] Decode batch. #running-req: 1, #token: 914, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.55, #queue-req: 0, 
[2025-07-28 00:58:29 DP1 TP0] Decode batch. #running-req: 1, #token: 3162, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.65, #queue-req: 0, 
[2025-07-28 00:58:29 DP0 TP0] Decode batch. #running-req: 1, #token: 954, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.37, #queue-req: 0, 
[2025-07-28 00:58:29 DP1 TP0] Decode batch. #running-req: 1, #token: 3202, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.20, #queue-req: 0, 
[2025-07-28 00:58:30 DP0 TP0] Decode batch. #running-req: 1, #token: 994, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.26, #queue-req: 0, 
[2025-07-28 00:58:30 DP1 TP0] Decode batch. #running-req: 1, #token: 3242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.84, #queue-req: 0, 
[2025-07-28 00:58:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1034, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.61, #queue-req: 0, 
[2025-07-28 00:58:30 DP1 TP0] Decode batch. #running-req: 1, #token: 3282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.43, #queue-req: 0, 
[2025-07-28 00:58:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1074, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.03, #queue-req: 0, 
[2025-07-28 00:58:30 DP1 TP0] Decode batch. #running-req: 1, #token: 3322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.68, #queue-req: 0, 
[2025-07-28 00:58:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1114, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.62, #queue-req: 0, 
[2025-07-28 00:58:30 DP1 TP0] Decode batch. #running-req: 1, #token: 3362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.61, #queue-req: 0, 
[2025-07-28 00:58:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1154, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.09, #queue-req: 0, 
[2025-07-28 00:58:30 DP1 TP0] Decode batch. #running-req: 1, #token: 3402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.01, #queue-req: 0, 
[2025-07-28 00:58:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1194, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.17, #queue-req: 0, 
[2025-07-28 00:58:30 DP1 TP0] Decode batch. #running-req: 1, #token: 3442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.30, #queue-req: 0, 
[2025-07-28 00:58:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1234, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.32, #queue-req: 0, 
[2025-07-28 00:58:30 DP1 TP0] Decode batch. #running-req: 1, #token: 3482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.16, #queue-req: 0, 
[2025-07-28 00:58:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.83, #queue-req: 0, 
[2025-07-28 00:58:31 DP1 TP0] Decode batch. #running-req: 1, #token: 3522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.11, #queue-req: 0, 
[2025-07-28 00:58:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.23, #queue-req: 0, 
[2025-07-28 00:58:31 DP1 TP0] Decode batch. #running-req: 1, #token: 3562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.32, #queue-req: 0, 
[2025-07-28 00:58:31] INFO:     127.0.0.1:54992 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:58:31 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 141, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:58:31 DP1 TP0] Decode batch. #running-req: 2, #token: 3779, token usage: 0.00, cuda graph: True, gen throughput (token/s): 360.44, #queue-req: 0, 
[2025-07-28 00:58:31 DP1 TP0] Decode batch. #running-req: 2, #token: 3859, token usage: 0.00, cuda graph: True, gen throughput (token/s): 588.80, #queue-req: 0, 
[2025-07-28 00:58:31 DP1 TP0] Decode batch. #running-req: 2, #token: 3939, token usage: 0.00, cuda graph: True, gen throughput (token/s): 600.76, #queue-req: 0, 
[2025-07-28 00:58:31 DP1 TP0] Decode batch. #running-req: 2, #token: 4019, token usage: 0.00, cuda graph: True, gen throughput (token/s): 600.10, #queue-req: 0, 
[2025-07-28 00:58:31 DP1 TP0] Decode batch. #running-req: 2, #token: 4099, token usage: 0.00, cuda graph: True, gen throughput (token/s): 595.37, #queue-req: 0, 
[2025-07-28 00:58:32 DP1 TP0] Decode batch. #running-req: 2, #token: 4179, token usage: 0.00, cuda graph: True, gen throughput (token/s): 530.14, #queue-req: 0, 
[2025-07-28 00:58:32 DP1 TP0] Decode batch. #running-req: 2, #token: 4259, token usage: 0.00, cuda graph: True, gen throughput (token/s): 600.40, #queue-req: 0, 
[2025-07-28 00:58:32 DP1 TP0] Decode batch. #running-req: 2, #token: 4339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 596.64, #queue-req: 0, 
[2025-07-28 00:58:32 DP1 TP0] Decode batch. #running-req: 2, #token: 4419, token usage: 0.00, cuda graph: True, gen throughput (token/s): 596.87, #queue-req: 0, 
[2025-07-28 00:58:32 DP1 TP0] Decode batch. #running-req: 2, #token: 4499, token usage: 0.00, cuda graph: True, gen throughput (token/s): 575.96, #queue-req: 0, 
[2025-07-28 00:58:32 DP1 TP0] Decode batch. #running-req: 2, #token: 4579, token usage: 0.00, cuda graph: True, gen throughput (token/s): 596.80, #queue-req: 0, 
[2025-07-28 00:58:32 DP1 TP0] Decode batch. #running-req: 2, #token: 4659, token usage: 0.00, cuda graph: True, gen throughput (token/s): 587.79, #queue-req: 0, 
[2025-07-28 00:58:32 DP1 TP0] Decode batch. #running-req: 2, #token: 4739, token usage: 0.00, cuda graph: True, gen throughput (token/s): 587.02, #queue-req: 0, 
[2025-07-28 00:58:33 DP1 TP0] Decode batch. #running-req: 2, #token: 4819, token usage: 0.00, cuda graph: True, gen throughput (token/s): 604.83, #queue-req: 0, 
[2025-07-28 00:58:33 DP1 TP0] Decode batch. #running-req: 2, #token: 4899, token usage: 0.00, cuda graph: True, gen throughput (token/s): 593.18, #queue-req: 0, 
[2025-07-28 00:58:33 DP1 TP0] Decode batch. #running-req: 2, #token: 4979, token usage: 0.00, cuda graph: True, gen throughput (token/s): 606.12, #queue-req: 0, 
[2025-07-28 00:58:33 DP1 TP0] Decode batch. #running-req: 2, #token: 5059, token usage: 0.00, cuda graph: True, gen throughput (token/s): 602.44, #queue-req: 0, 
[2025-07-28 00:58:33 DP1 TP0] Decode batch. #running-req: 2, #token: 5139, token usage: 0.00, cuda graph: True, gen throughput (token/s): 601.40, #queue-req: 0, 
[2025-07-28 00:58:33 DP1 TP0] Decode batch. #running-req: 2, #token: 5219, token usage: 0.00, cuda graph: True, gen throughput (token/s): 584.10, #queue-req: 0, 
[2025-07-28 00:58:33 DP1 TP0] Decode batch. #running-req: 2, #token: 5299, token usage: 0.00, cuda graph: True, gen throughput (token/s): 593.39, #queue-req: 0, 
[2025-07-28 00:58:34 DP1 TP0] Decode batch. #running-req: 2, #token: 5379, token usage: 0.00, cuda graph: True, gen throughput (token/s): 598.10, #queue-req: 0, 
[2025-07-28 00:58:34 DP1 TP0] Decode batch. #running-req: 2, #token: 5459, token usage: 0.00, cuda graph: True, gen throughput (token/s): 584.27, #queue-req: 0, 
[2025-07-28 00:58:34 DP1 TP0] Decode batch. #running-req: 2, #token: 5539, token usage: 0.00, cuda graph: True, gen throughput (token/s): 601.09, #queue-req: 0, 
[2025-07-28 00:58:34 DP1 TP0] Decode batch. #running-req: 2, #token: 5619, token usage: 0.00, cuda graph: True, gen throughput (token/s): 584.63, #queue-req: 0, 
[2025-07-28 00:58:34 DP1 TP0] Decode batch. #running-req: 2, #token: 5699, token usage: 0.00, cuda graph: True, gen throughput (token/s): 592.49, #queue-req: 0, 
[2025-07-28 00:58:34 DP1 TP0] Decode batch. #running-req: 2, #token: 5779, token usage: 0.00, cuda graph: True, gen throughput (token/s): 582.18, #queue-req: 0, 
[2025-07-28 00:58:34 DP1 TP0] Decode batch. #running-req: 2, #token: 5859, token usage: 0.00, cuda graph: True, gen throughput (token/s): 593.12, #queue-req: 0, 
[2025-07-28 00:58:35 DP1 TP0] Decode batch. #running-req: 2, #token: 5939, token usage: 0.00, cuda graph: True, gen throughput (token/s): 597.88, #queue-req: 0, 
[2025-07-28 00:58:35 DP1 TP0] Decode batch. #running-req: 2, #token: 6019, token usage: 0.00, cuda graph: True, gen throughput (token/s): 601.90, #queue-req: 0, 
[2025-07-28 00:58:35 DP1 TP0] Decode batch. #running-req: 2, #token: 6099, token usage: 0.00, cuda graph: True, gen throughput (token/s): 604.55, #queue-req: 0, 
[2025-07-28 00:58:35 DP1 TP0] Decode batch. #running-req: 2, #token: 6179, token usage: 0.00, cuda graph: True, gen throughput (token/s): 599.13, #queue-req: 0, 
[2025-07-28 00:58:35 DP1 TP0] Decode batch. #running-req: 2, #token: 6259, token usage: 0.00, cuda graph: True, gen throughput (token/s): 579.38, #queue-req: 0, 
[2025-07-28 00:58:35 DP1 TP0] Decode batch. #running-req: 2, #token: 6339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 595.76, #queue-req: 0, 
[2025-07-28 00:58:35 DP1 TP0] Decode batch. #running-req: 2, #token: 6419, token usage: 0.00, cuda graph: True, gen throughput (token/s): 597.24, #queue-req: 0, 
[2025-07-28 00:58:35 DP1 TP0] Decode batch. #running-req: 2, #token: 6499, token usage: 0.00, cuda graph: True, gen throughput (token/s): 596.74, #queue-req: 0, 
[2025-07-28 00:58:36 DP1 TP0] Decode batch. #running-req: 2, #token: 6579, token usage: 0.00, cuda graph: True, gen throughput (token/s): 595.43, #queue-req: 0, 
[2025-07-28 00:58:36 DP1 TP0] Decode batch. #running-req: 2, #token: 6659, token usage: 0.00, cuda graph: True, gen throughput (token/s): 591.71, #queue-req: 0, 
[2025-07-28 00:58:36 DP1 TP0] Decode batch. #running-req: 2, #token: 6739, token usage: 0.00, cuda graph: True, gen throughput (token/s): 600.63, #queue-req: 0, 
[2025-07-28 00:58:36 DP1 TP0] Decode batch. #running-req: 2, #token: 6819, token usage: 0.00, cuda graph: True, gen throughput (token/s): 596.22, #queue-req: 0, 
[2025-07-28 00:58:36 DP1 TP0] Decode batch. #running-req: 2, #token: 6899, token usage: 0.00, cuda graph: True, gen throughput (token/s): 604.59, #queue-req: 0, 
[2025-07-28 00:58:36] INFO:     127.0.0.1:55004 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:58:36 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 192, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:58:36 DP1 TP0] Decode batch. #running-req: 1, #token: 5202, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.97, #queue-req: 0, 
[2025-07-28 00:58:36 DP1 TP0] Decode batch. #running-req: 1, #token: 5242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.64, #queue-req: 0, 
[2025-07-28 00:58:36 DP0 TP0] Decode batch. #running-req: 1, #token: 313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 6.90, #queue-req: 0, 
[2025-07-28 00:58:37 DP1 TP0] Decode batch. #running-req: 1, #token: 5282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.79, #queue-req: 0, 
[2025-07-28 00:58:37 DP0 TP0] Decode batch. #running-req: 1, #token: 353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.48, #queue-req: 0, 
[2025-07-28 00:58:37 DP1 TP0] Decode batch. #running-req: 1, #token: 5322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.19, #queue-req: 0, 
[2025-07-28 00:58:37 DP0 TP0] Decode batch. #running-req: 1, #token: 393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.77, #queue-req: 0, 
[2025-07-28 00:58:37 DP1 TP0] Decode batch. #running-req: 1, #token: 5362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.40, #queue-req: 0, 
[2025-07-28 00:58:37 DP0 TP0] Decode batch. #running-req: 1, #token: 433, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.82, #queue-req: 0, 
[2025-07-28 00:58:37 DP1 TP0] Decode batch. #running-req: 1, #token: 5402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.06, #queue-req: 0, 
[2025-07-28 00:58:37 DP0 TP0] Decode batch. #running-req: 1, #token: 473, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.07, #queue-req: 0, 
[2025-07-28 00:58:37 DP1 TP0] Decode batch. #running-req: 1, #token: 5442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.03, #queue-req: 0, 
[2025-07-28 00:58:37 DP0 TP0] Decode batch. #running-req: 1, #token: 513, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.07, #queue-req: 0, 
[2025-07-28 00:58:37 DP1 TP0] Decode batch. #running-req: 1, #token: 5482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.72, #queue-req: 0, 
[2025-07-28 00:58:37 DP0 TP0] Decode batch. #running-req: 1, #token: 553, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.24, #queue-req: 0, 
[2025-07-28 00:58:37 DP1 TP0] Decode batch. #running-req: 1, #token: 5522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.70, #queue-req: 0, 
[2025-07-28 00:58:37 DP0 TP0] Decode batch. #running-req: 1, #token: 593, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.33, #queue-req: 0, 
[2025-07-28 00:58:37 DP1 TP0] Decode batch. #running-req: 1, #token: 5562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.25, #queue-req: 0, 
[2025-07-28 00:58:37 DP0 TP0] Decode batch. #running-req: 1, #token: 633, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.89, #queue-req: 0, 
[2025-07-28 00:58:38 DP1 TP0] Decode batch. #running-req: 1, #token: 5602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.68, #queue-req: 0, 
[2025-07-28 00:58:38 DP0 TP0] Decode batch. #running-req: 1, #token: 673, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.35, #queue-req: 0, 
[2025-07-28 00:58:38 DP1 TP0] Decode batch. #running-req: 1, #token: 5642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.28, #queue-req: 0, 
[2025-07-28 00:58:38 DP0 TP0] Decode batch. #running-req: 1, #token: 713, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.24, #queue-req: 0, 
[2025-07-28 00:58:38 DP1 TP0] Decode batch. #running-req: 1, #token: 5682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.60, #queue-req: 0, 
[2025-07-28 00:58:38 DP0 TP0] Decode batch. #running-req: 1, #token: 753, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.87, #queue-req: 0, 
[2025-07-28 00:58:38 DP1 TP0] Decode batch. #running-req: 1, #token: 5722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.86, #queue-req: 0, 
[2025-07-28 00:58:38 DP0 TP0] Decode batch. #running-req: 1, #token: 793, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.63, #queue-req: 0, 
[2025-07-28 00:58:38 DP1 TP0] Decode batch. #running-req: 1, #token: 5762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.32, #queue-req: 0, 
[2025-07-28 00:58:38 DP0 TP0] Decode batch. #running-req: 1, #token: 833, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.91, #queue-req: 0, 
[2025-07-28 00:58:38 DP1 TP0] Decode batch. #running-req: 1, #token: 5802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.71, #queue-req: 0, 
[2025-07-28 00:58:38 DP0 TP0] Decode batch. #running-req: 1, #token: 873, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.48, #queue-req: 0, 
[2025-07-28 00:58:38 DP1 TP0] Decode batch. #running-req: 1, #token: 5842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.27, #queue-req: 0, 
[2025-07-28 00:58:38 DP0 TP0] Decode batch. #running-req: 1, #token: 913, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.39, #queue-req: 0, 
[2025-07-28 00:58:38 DP1 TP0] Decode batch. #running-req: 1, #token: 5882, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.04, #queue-req: 0, 
[2025-07-28 00:58:39 DP0 TP0] Decode batch. #running-req: 1, #token: 953, token usage: 0.00, cuda graph: True, gen throughput (token/s): 282.89, #queue-req: 0, 
[2025-07-28 00:58:39 DP1 TP0] Decode batch. #running-req: 1, #token: 5922, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.18, #queue-req: 0, 
[2025-07-28 00:58:39 DP0 TP0] Decode batch. #running-req: 1, #token: 993, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.24, #queue-req: 0, 
[2025-07-28 00:58:39 DP1 TP0] Decode batch. #running-req: 1, #token: 5962, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.65, #queue-req: 0, 
[2025-07-28 00:58:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1033, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.81, #queue-req: 0, 
[2025-07-28 00:58:39 DP1 TP0] Decode batch. #running-req: 1, #token: 6002, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.83, #queue-req: 0, 
[2025-07-28 00:58:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1073, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.81, #queue-req: 0, 
[2025-07-28 00:58:39 DP1 TP0] Decode batch. #running-req: 1, #token: 6042, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.00, #queue-req: 0, 
[2025-07-28 00:58:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1113, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.65, #queue-req: 0, 
[2025-07-28 00:58:39 DP1 TP0] Decode batch. #running-req: 1, #token: 6082, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.15, #queue-req: 0, 
[2025-07-28 00:58:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1153, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.39, #queue-req: 0, 
[2025-07-28 00:58:39 DP1 TP0] Decode batch. #running-req: 1, #token: 6122, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.67, #queue-req: 0, 
[2025-07-28 00:58:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1193, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.67, #queue-req: 0, 
[2025-07-28 00:58:39 DP1 TP0] Decode batch. #running-req: 1, #token: 6162, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.24, #queue-req: 0, 
[2025-07-28 00:58:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1233, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.69, #queue-req: 0, 
[2025-07-28 00:58:39 DP1 TP0] Decode batch. #running-req: 1, #token: 6202, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.16, #queue-req: 0, 
[2025-07-28 00:58:40] INFO:     127.0.0.1:35410 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:58:40 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 190, #cached-token: 88, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:58:40 DP1 TP0] Decode batch. #running-req: 2, #token: 6437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 231.32, #queue-req: 0, 
[2025-07-28 00:58:40 DP1 TP0] Decode batch. #running-req: 2, #token: 6517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 615.06, #queue-req: 0, 
[2025-07-28 00:58:40 DP1 TP0] Decode batch. #running-req: 2, #token: 6597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 616.42, #queue-req: 0, 
[2025-07-28 00:58:40 DP1 TP0] Decode batch. #running-req: 2, #token: 6677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 617.97, #queue-req: 0, 
[2025-07-28 00:58:40 DP1 TP0] Decode batch. #running-req: 2, #token: 6757, token usage: 0.00, cuda graph: True, gen throughput (token/s): 618.35, #queue-req: 0, 
[2025-07-28 00:58:40 DP1 TP0] Decode batch. #running-req: 2, #token: 6837, token usage: 0.00, cuda graph: True, gen throughput (token/s): 618.84, #queue-req: 0, 
[2025-07-28 00:58:40 DP1 TP0] Decode batch. #running-req: 2, #token: 6917, token usage: 0.00, cuda graph: True, gen throughput (token/s): 614.12, #queue-req: 0, 
[2025-07-28 00:58:41 DP1 TP0] Decode batch. #running-req: 2, #token: 6997, token usage: 0.00, cuda graph: True, gen throughput (token/s): 613.15, #queue-req: 0, 
[2025-07-28 00:58:41 DP1 TP0] Decode batch. #running-req: 2, #token: 7077, token usage: 0.00, cuda graph: True, gen throughput (token/s): 614.03, #queue-req: 0, 
[2025-07-28 00:58:41 DP1 TP0] Decode batch. #running-req: 2, #token: 7157, token usage: 0.00, cuda graph: True, gen throughput (token/s): 610.28, #queue-req: 0, 
[2025-07-28 00:58:41 DP1 TP0] Decode batch. #running-req: 2, #token: 7237, token usage: 0.00, cuda graph: True, gen throughput (token/s): 614.43, #queue-req: 0, 
[2025-07-28 00:58:41 DP1 TP0] Decode batch. #running-req: 2, #token: 7317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 612.57, #queue-req: 0, 
[2025-07-28 00:58:41 DP1 TP0] Decode batch. #running-req: 2, #token: 7397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 614.68, #queue-req: 0, 
[2025-07-28 00:58:41 DP1 TP0] Decode batch. #running-req: 2, #token: 7477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 607.23, #queue-req: 0, 
[2025-07-28 00:58:41 DP1 TP0] Decode batch. #running-req: 2, #token: 7557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 613.50, #queue-req: 0, 
[2025-07-28 00:58:42 DP1 TP0] Decode batch. #running-req: 2, #token: 7637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 607.70, #queue-req: 0, 
[2025-07-28 00:58:42 DP1 TP0] Decode batch. #running-req: 2, #token: 7717, token usage: 0.00, cuda graph: True, gen throughput (token/s): 600.28, #queue-req: 0, 
[2025-07-28 00:58:42 DP1 TP0] Decode batch. #running-req: 2, #token: 7797, token usage: 0.00, cuda graph: True, gen throughput (token/s): 612.81, #queue-req: 0, 
[2025-07-28 00:58:42 DP1 TP0] Decode batch. #running-req: 2, #token: 7877, token usage: 0.00, cuda graph: True, gen throughput (token/s): 613.09, #queue-req: 0, 
[2025-07-28 00:58:42 DP1 TP0] Decode batch. #running-req: 2, #token: 7957, token usage: 0.00, cuda graph: True, gen throughput (token/s): 608.10, #queue-req: 0, 
[2025-07-28 00:58:42 DP1 TP0] Decode batch. #running-req: 2, #token: 8037, token usage: 0.00, cuda graph: True, gen throughput (token/s): 612.75, #queue-req: 0, 
[2025-07-28 00:58:42 DP1 TP0] Decode batch. #running-req: 2, #token: 8117, token usage: 0.00, cuda graph: True, gen throughput (token/s): 610.99, #queue-req: 0, 
[2025-07-28 00:58:43 DP1 TP0] Decode batch. #running-req: 2, #token: 8197, token usage: 0.00, cuda graph: True, gen throughput (token/s): 613.37, #queue-req: 0, 
[2025-07-28 00:58:43 DP1 TP0] Decode batch. #running-req: 2, #token: 8277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 613.09, #queue-req: 0, 
[2025-07-28 00:58:43 DP1 TP0] Decode batch. #running-req: 2, #token: 8357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 613.05, #queue-req: 0, 
[2025-07-28 00:58:43] INFO:     127.0.0.1:35416 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:58:43 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 137, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:58:43 DP1 TP0] Decode batch. #running-req: 1, #token: 7242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 519.09, #queue-req: 0, 
[2025-07-28 00:58:43 DP0 TP0] Decode batch. #running-req: 1, #token: 231, token usage: 0.00, cuda graph: True, gen throughput (token/s): 11.21, #queue-req: 0, 
[2025-07-28 00:58:43 DP1 TP0] Decode batch. #running-req: 1, #token: 7282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.37, #queue-req: 0, 
[2025-07-28 00:58:43 DP0 TP0] Decode batch. #running-req: 1, #token: 271, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.47, #queue-req: 0, 
[2025-07-28 00:58:43 DP1 TP0] Decode batch. #running-req: 1, #token: 7322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.66, #queue-req: 0, 
[2025-07-28 00:58:43 DP0 TP0] Decode batch. #running-req: 1, #token: 311, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.17, #queue-req: 0, 
[2025-07-28 00:58:43 DP1 TP0] Decode batch. #running-req: 1, #token: 7362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.34, #queue-req: 0, 
[2025-07-28 00:58:43 DP0 TP0] Decode batch. #running-req: 1, #token: 351, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.99, #queue-req: 0, 
[2025-07-28 00:58:43 DP1 TP0] Decode batch. #running-req: 1, #token: 7402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.68, #queue-req: 0, 
[2025-07-28 00:58:44 DP0 TP0] Decode batch. #running-req: 1, #token: 391, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.07, #queue-req: 0, 
[2025-07-28 00:58:44 DP1 TP0] Decode batch. #running-req: 1, #token: 7442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.08, #queue-req: 0, 
[2025-07-28 00:58:44 DP0 TP0] Decode batch. #running-req: 1, #token: 431, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.16, #queue-req: 0, 
[2025-07-28 00:58:44 DP1 TP0] Decode batch. #running-req: 1, #token: 7482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.98, #queue-req: 0, 
[2025-07-28 00:58:44 DP0 TP0] Decode batch. #running-req: 1, #token: 471, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.36, #queue-req: 0, 
[2025-07-28 00:58:44 DP1 TP0] Decode batch. #running-req: 1, #token: 7522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.88, #queue-req: 0, 
[2025-07-28 00:58:44 DP0 TP0] Decode batch. #running-req: 1, #token: 511, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.42, #queue-req: 0, 
[2025-07-28 00:58:44 DP1 TP0] Decode batch. #running-req: 1, #token: 7562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.85, #queue-req: 0, 
[2025-07-28 00:58:44 DP0 TP0] Decode batch. #running-req: 1, #token: 551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 288.22, #queue-req: 0, 
[2025-07-28 00:58:44 DP1 TP0] Decode batch. #running-req: 1, #token: 7602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.04, #queue-req: 0, 
[2025-07-28 00:58:44 DP1 TP0] Decode batch. #running-req: 1, #token: 7642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.15, #queue-req: 0, 
[2025-07-28 00:58:44 DP0 TP0] Decode batch. #running-req: 1, #token: 591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 263.30, #queue-req: 0, 
[2025-07-28 00:58:44 DP1 TP0] Decode batch. #running-req: 1, #token: 7682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.83, #queue-req: 0, 
[2025-07-28 00:58:44 DP0 TP0] Decode batch. #running-req: 1, #token: 631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.00, #queue-req: 0, 
[2025-07-28 00:58:44 DP1 TP0] Decode batch. #running-req: 1, #token: 7722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.47, #queue-req: 0, 
[2025-07-28 00:58:44 DP0 TP0] Decode batch. #running-req: 1, #token: 671, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.07, #queue-req: 0, 
[2025-07-28 00:58:45 DP1 TP0] Decode batch. #running-req: 1, #token: 7762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.13, #queue-req: 0, 
[2025-07-28 00:58:45 DP0 TP0] Decode batch. #running-req: 1, #token: 711, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.73, #queue-req: 0, 
[2025-07-28 00:58:45 DP1 TP0] Decode batch. #running-req: 1, #token: 7802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 276.37, #queue-req: 0, 
[2025-07-28 00:58:45 DP0 TP0] Decode batch. #running-req: 1, #token: 751, token usage: 0.00, cuda graph: True, gen throughput (token/s): 272.68, #queue-req: 0, 
[2025-07-28 00:58:45 DP1 TP0] Decode batch. #running-req: 1, #token: 7842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 279.32, #queue-req: 0, 
[2025-07-28 00:58:45 DP0 TP0] Decode batch. #running-req: 1, #token: 791, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.03, #queue-req: 0, 
[2025-07-28 00:58:45] INFO:     127.0.0.1:57816 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:58:45 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 132, #cached-token: 89, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:58:45 DP1 TP0] Decode batch. #running-req: 2, #token: 8037, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.05, #queue-req: 0, 
[2025-07-28 00:58:45 DP1 TP0] Decode batch. #running-req: 2, #token: 8117, token usage: 0.00, cuda graph: True, gen throughput (token/s): 534.26, #queue-req: 0, 
[2025-07-28 00:58:45 DP1 TP0] Decode batch. #running-req: 2, #token: 8197, token usage: 0.00, cuda graph: True, gen throughput (token/s): 502.83, #queue-req: 0, 
[2025-07-28 00:58:46 DP1 TP0] Decode batch. #running-req: 2, #token: 8277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 594.56, #queue-req: 0, 
[2025-07-28 00:58:46 DP1 TP0] Decode batch. #running-req: 2, #token: 8357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 600.74, #queue-req: 0, 
[2025-07-28 00:58:46 DP1 TP0] Decode batch. #running-req: 2, #token: 8437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 591.79, #queue-req: 0, 
[2025-07-28 00:58:46 DP1 TP0] Decode batch. #running-req: 2, #token: 8517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 604.24, #queue-req: 0, 
[2025-07-28 00:58:46 DP1 TP0] Decode batch. #running-req: 2, #token: 8597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 601.33, #queue-req: 0, 
[2025-07-28 00:58:46 DP1 TP0] Decode batch. #running-req: 2, #token: 8677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 588.57, #queue-req: 0, 
[2025-07-28 00:58:46 DP1 TP0] Decode batch. #running-req: 2, #token: 8757, token usage: 0.00, cuda graph: True, gen throughput (token/s): 599.89, #queue-req: 0, 
[2025-07-28 00:58:46 DP1 TP0] Decode batch. #running-req: 2, #token: 8837, token usage: 0.00, cuda graph: True, gen throughput (token/s): 594.25, #queue-req: 0, 
[2025-07-28 00:58:47 DP1 TP0] Decode batch. #running-req: 2, #token: 8917, token usage: 0.00, cuda graph: True, gen throughput (token/s): 601.98, #queue-req: 0, 
[2025-07-28 00:58:47 DP1 TP0] Decode batch. #running-req: 2, #token: 8997, token usage: 0.00, cuda graph: True, gen throughput (token/s): 601.10, #queue-req: 0, 
[2025-07-28 00:58:47 DP1 TP0] Decode batch. #running-req: 2, #token: 9077, token usage: 0.00, cuda graph: True, gen throughput (token/s): 598.83, #queue-req: 0, 
[2025-07-28 00:58:47 DP1 TP0] Decode batch. #running-req: 2, #token: 9157, token usage: 0.00, cuda graph: True, gen throughput (token/s): 595.52, #queue-req: 0, 
[2025-07-28 00:58:47] INFO:     127.0.0.1:44780 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:58:47 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 199, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:58:47 DP1 TP0] Decode batch. #running-req: 1, #token: 840, token usage: 0.00, cuda graph: True, gen throughput (token/s): 389.06, #queue-req: 0, 
[2025-07-28 00:58:47 DP0 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 17.00, #queue-req: 0, 
[2025-07-28 00:58:47 DP1 TP0] Decode batch. #running-req: 1, #token: 880, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.88, #queue-req: 0, 
[2025-07-28 00:58:47] INFO:     127.0.0.1:57824 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:58:47 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 194, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:58:47 DP0 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.49, #queue-req: 0, 
[2025-07-28 00:58:47 DP1 TP0] Decode batch. #running-req: 1, #token: 314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 202.90, #queue-req: 0, 
[2025-07-28 00:58:48 DP0 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.72, #queue-req: 0, 
[2025-07-28 00:58:48 DP1 TP0] Decode batch. #running-req: 1, #token: 354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.55, #queue-req: 0, 
[2025-07-28 00:58:48 DP0 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.87, #queue-req: 0, 
[2025-07-28 00:58:48 DP1 TP0] Decode batch. #running-req: 1, #token: 394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.74, #queue-req: 0, 
[2025-07-28 00:58:48 DP0 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.93, #queue-req: 0, 
[2025-07-28 00:58:48 DP1 TP0] Decode batch. #running-req: 1, #token: 434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.57, #queue-req: 0, 
[2025-07-28 00:58:48 DP0 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.61, #queue-req: 0, 
[2025-07-28 00:58:48 DP1 TP0] Decode batch. #running-req: 1, #token: 474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.60, #queue-req: 0, 
[2025-07-28 00:58:48 DP0 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.11, #queue-req: 0, 
[2025-07-28 00:58:48 DP1 TP0] Decode batch. #running-req: 1, #token: 514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.81, #queue-req: 0, 
[2025-07-28 00:58:48 DP0 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.42, #queue-req: 0, 
[2025-07-28 00:58:48 DP1 TP0] Decode batch. #running-req: 1, #token: 554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.03, #queue-req: 0, 
[2025-07-28 00:58:48 DP0 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.95, #queue-req: 0, 
[2025-07-28 00:58:48 DP1 TP0] Decode batch. #running-req: 1, #token: 594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.42, #queue-req: 0, 
[2025-07-28 00:58:48 DP0 TP0] Decode batch. #running-req: 1, #token: 676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.86, #queue-req: 0, 
[2025-07-28 00:58:49 DP1 TP0] Decode batch. #running-req: 1, #token: 634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.38, #queue-req: 0, 
[2025-07-28 00:58:49 DP0 TP0] Decode batch. #running-req: 1, #token: 716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.05, #queue-req: 0, 
[2025-07-28 00:58:49 DP1 TP0] Decode batch. #running-req: 1, #token: 674, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.97, #queue-req: 0, 
[2025-07-28 00:58:49 DP0 TP0] Decode batch. #running-req: 1, #token: 756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.87, #queue-req: 0, 
[2025-07-28 00:58:49 DP1 TP0] Decode batch. #running-req: 1, #token: 714, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.84, #queue-req: 0, 
[2025-07-28 00:58:49 DP0 TP0] Decode batch. #running-req: 1, #token: 796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.19, #queue-req: 0, 
[2025-07-28 00:58:49 DP1 TP0] Decode batch. #running-req: 1, #token: 754, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.53, #queue-req: 0, 
[2025-07-28 00:58:49] INFO:     127.0.0.1:57836 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:58:49 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 167, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:58:49 DP1 TP0] Decode batch. #running-req: 1, #token: 794, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.18, #queue-req: 0, 
[2025-07-28 00:58:49 DP0 TP0] Decode batch. #running-req: 1, #token: 274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 190.80, #queue-req: 0, 
[2025-07-28 00:58:49 DP1 TP0] Decode batch. #running-req: 1, #token: 834, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.83, #queue-req: 0, 
[2025-07-28 00:58:49 DP0 TP0] Decode batch. #running-req: 1, #token: 314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.79, #queue-req: 0, 
[2025-07-28 00:58:49 DP1 TP0] Decode batch. #running-req: 1, #token: 874, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.13, #queue-req: 0, 
[2025-07-28 00:58:49 DP0 TP0] Decode batch. #running-req: 1, #token: 354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.51, #queue-req: 0, 
[2025-07-28 00:58:49 DP1 TP0] Decode batch. #running-req: 1, #token: 914, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.73, #queue-req: 0, 
[2025-07-28 00:58:49 DP0 TP0] Decode batch. #running-req: 1, #token: 394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 272.02, #queue-req: 0, 
[2025-07-28 00:58:50 DP1 TP0] Decode batch. #running-req: 1, #token: 954, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.65, #queue-req: 0, 
[2025-07-28 00:58:50 DP0 TP0] Decode batch. #running-req: 1, #token: 434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 272.86, #queue-req: 0, 
[2025-07-28 00:58:50 DP1 TP0] Decode batch. #running-req: 1, #token: 994, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.91, #queue-req: 0, 
[2025-07-28 00:58:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1034, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.03, #queue-req: 0, 
[2025-07-28 00:58:50 DP0 TP0] Decode batch. #running-req: 1, #token: 474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 270.86, #queue-req: 0, 
[2025-07-28 00:58:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1074, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.64, #queue-req: 0, 
[2025-07-28 00:58:50 DP0 TP0] Decode batch. #running-req: 1, #token: 514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 263.49, #queue-req: 0, 
[2025-07-28 00:58:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1114, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.81, #queue-req: 0, 
[2025-07-28 00:58:50 DP0 TP0] Decode batch. #running-req: 1, #token: 554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.72, #queue-req: 0, 
[2025-07-28 00:58:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1154, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.02, #queue-req: 0, 
[2025-07-28 00:58:50 DP0 TP0] Decode batch. #running-req: 1, #token: 594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.91, #queue-req: 0, 
[2025-07-28 00:58:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1194, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.65, #queue-req: 0, 
[2025-07-28 00:58:50 DP0 TP0] Decode batch. #running-req: 1, #token: 634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.65, #queue-req: 0, 
[2025-07-28 00:58:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1234, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.55, #queue-req: 0, 
[2025-07-28 00:58:50 DP0 TP0] Decode batch. #running-req: 1, #token: 674, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.79, #queue-req: 0, 
[2025-07-28 00:58:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.16, #queue-req: 0, 
[2025-07-28 00:58:51 DP0 TP0] Decode batch. #running-req: 1, #token: 714, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.93, #queue-req: 0, 
[2025-07-28 00:58:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.35, #queue-req: 0, 
[2025-07-28 00:58:51 DP0 TP0] Decode batch. #running-req: 1, #token: 754, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.69, #queue-req: 0, 
[2025-07-28 00:58:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.57, #queue-req: 0, 
[2025-07-28 00:58:51 DP0 TP0] Decode batch. #running-req: 1, #token: 794, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.09, #queue-req: 0, 
[2025-07-28 00:58:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.61, #queue-req: 0, 
[2025-07-28 00:58:51 DP0 TP0] Decode batch. #running-req: 1, #token: 834, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.35, #queue-req: 0, 
[2025-07-28 00:58:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.58, #queue-req: 0, 
[2025-07-28 00:58:51 DP0 TP0] Decode batch. #running-req: 1, #token: 874, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.44, #queue-req: 0, 
[2025-07-28 00:58:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.15, #queue-req: 0, 
[2025-07-28 00:58:51 DP0 TP0] Decode batch. #running-req: 1, #token: 914, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.66, #queue-req: 0, 
[2025-07-28 00:58:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.93, #queue-req: 0, 
[2025-07-28 00:58:51] INFO:     127.0.0.1:57852 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:58:51 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 172, #cached-token: 89, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:58:51 DP0 TP0] Decode batch. #running-req: 1, #token: 954, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.42, #queue-req: 0, 
[2025-07-28 00:58:51 DP0 TP0] Decode batch. #running-req: 1, #token: 994, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.08, #queue-req: 0, 
[2025-07-28 00:58:52 DP1 TP0] Decode batch. #running-req: 1, #token: 287, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.18, #queue-req: 0, 
[2025-07-28 00:58:52 DP1 TP0] Decode batch. #running-req: 1, #token: 327, token usage: 0.00, cuda graph: True, gen throughput (token/s): 335.61, #queue-req: 0, 
[2025-07-28 00:58:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1034, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.43, #queue-req: 0, 
[2025-07-28 00:58:52 DP1 TP0] Decode batch. #running-req: 1, #token: 367, token usage: 0.00, cuda graph: True, gen throughput (token/s): 334.95, #queue-req: 0, 
[2025-07-28 00:58:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1074, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.19, #queue-req: 0, 
[2025-07-28 00:58:52 DP1 TP0] Decode batch. #running-req: 1, #token: 407, token usage: 0.00, cuda graph: True, gen throughput (token/s): 324.26, #queue-req: 0, 
[2025-07-28 00:58:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1114, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.39, #queue-req: 0, 
[2025-07-28 00:58:52 DP1 TP0] Decode batch. #running-req: 1, #token: 447, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.78, #queue-req: 0, 
[2025-07-28 00:58:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1154, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.80, #queue-req: 0, 
[2025-07-28 00:58:52 DP1 TP0] Decode batch. #running-req: 1, #token: 487, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.42, #queue-req: 0, 
[2025-07-28 00:58:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1194, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.98, #queue-req: 0, 
[2025-07-28 00:58:52 DP1 TP0] Decode batch. #running-req: 1, #token: 527, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.10, #queue-req: 0, 
[2025-07-28 00:58:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1234, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.03, #queue-req: 0, 
[2025-07-28 00:58:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.39, #queue-req: 0, 
[2025-07-28 00:58:52 DP1 TP0] Decode batch. #running-req: 1, #token: 567, token usage: 0.00, cuda graph: True, gen throughput (token/s): 232.51, #queue-req: 0, 
[2025-07-28 00:58:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.72, #queue-req: 0, 
[2025-07-28 00:58:53 DP1 TP0] Decode batch. #running-req: 1, #token: 607, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.77, #queue-req: 0, 
[2025-07-28 00:58:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.13, #queue-req: 0, 
[2025-07-28 00:58:53 DP1 TP0] Decode batch. #running-req: 1, #token: 647, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.65, #queue-req: 0, 
[2025-07-28 00:58:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.18, #queue-req: 0, 
[2025-07-28 00:58:53 DP1 TP0] Decode batch. #running-req: 1, #token: 687, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.38, #queue-req: 0, 
[2025-07-28 00:58:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.82, #queue-req: 0, 
[2025-07-28 00:58:53 DP1 TP0] Decode batch. #running-req: 1, #token: 727, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.39, #queue-req: 0, 
[2025-07-28 00:58:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.01, #queue-req: 0, 
[2025-07-28 00:58:53 DP1 TP0] Decode batch. #running-req: 1, #token: 767, token usage: 0.00, cuda graph: True, gen throughput (token/s): 320.50, #queue-req: 0, 
[2025-07-28 00:58:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.39, #queue-req: 0, 
[2025-07-28 00:58:53 DP1 TP0] Decode batch. #running-req: 1, #token: 807, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.92, #queue-req: 0, 
[2025-07-28 00:58:53 DP1 TP0] Decode batch. #running-req: 1, #token: 847, token usage: 0.00, cuda graph: True, gen throughput (token/s): 321.06, #queue-req: 0, 
[2025-07-28 00:58:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.91, #queue-req: 0, 
[2025-07-28 00:58:53] INFO:     127.0.0.1:57868 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:58:53 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 219, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:58:53 DP1 TP0] Decode batch. #running-req: 1, #token: 887, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.53, #queue-req: 0, 
[2025-07-28 00:58:53] INFO:     127.0.0.1:57882 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:58:53 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 155, #cached-token: 90, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:58:54 DP0 TP0] Decode batch. #running-req: 1, #token: 318, token usage: 0.00, cuda graph: True, gen throughput (token/s): 174.13, #queue-req: 0, 
[2025-07-28 00:58:54 DP1 TP0] Decode batch. #running-req: 1, #token: 281, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.12, #queue-req: 0, 
[2025-07-28 00:58:54 DP0 TP0] Decode batch. #running-req: 1, #token: 358, token usage: 0.00, cuda graph: True, gen throughput (token/s): 330.37, #queue-req: 0, 
[2025-07-28 00:58:54 DP1 TP0] Decode batch. #running-req: 1, #token: 321, token usage: 0.00, cuda graph: True, gen throughput (token/s): 341.59, #queue-req: 0, 
[2025-07-28 00:58:54 DP0 TP0] Decode batch. #running-req: 1, #token: 398, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.88, #queue-req: 0, 
[2025-07-28 00:58:54 DP1 TP0] Decode batch. #running-req: 1, #token: 361, token usage: 0.00, cuda graph: True, gen throughput (token/s): 341.28, #queue-req: 0, 
[2025-07-28 00:58:54 DP0 TP0] Decode batch. #running-req: 1, #token: 438, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.17, #queue-req: 0, 
[2025-07-28 00:58:54 DP1 TP0] Decode batch. #running-req: 1, #token: 401, token usage: 0.00, cuda graph: True, gen throughput (token/s): 325.84, #queue-req: 0, 
[2025-07-28 00:58:54 DP0 TP0] Decode batch. #running-req: 1, #token: 478, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.06, #queue-req: 0, 
[2025-07-28 00:58:54 DP1 TP0] Decode batch. #running-req: 1, #token: 441, token usage: 0.00, cuda graph: True, gen throughput (token/s): 322.16, #queue-req: 0, 
[2025-07-28 00:58:54 DP0 TP0] Decode batch. #running-req: 1, #token: 518, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.14, #queue-req: 0, 
[2025-07-28 00:58:54 DP1 TP0] Decode batch. #running-req: 1, #token: 481, token usage: 0.00, cuda graph: True, gen throughput (token/s): 320.33, #queue-req: 0, 
[2025-07-28 00:58:54 DP0 TP0] Decode batch. #running-req: 1, #token: 558, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.74, #queue-req: 0, 
[2025-07-28 00:58:54 DP1 TP0] Decode batch. #running-req: 1, #token: 521, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.73, #queue-req: 0, 
[2025-07-28 00:58:54 DP0 TP0] Decode batch. #running-req: 1, #token: 598, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.65, #queue-req: 0, 
[2025-07-28 00:58:55 DP1 TP0] Decode batch. #running-req: 1, #token: 561, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.62, #queue-req: 0, 
[2025-07-28 00:58:55 DP0 TP0] Decode batch. #running-req: 1, #token: 638, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.50, #queue-req: 0, 
[2025-07-28 00:58:55 DP1 TP0] Decode batch. #running-req: 1, #token: 601, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.18, #queue-req: 0, 
[2025-07-28 00:58:55 DP0 TP0] Decode batch. #running-req: 1, #token: 678, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.38, #queue-req: 0, 
[2025-07-28 00:58:55 DP1 TP0] Decode batch. #running-req: 1, #token: 641, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.34, #queue-req: 0, 
[2025-07-28 00:58:55 DP0 TP0] Decode batch. #running-req: 1, #token: 718, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.30, #queue-req: 0, 
[2025-07-28 00:58:55 DP1 TP0] Decode batch. #running-req: 1, #token: 681, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.86, #queue-req: 0, 
[2025-07-28 00:58:55 DP0 TP0] Decode batch. #running-req: 1, #token: 758, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.15, #queue-req: 0, 
[2025-07-28 00:58:55 DP1 TP0] Decode batch. #running-req: 1, #token: 721, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.44, #queue-req: 0, 
[2025-07-28 00:58:55 DP0 TP0] Decode batch. #running-req: 1, #token: 798, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.79, #queue-req: 0, 
[2025-07-28 00:58:55 DP1 TP0] Decode batch. #running-req: 1, #token: 761, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.17, #queue-req: 0, 
[2025-07-28 00:58:55 DP0 TP0] Decode batch. #running-req: 1, #token: 838, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.33, #queue-req: 0, 
[2025-07-28 00:58:55 DP1 TP0] Decode batch. #running-req: 1, #token: 801, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.66, #queue-req: 0, 
[2025-07-28 00:58:55 DP0 TP0] Decode batch. #running-req: 1, #token: 878, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.87, #queue-req: 0, 
[2025-07-28 00:58:55 DP1 TP0] Decode batch. #running-req: 1, #token: 841, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.41, #queue-req: 0, 
[2025-07-28 00:58:55 DP0 TP0] Decode batch. #running-req: 1, #token: 918, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.98, #queue-req: 0, 
[2025-07-28 00:58:56 DP1 TP0] Decode batch. #running-req: 1, #token: 881, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.41, #queue-req: 0, 
[2025-07-28 00:58:56 DP0 TP0] Decode batch. #running-req: 1, #token: 958, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.85, #queue-req: 0, 
[2025-07-28 00:58:56 DP1 TP0] Decode batch. #running-req: 1, #token: 921, token usage: 0.00, cuda graph: True, gen throughput (token/s): 320.16, #queue-req: 0, 
[2025-07-28 00:58:56 DP0 TP0] Decode batch. #running-req: 1, #token: 998, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.09, #queue-req: 0, 
[2025-07-28 00:58:56 DP1 TP0] Decode batch. #running-req: 1, #token: 961, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.74, #queue-req: 0, 
[2025-07-28 00:58:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1038, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.27, #queue-req: 0, 
[2025-07-28 00:58:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1001, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.66, #queue-req: 0, 
[2025-07-28 00:58:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1078, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.71, #queue-req: 0, 
[2025-07-28 00:58:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1041, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.40, #queue-req: 0, 
[2025-07-28 00:58:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1118, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.82, #queue-req: 0, 
[2025-07-28 00:58:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1081, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.99, #queue-req: 0, 
[2025-07-28 00:58:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1158, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.72, #queue-req: 0, 
[2025-07-28 00:58:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1121, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.11, #queue-req: 0, 
[2025-07-28 00:58:56] INFO:     127.0.0.1:51462 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:58:56 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 175, #cached-token: 89, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:58:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1161, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.58, #queue-req: 0, 
[2025-07-28 00:58:56 DP0 TP0] Decode batch. #running-req: 1, #token: 281, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.94, #queue-req: 0, 
[2025-07-28 00:58:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1201, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.76, #queue-req: 0, 
[2025-07-28 00:58:57 DP0 TP0] Decode batch. #running-req: 1, #token: 321, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.97, #queue-req: 0, 
[2025-07-28 00:58:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1241, token usage: 0.00, cuda graph: True, gen throughput (token/s): 320.10, #queue-req: 0, 
[2025-07-28 00:58:57 DP0 TP0] Decode batch. #running-req: 1, #token: 361, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.24, #queue-req: 0, 
[2025-07-28 00:58:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1281, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.68, #queue-req: 0, 
[2025-07-28 00:58:57 DP0 TP0] Decode batch. #running-req: 1, #token: 401, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.52, #queue-req: 0, 
[2025-07-28 00:58:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1321, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.54, #queue-req: 0, 
[2025-07-28 00:58:57 DP0 TP0] Decode batch. #running-req: 1, #token: 441, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.93, #queue-req: 0, 
[2025-07-28 00:58:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1361, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.55, #queue-req: 0, 
[2025-07-28 00:58:57 DP0 TP0] Decode batch. #running-req: 1, #token: 481, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.34, #queue-req: 0, 
[2025-07-28 00:58:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1401, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.90, #queue-req: 0, 
[2025-07-28 00:58:57 DP0 TP0] Decode batch. #running-req: 1, #token: 521, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.32, #queue-req: 0, 
[2025-07-28 00:58:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1441, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.86, #queue-req: 0, 
[2025-07-28 00:58:57 DP0 TP0] Decode batch. #running-req: 1, #token: 561, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.85, #queue-req: 0, 
[2025-07-28 00:58:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1481, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.54, #queue-req: 0, 
[2025-07-28 00:58:57] INFO:     127.0.0.1:51476 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:58:58 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 94, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:58:58 DP0 TP0] Decode batch. #running-req: 1, #token: 601, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.00, #queue-req: 0, 
[2025-07-28 00:58:58 DP1 TP0] Decode batch. #running-req: 1, #token: 198, token usage: 0.00, cuda graph: True, gen throughput (token/s): 195.03, #queue-req: 0, 
[2025-07-28 00:58:58 DP0 TP0] Decode batch. #running-req: 1, #token: 641, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.96, #queue-req: 0, 
[2025-07-28 00:58:58 DP1 TP0] Decode batch. #running-req: 1, #token: 238, token usage: 0.00, cuda graph: True, gen throughput (token/s): 337.31, #queue-req: 0, 
[2025-07-28 00:58:58 DP0 TP0] Decode batch. #running-req: 1, #token: 681, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.39, #queue-req: 0, 
[2025-07-28 00:58:58 DP1 TP0] Decode batch. #running-req: 1, #token: 278, token usage: 0.00, cuda graph: True, gen throughput (token/s): 336.60, #queue-req: 0, 
[2025-07-28 00:58:58 DP0 TP0] Decode batch. #running-req: 1, #token: 721, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.69, #queue-req: 0, 
[2025-07-28 00:58:58 DP1 TP0] Decode batch. #running-req: 1, #token: 318, token usage: 0.00, cuda graph: True, gen throughput (token/s): 338.28, #queue-req: 0, 
[2025-07-28 00:58:58 DP0 TP0] Decode batch. #running-req: 1, #token: 761, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.77, #queue-req: 0, 
[2025-07-28 00:58:58 DP1 TP0] Decode batch. #running-req: 1, #token: 358, token usage: 0.00, cuda graph: True, gen throughput (token/s): 337.85, #queue-req: 0, 
[2025-07-28 00:58:58 DP0 TP0] Decode batch. #running-req: 1, #token: 801, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.36, #queue-req: 0, 
[2025-07-28 00:58:58 DP1 TP0] Decode batch. #running-req: 1, #token: 398, token usage: 0.00, cuda graph: True, gen throughput (token/s): 339.40, #queue-req: 0, 
[2025-07-28 00:58:58 DP0 TP0] Decode batch. #running-req: 1, #token: 841, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.94, #queue-req: 0, 
[2025-07-28 00:58:58 DP1 TP0] Decode batch. #running-req: 1, #token: 438, token usage: 0.00, cuda graph: True, gen throughput (token/s): 336.96, #queue-req: 0, 
[2025-07-28 00:58:58 DP0 TP0] Decode batch. #running-req: 1, #token: 881, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.09, #queue-req: 0, 
[2025-07-28 00:58:58 DP1 TP0] Decode batch. #running-req: 1, #token: 478, token usage: 0.00, cuda graph: True, gen throughput (token/s): 338.56, #queue-req: 0, 
[2025-07-28 00:58:59 DP0 TP0] Decode batch. #running-req: 1, #token: 921, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.09, #queue-req: 0, 
[2025-07-28 00:58:59 DP1 TP0] Decode batch. #running-req: 1, #token: 518, token usage: 0.00, cuda graph: True, gen throughput (token/s): 340.61, #queue-req: 0, 
[2025-07-28 00:58:59 DP0 TP0] Decode batch. #running-req: 1, #token: 961, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.74, #queue-req: 0, 
[2025-07-28 00:58:59 DP1 TP0] Decode batch. #running-req: 1, #token: 558, token usage: 0.00, cuda graph: True, gen throughput (token/s): 338.41, #queue-req: 0, 
[2025-07-28 00:58:59 DP1 TP0] Decode batch. #running-req: 1, #token: 598, token usage: 0.00, cuda graph: True, gen throughput (token/s): 337.64, #queue-req: 0, 
[2025-07-28 00:58:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1001, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.99, #queue-req: 0, 
[2025-07-28 00:58:59 DP1 TP0] Decode batch. #running-req: 1, #token: 638, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.37, #queue-req: 0, 
[2025-07-28 00:58:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1041, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.96, #queue-req: 0, 
[2025-07-28 00:58:59 DP1 TP0] Decode batch. #running-req: 1, #token: 678, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.87, #queue-req: 0, 
[2025-07-28 00:58:59] INFO:     127.0.0.1:51504 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:58:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1081, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.68, #queue-req: 0, 
[2025-07-28 00:58:59 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 115, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:58:59 DP0 TP0] Decode batch. #running-req: 2, #token: 1273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 399.39, #queue-req: 0, 
[2025-07-28 00:58:59 DP0 TP0] Decode batch. #running-req: 2, #token: 1353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 625.33, #queue-req: 0, 
[2025-07-28 00:59:00 DP0 TP0] Decode batch. #running-req: 2, #token: 1433, token usage: 0.00, cuda graph: True, gen throughput (token/s): 625.31, #queue-req: 0, 
[2025-07-28 00:59:00 DP0 TP0] Decode batch. #running-req: 2, #token: 1513, token usage: 0.00, cuda graph: True, gen throughput (token/s): 626.28, #queue-req: 0, 
[2025-07-28 00:59:00 DP0 TP0] Decode batch. #running-req: 2, #token: 1593, token usage: 0.00, cuda graph: True, gen throughput (token/s): 623.59, #queue-req: 0, 
[2025-07-28 00:59:00 DP0 TP0] Decode batch. #running-req: 2, #token: 1673, token usage: 0.00, cuda graph: True, gen throughput (token/s): 624.87, #queue-req: 0, 
[2025-07-28 00:59:00 DP0 TP0] Decode batch. #running-req: 2, #token: 1753, token usage: 0.00, cuda graph: True, gen throughput (token/s): 626.75, #queue-req: 0, 
[2025-07-28 00:59:00 DP0 TP0] Decode batch. #running-req: 2, #token: 1833, token usage: 0.00, cuda graph: True, gen throughput (token/s): 627.76, #queue-req: 0, 
[2025-07-28 00:59:00 DP0 TP0] Decode batch. #running-req: 2, #token: 1913, token usage: 0.00, cuda graph: True, gen throughput (token/s): 621.45, #queue-req: 0, 
[2025-07-28 00:59:00 DP0 TP0] Decode batch. #running-req: 2, #token: 1993, token usage: 0.00, cuda graph: True, gen throughput (token/s): 626.38, #queue-req: 0, 
[2025-07-28 00:59:01 DP0 TP0] Decode batch. #running-req: 2, #token: 2073, token usage: 0.00, cuda graph: True, gen throughput (token/s): 625.29, #queue-req: 0, 
[2025-07-28 00:59:01 DP0 TP0] Decode batch. #running-req: 2, #token: 2153, token usage: 0.00, cuda graph: True, gen throughput (token/s): 627.69, #queue-req: 0, 
[2025-07-28 00:59:01 DP0 TP0] Decode batch. #running-req: 2, #token: 2233, token usage: 0.00, cuda graph: True, gen throughput (token/s): 601.94, #queue-req: 0, 
[2025-07-28 00:59:01 DP0 TP0] Decode batch. #running-req: 2, #token: 2313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 624.77, #queue-req: 0, 
[2025-07-28 00:59:01 DP0 TP0] Decode batch. #running-req: 2, #token: 2393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 626.02, #queue-req: 0, 
[2025-07-28 00:59:01 DP0 TP0] Decode batch. #running-req: 2, #token: 2473, token usage: 0.00, cuda graph: True, gen throughput (token/s): 624.13, #queue-req: 0, 
[2025-07-28 00:59:01 DP0 TP0] Decode batch. #running-req: 2, #token: 2553, token usage: 0.00, cuda graph: True, gen throughput (token/s): 619.74, #queue-req: 0, 
[2025-07-28 00:59:01 DP0 TP0] Decode batch. #running-req: 2, #token: 2633, token usage: 0.00, cuda graph: True, gen throughput (token/s): 625.34, #queue-req: 0, 
[2025-07-28 00:59:02 DP0 TP0] Decode batch. #running-req: 2, #token: 2713, token usage: 0.00, cuda graph: True, gen throughput (token/s): 626.59, #queue-req: 0, 
[2025-07-28 00:59:02 DP0 TP0] Decode batch. #running-req: 2, #token: 2793, token usage: 0.00, cuda graph: True, gen throughput (token/s): 616.38, #queue-req: 0, 
[2025-07-28 00:59:02 DP0 TP0] Decode batch. #running-req: 2, #token: 2873, token usage: 0.00, cuda graph: True, gen throughput (token/s): 621.25, #queue-req: 0, 
[2025-07-28 00:59:02 DP0 TP0] Decode batch. #running-req: 2, #token: 2953, token usage: 0.00, cuda graph: True, gen throughput (token/s): 568.72, #queue-req: 0, 
[2025-07-28 00:59:02 DP0 TP0] Decode batch. #running-req: 2, #token: 3033, token usage: 0.00, cuda graph: True, gen throughput (token/s): 580.59, #queue-req: 0, 
[2025-07-28 00:59:02 DP0 TP0] Decode batch. #running-req: 2, #token: 3113, token usage: 0.00, cuda graph: True, gen throughput (token/s): 621.16, #queue-req: 0, 
[2025-07-28 00:59:02 DP0 TP0] Decode batch. #running-req: 2, #token: 3193, token usage: 0.00, cuda graph: True, gen throughput (token/s): 610.60, #queue-req: 0, 
[2025-07-28 00:59:02 DP0 TP0] Decode batch. #running-req: 2, #token: 3273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 618.93, #queue-req: 0, 
[2025-07-28 00:59:03 DP0 TP0] Decode batch. #running-req: 2, #token: 3353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 620.49, #queue-req: 0, 
[2025-07-28 00:59:03 DP0 TP0] Decode batch. #running-req: 2, #token: 3433, token usage: 0.00, cuda graph: True, gen throughput (token/s): 617.28, #queue-req: 0, 
[2025-07-28 00:59:03 DP0 TP0] Decode batch. #running-req: 2, #token: 3513, token usage: 0.00, cuda graph: True, gen throughput (token/s): 617.91, #queue-req: 0, 
[2025-07-28 00:59:03 DP0 TP0] Decode batch. #running-req: 2, #token: 3593, token usage: 0.00, cuda graph: True, gen throughput (token/s): 620.01, #queue-req: 0, 
[2025-07-28 00:59:03 DP0 TP0] Decode batch. #running-req: 2, #token: 3673, token usage: 0.00, cuda graph: True, gen throughput (token/s): 615.91, #queue-req: 0, 
[2025-07-28 00:59:03 DP0 TP0] Decode batch. #running-req: 2, #token: 3753, token usage: 0.00, cuda graph: True, gen throughput (token/s): 621.15, #queue-req: 0, 
[2025-07-28 00:59:03 DP0 TP0] Decode batch. #running-req: 2, #token: 3833, token usage: 0.00, cuda graph: True, gen throughput (token/s): 598.38, #queue-req: 0, 
[2025-07-28 00:59:04 DP0 TP0] Decode batch. #running-req: 2, #token: 3913, token usage: 0.00, cuda graph: True, gen throughput (token/s): 608.22, #queue-req: 0, 
[2025-07-28 00:59:04 DP0 TP0] Decode batch. #running-req: 2, #token: 3993, token usage: 0.00, cuda graph: True, gen throughput (token/s): 610.57, #queue-req: 0, 
[2025-07-28 00:59:04 DP0 TP0] Decode batch. #running-req: 2, #token: 4073, token usage: 0.00, cuda graph: True, gen throughput (token/s): 599.33, #queue-req: 0, 
[2025-07-28 00:59:04 DP0 TP0] Decode batch. #running-req: 2, #token: 4153, token usage: 0.00, cuda graph: True, gen throughput (token/s): 575.68, #queue-req: 0, 
[2025-07-28 00:59:04 DP0 TP0] Decode batch. #running-req: 2, #token: 4233, token usage: 0.00, cuda graph: True, gen throughput (token/s): 581.63, #queue-req: 0, 
[2025-07-28 00:59:04 DP0 TP0] Decode batch. #running-req: 2, #token: 4313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 582.14, #queue-req: 0, 
[2025-07-28 00:59:04 DP0 TP0] Decode batch. #running-req: 2, #token: 4393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 583.42, #queue-req: 0, 
[2025-07-28 00:59:04 DP0 TP0] Decode batch. #running-req: 2, #token: 4473, token usage: 0.00, cuda graph: True, gen throughput (token/s): 574.96, #queue-req: 0, 
[2025-07-28 00:59:05] INFO:     127.0.0.1:51488 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:59:05 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 150, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:59:05 DP0 TP0] Decode batch. #running-req: 1, #token: 1877, token usage: 0.00, cuda graph: True, gen throughput (token/s): 474.43, #queue-req: 0, 
[2025-07-28 00:59:05 DP0 TP0] Decode batch. #running-req: 1, #token: 1917, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.83, #queue-req: 0, 
[2025-07-28 00:59:05 DP1 TP0] Decode batch. #running-req: 1, #token: 277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 6.95, #queue-req: 0, 
[2025-07-28 00:59:05 DP0 TP0] Decode batch. #running-req: 1, #token: 1957, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.97, #queue-req: 0, 
[2025-07-28 00:59:05 DP1 TP0] Decode batch. #running-req: 1, #token: 317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.62, #queue-req: 0, 
[2025-07-28 00:59:05 DP0 TP0] Decode batch. #running-req: 1, #token: 1997, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.97, #queue-req: 0, 
[2025-07-28 00:59:05 DP1 TP0] Decode batch. #running-req: 1, #token: 357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.54, #queue-req: 0, 
[2025-07-28 00:59:05 DP0 TP0] Decode batch. #running-req: 1, #token: 2037, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.24, #queue-req: 0, 
[2025-07-28 00:59:05 DP1 TP0] Decode batch. #running-req: 1, #token: 397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.17, #queue-req: 0, 
[2025-07-28 00:59:05 DP0 TP0] Decode batch. #running-req: 1, #token: 2077, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.52, #queue-req: 0, 
[2025-07-28 00:59:05 DP1 TP0] Decode batch. #running-req: 1, #token: 437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.24, #queue-req: 0, 
[2025-07-28 00:59:05 DP0 TP0] Decode batch. #running-req: 1, #token: 2117, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.71, #queue-req: 0, 
[2025-07-28 00:59:05 DP1 TP0] Decode batch. #running-req: 1, #token: 477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.97, #queue-req: 0, 
[2025-07-28 00:59:06 DP0 TP0] Decode batch. #running-req: 1, #token: 2157, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.47, #queue-req: 0, 
[2025-07-28 00:59:06 DP1 TP0] Decode batch. #running-req: 1, #token: 517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.62, #queue-req: 0, 
[2025-07-28 00:59:06 DP0 TP0] Decode batch. #running-req: 1, #token: 2197, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.85, #queue-req: 0, 
[2025-07-28 00:59:06 DP1 TP0] Decode batch. #running-req: 1, #token: 557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.93, #queue-req: 0, 
[2025-07-28 00:59:06 DP0 TP0] Decode batch. #running-req: 1, #token: 2237, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.18, #queue-req: 0, 
[2025-07-28 00:59:06 DP1 TP0] Decode batch. #running-req: 1, #token: 597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.36, #queue-req: 0, 
[2025-07-28 00:59:06 DP0 TP0] Decode batch. #running-req: 1, #token: 2277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.02, #queue-req: 0, 
[2025-07-28 00:59:06 DP1 TP0] Decode batch. #running-req: 1, #token: 637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.76, #queue-req: 0, 
[2025-07-28 00:59:06 DP0 TP0] Decode batch. #running-req: 1, #token: 2317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.89, #queue-req: 0, 
[2025-07-28 00:59:06 DP1 TP0] Decode batch. #running-req: 1, #token: 677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.23, #queue-req: 0, 
[2025-07-28 00:59:06 DP0 TP0] Decode batch. #running-req: 1, #token: 2357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.01, #queue-req: 0, 
[2025-07-28 00:59:06 DP1 TP0] Decode batch. #running-req: 1, #token: 717, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.44, #queue-req: 0, 
[2025-07-28 00:59:06 DP0 TP0] Decode batch. #running-req: 1, #token: 2397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.52, #queue-req: 0, 
[2025-07-28 00:59:06 DP1 TP0] Decode batch. #running-req: 1, #token: 757, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.61, #queue-req: 0, 
[2025-07-28 00:59:06 DP0 TP0] Decode batch. #running-req: 1, #token: 2437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.95, #queue-req: 0, 
[2025-07-28 00:59:06 DP1 TP0] Decode batch. #running-req: 1, #token: 797, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.03, #queue-req: 0, 
[2025-07-28 00:59:07 DP0 TP0] Decode batch. #running-req: 1, #token: 2477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.10, #queue-req: 0, 
[2025-07-28 00:59:07 DP1 TP0] Decode batch. #running-req: 1, #token: 837, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.14, #queue-req: 0, 
[2025-07-28 00:59:07 DP0 TP0] Decode batch. #running-req: 1, #token: 2517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.05, #queue-req: 0, 
[2025-07-28 00:59:07 DP1 TP0] Decode batch. #running-req: 1, #token: 877, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.66, #queue-req: 0, 
[2025-07-28 00:59:07 DP0 TP0] Decode batch. #running-req: 1, #token: 2557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.37, #queue-req: 0, 
[2025-07-28 00:59:07 DP1 TP0] Decode batch. #running-req: 1, #token: 917, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.90, #queue-req: 0, 
[2025-07-28 00:59:07 DP0 TP0] Decode batch. #running-req: 1, #token: 2597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.65, #queue-req: 0, 
[2025-07-28 00:59:07 DP1 TP0] Decode batch. #running-req: 1, #token: 957, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.55, #queue-req: 0, 
[2025-07-28 00:59:07 DP0 TP0] Decode batch. #running-req: 1, #token: 2637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.13, #queue-req: 0, 
[2025-07-28 00:59:07 DP1 TP0] Decode batch. #running-req: 1, #token: 997, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.71, #queue-req: 0, 
[2025-07-28 00:59:07 DP0 TP0] Decode batch. #running-req: 1, #token: 2677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.37, #queue-req: 0, 
[2025-07-28 00:59:07 DP1 TP0] Decode batch. #running-req: 1, #token: 1037, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.33, #queue-req: 0, 
[2025-07-28 00:59:07] INFO:     127.0.0.1:45376 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:59:07 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 148, #cached-token: 88, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:59:07 DP0 TP0] Decode batch. #running-req: 2, #token: 2878, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.96, #queue-req: 0, 
[2025-07-28 00:59:08 DP0 TP0] Decode batch. #running-req: 2, #token: 2958, token usage: 0.00, cuda graph: True, gen throughput (token/s): 614.01, #queue-req: 0, 
[2025-07-28 00:59:08 DP0 TP0] Decode batch. #running-req: 2, #token: 3038, token usage: 0.00, cuda graph: True, gen throughput (token/s): 618.89, #queue-req: 0, 
[2025-07-28 00:59:08 DP0 TP0] Decode batch. #running-req: 2, #token: 3118, token usage: 0.00, cuda graph: True, gen throughput (token/s): 619.66, #queue-req: 0, 
[2025-07-28 00:59:08 DP0 TP0] Decode batch. #running-req: 2, #token: 3198, token usage: 0.00, cuda graph: True, gen throughput (token/s): 620.19, #queue-req: 0, 
[2025-07-28 00:59:08 DP0 TP0] Decode batch. #running-req: 2, #token: 3278, token usage: 0.00, cuda graph: True, gen throughput (token/s): 616.00, #queue-req: 0, 
[2025-07-28 00:59:08 DP0 TP0] Decode batch. #running-req: 2, #token: 3358, token usage: 0.00, cuda graph: True, gen throughput (token/s): 572.25, #queue-req: 0, 
[2025-07-28 00:59:08 DP0 TP0] Decode batch. #running-req: 2, #token: 3438, token usage: 0.00, cuda graph: True, gen throughput (token/s): 587.59, #queue-req: 0, 
[2025-07-28 00:59:09 DP0 TP0] Decode batch. #running-req: 2, #token: 3518, token usage: 0.00, cuda graph: True, gen throughput (token/s): 568.66, #queue-req: 0, 
[2025-07-28 00:59:09 DP0 TP0] Decode batch. #running-req: 2, #token: 3598, token usage: 0.00, cuda graph: True, gen throughput (token/s): 585.56, #queue-req: 0, 
[2025-07-28 00:59:09 DP0 TP0] Decode batch. #running-req: 2, #token: 3678, token usage: 0.00, cuda graph: True, gen throughput (token/s): 589.47, #queue-req: 0, 
[2025-07-28 00:59:09 DP0 TP0] Decode batch. #running-req: 2, #token: 3758, token usage: 0.00, cuda graph: True, gen throughput (token/s): 584.49, #queue-req: 0, 
[2025-07-28 00:59:09 DP0 TP0] Decode batch. #running-req: 2, #token: 3838, token usage: 0.00, cuda graph: True, gen throughput (token/s): 553.66, #queue-req: 0, 
[2025-07-28 00:59:09 DP0 TP0] Decode batch. #running-req: 2, #token: 3918, token usage: 0.00, cuda graph: True, gen throughput (token/s): 563.87, #queue-req: 0, 
[2025-07-28 00:59:09 DP0 TP0] Decode batch. #running-req: 2, #token: 3998, token usage: 0.00, cuda graph: True, gen throughput (token/s): 572.58, #queue-req: 0, 
[2025-07-28 00:59:09 DP0 TP0] Decode batch. #running-req: 2, #token: 4078, token usage: 0.00, cuda graph: True, gen throughput (token/s): 585.99, #queue-req: 0, 
[2025-07-28 00:59:10 DP0 TP0] Decode batch. #running-req: 2, #token: 4158, token usage: 0.00, cuda graph: True, gen throughput (token/s): 583.32, #queue-req: 0, 
[2025-07-28 00:59:10 DP0 TP0] Decode batch. #running-req: 2, #token: 4238, token usage: 0.00, cuda graph: True, gen throughput (token/s): 589.00, #queue-req: 0, 
[2025-07-28 00:59:10 DP0 TP0] Decode batch. #running-req: 2, #token: 4318, token usage: 0.00, cuda graph: True, gen throughput (token/s): 588.01, #queue-req: 0, 
[2025-07-28 00:59:10 DP0 TP0] Decode batch. #running-req: 2, #token: 4398, token usage: 0.00, cuda graph: True, gen throughput (token/s): 588.12, #queue-req: 0, 
[2025-07-28 00:59:10 DP0 TP0] Decode batch. #running-req: 2, #token: 4478, token usage: 0.00, cuda graph: True, gen throughput (token/s): 588.12, #queue-req: 0, 
[2025-07-28 00:59:10 DP0 TP0] Decode batch. #running-req: 2, #token: 4558, token usage: 0.00, cuda graph: True, gen throughput (token/s): 588.35, #queue-req: 0, 
[2025-07-28 00:59:10 DP0 TP0] Decode batch. #running-req: 2, #token: 4638, token usage: 0.00, cuda graph: True, gen throughput (token/s): 589.63, #queue-req: 0, 
[2025-07-28 00:59:11 DP0 TP0] Decode batch. #running-req: 2, #token: 4718, token usage: 0.00, cuda graph: True, gen throughput (token/s): 605.51, #queue-req: 0, 
[2025-07-28 00:59:11 DP0 TP0] Decode batch. #running-req: 2, #token: 4798, token usage: 0.00, cuda graph: True, gen throughput (token/s): 581.47, #queue-req: 0, 
[2025-07-28 00:59:11 DP0 TP0] Decode batch. #running-req: 2, #token: 4878, token usage: 0.00, cuda graph: True, gen throughput (token/s): 575.90, #queue-req: 0, 
[2025-07-28 00:59:11] INFO:     127.0.0.1:51506 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:59:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1286, token usage: 0.00, cuda graph: True, gen throughput (token/s): 531.78, #queue-req: 0, 
[2025-07-28 00:59:11 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 160, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:59:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1326, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.21, #queue-req: 0, 
[2025-07-28 00:59:11 DP1 TP0] Decode batch. #running-req: 1, #token: 271, token usage: 0.00, cuda graph: True, gen throughput (token/s): 10.35, #queue-req: 0, 
[2025-07-28 00:59:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1366, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.65, #queue-req: 0, 
[2025-07-28 00:59:11 DP1 TP0] Decode batch. #running-req: 1, #token: 311, token usage: 0.00, cuda graph: True, gen throughput (token/s): 279.10, #queue-req: 0, 
[2025-07-28 00:59:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1406, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.88, #queue-req: 0, 
[2025-07-28 00:59:11 DP1 TP0] Decode batch. #running-req: 1, #token: 351, token usage: 0.00, cuda graph: True, gen throughput (token/s): 288.89, #queue-req: 0, 
[2025-07-28 00:59:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1446, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.93, #queue-req: 0, 
[2025-07-28 00:59:12 DP1 TP0] Decode batch. #running-req: 1, #token: 391, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.50, #queue-req: 0, 
[2025-07-28 00:59:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1486, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.34, #queue-req: 0, 
[2025-07-28 00:59:12 DP1 TP0] Decode batch. #running-req: 1, #token: 431, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.18, #queue-req: 0, 
[2025-07-28 00:59:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1526, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.21, #queue-req: 0, 
[2025-07-28 00:59:12 DP1 TP0] Decode batch. #running-req: 1, #token: 471, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.81, #queue-req: 0, 
[2025-07-28 00:59:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1566, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.26, #queue-req: 0, 
[2025-07-28 00:59:12 DP1 TP0] Decode batch. #running-req: 1, #token: 511, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.29, #queue-req: 0, 
[2025-07-28 00:59:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1606, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.51, #queue-req: 0, 
[2025-07-28 00:59:12 DP1 TP0] Decode batch. #running-req: 1, #token: 551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.85, #queue-req: 0, 
[2025-07-28 00:59:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1646, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.87, #queue-req: 0, 
[2025-07-28 00:59:12 DP1 TP0] Decode batch. #running-req: 1, #token: 591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.51, #queue-req: 0, 
[2025-07-28 00:59:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1686, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.82, #queue-req: 0, 
[2025-07-28 00:59:12 DP1 TP0] Decode batch. #running-req: 1, #token: 631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.93, #queue-req: 0, 
[2025-07-28 00:59:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1726, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.80, #queue-req: 0, 
[2025-07-28 00:59:13 DP1 TP0] Decode batch. #running-req: 1, #token: 671, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.58, #queue-req: 0, 
[2025-07-28 00:59:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1766, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.30, #queue-req: 0, 
[2025-07-28 00:59:13 DP1 TP0] Decode batch. #running-req: 1, #token: 711, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.56, #queue-req: 0, 
[2025-07-28 00:59:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1806, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.33, #queue-req: 0, 
[2025-07-28 00:59:13 DP1 TP0] Decode batch. #running-req: 1, #token: 751, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.11, #queue-req: 0, 
[2025-07-28 00:59:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1846, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.75, #queue-req: 0, 
[2025-07-28 00:59:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1886, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.91, #queue-req: 0, 
[2025-07-28 00:59:13 DP1 TP0] Decode batch. #running-req: 1, #token: 791, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.14, #queue-req: 0, 
[2025-07-28 00:59:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1926, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.61, #queue-req: 0, 
[2025-07-28 00:59:13 DP1 TP0] Decode batch. #running-req: 1, #token: 831, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.05, #queue-req: 0, 
[2025-07-28 00:59:13] INFO:     127.0.0.1:45380 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:59:13 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 114, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:59:13 DP1 TP0] Decode batch. #running-req: 1, #token: 871, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.18, #queue-req: 0, 
[2025-07-28 00:59:13 DP0 TP0] Decode batch. #running-req: 1, #token: 220, token usage: 0.00, cuda graph: True, gen throughput (token/s): 192.99, #queue-req: 0, 
[2025-07-28 00:59:13 DP1 TP0] Decode batch. #running-req: 1, #token: 911, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.71, #queue-req: 0, 
[2025-07-28 00:59:13 DP0 TP0] Decode batch. #running-req: 1, #token: 260, token usage: 0.00, cuda graph: True, gen throughput (token/s): 324.96, #queue-req: 0, 
[2025-07-28 00:59:13 DP1 TP0] Decode batch. #running-req: 1, #token: 951, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.33, #queue-req: 0, 
[2025-07-28 00:59:14 DP0 TP0] Decode batch. #running-req: 1, #token: 300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.65, #queue-req: 0, 
[2025-07-28 00:59:14 DP1 TP0] Decode batch. #running-req: 1, #token: 991, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.72, #queue-req: 0, 
[2025-07-28 00:59:14 DP0 TP0] Decode batch. #running-req: 1, #token: 340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.45, #queue-req: 0, 
[2025-07-28 00:59:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1031, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.88, #queue-req: 0, 
[2025-07-28 00:59:14 DP0 TP0] Decode batch. #running-req: 1, #token: 380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.86, #queue-req: 0, 
[2025-07-28 00:59:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1071, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.49, #queue-req: 0, 
[2025-07-28 00:59:14 DP0 TP0] Decode batch. #running-req: 1, #token: 420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.28, #queue-req: 0, 
[2025-07-28 00:59:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1111, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.77, #queue-req: 0, 
[2025-07-28 00:59:14 DP0 TP0] Decode batch. #running-req: 1, #token: 460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.42, #queue-req: 0, 
[2025-07-28 00:59:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1151, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.32, #queue-req: 0, 
[2025-07-28 00:59:14 DP0 TP0] Decode batch. #running-req: 1, #token: 500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.41, #queue-req: 0, 
[2025-07-28 00:59:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1191, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.31, #queue-req: 0, 
[2025-07-28 00:59:14 DP0 TP0] Decode batch. #running-req: 1, #token: 540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.76, #queue-req: 0, 
[2025-07-28 00:59:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1231, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.38, #queue-req: 0, 
[2025-07-28 00:59:14 DP0 TP0] Decode batch. #running-req: 1, #token: 580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.29, #queue-req: 0, 
[2025-07-28 00:59:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1271, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.08, #queue-req: 0, 
[2025-07-28 00:59:15 DP0 TP0] Decode batch. #running-req: 1, #token: 620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.47, #queue-req: 0, 
[2025-07-28 00:59:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1311, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.55, #queue-req: 0, 
[2025-07-28 00:59:15 DP0 TP0] Decode batch. #running-req: 1, #token: 660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.81, #queue-req: 0, 
[2025-07-28 00:59:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1351, token usage: 0.00, cuda graph: True, gen throughput (token/s): 320.10, #queue-req: 0, 
[2025-07-28 00:59:15 DP0 TP0] Decode batch. #running-req: 1, #token: 700, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.01, #queue-req: 0, 
[2025-07-28 00:59:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1391, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.34, #queue-req: 0, 
[2025-07-28 00:59:15 DP0 TP0] Decode batch. #running-req: 1, #token: 740, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.59, #queue-req: 0, 
[2025-07-28 00:59:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1431, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.32, #queue-req: 0, 
[2025-07-28 00:59:15 DP0 TP0] Decode batch. #running-req: 1, #token: 780, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.79, #queue-req: 0, 
[2025-07-28 00:59:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1471, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.85, #queue-req: 0, 
[2025-07-28 00:59:15 DP0 TP0] Decode batch. #running-req: 1, #token: 820, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.43, #queue-req: 0, 
[2025-07-28 00:59:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1511, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.40, #queue-req: 0, 
[2025-07-28 00:59:15 DP0 TP0] Decode batch. #running-req: 1, #token: 860, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.83, #queue-req: 0, 
[2025-07-28 00:59:15 DP0 TP0] Decode batch. #running-req: 1, #token: 900, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.16, #queue-req: 0, 
[2025-07-28 00:59:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.96, #queue-req: 0, 
[2025-07-28 00:59:16 DP0 TP0] Decode batch. #running-req: 1, #token: 940, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.42, #queue-req: 0, 
[2025-07-28 00:59:16 DP1 TP0] Decode batch. #running-req: 1, #token: 1591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.64, #queue-req: 0, 
[2025-07-28 00:59:16 DP0 TP0] Decode batch. #running-req: 1, #token: 980, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.34, #queue-req: 0, 
[2025-07-28 00:59:16] INFO:     127.0.0.1:56956 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:59:16 DP1 TP0] Decode batch. #running-req: 1, #token: 1631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.69, #queue-req: 0, 
[2025-07-28 00:59:16 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 80, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:59:16 DP1 TP0] Decode batch. #running-req: 2, #token: 1788, token usage: 0.00, cuda graph: True, gen throughput (token/s): 407.96, #queue-req: 0, 
[2025-07-28 00:59:16 DP1 TP0] Decode batch. #running-req: 2, #token: 1868, token usage: 0.00, cuda graph: True, gen throughput (token/s): 631.62, #queue-req: 0, 
[2025-07-28 00:59:16 DP1 TP0] Decode batch. #running-req: 2, #token: 1948, token usage: 0.00, cuda graph: True, gen throughput (token/s): 632.96, #queue-req: 0, 
[2025-07-28 00:59:16 DP1 TP0] Decode batch. #running-req: 2, #token: 2028, token usage: 0.00, cuda graph: True, gen throughput (token/s): 625.26, #queue-req: 0, 
[2025-07-28 00:59:16 DP1 TP0] Decode batch. #running-req: 2, #token: 2108, token usage: 0.00, cuda graph: True, gen throughput (token/s): 633.22, #queue-req: 0, 
[2025-07-28 00:59:17 DP1 TP0] Decode batch. #running-req: 2, #token: 2188, token usage: 0.00, cuda graph: True, gen throughput (token/s): 633.13, #queue-req: 0, 
[2025-07-28 00:59:17 DP1 TP0] Decode batch. #running-req: 2, #token: 2268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 632.27, #queue-req: 0, 
[2025-07-28 00:59:17 DP1 TP0] Decode batch. #running-req: 2, #token: 2348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 627.00, #queue-req: 0, 
[2025-07-28 00:59:17] INFO:     127.0.0.1:45388 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:59:17 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 136, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:59:17 DP1 TP0] Decode batch. #running-req: 1, #token: 522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.91, #queue-req: 0, 
[2025-07-28 00:59:17 DP0 TP0] Decode batch. #running-req: 1, #token: 258, token usage: 0.00, cuda graph: True, gen throughput (token/s): 30.08, #queue-req: 0, 
[2025-07-28 00:59:17 DP1 TP0] Decode batch. #running-req: 1, #token: 562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.67, #queue-req: 0, 
[2025-07-28 00:59:17 DP0 TP0] Decode batch. #running-req: 1, #token: 298, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.00, #queue-req: 0, 
[2025-07-28 00:59:17 DP1 TP0] Decode batch. #running-req: 1, #token: 602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.06, #queue-req: 0, 
[2025-07-28 00:59:17 DP0 TP0] Decode batch. #running-req: 1, #token: 338, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.68, #queue-req: 0, 
[2025-07-28 00:59:17 DP1 TP0] Decode batch. #running-req: 1, #token: 642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.90, #queue-req: 0, 
[2025-07-28 00:59:17 DP0 TP0] Decode batch. #running-req: 1, #token: 378, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.87, #queue-req: 0, 
[2025-07-28 00:59:17 DP1 TP0] Decode batch. #running-req: 1, #token: 682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.45, #queue-req: 0, 
[2025-07-28 00:59:18 DP0 TP0] Decode batch. #running-req: 1, #token: 418, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.66, #queue-req: 0, 
[2025-07-28 00:59:18 DP1 TP0] Decode batch. #running-req: 1, #token: 722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.94, #queue-req: 0, 
[2025-07-28 00:59:18 DP0 TP0] Decode batch. #running-req: 1, #token: 458, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.72, #queue-req: 0, 
[2025-07-28 00:59:18 DP1 TP0] Decode batch. #running-req: 1, #token: 762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.09, #queue-req: 0, 
[2025-07-28 00:59:18 DP0 TP0] Decode batch. #running-req: 1, #token: 498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.52, #queue-req: 0, 
[2025-07-28 00:59:18 DP1 TP0] Decode batch. #running-req: 1, #token: 802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.89, #queue-req: 0, 
[2025-07-28 00:59:18 DP0 TP0] Decode batch. #running-req: 1, #token: 538, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.93, #queue-req: 0, 
[2025-07-28 00:59:18 DP1 TP0] Decode batch. #running-req: 1, #token: 842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.18, #queue-req: 0, 
[2025-07-28 00:59:18 DP0 TP0] Decode batch. #running-req: 1, #token: 578, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.80, #queue-req: 0, 
[2025-07-28 00:59:18 DP1 TP0] Decode batch. #running-req: 1, #token: 882, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.93, #queue-req: 0, 
[2025-07-28 00:59:18 DP0 TP0] Decode batch. #running-req: 1, #token: 618, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.89, #queue-req: 0, 
[2025-07-28 00:59:18 DP1 TP0] Decode batch. #running-req: 1, #token: 922, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.01, #queue-req: 0, 
[2025-07-28 00:59:18 DP0 TP0] Decode batch. #running-req: 1, #token: 658, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.41, #queue-req: 0, 
[2025-07-28 00:59:18 DP1 TP0] Decode batch. #running-req: 1, #token: 962, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.60, #queue-req: 0, 
[2025-07-28 00:59:18 DP0 TP0] Decode batch. #running-req: 1, #token: 698, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.27, #queue-req: 0, 
[2025-07-28 00:59:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1002, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.00, #queue-req: 0, 
[2025-07-28 00:59:19 DP0 TP0] Decode batch. #running-req: 1, #token: 738, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.68, #queue-req: 0, 
[2025-07-28 00:59:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1042, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.44, #queue-req: 0, 
[2025-07-28 00:59:19 DP0 TP0] Decode batch. #running-req: 1, #token: 778, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.40, #queue-req: 0, 
[2025-07-28 00:59:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1082, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.43, #queue-req: 0, 
[2025-07-28 00:59:19 DP0 TP0] Decode batch. #running-req: 1, #token: 818, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.56, #queue-req: 0, 
[2025-07-28 00:59:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1122, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.98, #queue-req: 0, 
[2025-07-28 00:59:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1162, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.49, #queue-req: 0, 
[2025-07-28 00:59:19 DP0 TP0] Decode batch. #running-req: 1, #token: 858, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.81, #queue-req: 0, 
[2025-07-28 00:59:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1202, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.41, #queue-req: 0, 
[2025-07-28 00:59:19 DP0 TP0] Decode batch. #running-req: 1, #token: 898, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.23, #queue-req: 0, 
[2025-07-28 00:59:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.32, #queue-req: 0, 
[2025-07-28 00:59:19 DP0 TP0] Decode batch. #running-req: 1, #token: 938, token usage: 0.00, cuda graph: True, gen throughput (token/s): 278.53, #queue-req: 0, 
[2025-07-28 00:59:19] INFO:     127.0.0.1:56970 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:59:19 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 137, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:59:19 DP0 TP0] Decode batch. #running-req: 1, #token: 978, token usage: 0.00, cuda graph: True, gen throughput (token/s): 275.80, #queue-req: 0, 
[2025-07-28 00:59:19 DP1 TP0] Decode batch. #running-req: 1, #token: 247, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.73, #queue-req: 0, 
[2025-07-28 00:59:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1018, token usage: 0.00, cuda graph: True, gen throughput (token/s): 275.03, #queue-req: 0, 
[2025-07-28 00:59:20 DP1 TP0] Decode batch. #running-req: 1, #token: 287, token usage: 0.00, cuda graph: True, gen throughput (token/s): 331.12, #queue-req: 0, 
[2025-07-28 00:59:20 DP1 TP0] Decode batch. #running-req: 1, #token: 327, token usage: 0.00, cuda graph: True, gen throughput (token/s): 334.75, #queue-req: 0, 
[2025-07-28 00:59:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1058, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.97, #queue-req: 0, 
[2025-07-28 00:59:20 DP1 TP0] Decode batch. #running-req: 1, #token: 367, token usage: 0.00, cuda graph: True, gen throughput (token/s): 335.18, #queue-req: 0, 
[2025-07-28 00:59:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1098, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.49, #queue-req: 0, 
[2025-07-28 00:59:20 DP1 TP0] Decode batch. #running-req: 1, #token: 407, token usage: 0.00, cuda graph: True, gen throughput (token/s): 333.02, #queue-req: 0, 
[2025-07-28 00:59:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1138, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.18, #queue-req: 0, 
[2025-07-28 00:59:20 DP1 TP0] Decode batch. #running-req: 1, #token: 447, token usage: 0.00, cuda graph: True, gen throughput (token/s): 335.53, #queue-req: 0, 
[2025-07-28 00:59:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1178, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.74, #queue-req: 0, 
[2025-07-28 00:59:20 DP1 TP0] Decode batch. #running-req: 1, #token: 487, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.43, #queue-req: 0, 
[2025-07-28 00:59:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1218, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.28, #queue-req: 0, 
[2025-07-28 00:59:20 DP1 TP0] Decode batch. #running-req: 1, #token: 527, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.08, #queue-req: 0, 
[2025-07-28 00:59:20] INFO:     127.0.0.1:56978 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:59:20 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 85, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:59:20 DP0 TP0] Decode batch. #running-req: 1, #token: 173, token usage: 0.00, cuda graph: True, gen throughput (token/s): 193.88, #queue-req: 0, 
[2025-07-28 00:59:20 DP1 TP0] Decode batch. #running-req: 1, #token: 567, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.53, #queue-req: 0, 
[2025-07-28 00:59:21 DP0 TP0] Decode batch. #running-req: 1, #token: 213, token usage: 0.00, cuda graph: True, gen throughput (token/s): 327.37, #queue-req: 0, 
[2025-07-28 00:59:21 DP1 TP0] Decode batch. #running-req: 1, #token: 607, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.58, #queue-req: 0, 
[2025-07-28 00:59:21 DP0 TP0] Decode batch. #running-req: 1, #token: 253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.08, #queue-req: 0, 
[2025-07-28 00:59:21 DP1 TP0] Decode batch. #running-req: 1, #token: 647, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.99, #queue-req: 0, 
[2025-07-28 00:59:21 DP0 TP0] Decode batch. #running-req: 1, #token: 293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.13, #queue-req: 0, 
[2025-07-28 00:59:21 DP1 TP0] Decode batch. #running-req: 1, #token: 687, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.26, #queue-req: 0, 
[2025-07-28 00:59:21 DP1 TP0] Decode batch. #running-req: 1, #token: 727, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.94, #queue-req: 0, 
[2025-07-28 00:59:21 DP0 TP0] Decode batch. #running-req: 1, #token: 333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.21, #queue-req: 0, 
[2025-07-28 00:59:21 DP1 TP0] Decode batch. #running-req: 1, #token: 767, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.41, #queue-req: 0, 
[2025-07-28 00:59:21 DP0 TP0] Decode batch. #running-req: 1, #token: 373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.69, #queue-req: 0, 
[2025-07-28 00:59:21 DP1 TP0] Decode batch. #running-req: 1, #token: 807, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.45, #queue-req: 0, 
[2025-07-28 00:59:21 DP0 TP0] Decode batch. #running-req: 1, #token: 413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.68, #queue-req: 0, 
[2025-07-28 00:59:21 DP1 TP0] Decode batch. #running-req: 1, #token: 847, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.51, #queue-req: 0, 
[2025-07-28 00:59:21 DP0 TP0] Decode batch. #running-req: 1, #token: 453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.36, #queue-req: 0, 
[2025-07-28 00:59:21 DP1 TP0] Decode batch. #running-req: 1, #token: 887, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.17, #queue-req: 0, 
[2025-07-28 00:59:21 DP0 TP0] Decode batch. #running-req: 1, #token: 493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.50, #queue-req: 0, 
[2025-07-28 00:59:22 DP1 TP0] Decode batch. #running-req: 1, #token: 927, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.06, #queue-req: 0, 
[2025-07-28 00:59:22 DP0 TP0] Decode batch. #running-req: 1, #token: 533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.20, #queue-req: 0, 
[2025-07-28 00:59:22 DP1 TP0] Decode batch. #running-req: 1, #token: 967, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.09, #queue-req: 0, 
[2025-07-28 00:59:22 DP0 TP0] Decode batch. #running-req: 1, #token: 573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.91, #queue-req: 0, 
[2025-07-28 00:59:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1007, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.40, #queue-req: 0, 
[2025-07-28 00:59:22 DP0 TP0] Decode batch. #running-req: 1, #token: 613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.82, #queue-req: 0, 
[2025-07-28 00:59:22] INFO:     127.0.0.1:56992 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:59:22 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 157, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:59:22 DP0 TP0] Decode batch. #running-req: 1, #token: 653, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.29, #queue-req: 0, 
[2025-07-28 00:59:22 DP1 TP0] Decode batch. #running-req: 1, #token: 249, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.32, #queue-req: 0, 
[2025-07-28 00:59:22 DP0 TP0] Decode batch. #running-req: 1, #token: 693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.55, #queue-req: 0, 
[2025-07-28 00:59:22 DP1 TP0] Decode batch. #running-req: 1, #token: 289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 336.95, #queue-req: 0, 
[2025-07-28 00:59:22 DP0 TP0] Decode batch. #running-req: 1, #token: 733, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.13, #queue-req: 0, 
[2025-07-28 00:59:22 DP1 TP0] Decode batch. #running-req: 1, #token: 329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 335.25, #queue-req: 0, 
[2025-07-28 00:59:22 DP0 TP0] Decode batch. #running-req: 1, #token: 773, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.98, #queue-req: 0, 
[2025-07-28 00:59:22 DP1 TP0] Decode batch. #running-req: 1, #token: 369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 335.54, #queue-req: 0, 
[2025-07-28 00:59:23 DP1 TP0] Decode batch. #running-req: 1, #token: 409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 335.76, #queue-req: 0, 
[2025-07-28 00:59:23 DP0 TP0] Decode batch. #running-req: 1, #token: 813, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.24, #queue-req: 0, 
[2025-07-28 00:59:23 DP1 TP0] Decode batch. #running-req: 1, #token: 449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 336.91, #queue-req: 0, 
[2025-07-28 00:59:23 DP0 TP0] Decode batch. #running-req: 1, #token: 853, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.72, #queue-req: 0, 
[2025-07-28 00:59:23 DP1 TP0] Decode batch. #running-req: 1, #token: 489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 334.63, #queue-req: 0, 
[2025-07-28 00:59:23 DP0 TP0] Decode batch. #running-req: 1, #token: 893, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.75, #queue-req: 0, 
[2025-07-28 00:59:23 DP1 TP0] Decode batch. #running-req: 1, #token: 529, token usage: 0.00, cuda graph: True, gen throughput (token/s): 332.49, #queue-req: 0, 
[2025-07-28 00:59:23 DP0 TP0] Decode batch. #running-req: 1, #token: 933, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.25, #queue-req: 0, 
[2025-07-28 00:59:23 DP1 TP0] Decode batch. #running-req: 1, #token: 569, token usage: 0.00, cuda graph: True, gen throughput (token/s): 335.25, #queue-req: 0, 
[2025-07-28 00:59:23 DP0 TP0] Decode batch. #running-req: 1, #token: 973, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.90, #queue-req: 0, 
[2025-07-28 00:59:23 DP1 TP0] Decode batch. #running-req: 1, #token: 609, token usage: 0.00, cuda graph: True, gen throughput (token/s): 335.26, #queue-req: 0, 
[2025-07-28 00:59:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1013, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.74, #queue-req: 0, 
[2025-07-28 00:59:23 DP1 TP0] Decode batch. #running-req: 1, #token: 649, token usage: 0.00, cuda graph: True, gen throughput (token/s): 333.55, #queue-req: 0, 
[2025-07-28 00:59:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1053, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.23, #queue-req: 0, 
[2025-07-28 00:59:23 DP1 TP0] Decode batch. #running-req: 1, #token: 689, token usage: 0.00, cuda graph: True, gen throughput (token/s): 333.03, #queue-req: 0, 
[2025-07-28 00:59:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1093, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.10, #queue-req: 0, 
[2025-07-28 00:59:23 DP1 TP0] Decode batch. #running-req: 1, #token: 729, token usage: 0.00, cuda graph: True, gen throughput (token/s): 330.82, #queue-req: 0, 
[2025-07-28 00:59:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1133, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.25, #queue-req: 0, 
[2025-07-28 00:59:24 DP1 TP0] Decode batch. #running-req: 1, #token: 769, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.98, #queue-req: 0, 
[2025-07-28 00:59:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1173, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.25, #queue-req: 0, 
[2025-07-28 00:59:24 DP1 TP0] Decode batch. #running-req: 1, #token: 809, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.06, #queue-req: 0, 
[2025-07-28 00:59:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1213, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.67, #queue-req: 0, 
[2025-07-28 00:59:24 DP1 TP0] Decode batch. #running-req: 1, #token: 849, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.62, #queue-req: 0, 
[2025-07-28 00:59:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.09, #queue-req: 0, 
[2025-07-28 00:59:24 DP1 TP0] Decode batch. #running-req: 1, #token: 889, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.81, #queue-req: 0, 
[2025-07-28 00:59:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.78, #queue-req: 0, 
[2025-07-28 00:59:24 DP1 TP0] Decode batch. #running-req: 1, #token: 929, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.34, #queue-req: 0, 
[2025-07-28 00:59:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.36, #queue-req: 0, 
[2025-07-28 00:59:24 DP1 TP0] Decode batch. #running-req: 1, #token: 969, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.08, #queue-req: 0, 
[2025-07-28 00:59:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.56, #queue-req: 0, 
[2025-07-28 00:59:24 DP1 TP0] Decode batch. #running-req: 1, #token: 1009, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.07, #queue-req: 0, 
[2025-07-28 00:59:25 DP0 TP0] Decode batch. #running-req: 1, #token: 0, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.35, #queue-req: 0, 
[2025-07-28 00:59:25] INFO:     127.0.0.1:56994 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:59:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1049, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.16, #queue-req: 0, 
[2025-07-28 00:59:25 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 150, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:59:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1089, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.55, #queue-req: 0, 
[2025-07-28 00:59:25 DP0 TP0] Decode batch. #running-req: 1, #token: 279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 202.24, #queue-req: 0, 
[2025-07-28 00:59:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1129, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.57, #queue-req: 0, 
[2025-07-28 00:59:25 DP0 TP0] Decode batch. #running-req: 1, #token: 319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.52, #queue-req: 0, 
[2025-07-28 00:59:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1169, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.29, #queue-req: 0, 
[2025-07-28 00:59:25 DP0 TP0] Decode batch. #running-req: 1, #token: 359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.05, #queue-req: 0, 
[2025-07-28 00:59:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1209, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.83, #queue-req: 0, 
[2025-07-28 00:59:25 DP0 TP0] Decode batch. #running-req: 1, #token: 399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.78, #queue-req: 0, 
[2025-07-28 00:59:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1249, token usage: 0.00, cuda graph: True, gen throughput (token/s): 262.34, #queue-req: 0, 
[2025-07-28 00:59:25 DP0 TP0] Decode batch. #running-req: 1, #token: 439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.63, #queue-req: 0, 
[2025-07-28 00:59:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.07, #queue-req: 0, 
[2025-07-28 00:59:25 DP0 TP0] Decode batch. #running-req: 1, #token: 479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.91, #queue-req: 0, 
[2025-07-28 00:59:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.21, #queue-req: 0, 
[2025-07-28 00:59:25 DP0 TP0] Decode batch. #running-req: 1, #token: 519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.47, #queue-req: 0, 
[2025-07-28 00:59:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.46, #queue-req: 0, 
[2025-07-28 00:59:26 DP0 TP0] Decode batch. #running-req: 1, #token: 559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.60, #queue-req: 0, 
[2025-07-28 00:59:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.24, #queue-req: 0, 
[2025-07-28 00:59:26 DP0 TP0] Decode batch. #running-req: 1, #token: 599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.92, #queue-req: 0, 
[2025-07-28 00:59:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.72, #queue-req: 0, 
[2025-07-28 00:59:26 DP0 TP0] Decode batch. #running-req: 1, #token: 639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.39, #queue-req: 0, 
[2025-07-28 00:59:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.31, #queue-req: 0, 
[2025-07-28 00:59:26 DP0 TP0] Decode batch. #running-req: 1, #token: 679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.86, #queue-req: 0, 
[2025-07-28 00:59:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1529, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.93, #queue-req: 0, 
[2025-07-28 00:59:26 DP0 TP0] Decode batch. #running-req: 1, #token: 719, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.31, #queue-req: 0, 
[2025-07-28 00:59:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1569, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.43, #queue-req: 0, 
[2025-07-28 00:59:26 DP0 TP0] Decode batch. #running-req: 1, #token: 759, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.08, #queue-req: 0, 
[2025-07-28 00:59:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1609, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.89, #queue-req: 0, 
[2025-07-28 00:59:26 DP0 TP0] Decode batch. #running-req: 1, #token: 799, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.62, #queue-req: 0, 
[2025-07-28 00:59:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1649, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.42, #queue-req: 0, 
[2025-07-28 00:59:27 DP0 TP0] Decode batch. #running-req: 1, #token: 839, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.27, #queue-req: 0, 
[2025-07-28 00:59:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1689, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.01, #queue-req: 0, 
[2025-07-28 00:59:27 DP0 TP0] Decode batch. #running-req: 1, #token: 879, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.71, #queue-req: 0, 
[2025-07-28 00:59:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1729, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.83, #queue-req: 0, 
[2025-07-28 00:59:27 DP0 TP0] Decode batch. #running-req: 1, #token: 919, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.07, #queue-req: 0, 
[2025-07-28 00:59:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1769, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.05, #queue-req: 0, 
[2025-07-28 00:59:27 DP0 TP0] Decode batch. #running-req: 1, #token: 959, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.99, #queue-req: 0, 
[2025-07-28 00:59:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1809, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.90, #queue-req: 0, 
[2025-07-28 00:59:27 DP0 TP0] Decode batch. #running-req: 1, #token: 999, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.44, #queue-req: 0, 
[2025-07-28 00:59:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1849, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.99, #queue-req: 0, 
[2025-07-28 00:59:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1039, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.56, #queue-req: 0, 
[2025-07-28 00:59:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1889, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.04, #queue-req: 0, 
[2025-07-28 00:59:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1079, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.45, #queue-req: 0, 
[2025-07-28 00:59:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1929, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.98, #queue-req: 0, 
[2025-07-28 00:59:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1119, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.68, #queue-req: 0, 
[2025-07-28 00:59:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1969, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.39, #queue-req: 0, 
[2025-07-28 00:59:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1159, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.00, #queue-req: 0, 
[2025-07-28 00:59:28 DP1 TP0] Decode batch. #running-req: 1, #token: 2009, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.35, #queue-req: 0, 
[2025-07-28 00:59:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1199, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.09, #queue-req: 0, 
[2025-07-28 00:59:28 DP1 TP0] Decode batch. #running-req: 1, #token: 2049, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.12, #queue-req: 0, 
[2025-07-28 00:59:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1239, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.70, #queue-req: 0, 
[2025-07-28 00:59:28 DP1 TP0] Decode batch. #running-req: 1, #token: 2089, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.55, #queue-req: 0, 
[2025-07-28 00:59:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.04, #queue-req: 0, 
[2025-07-28 00:59:28 DP1 TP0] Decode batch. #running-req: 1, #token: 2129, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.57, #queue-req: 0, 
[2025-07-28 00:59:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.18, #queue-req: 0, 
[2025-07-28 00:59:28 DP1 TP0] Decode batch. #running-req: 1, #token: 2169, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.34, #queue-req: 0, 
[2025-07-28 00:59:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.71, #queue-req: 0, 
[2025-07-28 00:59:28 DP1 TP0] Decode batch. #running-req: 1, #token: 2209, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.42, #queue-req: 0, 
[2025-07-28 00:59:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.41, #queue-req: 0, 
[2025-07-28 00:59:28 DP1 TP0] Decode batch. #running-req: 1, #token: 2249, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.14, #queue-req: 0, 
[2025-07-28 00:59:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.71, #queue-req: 0, 
[2025-07-28 00:59:28 DP1 TP0] Decode batch. #running-req: 1, #token: 2289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 320.86, #queue-req: 0, 
[2025-07-28 00:59:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.64, #queue-req: 0, 
[2025-07-28 00:59:29 DP1 TP0] Decode batch. #running-req: 1, #token: 2329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.98, #queue-req: 0, 
[2025-07-28 00:59:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.45, #queue-req: 0, 
[2025-07-28 00:59:29 DP1 TP0] Decode batch. #running-req: 1, #token: 2369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.65, #queue-req: 0, 
[2025-07-28 00:59:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.61, #queue-req: 0, 
[2025-07-28 00:59:29 DP1 TP0] Decode batch. #running-req: 1, #token: 2409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.69, #queue-req: 0, 
[2025-07-28 00:59:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.44, #queue-req: 0, 
[2025-07-28 00:59:29 DP1 TP0] Decode batch. #running-req: 1, #token: 2449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.47, #queue-req: 0, 
[2025-07-28 00:59:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.05, #queue-req: 0, 
[2025-07-28 00:59:29 DP1 TP0] Decode batch. #running-req: 1, #token: 2489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.15, #queue-req: 0, 
[2025-07-28 00:59:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.71, #queue-req: 0, 
[2025-07-28 00:59:29 DP1 TP0] Decode batch. #running-req: 1, #token: 2529, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.44, #queue-req: 0, 
[2025-07-28 00:59:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1719, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.65, #queue-req: 0, 
[2025-07-28 00:59:29 DP1 TP0] Decode batch. #running-req: 1, #token: 2569, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.54, #queue-req: 0, 
[2025-07-28 00:59:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1759, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.56, #queue-req: 0, 
[2025-07-28 00:59:29 DP1 TP0] Decode batch. #running-req: 1, #token: 2609, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.42, #queue-req: 0, 
[2025-07-28 00:59:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1799, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.52, #queue-req: 0, 
[2025-07-28 00:59:30 DP1 TP0] Decode batch. #running-req: 1, #token: 2649, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.44, #queue-req: 0, 
[2025-07-28 00:59:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1839, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.32, #queue-req: 0, 
[2025-07-28 00:59:30 DP1 TP0] Decode batch. #running-req: 1, #token: 2689, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.24, #queue-req: 0, 
[2025-07-28 00:59:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1879, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.05, #queue-req: 0, 
[2025-07-28 00:59:30 DP1 TP0] Decode batch. #running-req: 1, #token: 2729, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.66, #queue-req: 0, 
[2025-07-28 00:59:30 DP1 TP0] Decode batch. #running-req: 1, #token: 2769, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.43, #queue-req: 0, 
[2025-07-28 00:59:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1919, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.51, #queue-req: 0, 
[2025-07-28 00:59:30 DP1 TP0] Decode batch. #running-req: 1, #token: 2809, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.71, #queue-req: 0, 
[2025-07-28 00:59:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1959, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.72, #queue-req: 0, 
[2025-07-28 00:59:30 DP1 TP0] Decode batch. #running-req: 1, #token: 2849, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.89, #queue-req: 0, 
[2025-07-28 00:59:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1999, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.02, #queue-req: 0, 
[2025-07-28 00:59:30 DP1 TP0] Decode batch. #running-req: 1, #token: 2889, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.74, #queue-req: 0, 
[2025-07-28 00:59:30 DP0 TP0] Decode batch. #running-req: 1, #token: 2039, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.76, #queue-req: 0, 
[2025-07-28 00:59:31 DP1 TP0] Decode batch. #running-req: 1, #token: 2929, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.85, #queue-req: 0, 
[2025-07-28 00:59:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2079, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.33, #queue-req: 0, 
[2025-07-28 00:59:31 DP1 TP0] Decode batch. #running-req: 1, #token: 2969, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.51, #queue-req: 0, 
[2025-07-28 00:59:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2119, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.31, #queue-req: 0, 
[2025-07-28 00:59:31 DP1 TP0] Decode batch. #running-req: 1, #token: 3009, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.29, #queue-req: 0, 
[2025-07-28 00:59:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2159, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.48, #queue-req: 0, 
[2025-07-28 00:59:31 DP1 TP0] Decode batch. #running-req: 1, #token: 3049, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.71, #queue-req: 0, 
[2025-07-28 00:59:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2199, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.06, #queue-req: 0, 
[2025-07-28 00:59:31 DP1 TP0] Decode batch. #running-req: 1, #token: 3089, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.39, #queue-req: 0, 
[2025-07-28 00:59:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2239, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.08, #queue-req: 0, 
[2025-07-28 00:59:31 DP1 TP0] Decode batch. #running-req: 1, #token: 3129, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.95, #queue-req: 0, 
[2025-07-28 00:59:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.34, #queue-req: 0, 
[2025-07-28 00:59:31 DP1 TP0] Decode batch. #running-req: 1, #token: 3169, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.17, #queue-req: 0, 
[2025-07-28 00:59:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.64, #queue-req: 0, 
[2025-07-28 00:59:31 DP1 TP0] Decode batch. #running-req: 1, #token: 3209, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.84, #queue-req: 0, 
[2025-07-28 00:59:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.37, #queue-req: 0, 
[2025-07-28 00:59:32 DP1 TP0] Decode batch. #running-req: 1, #token: 3249, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.23, #queue-req: 0, 
[2025-07-28 00:59:32 DP0 TP0] Decode batch. #running-req: 1, #token: 2399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.32, #queue-req: 0, 
[2025-07-28 00:59:32 DP1 TP0] Decode batch. #running-req: 1, #token: 3289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.20, #queue-req: 0, 
[2025-07-28 00:59:32 DP0 TP0] Decode batch. #running-req: 1, #token: 2439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.93, #queue-req: 0, 
[2025-07-28 00:59:32 DP1 TP0] Decode batch. #running-req: 1, #token: 3329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.99, #queue-req: 0, 
[2025-07-28 00:59:32 DP0 TP0] Decode batch. #running-req: 1, #token: 2479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.86, #queue-req: 0, 
[2025-07-28 00:59:32 DP1 TP0] Decode batch. #running-req: 1, #token: 3369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.29, #queue-req: 0, 
[2025-07-28 00:59:32 DP0 TP0] Decode batch. #running-req: 1, #token: 2519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.67, #queue-req: 0, 
[2025-07-28 00:59:32 DP1 TP0] Decode batch. #running-req: 1, #token: 3409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.43, #queue-req: 0, 
[2025-07-28 00:59:32 DP0 TP0] Decode batch. #running-req: 1, #token: 2559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.54, #queue-req: 0, 
[2025-07-28 00:59:32 DP1 TP0] Decode batch. #running-req: 1, #token: 3449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.12, #queue-req: 0, 
[2025-07-28 00:59:32 DP0 TP0] Decode batch. #running-req: 1, #token: 2599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.01, #queue-req: 0, 
[2025-07-28 00:59:32 DP1 TP0] Decode batch. #running-req: 1, #token: 3489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.59, #queue-req: 0, 
[2025-07-28 00:59:32 DP0 TP0] Decode batch. #running-req: 1, #token: 2639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.20, #queue-req: 0, 
[2025-07-28 00:59:32 DP1 TP0] Decode batch. #running-req: 1, #token: 3529, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.81, #queue-req: 0, 
[2025-07-28 00:59:33 DP0 TP0] Decode batch. #running-req: 1, #token: 2679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.24, #queue-req: 0, 
[2025-07-28 00:59:33 DP1 TP0] Decode batch. #running-req: 1, #token: 3569, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.15, #queue-req: 0, 
[2025-07-28 00:59:33 DP0 TP0] Decode batch. #running-req: 1, #token: 2719, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.28, #queue-req: 0, 
[2025-07-28 00:59:33 DP1 TP0] Decode batch. #running-req: 1, #token: 3609, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.91, #queue-req: 0, 
[2025-07-28 00:59:33 DP0 TP0] Decode batch. #running-req: 1, #token: 2759, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.84, #queue-req: 0, 
[2025-07-28 00:59:33 DP1 TP0] Decode batch. #running-req: 1, #token: 3649, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.92, #queue-req: 0, 
[2025-07-28 00:59:33 DP0 TP0] Decode batch. #running-req: 1, #token: 2799, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.13, #queue-req: 0, 
[2025-07-28 00:59:33 DP1 TP0] Decode batch. #running-req: 1, #token: 3689, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.01, #queue-req: 0, 
[2025-07-28 00:59:33 DP0 TP0] Decode batch. #running-req: 1, #token: 2839, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.94, #queue-req: 0, 
[2025-07-28 00:59:33 DP1 TP0] Decode batch. #running-req: 1, #token: 3729, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.91, #queue-req: 0, 
[2025-07-28 00:59:33 DP0 TP0] Decode batch. #running-req: 1, #token: 2879, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.50, #queue-req: 0, 
[2025-07-28 00:59:33 DP1 TP0] Decode batch. #running-req: 1, #token: 3769, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.19, #queue-req: 0, 
[2025-07-28 00:59:33 DP0 TP0] Decode batch. #running-req: 1, #token: 2919, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.35, #queue-req: 0, 
[2025-07-28 00:59:33 DP1 TP0] Decode batch. #running-req: 1, #token: 3809, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.39, #queue-req: 0, 
[2025-07-28 00:59:33 DP0 TP0] Decode batch. #running-req: 1, #token: 2959, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.44, #queue-req: 0, 
[2025-07-28 00:59:33 DP1 TP0] Decode batch. #running-req: 1, #token: 3849, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.12, #queue-req: 0, 
[2025-07-28 00:59:34 DP0 TP0] Decode batch. #running-req: 1, #token: 2999, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.84, #queue-req: 0, 
[2025-07-28 00:59:34 DP1 TP0] Decode batch. #running-req: 1, #token: 3889, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.90, #queue-req: 0, 
[2025-07-28 00:59:34 DP0 TP0] Decode batch. #running-req: 1, #token: 3039, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.73, #queue-req: 0, 
[2025-07-28 00:59:34 DP1 TP0] Decode batch. #running-req: 1, #token: 3929, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.48, #queue-req: 0, 
[2025-07-28 00:59:34 DP0 TP0] Decode batch. #running-req: 1, #token: 3079, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.65, #queue-req: 0, 
[2025-07-28 00:59:34 DP1 TP0] Decode batch. #running-req: 1, #token: 3969, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.00, #queue-req: 0, 
[2025-07-28 00:59:34 DP0 TP0] Decode batch. #running-req: 1, #token: 3119, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.90, #queue-req: 0, 
[2025-07-28 00:59:34 DP1 TP0] Decode batch. #running-req: 1, #token: 4009, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.73, #queue-req: 0, 
[2025-07-28 00:59:34 DP0 TP0] Decode batch. #running-req: 1, #token: 3159, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.35, #queue-req: 0, 
[2025-07-28 00:59:34 DP1 TP0] Decode batch. #running-req: 1, #token: 4049, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.63, #queue-req: 0, 
[2025-07-28 00:59:34 DP0 TP0] Decode batch. #running-req: 1, #token: 3199, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.39, #queue-req: 0, 
[2025-07-28 00:59:34 DP1 TP0] Decode batch. #running-req: 1, #token: 4089, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.05, #queue-req: 0, 
[2025-07-28 00:59:34 DP0 TP0] Decode batch. #running-req: 1, #token: 3239, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.04, #queue-req: 0, 
[2025-07-28 00:59:34 DP1 TP0] Decode batch. #running-req: 1, #token: 4129, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.47, #queue-req: 0, 
[2025-07-28 00:59:34 DP0 TP0] Decode batch. #running-req: 1, #token: 3279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.65, #queue-req: 0, 
[2025-07-28 00:59:35 DP1 TP0] Decode batch. #running-req: 1, #token: 4169, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.75, #queue-req: 0, 
[2025-07-28 00:59:35 DP0 TP0] Decode batch. #running-req: 1, #token: 3319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.51, #queue-req: 0, 
[2025-07-28 00:59:35 DP1 TP0] Decode batch. #running-req: 1, #token: 4209, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.40, #queue-req: 0, 
[2025-07-28 00:59:35 DP0 TP0] Decode batch. #running-req: 1, #token: 3359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.22, #queue-req: 0, 
[2025-07-28 00:59:35 DP1 TP0] Decode batch. #running-req: 1, #token: 4249, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.58, #queue-req: 0, 
[2025-07-28 00:59:35 DP0 TP0] Decode batch. #running-req: 1, #token: 3399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.66, #queue-req: 0, 
[2025-07-28 00:59:35 DP1 TP0] Decode batch. #running-req: 1, #token: 4289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.26, #queue-req: 0, 
[2025-07-28 00:59:35 DP0 TP0] Decode batch. #running-req: 1, #token: 3439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.41, #queue-req: 0, 
[2025-07-28 00:59:35 DP1 TP0] Decode batch. #running-req: 1, #token: 4329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.81, #queue-req: 0, 
[2025-07-28 00:59:35 DP0 TP0] Decode batch. #running-req: 1, #token: 3479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.14, #queue-req: 0, 
[2025-07-28 00:59:35 DP1 TP0] Decode batch. #running-req: 1, #token: 4369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.72, #queue-req: 0, 
[2025-07-28 00:59:35 DP0 TP0] Decode batch. #running-req: 1, #token: 3519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.16, #queue-req: 0, 
[2025-07-28 00:59:35 DP1 TP0] Decode batch. #running-req: 1, #token: 4409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.04, #queue-req: 0, 
[2025-07-28 00:59:35 DP0 TP0] Decode batch. #running-req: 1, #token: 3559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.32, #queue-req: 0, 
[2025-07-28 00:59:35 DP1 TP0] Decode batch. #running-req: 1, #token: 4449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.80, #queue-req: 0, 
[2025-07-28 00:59:36 DP0 TP0] Decode batch. #running-req: 1, #token: 3599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.70, #queue-req: 0, 
[2025-07-28 00:59:36 DP1 TP0] Decode batch. #running-req: 1, #token: 4489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.47, #queue-req: 0, 
[2025-07-28 00:59:36 DP0 TP0] Decode batch. #running-req: 1, #token: 3639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.72, #queue-req: 0, 
[2025-07-28 00:59:36 DP1 TP0] Decode batch. #running-req: 1, #token: 4529, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.26, #queue-req: 0, 
[2025-07-28 00:59:36 DP1 TP0] Decode batch. #running-req: 1, #token: 4569, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.04, #queue-req: 0, 
[2025-07-28 00:59:36 DP0 TP0] Decode batch. #running-req: 1, #token: 3679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.16, #queue-req: 0, 
[2025-07-28 00:59:36 DP1 TP0] Decode batch. #running-req: 1, #token: 4609, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.22, #queue-req: 0, 
[2025-07-28 00:59:36 DP0 TP0] Decode batch. #running-req: 1, #token: 3719, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.38, #queue-req: 0, 
[2025-07-28 00:59:36 DP1 TP0] Decode batch. #running-req: 1, #token: 4649, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.28, #queue-req: 0, 
[2025-07-28 00:59:36 DP0 TP0] Decode batch. #running-req: 1, #token: 3759, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.19, #queue-req: 0, 
[2025-07-28 00:59:36 DP1 TP0] Decode batch. #running-req: 1, #token: 4689, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.81, #queue-req: 0, 
[2025-07-28 00:59:36 DP0 TP0] Decode batch. #running-req: 1, #token: 3799, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.68, #queue-req: 0, 
[2025-07-28 00:59:36 DP1 TP0] Decode batch. #running-req: 1, #token: 4729, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.10, #queue-req: 0, 
[2025-07-28 00:59:36 DP0 TP0] Decode batch. #running-req: 1, #token: 3839, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.87, #queue-req: 0, 
[2025-07-28 00:59:36 DP1 TP0] Decode batch. #running-req: 1, #token: 4769, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.26, #queue-req: 0, 
[2025-07-28 00:59:36 DP0 TP0] Decode batch. #running-req: 1, #token: 3879, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.27, #queue-req: 0, 
[2025-07-28 00:59:37 DP1 TP0] Decode batch. #running-req: 1, #token: 4809, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.45, #queue-req: 0, 
[2025-07-28 00:59:37 DP0 TP0] Decode batch. #running-req: 1, #token: 3919, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.04, #queue-req: 0, 
[2025-07-28 00:59:37 DP1 TP0] Decode batch. #running-req: 1, #token: 4849, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.95, #queue-req: 0, 
[2025-07-28 00:59:37 DP0 TP0] Decode batch. #running-req: 1, #token: 3959, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.60, #queue-req: 0, 
[2025-07-28 00:59:37 DP1 TP0] Decode batch. #running-req: 1, #token: 4889, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.34, #queue-req: 0, 
[2025-07-28 00:59:37 DP0 TP0] Decode batch. #running-req: 1, #token: 3999, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.07, #queue-req: 0, 
[2025-07-28 00:59:37 DP1 TP0] Decode batch. #running-req: 1, #token: 4929, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.81, #queue-req: 0, 
[2025-07-28 00:59:37 DP0 TP0] Decode batch. #running-req: 1, #token: 4039, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.09, #queue-req: 0, 
[2025-07-28 00:59:37 DP1 TP0] Decode batch. #running-req: 1, #token: 4969, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.94, #queue-req: 0, 
[2025-07-28 00:59:37 DP0 TP0] Decode batch. #running-req: 1, #token: 4079, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.98, #queue-req: 0, 
[2025-07-28 00:59:37 DP1 TP0] Decode batch. #running-req: 1, #token: 5009, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.64, #queue-req: 0, 
[2025-07-28 00:59:37 DP0 TP0] Decode batch. #running-req: 1, #token: 4119, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.75, #queue-req: 0, 
[2025-07-28 00:59:37 DP1 TP0] Decode batch. #running-req: 1, #token: 5049, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.02, #queue-req: 0, 
[2025-07-28 00:59:37 DP0 TP0] Decode batch. #running-req: 1, #token: 4159, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.18, #queue-req: 0, 
[2025-07-28 00:59:37 DP1 TP0] Decode batch. #running-req: 1, #token: 5089, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.60, #queue-req: 0, 
[2025-07-28 00:59:37 DP0 TP0] Decode batch. #running-req: 1, #token: 4199, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.85, #queue-req: 0, 
[2025-07-28 00:59:38 DP1 TP0] Decode batch. #running-req: 1, #token: 5129, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.52, #queue-req: 0, 
[2025-07-28 00:59:38 DP0 TP0] Decode batch. #running-req: 1, #token: 4239, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.27, #queue-req: 0, 
[2025-07-28 00:59:38 DP1 TP0] Decode batch. #running-req: 1, #token: 5169, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.99, #queue-req: 0, 
[2025-07-28 00:59:38 DP0 TP0] Decode batch. #running-req: 1, #token: 4279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.92, #queue-req: 0, 
[2025-07-28 00:59:38 DP1 TP0] Decode batch. #running-req: 1, #token: 5209, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.86, #queue-req: 0, 
[2025-07-28 00:59:38 DP0 TP0] Decode batch. #running-req: 1, #token: 4319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.83, #queue-req: 0, 
[2025-07-28 00:59:38 DP1 TP0] Decode batch. #running-req: 1, #token: 5249, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.88, #queue-req: 0, 
[2025-07-28 00:59:38 DP0 TP0] Decode batch. #running-req: 1, #token: 4359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.86, #queue-req: 0, 
[2025-07-28 00:59:38 DP1 TP0] Decode batch. #running-req: 1, #token: 5289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.49, #queue-req: 0, 
[2025-07-28 00:59:38 DP0 TP0] Decode batch. #running-req: 1, #token: 4399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.14, #queue-req: 0, 
[2025-07-28 00:59:38 DP1 TP0] Decode batch. #running-req: 1, #token: 5329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.27, #queue-req: 0, 
[2025-07-28 00:59:38 DP0 TP0] Decode batch. #running-req: 1, #token: 4439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.42, #queue-req: 0, 
[2025-07-28 00:59:38 DP1 TP0] Decode batch. #running-req: 1, #token: 5369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.18, #queue-req: 0, 
[2025-07-28 00:59:38 DP0 TP0] Decode batch. #running-req: 1, #token: 4479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.94, #queue-req: 0, 
[2025-07-28 00:59:38 DP1 TP0] Decode batch. #running-req: 1, #token: 5409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.43, #queue-req: 0, 
[2025-07-28 00:59:38 DP0 TP0] Decode batch. #running-req: 1, #token: 4519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.80, #queue-req: 0, 
[2025-07-28 00:59:39 DP1 TP0] Decode batch. #running-req: 1, #token: 5449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.43, #queue-req: 0, 
[2025-07-28 00:59:39 DP0 TP0] Decode batch. #running-req: 1, #token: 4559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.27, #queue-req: 0, 
[2025-07-28 00:59:39 DP1 TP0] Decode batch. #running-req: 1, #token: 5489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.37, #queue-req: 0, 
[2025-07-28 00:59:39 DP0 TP0] Decode batch. #running-req: 1, #token: 4599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.67, #queue-req: 0, 
[2025-07-28 00:59:39 DP1 TP0] Decode batch. #running-req: 1, #token: 5529, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.64, #queue-req: 0, 
[2025-07-28 00:59:39 DP0 TP0] Decode batch. #running-req: 1, #token: 4639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.74, #queue-req: 0, 
[2025-07-28 00:59:39 DP1 TP0] Decode batch. #running-req: 1, #token: 5569, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.79, #queue-req: 0, 
[2025-07-28 00:59:39 DP0 TP0] Decode batch. #running-req: 1, #token: 4679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.08, #queue-req: 0, 
[2025-07-28 00:59:39 DP1 TP0] Decode batch. #running-req: 1, #token: 5609, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.90, #queue-req: 0, 
[2025-07-28 00:59:39 DP0 TP0] Decode batch. #running-req: 1, #token: 4719, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.25, #queue-req: 0, 
[2025-07-28 00:59:39 DP1 TP0] Decode batch. #running-req: 1, #token: 5649, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.82, #queue-req: 0, 
[2025-07-28 00:59:39 DP0 TP0] Decode batch. #running-req: 1, #token: 4759, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.64, #queue-req: 0, 
[2025-07-28 00:59:39 DP1 TP0] Decode batch. #running-req: 1, #token: 5689, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.01, #queue-req: 0, 
[2025-07-28 00:59:39 DP0 TP0] Decode batch. #running-req: 1, #token: 4799, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.00, #queue-req: 0, 
[2025-07-28 00:59:39 DP1 TP0] Decode batch. #running-req: 1, #token: 5729, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.77, #queue-req: 0, 
[2025-07-28 00:59:40 DP0 TP0] Decode batch. #running-req: 1, #token: 4839, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.08, #queue-req: 0, 
[2025-07-28 00:59:40 DP1 TP0] Decode batch. #running-req: 1, #token: 5769, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.83, #queue-req: 0, 
[2025-07-28 00:59:40 DP0 TP0] Decode batch. #running-req: 1, #token: 4879, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.66, #queue-req: 0, 
[2025-07-28 00:59:40 DP1 TP0] Decode batch. #running-req: 1, #token: 5809, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.95, #queue-req: 0, 
[2025-07-28 00:59:40 DP0 TP0] Decode batch. #running-req: 1, #token: 4919, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.23, #queue-req: 0, 
[2025-07-28 00:59:40 DP1 TP0] Decode batch. #running-req: 1, #token: 5849, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.90, #queue-req: 0, 
[2025-07-28 00:59:40 DP0 TP0] Decode batch. #running-req: 1, #token: 4959, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.48, #queue-req: 0, 
[2025-07-28 00:59:40 DP1 TP0] Decode batch. #running-req: 1, #token: 5889, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.89, #queue-req: 0, 
[2025-07-28 00:59:40 DP0 TP0] Decode batch. #running-req: 1, #token: 4999, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.31, #queue-req: 0, 
[2025-07-28 00:59:40 DP1 TP0] Decode batch. #running-req: 1, #token: 5929, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.02, #queue-req: 0, 
[2025-07-28 00:59:40 DP0 TP0] Decode batch. #running-req: 1, #token: 5039, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.23, #queue-req: 0, 
[2025-07-28 00:59:40 DP1 TP0] Decode batch. #running-req: 1, #token: 5969, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.19, #queue-req: 0, 
[2025-07-28 00:59:40 DP0 TP0] Decode batch. #running-req: 1, #token: 5079, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.07, #queue-req: 0, 
[2025-07-28 00:59:40 DP1 TP0] Decode batch. #running-req: 1, #token: 6009, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.45, #queue-req: 0, 
[2025-07-28 00:59:40 DP0 TP0] Decode batch. #running-req: 1, #token: 5119, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.60, #queue-req: 0, 
[2025-07-28 00:59:40 DP1 TP0] Decode batch. #running-req: 1, #token: 6049, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.57, #queue-req: 0, 
[2025-07-28 00:59:41 DP0 TP0] Decode batch. #running-req: 1, #token: 5159, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.42, #queue-req: 0, 
[2025-07-28 00:59:41 DP1 TP0] Decode batch. #running-req: 1, #token: 6089, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.36, #queue-req: 0, 
[2025-07-28 00:59:41 DP0 TP0] Decode batch. #running-req: 1, #token: 5199, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.25, #queue-req: 0, 
[2025-07-28 00:59:41 DP1 TP0] Decode batch. #running-req: 1, #token: 6129, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.56, #queue-req: 0, 
[2025-07-28 00:59:41 DP0 TP0] Decode batch. #running-req: 1, #token: 5239, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.76, #queue-req: 0, 
[2025-07-28 00:59:41 DP1 TP0] Decode batch. #running-req: 1, #token: 6169, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.33, #queue-req: 0, 
[2025-07-28 00:59:41 DP0 TP0] Decode batch. #running-req: 1, #token: 5279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.20, #queue-req: 0, 
[2025-07-28 00:59:41 DP1 TP0] Decode batch. #running-req: 1, #token: 6209, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.04, #queue-req: 0, 
[2025-07-28 00:59:41 DP0 TP0] Decode batch. #running-req: 1, #token: 5319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.14, #queue-req: 0, 
[2025-07-28 00:59:41 DP1 TP0] Decode batch. #running-req: 1, #token: 6249, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.01, #queue-req: 0, 
[2025-07-28 00:59:41 DP0 TP0] Decode batch. #running-req: 1, #token: 5359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.31, #queue-req: 0, 
[2025-07-28 00:59:41 DP1 TP0] Decode batch. #running-req: 1, #token: 6289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.15, #queue-req: 0, 
[2025-07-28 00:59:41 DP0 TP0] Decode batch. #running-req: 1, #token: 5399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.83, #queue-req: 0, 
[2025-07-28 00:59:41 DP1 TP0] Decode batch. #running-req: 1, #token: 6329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.50, #queue-req: 0, 
[2025-07-28 00:59:41 DP0 TP0] Decode batch. #running-req: 1, #token: 5439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.61, #queue-req: 0, 
[2025-07-28 00:59:42 DP1 TP0] Decode batch. #running-req: 1, #token: 6369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.11, #queue-req: 0, 
[2025-07-28 00:59:42 DP0 TP0] Decode batch. #running-req: 1, #token: 5479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.77, #queue-req: 0, 
[2025-07-28 00:59:42 DP1 TP0] Decode batch. #running-req: 1, #token: 6409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.64, #queue-req: 0, 
[2025-07-28 00:59:42 DP0 TP0] Decode batch. #running-req: 1, #token: 5519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.08, #queue-req: 0, 
[2025-07-28 00:59:42 DP1 TP0] Decode batch. #running-req: 1, #token: 6449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.49, #queue-req: 0, 
[2025-07-28 00:59:42 DP0 TP0] Decode batch. #running-req: 1, #token: 5559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.98, #queue-req: 0, 
[2025-07-28 00:59:42 DP1 TP0] Decode batch. #running-req: 1, #token: 6489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.31, #queue-req: 0, 
[2025-07-28 00:59:42 DP0 TP0] Decode batch. #running-req: 1, #token: 5599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.55, #queue-req: 0, 
[2025-07-28 00:59:42 DP1 TP0] Decode batch. #running-req: 1, #token: 6529, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.65, #queue-req: 0, 
[2025-07-28 00:59:42 DP0 TP0] Decode batch. #running-req: 1, #token: 5639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.29, #queue-req: 0, 
[2025-07-28 00:59:42 DP1 TP0] Decode batch. #running-req: 1, #token: 6569, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.80, #queue-req: 0, 
[2025-07-28 00:59:42 DP0 TP0] Decode batch. #running-req: 1, #token: 5679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.18, #queue-req: 0, 
[2025-07-28 00:59:42 DP1 TP0] Decode batch. #running-req: 1, #token: 6609, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.63, #queue-req: 0, 
[2025-07-28 00:59:42 DP0 TP0] Decode batch. #running-req: 1, #token: 5719, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.09, #queue-req: 0, 
[2025-07-28 00:59:42 DP1 TP0] Decode batch. #running-req: 1, #token: 6649, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.00, #queue-req: 0, 
[2025-07-28 00:59:42 DP0 TP0] Decode batch. #running-req: 1, #token: 5759, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.56, #queue-req: 0, 
[2025-07-28 00:59:43 DP1 TP0] Decode batch. #running-req: 1, #token: 6689, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.43, #queue-req: 0, 
[2025-07-28 00:59:43 DP0 TP0] Decode batch. #running-req: 1, #token: 5799, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.15, #queue-req: 0, 
[2025-07-28 00:59:43 DP1 TP0] Decode batch. #running-req: 1, #token: 6729, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.38, #queue-req: 0, 
[2025-07-28 00:59:43 DP0 TP0] Decode batch. #running-req: 1, #token: 5839, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.44, #queue-req: 0, 
[2025-07-28 00:59:43 DP1 TP0] Decode batch. #running-req: 1, #token: 6769, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.17, #queue-req: 0, 
[2025-07-28 00:59:43 DP0 TP0] Decode batch. #running-req: 1, #token: 5879, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.97, #queue-req: 0, 
[2025-07-28 00:59:43 DP1 TP0] Decode batch. #running-req: 1, #token: 6809, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.03, #queue-req: 0, 
[2025-07-28 00:59:43 DP0 TP0] Decode batch. #running-req: 1, #token: 5919, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.39, #queue-req: 0, 
[2025-07-28 00:59:43 DP1 TP0] Decode batch. #running-req: 1, #token: 6849, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.59, #queue-req: 0, 
[2025-07-28 00:59:43 DP0 TP0] Decode batch. #running-req: 1, #token: 5959, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.60, #queue-req: 0, 
[2025-07-28 00:59:43 DP1 TP0] Decode batch. #running-req: 1, #token: 6889, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.96, #queue-req: 0, 
[2025-07-28 00:59:43 DP0 TP0] Decode batch. #running-req: 1, #token: 5999, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.79, #queue-req: 0, 
[2025-07-28 00:59:43 DP1 TP0] Decode batch. #running-req: 1, #token: 6929, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.09, #queue-req: 0, 
[2025-07-28 00:59:43 DP0 TP0] Decode batch. #running-req: 1, #token: 6039, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.97, #queue-req: 0, 
[2025-07-28 00:59:43 DP1 TP0] Decode batch. #running-req: 1, #token: 6969, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.53, #queue-req: 0, 
[2025-07-28 00:59:44 DP0 TP0] Decode batch. #running-req: 1, #token: 6079, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.85, #queue-req: 0, 
[2025-07-28 00:59:44 DP1 TP0] Decode batch. #running-req: 1, #token: 7009, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.52, #queue-req: 0, 
[2025-07-28 00:59:44 DP0 TP0] Decode batch. #running-req: 1, #token: 6119, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.84, #queue-req: 0, 
[2025-07-28 00:59:44 DP1 TP0] Decode batch. #running-req: 1, #token: 7049, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.07, #queue-req: 0, 
[2025-07-28 00:59:44 DP0 TP0] Decode batch. #running-req: 1, #token: 6159, token usage: 0.00, cuda graph: True, gen throughput (token/s): 288.03, #queue-req: 0, 
[2025-07-28 00:59:44 DP1 TP0] Decode batch. #running-req: 1, #token: 7089, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.05, #queue-req: 0, 
[2025-07-28 00:59:44 DP0 TP0] Decode batch. #running-req: 1, #token: 6199, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.81, #queue-req: 0, 
[2025-07-28 00:59:44 DP1 TP0] Decode batch. #running-req: 1, #token: 7129, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.96, #queue-req: 0, 
[2025-07-28 00:59:44 DP0 TP0] Decode batch. #running-req: 1, #token: 6239, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.21, #queue-req: 0, 
[2025-07-28 00:59:44 DP1 TP0] Decode batch. #running-req: 1, #token: 7169, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.80, #queue-req: 0, 
[2025-07-28 00:59:44 DP0 TP0] Decode batch. #running-req: 1, #token: 6279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.71, #queue-req: 0, 
[2025-07-28 00:59:44 DP1 TP0] Decode batch. #running-req: 1, #token: 7209, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.87, #queue-req: 0, 
[2025-07-28 00:59:44 DP0 TP0] Decode batch. #running-req: 1, #token: 6319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.02, #queue-req: 0, 
[2025-07-28 00:59:44 DP1 TP0] Decode batch. #running-req: 1, #token: 7249, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.26, #queue-req: 0, 
[2025-07-28 00:59:44 DP0 TP0] Decode batch. #running-req: 1, #token: 6359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.56, #queue-req: 0, 
[2025-07-28 00:59:44 DP1 TP0] Decode batch. #running-req: 1, #token: 7289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.13, #queue-req: 0, 
[2025-07-28 00:59:45 DP0 TP0] Decode batch. #running-req: 1, #token: 6399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.09, #queue-req: 0, 
[2025-07-28 00:59:45 DP1 TP0] Decode batch. #running-req: 1, #token: 7329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.44, #queue-req: 0, 
[2025-07-28 00:59:45] INFO:     127.0.0.1:47736 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:59:45 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 111, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:59:45 DP0 TP0] Decode batch. #running-req: 1, #token: 6439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.44, #queue-req: 0, 
[2025-07-28 00:59:45 DP1 TP0] Decode batch. #running-req: 1, #token: 210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 187.35, #queue-req: 0, 
[2025-07-28 00:59:45 DP0 TP0] Decode batch. #running-req: 1, #token: 6479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.85, #queue-req: 0, 
[2025-07-28 00:59:45 DP1 TP0] Decode batch. #running-req: 1, #token: 250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 327.88, #queue-req: 0, 
[2025-07-28 00:59:45 DP0 TP0] Decode batch. #running-req: 1, #token: 6519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.10, #queue-req: 0, 
[2025-07-28 00:59:45 DP1 TP0] Decode batch. #running-req: 1, #token: 290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.88, #queue-req: 0, 
[2025-07-28 00:59:45 DP0 TP0] Decode batch. #running-req: 1, #token: 6559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.69, #queue-req: 0, 
[2025-07-28 00:59:45 DP1 TP0] Decode batch. #running-req: 1, #token: 330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.19, #queue-req: 0, 
[2025-07-28 00:59:45 DP0 TP0] Decode batch. #running-req: 1, #token: 6599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.50, #queue-req: 0, 
[2025-07-28 00:59:45 DP1 TP0] Decode batch. #running-req: 1, #token: 370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.11, #queue-req: 0, 
[2025-07-28 00:59:45 DP0 TP0] Decode batch. #running-req: 1, #token: 6639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.46, #queue-req: 0, 
[2025-07-28 00:59:45 DP1 TP0] Decode batch. #running-req: 1, #token: 410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.42, #queue-req: 0, 
[2025-07-28 00:59:45 DP0 TP0] Decode batch. #running-req: 1, #token: 6679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.87, #queue-req: 0, 
[2025-07-28 00:59:46 DP1 TP0] Decode batch. #running-req: 1, #token: 450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.74, #queue-req: 0, 
[2025-07-28 00:59:46 DP0 TP0] Decode batch. #running-req: 1, #token: 6719, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.57, #queue-req: 0, 
[2025-07-28 00:59:46 DP1 TP0] Decode batch. #running-req: 1, #token: 490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.48, #queue-req: 0, 
[2025-07-28 00:59:46 DP0 TP0] Decode batch. #running-req: 1, #token: 6759, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.96, #queue-req: 0, 
[2025-07-28 00:59:46 DP1 TP0] Decode batch. #running-req: 1, #token: 530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.84, #queue-req: 0, 
[2025-07-28 00:59:46 DP0 TP0] Decode batch. #running-req: 1, #token: 6799, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.95, #queue-req: 0, 
[2025-07-28 00:59:46 DP1 TP0] Decode batch. #running-req: 1, #token: 570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.71, #queue-req: 0, 
[2025-07-28 00:59:46 DP0 TP0] Decode batch. #running-req: 1, #token: 6839, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.54, #queue-req: 0, 
[2025-07-28 00:59:46 DP1 TP0] Decode batch. #running-req: 1, #token: 610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.47, #queue-req: 0, 
[2025-07-28 00:59:46 DP0 TP0] Decode batch. #running-req: 1, #token: 6879, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.08, #queue-req: 0, 
[2025-07-28 00:59:46 DP1 TP0] Decode batch. #running-req: 1, #token: 650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.30, #queue-req: 0, 
[2025-07-28 00:59:46 DP0 TP0] Decode batch. #running-req: 1, #token: 6919, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.36, #queue-req: 0, 
[2025-07-28 00:59:46 DP1 TP0] Decode batch. #running-req: 1, #token: 690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.37, #queue-req: 0, 
[2025-07-28 00:59:46 DP0 TP0] Decode batch. #running-req: 1, #token: 6959, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.97, #queue-req: 0, 
[2025-07-28 00:59:46 DP1 TP0] Decode batch. #running-req: 1, #token: 730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.25, #queue-req: 0, 
[2025-07-28 00:59:47 DP0 TP0] Decode batch. #running-req: 1, #token: 6999, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.05, #queue-req: 0, 
[2025-07-28 00:59:47 DP1 TP0] Decode batch. #running-req: 1, #token: 770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.98, #queue-req: 0, 
[2025-07-28 00:59:47 DP0 TP0] Decode batch. #running-req: 1, #token: 7039, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.86, #queue-req: 0, 
[2025-07-28 00:59:47 DP1 TP0] Decode batch. #running-req: 1, #token: 810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.40, #queue-req: 0, 
[2025-07-28 00:59:47 DP0 TP0] Decode batch. #running-req: 1, #token: 7079, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.37, #queue-req: 0, 
[2025-07-28 00:59:47 DP1 TP0] Decode batch. #running-req: 1, #token: 850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.12, #queue-req: 0, 
[2025-07-28 00:59:47 DP0 TP0] Decode batch. #running-req: 1, #token: 7119, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.30, #queue-req: 0, 
[2025-07-28 00:59:47 DP1 TP0] Decode batch. #running-req: 1, #token: 890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.97, #queue-req: 0, 
[2025-07-28 00:59:47 DP0 TP0] Decode batch. #running-req: 1, #token: 7159, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.89, #queue-req: 0, 
[2025-07-28 00:59:47 DP1 TP0] Decode batch. #running-req: 1, #token: 930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.72, #queue-req: 0, 
[2025-07-28 00:59:47 DP0 TP0] Decode batch. #running-req: 1, #token: 7199, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.01, #queue-req: 0, 
[2025-07-28 00:59:47 DP1 TP0] Decode batch. #running-req: 1, #token: 970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.74, #queue-req: 0, 
[2025-07-28 00:59:47 DP0 TP0] Decode batch. #running-req: 1, #token: 7239, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.16, #queue-req: 0, 
[2025-07-28 00:59:47 DP1 TP0] Decode batch. #running-req: 1, #token: 1010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.12, #queue-req: 0, 
[2025-07-28 00:59:47 DP0 TP0] Decode batch. #running-req: 1, #token: 7279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.92, #queue-req: 0, 
[2025-07-28 00:59:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.63, #queue-req: 0, 
[2025-07-28 00:59:48 DP0 TP0] Decode batch. #running-req: 1, #token: 7319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.04, #queue-req: 0, 
[2025-07-28 00:59:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.91, #queue-req: 0, 
[2025-07-28 00:59:48 DP0 TP0] Decode batch. #running-req: 1, #token: 7359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.31, #queue-req: 0, 
[2025-07-28 00:59:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.34, #queue-req: 0, 
[2025-07-28 00:59:48 DP0 TP0] Decode batch. #running-req: 1, #token: 7399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.64, #queue-req: 0, 
[2025-07-28 00:59:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 257.43, #queue-req: 0, 
[2025-07-28 00:59:48 DP0 TP0] Decode batch. #running-req: 1, #token: 7439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.20, #queue-req: 0, 
[2025-07-28 00:59:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.27, #queue-req: 0, 
[2025-07-28 00:59:48 DP0 TP0] Decode batch. #running-req: 1, #token: 7479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.86, #queue-req: 0, 
[2025-07-28 00:59:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.53, #queue-req: 0, 
[2025-07-28 00:59:48 DP0 TP0] Decode batch. #running-req: 1, #token: 7519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.37, #queue-req: 0, 
[2025-07-28 00:59:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.97, #queue-req: 0, 
[2025-07-28 00:59:48 DP0 TP0] Decode batch. #running-req: 1, #token: 7559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.69, #queue-req: 0, 
[2025-07-28 00:59:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.22, #queue-req: 0, 
[2025-07-28 00:59:48 DP0 TP0] Decode batch. #running-req: 1, #token: 7599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.49, #queue-req: 0, 
[2025-07-28 00:59:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.40, #queue-req: 0, 
[2025-07-28 00:59:49 DP0 TP0] Decode batch. #running-req: 1, #token: 7639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.31, #queue-req: 0, 
[2025-07-28 00:59:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.72, #queue-req: 0, 
[2025-07-28 00:59:49 DP0 TP0] Decode batch. #running-req: 1, #token: 7679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.47, #queue-req: 0, 
[2025-07-28 00:59:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.91, #queue-req: 0, 
[2025-07-28 00:59:49 DP0 TP0] Decode batch. #running-req: 1, #token: 7719, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.63, #queue-req: 0, 
[2025-07-28 00:59:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.12, #queue-req: 0, 
[2025-07-28 00:59:49 DP0 TP0] Decode batch. #running-req: 1, #token: 7759, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.41, #queue-req: 0, 
[2025-07-28 00:59:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.42, #queue-req: 0, 
[2025-07-28 00:59:49 DP0 TP0] Decode batch. #running-req: 1, #token: 7799, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.04, #queue-req: 0, 
[2025-07-28 00:59:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.67, #queue-req: 0, 
[2025-07-28 00:59:49 DP0 TP0] Decode batch. #running-req: 1, #token: 7839, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.03, #queue-req: 0, 
[2025-07-28 00:59:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.01, #queue-req: 0, 
[2025-07-28 00:59:49 DP0 TP0] Decode batch. #running-req: 1, #token: 7879, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.98, #queue-req: 0, 
[2025-07-28 00:59:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.25, #queue-req: 0, 
[2025-07-28 00:59:49 DP0 TP0] Decode batch. #running-req: 1, #token: 7919, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.91, #queue-req: 0, 
[2025-07-28 00:59:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.93, #queue-req: 0, 
[2025-07-28 00:59:50 DP0 TP0] Decode batch. #running-req: 1, #token: 7959, token usage: 0.00, cuda graph: True, gen throughput (token/s): 272.41, #queue-req: 0, 
[2025-07-28 00:59:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.72, #queue-req: 0, 
[2025-07-28 00:59:50 DP0 TP0] Decode batch. #running-req: 1, #token: 7999, token usage: 0.00, cuda graph: True, gen throughput (token/s): 270.54, #queue-req: 0, 
[2025-07-28 00:59:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.63, #queue-req: 0, 
[2025-07-28 00:59:50 DP0 TP0] Decode batch. #running-req: 1, #token: 8039, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.72, #queue-req: 0, 
[2025-07-28 00:59:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 324.40, #queue-req: 0, 
[2025-07-28 00:59:50 DP0 TP0] Decode batch. #running-req: 1, #token: 8079, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.99, #queue-req: 0, 
[2025-07-28 00:59:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 333.46, #queue-req: 0, 
[2025-07-28 00:59:50 DP0 TP0] Decode batch. #running-req: 1, #token: 8119, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.77, #queue-req: 0, 
[2025-07-28 00:59:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 333.05, #queue-req: 0, 
[2025-07-28 00:59:50 DP0 TP0] Decode batch. #running-req: 1, #token: 8159, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.86, #queue-req: 0, 
[2025-07-28 00:59:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 329.71, #queue-req: 0, 
[2025-07-28 00:59:50 DP0 TP0] Decode batch. #running-req: 1, #token: 8199, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.66, #queue-req: 0, 
[2025-07-28 00:59:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 330.12, #queue-req: 0, 
[2025-07-28 00:59:51 DP0 TP0] Decode batch. #running-req: 1, #token: 8239, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.97, #queue-req: 0, 
[2025-07-28 00:59:51 DP1 TP0] Decode batch. #running-req: 1, #token: 2010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 330.28, #queue-req: 0, 
[2025-07-28 00:59:51 DP0 TP0] Decode batch. #running-req: 1, #token: 8279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.34, #queue-req: 0, 
[2025-07-28 00:59:51 DP1 TP0] Decode batch. #running-req: 1, #token: 2050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 320.75, #queue-req: 0, 
[2025-07-28 00:59:51 DP0 TP0] Decode batch. #running-req: 1, #token: 8319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.17, #queue-req: 0, 
[2025-07-28 00:59:51 DP1 TP0] Decode batch. #running-req: 1, #token: 2090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.63, #queue-req: 0, 
[2025-07-28 00:59:51 DP0 TP0] Decode batch. #running-req: 1, #token: 8359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.39, #queue-req: 0, 
[2025-07-28 00:59:51 DP1 TP0] Decode batch. #running-req: 1, #token: 2130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.32, #queue-req: 0, 
[2025-07-28 00:59:51 DP0 TP0] Decode batch. #running-req: 1, #token: 8399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.91, #queue-req: 0, 
[2025-07-28 00:59:51 DP1 TP0] Decode batch. #running-req: 1, #token: 2170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.12, #queue-req: 0, 
[2025-07-28 00:59:51] INFO:     127.0.0.1:47752 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:59:51 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 95, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:59:51 DP1 TP0] Decode batch. #running-req: 1, #token: 2210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.21, #queue-req: 0, 
[2025-07-28 00:59:51 DP0 TP0] Decode batch. #running-req: 1, #token: 189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 191.13, #queue-req: 0, 
[2025-07-28 00:59:51 DP1 TP0] Decode batch. #running-req: 1, #token: 2250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.92, #queue-req: 0, 
[2025-07-28 00:59:51 DP0 TP0] Decode batch. #running-req: 1, #token: 229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.33, #queue-req: 0, 
[2025-07-28 00:59:51 DP1 TP0] Decode batch. #running-req: 1, #token: 2290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 320.23, #queue-req: 0, 
[2025-07-28 00:59:52 DP0 TP0] Decode batch. #running-req: 1, #token: 269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.55, #queue-req: 0, 
[2025-07-28 00:59:52 DP1 TP0] Decode batch. #running-req: 1, #token: 2330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.78, #queue-req: 0, 
[2025-07-28 00:59:52 DP0 TP0] Decode batch. #running-req: 1, #token: 309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.97, #queue-req: 0, 
[2025-07-28 00:59:52 DP1 TP0] Decode batch. #running-req: 1, #token: 2370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.17, #queue-req: 0, 
[2025-07-28 00:59:52 DP0 TP0] Decode batch. #running-req: 1, #token: 349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.49, #queue-req: 0, 
[2025-07-28 00:59:52 DP1 TP0] Decode batch. #running-req: 1, #token: 2410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.67, #queue-req: 0, 
[2025-07-28 00:59:52 DP0 TP0] Decode batch. #running-req: 1, #token: 389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.62, #queue-req: 0, 
[2025-07-28 00:59:52 DP1 TP0] Decode batch. #running-req: 1, #token: 2450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.27, #queue-req: 0, 
[2025-07-28 00:59:52 DP0 TP0] Decode batch. #running-req: 1, #token: 429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.39, #queue-req: 0, 
[2025-07-28 00:59:52 DP1 TP0] Decode batch. #running-req: 1, #token: 2490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.76, #queue-req: 0, 
[2025-07-28 00:59:52 DP0 TP0] Decode batch. #running-req: 1, #token: 469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.74, #queue-req: 0, 
[2025-07-28 00:59:52 DP1 TP0] Decode batch. #running-req: 1, #token: 2530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.80, #queue-req: 0, 
[2025-07-28 00:59:52 DP0 TP0] Decode batch. #running-req: 1, #token: 509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.36, #queue-req: 0, 
[2025-07-28 00:59:52 DP1 TP0] Decode batch. #running-req: 1, #token: 2570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.92, #queue-req: 0, 
[2025-07-28 00:59:52 DP1 TP0] Decode batch. #running-req: 1, #token: 2610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.47, #queue-req: 0, 
[2025-07-28 00:59:52 DP0 TP0] Decode batch. #running-req: 1, #token: 549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.76, #queue-req: 0, 
[2025-07-28 00:59:53 DP1 TP0] Decode batch. #running-req: 1, #token: 2650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.64, #queue-req: 0, 
[2025-07-28 00:59:53 DP0 TP0] Decode batch. #running-req: 1, #token: 589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.43, #queue-req: 0, 
[2025-07-28 00:59:53 DP1 TP0] Decode batch. #running-req: 1, #token: 2690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.98, #queue-req: 0, 
[2025-07-28 00:59:53 DP0 TP0] Decode batch. #running-req: 1, #token: 629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.90, #queue-req: 0, 
[2025-07-28 00:59:53 DP1 TP0] Decode batch. #running-req: 1, #token: 2730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.24, #queue-req: 0, 
[2025-07-28 00:59:53 DP0 TP0] Decode batch. #running-req: 1, #token: 669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.20, #queue-req: 0, 
[2025-07-28 00:59:53 DP1 TP0] Decode batch. #running-req: 1, #token: 2770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.53, #queue-req: 0, 
[2025-07-28 00:59:53 DP0 TP0] Decode batch. #running-req: 1, #token: 709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.61, #queue-req: 0, 
[2025-07-28 00:59:53 DP1 TP0] Decode batch. #running-req: 1, #token: 2810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.95, #queue-req: 0, 
[2025-07-28 00:59:53 DP0 TP0] Decode batch. #running-req: 1, #token: 749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.83, #queue-req: 0, 
[2025-07-28 00:59:53 DP1 TP0] Decode batch. #running-req: 1, #token: 2850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.53, #queue-req: 0, 
[2025-07-28 00:59:53 DP0 TP0] Decode batch. #running-req: 1, #token: 789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.19, #queue-req: 0, 
[2025-07-28 00:59:53 DP1 TP0] Decode batch. #running-req: 1, #token: 2890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.36, #queue-req: 0, 
[2025-07-28 00:59:53 DP0 TP0] Decode batch. #running-req: 1, #token: 829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.40, #queue-req: 0, 
[2025-07-28 00:59:53 DP1 TP0] Decode batch. #running-req: 1, #token: 2930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.40, #queue-req: 0, 
[2025-07-28 00:59:54 DP0 TP0] Decode batch. #running-req: 1, #token: 869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.78, #queue-req: 0, 
[2025-07-28 00:59:54 DP1 TP0] Decode batch. #running-req: 1, #token: 2970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.62, #queue-req: 0, 
[2025-07-28 00:59:54 DP0 TP0] Decode batch. #running-req: 1, #token: 909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.54, #queue-req: 0, 
[2025-07-28 00:59:54 DP1 TP0] Decode batch. #running-req: 1, #token: 3010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.77, #queue-req: 0, 
[2025-07-28 00:59:54 DP0 TP0] Decode batch. #running-req: 1, #token: 949, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.41, #queue-req: 0, 
[2025-07-28 00:59:54 DP1 TP0] Decode batch. #running-req: 1, #token: 3050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.21, #queue-req: 0, 
[2025-07-28 00:59:54 DP0 TP0] Decode batch. #running-req: 1, #token: 989, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.92, #queue-req: 0, 
[2025-07-28 00:59:54 DP1 TP0] Decode batch. #running-req: 1, #token: 3090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.61, #queue-req: 0, 
[2025-07-28 00:59:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1029, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.65, #queue-req: 0, 
[2025-07-28 00:59:54 DP1 TP0] Decode batch. #running-req: 1, #token: 3130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.76, #queue-req: 0, 
[2025-07-28 00:59:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1069, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.88, #queue-req: 0, 
[2025-07-28 00:59:54 DP1 TP0] Decode batch. #running-req: 1, #token: 3170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.68, #queue-req: 0, 
[2025-07-28 00:59:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1109, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.07, #queue-req: 0, 
[2025-07-28 00:59:54 DP1 TP0] Decode batch. #running-req: 1, #token: 3210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.13, #queue-req: 0, 
[2025-07-28 00:59:55 DP1 TP0] Decode batch. #running-req: 1, #token: 3250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.54, #queue-req: 0, 
[2025-07-28 00:59:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1149, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.25, #queue-req: 0, 
[2025-07-28 00:59:55 DP1 TP0] Decode batch. #running-req: 1, #token: 3290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 321.04, #queue-req: 0, 
[2025-07-28 00:59:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.06, #queue-req: 0, 
[2025-07-28 00:59:55 DP1 TP0] Decode batch. #running-req: 1, #token: 3330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.86, #queue-req: 0, 
[2025-07-28 00:59:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.62, #queue-req: 0, 
[2025-07-28 00:59:55 DP1 TP0] Decode batch. #running-req: 1, #token: 3370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.57, #queue-req: 0, 
[2025-07-28 00:59:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.95, #queue-req: 0, 
[2025-07-28 00:59:55 DP1 TP0] Decode batch. #running-req: 1, #token: 3410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.01, #queue-req: 0, 
[2025-07-28 00:59:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.45, #queue-req: 0, 
[2025-07-28 00:59:55 DP1 TP0] Decode batch. #running-req: 1, #token: 3450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.24, #queue-req: 0, 
[2025-07-28 00:59:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.08, #queue-req: 0, 
[2025-07-28 00:59:55 DP1 TP0] Decode batch. #running-req: 1, #token: 3490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.57, #queue-req: 0, 
[2025-07-28 00:59:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.63, #queue-req: 0, 
[2025-07-28 00:59:55 DP1 TP0] Decode batch. #running-req: 1, #token: 3530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.46, #queue-req: 0, 
[2025-07-28 00:59:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.88, #queue-req: 0, 
[2025-07-28 00:59:56 DP1 TP0] Decode batch. #running-req: 1, #token: 3570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.47, #queue-req: 0, 
[2025-07-28 00:59:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.92, #queue-req: 0, 
[2025-07-28 00:59:56 DP1 TP0] Decode batch. #running-req: 1, #token: 3610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.24, #queue-req: 0, 
[2025-07-28 00:59:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.78, #queue-req: 0, 
[2025-07-28 00:59:56 DP1 TP0] Decode batch. #running-req: 1, #token: 3650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.39, #queue-req: 0, 
[2025-07-28 00:59:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.24, #queue-req: 0, 
[2025-07-28 00:59:56 DP1 TP0] Decode batch. #running-req: 1, #token: 3690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.22, #queue-req: 0, 
[2025-07-28 00:59:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.25, #queue-req: 0, 
[2025-07-28 00:59:56 DP1 TP0] Decode batch. #running-req: 1, #token: 3730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.58, #queue-req: 0, 
[2025-07-28 00:59:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.28, #queue-req: 0, 
[2025-07-28 00:59:56 DP1 TP0] Decode batch. #running-req: 1, #token: 3770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.07, #queue-req: 0, 
[2025-07-28 00:59:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 323.18, #queue-req: 0, 
[2025-07-28 00:59:56 DP1 TP0] Decode batch. #running-req: 1, #token: 3810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.62, #queue-req: 0, 
[2025-07-28 00:59:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.44, #queue-req: 0, 
[2025-07-28 00:59:56 DP1 TP0] Decode batch. #running-req: 1, #token: 3850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.39, #queue-req: 0, 
[2025-07-28 00:59:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.20, #queue-req: 0, 
[2025-07-28 00:59:57 DP1 TP0] Decode batch. #running-req: 1, #token: 3890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.89, #queue-req: 0, 
[2025-07-28 00:59:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.46, #queue-req: 0, 
[2025-07-28 00:59:57 DP1 TP0] Decode batch. #running-req: 1, #token: 3930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.11, #queue-req: 0, 
[2025-07-28 00:59:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.69, #queue-req: 0, 
[2025-07-28 00:59:57 DP1 TP0] Decode batch. #running-req: 1, #token: 3970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.38, #queue-req: 0, 
[2025-07-28 00:59:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.21, #queue-req: 0, 
[2025-07-28 00:59:57 DP1 TP0] Decode batch. #running-req: 1, #token: 4010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.87, #queue-req: 0, 
[2025-07-28 00:59:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.48, #queue-req: 0, 
[2025-07-28 00:59:57 DP1 TP0] Decode batch. #running-req: 1, #token: 4050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.70, #queue-req: 0, 
[2025-07-28 00:59:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1949, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.57, #queue-req: 0, 
[2025-07-28 00:59:57 DP1 TP0] Decode batch. #running-req: 1, #token: 4090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.79, #queue-req: 0, 
[2025-07-28 00:59:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1989, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.21, #queue-req: 0, 
[2025-07-28 00:59:57 DP1 TP0] Decode batch. #running-req: 1, #token: 4130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.99, #queue-req: 0, 
[2025-07-28 00:59:57 DP0 TP0] Decode batch. #running-req: 1, #token: 2029, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.29, #queue-req: 0, 
[2025-07-28 00:59:57 DP1 TP0] Decode batch. #running-req: 1, #token: 4170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.54, #queue-req: 0, 
[2025-07-28 00:59:58 DP0 TP0] Decode batch. #running-req: 1, #token: 2069, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.78, #queue-req: 0, 
[2025-07-28 00:59:58 DP1 TP0] Decode batch. #running-req: 1, #token: 4210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.89, #queue-req: 0, 
[2025-07-28 00:59:58 DP0 TP0] Decode batch. #running-req: 1, #token: 2109, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.02, #queue-req: 0, 
[2025-07-28 00:59:58 DP1 TP0] Decode batch. #running-req: 1, #token: 4250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.24, #queue-req: 0, 
[2025-07-28 00:59:58 DP0 TP0] Decode batch. #running-req: 1, #token: 2149, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.83, #queue-req: 0, 
[2025-07-28 00:59:58 DP0 TP0] Decode batch. #running-req: 1, #token: 2189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.37, #queue-req: 0, 
[2025-07-28 00:59:58 DP1 TP0] Decode batch. #running-req: 1, #token: 4290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 219.24, #queue-req: 0, 
[2025-07-28 00:59:58 DP0 TP0] Decode batch. #running-req: 1, #token: 2229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.90, #queue-req: 0, 
[2025-07-28 00:59:58 DP1 TP0] Decode batch. #running-req: 1, #token: 4330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.27, #queue-req: 0, 
[2025-07-28 00:59:58 DP1 TP0] Decode batch. #running-req: 1, #token: 4370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.58, #queue-req: 0, 
[2025-07-28 00:59:58 DP0 TP0] Decode batch. #running-req: 1, #token: 2269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.54, #queue-req: 0, 
[2025-07-28 00:59:58 DP1 TP0] Decode batch. #running-req: 1, #token: 4410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.68, #queue-req: 0, 
[2025-07-28 00:59:58 DP0 TP0] Decode batch. #running-req: 1, #token: 2309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.75, #queue-req: 0, 
[2025-07-28 00:59:58 DP1 TP0] Decode batch. #running-req: 1, #token: 4450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.87, #queue-req: 0, 
[2025-07-28 00:59:58 DP0 TP0] Decode batch. #running-req: 1, #token: 2349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.86, #queue-req: 0, 
[2025-07-28 00:59:59 DP1 TP0] Decode batch. #running-req: 1, #token: 4490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.99, #queue-req: 0, 
[2025-07-28 00:59:59 DP0 TP0] Decode batch. #running-req: 1, #token: 2389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.00, #queue-req: 0, 
[2025-07-28 00:59:59 DP1 TP0] Decode batch. #running-req: 1, #token: 4530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.34, #queue-req: 0, 
[2025-07-28 00:59:59 DP0 TP0] Decode batch. #running-req: 1, #token: 2429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.29, #queue-req: 0, 
[2025-07-28 00:59:59 DP1 TP0] Decode batch. #running-req: 1, #token: 4570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.35, #queue-req: 0, 
[2025-07-28 00:59:59 DP0 TP0] Decode batch. #running-req: 1, #token: 2469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.20, #queue-req: 0, 
[2025-07-28 00:59:59 DP1 TP0] Decode batch. #running-req: 1, #token: 4610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.03, #queue-req: 0, 
[2025-07-28 00:59:59 DP0 TP0] Decode batch. #running-req: 1, #token: 2509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.58, #queue-req: 0, 
[2025-07-28 00:59:59 DP1 TP0] Decode batch. #running-req: 1, #token: 4650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.16, #queue-req: 0, 
[2025-07-28 00:59:59 DP0 TP0] Decode batch. #running-req: 1, #token: 2549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.48, #queue-req: 0, 
[2025-07-28 00:59:59 DP1 TP0] Decode batch. #running-req: 1, #token: 4690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.85, #queue-req: 0, 
[2025-07-28 00:59:59 DP0 TP0] Decode batch. #running-req: 1, #token: 2589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.76, #queue-req: 0, 
[2025-07-28 00:59:59 DP1 TP0] Decode batch. #running-req: 1, #token: 4730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.79, #queue-req: 0, 
[2025-07-28 00:59:59 DP0 TP0] Decode batch. #running-req: 1, #token: 2629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.73, #queue-req: 0, 
[2025-07-28 00:59:59 DP1 TP0] Decode batch. #running-req: 1, #token: 4770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.20, #queue-req: 0, 
[2025-07-28 01:00:00 DP0 TP0] Decode batch. #running-req: 1, #token: 2669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.46, #queue-req: 0, 
[2025-07-28 01:00:00 DP1 TP0] Decode batch. #running-req: 1, #token: 4810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 320.15, #queue-req: 0, 
[2025-07-28 01:00:00 DP0 TP0] Decode batch. #running-req: 1, #token: 2709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.06, #queue-req: 0, 
[2025-07-28 01:00:00 DP1 TP0] Decode batch. #running-req: 1, #token: 4850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.78, #queue-req: 0, 
[2025-07-28 01:00:00 DP0 TP0] Decode batch. #running-req: 1, #token: 2749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.59, #queue-req: 0, 
[2025-07-28 01:00:00 DP1 TP0] Decode batch. #running-req: 1, #token: 4890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.65, #queue-req: 0, 
[2025-07-28 01:00:00 DP0 TP0] Decode batch. #running-req: 1, #token: 2789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.13, #queue-req: 0, 
[2025-07-28 01:00:00 DP1 TP0] Decode batch. #running-req: 1, #token: 4930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 320.26, #queue-req: 0, 
[2025-07-28 01:00:00 DP0 TP0] Decode batch. #running-req: 1, #token: 2829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.52, #queue-req: 0, 
[2025-07-28 01:00:00 DP1 TP0] Decode batch. #running-req: 1, #token: 4970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.16, #queue-req: 0, 
[2025-07-28 01:00:00 DP0 TP0] Decode batch. #running-req: 1, #token: 2869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.16, #queue-req: 0, 
[2025-07-28 01:00:00 DP1 TP0] Decode batch. #running-req: 1, #token: 5010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.37, #queue-req: 0, 
[2025-07-28 01:00:00 DP0 TP0] Decode batch. #running-req: 1, #token: 2909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.73, #queue-req: 0, 
[2025-07-28 01:00:00 DP1 TP0] Decode batch. #running-req: 1, #token: 5050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.56, #queue-req: 0, 
[2025-07-28 01:00:00 DP0 TP0] Decode batch. #running-req: 1, #token: 2949, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.04, #queue-req: 0, 
[2025-07-28 01:00:01 DP1 TP0] Decode batch. #running-req: 1, #token: 5090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.09, #queue-req: 0, 
[2025-07-28 01:00:01 DP0 TP0] Decode batch. #running-req: 1, #token: 2989, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.78, #queue-req: 0, 
[2025-07-28 01:00:01 DP1 TP0] Decode batch. #running-req: 1, #token: 5130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.06, #queue-req: 0, 
[2025-07-28 01:00:01 DP0 TP0] Decode batch. #running-req: 1, #token: 3029, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.58, #queue-req: 0, 
[2025-07-28 01:00:01 DP1 TP0] Decode batch. #running-req: 1, #token: 5170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 320.21, #queue-req: 0, 
[2025-07-28 01:00:01 DP0 TP0] Decode batch. #running-req: 1, #token: 3069, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.06, #queue-req: 0, 
[2025-07-28 01:00:01 DP1 TP0] Decode batch. #running-req: 1, #token: 5210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.99, #queue-req: 0, 
[2025-07-28 01:00:01 DP0 TP0] Decode batch. #running-req: 1, #token: 3109, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.66, #queue-req: 0, 
[2025-07-28 01:00:01 DP1 TP0] Decode batch. #running-req: 1, #token: 5250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.72, #queue-req: 0, 
[2025-07-28 01:00:01 DP0 TP0] Decode batch. #running-req: 1, #token: 3149, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.20, #queue-req: 0, 
[2025-07-28 01:00:01 DP1 TP0] Decode batch. #running-req: 1, #token: 5290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.54, #queue-req: 0, 
[2025-07-28 01:00:01 DP0 TP0] Decode batch. #running-req: 1, #token: 3189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 275.92, #queue-req: 0, 
[2025-07-28 01:00:01 DP1 TP0] Decode batch. #running-req: 1, #token: 5330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.34, #queue-req: 0, 
[2025-07-28 01:00:01 DP0 TP0] Decode batch. #running-req: 1, #token: 3229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.72, #queue-req: 0, 
[2025-07-28 01:00:01 DP1 TP0] Decode batch. #running-req: 1, #token: 5370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.44, #queue-req: 0, 
[2025-07-28 01:00:01 DP0 TP0] Decode batch. #running-req: 1, #token: 3269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.58, #queue-req: 0, 
[2025-07-28 01:00:02 DP1 TP0] Decode batch. #running-req: 1, #token: 5410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.37, #queue-req: 0, 
[2025-07-28 01:00:02 DP0 TP0] Decode batch. #running-req: 1, #token: 3309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.39, #queue-req: 0, 
[2025-07-28 01:00:02 DP1 TP0] Decode batch. #running-req: 1, #token: 5450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.03, #queue-req: 0, 
[2025-07-28 01:00:02 DP0 TP0] Decode batch. #running-req: 1, #token: 3349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.48, #queue-req: 0, 
[2025-07-28 01:00:02 DP1 TP0] Decode batch. #running-req: 1, #token: 5490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.23, #queue-req: 0, 
[2025-07-28 01:00:02 DP0 TP0] Decode batch. #running-req: 1, #token: 3389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.23, #queue-req: 0, 
[2025-07-28 01:00:02 DP1 TP0] Decode batch. #running-req: 1, #token: 5530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.37, #queue-req: 0, 
[2025-07-28 01:00:02 DP0 TP0] Decode batch. #running-req: 1, #token: 3429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.21, #queue-req: 0, 
[2025-07-28 01:00:02 DP1 TP0] Decode batch. #running-req: 1, #token: 5570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.48, #queue-req: 0, 
[2025-07-28 01:00:02 DP0 TP0] Decode batch. #running-req: 1, #token: 3469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.31, #queue-req: 0, 
[2025-07-28 01:00:02 DP1 TP0] Decode batch. #running-req: 1, #token: 5610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.55, #queue-req: 0, 
[2025-07-28 01:00:02 DP0 TP0] Decode batch. #running-req: 1, #token: 3509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.05, #queue-req: 0, 
[2025-07-28 01:00:02 DP1 TP0] Decode batch. #running-req: 1, #token: 5650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.11, #queue-req: 0, 
[2025-07-28 01:00:02 DP0 TP0] Decode batch. #running-req: 1, #token: 3549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.36, #queue-req: 0, 
[2025-07-28 01:00:02 DP1 TP0] Decode batch. #running-req: 1, #token: 5690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.57, #queue-req: 0, 
[2025-07-28 01:00:03 DP0 TP0] Decode batch. #running-req: 1, #token: 3589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.53, #queue-req: 0, 
[2025-07-28 01:00:03 DP1 TP0] Decode batch. #running-req: 1, #token: 5730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.87, #queue-req: 0, 
[2025-07-28 01:00:03 DP0 TP0] Decode batch. #running-req: 1, #token: 3629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.57, #queue-req: 0, 
[2025-07-28 01:00:03 DP1 TP0] Decode batch. #running-req: 1, #token: 5770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.48, #queue-req: 0, 
[2025-07-28 01:00:03 DP0 TP0] Decode batch. #running-req: 1, #token: 3669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.68, #queue-req: 0, 
[2025-07-28 01:00:03 DP1 TP0] Decode batch. #running-req: 1, #token: 5810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.76, #queue-req: 0, 
[2025-07-28 01:00:03 DP0 TP0] Decode batch. #running-req: 1, #token: 3709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.98, #queue-req: 0, 
[2025-07-28 01:00:03 DP1 TP0] Decode batch. #running-req: 1, #token: 5850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.78, #queue-req: 0, 
[2025-07-28 01:00:03 DP0 TP0] Decode batch. #running-req: 1, #token: 3749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.09, #queue-req: 0, 
[2025-07-28 01:00:03 DP1 TP0] Decode batch. #running-req: 1, #token: 5890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.63, #queue-req: 0, 
[2025-07-28 01:00:03 DP0 TP0] Decode batch. #running-req: 1, #token: 3789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.03, #queue-req: 0, 
[2025-07-28 01:00:03 DP1 TP0] Decode batch. #running-req: 1, #token: 5930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.72, #queue-req: 0, 
[2025-07-28 01:00:03 DP0 TP0] Decode batch. #running-req: 1, #token: 3829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.64, #queue-req: 0, 
[2025-07-28 01:00:03 DP1 TP0] Decode batch. #running-req: 1, #token: 5970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.87, #queue-req: 0, 
[2025-07-28 01:00:03 DP0 TP0] Decode batch. #running-req: 1, #token: 3869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.46, #queue-req: 0, 
[2025-07-28 01:00:03 DP1 TP0] Decode batch. #running-req: 1, #token: 6010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.46, #queue-req: 0, 
[2025-07-28 01:00:04 DP1 TP0] Decode batch. #running-req: 1, #token: 6050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.12, #queue-req: 0, 
[2025-07-28 01:00:04 DP0 TP0] Decode batch. #running-req: 1, #token: 3909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.13, #queue-req: 0, 
[2025-07-28 01:00:04 DP1 TP0] Decode batch. #running-req: 1, #token: 6090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.39, #queue-req: 0, 
[2025-07-28 01:00:04 DP0 TP0] Decode batch. #running-req: 1, #token: 3949, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.82, #queue-req: 0, 
[2025-07-28 01:00:04 DP1 TP0] Decode batch. #running-req: 1, #token: 6130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.29, #queue-req: 0, 
[2025-07-28 01:00:04 DP0 TP0] Decode batch. #running-req: 1, #token: 3989, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.64, #queue-req: 0, 
[2025-07-28 01:00:04 DP1 TP0] Decode batch. #running-req: 1, #token: 6170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.93, #queue-req: 0, 
[2025-07-28 01:00:04 DP0 TP0] Decode batch. #running-req: 1, #token: 4029, token usage: 0.00, cuda graph: True, gen throughput (token/s): 288.35, #queue-req: 0, 
[2025-07-28 01:00:04 DP1 TP0] Decode batch. #running-req: 1, #token: 6210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.01, #queue-req: 0, 
[2025-07-28 01:00:04 DP0 TP0] Decode batch. #running-req: 1, #token: 4069, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.09, #queue-req: 0, 
[2025-07-28 01:00:04 DP1 TP0] Decode batch. #running-req: 1, #token: 6250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.54, #queue-req: 0, 
[2025-07-28 01:00:04 DP0 TP0] Decode batch. #running-req: 1, #token: 4109, token usage: 0.00, cuda graph: True, gen throughput (token/s): 288.40, #queue-req: 0, 
[2025-07-28 01:00:04 DP1 TP0] Decode batch. #running-req: 1, #token: 6290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.54, #queue-req: 0, 
[2025-07-28 01:00:04 DP0 TP0] Decode batch. #running-req: 1, #token: 4149, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.20, #queue-req: 0, 
[2025-07-28 01:00:04 DP1 TP0] Decode batch. #running-req: 1, #token: 6330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.03, #queue-req: 0, 
[2025-07-28 01:00:05 DP0 TP0] Decode batch. #running-req: 1, #token: 4189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.90, #queue-req: 0, 
[2025-07-28 01:00:05 DP1 TP0] Decode batch. #running-req: 1, #token: 6370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.69, #queue-req: 0, 
[2025-07-28 01:00:05 DP0 TP0] Decode batch. #running-req: 1, #token: 4229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.92, #queue-req: 0, 
[2025-07-28 01:00:05 DP1 TP0] Decode batch. #running-req: 1, #token: 6410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.13, #queue-req: 0, 
[2025-07-28 01:00:05 DP0 TP0] Decode batch. #running-req: 1, #token: 4269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.67, #queue-req: 0, 
[2025-07-28 01:00:05 DP1 TP0] Decode batch. #running-req: 1, #token: 6450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.92, #queue-req: 0, 
[2025-07-28 01:00:05 DP0 TP0] Decode batch. #running-req: 1, #token: 4309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.61, #queue-req: 0, 
[2025-07-28 01:00:05 DP1 TP0] Decode batch. #running-req: 1, #token: 6490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.57, #queue-req: 0, 
[2025-07-28 01:00:05 DP0 TP0] Decode batch. #running-req: 1, #token: 4349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.69, #queue-req: 0, 
[2025-07-28 01:00:05 DP1 TP0] Decode batch. #running-req: 1, #token: 6530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.92, #queue-req: 0, 
[2025-07-28 01:00:05 DP0 TP0] Decode batch. #running-req: 1, #token: 4389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.79, #queue-req: 0, 
[2025-07-28 01:00:05 DP1 TP0] Decode batch. #running-req: 1, #token: 6570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.23, #queue-req: 0, 
[2025-07-28 01:00:05 DP0 TP0] Decode batch. #running-req: 1, #token: 4429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.69, #queue-req: 0, 
[2025-07-28 01:00:05 DP1 TP0] Decode batch. #running-req: 1, #token: 6610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.19, #queue-req: 0, 
[2025-07-28 01:00:05 DP0 TP0] Decode batch. #running-req: 1, #token: 4469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.54, #queue-req: 0, 
[2025-07-28 01:00:05 DP1 TP0] Decode batch. #running-req: 1, #token: 6650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.69, #queue-req: 0, 
[2025-07-28 01:00:06 DP0 TP0] Decode batch. #running-req: 1, #token: 4509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.52, #queue-req: 0, 
[2025-07-28 01:00:06 DP1 TP0] Decode batch. #running-req: 1, #token: 6690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.99, #queue-req: 0, 
[2025-07-28 01:00:06 DP0 TP0] Decode batch. #running-req: 1, #token: 4549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.31, #queue-req: 0, 
[2025-07-28 01:00:06 DP1 TP0] Decode batch. #running-req: 1, #token: 6730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.31, #queue-req: 0, 
[2025-07-28 01:00:06 DP1 TP0] Decode batch. #running-req: 1, #token: 6770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.29, #queue-req: 0, 
[2025-07-28 01:00:06 DP0 TP0] Decode batch. #running-req: 1, #token: 4589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.42, #queue-req: 0, 
[2025-07-28 01:00:06 DP1 TP0] Decode batch. #running-req: 1, #token: 6810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.79, #queue-req: 0, 
[2025-07-28 01:00:06 DP0 TP0] Decode batch. #running-req: 1, #token: 4629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.08, #queue-req: 0, 
[2025-07-28 01:00:06 DP1 TP0] Decode batch. #running-req: 1, #token: 6850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.38, #queue-req: 0, 
[2025-07-28 01:00:06 DP0 TP0] Decode batch. #running-req: 1, #token: 4669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.64, #queue-req: 0, 
[2025-07-28 01:00:06 DP1 TP0] Decode batch. #running-req: 1, #token: 6890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.30, #queue-req: 0, 
[2025-07-28 01:00:06 DP0 TP0] Decode batch. #running-req: 1, #token: 4709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.65, #queue-req: 0, 
[2025-07-28 01:00:06 DP1 TP0] Decode batch. #running-req: 1, #token: 6930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.19, #queue-req: 0, 
[2025-07-28 01:00:06 DP0 TP0] Decode batch. #running-req: 1, #token: 4749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.25, #queue-req: 0, 
[2025-07-28 01:00:06 DP1 TP0] Decode batch. #running-req: 1, #token: 6970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.05, #queue-req: 0, 
[2025-07-28 01:00:07 DP0 TP0] Decode batch. #running-req: 1, #token: 4789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.43, #queue-req: 0, 
[2025-07-28 01:00:07 DP1 TP0] Decode batch. #running-req: 1, #token: 7010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.49, #queue-req: 0, 
[2025-07-28 01:00:07 DP0 TP0] Decode batch. #running-req: 1, #token: 4829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.18, #queue-req: 0, 
[2025-07-28 01:00:07 DP1 TP0] Decode batch. #running-req: 1, #token: 7050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.17, #queue-req: 0, 
[2025-07-28 01:00:07 DP0 TP0] Decode batch. #running-req: 1, #token: 4869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.24, #queue-req: 0, 
[2025-07-28 01:00:07 DP1 TP0] Decode batch. #running-req: 1, #token: 7090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.58, #queue-req: 0, 
[2025-07-28 01:00:07 DP0 TP0] Decode batch. #running-req: 1, #token: 4909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.59, #queue-req: 0, 
[2025-07-28 01:00:07 DP1 TP0] Decode batch. #running-req: 1, #token: 7130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.10, #queue-req: 0, 
[2025-07-28 01:00:07 DP0 TP0] Decode batch. #running-req: 1, #token: 4949, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.10, #queue-req: 0, 
[2025-07-28 01:00:07 DP1 TP0] Decode batch. #running-req: 1, #token: 7170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.32, #queue-req: 0, 
[2025-07-28 01:00:07 DP0 TP0] Decode batch. #running-req: 1, #token: 4989, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.20, #queue-req: 0, 
[2025-07-28 01:00:07 DP1 TP0] Decode batch. #running-req: 1, #token: 7210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.88, #queue-req: 0, 
[2025-07-28 01:00:07 DP0 TP0] Decode batch. #running-req: 1, #token: 5029, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.39, #queue-req: 0, 
[2025-07-28 01:00:07 DP1 TP0] Decode batch. #running-req: 1, #token: 7250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.40, #queue-req: 0, 
[2025-07-28 01:00:07 DP0 TP0] Decode batch. #running-req: 1, #token: 5069, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.17, #queue-req: 0, 
[2025-07-28 01:00:08 DP1 TP0] Decode batch. #running-req: 1, #token: 7290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.50, #queue-req: 0, 
[2025-07-28 01:00:08 DP0 TP0] Decode batch. #running-req: 1, #token: 5109, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.63, #queue-req: 0, 
[2025-07-28 01:00:08 DP1 TP0] Decode batch. #running-req: 1, #token: 7330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.98, #queue-req: 0, 
[2025-07-28 01:00:08 DP0 TP0] Decode batch. #running-req: 1, #token: 5149, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.34, #queue-req: 0, 
[2025-07-28 01:00:08 DP1 TP0] Decode batch. #running-req: 1, #token: 7370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.10, #queue-req: 0, 
[2025-07-28 01:00:08 DP0 TP0] Decode batch. #running-req: 1, #token: 5189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.80, #queue-req: 0, 
[2025-07-28 01:00:08 DP1 TP0] Decode batch. #running-req: 1, #token: 7410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.86, #queue-req: 0, 
[2025-07-28 01:00:08 DP0 TP0] Decode batch. #running-req: 1, #token: 5229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.17, #queue-req: 0, 
[2025-07-28 01:00:08 DP1 TP0] Decode batch. #running-req: 1, #token: 7450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.21, #queue-req: 0, 
[2025-07-28 01:00:08 DP0 TP0] Decode batch. #running-req: 1, #token: 5269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.98, #queue-req: 0, 
[2025-07-28 01:00:08 DP1 TP0] Decode batch. #running-req: 1, #token: 7490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.68, #queue-req: 0, 
[2025-07-28 01:00:08 DP0 TP0] Decode batch. #running-req: 1, #token: 5309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.25, #queue-req: 0, 
[2025-07-28 01:00:08 DP1 TP0] Decode batch. #running-req: 1, #token: 7530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.57, #queue-req: 0, 
[2025-07-28 01:00:08 DP0 TP0] Decode batch. #running-req: 1, #token: 5349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.70, #queue-req: 0, 
[2025-07-28 01:00:08 DP1 TP0] Decode batch. #running-req: 1, #token: 7570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.92, #queue-req: 0, 
[2025-07-28 01:00:08 DP0 TP0] Decode batch. #running-req: 1, #token: 5389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.89, #queue-req: 0, 
[2025-07-28 01:00:09 DP1 TP0] Decode batch. #running-req: 1, #token: 7610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.26, #queue-req: 0, 
[2025-07-28 01:00:09 DP0 TP0] Decode batch. #running-req: 1, #token: 5429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.92, #queue-req: 0, 
[2025-07-28 01:00:09 DP1 TP0] Decode batch. #running-req: 1, #token: 7650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.31, #queue-req: 0, 
[2025-07-28 01:00:09 DP0 TP0] Decode batch. #running-req: 1, #token: 5469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.06, #queue-req: 0, 
[2025-07-28 01:00:09 DP1 TP0] Decode batch. #running-req: 1, #token: 7690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.43, #queue-req: 0, 
[2025-07-28 01:00:09 DP0 TP0] Decode batch. #running-req: 1, #token: 5509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.84, #queue-req: 0, 
[2025-07-28 01:00:09 DP1 TP0] Decode batch. #running-req: 1, #token: 7730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.75, #queue-req: 0, 
[2025-07-28 01:00:09 DP0 TP0] Decode batch. #running-req: 1, #token: 5549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.90, #queue-req: 0, 
[2025-07-28 01:00:09 DP1 TP0] Decode batch. #running-req: 1, #token: 7770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.64, #queue-req: 0, 
[2025-07-28 01:00:09 DP0 TP0] Decode batch. #running-req: 1, #token: 5589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.42, #queue-req: 0, 
[2025-07-28 01:00:09 DP1 TP0] Decode batch. #running-req: 1, #token: 7810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.94, #queue-req: 0, 
[2025-07-28 01:00:09 DP0 TP0] Decode batch. #running-req: 1, #token: 5629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.95, #queue-req: 0, 
[2025-07-28 01:00:09 DP1 TP0] Decode batch. #running-req: 1, #token: 7850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.91, #queue-req: 0, 
[2025-07-28 01:00:09 DP0 TP0] Decode batch. #running-req: 1, #token: 5669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.77, #queue-req: 0, 
[2025-07-28 01:00:09 DP1 TP0] Decode batch. #running-req: 1, #token: 7890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 320.29, #queue-req: 0, 
[2025-07-28 01:00:09 DP0 TP0] Decode batch. #running-req: 1, #token: 5709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.74, #queue-req: 0, 
[2025-07-28 01:00:10 DP1 TP0] Decode batch. #running-req: 1, #token: 7930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.76, #queue-req: 0, 
[2025-07-28 01:00:10 DP0 TP0] Decode batch. #running-req: 1, #token: 5749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.48, #queue-req: 0, 
[2025-07-28 01:00:10 DP1 TP0] Decode batch. #running-req: 1, #token: 7970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 320.56, #queue-req: 0, 
[2025-07-28 01:00:10 DP0 TP0] Decode batch. #running-req: 1, #token: 5789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.67, #queue-req: 0, 
[2025-07-28 01:00:10 DP1 TP0] Decode batch. #running-req: 1, #token: 8010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.65, #queue-req: 0, 
[2025-07-28 01:00:10 DP0 TP0] Decode batch. #running-req: 1, #token: 5829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.84, #queue-req: 0, 
[2025-07-28 01:00:10 DP1 TP0] Decode batch. #running-req: 1, #token: 8050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.82, #queue-req: 0, 
[2025-07-28 01:00:10 DP0 TP0] Decode batch. #running-req: 1, #token: 5869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.22, #queue-req: 0, 
[2025-07-28 01:00:10 DP1 TP0] Decode batch. #running-req: 1, #token: 8090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.93, #queue-req: 0, 
[2025-07-28 01:00:10 DP0 TP0] Decode batch. #running-req: 1, #token: 5909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.47, #queue-req: 0, 
[2025-07-28 01:00:10 DP1 TP0] Decode batch. #running-req: 1, #token: 8130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.44, #queue-req: 0, 
[2025-07-28 01:00:10 DP0 TP0] Decode batch. #running-req: 1, #token: 5949, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.76, #queue-req: 0, 
[2025-07-28 01:00:10 DP1 TP0] Decode batch. #running-req: 1, #token: 8170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.70, #queue-req: 0, 
[2025-07-28 01:00:10 DP0 TP0] Decode batch. #running-req: 1, #token: 5989, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.62, #queue-req: 0, 
[2025-07-28 01:00:10 DP1 TP0] Decode batch. #running-req: 1, #token: 8210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.43, #queue-req: 0, 
[2025-07-28 01:00:11 DP0 TP0] Decode batch. #running-req: 1, #token: 6029, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.51, #queue-req: 0, 
[2025-07-28 01:00:11 DP1 TP0] Decode batch. #running-req: 1, #token: 8250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.00, #queue-req: 0, 
[2025-07-28 01:00:11 DP0 TP0] Decode batch. #running-req: 1, #token: 6069, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.04, #queue-req: 0, 
[2025-07-28 01:00:11 DP1 TP0] Decode batch. #running-req: 1, #token: 8290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.56, #queue-req: 0, 
[2025-07-28 01:00:11 DP0 TP0] Decode batch. #running-req: 1, #token: 6109, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.40, #queue-req: 0, 
[2025-07-28 01:00:11 DP1 TP0] Decode batch. #running-req: 1, #token: 8330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.86, #queue-req: 0, 
[2025-07-28 01:00:11 DP0 TP0] Decode batch. #running-req: 1, #token: 6149, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.10, #queue-req: 0, 
[2025-07-28 01:00:11 DP1 TP0] Decode batch. #running-req: 1, #token: 8370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 320.25, #queue-req: 0, 
[2025-07-28 01:00:11] INFO:     127.0.0.1:34994 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:00:11 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 89, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:00:11 DP0 TP0] Decode batch. #running-req: 1, #token: 6189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.74, #queue-req: 0, 
[2025-07-28 01:00:11 DP1 TP0] Decode batch. #running-req: 1, #token: 196, token usage: 0.00, cuda graph: True, gen throughput (token/s): 202.32, #queue-req: 0, 
[2025-07-28 01:00:11 DP0 TP0] Decode batch. #running-req: 1, #token: 6229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.20, #queue-req: 0, 
[2025-07-28 01:00:11 DP1 TP0] Decode batch. #running-req: 1, #token: 236, token usage: 0.00, cuda graph: True, gen throughput (token/s): 339.79, #queue-req: 0, 
[2025-07-28 01:00:11 DP0 TP0] Decode batch. #running-req: 1, #token: 6269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.48, #queue-req: 0, 
[2025-07-28 01:00:11 DP1 TP0] Decode batch. #running-req: 1, #token: 276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 335.93, #queue-req: 0, 
[2025-07-28 01:00:11 DP0 TP0] Decode batch. #running-req: 1, #token: 6309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.28, #queue-req: 0, 
[2025-07-28 01:00:12 DP1 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.84, #queue-req: 0, 
[2025-07-28 01:00:12 DP0 TP0] Decode batch. #running-req: 1, #token: 6349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.19, #queue-req: 0, 
[2025-07-28 01:00:12 DP1 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.71, #queue-req: 0, 
[2025-07-28 01:00:12 DP0 TP0] Decode batch. #running-req: 1, #token: 6389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.33, #queue-req: 0, 
[2025-07-28 01:00:12 DP1 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.86, #queue-req: 0, 
[2025-07-28 01:00:12 DP0 TP0] Decode batch. #running-req: 1, #token: 6429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.00, #queue-req: 0, 
[2025-07-28 01:00:12 DP1 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 320.90, #queue-req: 0, 
[2025-07-28 01:00:12 DP0 TP0] Decode batch. #running-req: 1, #token: 6469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.52, #queue-req: 0, 
[2025-07-28 01:00:12 DP1 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.80, #queue-req: 0, 
[2025-07-28 01:00:12 DP0 TP0] Decode batch. #running-req: 1, #token: 6509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.45, #queue-req: 0, 
[2025-07-28 01:00:12 DP1 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.78, #queue-req: 0, 
[2025-07-28 01:00:12 DP0 TP0] Decode batch. #running-req: 1, #token: 6549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.62, #queue-req: 0, 
[2025-07-28 01:00:12 DP1 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.40, #queue-req: 0, 
[2025-07-28 01:00:12 DP0 TP0] Decode batch. #running-req: 1, #token: 6589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.80, #queue-req: 0, 
[2025-07-28 01:00:12 DP1 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.51, #queue-req: 0, 
[2025-07-28 01:00:13 DP0 TP0] Decode batch. #running-req: 1, #token: 6629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.75, #queue-req: 0, 
[2025-07-28 01:00:13 DP1 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.90, #queue-req: 0, 
[2025-07-28 01:00:13 DP0 TP0] Decode batch. #running-req: 1, #token: 6669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.09, #queue-req: 0, 
[2025-07-28 01:00:13 DP1 TP0] Decode batch. #running-req: 1, #token: 676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.65, #queue-req: 0, 
[2025-07-28 01:00:13 DP1 TP0] Decode batch. #running-req: 1, #token: 716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.08, #queue-req: 0, 
[2025-07-28 01:00:13 DP0 TP0] Decode batch. #running-req: 1, #token: 6709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.62, #queue-req: 0, 
[2025-07-28 01:00:13 DP1 TP0] Decode batch. #running-req: 1, #token: 756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.70, #queue-req: 0, 
[2025-07-28 01:00:13 DP0 TP0] Decode batch. #running-req: 1, #token: 6749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.84, #queue-req: 0, 
[2025-07-28 01:00:13 DP1 TP0] Decode batch. #running-req: 1, #token: 796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.14, #queue-req: 0, 
[2025-07-28 01:00:13 DP0 TP0] Decode batch. #running-req: 1, #token: 6789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.23, #queue-req: 0, 
[2025-07-28 01:00:13 DP1 TP0] Decode batch. #running-req: 1, #token: 836, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.43, #queue-req: 0, 
[2025-07-28 01:00:13 DP0 TP0] Decode batch. #running-req: 1, #token: 6829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.77, #queue-req: 0, 
[2025-07-28 01:00:13 DP1 TP0] Decode batch. #running-req: 1, #token: 876, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.66, #queue-req: 0, 
[2025-07-28 01:00:13 DP0 TP0] Decode batch. #running-req: 1, #token: 6869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.74, #queue-req: 0, 
[2025-07-28 01:00:13 DP1 TP0] Decode batch. #running-req: 1, #token: 916, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.93, #queue-req: 0, 
[2025-07-28 01:00:13 DP0 TP0] Decode batch. #running-req: 1, #token: 6909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.49, #queue-req: 0, 
[2025-07-28 01:00:14 DP1 TP0] Decode batch. #running-req: 1, #token: 956, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.44, #queue-req: 0, 
[2025-07-28 01:00:14 DP0 TP0] Decode batch. #running-req: 1, #token: 6949, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.17, #queue-req: 0, 
[2025-07-28 01:00:14 DP1 TP0] Decode batch. #running-req: 1, #token: 996, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.10, #queue-req: 0, 
[2025-07-28 01:00:14 DP0 TP0] Decode batch. #running-req: 1, #token: 6989, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.44, #queue-req: 0, 
[2025-07-28 01:00:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1036, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.86, #queue-req: 0, 
[2025-07-28 01:00:14 DP0 TP0] Decode batch. #running-req: 1, #token: 7029, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.59, #queue-req: 0, 
[2025-07-28 01:00:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1076, token usage: 0.00, cuda graph: True, gen throughput (token/s): 320.46, #queue-req: 0, 
[2025-07-28 01:00:14 DP0 TP0] Decode batch. #running-req: 1, #token: 7069, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.60, #queue-req: 0, 
[2025-07-28 01:00:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1116, token usage: 0.00, cuda graph: True, gen throughput (token/s): 321.49, #queue-req: 0, 
[2025-07-28 01:00:14 DP0 TP0] Decode batch. #running-req: 1, #token: 7109, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.36, #queue-req: 0, 
[2025-07-28 01:00:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1156, token usage: 0.00, cuda graph: True, gen throughput (token/s): 321.17, #queue-req: 0, 
[2025-07-28 01:00:14 DP0 TP0] Decode batch. #running-req: 1, #token: 7149, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.28, #queue-req: 0, 
[2025-07-28 01:00:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1196, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.92, #queue-req: 0, 
[2025-07-28 01:00:14 DP0 TP0] Decode batch. #running-req: 1, #token: 7189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.34, #queue-req: 0, 
[2025-07-28 01:00:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1236, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.67, #queue-req: 0, 
[2025-07-28 01:00:14 DP0 TP0] Decode batch. #running-req: 1, #token: 7229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.81, #queue-req: 0, 
[2025-07-28 01:00:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.98, #queue-req: 0, 
[2025-07-28 01:00:15 DP0 TP0] Decode batch. #running-req: 1, #token: 7269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.01, #queue-req: 0, 
[2025-07-28 01:00:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.32, #queue-req: 0, 
[2025-07-28 01:00:15 DP0 TP0] Decode batch. #running-req: 1, #token: 7309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.07, #queue-req: 0, 
[2025-07-28 01:00:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.60, #queue-req: 0, 
[2025-07-28 01:00:15 DP0 TP0] Decode batch. #running-req: 1, #token: 7349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.32, #queue-req: 0, 
[2025-07-28 01:00:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.38, #queue-req: 0, 
[2025-07-28 01:00:15 DP0 TP0] Decode batch. #running-req: 1, #token: 7389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.21, #queue-req: 0, 
[2025-07-28 01:00:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.27, #queue-req: 0, 
[2025-07-28 01:00:15 DP0 TP0] Decode batch. #running-req: 1, #token: 7429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.06, #queue-req: 0, 
[2025-07-28 01:00:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 320.62, #queue-req: 0, 
[2025-07-28 01:00:15 DP0 TP0] Decode batch. #running-req: 1, #token: 7469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.60, #queue-req: 0, 
[2025-07-28 01:00:15] INFO:     127.0.0.1:32784 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:00:15 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 115, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:00:15 DP0 TP0] Decode batch. #running-req: 2, #token: 7656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 367.43, #queue-req: 0, 
[2025-07-28 01:00:16 DP0 TP0] Decode batch. #running-req: 2, #token: 7736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 612.09, #queue-req: 0, 
[2025-07-28 01:00:16 DP0 TP0] Decode batch. #running-req: 2, #token: 7816, token usage: 0.00, cuda graph: True, gen throughput (token/s): 610.79, #queue-req: 0, 
[2025-07-28 01:00:16 DP0 TP0] Decode batch. #running-req: 2, #token: 7896, token usage: 0.00, cuda graph: True, gen throughput (token/s): 610.84, #queue-req: 0, 
[2025-07-28 01:00:16 DP0 TP0] Decode batch. #running-req: 2, #token: 7976, token usage: 0.00, cuda graph: True, gen throughput (token/s): 612.80, #queue-req: 0, 
[2025-07-28 01:00:16 DP0 TP0] Decode batch. #running-req: 2, #token: 8056, token usage: 0.00, cuda graph: True, gen throughput (token/s): 610.76, #queue-req: 0, 
[2025-07-28 01:00:16 DP0 TP0] Decode batch. #running-req: 2, #token: 8136, token usage: 0.00, cuda graph: True, gen throughput (token/s): 611.56, #queue-req: 0, 
[2025-07-28 01:00:16 DP0 TP0] Decode batch. #running-req: 2, #token: 8216, token usage: 0.00, cuda graph: True, gen throughput (token/s): 610.88, #queue-req: 0, 
[2025-07-28 01:00:16 DP0 TP0] Decode batch. #running-req: 2, #token: 8296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 611.78, #queue-req: 0, 
[2025-07-28 01:00:17 DP0 TP0] Decode batch. #running-req: 2, #token: 8376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 605.21, #queue-req: 0, 
[2025-07-28 01:00:17 DP0 TP0] Decode batch. #running-req: 2, #token: 8456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 590.30, #queue-req: 0, 
[2025-07-28 01:00:17 DP0 TP0] Decode batch. #running-req: 2, #token: 8536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 581.77, #queue-req: 0, 
[2025-07-28 01:00:17 DP0 TP0] Decode batch. #running-req: 2, #token: 8616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 570.22, #queue-req: 0, 
[2025-07-28 01:00:17 DP0 TP0] Decode batch. #running-req: 2, #token: 8696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 585.05, #queue-req: 0, 
[2025-07-28 01:00:17 DP0 TP0] Decode batch. #running-req: 2, #token: 8776, token usage: 0.00, cuda graph: True, gen throughput (token/s): 584.35, #queue-req: 0, 
[2025-07-28 01:00:17 DP0 TP0] Decode batch. #running-req: 2, #token: 8856, token usage: 0.00, cuda graph: True, gen throughput (token/s): 585.82, #queue-req: 0, 
[2025-07-28 01:00:18 DP0 TP0] Decode batch. #running-req: 2, #token: 8936, token usage: 0.00, cuda graph: True, gen throughput (token/s): 585.30, #queue-req: 0, 
[2025-07-28 01:00:18 DP0 TP0] Decode batch. #running-req: 2, #token: 9016, token usage: 0.00, cuda graph: True, gen throughput (token/s): 583.03, #queue-req: 0, 
[2025-07-28 01:00:18 DP0 TP0] Decode batch. #running-req: 2, #token: 9096, token usage: 0.00, cuda graph: True, gen throughput (token/s): 584.30, #queue-req: 0, 
[2025-07-28 01:00:18 DP0 TP0] Decode batch. #running-req: 2, #token: 9176, token usage: 0.00, cuda graph: True, gen throughput (token/s): 586.25, #queue-req: 0, 
[2025-07-28 01:00:18 DP0 TP0] Decode batch. #running-req: 2, #token: 9256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 588.26, #queue-req: 0, 
[2025-07-28 01:00:18 DP0 TP0] Decode batch. #running-req: 2, #token: 9336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 586.04, #queue-req: 0, 
[2025-07-28 01:00:18] INFO:     127.0.0.1:35008 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:00:18 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 164, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:00:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1112, token usage: 0.00, cuda graph: True, gen throughput (token/s): 468.69, #queue-req: 0, 
[2025-07-28 01:00:18 DP1 TP0] Decode batch. #running-req: 1, #token: 261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 12.09, #queue-req: 0, 
[2025-07-28 01:00:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1152, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.23, #queue-req: 0, 
[2025-07-28 01:00:19 DP1 TP0] Decode batch. #running-req: 1, #token: 301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.08, #queue-req: 0, 
[2025-07-28 01:00:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1192, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.52, #queue-req: 0, 
[2025-07-28 01:00:19 DP1 TP0] Decode batch. #running-req: 1, #token: 341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.12, #queue-req: 0, 
[2025-07-28 01:00:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1232, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.09, #queue-req: 0, 
[2025-07-28 01:00:19 DP1 TP0] Decode batch. #running-req: 1, #token: 381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.34, #queue-req: 0, 
[2025-07-28 01:00:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1272, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.26, #queue-req: 0, 
[2025-07-28 01:00:19 DP1 TP0] Decode batch. #running-req: 1, #token: 421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.24, #queue-req: 0, 
[2025-07-28 01:00:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1312, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.36, #queue-req: 0, 
[2025-07-28 01:00:19 DP1 TP0] Decode batch. #running-req: 1, #token: 461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 321.05, #queue-req: 0, 
[2025-07-28 01:00:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1352, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.06, #queue-req: 0, 
[2025-07-28 01:00:19 DP1 TP0] Decode batch. #running-req: 1, #token: 501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 321.33, #queue-req: 0, 
[2025-07-28 01:00:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1392, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.27, #queue-req: 0, 
[2025-07-28 01:00:19 DP1 TP0] Decode batch. #running-req: 1, #token: 541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.45, #queue-req: 0, 
[2025-07-28 01:00:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1432, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.24, #queue-req: 0, 
[2025-07-28 01:00:19 DP1 TP0] Decode batch. #running-req: 1, #token: 581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.13, #queue-req: 0, 
[2025-07-28 01:00:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1472, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.70, #queue-req: 0, 
[2025-07-28 01:00:20 DP1 TP0] Decode batch. #running-req: 1, #token: 621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.84, #queue-req: 0, 
[2025-07-28 01:00:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1512, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.62, #queue-req: 0, 
[2025-07-28 01:00:20 DP1 TP0] Decode batch. #running-req: 1, #token: 661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.39, #queue-req: 0, 
[2025-07-28 01:00:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1552, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.84, #queue-req: 0, 
[2025-07-28 01:00:20 DP1 TP0] Decode batch. #running-req: 1, #token: 701, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.77, #queue-req: 0, 
[2025-07-28 01:00:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1592, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.02, #queue-req: 0, 
[2025-07-28 01:00:20 DP1 TP0] Decode batch. #running-req: 1, #token: 741, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.85, #queue-req: 0, 
[2025-07-28 01:00:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1632, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.05, #queue-req: 0, 
[2025-07-28 01:00:20 DP1 TP0] Decode batch. #running-req: 1, #token: 781, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.67, #queue-req: 0, 
[2025-07-28 01:00:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1672, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.82, #queue-req: 0, 
[2025-07-28 01:00:20 DP1 TP0] Decode batch. #running-req: 1, #token: 821, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.13, #queue-req: 0, 
[2025-07-28 01:00:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1712, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.66, #queue-req: 0, 
[2025-07-28 01:00:20 DP1 TP0] Decode batch. #running-req: 1, #token: 861, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.40, #queue-req: 0, 
[2025-07-28 01:00:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1752, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.52, #queue-req: 0, 
[2025-07-28 01:00:20 DP1 TP0] Decode batch. #running-req: 1, #token: 901, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.09, #queue-req: 0, 
[2025-07-28 01:00:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1792, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.74, #queue-req: 0, 
[2025-07-28 01:00:21 DP1 TP0] Decode batch. #running-req: 1, #token: 941, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.65, #queue-req: 0, 
[2025-07-28 01:00:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1832, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.90, #queue-req: 0, 
[2025-07-28 01:00:21 DP1 TP0] Decode batch. #running-req: 1, #token: 981, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.94, #queue-req: 0, 
[2025-07-28 01:00:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1872, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.82, #queue-req: 0, 
[2025-07-28 01:00:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1021, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.76, #queue-req: 0, 
[2025-07-28 01:00:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1912, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.25, #queue-req: 0, 
[2025-07-28 01:00:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1061, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.15, #queue-req: 0, 
[2025-07-28 01:00:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1952, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.04, #queue-req: 0, 
[2025-07-28 01:00:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1101, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.98, #queue-req: 0, 
[2025-07-28 01:00:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1992, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.33, #queue-req: 0, 
[2025-07-28 01:00:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1141, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.66, #queue-req: 0, 
[2025-07-28 01:00:21 DP0 TP0] Decode batch. #running-req: 1, #token: 2032, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.26, #queue-req: 0, 
[2025-07-28 01:00:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1181, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.40, #queue-req: 0, 
[2025-07-28 01:00:21 DP0 TP0] Decode batch. #running-req: 1, #token: 2072, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.74, #queue-req: 0, 
[2025-07-28 01:00:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1221, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.01, #queue-req: 0, 
[2025-07-28 01:00:22 DP0 TP0] Decode batch. #running-req: 1, #token: 2112, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.41, #queue-req: 0, 
[2025-07-28 01:00:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.40, #queue-req: 0, 
[2025-07-28 01:00:22 DP0 TP0] Decode batch. #running-req: 1, #token: 2152, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.74, #queue-req: 0, 
[2025-07-28 01:00:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.80, #queue-req: 0, 
[2025-07-28 01:00:22 DP0 TP0] Decode batch. #running-req: 1, #token: 2192, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.40, #queue-req: 0, 
[2025-07-28 01:00:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.88, #queue-req: 0, 
[2025-07-28 01:00:22 DP0 TP0] Decode batch. #running-req: 1, #token: 2232, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.58, #queue-req: 0, 
[2025-07-28 01:00:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.90, #queue-req: 0, 
[2025-07-28 01:00:22 DP0 TP0] Decode batch. #running-req: 1, #token: 2272, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.11, #queue-req: 0, 
[2025-07-28 01:00:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.81, #queue-req: 0, 
[2025-07-28 01:00:22 DP0 TP0] Decode batch. #running-req: 1, #token: 2312, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.38, #queue-req: 0, 
[2025-07-28 01:00:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 320.03, #queue-req: 0, 
[2025-07-28 01:00:22 DP0 TP0] Decode batch. #running-req: 1, #token: 2352, token usage: 0.00, cuda graph: True, gen throughput (token/s): 273.44, #queue-req: 0, 
[2025-07-28 01:00:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.74, #queue-req: 0, 
[2025-07-28 01:00:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.61, #queue-req: 0, 
[2025-07-28 01:00:23 DP0 TP0] Decode batch. #running-req: 1, #token: 2392, token usage: 0.00, cuda graph: True, gen throughput (token/s): 261.35, #queue-req: 0, 
[2025-07-28 01:00:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 320.32, #queue-req: 0, 
[2025-07-28 01:00:23 DP0 TP0] Decode batch. #running-req: 1, #token: 2432, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.14, #queue-req: 0, 
[2025-07-28 01:00:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.67, #queue-req: 0, 
[2025-07-28 01:00:23 DP0 TP0] Decode batch. #running-req: 1, #token: 2472, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.35, #queue-req: 0, 
[2025-07-28 01:00:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.64, #queue-req: 0, 
[2025-07-28 01:00:23 DP0 TP0] Decode batch. #running-req: 1, #token: 2512, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.44, #queue-req: 0, 
[2025-07-28 01:00:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1701, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.90, #queue-req: 0, 
[2025-07-28 01:00:23 DP0 TP0] Decode batch. #running-req: 1, #token: 2552, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.48, #queue-req: 0, 
[2025-07-28 01:00:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1741, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.97, #queue-req: 0, 
[2025-07-28 01:00:23 DP0 TP0] Decode batch. #running-req: 1, #token: 2592, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.83, #queue-req: 0, 
[2025-07-28 01:00:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1781, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.15, #queue-req: 0, 
[2025-07-28 01:00:23 DP0 TP0] Decode batch. #running-req: 1, #token: 2632, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.55, #queue-req: 0, 
[2025-07-28 01:00:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1821, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.54, #queue-req: 0, 
[2025-07-28 01:00:23 DP0 TP0] Decode batch. #running-req: 1, #token: 2672, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.92, #queue-req: 0, 
[2025-07-28 01:00:24 DP1 TP0] Decode batch. #running-req: 1, #token: 1861, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.69, #queue-req: 0, 
[2025-07-28 01:00:24 DP0 TP0] Decode batch. #running-req: 1, #token: 2712, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.17, #queue-req: 0, 
[2025-07-28 01:00:24 DP1 TP0] Decode batch. #running-req: 1, #token: 1901, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.67, #queue-req: 0, 
[2025-07-28 01:00:24 DP0 TP0] Decode batch. #running-req: 1, #token: 2752, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.72, #queue-req: 0, 
[2025-07-28 01:00:24 DP1 TP0] Decode batch. #running-req: 1, #token: 1941, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.83, #queue-req: 0, 
[2025-07-28 01:00:24 DP0 TP0] Decode batch. #running-req: 1, #token: 2792, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.59, #queue-req: 0, 
[2025-07-28 01:00:24 DP1 TP0] Decode batch. #running-req: 1, #token: 1981, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.43, #queue-req: 0, 
[2025-07-28 01:00:24 DP0 TP0] Decode batch. #running-req: 1, #token: 2832, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.96, #queue-req: 0, 
[2025-07-28 01:00:24 DP1 TP0] Decode batch. #running-req: 1, #token: 2021, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.94, #queue-req: 0, 
[2025-07-28 01:00:24 DP0 TP0] Decode batch. #running-req: 1, #token: 2872, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.66, #queue-req: 0, 
[2025-07-28 01:00:24 DP1 TP0] Decode batch. #running-req: 1, #token: 2061, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.91, #queue-req: 0, 
[2025-07-28 01:00:24 DP0 TP0] Decode batch. #running-req: 1, #token: 2912, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.20, #queue-req: 0, 
[2025-07-28 01:00:24 DP1 TP0] Decode batch. #running-req: 1, #token: 2101, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.76, #queue-req: 0, 
[2025-07-28 01:00:24 DP0 TP0] Decode batch. #running-req: 1, #token: 2952, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.90, #queue-req: 0, 
[2025-07-28 01:00:24] INFO:     127.0.0.1:49868 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:00:24 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 143, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:00:25 DP0 TP0] Decode batch. #running-req: 2, #token: 3143, token usage: 0.00, cuda graph: True, gen throughput (token/s): 250.65, #queue-req: 0, 
[2025-07-28 01:00:25 DP0 TP0] Decode batch. #running-req: 2, #token: 3223, token usage: 0.00, cuda graph: True, gen throughput (token/s): 614.01, #queue-req: 0, 
[2025-07-28 01:00:25 DP0 TP0] Decode batch. #running-req: 2, #token: 3303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 623.84, #queue-req: 0, 
[2025-07-28 01:00:25 DP0 TP0] Decode batch. #running-req: 2, #token: 3383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 624.49, #queue-req: 0, 
[2025-07-28 01:00:25 DP0 TP0] Decode batch. #running-req: 2, #token: 3463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 624.83, #queue-req: 0, 
[2025-07-28 01:00:25 DP0 TP0] Decode batch. #running-req: 2, #token: 3543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 612.36, #queue-req: 0, 
[2025-07-28 01:00:25 DP0 TP0] Decode batch. #running-req: 2, #token: 3623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 594.80, #queue-req: 0, 
[2025-07-28 01:00:25 DP0 TP0] Decode batch. #running-req: 2, #token: 3703, token usage: 0.00, cuda graph: True, gen throughput (token/s): 623.09, #queue-req: 0, 
[2025-07-28 01:00:26 DP0 TP0] Decode batch. #running-req: 2, #token: 3783, token usage: 0.00, cuda graph: True, gen throughput (token/s): 623.68, #queue-req: 0, 
[2025-07-28 01:00:26 DP0 TP0] Decode batch. #running-req: 2, #token: 3863, token usage: 0.00, cuda graph: True, gen throughput (token/s): 619.12, #queue-req: 0, 
[2025-07-28 01:00:26 DP0 TP0] Decode batch. #running-req: 2, #token: 3943, token usage: 0.00, cuda graph: True, gen throughput (token/s): 625.03, #queue-req: 0, 
[2025-07-28 01:00:26 DP0 TP0] Decode batch. #running-req: 2, #token: 4023, token usage: 0.00, cuda graph: True, gen throughput (token/s): 624.51, #queue-req: 0, 
[2025-07-28 01:00:26 DP0 TP0] Decode batch. #running-req: 2, #token: 4103, token usage: 0.00, cuda graph: True, gen throughput (token/s): 624.30, #queue-req: 0, 
[2025-07-28 01:00:26 DP0 TP0] Decode batch. #running-req: 2, #token: 4183, token usage: 0.00, cuda graph: True, gen throughput (token/s): 623.60, #queue-req: 0, 
[2025-07-28 01:00:26 DP0 TP0] Decode batch. #running-req: 2, #token: 4263, token usage: 0.00, cuda graph: True, gen throughput (token/s): 623.03, #queue-req: 0, 
[2025-07-28 01:00:26 DP0 TP0] Decode batch. #running-req: 2, #token: 4343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 621.68, #queue-req: 0, 
[2025-07-28 01:00:27 DP0 TP0] Decode batch. #running-req: 2, #token: 4423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 622.85, #queue-req: 0, 
[2025-07-28 01:00:27 DP0 TP0] Decode batch. #running-req: 2, #token: 4503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 618.15, #queue-req: 0, 
[2025-07-28 01:00:27 DP0 TP0] Decode batch. #running-req: 2, #token: 4583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 622.51, #queue-req: 0, 
[2025-07-28 01:00:27 DP0 TP0] Decode batch. #running-req: 2, #token: 4663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 623.71, #queue-req: 0, 
[2025-07-28 01:00:27 DP0 TP0] Decode batch. #running-req: 2, #token: 4743, token usage: 0.00, cuda graph: True, gen throughput (token/s): 623.67, #queue-req: 0, 
[2025-07-28 01:00:27 DP0 TP0] Decode batch. #running-req: 2, #token: 4823, token usage: 0.00, cuda graph: True, gen throughput (token/s): 614.97, #queue-req: 0, 
[2025-07-28 01:00:27 DP0 TP0] Decode batch. #running-req: 2, #token: 4903, token usage: 0.00, cuda graph: True, gen throughput (token/s): 621.29, #queue-req: 0, 
[2025-07-28 01:00:27 DP0 TP0] Decode batch. #running-req: 2, #token: 4983, token usage: 0.00, cuda graph: True, gen throughput (token/s): 621.10, #queue-req: 0, 
[2025-07-28 01:00:28 DP0 TP0] Decode batch. #running-req: 2, #token: 5063, token usage: 0.00, cuda graph: True, gen throughput (token/s): 620.93, #queue-req: 0, 
[2025-07-28 01:00:28 DP0 TP0] Decode batch. #running-req: 2, #token: 5143, token usage: 0.00, cuda graph: True, gen throughput (token/s): 612.90, #queue-req: 0, 
[2025-07-28 01:00:28 DP0 TP0] Decode batch. #running-req: 2, #token: 5223, token usage: 0.00, cuda graph: True, gen throughput (token/s): 620.86, #queue-req: 0, 
[2025-07-28 01:00:28 DP0 TP0] Decode batch. #running-req: 2, #token: 5303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 621.79, #queue-req: 0, 
[2025-07-28 01:00:28 DP0 TP0] Decode batch. #running-req: 2, #token: 5383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 621.43, #queue-req: 0, 
[2025-07-28 01:00:28 DP0 TP0] Decode batch. #running-req: 2, #token: 5463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 618.89, #queue-req: 0, 
[2025-07-28 01:00:28 DP0 TP0] Decode batch. #running-req: 2, #token: 5543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 596.31, #queue-req: 0, 
[2025-07-28 01:00:29 DP0 TP0] Decode batch. #running-req: 2, #token: 5623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 600.64, #queue-req: 0, 
[2025-07-28 01:00:29 DP0 TP0] Decode batch. #running-req: 2, #token: 5703, token usage: 0.00, cuda graph: True, gen throughput (token/s): 603.73, #queue-req: 0, 
[2025-07-28 01:00:29 DP0 TP0] Decode batch. #running-req: 2, #token: 5783, token usage: 0.00, cuda graph: True, gen throughput (token/s): 600.19, #queue-req: 0, 
[2025-07-28 01:00:29 DP0 TP0] Decode batch. #running-req: 2, #token: 5863, token usage: 0.00, cuda graph: True, gen throughput (token/s): 603.10, #queue-req: 0, 
[2025-07-28 01:00:29 DP0 TP0] Decode batch. #running-req: 2, #token: 5943, token usage: 0.00, cuda graph: True, gen throughput (token/s): 592.43, #queue-req: 0, 
[2025-07-28 01:00:29 DP0 TP0] Decode batch. #running-req: 2, #token: 6023, token usage: 0.00, cuda graph: True, gen throughput (token/s): 599.70, #queue-req: 0, 
[2025-07-28 01:00:29 DP0 TP0] Decode batch. #running-req: 2, #token: 6103, token usage: 0.00, cuda graph: True, gen throughput (token/s): 597.48, #queue-req: 0, 
[2025-07-28 01:00:29 DP0 TP0] Decode batch. #running-req: 2, #token: 6183, token usage: 0.00, cuda graph: True, gen throughput (token/s): 577.64, #queue-req: 0, 
[2025-07-28 01:00:30 DP0 TP0] Decode batch. #running-req: 2, #token: 6263, token usage: 0.00, cuda graph: True, gen throughput (token/s): 570.61, #queue-req: 0, 
[2025-07-28 01:00:30 DP0 TP0] Decode batch. #running-req: 2, #token: 6343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 570.54, #queue-req: 0, 
[2025-07-28 01:00:30 DP0 TP0] Decode batch. #running-req: 2, #token: 6423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 609.95, #queue-req: 0, 
[2025-07-28 01:00:30 DP0 TP0] Decode batch. #running-req: 2, #token: 6503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 606.83, #queue-req: 0, 
[2025-07-28 01:00:30 DP0 TP0] Decode batch. #running-req: 2, #token: 6583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 609.18, #queue-req: 0, 
[2025-07-28 01:00:30 DP0 TP0] Decode batch. #running-req: 2, #token: 6663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 606.42, #queue-req: 0, 
[2025-07-28 01:00:30 DP0 TP0] Decode batch. #running-req: 2, #token: 6743, token usage: 0.00, cuda graph: True, gen throughput (token/s): 599.03, #queue-req: 0, 
[2025-07-28 01:00:31 DP0 TP0] Decode batch. #running-req: 2, #token: 6823, token usage: 0.00, cuda graph: True, gen throughput (token/s): 599.58, #queue-req: 0, 
[2025-07-28 01:00:31 DP0 TP0] Decode batch. #running-req: 2, #token: 6903, token usage: 0.00, cuda graph: True, gen throughput (token/s): 599.78, #queue-req: 0, 
[2025-07-28 01:00:31 DP0 TP0] Decode batch. #running-req: 2, #token: 6983, token usage: 0.00, cuda graph: True, gen throughput (token/s): 601.56, #queue-req: 0, 
[2025-07-28 01:00:31] INFO:     127.0.0.1:35036 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:00:31 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 117, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:00:31 DP0 TP0] Decode batch. #running-req: 1, #token: 4952, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.51, #queue-req: 0, 
[2025-07-28 01:00:31 DP1 TP0] Decode batch. #running-req: 1, #token: 211, token usage: 0.00, cuda graph: True, gen throughput (token/s): 5.95, #queue-req: 0, 
[2025-07-28 01:00:31 DP0 TP0] Decode batch. #running-req: 1, #token: 4992, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.76, #queue-req: 0, 
[2025-07-28 01:00:31 DP1 TP0] Decode batch. #running-req: 1, #token: 251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.15, #queue-req: 0, 
[2025-07-28 01:00:31 DP0 TP0] Decode batch. #running-req: 1, #token: 5032, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.00, #queue-req: 0, 
[2025-07-28 01:00:31 DP1 TP0] Decode batch. #running-req: 1, #token: 291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.26, #queue-req: 0, 
[2025-07-28 01:00:31 DP0 TP0] Decode batch. #running-req: 1, #token: 5072, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.32, #queue-req: 0, 
[2025-07-28 01:00:31 DP1 TP0] Decode batch. #running-req: 1, #token: 331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.43, #queue-req: 0, 
[2025-07-28 01:00:31 DP0 TP0] Decode batch. #running-req: 1, #token: 5112, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.61, #queue-req: 0, 
[2025-07-28 01:00:32 DP1 TP0] Decode batch. #running-req: 1, #token: 371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.48, #queue-req: 0, 
[2025-07-28 01:00:32 DP0 TP0] Decode batch. #running-req: 1, #token: 5152, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.58, #queue-req: 0, 
[2025-07-28 01:00:32 DP1 TP0] Decode batch. #running-req: 1, #token: 411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.53, #queue-req: 0, 
[2025-07-28 01:00:32 DP0 TP0] Decode batch. #running-req: 1, #token: 5192, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.67, #queue-req: 0, 
[2025-07-28 01:00:32 DP1 TP0] Decode batch. #running-req: 1, #token: 451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.34, #queue-req: 0, 
[2025-07-28 01:00:32 DP0 TP0] Decode batch. #running-req: 1, #token: 5232, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.68, #queue-req: 0, 
[2025-07-28 01:00:32 DP1 TP0] Decode batch. #running-req: 1, #token: 491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.42, #queue-req: 0, 
[2025-07-28 01:00:32 DP0 TP0] Decode batch. #running-req: 1, #token: 5272, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.92, #queue-req: 0, 
[2025-07-28 01:00:32 DP1 TP0] Decode batch. #running-req: 1, #token: 531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.73, #queue-req: 0, 
[2025-07-28 01:00:32 DP0 TP0] Decode batch. #running-req: 1, #token: 5312, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.80, #queue-req: 0, 
[2025-07-28 01:00:32 DP1 TP0] Decode batch. #running-req: 1, #token: 571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.82, #queue-req: 0, 
[2025-07-28 01:00:32 DP0 TP0] Decode batch. #running-req: 1, #token: 5352, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.69, #queue-req: 0, 
[2025-07-28 01:00:32 DP1 TP0] Decode batch. #running-req: 1, #token: 611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.29, #queue-req: 0, 
[2025-07-28 01:00:32 DP0 TP0] Decode batch. #running-req: 1, #token: 5392, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.03, #queue-req: 0, 
[2025-07-28 01:00:32 DP1 TP0] Decode batch. #running-req: 1, #token: 651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.79, #queue-req: 0, 
[2025-07-28 01:00:33 DP0 TP0] Decode batch. #running-req: 1, #token: 5432, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.81, #queue-req: 0, 
[2025-07-28 01:00:33 DP1 TP0] Decode batch. #running-req: 1, #token: 691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.88, #queue-req: 0, 
[2025-07-28 01:00:33 DP0 TP0] Decode batch. #running-req: 1, #token: 5472, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.98, #queue-req: 0, 
[2025-07-28 01:00:33 DP1 TP0] Decode batch. #running-req: 1, #token: 731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.25, #queue-req: 0, 
[2025-07-28 01:00:33 DP0 TP0] Decode batch. #running-req: 1, #token: 5512, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.68, #queue-req: 0, 
[2025-07-28 01:00:33 DP1 TP0] Decode batch. #running-req: 1, #token: 771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.37, #queue-req: 0, 
[2025-07-28 01:00:33 DP0 TP0] Decode batch. #running-req: 1, #token: 5552, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.00, #queue-req: 0, 
[2025-07-28 01:00:33 DP1 TP0] Decode batch. #running-req: 1, #token: 811, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.87, #queue-req: 0, 
[2025-07-28 01:00:33 DP0 TP0] Decode batch. #running-req: 1, #token: 5592, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.94, #queue-req: 0, 
[2025-07-28 01:00:33 DP1 TP0] Decode batch. #running-req: 1, #token: 851, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.68, #queue-req: 0, 
[2025-07-28 01:00:33 DP0 TP0] Decode batch. #running-req: 1, #token: 5632, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.22, #queue-req: 0, 
[2025-07-28 01:00:33 DP1 TP0] Decode batch. #running-req: 1, #token: 891, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.86, #queue-req: 0, 
[2025-07-28 01:00:33 DP0 TP0] Decode batch. #running-req: 1, #token: 5672, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.79, #queue-req: 0, 
[2025-07-28 01:00:33 DP1 TP0] Decode batch. #running-req: 1, #token: 931, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.85, #queue-req: 0, 
[2025-07-28 01:00:33 DP0 TP0] Decode batch. #running-req: 1, #token: 5712, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.86, #queue-req: 0, 
[2025-07-28 01:00:33 DP1 TP0] Decode batch. #running-req: 1, #token: 971, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.14, #queue-req: 0, 
[2025-07-28 01:00:34 DP0 TP0] Decode batch. #running-req: 1, #token: 5752, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.65, #queue-req: 0, 
[2025-07-28 01:00:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1011, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.05, #queue-req: 0, 
[2025-07-28 01:00:34 DP0 TP0] Decode batch. #running-req: 1, #token: 5792, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.40, #queue-req: 0, 
[2025-07-28 01:00:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1051, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.89, #queue-req: 0, 
[2025-07-28 01:00:34 DP0 TP0] Decode batch. #running-req: 1, #token: 5832, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.13, #queue-req: 0, 
[2025-07-28 01:00:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1091, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.85, #queue-req: 0, 
[2025-07-28 01:00:34 DP0 TP0] Decode batch. #running-req: 1, #token: 5872, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.36, #queue-req: 0, 
[2025-07-28 01:00:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1131, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.69, #queue-req: 0, 
[2025-07-28 01:00:34 DP0 TP0] Decode batch. #running-req: 1, #token: 5912, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.25, #queue-req: 0, 
[2025-07-28 01:00:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1171, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.46, #queue-req: 0, 
[2025-07-28 01:00:34 DP0 TP0] Decode batch. #running-req: 1, #token: 5952, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.92, #queue-req: 0, 
[2025-07-28 01:00:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1211, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.57, #queue-req: 0, 
[2025-07-28 01:00:34 DP0 TP0] Decode batch. #running-req: 1, #token: 5992, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.52, #queue-req: 0, 
[2025-07-28 01:00:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.79, #queue-req: 0, 
[2025-07-28 01:00:34 DP0 TP0] Decode batch. #running-req: 1, #token: 6032, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.03, #queue-req: 0, 
[2025-07-28 01:00:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.73, #queue-req: 0, 
[2025-07-28 01:00:35 DP0 TP0] Decode batch. #running-req: 1, #token: 6072, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.23, #queue-req: 0, 
[2025-07-28 01:00:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.65, #queue-req: 0, 
[2025-07-28 01:00:35 DP0 TP0] Decode batch. #running-req: 1, #token: 6112, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.48, #queue-req: 0, 
[2025-07-28 01:00:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.73, #queue-req: 0, 
[2025-07-28 01:00:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.62, #queue-req: 0, 
[2025-07-28 01:00:35 DP0 TP0] Decode batch. #running-req: 1, #token: 6152, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.41, #queue-req: 0, 
[2025-07-28 01:00:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.66, #queue-req: 0, 
[2025-07-28 01:00:35 DP0 TP0] Decode batch. #running-req: 1, #token: 6192, token usage: 0.00, cuda graph: True, gen throughput (token/s): 275.76, #queue-req: 0, 
[2025-07-28 01:00:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.40, #queue-req: 0, 
[2025-07-28 01:00:35 DP0 TP0] Decode batch. #running-req: 1, #token: 6232, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.63, #queue-req: 0, 
[2025-07-28 01:00:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.57, #queue-req: 0, 
[2025-07-28 01:00:35 DP0 TP0] Decode batch. #running-req: 1, #token: 6272, token usage: 0.00, cuda graph: True, gen throughput (token/s): 278.86, #queue-req: 0, 
[2025-07-28 01:00:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.85, #queue-req: 0, 
[2025-07-28 01:00:35 DP0 TP0] Decode batch. #running-req: 1, #token: 6312, token usage: 0.00, cuda graph: True, gen throughput (token/s): 278.23, #queue-req: 0, 
[2025-07-28 01:00:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.13, #queue-req: 0, 
[2025-07-28 01:00:36 DP0 TP0] Decode batch. #running-req: 1, #token: 6352, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.74, #queue-req: 0, 
[2025-07-28 01:00:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.64, #queue-req: 0, 
[2025-07-28 01:00:36 DP0 TP0] Decode batch. #running-req: 1, #token: 6392, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.08, #queue-req: 0, 
[2025-07-28 01:00:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.90, #queue-req: 0, 
[2025-07-28 01:00:36] INFO:     127.0.0.1:35042 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:00:36 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 125, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:00:36 DP0 TP0] Decode batch. #running-req: 2, #token: 6572, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.07, #queue-req: 0, 
[2025-07-28 01:00:36 DP0 TP0] Decode batch. #running-req: 2, #token: 6652, token usage: 0.00, cuda graph: True, gen throughput (token/s): 618.93, #queue-req: 0, 
[2025-07-28 01:00:36 DP0 TP0] Decode batch. #running-req: 2, #token: 6732, token usage: 0.00, cuda graph: True, gen throughput (token/s): 610.67, #queue-req: 0, 
[2025-07-28 01:00:36 DP0 TP0] Decode batch. #running-req: 2, #token: 6812, token usage: 0.00, cuda graph: True, gen throughput (token/s): 614.27, #queue-req: 0, 
[2025-07-28 01:00:36 DP0 TP0] Decode batch. #running-req: 2, #token: 6892, token usage: 0.00, cuda graph: True, gen throughput (token/s): 615.49, #queue-req: 0, 
[2025-07-28 01:00:37 DP0 TP0] Decode batch. #running-req: 2, #token: 6972, token usage: 0.00, cuda graph: True, gen throughput (token/s): 610.32, #queue-req: 0, 
[2025-07-28 01:00:37 DP0 TP0] Decode batch. #running-req: 2, #token: 7052, token usage: 0.00, cuda graph: True, gen throughput (token/s): 609.12, #queue-req: 0, 
[2025-07-28 01:00:37 DP0 TP0] Decode batch. #running-req: 2, #token: 7132, token usage: 0.00, cuda graph: True, gen throughput (token/s): 603.53, #queue-req: 0, 
[2025-07-28 01:00:37 DP0 TP0] Decode batch. #running-req: 2, #token: 7212, token usage: 0.00, cuda graph: True, gen throughput (token/s): 598.49, #queue-req: 0, 
[2025-07-28 01:00:37 DP0 TP0] Decode batch. #running-req: 2, #token: 7292, token usage: 0.00, cuda graph: True, gen throughput (token/s): 602.09, #queue-req: 0, 
[2025-07-28 01:00:37 DP0 TP0] Decode batch. #running-req: 2, #token: 7372, token usage: 0.00, cuda graph: True, gen throughput (token/s): 600.33, #queue-req: 0, 
[2025-07-28 01:00:37 DP0 TP0] Decode batch. #running-req: 2, #token: 7452, token usage: 0.00, cuda graph: True, gen throughput (token/s): 601.16, #queue-req: 0, 
[2025-07-28 01:00:37 DP0 TP0] Decode batch. #running-req: 2, #token: 7532, token usage: 0.00, cuda graph: True, gen throughput (token/s): 598.62, #queue-req: 0, 
[2025-07-28 01:00:38 DP0 TP0] Decode batch. #running-req: 2, #token: 7612, token usage: 0.00, cuda graph: True, gen throughput (token/s): 599.03, #queue-req: 0, 
[2025-07-28 01:00:38 DP0 TP0] Decode batch. #running-req: 2, #token: 7692, token usage: 0.00, cuda graph: True, gen throughput (token/s): 600.72, #queue-req: 0, 
[2025-07-28 01:00:38 DP0 TP0] Decode batch. #running-req: 2, #token: 7772, token usage: 0.00, cuda graph: True, gen throughput (token/s): 597.89, #queue-req: 0, 
[2025-07-28 01:00:38 DP0 TP0] Decode batch. #running-req: 2, #token: 7852, token usage: 0.00, cuda graph: True, gen throughput (token/s): 600.15, #queue-req: 0, 
[2025-07-28 01:00:38] INFO:     127.0.0.1:49858 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:00:38 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 196, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:00:38 DP0 TP0] Decode batch. #running-req: 1, #token: 905, token usage: 0.00, cuda graph: True, gen throughput (token/s): 443.49, #queue-req: 0, 
[2025-07-28 01:00:38 DP0 TP0] Decode batch. #running-req: 1, #token: 945, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.33, #queue-req: 0, 
[2025-07-28 01:00:38 DP1 TP0] Decode batch. #running-req: 1, #token: 317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 15.77, #queue-req: 0, 
[2025-07-28 01:00:38 DP0 TP0] Decode batch. #running-req: 1, #token: 985, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.98, #queue-req: 0, 
[2025-07-28 01:00:38 DP1 TP0] Decode batch. #running-req: 1, #token: 357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.21, #queue-req: 0, 
[2025-07-28 01:00:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1025, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.37, #queue-req: 0, 
[2025-07-28 01:00:39 DP1 TP0] Decode batch. #running-req: 1, #token: 397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.36, #queue-req: 0, 
[2025-07-28 01:00:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1065, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.71, #queue-req: 0, 
[2025-07-28 01:00:39 DP1 TP0] Decode batch. #running-req: 1, #token: 437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.70, #queue-req: 0, 
[2025-07-28 01:00:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1105, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.44, #queue-req: 0, 
[2025-07-28 01:00:39 DP1 TP0] Decode batch. #running-req: 1, #token: 477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.22, #queue-req: 0, 
[2025-07-28 01:00:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1145, token usage: 0.00, cuda graph: True, gen throughput (token/s): 278.84, #queue-req: 0, 
[2025-07-28 01:00:39 DP1 TP0] Decode batch. #running-req: 1, #token: 517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.05, #queue-req: 0, 
[2025-07-28 01:00:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1185, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.97, #queue-req: 0, 
[2025-07-28 01:00:39 DP1 TP0] Decode batch. #running-req: 1, #token: 557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.48, #queue-req: 0, 
[2025-07-28 01:00:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1225, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.09, #queue-req: 0, 
[2025-07-28 01:00:39 DP1 TP0] Decode batch. #running-req: 1, #token: 597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.61, #queue-req: 0, 
[2025-07-28 01:00:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.58, #queue-req: 0, 
[2025-07-28 01:00:39 DP1 TP0] Decode batch. #running-req: 1, #token: 637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.72, #queue-req: 0, 
[2025-07-28 01:00:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.80, #queue-req: 0, 
[2025-07-28 01:00:39 DP1 TP0] Decode batch. #running-req: 1, #token: 677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.37, #queue-req: 0, 
[2025-07-28 01:00:40 DP0 TP0] Decode batch. #running-req: 1, #token: 1345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.61, #queue-req: 0, 
[2025-07-28 01:00:40 DP1 TP0] Decode batch. #running-req: 1, #token: 717, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.87, #queue-req: 0, 
[2025-07-28 01:00:40 DP0 TP0] Decode batch. #running-req: 1, #token: 1385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.12, #queue-req: 0, 
[2025-07-28 01:00:40 DP1 TP0] Decode batch. #running-req: 1, #token: 757, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.65, #queue-req: 0, 
[2025-07-28 01:00:40 DP0 TP0] Decode batch. #running-req: 1, #token: 1425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.09, #queue-req: 0, 
[2025-07-28 01:00:40 DP1 TP0] Decode batch. #running-req: 1, #token: 797, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.08, #queue-req: 0, 
[2025-07-28 01:00:40] INFO:     127.0.0.1:57452 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:00:40 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:00:40 DP1 TP0] Decode batch. #running-req: 1, #token: 837, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.77, #queue-req: 0, 
[2025-07-28 01:00:40 DP0 TP0] Decode batch. #running-req: 1, #token: 188, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.35, #queue-req: 0, 
[2025-07-28 01:00:40 DP1 TP0] Decode batch. #running-req: 1, #token: 877, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.61, #queue-req: 0, 
[2025-07-28 01:00:40 DP0 TP0] Decode batch. #running-req: 1, #token: 228, token usage: 0.00, cuda graph: True, gen throughput (token/s): 331.35, #queue-req: 0, 
[2025-07-28 01:00:40 DP1 TP0] Decode batch. #running-req: 1, #token: 917, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.18, #queue-req: 0, 
[2025-07-28 01:00:40 DP0 TP0] Decode batch. #running-req: 1, #token: 268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.17, #queue-req: 0, 
[2025-07-28 01:00:40 DP1 TP0] Decode batch. #running-req: 1, #token: 957, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.21, #queue-req: 0, 
[2025-07-28 01:00:40 DP0 TP0] Decode batch. #running-req: 1, #token: 308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.61, #queue-req: 0, 
[2025-07-28 01:00:40 DP1 TP0] Decode batch. #running-req: 1, #token: 997, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.89, #queue-req: 0, 
[2025-07-28 01:00:41 DP0 TP0] Decode batch. #running-req: 1, #token: 348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.21, #queue-req: 0, 
[2025-07-28 01:00:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1037, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.76, #queue-req: 0, 
[2025-07-28 01:00:41 DP0 TP0] Decode batch. #running-req: 1, #token: 388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.97, #queue-req: 0, 
[2025-07-28 01:00:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1077, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.20, #queue-req: 0, 
[2025-07-28 01:00:41 DP0 TP0] Decode batch. #running-req: 1, #token: 428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.56, #queue-req: 0, 
[2025-07-28 01:00:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1117, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.91, #queue-req: 0, 
[2025-07-28 01:00:41 DP0 TP0] Decode batch. #running-req: 1, #token: 468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.70, #queue-req: 0, 
[2025-07-28 01:00:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1157, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.51, #queue-req: 0, 
[2025-07-28 01:00:41 DP0 TP0] Decode batch. #running-req: 1, #token: 508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.16, #queue-req: 0, 
[2025-07-28 01:00:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1197, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.97, #queue-req: 0, 
[2025-07-28 01:00:41 DP0 TP0] Decode batch. #running-req: 1, #token: 548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.97, #queue-req: 0, 
[2025-07-28 01:00:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1237, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.57, #queue-req: 0, 
[2025-07-28 01:00:41 DP0 TP0] Decode batch. #running-req: 1, #token: 588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.30, #queue-req: 0, 
[2025-07-28 01:00:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.68, #queue-req: 0, 
[2025-07-28 01:00:41 DP0 TP0] Decode batch. #running-req: 1, #token: 628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.00, #queue-req: 0, 
[2025-07-28 01:00:42 DP1 TP0] Decode batch. #running-req: 1, #token: 1317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.63, #queue-req: 0, 
[2025-07-28 01:00:42 DP0 TP0] Decode batch. #running-req: 1, #token: 668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.73, #queue-req: 0, 
[2025-07-28 01:00:42 DP1 TP0] Decode batch. #running-req: 1, #token: 1357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.93, #queue-req: 0, 
[2025-07-28 01:00:42 DP0 TP0] Decode batch. #running-req: 1, #token: 708, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.87, #queue-req: 0, 
[2025-07-28 01:00:42 DP1 TP0] Decode batch. #running-req: 1, #token: 1397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.27, #queue-req: 0, 
[2025-07-28 01:00:42 DP0 TP0] Decode batch. #running-req: 1, #token: 748, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.21, #queue-req: 0, 
[2025-07-28 01:00:42 DP1 TP0] Decode batch. #running-req: 1, #token: 1437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.32, #queue-req: 0, 
[2025-07-28 01:00:42 DP0 TP0] Decode batch. #running-req: 1, #token: 788, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.24, #queue-req: 0, 
[2025-07-28 01:00:42 DP1 TP0] Decode batch. #running-req: 1, #token: 1477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.92, #queue-req: 0, 
[2025-07-28 01:00:42 DP0 TP0] Decode batch. #running-req: 1, #token: 828, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.45, #queue-req: 0, 
[2025-07-28 01:00:42 DP1 TP0] Decode batch. #running-req: 1, #token: 1517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.22, #queue-req: 0, 
[2025-07-28 01:00:42 DP0 TP0] Decode batch. #running-req: 1, #token: 868, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.92, #queue-req: 0, 
[2025-07-28 01:00:42 DP1 TP0] Decode batch. #running-req: 1, #token: 1557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.00, #queue-req: 0, 
[2025-07-28 01:00:42 DP0 TP0] Decode batch. #running-req: 1, #token: 908, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.49, #queue-req: 0, 
[2025-07-28 01:00:42 DP1 TP0] Decode batch. #running-req: 1, #token: 1597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.66, #queue-req: 0, 
[2025-07-28 01:00:42 DP0 TP0] Decode batch. #running-req: 1, #token: 948, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.70, #queue-req: 0, 
[2025-07-28 01:00:43 DP1 TP0] Decode batch. #running-req: 1, #token: 1637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.20, #queue-req: 0, 
[2025-07-28 01:00:43 DP0 TP0] Decode batch. #running-req: 1, #token: 988, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.77, #queue-req: 0, 
[2025-07-28 01:00:43 DP1 TP0] Decode batch. #running-req: 1, #token: 1677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.25, #queue-req: 0, 
[2025-07-28 01:00:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1028, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.31, #queue-req: 0, 
[2025-07-28 01:00:43 DP1 TP0] Decode batch. #running-req: 1, #token: 1717, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.62, #queue-req: 0, 
[2025-07-28 01:00:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1068, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.74, #queue-req: 0, 
[2025-07-28 01:00:43 DP1 TP0] Decode batch. #running-req: 1, #token: 1757, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.76, #queue-req: 0, 
[2025-07-28 01:00:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1108, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.17, #queue-req: 0, 
[2025-07-28 01:00:43 DP1 TP0] Decode batch. #running-req: 1, #token: 1797, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.21, #queue-req: 0, 
[2025-07-28 01:00:43] INFO:     127.0.0.1:57466 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:00:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1148, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.23, #queue-req: 0, 
[2025-07-28 01:00:43 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 154, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:00:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1188, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.91, #queue-req: 0, 
[2025-07-28 01:00:43 DP1 TP0] Decode batch. #running-req: 1, #token: 276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.22, #queue-req: 0, 
[2025-07-28 01:00:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1228, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.05, #queue-req: 0, 
[2025-07-28 01:00:43 DP1 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 331.32, #queue-req: 0, 
[2025-07-28 01:00:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.36, #queue-req: 0, 
[2025-07-28 01:00:44 DP1 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.53, #queue-req: 0, 
[2025-07-28 01:00:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.64, #queue-req: 0, 
[2025-07-28 01:00:44 DP1 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.21, #queue-req: 0, 
[2025-07-28 01:00:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.22, #queue-req: 0, 
[2025-07-28 01:00:44 DP1 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.93, #queue-req: 0, 
[2025-07-28 01:00:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.05, #queue-req: 0, 
[2025-07-28 01:00:44 DP1 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.28, #queue-req: 0, 
[2025-07-28 01:00:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.17, #queue-req: 0, 
[2025-07-28 01:00:44 DP1 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.70, #queue-req: 0, 
[2025-07-28 01:00:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.09, #queue-req: 0, 
[2025-07-28 01:00:44 DP1 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.45, #queue-req: 0, 
[2025-07-28 01:00:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.30, #queue-req: 0, 
[2025-07-28 01:00:44 DP1 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.02, #queue-req: 0, 
[2025-07-28 01:00:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.26, #queue-req: 0, 
[2025-07-28 01:00:44 DP1 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.00, #queue-req: 0, 
[2025-07-28 01:00:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.64, #queue-req: 0, 
[2025-07-28 01:00:45 DP1 TP0] Decode batch. #running-req: 1, #token: 676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.65, #queue-req: 0, 
[2025-07-28 01:00:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.48, #queue-req: 0, 
[2025-07-28 01:00:45 DP1 TP0] Decode batch. #running-req: 1, #token: 716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.23, #queue-req: 0, 
[2025-07-28 01:00:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.99, #queue-req: 0, 
[2025-07-28 01:00:45 DP1 TP0] Decode batch. #running-req: 1, #token: 756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.40, #queue-req: 0, 
[2025-07-28 01:00:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1708, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.84, #queue-req: 0, 
[2025-07-28 01:00:45 DP1 TP0] Decode batch. #running-req: 1, #token: 796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.23, #queue-req: 0, 
[2025-07-28 01:00:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1748, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.59, #queue-req: 0, 
[2025-07-28 01:00:45 DP1 TP0] Decode batch. #running-req: 1, #token: 836, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.30, #queue-req: 0, 
[2025-07-28 01:00:45] INFO:     127.0.0.1:57480 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:00:45 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 128, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:00:45 DP1 TP0] Decode batch. #running-req: 1, #token: 876, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.67, #queue-req: 0, 
[2025-07-28 01:00:45 DP0 TP0] Decode batch. #running-req: 1, #token: 239, token usage: 0.00, cuda graph: True, gen throughput (token/s): 183.67, #queue-req: 0, 
[2025-07-28 01:00:45 DP1 TP0] Decode batch. #running-req: 1, #token: 916, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.45, #queue-req: 0, 
[2025-07-28 01:00:45 DP0 TP0] Decode batch. #running-req: 1, #token: 279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.51, #queue-req: 0, 
[2025-07-28 01:00:45 DP1 TP0] Decode batch. #running-req: 1, #token: 956, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.64, #queue-req: 0, 
[2025-07-28 01:00:46 DP0 TP0] Decode batch. #running-req: 1, #token: 319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.73, #queue-req: 0, 
[2025-07-28 01:00:46 DP1 TP0] Decode batch. #running-req: 1, #token: 996, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.02, #queue-req: 0, 
[2025-07-28 01:00:46 DP0 TP0] Decode batch. #running-req: 1, #token: 359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.45, #queue-req: 0, 
[2025-07-28 01:00:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1036, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.30, #queue-req: 0, 
[2025-07-28 01:00:46 DP0 TP0] Decode batch. #running-req: 1, #token: 399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.47, #queue-req: 0, 
[2025-07-28 01:00:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1076, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.86, #queue-req: 0, 
[2025-07-28 01:00:46 DP0 TP0] Decode batch. #running-req: 1, #token: 439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.04, #queue-req: 0, 
[2025-07-28 01:00:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1116, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.68, #queue-req: 0, 
[2025-07-28 01:00:46 DP0 TP0] Decode batch. #running-req: 1, #token: 479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.00, #queue-req: 0, 
[2025-07-28 01:00:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1156, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.51, #queue-req: 0, 
[2025-07-28 01:00:46 DP0 TP0] Decode batch. #running-req: 1, #token: 519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.93, #queue-req: 0, 
[2025-07-28 01:00:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1196, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.11, #queue-req: 0, 
[2025-07-28 01:00:46 DP0 TP0] Decode batch. #running-req: 1, #token: 559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.71, #queue-req: 0, 
[2025-07-28 01:00:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1236, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.56, #queue-req: 0, 
[2025-07-28 01:00:46 DP0 TP0] Decode batch. #running-req: 1, #token: 599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.65, #queue-req: 0, 
[2025-07-28 01:00:47 DP1 TP0] Decode batch. #running-req: 1, #token: 1276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.95, #queue-req: 0, 
[2025-07-28 01:00:47] INFO:     127.0.0.1:36882 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:00:47 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:00:47 DP0 TP0] Decode batch. #running-req: 1, #token: 639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.35, #queue-req: 0, 
[2025-07-28 01:00:47 DP1 TP0] Decode batch. #running-req: 1, #token: 266, token usage: 0.00, cuda graph: True, gen throughput (token/s): 185.50, #queue-req: 0, 
[2025-07-28 01:00:47 DP0 TP0] Decode batch. #running-req: 1, #token: 679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.57, #queue-req: 0, 
[2025-07-28 01:00:47 DP1 TP0] Decode batch. #running-req: 1, #token: 306, token usage: 0.00, cuda graph: True, gen throughput (token/s): 320.86, #queue-req: 0, 
[2025-07-28 01:00:47 DP0 TP0] Decode batch. #running-req: 1, #token: 719, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.06, #queue-req: 0, 
[2025-07-28 01:00:47 DP1 TP0] Decode batch. #running-req: 1, #token: 346, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.43, #queue-req: 0, 
[2025-07-28 01:00:47 DP0 TP0] Decode batch. #running-req: 1, #token: 759, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.16, #queue-req: 0, 
[2025-07-28 01:00:47 DP1 TP0] Decode batch. #running-req: 1, #token: 386, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.53, #queue-req: 0, 
[2025-07-28 01:00:47 DP0 TP0] Decode batch. #running-req: 1, #token: 799, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.59, #queue-req: 0, 
[2025-07-28 01:00:47 DP1 TP0] Decode batch. #running-req: 1, #token: 426, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.23, #queue-req: 0, 
[2025-07-28 01:00:47 DP0 TP0] Decode batch. #running-req: 1, #token: 839, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.53, #queue-req: 0, 
[2025-07-28 01:00:47 DP1 TP0] Decode batch. #running-req: 1, #token: 466, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.84, #queue-req: 0, 
[2025-07-28 01:00:47 DP0 TP0] Decode batch. #running-req: 1, #token: 879, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.32, #queue-req: 0, 
[2025-07-28 01:00:47 DP1 TP0] Decode batch. #running-req: 1, #token: 506, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.06, #queue-req: 0, 
[2025-07-28 01:00:48 DP0 TP0] Decode batch. #running-req: 1, #token: 919, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.13, #queue-req: 0, 
[2025-07-28 01:00:48 DP1 TP0] Decode batch. #running-req: 1, #token: 546, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.43, #queue-req: 0, 
[2025-07-28 01:00:48 DP0 TP0] Decode batch. #running-req: 1, #token: 959, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.62, #queue-req: 0, 
[2025-07-28 01:00:48 DP1 TP0] Decode batch. #running-req: 1, #token: 586, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.41, #queue-req: 0, 
[2025-07-28 01:00:48 DP0 TP0] Decode batch. #running-req: 1, #token: 999, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.76, #queue-req: 0, 
[2025-07-28 01:00:48 DP1 TP0] Decode batch. #running-req: 1, #token: 626, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.03, #queue-req: 0, 
[2025-07-28 01:00:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1039, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.21, #queue-req: 0, 
[2025-07-28 01:00:48 DP1 TP0] Decode batch. #running-req: 1, #token: 666, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.45, #queue-req: 0, 
[2025-07-28 01:00:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1079, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.17, #queue-req: 0, 
[2025-07-28 01:00:48 DP1 TP0] Decode batch. #running-req: 1, #token: 706, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.44, #queue-req: 0, 
[2025-07-28 01:00:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1119, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.61, #queue-req: 0, 
[2025-07-28 01:00:48 DP1 TP0] Decode batch. #running-req: 1, #token: 746, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.94, #queue-req: 0, 
[2025-07-28 01:00:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1159, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.73, #queue-req: 0, 
[2025-07-28 01:00:48 DP1 TP0] Decode batch. #running-req: 1, #token: 786, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.18, #queue-req: 0, 
[2025-07-28 01:00:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1199, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.22, #queue-req: 0, 
[2025-07-28 01:00:49 DP1 TP0] Decode batch. #running-req: 1, #token: 826, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.94, #queue-req: 0, 
[2025-07-28 01:00:49 DP0 TP0] Decode batch. #running-req: 1, #token: 1239, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.94, #queue-req: 0, 
[2025-07-28 01:00:49 DP1 TP0] Decode batch. #running-req: 1, #token: 866, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.51, #queue-req: 0, 
[2025-07-28 01:00:49 DP0 TP0] Decode batch. #running-req: 1, #token: 1279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.96, #queue-req: 0, 
[2025-07-28 01:00:49 DP1 TP0] Decode batch. #running-req: 1, #token: 906, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.65, #queue-req: 0, 
[2025-07-28 01:00:49 DP0 TP0] Decode batch. #running-req: 1, #token: 1319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.95, #queue-req: 0, 
[2025-07-28 01:00:49 DP1 TP0] Decode batch. #running-req: 1, #token: 946, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.63, #queue-req: 0, 
[2025-07-28 01:00:49 DP0 TP0] Decode batch. #running-req: 1, #token: 1359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.43, #queue-req: 0, 
[2025-07-28 01:00:49 DP1 TP0] Decode batch. #running-req: 1, #token: 986, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.70, #queue-req: 0, 
[2025-07-28 01:00:49 DP0 TP0] Decode batch. #running-req: 1, #token: 1399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.14, #queue-req: 0, 
[2025-07-28 01:00:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1026, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.58, #queue-req: 0, 
[2025-07-28 01:00:49 DP0 TP0] Decode batch. #running-req: 1, #token: 1439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.66, #queue-req: 0, 
[2025-07-28 01:00:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1066, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.34, #queue-req: 0, 
[2025-07-28 01:00:49 DP0 TP0] Decode batch. #running-req: 1, #token: 1479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.66, #queue-req: 0, 
[2025-07-28 01:00:49] INFO:     127.0.0.1:36910 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:00:49 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 88, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:00:50 DP0 TP0] Decode batch. #running-req: 2, #token: 1690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 366.16, #queue-req: 0, 
[2025-07-28 01:00:50 DP0 TP0] Decode batch. #running-req: 2, #token: 1770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 631.81, #queue-req: 0, 
[2025-07-28 01:00:50 DP0 TP0] Decode batch. #running-req: 2, #token: 1850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 617.07, #queue-req: 0, 
[2025-07-28 01:00:50 DP0 TP0] Decode batch. #running-req: 2, #token: 1930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 630.56, #queue-req: 0, 
[2025-07-28 01:00:50 DP0 TP0] Decode batch. #running-req: 2, #token: 2010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 627.25, #queue-req: 0, 
[2025-07-28 01:00:50 DP0 TP0] Decode batch. #running-req: 2, #token: 2090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 626.49, #queue-req: 0, 
[2025-07-28 01:00:50 DP0 TP0] Decode batch. #running-req: 2, #token: 2170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 628.24, #queue-req: 0, 
[2025-07-28 01:00:50 DP0 TP0] Decode batch. #running-req: 2, #token: 2250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 628.10, #queue-req: 0, 
[2025-07-28 01:00:51 DP0 TP0] Decode batch. #running-req: 2, #token: 2330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 627.80, #queue-req: 0, 
[2025-07-28 01:00:51 DP0 TP0] Decode batch. #running-req: 2, #token: 2410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 622.16, #queue-req: 0, 
[2025-07-28 01:00:51 DP0 TP0] Decode batch. #running-req: 2, #token: 2490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 619.48, #queue-req: 0, 
[2025-07-28 01:00:51 DP0 TP0] Decode batch. #running-req: 2, #token: 2570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 623.66, #queue-req: 0, 
[2025-07-28 01:00:51 DP0 TP0] Decode batch. #running-req: 2, #token: 2650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 622.97, #queue-req: 0, 
[2025-07-28 01:00:51 DP0 TP0] Decode batch. #running-req: 2, #token: 2730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 621.98, #queue-req: 0, 
[2025-07-28 01:00:51 DP0 TP0] Decode batch. #running-req: 2, #token: 2810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 619.91, #queue-req: 0, 
[2025-07-28 01:00:51 DP0 TP0] Decode batch. #running-req: 2, #token: 2890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 623.63, #queue-req: 0, 
[2025-07-28 01:00:52 DP0 TP0] Decode batch. #running-req: 2, #token: 2970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 625.16, #queue-req: 0, 
[2025-07-28 01:00:52 DP0 TP0] Decode batch. #running-req: 2, #token: 3050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 571.39, #queue-req: 0, 
[2025-07-28 01:00:52 DP0 TP0] Decode batch. #running-req: 2, #token: 3130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 612.19, #queue-req: 0, 
[2025-07-28 01:00:52 DP0 TP0] Decode batch. #running-req: 2, #token: 3210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 622.56, #queue-req: 0, 
[2025-07-28 01:00:52 DP0 TP0] Decode batch. #running-req: 2, #token: 3290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 616.17, #queue-req: 0, 
[2025-07-28 01:00:52 DP0 TP0] Decode batch. #running-req: 2, #token: 3370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 623.74, #queue-req: 0, 
[2025-07-28 01:00:52] INFO:     127.0.0.1:36894 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:00:52 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 95, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:00:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1137, token usage: 0.00, cuda graph: True, gen throughput (token/s): 552.91, #queue-req: 0, 
[2025-07-28 01:00:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1177, token usage: 0.00, cuda graph: True, gen throughput (token/s): 338.61, #queue-req: 0, 
[2025-07-28 01:00:52 DP1 TP0] Decode batch. #running-req: 1, #token: 208, token usage: 0.00, cuda graph: True, gen throughput (token/s): 12.49, #queue-req: 0, 
[2025-07-28 01:00:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1217, token usage: 0.00, cuda graph: True, gen throughput (token/s): 336.52, #queue-req: 0, 
[2025-07-28 01:00:53 DP1 TP0] Decode batch. #running-req: 1, #token: 248, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.33, #queue-req: 0, 
[2025-07-28 01:00:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1257, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.52, #queue-req: 0, 
[2025-07-28 01:00:53 DP1 TP0] Decode batch. #running-req: 1, #token: 288, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.60, #queue-req: 0, 
[2025-07-28 01:00:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1297, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.01, #queue-req: 0, 
[2025-07-28 01:00:53 DP1 TP0] Decode batch. #running-req: 1, #token: 328, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.60, #queue-req: 0, 
[2025-07-28 01:00:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1337, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.72, #queue-req: 0, 
[2025-07-28 01:00:53 DP1 TP0] Decode batch. #running-req: 1, #token: 368, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.64, #queue-req: 0, 
[2025-07-28 01:00:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1377, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.04, #queue-req: 0, 
[2025-07-28 01:00:53 DP1 TP0] Decode batch. #running-req: 1, #token: 408, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.43, #queue-req: 0, 
[2025-07-28 01:00:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1417, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.31, #queue-req: 0, 
[2025-07-28 01:00:53 DP1 TP0] Decode batch. #running-req: 1, #token: 448, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.46, #queue-req: 0, 
[2025-07-28 01:00:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1457, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.15, #queue-req: 0, 
[2025-07-28 01:00:53 DP1 TP0] Decode batch. #running-req: 1, #token: 488, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.49, #queue-req: 0, 
[2025-07-28 01:00:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1497, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.39, #queue-req: 0, 
[2025-07-28 01:00:54 DP1 TP0] Decode batch. #running-req: 1, #token: 528, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.82, #queue-req: 0, 
[2025-07-28 01:00:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1537, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.02, #queue-req: 0, 
[2025-07-28 01:00:54 DP1 TP0] Decode batch. #running-req: 1, #token: 568, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.25, #queue-req: 0, 
[2025-07-28 01:00:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1577, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.72, #queue-req: 0, 
[2025-07-28 01:00:54 DP1 TP0] Decode batch. #running-req: 1, #token: 608, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.52, #queue-req: 0, 
[2025-07-28 01:00:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1617, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.20, #queue-req: 0, 
[2025-07-28 01:00:54 DP1 TP0] Decode batch. #running-req: 1, #token: 648, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.63, #queue-req: 0, 
[2025-07-28 01:00:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1657, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.62, #queue-req: 0, 
[2025-07-28 01:00:54 DP1 TP0] Decode batch. #running-req: 1, #token: 688, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.04, #queue-req: 0, 
[2025-07-28 01:00:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1697, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.56, #queue-req: 0, 
[2025-07-28 01:00:54 DP1 TP0] Decode batch. #running-req: 1, #token: 728, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.16, #queue-req: 0, 
[2025-07-28 01:00:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1737, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.96, #queue-req: 0, 
[2025-07-28 01:00:54 DP1 TP0] Decode batch. #running-req: 1, #token: 768, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.86, #queue-req: 0, 
[2025-07-28 01:00:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1777, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.36, #queue-req: 0, 
[2025-07-28 01:00:54 DP1 TP0] Decode batch. #running-req: 1, #token: 808, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.70, #queue-req: 0, 
[2025-07-28 01:00:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1817, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.09, #queue-req: 0, 
[2025-07-28 01:00:55 DP1 TP0] Decode batch. #running-req: 1, #token: 848, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.88, #queue-req: 0, 
[2025-07-28 01:00:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1857, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.31, #queue-req: 0, 
[2025-07-28 01:00:55 DP1 TP0] Decode batch. #running-req: 1, #token: 888, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.28, #queue-req: 0, 
[2025-07-28 01:00:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1897, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.18, #queue-req: 0, 
[2025-07-28 01:00:55 DP1 TP0] Decode batch. #running-req: 1, #token: 928, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.26, #queue-req: 0, 
[2025-07-28 01:00:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1937, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.63, #queue-req: 0, 
[2025-07-28 01:00:55 DP1 TP0] Decode batch. #running-req: 1, #token: 968, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.67, #queue-req: 0, 
[2025-07-28 01:00:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1977, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.62, #queue-req: 0, 
[2025-07-28 01:00:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1008, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.92, #queue-req: 0, 
[2025-07-28 01:00:55 DP0 TP0] Decode batch. #running-req: 1, #token: 2017, token usage: 0.00, cuda graph: True, gen throughput (token/s): 279.81, #queue-req: 0, 
[2025-07-28 01:00:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1048, token usage: 0.00, cuda graph: True, gen throughput (token/s): 276.90, #queue-req: 0, 
[2025-07-28 01:00:55 DP0 TP0] Decode batch. #running-req: 1, #token: 2057, token usage: 0.00, cuda graph: True, gen throughput (token/s): 261.95, #queue-req: 0, 
[2025-07-28 01:00:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1088, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.12, #queue-req: 0, 
[2025-07-28 01:00:55 DP0 TP0] Decode batch. #running-req: 1, #token: 2097, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.17, #queue-req: 0, 
[2025-07-28 01:00:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1128, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.39, #queue-req: 0, 
[2025-07-28 01:00:56 DP0 TP0] Decode batch. #running-req: 1, #token: 2137, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.26, #queue-req: 0, 
[2025-07-28 01:00:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1168, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.34, #queue-req: 0, 
[2025-07-28 01:00:56] INFO:     127.0.0.1:46396 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:00:56 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 86, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:00:56 DP0 TP0] Decode batch. #running-req: 2, #token: 2339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 329.57, #queue-req: 0, 
[2025-07-28 01:00:56 DP0 TP0] Decode batch. #running-req: 2, #token: 2419, token usage: 0.00, cuda graph: True, gen throughput (token/s): 628.43, #queue-req: 0, 
[2025-07-28 01:00:56 DP0 TP0] Decode batch. #running-req: 2, #token: 2499, token usage: 0.00, cuda graph: True, gen throughput (token/s): 627.70, #queue-req: 0, 
[2025-07-28 01:00:56 DP0 TP0] Decode batch. #running-req: 2, #token: 2579, token usage: 0.00, cuda graph: True, gen throughput (token/s): 627.80, #queue-req: 0, 
[2025-07-28 01:00:56 DP0 TP0] Decode batch. #running-req: 2, #token: 2659, token usage: 0.00, cuda graph: True, gen throughput (token/s): 627.28, #queue-req: 0, 
[2025-07-28 01:00:56 DP0 TP0] Decode batch. #running-req: 2, #token: 2739, token usage: 0.00, cuda graph: True, gen throughput (token/s): 627.75, #queue-req: 0, 
[2025-07-28 01:00:57 DP0 TP0] Decode batch. #running-req: 2, #token: 2819, token usage: 0.00, cuda graph: True, gen throughput (token/s): 627.94, #queue-req: 0, 
[2025-07-28 01:00:57 DP0 TP0] Decode batch. #running-req: 2, #token: 2899, token usage: 0.00, cuda graph: True, gen throughput (token/s): 627.08, #queue-req: 0, 
[2025-07-28 01:00:57 DP0 TP0] Decode batch. #running-req: 2, #token: 2979, token usage: 0.00, cuda graph: True, gen throughput (token/s): 626.05, #queue-req: 0, 
[2025-07-28 01:00:57 DP0 TP0] Decode batch. #running-req: 2, #token: 3059, token usage: 0.00, cuda graph: True, gen throughput (token/s): 628.21, #queue-req: 0, 
[2025-07-28 01:00:57 DP0 TP0] Decode batch. #running-req: 2, #token: 3139, token usage: 0.00, cuda graph: True, gen throughput (token/s): 627.91, #queue-req: 0, 
[2025-07-28 01:00:57 DP0 TP0] Decode batch. #running-req: 2, #token: 3219, token usage: 0.00, cuda graph: True, gen throughput (token/s): 626.13, #queue-req: 0, 
[2025-07-28 01:00:57 DP0 TP0] Decode batch. #running-req: 2, #token: 3299, token usage: 0.00, cuda graph: True, gen throughput (token/s): 625.39, #queue-req: 0, 
[2025-07-28 01:00:57 DP0 TP0] Decode batch. #running-req: 2, #token: 3379, token usage: 0.00, cuda graph: True, gen throughput (token/s): 624.99, #queue-req: 0, 
[2025-07-28 01:00:58 DP0 TP0] Decode batch. #running-req: 2, #token: 3459, token usage: 0.00, cuda graph: True, gen throughput (token/s): 626.02, #queue-req: 0, 
[2025-07-28 01:00:58 DP0 TP0] Decode batch. #running-req: 2, #token: 3539, token usage: 0.00, cuda graph: True, gen throughput (token/s): 626.51, #queue-req: 0, 
[2025-07-28 01:00:58 DP0 TP0] Decode batch. #running-req: 2, #token: 3619, token usage: 0.00, cuda graph: True, gen throughput (token/s): 623.27, #queue-req: 0, 
[2025-07-28 01:00:58 DP0 TP0] Decode batch. #running-req: 2, #token: 3699, token usage: 0.00, cuda graph: True, gen throughput (token/s): 625.45, #queue-req: 0, 
[2025-07-28 01:00:58 DP0 TP0] Decode batch. #running-req: 2, #token: 3779, token usage: 0.00, cuda graph: True, gen throughput (token/s): 625.38, #queue-req: 0, 
[2025-07-28 01:00:58 DP0 TP0] Decode batch. #running-req: 2, #token: 3859, token usage: 0.00, cuda graph: True, gen throughput (token/s): 589.15, #queue-req: 0, 
[2025-07-28 01:00:58 DP0 TP0] Decode batch. #running-req: 2, #token: 3939, token usage: 0.00, cuda graph: True, gen throughput (token/s): 584.36, #queue-req: 0, 
[2025-07-28 01:00:58 DP0 TP0] Decode batch. #running-req: 2, #token: 4019, token usage: 0.00, cuda graph: True, gen throughput (token/s): 580.89, #queue-req: 0, 
[2025-07-28 01:00:59 DP0 TP0] Decode batch. #running-req: 2, #token: 4099, token usage: 0.00, cuda graph: True, gen throughput (token/s): 584.88, #queue-req: 0, 
[2025-07-28 01:00:59 DP0 TP0] Decode batch. #running-req: 2, #token: 4179, token usage: 0.00, cuda graph: True, gen throughput (token/s): 584.95, #queue-req: 0, 
[2025-07-28 01:00:59 DP0 TP0] Decode batch. #running-req: 2, #token: 4259, token usage: 0.00, cuda graph: True, gen throughput (token/s): 590.32, #queue-req: 0, 
[2025-07-28 01:00:59 DP0 TP0] Decode batch. #running-req: 2, #token: 4339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 609.34, #queue-req: 0, 
[2025-07-28 01:00:59 DP0 TP0] Decode batch. #running-req: 2, #token: 4419, token usage: 0.00, cuda graph: True, gen throughput (token/s): 602.18, #queue-req: 0, 
[2025-07-28 01:00:59 DP0 TP0] Decode batch. #running-req: 2, #token: 4499, token usage: 0.00, cuda graph: True, gen throughput (token/s): 596.63, #queue-req: 0, 
[2025-07-28 01:00:59 DP0 TP0] Decode batch. #running-req: 2, #token: 4579, token usage: 0.00, cuda graph: True, gen throughput (token/s): 588.40, #queue-req: 0, 
[2025-07-28 01:01:00 DP0 TP0] Decode batch. #running-req: 2, #token: 4659, token usage: 0.00, cuda graph: True, gen throughput (token/s): 595.70, #queue-req: 0, 
[2025-07-28 01:01:00 DP0 TP0] Decode batch. #running-req: 2, #token: 4739, token usage: 0.00, cuda graph: True, gen throughput (token/s): 597.35, #queue-req: 0, 
[2025-07-28 01:01:00 DP0 TP0] Decode batch. #running-req: 2, #token: 4819, token usage: 0.00, cuda graph: True, gen throughput (token/s): 596.04, #queue-req: 0, 
[2025-07-28 01:01:00 DP0 TP0] Decode batch. #running-req: 2, #token: 4899, token usage: 0.00, cuda graph: True, gen throughput (token/s): 594.48, #queue-req: 0, 
[2025-07-28 01:01:00 DP0 TP0] Decode batch. #running-req: 2, #token: 4979, token usage: 0.00, cuda graph: True, gen throughput (token/s): 597.66, #queue-req: 0, 
[2025-07-28 01:01:00] INFO:     127.0.0.1:36920 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:01:00 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 78, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:01:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1608, token usage: 0.00, cuda graph: True, gen throughput (token/s): 457.22, #queue-req: 0, 
[2025-07-28 01:01:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1648, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.90, #queue-req: 0, 
[2025-07-28 01:01:00 DP1 TP0] Decode batch. #running-req: 1, #token: 201, token usage: 0.00, cuda graph: True, gen throughput (token/s): 8.39, #queue-req: 0, 
[2025-07-28 01:01:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1688, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.64, #queue-req: 0, 
[2025-07-28 01:01:01 DP1 TP0] Decode batch. #running-req: 1, #token: 241, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.10, #queue-req: 0, 
[2025-07-28 01:01:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1728, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.32, #queue-req: 0, 
[2025-07-28 01:01:01 DP1 TP0] Decode batch. #running-req: 1, #token: 281, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.53, #queue-req: 0, 
[2025-07-28 01:01:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1768, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.20, #queue-req: 0, 
[2025-07-28 01:01:01 DP1 TP0] Decode batch. #running-req: 1, #token: 321, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.42, #queue-req: 0, 
[2025-07-28 01:01:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1808, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.58, #queue-req: 0, 
[2025-07-28 01:01:01 DP1 TP0] Decode batch. #running-req: 1, #token: 361, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.58, #queue-req: 0, 
[2025-07-28 01:01:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1848, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.75, #queue-req: 0, 
[2025-07-28 01:01:01 DP1 TP0] Decode batch. #running-req: 1, #token: 401, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.72, #queue-req: 0, 
[2025-07-28 01:01:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1888, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.76, #queue-req: 0, 
[2025-07-28 01:01:01 DP1 TP0] Decode batch. #running-req: 1, #token: 441, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.25, #queue-req: 0, 
[2025-07-28 01:01:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1928, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.08, #queue-req: 0, 
[2025-07-28 01:01:01 DP1 TP0] Decode batch. #running-req: 1, #token: 481, token usage: 0.00, cuda graph: True, gen throughput (token/s): 288.34, #queue-req: 0, 
[2025-07-28 01:01:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1968, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.41, #queue-req: 0, 
[2025-07-28 01:01:01 DP1 TP0] Decode batch. #running-req: 1, #token: 521, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.39, #queue-req: 0, 
[2025-07-28 01:01:01 DP0 TP0] Decode batch. #running-req: 1, #token: 2008, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.98, #queue-req: 0, 
[2025-07-28 01:01:02 DP1 TP0] Decode batch. #running-req: 1, #token: 561, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.16, #queue-req: 0, 
[2025-07-28 01:01:02 DP0 TP0] Decode batch. #running-req: 1, #token: 2048, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.30, #queue-req: 0, 
[2025-07-28 01:01:02 DP1 TP0] Decode batch. #running-req: 1, #token: 601, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.16, #queue-req: 0, 
[2025-07-28 01:01:02 DP0 TP0] Decode batch. #running-req: 1, #token: 2088, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.03, #queue-req: 0, 
[2025-07-28 01:01:02 DP0 TP0] Decode batch. #running-req: 1, #token: 2128, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.84, #queue-req: 0, 
[2025-07-28 01:01:02 DP1 TP0] Decode batch. #running-req: 1, #token: 641, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.29, #queue-req: 0, 
[2025-07-28 01:01:02 DP0 TP0] Decode batch. #running-req: 1, #token: 2168, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.59, #queue-req: 0, 
[2025-07-28 01:01:02 DP1 TP0] Decode batch. #running-req: 1, #token: 681, token usage: 0.00, cuda graph: True, gen throughput (token/s): 264.96, #queue-req: 0, 
[2025-07-28 01:01:02 DP0 TP0] Decode batch. #running-req: 1, #token: 2208, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.55, #queue-req: 0, 
[2025-07-28 01:01:02 DP1 TP0] Decode batch. #running-req: 1, #token: 721, token usage: 0.00, cuda graph: True, gen throughput (token/s): 264.63, #queue-req: 0, 
[2025-07-28 01:01:02 DP0 TP0] Decode batch. #running-req: 1, #token: 2248, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.14, #queue-req: 0, 
[2025-07-28 01:01:02 DP1 TP0] Decode batch. #running-req: 1, #token: 761, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.33, #queue-req: 0, 
[2025-07-28 01:01:02 DP0 TP0] Decode batch. #running-req: 1, #token: 2288, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.99, #queue-req: 0, 
[2025-07-28 01:01:02 DP1 TP0] Decode batch. #running-req: 1, #token: 801, token usage: 0.00, cuda graph: True, gen throughput (token/s): 264.52, #queue-req: 0, 
[2025-07-28 01:01:03 DP0 TP0] Decode batch. #running-req: 1, #token: 2328, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.35, #queue-req: 0, 
[2025-07-28 01:01:03 DP0 TP0] Decode batch. #running-req: 1, #token: 2368, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.37, #queue-req: 0, 
[2025-07-28 01:01:03 DP1 TP0] Decode batch. #running-req: 1, #token: 841, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.22, #queue-req: 0, 
[2025-07-28 01:01:03 DP0 TP0] Decode batch. #running-req: 1, #token: 2408, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.20, #queue-req: 0, 
[2025-07-28 01:01:03 DP1 TP0] Decode batch. #running-req: 1, #token: 881, token usage: 0.00, cuda graph: True, gen throughput (token/s): 281.92, #queue-req: 0, 
[2025-07-28 01:01:03 DP0 TP0] Decode batch. #running-req: 1, #token: 2448, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.32, #queue-req: 0, 
[2025-07-28 01:01:03 DP1 TP0] Decode batch. #running-req: 1, #token: 921, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.21, #queue-req: 0, 
[2025-07-28 01:01:03 DP0 TP0] Decode batch. #running-req: 1, #token: 2488, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.35, #queue-req: 0, 
[2025-07-28 01:01:03 DP1 TP0] Decode batch. #running-req: 1, #token: 961, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.62, #queue-req: 0, 
[2025-07-28 01:01:03] INFO:     127.0.0.1:46408 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:01:03 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 146, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:01:03 DP0 TP0] Decode batch. #running-req: 2, #token: 2687, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.06, #queue-req: 0, 
[2025-07-28 01:01:03 DP0 TP0] Decode batch. #running-req: 2, #token: 2767, token usage: 0.00, cuda graph: True, gen throughput (token/s): 631.17, #queue-req: 0, 
[2025-07-28 01:01:03 DP0 TP0] Decode batch. #running-req: 2, #token: 2847, token usage: 0.00, cuda graph: True, gen throughput (token/s): 629.34, #queue-req: 0, 
[2025-07-28 01:01:04 DP0 TP0] Decode batch. #running-req: 2, #token: 2927, token usage: 0.00, cuda graph: True, gen throughput (token/s): 629.74, #queue-req: 0, 
[2025-07-28 01:01:04 DP0 TP0] Decode batch. #running-req: 2, #token: 3007, token usage: 0.00, cuda graph: True, gen throughput (token/s): 629.24, #queue-req: 0, 
[2025-07-28 01:01:04] INFO:     127.0.0.1:46398 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:01:04 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:01:04 DP0 TP0] Decode batch. #running-req: 1, #token: 444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 433.40, #queue-req: 0, 
[2025-07-28 01:01:04 DP0 TP0] Decode batch. #running-req: 1, #token: 484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 338.09, #queue-req: 0, 
[2025-07-28 01:01:04 DP1 TP0] Decode batch. #running-req: 1, #token: 204, token usage: 0.00, cuda graph: True, gen throughput (token/s): 44.58, #queue-req: 0, 
[2025-07-28 01:01:04 DP0 TP0] Decode batch. #running-req: 1, #token: 524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.57, #queue-req: 0, 
[2025-07-28 01:01:04 DP1 TP0] Decode batch. #running-req: 1, #token: 244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 263.24, #queue-req: 0, 
[2025-07-28 01:01:04 DP0 TP0] Decode batch. #running-req: 1, #token: 564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.11, #queue-req: 0, 
[2025-07-28 01:01:04 DP1 TP0] Decode batch. #running-req: 1, #token: 284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.90, #queue-req: 0, 
[2025-07-28 01:01:04 DP0 TP0] Decode batch. #running-req: 1, #token: 604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.89, #queue-req: 0, 
[2025-07-28 01:01:04 DP1 TP0] Decode batch. #running-req: 1, #token: 324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.52, #queue-req: 0, 
[2025-07-28 01:01:04 DP0 TP0] Decode batch. #running-req: 1, #token: 644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.54, #queue-req: 0, 
[2025-07-28 01:01:05 DP1 TP0] Decode batch. #running-req: 1, #token: 364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.72, #queue-req: 0, 
[2025-07-28 01:01:05 DP0 TP0] Decode batch. #running-req: 1, #token: 684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.28, #queue-req: 0, 
[2025-07-28 01:01:05 DP0 TP0] Decode batch. #running-req: 1, #token: 724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.43, #queue-req: 0, 
[2025-07-28 01:01:05 DP1 TP0] Decode batch. #running-req: 1, #token: 404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.16, #queue-req: 0, 
[2025-07-28 01:01:05 DP0 TP0] Decode batch. #running-req: 1, #token: 764, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.54, #queue-req: 0, 
[2025-07-28 01:01:05 DP1 TP0] Decode batch. #running-req: 1, #token: 444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.40, #queue-req: 0, 
[2025-07-28 01:01:05 DP0 TP0] Decode batch. #running-req: 1, #token: 804, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.35, #queue-req: 0, 
[2025-07-28 01:01:05 DP1 TP0] Decode batch. #running-req: 1, #token: 484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.51, #queue-req: 0, 
[2025-07-28 01:01:05 DP0 TP0] Decode batch. #running-req: 1, #token: 844, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.55, #queue-req: 0, 
[2025-07-28 01:01:05 DP1 TP0] Decode batch. #running-req: 1, #token: 524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.02, #queue-req: 0, 
[2025-07-28 01:01:05 DP0 TP0] Decode batch. #running-req: 1, #token: 884, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.57, #queue-req: 0, 
[2025-07-28 01:01:05 DP1 TP0] Decode batch. #running-req: 1, #token: 564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 271.57, #queue-req: 0, 
[2025-07-28 01:01:05 DP0 TP0] Decode batch. #running-req: 1, #token: 924, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.37, #queue-req: 0, 
[2025-07-28 01:01:05 DP1 TP0] Decode batch. #running-req: 1, #token: 604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 288.04, #queue-req: 0, 
[2025-07-28 01:01:05 DP0 TP0] Decode batch. #running-req: 1, #token: 964, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.18, #queue-req: 0, 
[2025-07-28 01:01:06 DP1 TP0] Decode batch. #running-req: 1, #token: 644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.70, #queue-req: 0, 
[2025-07-28 01:01:06 DP0 TP0] Decode batch. #running-req: 1, #token: 1004, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.61, #queue-req: 0, 
[2025-07-28 01:01:06 DP1 TP0] Decode batch. #running-req: 1, #token: 684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.64, #queue-req: 0, 
[2025-07-28 01:01:06 DP0 TP0] Decode batch. #running-req: 1, #token: 1044, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.59, #queue-req: 0, 
[2025-07-28 01:01:06 DP0 TP0] Decode batch. #running-req: 1, #token: 1084, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.29, #queue-req: 0, 
[2025-07-28 01:01:06 DP1 TP0] Decode batch. #running-req: 1, #token: 724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.78, #queue-req: 0, 
[2025-07-28 01:01:06] INFO:     127.0.0.1:54088 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:01:06 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 173, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:01:06 DP1 TP0] Decode batch. #running-req: 1, #token: 764, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.48, #queue-req: 0, 
[2025-07-28 01:01:06 DP0 TP0] Decode batch. #running-req: 1, #token: 274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 175.04, #queue-req: 0, 
[2025-07-28 01:01:06 DP1 TP0] Decode batch. #running-req: 1, #token: 804, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.27, #queue-req: 0, 
[2025-07-28 01:01:06 DP0 TP0] Decode batch. #running-req: 1, #token: 314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.21, #queue-req: 0, 
[2025-07-28 01:01:06] INFO:     127.0.0.1:54102 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:01:06 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 178, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:01:06 DP1 TP0] Decode batch. #running-req: 1, #token: 268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 195.79, #queue-req: 0, 
[2025-07-28 01:01:06 DP0 TP0] Decode batch. #running-req: 1, #token: 354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 281.72, #queue-req: 0, 
[2025-07-28 01:01:06 DP1 TP0] Decode batch. #running-req: 1, #token: 308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 332.51, #queue-req: 0, 
[2025-07-28 01:01:06 DP0 TP0] Decode batch. #running-req: 1, #token: 394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 275.90, #queue-req: 0, 
[2025-07-28 01:01:07 DP1 TP0] Decode batch. #running-req: 1, #token: 348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 334.11, #queue-req: 0, 
[2025-07-28 01:01:07 DP0 TP0] Decode batch. #running-req: 1, #token: 434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 276.99, #queue-req: 0, 
[2025-07-28 01:01:07 DP1 TP0] Decode batch. #running-req: 1, #token: 388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 332.08, #queue-req: 0, 
[2025-07-28 01:01:07 DP0 TP0] Decode batch. #running-req: 1, #token: 474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 275.73, #queue-req: 0, 
[2025-07-28 01:01:07 DP1 TP0] Decode batch. #running-req: 1, #token: 428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 327.43, #queue-req: 0, 
[2025-07-28 01:01:07 DP0 TP0] Decode batch. #running-req: 1, #token: 514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 276.88, #queue-req: 0, 
[2025-07-28 01:01:07 DP1 TP0] Decode batch. #running-req: 1, #token: 468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.94, #queue-req: 0, 
[2025-07-28 01:01:07 DP1 TP0] Decode batch. #running-req: 1, #token: 508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.83, #queue-req: 0, 
[2025-07-28 01:01:07 DP0 TP0] Decode batch. #running-req: 1, #token: 554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.20, #queue-req: 0, 
[2025-07-28 01:01:07 DP1 TP0] Decode batch. #running-req: 1, #token: 548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.24, #queue-req: 0, 
[2025-07-28 01:01:07 DP0 TP0] Decode batch. #running-req: 1, #token: 594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 275.87, #queue-req: 0, 
[2025-07-28 01:01:07 DP1 TP0] Decode batch. #running-req: 1, #token: 588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.91, #queue-req: 0, 
[2025-07-28 01:01:07 DP0 TP0] Decode batch. #running-req: 1, #token: 634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.05, #queue-req: 0, 
[2025-07-28 01:01:07 DP1 TP0] Decode batch. #running-req: 1, #token: 628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.32, #queue-req: 0, 
[2025-07-28 01:01:07 DP0 TP0] Decode batch. #running-req: 1, #token: 674, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.22, #queue-req: 0, 
[2025-07-28 01:01:08 DP1 TP0] Decode batch. #running-req: 1, #token: 668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.77, #queue-req: 0, 
[2025-07-28 01:01:08 DP0 TP0] Decode batch. #running-req: 1, #token: 714, token usage: 0.00, cuda graph: True, gen throughput (token/s): 274.63, #queue-req: 0, 
[2025-07-28 01:01:08 DP1 TP0] Decode batch. #running-req: 1, #token: 708, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.21, #queue-req: 0, 
[2025-07-28 01:01:08 DP0 TP0] Decode batch. #running-req: 1, #token: 754, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.18, #queue-req: 0, 
[2025-07-28 01:01:08 DP1 TP0] Decode batch. #running-req: 1, #token: 748, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.46, #queue-req: 0, 
[2025-07-28 01:01:08 DP0 TP0] Decode batch. #running-req: 1, #token: 794, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.47, #queue-req: 0, 
[2025-07-28 01:01:08 DP1 TP0] Decode batch. #running-req: 1, #token: 788, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.05, #queue-req: 0, 
[2025-07-28 01:01:08 DP0 TP0] Decode batch. #running-req: 1, #token: 834, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.67, #queue-req: 0, 
[2025-07-28 01:01:08] INFO:     127.0.0.1:54116 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:01:08 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 88, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:01:08 DP0 TP0] Decode batch. #running-req: 2, #token: 996, token usage: 0.00, cuda graph: True, gen throughput (token/s): 392.41, #queue-req: 0, 
[2025-07-28 01:01:08] INFO:     127.0.0.1:54114 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:01:08 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 153, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:01:08 DP0 TP0] Decode batch. #running-req: 1, #token: 247, token usage: 0.00, cuda graph: True, gen throughput (token/s): 560.99, #queue-req: 0, 
[2025-07-28 01:01:09 DP1 TP0] Decode batch. #running-req: 1, #token: 257, token usage: 0.00, cuda graph: True, gen throughput (token/s): 79.73, #queue-req: 0, 
[2025-07-28 01:01:09 DP0 TP0] Decode batch. #running-req: 1, #token: 287, token usage: 0.00, cuda graph: True, gen throughput (token/s): 334.20, #queue-req: 0, 
[2025-07-28 01:01:09 DP0 TP0] Decode batch. #running-req: 1, #token: 327, token usage: 0.00, cuda graph: True, gen throughput (token/s): 334.82, #queue-req: 0, 
[2025-07-28 01:01:09 DP1 TP0] Decode batch. #running-req: 1, #token: 297, token usage: 0.00, cuda graph: True, gen throughput (token/s): 264.48, #queue-req: 0, 
[2025-07-28 01:01:09 DP0 TP0] Decode batch. #running-req: 1, #token: 367, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.03, #queue-req: 0, 
[2025-07-28 01:01:09 DP1 TP0] Decode batch. #running-req: 1, #token: 337, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.00, #queue-req: 0, 
[2025-07-28 01:01:09 DP0 TP0] Decode batch. #running-req: 1, #token: 407, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.16, #queue-req: 0, 
[2025-07-28 01:01:09 DP1 TP0] Decode batch. #running-req: 1, #token: 377, token usage: 0.00, cuda graph: True, gen throughput (token/s): 264.99, #queue-req: 0, 
[2025-07-28 01:01:09 DP0 TP0] Decode batch. #running-req: 1, #token: 447, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.15, #queue-req: 0, 
[2025-07-28 01:01:09 DP1 TP0] Decode batch. #running-req: 1, #token: 417, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.21, #queue-req: 0, 
[2025-07-28 01:01:09 DP0 TP0] Decode batch. #running-req: 1, #token: 487, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.05, #queue-req: 0, 
[2025-07-28 01:01:09 DP1 TP0] Decode batch. #running-req: 1, #token: 457, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.05, #queue-req: 0, 
[2025-07-28 01:01:09 DP0 TP0] Decode batch. #running-req: 1, #token: 527, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.65, #queue-req: 0, 
[2025-07-28 01:01:09 DP1 TP0] Decode batch. #running-req: 1, #token: 497, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.30, #queue-req: 0, 
[2025-07-28 01:01:09 DP0 TP0] Decode batch. #running-req: 1, #token: 567, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.03, #queue-req: 0, 
[2025-07-28 01:01:10 DP0 TP0] Decode batch. #running-req: 1, #token: 607, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.34, #queue-req: 0, 
[2025-07-28 01:01:10 DP1 TP0] Decode batch. #running-req: 1, #token: 537, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.16, #queue-req: 0, 
[2025-07-28 01:01:10 DP0 TP0] Decode batch. #running-req: 1, #token: 647, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.74, #queue-req: 0, 
[2025-07-28 01:01:10 DP1 TP0] Decode batch. #running-req: 1, #token: 577, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.40, #queue-req: 0, 
[2025-07-28 01:01:10 DP0 TP0] Decode batch. #running-req: 1, #token: 687, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.41, #queue-req: 0, 
[2025-07-28 01:01:10 DP1 TP0] Decode batch. #running-req: 1, #token: 617, token usage: 0.00, cuda graph: True, gen throughput (token/s): 264.63, #queue-req: 0, 
[2025-07-28 01:01:10 DP0 TP0] Decode batch. #running-req: 1, #token: 727, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.06, #queue-req: 0, 
[2025-07-28 01:01:10 DP1 TP0] Decode batch. #running-req: 1, #token: 657, token usage: 0.00, cuda graph: True, gen throughput (token/s): 264.95, #queue-req: 0, 
[2025-07-28 01:01:10 DP0 TP0] Decode batch. #running-req: 1, #token: 767, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.01, #queue-req: 0, 
[2025-07-28 01:01:10 DP1 TP0] Decode batch. #running-req: 1, #token: 697, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.79, #queue-req: 0, 
[2025-07-28 01:01:10 DP0 TP0] Decode batch. #running-req: 1, #token: 807, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.52, #queue-req: 0, 
[2025-07-28 01:01:10] INFO:     127.0.0.1:54130 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:01:10 DP1 TP0] Decode batch. #running-req: 1, #token: 737, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.02, #queue-req: 0, 
[2025-07-28 01:01:10 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 123, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:01:10 DP0 TP0] Decode batch. #running-req: 1, #token: 218, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.36, #queue-req: 0, 
[2025-07-28 01:01:10 DP1 TP0] Decode batch. #running-req: 1, #token: 777, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.29, #queue-req: 0, 
[2025-07-28 01:01:11 DP0 TP0] Decode batch. #running-req: 1, #token: 258, token usage: 0.00, cuda graph: True, gen throughput (token/s): 336.07, #queue-req: 0, 
[2025-07-28 01:01:11 DP1 TP0] Decode batch. #running-req: 1, #token: 817, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.33, #queue-req: 0, 
[2025-07-28 01:01:11 DP0 TP0] Decode batch. #running-req: 1, #token: 298, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.31, #queue-req: 0, 
[2025-07-28 01:01:11] INFO:     127.0.0.1:54146 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:01:11 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 129, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:01:11 DP0 TP0] Decode batch. #running-req: 1, #token: 338, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.11, #queue-req: 0, 
[2025-07-28 01:01:11 DP1 TP0] Decode batch. #running-req: 1, #token: 226, token usage: 0.00, cuda graph: True, gen throughput (token/s): 182.06, #queue-req: 0, 
[2025-07-28 01:01:11 DP0 TP0] Decode batch. #running-req: 1, #token: 378, token usage: 0.00, cuda graph: True, gen throughput (token/s): 282.80, #queue-req: 0, 
[2025-07-28 01:01:11 DP1 TP0] Decode batch. #running-req: 1, #token: 266, token usage: 0.00, cuda graph: True, gen throughput (token/s): 336.02, #queue-req: 0, 
[2025-07-28 01:01:11 DP0 TP0] Decode batch. #running-req: 1, #token: 418, token usage: 0.00, cuda graph: True, gen throughput (token/s): 279.47, #queue-req: 0, 
[2025-07-28 01:01:11 DP1 TP0] Decode batch. #running-req: 1, #token: 306, token usage: 0.00, cuda graph: True, gen throughput (token/s): 341.42, #queue-req: 0, 
[2025-07-28 01:01:11 DP1 TP0] Decode batch. #running-req: 1, #token: 346, token usage: 0.00, cuda graph: True, gen throughput (token/s): 341.08, #queue-req: 0, 
[2025-07-28 01:01:11 DP0 TP0] Decode batch. #running-req: 1, #token: 458, token usage: 0.00, cuda graph: True, gen throughput (token/s): 324.85, #queue-req: 0, 
[2025-07-28 01:01:11 DP1 TP0] Decode batch. #running-req: 1, #token: 386, token usage: 0.00, cuda graph: True, gen throughput (token/s): 342.52, #queue-req: 0, 
[2025-07-28 01:01:11 DP0 TP0] Decode batch. #running-req: 1, #token: 498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 328.69, #queue-req: 0, 
[2025-07-28 01:01:11 DP1 TP0] Decode batch. #running-req: 1, #token: 426, token usage: 0.00, cuda graph: True, gen throughput (token/s): 343.33, #queue-req: 0, 
[2025-07-28 01:01:11 DP0 TP0] Decode batch. #running-req: 1, #token: 538, token usage: 0.00, cuda graph: True, gen throughput (token/s): 326.91, #queue-req: 0, 
[2025-07-28 01:01:12 DP1 TP0] Decode batch. #running-req: 1, #token: 466, token usage: 0.00, cuda graph: True, gen throughput (token/s): 326.27, #queue-req: 0, 
[2025-07-28 01:01:12 DP0 TP0] Decode batch. #running-req: 1, #token: 578, token usage: 0.00, cuda graph: True, gen throughput (token/s): 321.14, #queue-req: 0, 
[2025-07-28 01:01:12 DP1 TP0] Decode batch. #running-req: 1, #token: 506, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.39, #queue-req: 0, 
[2025-07-28 01:01:12 DP0 TP0] Decode batch. #running-req: 1, #token: 618, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.19, #queue-req: 0, 
[2025-07-28 01:01:12 DP1 TP0] Decode batch. #running-req: 1, #token: 546, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.12, #queue-req: 0, 
[2025-07-28 01:01:12 DP0 TP0] Decode batch. #running-req: 1, #token: 658, token usage: 0.00, cuda graph: True, gen throughput (token/s): 280.86, #queue-req: 0, 
[2025-07-28 01:01:12 DP1 TP0] Decode batch. #running-req: 1, #token: 586, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.84, #queue-req: 0, 
[2025-07-28 01:01:12 DP0 TP0] Decode batch. #running-req: 1, #token: 698, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.35, #queue-req: 0, 
[2025-07-28 01:01:12 DP1 TP0] Decode batch. #running-req: 1, #token: 626, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.19, #queue-req: 0, 
[2025-07-28 01:01:12 DP0 TP0] Decode batch. #running-req: 1, #token: 738, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.10, #queue-req: 0, 
[2025-07-28 01:01:12 DP1 TP0] Decode batch. #running-req: 1, #token: 666, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.43, #queue-req: 0, 
[2025-07-28 01:01:12 DP0 TP0] Decode batch. #running-req: 1, #token: 778, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.00, #queue-req: 0, 
[2025-07-28 01:01:12 DP1 TP0] Decode batch. #running-req: 1, #token: 706, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.62, #queue-req: 0, 
[2025-07-28 01:01:12 DP1 TP0] Decode batch. #running-req: 1, #token: 746, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.34, #queue-req: 0, 
[2025-07-28 01:01:12 DP0 TP0] Decode batch. #running-req: 1, #token: 818, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.32, #queue-req: 0, 
[2025-07-28 01:01:13 DP0 TP0] Decode batch. #running-req: 1, #token: 858, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.40, #queue-req: 0, 
[2025-07-28 01:01:13 DP1 TP0] Decode batch. #running-req: 1, #token: 786, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.15, #queue-req: 0, 
[2025-07-28 01:01:13 DP0 TP0] Decode batch. #running-req: 1, #token: 898, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.06, #queue-req: 0, 
[2025-07-28 01:01:13 DP1 TP0] Decode batch. #running-req: 1, #token: 826, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.11, #queue-req: 0, 
[2025-07-28 01:01:13 DP0 TP0] Decode batch. #running-req: 1, #token: 938, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.59, #queue-req: 0, 
[2025-07-28 01:01:13 DP1 TP0] Decode batch. #running-req: 1, #token: 866, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.06, #queue-req: 0, 
[2025-07-28 01:01:13 DP0 TP0] Decode batch. #running-req: 1, #token: 978, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.06, #queue-req: 0, 
[2025-07-28 01:01:13 DP1 TP0] Decode batch. #running-req: 1, #token: 906, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.94, #queue-req: 0, 
[2025-07-28 01:01:13] INFO:     127.0.0.1:54162 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:01:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1018, token usage: 0.00, cuda graph: True, gen throughput (token/s): 281.41, #queue-req: 0, 
[2025-07-28 01:01:13 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 91, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:01:13 DP0 TP0] Decode batch. #running-req: 2, #token: 1229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 414.88, #queue-req: 0, 
[2025-07-28 01:01:13 DP0 TP0] Decode batch. #running-req: 2, #token: 1309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 626.35, #queue-req: 0, 
[2025-07-28 01:01:14 DP0 TP0] Decode batch. #running-req: 2, #token: 339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 605.23, #queue-req: 0, 
[2025-07-28 01:01:14] INFO:     127.0.0.1:54156 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:01:14 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 92, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:01:14 DP1 TP0] Decode batch. #running-req: 1, #token: 246, token usage: 0.00, cuda graph: True, gen throughput (token/s): 57.52, #queue-req: 0, 
[2025-07-28 01:01:14 DP0 TP0] Decode batch. #running-req: 1, #token: 378, token usage: 0.00, cuda graph: True, gen throughput (token/s): 325.80, #queue-req: 0, 
[2025-07-28 01:01:14 DP0 TP0] Decode batch. #running-req: 1, #token: 418, token usage: 0.00, cuda graph: True, gen throughput (token/s): 322.32, #queue-req: 0, 
[2025-07-28 01:01:14 DP1 TP0] Decode batch. #running-req: 1, #token: 286, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.55, #queue-req: 0, 
[2025-07-28 01:01:14 DP0 TP0] Decode batch. #running-req: 1, #token: 458, token usage: 0.00, cuda graph: True, gen throughput (token/s): 322.62, #queue-req: 0, 
[2025-07-28 01:01:14 DP1 TP0] Decode batch. #running-req: 1, #token: 326, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.51, #queue-req: 0, 
[2025-07-28 01:01:14 DP0 TP0] Decode batch. #running-req: 1, #token: 498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 321.38, #queue-req: 0, 
[2025-07-28 01:01:14 DP1 TP0] Decode batch. #running-req: 1, #token: 366, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.17, #queue-req: 0, 
[2025-07-28 01:01:14 DP0 TP0] Decode batch. #running-req: 1, #token: 538, token usage: 0.00, cuda graph: True, gen throughput (token/s): 321.52, #queue-req: 0, 
[2025-07-28 01:01:14 DP1 TP0] Decode batch. #running-req: 1, #token: 406, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.41, #queue-req: 0, 
[2025-07-28 01:01:14 DP0 TP0] Decode batch. #running-req: 1, #token: 578, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.54, #queue-req: 0, 
[2025-07-28 01:01:14 DP1 TP0] Decode batch. #running-req: 1, #token: 446, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.26, #queue-req: 0, 
[2025-07-28 01:01:14 DP0 TP0] Decode batch. #running-req: 1, #token: 618, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.25, #queue-req: 0, 
[2025-07-28 01:01:15 DP0 TP0] Decode batch. #running-req: 1, #token: 658, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.62, #queue-req: 0, 
[2025-07-28 01:01:15 DP1 TP0] Decode batch. #running-req: 1, #token: 486, token usage: 0.00, cuda graph: True, gen throughput (token/s): 264.77, #queue-req: 0, 
[2025-07-28 01:01:15 DP0 TP0] Decode batch. #running-req: 1, #token: 698, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.86, #queue-req: 0, 
[2025-07-28 01:01:15 DP1 TP0] Decode batch. #running-req: 1, #token: 526, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.64, #queue-req: 0, 
[2025-07-28 01:01:15 DP0 TP0] Decode batch. #running-req: 1, #token: 738, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.43, #queue-req: 0, 
[2025-07-28 01:01:15 DP1 TP0] Decode batch. #running-req: 1, #token: 566, token usage: 0.00, cuda graph: True, gen throughput (token/s): 264.44, #queue-req: 0, 
[2025-07-28 01:01:15 DP0 TP0] Decode batch. #running-req: 1, #token: 778, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.98, #queue-req: 0, 
[2025-07-28 01:01:15 DP1 TP0] Decode batch. #running-req: 1, #token: 606, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.12, #queue-req: 0, 
[2025-07-28 01:01:15 DP0 TP0] Decode batch. #running-req: 1, #token: 818, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.59, #queue-req: 0, 
[2025-07-28 01:01:15] INFO:     127.0.0.1:36654 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:01:15 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 103, #cached-token: 91, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:01:15 DP1 TP0] Decode batch. #running-req: 1, #token: 646, token usage: 0.00, cuda graph: True, gen throughput (token/s): 264.75, #queue-req: 0, 
[2025-07-28 01:01:15 DP0 TP0] Decode batch. #running-req: 1, #token: 213, token usage: 0.00, cuda graph: True, gen throughput (token/s): 203.73, #queue-req: 0, 
[2025-07-28 01:01:15 DP1 TP0] Decode batch. #running-req: 1, #token: 686, token usage: 0.00, cuda graph: True, gen throughput (token/s): 264.62, #queue-req: 0, 
[2025-07-28 01:01:15 DP0 TP0] Decode batch. #running-req: 1, #token: 253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 334.31, #queue-req: 0, 
[2025-07-28 01:01:15 DP1 TP0] Decode batch. #running-req: 1, #token: 726, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.11, #queue-req: 0, 
[2025-07-28 01:01:16 DP0 TP0] Decode batch. #running-req: 1, #token: 293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 323.87, #queue-req: 0, 
[2025-07-28 01:01:16 DP0 TP0] Decode batch. #running-req: 1, #token: 333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 321.00, #queue-req: 0, 
[2025-07-28 01:01:16 DP1 TP0] Decode batch. #running-req: 1, #token: 766, token usage: 0.00, cuda graph: True, gen throughput (token/s): 259.60, #queue-req: 0, 
[2025-07-28 01:01:16 DP0 TP0] Decode batch. #running-req: 1, #token: 373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.37, #queue-req: 0, 
[2025-07-28 01:01:16 DP1 TP0] Decode batch. #running-req: 1, #token: 806, token usage: 0.00, cuda graph: True, gen throughput (token/s): 263.95, #queue-req: 0, 
[2025-07-28 01:01:16 DP0 TP0] Decode batch. #running-req: 1, #token: 413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 323.30, #queue-req: 0, 
[2025-07-28 01:01:16 DP1 TP0] Decode batch. #running-req: 1, #token: 846, token usage: 0.00, cuda graph: True, gen throughput (token/s): 264.46, #queue-req: 0, 
[2025-07-28 01:01:16 DP0 TP0] Decode batch. #running-req: 1, #token: 453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 321.87, #queue-req: 0, 
[2025-07-28 01:01:16 DP1 TP0] Decode batch. #running-req: 1, #token: 886, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.38, #queue-req: 0, 
[2025-07-28 01:01:16 DP0 TP0] Decode batch. #running-req: 1, #token: 493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.24, #queue-req: 0, 
[2025-07-28 01:01:16 DP1 TP0] Decode batch. #running-req: 1, #token: 926, token usage: 0.00, cuda graph: True, gen throughput (token/s): 264.83, #queue-req: 0, 
[2025-07-28 01:01:16 DP0 TP0] Decode batch. #running-req: 1, #token: 533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.54, #queue-req: 0, 
[2025-07-28 01:01:16 DP0 TP0] Decode batch. #running-req: 1, #token: 573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.55, #queue-req: 0, 
[2025-07-28 01:01:16 DP1 TP0] Decode batch. #running-req: 1, #token: 966, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.29, #queue-req: 0, 
[2025-07-28 01:01:17 DP0 TP0] Decode batch. #running-req: 1, #token: 613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.47, #queue-req: 0, 
[2025-07-28 01:01:17 DP1 TP0] Decode batch. #running-req: 1, #token: 1006, token usage: 0.00, cuda graph: True, gen throughput (token/s): 264.29, #queue-req: 0, 
[2025-07-28 01:01:17 DP0 TP0] Decode batch. #running-req: 1, #token: 653, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.78, #queue-req: 0, 
[2025-07-28 01:01:17 DP1 TP0] Decode batch. #running-req: 1, #token: 1046, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.32, #queue-req: 0, 
[2025-07-28 01:01:17 DP0 TP0] Decode batch. #running-req: 1, #token: 693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.81, #queue-req: 0, 
[2025-07-28 01:01:17 DP1 TP0] Decode batch. #running-req: 1, #token: 1086, token usage: 0.00, cuda graph: True, gen throughput (token/s): 264.44, #queue-req: 0, 
[2025-07-28 01:01:17 DP0 TP0] Decode batch. #running-req: 1, #token: 733, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.50, #queue-req: 0, 
[2025-07-28 01:01:17 DP1 TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 263.55, #queue-req: 0, 
[2025-07-28 01:01:17] INFO:     127.0.0.1:36656 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:01:17 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 89, #cached-token: 91, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:01:17 DP0 TP0] Decode batch. #running-req: 1, #token: 773, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.14, #queue-req: 0, 
[2025-07-28 01:01:17 DP0 TP0] Decode batch. #running-req: 1, #token: 813, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.20, #queue-req: 0, 
[2025-07-28 01:01:17 DP1 TP0] Decode batch. #running-req: 1, #token: 220, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.16, #queue-req: 0, 
[2025-07-28 01:01:17 DP0 TP0] Decode batch. #running-req: 1, #token: 853, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.55, #queue-req: 0, 
[2025-07-28 01:01:17 DP1 TP0] Decode batch. #running-req: 1, #token: 260, token usage: 0.00, cuda graph: True, gen throughput (token/s): 344.97, #queue-req: 0, 
[2025-07-28 01:01:17 DP0 TP0] Decode batch. #running-req: 1, #token: 893, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.70, #queue-req: 0, 
[2025-07-28 01:01:17 DP1 TP0] Decode batch. #running-req: 1, #token: 300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 331.45, #queue-req: 0, 
[2025-07-28 01:01:18 DP0 TP0] Decode batch. #running-req: 1, #token: 933, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.20, #queue-req: 0, 
[2025-07-28 01:01:18 DP1 TP0] Decode batch. #running-req: 1, #token: 340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.94, #queue-req: 0, 
[2025-07-28 01:01:18 DP0 TP0] Decode batch. #running-req: 1, #token: 973, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.85, #queue-req: 0, 
[2025-07-28 01:01:18 DP1 TP0] Decode batch. #running-req: 1, #token: 380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.13, #queue-req: 0, 
[2025-07-28 01:01:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1013, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.50, #queue-req: 0, 
[2025-07-28 01:01:18 DP1 TP0] Decode batch. #running-req: 1, #token: 420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.16, #queue-req: 0, 
[2025-07-28 01:01:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1053, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.85, #queue-req: 0, 
[2025-07-28 01:01:18 DP1 TP0] Decode batch. #running-req: 1, #token: 460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.24, #queue-req: 0, 
[2025-07-28 01:01:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1093, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.96, #queue-req: 0, 
[2025-07-28 01:01:18 DP1 TP0] Decode batch. #running-req: 1, #token: 500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.05, #queue-req: 0, 
[2025-07-28 01:01:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1133, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.46, #queue-req: 0, 
[2025-07-28 01:01:18 DP1 TP0] Decode batch. #running-req: 1, #token: 540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.78, #queue-req: 0, 
[2025-07-28 01:01:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1173, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.71, #queue-req: 0, 
[2025-07-28 01:01:18 DP1 TP0] Decode batch. #running-req: 1, #token: 580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.93, #queue-req: 0, 
[2025-07-28 01:01:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1213, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.18, #queue-req: 0, 
[2025-07-28 01:01:18 DP1 TP0] Decode batch. #running-req: 1, #token: 620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.69, #queue-req: 0, 
[2025-07-28 01:01:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.22, #queue-req: 0, 
[2025-07-28 01:01:19 DP1 TP0] Decode batch. #running-req: 1, #token: 660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.68, #queue-req: 0, 
[2025-07-28 01:01:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.03, #queue-req: 0, 
[2025-07-28 01:01:19 DP1 TP0] Decode batch. #running-req: 1, #token: 700, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.81, #queue-req: 0, 
[2025-07-28 01:01:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.85, #queue-req: 0, 
[2025-07-28 01:01:19 DP1 TP0] Decode batch. #running-req: 1, #token: 740, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.47, #queue-req: 0, 
[2025-07-28 01:01:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.84, #queue-req: 0, 
[2025-07-28 01:01:19 DP1 TP0] Decode batch. #running-req: 1, #token: 780, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.60, #queue-req: 0, 
[2025-07-28 01:01:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.40, #queue-req: 0, 
[2025-07-28 01:01:19 DP1 TP0] Decode batch. #running-req: 1, #token: 820, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.86, #queue-req: 0, 
[2025-07-28 01:01:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.88, #queue-req: 0, 
[2025-07-28 01:01:19 DP1 TP0] Decode batch. #running-req: 1, #token: 860, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.06, #queue-req: 0, 
[2025-07-28 01:01:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.76, #queue-req: 0, 
[2025-07-28 01:01:19 DP1 TP0] Decode batch. #running-req: 1, #token: 900, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.00, #queue-req: 0, 
[2025-07-28 01:01:19] INFO:     127.0.0.1:36680 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:01:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.44, #queue-req: 0, 
[2025-07-28 01:01:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.23, #queue-req: 0, 
[2025-07-28 01:01:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.16, #queue-req: 0, 
[2025-07-28 01:01:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1653, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.42, #queue-req: 0, 
[2025-07-28 01:01:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.70, #queue-req: 0, 
[2025-07-28 01:01:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1733, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.20, #queue-req: 0, 
[2025-07-28 01:01:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1773, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.36, #queue-req: 0, 
[2025-07-28 01:01:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1813, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.87, #queue-req: 0, 
[2025-07-28 01:01:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1853, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.15, #queue-req: 0, 
[2025-07-28 01:01:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1893, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.02, #queue-req: 0, 
[2025-07-28 01:01:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1933, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.76, #queue-req: 0, 
[2025-07-28 01:01:21] INFO:     127.0.0.1:36664 - "POST /v1/chat/completions HTTP/1.1" 200 OK

[2025-07-28 00:55:23] server_args=ServerArgs(model_path='/data/home/<USER>/repos/anesbench/share/models/Qwen3-0.6B', tokenizer_path='/data/home/<USER>/repos/anesbench/share/models/Qwen3-0.6B', tokenizer_mode='auto', skip_tokenizer_init=False, load_format='auto', model_loader_extra_config='{}', trust_remote_code=False, context_length=32768, is_embedding=False, enable_multimodal=None, revision=None, model_impl='auto', host='127.0.0.1', port=8003, skip_server_warmup=False, warmups=None, nccl_port=None, dtype='auto', quantization=None, quantization_param_path=None, kv_cache_dtype='auto', mem_fraction_static=0.85, max_running_requests=None, max_total_tokens=None, chunked_prefill_size=8192, max_prefill_tokens=16384, schedule_policy='fcfs', schedule_conservativeness=1.0, cpu_offload_gb=0, page_size=1, hybrid_kvcache_ratio=None, swa_full_tokens_ratio=0.8, disable_hybrid_swa_memory=False, device='cuda', tp_size=4, pp_size=1, max_micro_batch_size=None, stream_interval=1, stream_output=False, random_seed=338621645, constrained_json_whitespace_pattern=None, watchdog_timeout=300, dist_timeout=None, download_dir=None, base_gpu_id=0, gpu_id_step=1, sleep_on_idle=False, log_level='info', log_level_http=None, log_requests=False, log_requests_level=0, crash_dump_folder=None, show_time_cost=True, enable_metrics=True, enable_metrics_for_all_schedulers=False, bucket_time_to_first_token=None, bucket_inter_token_latency=None, bucket_e2e_request_latency=None, collect_tokens_histogram=False, decode_log_interval=40, enable_request_time_stats_logging=False, kv_events_config=None, api_key=None, served_model_name='/data/home/<USER>/repos/anesbench/share/models/Qwen3-0.6B', chat_template=None, completion_template=None, file_storage_path='sglang_storage', enable_cache_report=False, reasoning_parser=None, tool_call_parser=None, dp_size=2, load_balance_method='round_robin', dist_init_addr=None, nnodes=1, node_rank=0, json_model_override_args='{}', preferred_sampling_params=None, enable_lora=None, max_lora_rank=None, lora_target_modules=None, lora_paths=None, max_loras_per_batch=8, lora_backend='triton', attention_backend=None, sampling_backend='flashinfer', grammar_backend='xgrammar', mm_attention_backend=None, speculative_algorithm=None, speculative_draft_model_path=None, speculative_num_steps=None, speculative_eagle_topk=None, speculative_num_draft_tokens=None, speculative_accept_threshold_single=1.0, speculative_accept_threshold_acc=1.0, speculative_token_map=None, ep_size=1, enable_ep_moe=False, enable_deepep_moe=False, enable_flashinfer_moe=False, enable_flashinfer_allreduce_fusion=False, deepep_mode='auto', ep_num_redundant_experts=0, ep_dispatch_algorithm='static', init_expert_location='trivial', enable_eplb=False, eplb_algorithm='auto', eplb_rebalance_num_iterations=1000, eplb_rebalance_layers_per_chunk=None, expert_distribution_recorder_mode=None, expert_distribution_recorder_buffer_size=1000, enable_expert_distribution_metrics=False, deepep_config=None, moe_dense_tp_size=None, enable_hierarchical_cache=False, hicache_ratio=2.0, hicache_size=0, hicache_write_policy='write_through_selective', hicache_io_backend='', hicache_storage_backend=None, enable_double_sparsity=False, ds_channel_config_path=None, ds_heavy_channel_num=32, ds_heavy_token_num=256, ds_heavy_channel_type='qk', ds_sparse_decode_threshold=4096, disable_radix_cache=False, cuda_graph_max_bs=None, cuda_graph_bs=None, disable_cuda_graph=False, disable_cuda_graph_padding=False, enable_profile_cuda_graph=False, enable_nccl_nvls=False, enable_tokenizer_batch_encode=False, disable_outlines_disk_cache=False, disable_custom_all_reduce=False, enable_mscclpp=False, disable_overlap_schedule=False, enable_mixed_chunk=False, enable_dp_attention=False, enable_dp_lm_head=False, enable_two_batch_overlap=False, enable_torch_compile=False, torch_compile_max_bs=32, torchao_config='', enable_nan_detection=False, enable_p2p_check=False, triton_attention_reduce_in_fp32=False, triton_attention_num_kv_splits=8, num_continuous_decode_steps=1, delete_ckpt_after_loading=False, enable_memory_saver=False, allow_auto_truncate=False, enable_custom_logit_processor=False, flashinfer_mla_disable_ragged=False, disable_shared_experts_fusion=False, disable_chunked_prefix_cache=False, disable_fast_image_processor=False, enable_return_hidden_states=False, enable_triton_kernel_moe=False, debug_tensor_dump_output_folder=None, debug_tensor_dump_input_file=None, debug_tensor_dump_inject=False, debug_tensor_dump_prefill_only=False, disaggregation_mode='null', disaggregation_transfer_backend='mooncake', disaggregation_bootstrap_port=8998, disaggregation_decode_tp=None, disaggregation_decode_dp=None, disaggregation_prefill_pp=1, disaggregation_ib_device=None, num_reserved_decode_tokens=512, pdlb_url=None, custom_weight_loader=[], weight_loader_disable_mmap=False, enable_pdmux=False, sm_group_num=3)
[2025-07-28 00:55:29] Launch DP0 starting at GPU #0.
[2025-07-28 00:55:29] Launch DP1 starting at GPU #4.
[2025-07-28 00:55:37 DP0 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 00:55:37 DP0 TP0] Init torch distributed begin.
[2025-07-28 00:55:37 DP1 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 00:55:37 DP1 TP0] Init torch distributed begin.
[2025-07-28 00:55:38 DP1 TP0] sglang is using nccl==2.26.2
[2025-07-28 00:55:38 DP0 TP0] sglang is using nccl==2.26.2
[2025-07-28 00:55:39 DP1 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 00:55:39 DP0 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 00:55:39 DP1 TP0] Load weight begin. avail mem=78.21 GB

Loading safetensors checkpoint shards:   0% Completed | 0/1 [00:00<?, ?it/s]
[2025-07-28 00:55:40 DP0 TP0] Load weight begin. avail mem=78.21 GB

Loading safetensors checkpoint shards:   0% Completed | 0/1 [00:00<?, ?it/s]

Loading safetensors checkpoint shards: 100% Completed | 1/1 [00:00<00:00,  9.51it/s]

Loading safetensors checkpoint shards: 100% Completed | 1/1 [00:00<00:00,  9.49it/s]

[2025-07-28 00:55:40 DP1 TP0] Load weight end. type=Qwen3ForCausalLM, dtype=torch.bfloat16, avail mem=77.91 GB, mem usage=0.30 GB.
[2025-07-28 00:55:40 DP1 TP3] KV Cache is allocated. #tokens: 2475324, K size: 33.05 GB, V size: 33.05 GB
[2025-07-28 00:55:40 DP1 TP1] KV Cache is allocated. #tokens: 2475324, K size: 33.05 GB, V size: 33.05 GB
[2025-07-28 00:55:40 DP1 TP2] KV Cache is allocated. #tokens: 2475324, K size: 33.05 GB, V size: 33.05 GB
[2025-07-28 00:55:40 DP1 TP0] KV Cache is allocated. #tokens: 2475324, K size: 33.05 GB, V size: 33.05 GB
[2025-07-28 00:55:40 DP1 TP0] Memory pool end. avail mem=11.22 GB

Loading safetensors checkpoint shards: 100% Completed | 1/1 [00:00<00:00, 10.12it/s]

[2025-07-28 00:55:40 DP0 TP0] Load weight end. type=Qwen3ForCausalLM, dtype=torch.bfloat16, avail mem=77.91 GB, mem usage=0.30 GB.
[2025-07-28 00:55:40 DP0 TP0] KV Cache is allocated. #tokens: 2475324, K size: 33.05 GB, V size: 33.05 GB
[2025-07-28 00:55:40 DP0 TP0] Memory pool end. avail mem=11.22 GB
[2025-07-28 00:55:40 DP0 TP2] KV Cache is allocated. #tokens: 2475324, K size: 33.05 GB, V size: 33.05 GB
[2025-07-28 00:55:40 DP0 TP3] KV Cache is allocated. #tokens: 2475324, K size: 33.05 GB, V size: 33.05 GB
[2025-07-28 00:55:40 DP0 TP1] KV Cache is allocated. #tokens: 2475324, K size: 33.05 GB, V size: 33.05 GB
[2025-07-28 00:55:40 DP1 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.58 GB
[2025-07-28 00:55:40 DP0 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.58 GB
[2025-07-28 00:55:40 DP1 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.56 GB):   0%|          | 0/23 [00:00<?, ?it/s][2025-07-28 00:55:40 DP0 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.56 GB):   0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.56 GB):   4%|▍         | 1/23 [00:01<00:22,  1.01s/it]
Capturing batches (bs=152 avail_mem=10.33 GB):   4%|▍         | 1/23 [00:01<00:22,  1.01s/it]
Capturing batches (bs=160 avail_mem=10.56 GB):   4%|▍         | 1/23 [00:01<00:25,  1.16s/it]
Capturing batches (bs=152 avail_mem=10.33 GB):   4%|▍         | 1/23 [00:01<00:25,  1.16s/it]
Capturing batches (bs=152 avail_mem=10.33 GB):   9%|▊         | 2/23 [00:01<00:12,  1.63it/s]
Capturing batches (bs=144 avail_mem=10.21 GB):   9%|▊         | 2/23 [00:01<00:12,  1.63it/s]
Capturing batches (bs=152 avail_mem=10.33 GB):   9%|▊         | 2/23 [00:01<00:14,  1.43it/s]
Capturing batches (bs=144 avail_mem=10.21 GB):   9%|▊         | 2/23 [00:01<00:14,  1.43it/s]
Capturing batches (bs=144 avail_mem=10.21 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.74it/s]
Capturing batches (bs=136 avail_mem=10.13 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.74it/s]
Capturing batches (bs=144 avail_mem=10.21 GB):  13%|█▎        | 3/23 [00:02<00:12,  1.59it/s]
Capturing batches (bs=136 avail_mem=10.13 GB):  13%|█▎        | 3/23 [00:02<00:12,  1.59it/s]
Capturing batches (bs=136 avail_mem=10.13 GB):  17%|█▋        | 4/23 [00:02<00:08,  2.18it/s]
Capturing batches (bs=128 avail_mem=10.02 GB):  17%|█▋        | 4/23 [00:02<00:08,  2.18it/s]
Capturing batches (bs=136 avail_mem=10.13 GB):  17%|█▋        | 4/23 [00:02<00:09,  1.99it/s]
Capturing batches (bs=128 avail_mem=10.02 GB):  17%|█▋        | 4/23 [00:02<00:09,  1.99it/s]
Capturing batches (bs=128 avail_mem=10.02 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.53it/s]
Capturing batches (bs=120 avail_mem=9.94 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.53it/s] 
Capturing batches (bs=128 avail_mem=10.02 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.33it/s]
Capturing batches (bs=120 avail_mem=9.94 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.33it/s] 
Capturing batches (bs=120 avail_mem=9.94 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.81it/s]
Capturing batches (bs=112 avail_mem=9.85 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.81it/s]
Capturing batches (bs=120 avail_mem=9.94 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.60it/s]
Capturing batches (bs=112 avail_mem=9.85 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.60it/s]
Capturing batches (bs=112 avail_mem=9.85 GB):  30%|███       | 7/23 [00:03<00:05,  3.02it/s]
Capturing batches (bs=104 avail_mem=9.78 GB):  30%|███       | 7/23 [00:03<00:05,  3.02it/s]
Capturing batches (bs=112 avail_mem=9.85 GB):  30%|███       | 7/23 [00:03<00:05,  2.80it/s]
Capturing batches (bs=104 avail_mem=9.78 GB):  30%|███       | 7/23 [00:03<00:05,  2.80it/s]
Capturing batches (bs=104 avail_mem=9.78 GB):  35%|███▍      | 8/23 [00:03<00:04,  3.17it/s]
Capturing batches (bs=96 avail_mem=9.69 GB):  35%|███▍      | 8/23 [00:03<00:04,  3.17it/s] 
Capturing batches (bs=104 avail_mem=9.78 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.95it/s]
Capturing batches (bs=96 avail_mem=9.69 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.95it/s] 
Capturing batches (bs=96 avail_mem=9.69 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.28it/s]
Capturing batches (bs=88 avail_mem=9.63 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.28it/s]
Capturing batches (bs=96 avail_mem=9.69 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.06it/s]
Capturing batches (bs=88 avail_mem=9.63 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.06it/s]
Capturing batches (bs=88 avail_mem=9.63 GB):  43%|████▎     | 10/23 [00:03<00:03,  3.35it/s]
Capturing batches (bs=80 avail_mem=9.56 GB):  43%|████▎     | 10/23 [00:03<00:03,  3.35it/s]
Capturing batches (bs=88 avail_mem=9.63 GB):  43%|████▎     | 10/23 [00:04<00:04,  3.14it/s]
Capturing batches (bs=80 avail_mem=9.56 GB):  43%|████▎     | 10/23 [00:04<00:04,  3.14it/s]
Capturing batches (bs=80 avail_mem=9.56 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.40it/s]
Capturing batches (bs=72 avail_mem=9.55 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.40it/s]
Capturing batches (bs=72 avail_mem=9.55 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.39it/s]
Capturing batches (bs=80 avail_mem=9.56 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.17it/s]
Capturing batches (bs=64 avail_mem=9.49 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.39it/s]
Capturing batches (bs=72 avail_mem=9.55 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.17it/s]
Capturing batches (bs=64 avail_mem=9.49 GB):  57%|█████▋    | 13/23 [00:04<00:03,  3.30it/s]
Capturing batches (bs=56 avail_mem=9.45 GB):  57%|█████▋    | 13/23 [00:04<00:03,  3.30it/s]
Capturing batches (bs=72 avail_mem=9.55 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.05it/s]
Capturing batches (bs=64 avail_mem=9.49 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.05it/s]
Capturing batches (bs=56 avail_mem=9.45 GB):  61%|██████    | 14/23 [00:05<00:02,  3.35it/s]
Capturing batches (bs=48 avail_mem=9.39 GB):  61%|██████    | 14/23 [00:05<00:02,  3.35it/s]
Capturing batches (bs=64 avail_mem=9.49 GB):  57%|█████▋    | 13/23 [00:05<00:03,  3.14it/s]
Capturing batches (bs=56 avail_mem=9.45 GB):  57%|█████▋    | 13/23 [00:05<00:03,  3.14it/s]
Capturing batches (bs=48 avail_mem=9.39 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.40it/s]
Capturing batches (bs=40 avail_mem=9.36 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.40it/s]
Capturing batches (bs=56 avail_mem=9.45 GB):  61%|██████    | 14/23 [00:05<00:02,  3.21it/s]
Capturing batches (bs=48 avail_mem=9.39 GB):  61%|██████    | 14/23 [00:05<00:02,  3.21it/s]
Capturing batches (bs=40 avail_mem=9.36 GB):  70%|██████▉   | 16/23 [00:05<00:02,  3.44it/s]
Capturing batches (bs=32 avail_mem=9.33 GB):  70%|██████▉   | 16/23 [00:05<00:02,  3.44it/s]
Capturing batches (bs=48 avail_mem=9.39 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.25it/s]
Capturing batches (bs=40 avail_mem=9.36 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.25it/s]
Capturing batches (bs=32 avail_mem=9.33 GB):  74%|███████▍  | 17/23 [00:05<00:01,  3.34it/s]
Capturing batches (bs=24 avail_mem=9.31 GB):  74%|███████▍  | 17/23 [00:05<00:01,  3.34it/s]
Capturing batches (bs=40 avail_mem=9.36 GB):  70%|██████▉   | 16/23 [00:06<00:02,  3.29it/s]
Capturing batches (bs=32 avail_mem=9.33 GB):  70%|██████▉   | 16/23 [00:06<00:02,  3.29it/s]
Capturing batches (bs=24 avail_mem=9.31 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.39it/s]
Capturing batches (bs=16 avail_mem=9.28 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.39it/s]
Capturing batches (bs=32 avail_mem=9.33 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.23it/s]
Capturing batches (bs=24 avail_mem=9.31 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.23it/s]
Capturing batches (bs=16 avail_mem=9.28 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.43it/s]
Capturing batches (bs=8 avail_mem=9.25 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.43it/s] 
Capturing batches (bs=24 avail_mem=9.31 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.27it/s]
Capturing batches (bs=16 avail_mem=9.28 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.27it/s]
Capturing batches (bs=8 avail_mem=9.25 GB):  87%|████████▋ | 20/23 [00:06<00:00,  3.47it/s]
Capturing batches (bs=4 avail_mem=9.23 GB):  87%|████████▋ | 20/23 [00:06<00:00,  3.47it/s]
Capturing batches (bs=16 avail_mem=9.28 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.28it/s]
Capturing batches (bs=8 avail_mem=9.25 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.28it/s] 
Capturing batches (bs=4 avail_mem=9.23 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.48it/s]
Capturing batches (bs=2 avail_mem=9.23 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.48it/s]
Capturing batches (bs=8 avail_mem=9.25 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.30it/s]
Capturing batches (bs=4 avail_mem=9.23 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.30it/s]
Capturing batches (bs=2 avail_mem=9.23 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.51it/s]
Capturing batches (bs=1 avail_mem=9.20 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.51it/s]
Capturing batches (bs=4 avail_mem=9.23 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.29it/s]
Capturing batches (bs=2 avail_mem=9.23 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.29it/s][2025-07-28 00:55:48 DP0 TP2] Registering 1311 cuda graph addresses
[2025-07-28 00:55:48 DP0 TP1] Registering 1311 cuda graph addresses
[2025-07-28 00:55:48 DP0 TP3] Registering 1311 cuda graph addresses

Capturing batches (bs=1 avail_mem=9.20 GB): 100%|██████████| 23/23 [00:07<00:00,  3.52it/s]
Capturing batches (bs=1 avail_mem=9.20 GB): 100%|██████████| 23/23 [00:07<00:00,  3.02it/s]
[2025-07-28 00:55:48 DP0 TP0] Registering 1311 cuda graph addresses
[2025-07-28 00:55:48 DP0 TP0] Capture cuda graph end. Time elapsed: 7.77 s. mem usage=1.38 GB. avail mem=9.20 GB.

Capturing batches (bs=2 avail_mem=9.23 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.32it/s]
Capturing batches (bs=1 avail_mem=9.20 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.32it/s][2025-07-28 00:55:48 DP0 TP0] max_total_num_tokens=2475324, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=9.20 GB
[2025-07-28 00:55:48 DP1 TP1] Registering 1311 cuda graph addresses

Capturing batches (bs=1 avail_mem=9.20 GB): 100%|██████████| 23/23 [00:08<00:00,  3.32it/s]
Capturing batches (bs=1 avail_mem=9.20 GB): 100%|██████████| 23/23 [00:08<00:00,  2.82it/s]
[2025-07-28 00:55:48 DP1 TP0] Registering 1311 cuda graph addresses
[2025-07-28 00:55:48 DP1 TP3] Registering 1311 cuda graph addresses
[2025-07-28 00:55:48 DP1 TP2] Registering 1311 cuda graph addresses
[2025-07-28 00:55:48 DP1 TP0] Capture cuda graph end. Time elapsed: 8.32 s. mem usage=1.38 GB. avail mem=9.20 GB.
[2025-07-28 00:55:49 DP1 TP0] max_total_num_tokens=2475324, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=9.20 GB
[2025-07-28 00:55:49] INFO:     Started server process [1795977]
[2025-07-28 00:55:49] INFO:     Waiting for application startup.
[2025-07-28 00:55:49] INFO:     Application startup complete.
[2025-07-28 00:55:49] INFO:     Uvicorn running on http://127.0.0.1:8003 (Press CTRL+C to quit)
[2025-07-28 00:55:50] INFO:     127.0.0.1:37958 - "GET /health HTTP/1.1" 200 OK
[2025-07-28 00:55:50 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 235, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:55:50 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 194, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:55:50] INFO:     127.0.0.1:37986 - "GET /get_model_info HTTP/1.1" 200 OK
[2025-07-28 00:55:50 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 0, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:55:51 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 0, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:55:51] INFO:     127.0.0.1:38002 - "POST /generate HTTP/1.1" 200 OK
[2025-07-28 00:55:51] The server is fired up and ready to roll!
[2025-07-28 00:55:51 DP0 TP0] Decode batch. #running-req: 1, #token: 276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 18.93, #queue-req: 0, 
[2025-07-28 00:55:51 DP1 TP0] Decode batch. #running-req: 1, #token: 235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 22.79, #queue-req: 0, 
[2025-07-28 00:55:51 DP0 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.74, #queue-req: 0, 
[2025-07-28 00:55:51 DP1 TP0] Decode batch. #running-req: 1, #token: 275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 270.41, #queue-req: 0, 
[2025-07-28 00:55:51 DP0 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.11, #queue-req: 0, 
[2025-07-28 00:55:51 DP1 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.21, #queue-req: 0, 
[2025-07-28 00:55:51 DP1 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.39, #queue-req: 0, 
[2025-07-28 00:55:51 DP0 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 270.23, #queue-req: 0, 
[2025-07-28 00:55:51 DP1 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 322.57, #queue-req: 0, 
[2025-07-28 00:55:51 DP0 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 258.31, #queue-req: 0, 
[2025-07-28 00:55:51 DP1 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 325.47, #queue-req: 0, 
[2025-07-28 00:55:52 DP0 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.70, #queue-req: 0, 
[2025-07-28 00:55:52 DP1 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 330.34, #queue-req: 0, 
[2025-07-28 00:55:52 DP0 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.57, #queue-req: 0, 
[2025-07-28 00:55:52 DP1 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 326.84, #queue-req: 0, 
[2025-07-28 00:55:52 DP0 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.50, #queue-req: 0, 
[2025-07-28 00:55:52 DP1 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 332.49, #queue-req: 0, 
[2025-07-28 00:55:52 DP0 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.76, #queue-req: 0, 
[2025-07-28 00:55:52 DP1 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 331.96, #queue-req: 0, 
[2025-07-28 00:55:52 DP0 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.03, #queue-req: 0, 
[2025-07-28 00:55:52 DP1 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 328.47, #queue-req: 0, 
[2025-07-28 00:55:52 DP0 TP0] Decode batch. #running-req: 1, #token: 676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.24, #queue-req: 0, 
[2025-07-28 00:55:52 DP1 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 331.35, #queue-req: 0, 
[2025-07-28 00:55:52 DP1 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 328.64, #queue-req: 0, 
[2025-07-28 00:55:52 DP0 TP0] Decode batch. #running-req: 1, #token: 716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.55, #queue-req: 0, 
[2025-07-28 00:55:52 DP1 TP0] Decode batch. #running-req: 1, #token: 755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 330.20, #queue-req: 0, 
[2025-07-28 00:55:52 DP0 TP0] Decode batch. #running-req: 1, #token: 756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.13, #queue-req: 0, 
[2025-07-28 00:55:53 DP1 TP0] Decode batch. #running-req: 1, #token: 795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 329.79, #queue-req: 0, 
[2025-07-28 00:55:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.03, #queue-req: 0, 
[2025-07-28 00:55:53] INFO:     127.0.0.1:37966 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:55:53 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:55:53 DP1 TP0] Decode batch. #running-req: 1, #token: 835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 327.40, #queue-req: 0, 
[2025-07-28 00:55:53 DP1 TP0] Decode batch. #running-req: 1, #token: 875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 331.66, #queue-req: 0, 
[2025-07-28 00:55:53 DP1 TP0] Decode batch. #running-req: 1, #token: 915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 333.16, #queue-req: 0, 
[2025-07-28 00:55:53 DP0 TP0] Decode batch. #running-req: 1, #token: 209, token usage: 0.00, cuda graph: True, gen throughput (token/s): 106.04, #queue-req: 0, 
[2025-07-28 00:55:53 DP1 TP0] Decode batch. #running-req: 1, #token: 955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.88, #queue-req: 0, 
[2025-07-28 00:55:53 DP0 TP0] Decode batch. #running-req: 1, #token: 249, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.50, #queue-req: 0, 
[2025-07-28 00:55:53 DP1 TP0] Decode batch. #running-req: 1, #token: 995, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.88, #queue-req: 0, 
[2025-07-28 00:55:53 DP0 TP0] Decode batch. #running-req: 1, #token: 289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.23, #queue-req: 0, 
[2025-07-28 00:55:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.18, #queue-req: 0, 
[2025-07-28 00:55:53 DP0 TP0] Decode batch. #running-req: 1, #token: 329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.49, #queue-req: 0, 
[2025-07-28 00:55:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1075, token usage: 0.00, cuda graph: True, gen throughput (token/s): 273.86, #queue-req: 0, 
[2025-07-28 00:55:54 DP0 TP0] Decode batch. #running-req: 1, #token: 369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.85, #queue-req: 0, 
[2025-07-28 00:55:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.36, #queue-req: 0, 
[2025-07-28 00:55:54 DP0 TP0] Decode batch. #running-req: 1, #token: 409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.27, #queue-req: 0, 
[2025-07-28 00:55:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1155, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.55, #queue-req: 0, 
[2025-07-28 00:55:54 DP0 TP0] Decode batch. #running-req: 1, #token: 449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.64, #queue-req: 0, 
[2025-07-28 00:55:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.18, #queue-req: 0, 
[2025-07-28 00:55:54 DP0 TP0] Decode batch. #running-req: 1, #token: 489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.65, #queue-req: 0, 
[2025-07-28 00:55:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.01, #queue-req: 0, 
[2025-07-28 00:55:54 DP0 TP0] Decode batch. #running-req: 1, #token: 529, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.32, #queue-req: 0, 
[2025-07-28 00:55:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.82, #queue-req: 0, 
[2025-07-28 00:55:54 DP0 TP0] Decode batch. #running-req: 1, #token: 569, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.62, #queue-req: 0, 
[2025-07-28 00:55:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.76, #queue-req: 0, 
[2025-07-28 00:55:54 DP0 TP0] Decode batch. #running-req: 1, #token: 609, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.80, #queue-req: 0, 
[2025-07-28 00:55:54] INFO:     127.0.0.1:41548 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:55:54 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 207, #cached-token: 86, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:55:55 DP1 TP0] Decode batch. #running-req: 2, #token: 1564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 107.46, #queue-req: 0, 
[2025-07-28 00:55:55 DP1 TP0] Decode batch. #running-req: 2, #token: 1644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 599.80, #queue-req: 0, 
[2025-07-28 00:55:55 DP1 TP0] Decode batch. #running-req: 2, #token: 1724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 588.39, #queue-req: 0, 
[2025-07-28 00:55:55 DP1 TP0] Decode batch. #running-req: 2, #token: 1804, token usage: 0.00, cuda graph: True, gen throughput (token/s): 558.49, #queue-req: 0, 
[2025-07-28 00:55:55 DP1 TP0] Decode batch. #running-req: 2, #token: 1884, token usage: 0.00, cuda graph: True, gen throughput (token/s): 598.67, #queue-req: 0, 
[2025-07-28 00:55:55 DP1 TP0] Decode batch. #running-req: 2, #token: 1964, token usage: 0.00, cuda graph: True, gen throughput (token/s): 595.55, #queue-req: 0, 
[2025-07-28 00:55:56 DP1 TP0] Decode batch. #running-req: 2, #token: 2044, token usage: 0.00, cuda graph: True, gen throughput (token/s): 594.44, #queue-req: 0, 
[2025-07-28 00:55:56 DP1 TP0] Decode batch. #running-req: 2, #token: 2124, token usage: 0.00, cuda graph: True, gen throughput (token/s): 591.16, #queue-req: 0, 
[2025-07-28 00:55:56 DP1 TP0] Decode batch. #running-req: 2, #token: 2204, token usage: 0.00, cuda graph: True, gen throughput (token/s): 601.11, #queue-req: 0, 
[2025-07-28 00:55:56 DP1 TP0] Decode batch. #running-req: 2, #token: 2284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 599.71, #queue-req: 0, 
[2025-07-28 00:55:56 DP1 TP0] Decode batch. #running-req: 2, #token: 2364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 601.16, #queue-req: 0, 
[2025-07-28 00:55:56 DP1 TP0] Decode batch. #running-req: 2, #token: 2444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 597.52, #queue-req: 0, 
[2025-07-28 00:55:56 DP1 TP0] Decode batch. #running-req: 2, #token: 2524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 603.90, #queue-req: 0, 
[2025-07-28 00:55:56 DP1 TP0] Decode batch. #running-req: 2, #token: 2604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 602.30, #queue-req: 0, 
[2025-07-28 00:55:57] INFO:     127.0.0.1:37974 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:55:57 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:55:57 DP1 TP0] Decode batch. #running-req: 1, #token: 855, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.27, #queue-req: 0, 
[2025-07-28 00:55:57 DP0 TP0] Decode batch. #running-req: 1, #token: 240, token usage: 0.00, cuda graph: True, gen throughput (token/s): 17.16, #queue-req: 0, 
[2025-07-28 00:55:57 DP1 TP0] Decode batch. #running-req: 1, #token: 895, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.97, #queue-req: 0, 
[2025-07-28 00:55:57 DP0 TP0] Decode batch. #running-req: 1, #token: 280, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.54, #queue-req: 0, 
[2025-07-28 00:55:57 DP1 TP0] Decode batch. #running-req: 1, #token: 935, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.96, #queue-req: 0, 
[2025-07-28 00:55:57 DP0 TP0] Decode batch. #running-req: 1, #token: 320, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.33, #queue-req: 0, 
[2025-07-28 00:55:57 DP1 TP0] Decode batch. #running-req: 1, #token: 975, token usage: 0.00, cuda graph: True, gen throughput (token/s): 273.32, #queue-req: 0, 
[2025-07-28 00:55:57 DP0 TP0] Decode batch. #running-req: 1, #token: 360, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.49, #queue-req: 0, 
[2025-07-28 00:55:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1015, token usage: 0.00, cuda graph: True, gen throughput (token/s): 273.81, #queue-req: 0, 
[2025-07-28 00:55:57 DP0 TP0] Decode batch. #running-req: 1, #token: 400, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.99, #queue-req: 0, 
[2025-07-28 00:55:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1055, token usage: 0.00, cuda graph: True, gen throughput (token/s): 272.36, #queue-req: 0, 
[2025-07-28 00:55:57 DP0 TP0] Decode batch. #running-req: 1, #token: 440, token usage: 0.00, cuda graph: True, gen throughput (token/s): 270.17, #queue-req: 0, 
[2025-07-28 00:55:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1095, token usage: 0.00, cuda graph: True, gen throughput (token/s): 274.41, #queue-req: 0, 
[2025-07-28 00:55:58 DP0 TP0] Decode batch. #running-req: 1, #token: 480, token usage: 0.00, cuda graph: True, gen throughput (token/s): 270.75, #queue-req: 0, 
[2025-07-28 00:55:58 DP1 TP0] Decode batch. #running-req: 1, #token: 1135, token usage: 0.00, cuda graph: True, gen throughput (token/s): 264.83, #queue-req: 0, 
[2025-07-28 00:55:58 DP0 TP0] Decode batch. #running-req: 1, #token: 520, token usage: 0.00, cuda graph: True, gen throughput (token/s): 273.46, #queue-req: 0, 
[2025-07-28 00:55:58 DP1 TP0] Decode batch. #running-req: 1, #token: 1175, token usage: 0.00, cuda graph: True, gen throughput (token/s): 272.36, #queue-req: 0, 
[2025-07-28 00:55:58 DP0 TP0] Decode batch. #running-req: 1, #token: 560, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.61, #queue-req: 0, 
[2025-07-28 00:55:58 DP1 TP0] Decode batch. #running-req: 1, #token: 1215, token usage: 0.00, cuda graph: True, gen throughput (token/s): 281.18, #queue-req: 0, 
[2025-07-28 00:55:58 DP0 TP0] Decode batch. #running-req: 1, #token: 600, token usage: 0.00, cuda graph: True, gen throughput (token/s): 278.65, #queue-req: 0, 
[2025-07-28 00:55:58 DP1 TP0] Decode batch. #running-req: 1, #token: 1255, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.21, #queue-req: 0, 
[2025-07-28 00:55:58 DP0 TP0] Decode batch. #running-req: 1, #token: 640, token usage: 0.00, cuda graph: True, gen throughput (token/s): 274.98, #queue-req: 0, 
[2025-07-28 00:55:58 DP1 TP0] Decode batch. #running-req: 1, #token: 1295, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.98, #queue-req: 0, 
[2025-07-28 00:55:58 DP0 TP0] Decode batch. #running-req: 1, #token: 680, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.70, #queue-req: 0, 
[2025-07-28 00:55:58 DP1 TP0] Decode batch. #running-req: 1, #token: 1335, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.96, #queue-req: 0, 
[2025-07-28 00:55:58 DP0 TP0] Decode batch. #running-req: 1, #token: 0, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.65, #queue-req: 0, 
[2025-07-28 00:55:58] INFO:     127.0.0.1:41564 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:55:58 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 133, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:55:58 DP1 TP0] Decode batch. #running-req: 2, #token: 1511, token usage: 0.00, cuda graph: True, gen throughput (token/s): 223.97, #queue-req: 0, 
[2025-07-28 00:55:59] INFO:     127.0.0.1:41550 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:55:59 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 186, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:55:59 DP1 TP0] Decode batch. #running-req: 1, #token: 261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.90, #queue-req: 0, 
[2025-07-28 00:55:59 DP1 TP0] Decode batch. #running-req: 1, #token: 301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 333.08, #queue-req: 0, 
[2025-07-28 00:55:59 DP0 TP0] Decode batch. #running-req: 1, #token: 312, token usage: 0.00, cuda graph: True, gen throughput (token/s): 119.82, #queue-req: 0, 
[2025-07-28 00:55:59 DP1 TP0] Decode batch. #running-req: 1, #token: 341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 333.34, #queue-req: 0, 
[2025-07-28 00:55:59 DP0 TP0] Decode batch. #running-req: 1, #token: 352, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.56, #queue-req: 0, 
[2025-07-28 00:55:59 DP1 TP0] Decode batch. #running-req: 1, #token: 381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.42, #queue-req: 0, 
[2025-07-28 00:55:59 DP0 TP0] Decode batch. #running-req: 1, #token: 392, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.67, #queue-req: 0, 
[2025-07-28 00:55:59 DP1 TP0] Decode batch. #running-req: 1, #token: 421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 276.54, #queue-req: 0, 
[2025-07-28 00:55:59 DP0 TP0] Decode batch. #running-req: 1, #token: 432, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.54, #queue-req: 0, 
[2025-07-28 00:55:59 DP0 TP0] Decode batch. #running-req: 1, #token: 472, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.42, #queue-req: 0, 
[2025-07-28 00:55:59 DP1 TP0] Decode batch. #running-req: 1, #token: 461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.81, #queue-req: 0, 
[2025-07-28 00:55:59 DP0 TP0] Decode batch. #running-req: 1, #token: 512, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.70, #queue-req: 0, 
[2025-07-28 00:55:59 DP1 TP0] Decode batch. #running-req: 1, #token: 501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.72, #queue-req: 0, 
[2025-07-28 00:56:00 DP0 TP0] Decode batch. #running-req: 1, #token: 552, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.29, #queue-req: 0, 
[2025-07-28 00:56:00 DP1 TP0] Decode batch. #running-req: 1, #token: 541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.01, #queue-req: 0, 
[2025-07-28 00:56:00 DP0 TP0] Decode batch. #running-req: 1, #token: 592, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.99, #queue-req: 0, 
[2025-07-28 00:56:00 DP1 TP0] Decode batch. #running-req: 1, #token: 581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 275.70, #queue-req: 0, 
[2025-07-28 00:56:00 DP0 TP0] Decode batch. #running-req: 1, #token: 632, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.46, #queue-req: 0, 
[2025-07-28 00:56:00 DP1 TP0] Decode batch. #running-req: 1, #token: 621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.07, #queue-req: 0, 
[2025-07-28 00:56:00 DP0 TP0] Decode batch. #running-req: 1, #token: 672, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.72, #queue-req: 0, 
[2025-07-28 00:56:00 DP1 TP0] Decode batch. #running-req: 1, #token: 661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.23, #queue-req: 0, 
[2025-07-28 00:56:00 DP0 TP0] Decode batch. #running-req: 1, #token: 712, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.02, #queue-req: 0, 
[2025-07-28 00:56:00 DP1 TP0] Decode batch. #running-req: 1, #token: 701, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.66, #queue-req: 0, 
[2025-07-28 00:56:00 DP0 TP0] Decode batch. #running-req: 1, #token: 752, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.29, #queue-req: 0, 
[2025-07-28 00:56:00 DP1 TP0] Decode batch. #running-req: 1, #token: 741, token usage: 0.00, cuda graph: True, gen throughput (token/s): 264.49, #queue-req: 0, 
[2025-07-28 00:56:00 DP0 TP0] Decode batch. #running-req: 1, #token: 792, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.57, #queue-req: 0, 
[2025-07-28 00:56:00 DP1 TP0] Decode batch. #running-req: 1, #token: 781, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.33, #queue-req: 0, 
[2025-07-28 00:56:00] INFO:     127.0.0.1:41592 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:00 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 173, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:56:01 DP1 TP0] Decode batch. #running-req: 2, #token: 1029, token usage: 0.00, cuda graph: True, gen throughput (token/s): 379.43, #queue-req: 0, 
[2025-07-28 00:56:01 DP1 TP0] Decode batch. #running-req: 2, #token: 1109, token usage: 0.00, cuda graph: True, gen throughput (token/s): 594.39, #queue-req: 0, 
[2025-07-28 00:56:01 DP1 TP0] Decode batch. #running-req: 2, #token: 1189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 578.66, #queue-req: 0, 
[2025-07-28 00:56:01] INFO:     127.0.0.1:41576 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:01 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 226, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:01 DP1 TP0] Decode batch. #running-req: 1, #token: 413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 436.47, #queue-req: 0, 
[2025-07-28 00:56:01 DP0 TP0] Decode batch. #running-req: 1, #token: 328, token usage: 0.00, cuda graph: True, gen throughput (token/s): 53.71, #queue-req: 0, 
[2025-07-28 00:56:01 DP1 TP0] Decode batch. #running-req: 1, #token: 453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.54, #queue-req: 0, 
[2025-07-28 00:56:01 DP0 TP0] Decode batch. #running-req: 1, #token: 368, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.57, #queue-req: 0, 
[2025-07-28 00:56:01 DP1 TP0] Decode batch. #running-req: 1, #token: 493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 275.95, #queue-req: 0, 
[2025-07-28 00:56:01 DP0 TP0] Decode batch. #running-req: 1, #token: 408, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.99, #queue-req: 0, 
[2025-07-28 00:56:01 DP1 TP0] Decode batch. #running-req: 1, #token: 533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 274.57, #queue-req: 0, 
[2025-07-28 00:56:01 DP0 TP0] Decode batch. #running-req: 1, #token: 448, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.60, #queue-req: 0, 
[2025-07-28 00:56:02 DP1 TP0] Decode batch. #running-req: 1, #token: 573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.43, #queue-req: 0, 
[2025-07-28 00:56:02 DP0 TP0] Decode batch. #running-req: 1, #token: 488, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.45, #queue-req: 0, 
[2025-07-28 00:56:02 DP1 TP0] Decode batch. #running-req: 1, #token: 613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.91, #queue-req: 0, 
[2025-07-28 00:56:02 DP0 TP0] Decode batch. #running-req: 1, #token: 528, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.83, #queue-req: 0, 
[2025-07-28 00:56:02 DP1 TP0] Decode batch. #running-req: 1, #token: 653, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.51, #queue-req: 0, 
[2025-07-28 00:56:02 DP0 TP0] Decode batch. #running-req: 1, #token: 568, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.88, #queue-req: 0, 
[2025-07-28 00:56:02 DP1 TP0] Decode batch. #running-req: 1, #token: 693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.81, #queue-req: 0, 
[2025-07-28 00:56:02 DP0 TP0] Decode batch. #running-req: 1, #token: 608, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.50, #queue-req: 0, 
[2025-07-28 00:56:02 DP1 TP0] Decode batch. #running-req: 1, #token: 733, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.93, #queue-req: 0, 
[2025-07-28 00:56:02 DP0 TP0] Decode batch. #running-req: 1, #token: 648, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.61, #queue-req: 0, 
[2025-07-28 00:56:02 DP1 TP0] Decode batch. #running-req: 1, #token: 773, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.24, #queue-req: 0, 
[2025-07-28 00:56:02 DP0 TP0] Decode batch. #running-req: 1, #token: 688, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.05, #queue-req: 0, 
[2025-07-28 00:56:02 DP1 TP0] Decode batch. #running-req: 1, #token: 813, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.39, #queue-req: 0, 
[2025-07-28 00:56:02 DP0 TP0] Decode batch. #running-req: 1, #token: 728, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.66, #queue-req: 0, 
[2025-07-28 00:56:02 DP1 TP0] Decode batch. #running-req: 1, #token: 853, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.56, #queue-req: 0, 
[2025-07-28 00:56:03 DP0 TP0] Decode batch. #running-req: 1, #token: 768, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.33, #queue-req: 0, 
[2025-07-28 00:56:03 DP1 TP0] Decode batch. #running-req: 1, #token: 893, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.85, #queue-req: 0, 
[2025-07-28 00:56:03 DP0 TP0] Decode batch. #running-req: 1, #token: 808, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.94, #queue-req: 0, 
[2025-07-28 00:56:03 DP1 TP0] Decode batch. #running-req: 1, #token: 933, token usage: 0.00, cuda graph: True, gen throughput (token/s): 274.67, #queue-req: 0, 
[2025-07-28 00:56:03 DP0 TP0] Decode batch. #running-req: 1, #token: 848, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.34, #queue-req: 0, 
[2025-07-28 00:56:03 DP1 TP0] Decode batch. #running-req: 1, #token: 973, token usage: 0.00, cuda graph: True, gen throughput (token/s): 275.41, #queue-req: 0, 
[2025-07-28 00:56:03 DP0 TP0] Decode batch. #running-req: 1, #token: 888, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.17, #queue-req: 0, 
[2025-07-28 00:56:03] INFO:     127.0.0.1:41618 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:03 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 168, #cached-token: 88, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:56:03 DP1 TP0] Decode batch. #running-req: 2, #token: 1197, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.58, #queue-req: 0, 
[2025-07-28 00:56:03] INFO:     127.0.0.1:41602 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:03 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 154, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:03 DP1 TP0] Decode batch. #running-req: 1, #token: 311, token usage: 0.00, cuda graph: True, gen throughput (token/s): 499.16, #queue-req: 0, 
[2025-07-28 00:56:03 DP0 TP0] Decode batch. #running-req: 1, #token: 266, token usage: 0.00, cuda graph: True, gen throughput (token/s): 96.49, #queue-req: 0, 
[2025-07-28 00:56:03 DP1 TP0] Decode batch. #running-req: 1, #token: 351, token usage: 0.00, cuda graph: True, gen throughput (token/s): 337.27, #queue-req: 0, 
[2025-07-28 00:56:03 DP0 TP0] Decode batch. #running-req: 1, #token: 306, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.42, #queue-req: 0, 
[2025-07-28 00:56:03 DP1 TP0] Decode batch. #running-req: 1, #token: 391, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.03, #queue-req: 0, 
[2025-07-28 00:56:04 DP0 TP0] Decode batch. #running-req: 1, #token: 346, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.88, #queue-req: 0, 
[2025-07-28 00:56:04 DP1 TP0] Decode batch. #running-req: 1, #token: 431, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.51, #queue-req: 0, 
[2025-07-28 00:56:04 DP1 TP0] Decode batch. #running-req: 1, #token: 471, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.43, #queue-req: 0, 
[2025-07-28 00:56:04 DP0 TP0] Decode batch. #running-req: 1, #token: 386, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.99, #queue-req: 0, 
[2025-07-28 00:56:04 DP1 TP0] Decode batch. #running-req: 1, #token: 511, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.59, #queue-req: 0, 
[2025-07-28 00:56:04 DP0 TP0] Decode batch. #running-req: 1, #token: 426, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.21, #queue-req: 0, 
[2025-07-28 00:56:04 DP1 TP0] Decode batch. #running-req: 1, #token: 551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.61, #queue-req: 0, 
[2025-07-28 00:56:04 DP0 TP0] Decode batch. #running-req: 1, #token: 466, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.22, #queue-req: 0, 
[2025-07-28 00:56:04 DP1 TP0] Decode batch. #running-req: 1, #token: 591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.93, #queue-req: 0, 
[2025-07-28 00:56:04 DP0 TP0] Decode batch. #running-req: 1, #token: 506, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.51, #queue-req: 0, 
[2025-07-28 00:56:04 DP1 TP0] Decode batch. #running-req: 1, #token: 631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.87, #queue-req: 0, 
[2025-07-28 00:56:04 DP0 TP0] Decode batch. #running-req: 1, #token: 546, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.41, #queue-req: 0, 
[2025-07-28 00:56:04 DP1 TP0] Decode batch. #running-req: 1, #token: 671, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.55, #queue-req: 0, 
[2025-07-28 00:56:04 DP0 TP0] Decode batch. #running-req: 1, #token: 586, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.74, #queue-req: 0, 
[2025-07-28 00:56:04 DP1 TP0] Decode batch. #running-req: 1, #token: 711, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.51, #queue-req: 0, 
[2025-07-28 00:56:05 DP0 TP0] Decode batch. #running-req: 1, #token: 626, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.59, #queue-req: 0, 
[2025-07-28 00:56:05 DP1 TP0] Decode batch. #running-req: 1, #token: 751, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.27, #queue-req: 0, 
[2025-07-28 00:56:05 DP0 TP0] Decode batch. #running-req: 1, #token: 666, token usage: 0.00, cuda graph: True, gen throughput (token/s): 270.19, #queue-req: 0, 
[2025-07-28 00:56:05 DP1 TP0] Decode batch. #running-req: 1, #token: 791, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.14, #queue-req: 0, 
[2025-07-28 00:56:05] INFO:     127.0.0.1:52046 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:05 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 141, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:56:05 DP1 TP0] Decode batch. #running-req: 2, #token: 1003, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.09, #queue-req: 0, 
[2025-07-28 00:56:05 DP1 TP0] Decode batch. #running-req: 2, #token: 1083, token usage: 0.00, cuda graph: True, gen throughput (token/s): 631.55, #queue-req: 0, 
[2025-07-28 00:56:05 DP1 TP0] Decode batch. #running-req: 2, #token: 1163, token usage: 0.00, cuda graph: True, gen throughput (token/s): 562.89, #queue-req: 0, 
[2025-07-28 00:56:05] INFO:     127.0.0.1:52030 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:05 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 192, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:05 DP1 TP0] Decode batch. #running-req: 1, #token: 377, token usage: 0.00, cuda graph: True, gen throughput (token/s): 395.41, #queue-req: 0, 
[2025-07-28 00:56:05 DP0 TP0] Decode batch. #running-req: 1, #token: 299, token usage: 0.00, cuda graph: True, gen throughput (token/s): 55.65, #queue-req: 0, 
[2025-07-28 00:56:05 DP1 TP0] Decode batch. #running-req: 1, #token: 417, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.10, #queue-req: 0, 
[2025-07-28 00:56:06 DP0 TP0] Decode batch. #running-req: 1, #token: 339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.48, #queue-req: 0, 
[2025-07-28 00:56:06 DP1 TP0] Decode batch. #running-req: 1, #token: 457, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.85, #queue-req: 0, 
[2025-07-28 00:56:06 DP0 TP0] Decode batch. #running-req: 1, #token: 379, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.89, #queue-req: 0, 
[2025-07-28 00:56:06 DP1 TP0] Decode batch. #running-req: 1, #token: 497, token usage: 0.00, cuda graph: True, gen throughput (token/s): 282.16, #queue-req: 0, 
[2025-07-28 00:56:06 DP0 TP0] Decode batch. #running-req: 1, #token: 419, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.21, #queue-req: 0, 
[2025-07-28 00:56:06 DP1 TP0] Decode batch. #running-req: 1, #token: 537, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.74, #queue-req: 0, 
[2025-07-28 00:56:06 DP0 TP0] Decode batch. #running-req: 1, #token: 459, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.01, #queue-req: 0, 
[2025-07-28 00:56:06 DP1 TP0] Decode batch. #running-req: 1, #token: 577, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.56, #queue-req: 0, 
[2025-07-28 00:56:06 DP0 TP0] Decode batch. #running-req: 1, #token: 499, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.93, #queue-req: 0, 
[2025-07-28 00:56:06 DP1 TP0] Decode batch. #running-req: 1, #token: 617, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.74, #queue-req: 0, 
[2025-07-28 00:56:06 DP0 TP0] Decode batch. #running-req: 1, #token: 539, token usage: 0.00, cuda graph: True, gen throughput (token/s): 288.42, #queue-req: 0, 
[2025-07-28 00:56:06 DP1 TP0] Decode batch. #running-req: 1, #token: 657, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.09, #queue-req: 0, 
[2025-07-28 00:56:06 DP0 TP0] Decode batch. #running-req: 1, #token: 579, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.17, #queue-req: 0, 
[2025-07-28 00:56:06 DP1 TP0] Decode batch. #running-req: 1, #token: 697, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.89, #queue-req: 0, 
[2025-07-28 00:56:06 DP0 TP0] Decode batch. #running-req: 1, #token: 619, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.32, #queue-req: 0, 
[2025-07-28 00:56:07 DP1 TP0] Decode batch. #running-req: 1, #token: 737, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.60, #queue-req: 0, 
[2025-07-28 00:56:07 DP0 TP0] Decode batch. #running-req: 1, #token: 659, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.45, #queue-req: 0, 
[2025-07-28 00:56:07 DP1 TP0] Decode batch. #running-req: 1, #token: 777, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.94, #queue-req: 0, 
[2025-07-28 00:56:07] INFO:     127.0.0.1:52050 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:07 DP0 TP0] Decode batch. #running-req: 1, #token: 699, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.19, #queue-req: 0, 
[2025-07-28 00:56:07 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 190, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:07 DP0 TP0] Decode batch. #running-req: 1, #token: 739, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.73, #queue-req: 0, 
[2025-07-28 00:56:07 DP1 TP0] Decode batch. #running-req: 1, #token: 301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 189.01, #queue-req: 0, 
[2025-07-28 00:56:07 DP1 TP0] Decode batch. #running-req: 1, #token: 341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 323.64, #queue-req: 0, 
[2025-07-28 00:56:07 DP0 TP0] Decode batch. #running-req: 1, #token: 779, token usage: 0.00, cuda graph: True, gen throughput (token/s): 271.31, #queue-req: 0, 
[2025-07-28 00:56:07 DP1 TP0] Decode batch. #running-req: 1, #token: 381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.58, #queue-req: 0, 
[2025-07-28 00:56:07 DP0 TP0] Decode batch. #running-req: 1, #token: 819, token usage: 0.00, cuda graph: True, gen throughput (token/s): 271.03, #queue-req: 0, 
[2025-07-28 00:56:07 DP1 TP0] Decode batch. #running-req: 1, #token: 421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 273.26, #queue-req: 0, 
[2025-07-28 00:56:07] INFO:     127.0.0.1:52052 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:07 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 137, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:07 DP0 TP0] Decode batch. #running-req: 1, #token: 230, token usage: 0.00, cuda graph: True, gen throughput (token/s): 176.40, #queue-req: 0, 
[2025-07-28 00:56:07 DP1 TP0] Decode batch. #running-req: 1, #token: 461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.96, #queue-req: 0, 
[2025-07-28 00:56:08 DP0 TP0] Decode batch. #running-req: 1, #token: 270, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.47, #queue-req: 0, 
[2025-07-28 00:56:08 DP1 TP0] Decode batch. #running-req: 1, #token: 501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.47, #queue-req: 0, 
[2025-07-28 00:56:08 DP1 TP0] Decode batch. #running-req: 1, #token: 541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 270.80, #queue-req: 0, 
[2025-07-28 00:56:08 DP0 TP0] Decode batch. #running-req: 1, #token: 310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 251.48, #queue-req: 0, 
[2025-07-28 00:56:08 DP1 TP0] Decode batch. #running-req: 1, #token: 581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 282.20, #queue-req: 0, 
[2025-07-28 00:56:08 DP0 TP0] Decode batch. #running-req: 1, #token: 350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 259.16, #queue-req: 0, 
[2025-07-28 00:56:08 DP1 TP0] Decode batch. #running-req: 1, #token: 621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 280.17, #queue-req: 0, 
[2025-07-28 00:56:08 DP0 TP0] Decode batch. #running-req: 1, #token: 390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 250.46, #queue-req: 0, 
[2025-07-28 00:56:08 DP1 TP0] Decode batch. #running-req: 1, #token: 661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 274.63, #queue-req: 0, 
[2025-07-28 00:56:08 DP0 TP0] Decode batch. #running-req: 1, #token: 430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.44, #queue-req: 0, 
[2025-07-28 00:56:08 DP1 TP0] Decode batch. #running-req: 1, #token: 701, token usage: 0.00, cuda graph: True, gen throughput (token/s): 274.30, #queue-req: 0, 
[2025-07-28 00:56:08 DP0 TP0] Decode batch. #running-req: 1, #token: 470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 263.68, #queue-req: 0, 
[2025-07-28 00:56:08 DP1 TP0] Decode batch. #running-req: 1, #token: 741, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.37, #queue-req: 0, 
[2025-07-28 00:56:08 DP0 TP0] Decode batch. #running-req: 1, #token: 510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 259.11, #queue-req: 0, 
[2025-07-28 00:56:09 DP1 TP0] Decode batch. #running-req: 1, #token: 781, token usage: 0.00, cuda graph: True, gen throughput (token/s): 278.55, #queue-req: 0, 
[2025-07-28 00:56:09 DP0 TP0] Decode batch. #running-req: 1, #token: 550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 275.69, #queue-req: 0, 
[2025-07-28 00:56:09 DP1 TP0] Decode batch. #running-req: 1, #token: 821, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.86, #queue-req: 0, 
[2025-07-28 00:56:09 DP0 TP0] Decode batch. #running-req: 1, #token: 590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 281.97, #queue-req: 0, 
[2025-07-28 00:56:09 DP1 TP0] Decode batch. #running-req: 1, #token: 861, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.30, #queue-req: 0, 
[2025-07-28 00:56:09 DP0 TP0] Decode batch. #running-req: 1, #token: 630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.91, #queue-req: 0, 
[2025-07-28 00:56:09 DP1 TP0] Decode batch. #running-req: 1, #token: 901, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.97, #queue-req: 0, 
[2025-07-28 00:56:09 DP0 TP0] Decode batch. #running-req: 1, #token: 670, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.49, #queue-req: 0, 
[2025-07-28 00:56:09 DP1 TP0] Decode batch. #running-req: 1, #token: 941, token usage: 0.00, cuda graph: True, gen throughput (token/s): 271.53, #queue-req: 0, 
[2025-07-28 00:56:09 DP0 TP0] Decode batch. #running-req: 1, #token: 710, token usage: 0.00, cuda graph: True, gen throughput (token/s): 273.82, #queue-req: 0, 
[2025-07-28 00:56:09 DP1 TP0] Decode batch. #running-req: 1, #token: 981, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.15, #queue-req: 0, 
[2025-07-28 00:56:09 DP0 TP0] Decode batch. #running-req: 1, #token: 750, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.58, #queue-req: 0, 
[2025-07-28 00:56:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1021, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.05, #queue-req: 0, 
[2025-07-28 00:56:09] INFO:     127.0.0.1:52054 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:09 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 132, #cached-token: 89, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:09 DP0 TP0] Decode batch. #running-req: 1, #token: 790, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.52, #queue-req: 0, 
[2025-07-28 00:56:10 DP1 TP0] Decode batch. #running-req: 1, #token: 252, token usage: 0.00, cuda graph: True, gen throughput (token/s): 195.90, #queue-req: 0, 
[2025-07-28 00:56:10 DP0 TP0] Decode batch. #running-req: 1, #token: 830, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.52, #queue-req: 0, 
[2025-07-28 00:56:10 DP1 TP0] Decode batch. #running-req: 1, #token: 292, token usage: 0.00, cuda graph: True, gen throughput (token/s): 328.51, #queue-req: 0, 
[2025-07-28 00:56:10 DP0 TP0] Decode batch. #running-req: 1, #token: 870, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.41, #queue-req: 0, 
[2025-07-28 00:56:10 DP1 TP0] Decode batch. #running-req: 1, #token: 332, token usage: 0.00, cuda graph: True, gen throughput (token/s): 322.34, #queue-req: 0, 
[2025-07-28 00:56:10 DP0 TP0] Decode batch. #running-req: 1, #token: 0, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.62, #queue-req: 0, 
[2025-07-28 00:56:10] INFO:     127.0.0.1:52062 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:10 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 199, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:10 DP1 TP0] Decode batch. #running-req: 1, #token: 372, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.61, #queue-req: 0, 
[2025-07-28 00:56:10 DP0 TP0] Decode batch. #running-req: 1, #token: 328, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.56, #queue-req: 0, 
[2025-07-28 00:56:10 DP1 TP0] Decode batch. #running-req: 1, #token: 412, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.94, #queue-req: 0, 
[2025-07-28 00:56:10 DP0 TP0] Decode batch. #running-req: 1, #token: 368, token usage: 0.00, cuda graph: True, gen throughput (token/s): 324.36, #queue-req: 0, 
[2025-07-28 00:56:10 DP1 TP0] Decode batch. #running-req: 1, #token: 452, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.16, #queue-req: 0, 
[2025-07-28 00:56:10 DP1 TP0] Decode batch. #running-req: 1, #token: 492, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.65, #queue-req: 0, 
[2025-07-28 00:56:10 DP0 TP0] Decode batch. #running-req: 1, #token: 408, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.29, #queue-req: 0, 
[2025-07-28 00:56:10 DP0 TP0] Decode batch. #running-req: 1, #token: 448, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.75, #queue-req: 0, 
[2025-07-28 00:56:10 DP1 TP0] Decode batch. #running-req: 1, #token: 532, token usage: 0.00, cuda graph: True, gen throughput (token/s): 261.59, #queue-req: 0, 
[2025-07-28 00:56:11 DP0 TP0] Decode batch. #running-req: 1, #token: 488, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.33, #queue-req: 0, 
[2025-07-28 00:56:11 DP1 TP0] Decode batch. #running-req: 1, #token: 572, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.76, #queue-req: 0, 
[2025-07-28 00:56:11 DP0 TP0] Decode batch. #running-req: 1, #token: 528, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.66, #queue-req: 0, 
[2025-07-28 00:56:11 DP1 TP0] Decode batch. #running-req: 1, #token: 612, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.61, #queue-req: 0, 
[2025-07-28 00:56:11 DP0 TP0] Decode batch. #running-req: 1, #token: 568, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.27, #queue-req: 0, 
[2025-07-28 00:56:11 DP1 TP0] Decode batch. #running-req: 1, #token: 652, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.86, #queue-req: 0, 
[2025-07-28 00:56:11 DP0 TP0] Decode batch. #running-req: 1, #token: 608, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.78, #queue-req: 0, 
[2025-07-28 00:56:11 DP1 TP0] Decode batch. #running-req: 1, #token: 692, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.40, #queue-req: 0, 
[2025-07-28 00:56:11] INFO:     127.0.0.1:52066 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:11 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 194, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:11 DP0 TP0] Decode batch. #running-req: 1, #token: 648, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.83, #queue-req: 0, 
[2025-07-28 00:56:11 DP1 TP0] Decode batch. #running-req: 1, #token: 303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 195.86, #queue-req: 0, 
[2025-07-28 00:56:11 DP0 TP0] Decode batch. #running-req: 1, #token: 688, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.14, #queue-req: 0, 
[2025-07-28 00:56:11 DP1 TP0] Decode batch. #running-req: 1, #token: 343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 332.07, #queue-req: 0, 
[2025-07-28 00:56:11 DP0 TP0] Decode batch. #running-req: 1, #token: 728, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.65, #queue-req: 0, 
[2025-07-28 00:56:11 DP1 TP0] Decode batch. #running-req: 1, #token: 383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 336.85, #queue-req: 0, 
[2025-07-28 00:56:12 DP0 TP0] Decode batch. #running-req: 1, #token: 768, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.80, #queue-req: 0, 
[2025-07-28 00:56:12 DP1 TP0] Decode batch. #running-req: 1, #token: 423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.74, #queue-req: 0, 
[2025-07-28 00:56:12 DP0 TP0] Decode batch. #running-req: 1, #token: 808, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.17, #queue-req: 0, 
[2025-07-28 00:56:12 DP1 TP0] Decode batch. #running-req: 1, #token: 463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.48, #queue-req: 0, 
[2025-07-28 00:56:12 DP0 TP0] Decode batch. #running-req: 1, #token: 848, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.83, #queue-req: 0, 
[2025-07-28 00:56:12 DP1 TP0] Decode batch. #running-req: 1, #token: 503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.38, #queue-req: 0, 
[2025-07-28 00:56:12 DP0 TP0] Decode batch. #running-req: 1, #token: 888, token usage: 0.00, cuda graph: True, gen throughput (token/s): 288.70, #queue-req: 0, 
[2025-07-28 00:56:12 DP1 TP0] Decode batch. #running-req: 1, #token: 543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.03, #queue-req: 0, 
[2025-07-28 00:56:12 DP0 TP0] Decode batch. #running-req: 1, #token: 928, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.02, #queue-req: 0, 
[2025-07-28 00:56:12 DP1 TP0] Decode batch. #running-req: 1, #token: 583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.32, #queue-req: 0, 
[2025-07-28 00:56:12 DP1 TP0] Decode batch. #running-req: 1, #token: 623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.52, #queue-req: 0, 
[2025-07-28 00:56:12 DP0 TP0] Decode batch. #running-req: 1, #token: 968, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.23, #queue-req: 0, 
[2025-07-28 00:56:12 DP1 TP0] Decode batch. #running-req: 1, #token: 663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.46, #queue-req: 0, 
[2025-07-28 00:56:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1008, token usage: 0.00, cuda graph: True, gen throughput (token/s): 276.83, #queue-req: 0, 
[2025-07-28 00:56:12 DP1 TP0] Decode batch. #running-req: 1, #token: 703, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.53, #queue-req: 0, 
[2025-07-28 00:56:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1048, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.26, #queue-req: 0, 
[2025-07-28 00:56:13 DP1 TP0] Decode batch. #running-req: 1, #token: 743, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.04, #queue-req: 0, 
[2025-07-28 00:56:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1088, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.83, #queue-req: 0, 
[2025-07-28 00:56:13 DP1 TP0] Decode batch. #running-req: 1, #token: 783, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.85, #queue-req: 0, 
[2025-07-28 00:56:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1128, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.67, #queue-req: 0, 
[2025-07-28 00:56:13 DP1 TP0] Decode batch. #running-req: 1, #token: 823, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.48, #queue-req: 0, 
[2025-07-28 00:56:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1168, token usage: 0.00, cuda graph: True, gen throughput (token/s): 252.14, #queue-req: 0, 
[2025-07-28 00:56:13 DP1 TP0] Decode batch. #running-req: 1, #token: 863, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.03, #queue-req: 0, 
[2025-07-28 00:56:13] INFO:     127.0.0.1:52082 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:13 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 167, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:13 DP1 TP0] Decode batch. #running-req: 1, #token: 903, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.89, #queue-req: 0, 
[2025-07-28 00:56:13 DP0 TP0] Decode batch. #running-req: 1, #token: 276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 184.29, #queue-req: 0, 
[2025-07-28 00:56:13] INFO:     127.0.0.1:52096 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:13 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 172, #cached-token: 89, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:13 DP0 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.39, #queue-req: 0, 
[2025-07-28 00:56:13 DP1 TP0] Decode batch. #running-req: 1, #token: 284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.64, #queue-req: 0, 
[2025-07-28 00:56:13 DP0 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.70, #queue-req: 0, 
[2025-07-28 00:56:13 DP1 TP0] Decode batch. #running-req: 1, #token: 324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.09, #queue-req: 0, 
[2025-07-28 00:56:14 DP0 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.23, #queue-req: 0, 
[2025-07-28 00:56:14 DP1 TP0] Decode batch. #running-req: 1, #token: 364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.99, #queue-req: 0, 
[2025-07-28 00:56:14 DP0 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.53, #queue-req: 0, 
[2025-07-28 00:56:14 DP1 TP0] Decode batch. #running-req: 1, #token: 404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.04, #queue-req: 0, 
[2025-07-28 00:56:14 DP0 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 279.66, #queue-req: 0, 
[2025-07-28 00:56:14 DP1 TP0] Decode batch. #running-req: 1, #token: 444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.10, #queue-req: 0, 
[2025-07-28 00:56:14 DP0 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.50, #queue-req: 0, 
[2025-07-28 00:56:14 DP1 TP0] Decode batch. #running-req: 1, #token: 484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.96, #queue-req: 0, 
[2025-07-28 00:56:14 DP0 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.26, #queue-req: 0, 
[2025-07-28 00:56:14 DP1 TP0] Decode batch. #running-req: 1, #token: 524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.94, #queue-req: 0, 
[2025-07-28 00:56:14 DP0 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.15, #queue-req: 0, 
[2025-07-28 00:56:14 DP1 TP0] Decode batch. #running-req: 1, #token: 564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.99, #queue-req: 0, 
[2025-07-28 00:56:14 DP1 TP0] Decode batch. #running-req: 1, #token: 604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.33, #queue-req: 0, 
[2025-07-28 00:56:14 DP0 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.88, #queue-req: 0, 
[2025-07-28 00:56:15 DP1 TP0] Decode batch. #running-req: 1, #token: 644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.66, #queue-req: 0, 
[2025-07-28 00:56:15 DP0 TP0] Decode batch. #running-req: 1, #token: 676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.12, #queue-req: 0, 
[2025-07-28 00:56:15 DP0 TP0] Decode batch. #running-req: 1, #token: 716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.59, #queue-req: 0, 
[2025-07-28 00:56:15 DP1 TP0] Decode batch. #running-req: 1, #token: 684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 282.79, #queue-req: 0, 
[2025-07-28 00:56:15 DP1 TP0] Decode batch. #running-req: 1, #token: 724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.67, #queue-req: 0, 
[2025-07-28 00:56:15 DP0 TP0] Decode batch. #running-req: 1, #token: 756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.86, #queue-req: 0, 
[2025-07-28 00:56:15 DP1 TP0] Decode batch. #running-req: 1, #token: 764, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.69, #queue-req: 0, 
[2025-07-28 00:56:15 DP0 TP0] Decode batch. #running-req: 1, #token: 796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.97, #queue-req: 0, 
[2025-07-28 00:56:15 DP1 TP0] Decode batch. #running-req: 1, #token: 804, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.11, #queue-req: 0, 
[2025-07-28 00:56:15 DP0 TP0] Decode batch. #running-req: 1, #token: 836, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.15, #queue-req: 0, 
[2025-07-28 00:56:15 DP1 TP0] Decode batch. #running-req: 1, #token: 844, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.83, #queue-req: 0, 
[2025-07-28 00:56:15 DP0 TP0] Decode batch. #running-req: 1, #token: 876, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.54, #queue-req: 0, 
[2025-07-28 00:56:15 DP1 TP0] Decode batch. #running-req: 1, #token: 884, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.34, #queue-req: 0, 
[2025-07-28 00:56:15 DP0 TP0] Decode batch. #running-req: 1, #token: 916, token usage: 0.00, cuda graph: True, gen throughput (token/s): 288.45, #queue-req: 0, 
[2025-07-28 00:56:15 DP1 TP0] Decode batch. #running-req: 1, #token: 924, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.80, #queue-req: 0, 
[2025-07-28 00:56:15 DP0 TP0] Decode batch. #running-req: 1, #token: 956, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.54, #queue-req: 0, 
[2025-07-28 00:56:16 DP1 TP0] Decode batch. #running-req: 1, #token: 964, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.80, #queue-req: 0, 
[2025-07-28 00:56:16 DP0 TP0] Decode batch. #running-req: 1, #token: 996, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.43, #queue-req: 0, 
[2025-07-28 00:56:16 DP1 TP0] Decode batch. #running-req: 1, #token: 1004, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.35, #queue-req: 0, 
[2025-07-28 00:56:16] INFO:     127.0.0.1:44506 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:16 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 219, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:56:16 DP0 TP0] Decode batch. #running-req: 1, #token: 306, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.00, #queue-req: 0, 
[2025-07-28 00:56:16] INFO:     127.0.0.1:44500 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:16 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 155, #cached-token: 90, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:16 DP0 TP0] Decode batch. #running-req: 1, #token: 347, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.49, #queue-req: 0, 
[2025-07-28 00:56:16 DP1 TP0] Decode batch. #running-req: 1, #token: 279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 129.06, #queue-req: 0, 
[2025-07-28 00:56:16 DP0 TP0] Decode batch. #running-req: 1, #token: 387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.98, #queue-req: 0, 
[2025-07-28 00:56:16 DP1 TP0] Decode batch. #running-req: 1, #token: 319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.43, #queue-req: 0, 
[2025-07-28 00:56:16 DP0 TP0] Decode batch. #running-req: 1, #token: 427, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.10, #queue-req: 0, 
[2025-07-28 00:56:16 DP1 TP0] Decode batch. #running-req: 1, #token: 359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.20, #queue-req: 0, 
[2025-07-28 00:56:16 DP0 TP0] Decode batch. #running-req: 1, #token: 467, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.65, #queue-req: 0, 
[2025-07-28 00:56:16 DP1 TP0] Decode batch. #running-req: 1, #token: 399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.21, #queue-req: 0, 
[2025-07-28 00:56:16 DP0 TP0] Decode batch. #running-req: 1, #token: 507, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.89, #queue-req: 0, 
[2025-07-28 00:56:17 DP1 TP0] Decode batch. #running-req: 1, #token: 439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.39, #queue-req: 0, 
[2025-07-28 00:56:17 DP0 TP0] Decode batch. #running-req: 1, #token: 547, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.18, #queue-req: 0, 
[2025-07-28 00:56:17 DP1 TP0] Decode batch. #running-req: 1, #token: 479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.75, #queue-req: 0, 
[2025-07-28 00:56:17 DP0 TP0] Decode batch. #running-req: 1, #token: 587, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.69, #queue-req: 0, 
[2025-07-28 00:56:17 DP1 TP0] Decode batch. #running-req: 1, #token: 519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.33, #queue-req: 0, 
[2025-07-28 00:56:17 DP0 TP0] Decode batch. #running-req: 1, #token: 627, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.30, #queue-req: 0, 
[2025-07-28 00:56:17 DP1 TP0] Decode batch. #running-req: 1, #token: 559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.03, #queue-req: 0, 
[2025-07-28 00:56:17 DP0 TP0] Decode batch. #running-req: 1, #token: 667, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.27, #queue-req: 0, 
[2025-07-28 00:56:17 DP1 TP0] Decode batch. #running-req: 1, #token: 599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.86, #queue-req: 0, 
[2025-07-28 00:56:17 DP0 TP0] Decode batch. #running-req: 1, #token: 707, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.94, #queue-req: 0, 
[2025-07-28 00:56:17 DP1 TP0] Decode batch. #running-req: 1, #token: 639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.63, #queue-req: 0, 
[2025-07-28 00:56:17 DP0 TP0] Decode batch. #running-req: 1, #token: 747, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.61, #queue-req: 0, 
[2025-07-28 00:56:17 DP1 TP0] Decode batch. #running-req: 1, #token: 679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.75, #queue-req: 0, 
[2025-07-28 00:56:17 DP0 TP0] Decode batch. #running-req: 1, #token: 787, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.72, #queue-req: 0, 
[2025-07-28 00:56:17 DP1 TP0] Decode batch. #running-req: 1, #token: 719, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.18, #queue-req: 0, 
[2025-07-28 00:56:18 DP0 TP0] Decode batch. #running-req: 1, #token: 827, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.37, #queue-req: 0, 
[2025-07-28 00:56:18 DP1 TP0] Decode batch. #running-req: 1, #token: 759, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.77, #queue-req: 0, 
[2025-07-28 00:56:18 DP0 TP0] Decode batch. #running-req: 1, #token: 867, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.77, #queue-req: 0, 
[2025-07-28 00:56:18 DP1 TP0] Decode batch. #running-req: 1, #token: 799, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.94, #queue-req: 0, 
[2025-07-28 00:56:18 DP0 TP0] Decode batch. #running-req: 1, #token: 907, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.85, #queue-req: 0, 
[2025-07-28 00:56:18 DP1 TP0] Decode batch. #running-req: 1, #token: 839, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.59, #queue-req: 0, 
[2025-07-28 00:56:18 DP1 TP0] Decode batch. #running-req: 1, #token: 879, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.28, #queue-req: 0, 
[2025-07-28 00:56:18 DP0 TP0] Decode batch. #running-req: 1, #token: 947, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.52, #queue-req: 0, 
[2025-07-28 00:56:18 DP1 TP0] Decode batch. #running-req: 1, #token: 919, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.78, #queue-req: 0, 
[2025-07-28 00:56:18 DP0 TP0] Decode batch. #running-req: 1, #token: 987, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.23, #queue-req: 0, 
[2025-07-28 00:56:18] INFO:     127.0.0.1:44510 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:18 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 175, #cached-token: 89, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:56:18 DP0 TP0] Decode batch. #running-req: 2, #token: 1228, token usage: 0.00, cuda graph: True, gen throughput (token/s): 325.74, #queue-req: 0, 
[2025-07-28 00:56:18] INFO:     127.0.0.1:44508 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:18 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 94, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:18 DP0 TP0] Decode batch. #running-req: 1, #token: 328, token usage: 0.00, cuda graph: True, gen throughput (token/s): 330.22, #queue-req: 0, 
[2025-07-28 00:56:18 DP1 TP0] Decode batch. #running-req: 1, #token: 203, token usage: 0.00, cuda graph: True, gen throughput (token/s): 102.85, #queue-req: 0, 
[2025-07-28 00:56:19 DP0 TP0] Decode batch. #running-req: 1, #token: 368, token usage: 0.00, cuda graph: True, gen throughput (token/s): 274.25, #queue-req: 0, 
[2025-07-28 00:56:19 DP1 TP0] Decode batch. #running-req: 1, #token: 243, token usage: 0.00, cuda graph: True, gen throughput (token/s): 282.26, #queue-req: 0, 
[2025-07-28 00:56:19 DP0 TP0] Decode batch. #running-req: 1, #token: 408, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.90, #queue-req: 0, 
[2025-07-28 00:56:19 DP1 TP0] Decode batch. #running-req: 1, #token: 283, token usage: 0.00, cuda graph: True, gen throughput (token/s): 279.94, #queue-req: 0, 
[2025-07-28 00:56:19 DP0 TP0] Decode batch. #running-req: 1, #token: 448, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.86, #queue-req: 0, 
[2025-07-28 00:56:19 DP1 TP0] Decode batch. #running-req: 1, #token: 323, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.33, #queue-req: 0, 
[2025-07-28 00:56:19 DP0 TP0] Decode batch. #running-req: 1, #token: 488, token usage: 0.00, cuda graph: True, gen throughput (token/s): 279.39, #queue-req: 0, 
[2025-07-28 00:56:19 DP1 TP0] Decode batch. #running-req: 1, #token: 363, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.78, #queue-req: 0, 
[2025-07-28 00:56:19 DP0 TP0] Decode batch. #running-req: 1, #token: 528, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.96, #queue-req: 0, 
[2025-07-28 00:56:19 DP1 TP0] Decode batch. #running-req: 1, #token: 403, token usage: 0.00, cuda graph: True, gen throughput (token/s): 283.62, #queue-req: 0, 
[2025-07-28 00:56:19 DP0 TP0] Decode batch. #running-req: 1, #token: 568, token usage: 0.00, cuda graph: True, gen throughput (token/s): 271.29, #queue-req: 0, 
[2025-07-28 00:56:19 DP1 TP0] Decode batch. #running-req: 1, #token: 443, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.65, #queue-req: 0, 
[2025-07-28 00:56:19 DP0 TP0] Decode batch. #running-req: 1, #token: 608, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.73, #queue-req: 0, 
[2025-07-28 00:56:19 DP1 TP0] Decode batch. #running-req: 1, #token: 483, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.02, #queue-req: 0, 
[2025-07-28 00:56:20 DP0 TP0] Decode batch. #running-req: 1, #token: 648, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.16, #queue-req: 0, 
[2025-07-28 00:56:20 DP1 TP0] Decode batch. #running-req: 1, #token: 523, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.68, #queue-req: 0, 
[2025-07-28 00:56:20 DP0 TP0] Decode batch. #running-req: 1, #token: 688, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.58, #queue-req: 0, 
[2025-07-28 00:56:20 DP1 TP0] Decode batch. #running-req: 1, #token: 563, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.60, #queue-req: 0, 
[2025-07-28 00:56:20 DP0 TP0] Decode batch. #running-req: 1, #token: 728, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.43, #queue-req: 0, 
[2025-07-28 00:56:20 DP1 TP0] Decode batch. #running-req: 1, #token: 603, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.87, #queue-req: 0, 
[2025-07-28 00:56:20 DP0 TP0] Decode batch. #running-req: 1, #token: 768, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.77, #queue-req: 0, 
[2025-07-28 00:56:20 DP1 TP0] Decode batch. #running-req: 1, #token: 643, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.52, #queue-req: 0, 
[2025-07-28 00:56:20 DP0 TP0] Decode batch. #running-req: 1, #token: 808, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.23, #queue-req: 0, 
[2025-07-28 00:56:20 DP1 TP0] Decode batch. #running-req: 1, #token: 683, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.08, #queue-req: 0, 
[2025-07-28 00:56:20 DP0 TP0] Decode batch. #running-req: 1, #token: 848, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.07, #queue-req: 0, 
[2025-07-28 00:56:20 DP1 TP0] Decode batch. #running-req: 1, #token: 723, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.12, #queue-req: 0, 
[2025-07-28 00:56:20 DP0 TP0] Decode batch. #running-req: 1, #token: 888, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.75, #queue-req: 0, 
[2025-07-28 00:56:20 DP1 TP0] Decode batch. #running-req: 1, #token: 763, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.91, #queue-req: 0, 
[2025-07-28 00:56:20 DP0 TP0] Decode batch. #running-req: 1, #token: 928, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.60, #queue-req: 0, 
[2025-07-28 00:56:21 DP1 TP0] Decode batch. #running-req: 1, #token: 803, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.69, #queue-req: 0, 
[2025-07-28 00:56:21 DP0 TP0] Decode batch. #running-req: 1, #token: 968, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.27, #queue-req: 0, 
[2025-07-28 00:56:21 DP1 TP0] Decode batch. #running-req: 1, #token: 843, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.49, #queue-req: 0, 
[2025-07-28 00:56:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1008, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.81, #queue-req: 0, 
[2025-07-28 00:56:21] INFO:     127.0.0.1:44514 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:21 DP1 TP0] Decode batch. #running-req: 1, #token: 883, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.90, #queue-req: 0, 
[2025-07-28 00:56:21 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 115, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:21 DP1 TP0] Decode batch. #running-req: 1, #token: 923, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.15, #queue-req: 0, 
[2025-07-28 00:56:21 DP0 TP0] Decode batch. #running-req: 1, #token: 226, token usage: 0.00, cuda graph: True, gen throughput (token/s): 192.17, #queue-req: 0, 
[2025-07-28 00:56:21 DP1 TP0] Decode batch. #running-req: 1, #token: 963, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.16, #queue-req: 0, 
[2025-07-28 00:56:21 DP0 TP0] Decode batch. #running-req: 1, #token: 266, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.31, #queue-req: 0, 
[2025-07-28 00:56:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1003, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.15, #queue-req: 0, 
[2025-07-28 00:56:21 DP0 TP0] Decode batch. #running-req: 1, #token: 306, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.11, #queue-req: 0, 
[2025-07-28 00:56:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1043, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.68, #queue-req: 0, 
[2025-07-28 00:56:21 DP0 TP0] Decode batch. #running-req: 1, #token: 346, token usage: 0.00, cuda graph: True, gen throughput (token/s): 288.55, #queue-req: 0, 
[2025-07-28 00:56:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1083, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.86, #queue-req: 0, 
[2025-07-28 00:56:21 DP0 TP0] Decode batch. #running-req: 1, #token: 386, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.30, #queue-req: 0, 
[2025-07-28 00:56:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1123, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.27, #queue-req: 0, 
[2025-07-28 00:56:22 DP0 TP0] Decode batch. #running-req: 1, #token: 426, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.20, #queue-req: 0, 
[2025-07-28 00:56:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1163, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.70, #queue-req: 0, 
[2025-07-28 00:56:22 DP0 TP0] Decode batch. #running-req: 1, #token: 466, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.25, #queue-req: 0, 
[2025-07-28 00:56:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1203, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.71, #queue-req: 0, 
[2025-07-28 00:56:22 DP0 TP0] Decode batch. #running-req: 1, #token: 506, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.78, #queue-req: 0, 
[2025-07-28 00:56:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1243, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.45, #queue-req: 0, 
[2025-07-28 00:56:22 DP0 TP0] Decode batch. #running-req: 1, #token: 546, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.38, #queue-req: 0, 
[2025-07-28 00:56:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1283, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.15, #queue-req: 0, 
[2025-07-28 00:56:22 DP0 TP0] Decode batch. #running-req: 1, #token: 586, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.02, #queue-req: 0, 
[2025-07-28 00:56:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1323, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.18, #queue-req: 0, 
[2025-07-28 00:56:22 DP0 TP0] Decode batch. #running-req: 1, #token: 626, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.53, #queue-req: 0, 
[2025-07-28 00:56:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1363, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.26, #queue-req: 0, 
[2025-07-28 00:56:22 DP0 TP0] Decode batch. #running-req: 1, #token: 666, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.21, #queue-req: 0, 
[2025-07-28 00:56:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1403, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.05, #queue-req: 0, 
[2025-07-28 00:56:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.14, #queue-req: 0, 
[2025-07-28 00:56:23] INFO:     127.0.0.1:44542 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:23 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 150, #cached-token: 88, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:56:23 DP1 TP0] Decode batch. #running-req: 2, #token: 1619, token usage: 0.00, cuda graph: True, gen throughput (token/s): 333.86, #queue-req: 0, 
[2025-07-28 00:56:23 DP1 TP0] Decode batch. #running-req: 2, #token: 1699, token usage: 0.00, cuda graph: True, gen throughput (token/s): 633.76, #queue-req: 0, 
[2025-07-28 00:56:23] INFO:     127.0.0.1:44528 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:23 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 148, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:23 DP1 TP0] Decode batch. #running-req: 1, #token: 341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 490.92, #queue-req: 0, 
[2025-07-28 00:56:23 DP1 TP0] Decode batch. #running-req: 1, #token: 381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.48, #queue-req: 0, 
[2025-07-28 00:56:23 DP0 TP0] Decode batch. #running-req: 1, #token: 276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.80, #queue-req: 0, 
[2025-07-28 00:56:23 DP1 TP0] Decode batch. #running-req: 1, #token: 421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.05, #queue-req: 0, 
[2025-07-28 00:56:23 DP0 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.27, #queue-req: 0, 
[2025-07-28 00:56:23 DP1 TP0] Decode batch. #running-req: 1, #token: 461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.31, #queue-req: 0, 
[2025-07-28 00:56:23 DP0 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.60, #queue-req: 0, 
[2025-07-28 00:56:23 DP1 TP0] Decode batch. #running-req: 1, #token: 501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.81, #queue-req: 0, 
[2025-07-28 00:56:24 DP0 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.68, #queue-req: 0, 
[2025-07-28 00:56:24 DP1 TP0] Decode batch. #running-req: 1, #token: 541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.65, #queue-req: 0, 
[2025-07-28 00:56:24 DP0 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.31, #queue-req: 0, 
[2025-07-28 00:56:24 DP1 TP0] Decode batch. #running-req: 1, #token: 581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.96, #queue-req: 0, 
[2025-07-28 00:56:24 DP0 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.04, #queue-req: 0, 
[2025-07-28 00:56:24 DP1 TP0] Decode batch. #running-req: 1, #token: 621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 288.53, #queue-req: 0, 
[2025-07-28 00:56:24 DP0 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.48, #queue-req: 0, 
[2025-07-28 00:56:24 DP1 TP0] Decode batch. #running-req: 1, #token: 661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 288.50, #queue-req: 0, 
[2025-07-28 00:56:24 DP0 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.17, #queue-req: 0, 
[2025-07-28 00:56:24 DP1 TP0] Decode batch. #running-req: 1, #token: 701, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.74, #queue-req: 0, 
[2025-07-28 00:56:24 DP0 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.21, #queue-req: 0, 
[2025-07-28 00:56:24 DP1 TP0] Decode batch. #running-req: 1, #token: 741, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.15, #queue-req: 0, 
[2025-07-28 00:56:24 DP0 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.96, #queue-req: 0, 
[2025-07-28 00:56:24 DP1 TP0] Decode batch. #running-req: 1, #token: 781, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.79, #queue-req: 0, 
[2025-07-28 00:56:24 DP0 TP0] Decode batch. #running-req: 1, #token: 676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.06, #queue-req: 0, 
[2025-07-28 00:56:25 DP1 TP0] Decode batch. #running-req: 1, #token: 821, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.68, #queue-req: 0, 
[2025-07-28 00:56:25 DP0 TP0] Decode batch. #running-req: 1, #token: 716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.19, #queue-req: 0, 
[2025-07-28 00:56:25 DP1 TP0] Decode batch. #running-req: 1, #token: 861, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.21, #queue-req: 0, 
[2025-07-28 00:56:25 DP0 TP0] Decode batch. #running-req: 1, #token: 756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.49, #queue-req: 0, 
[2025-07-28 00:56:25 DP1 TP0] Decode batch. #running-req: 1, #token: 901, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.79, #queue-req: 0, 
[2025-07-28 00:56:25 DP0 TP0] Decode batch. #running-req: 1, #token: 796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.75, #queue-req: 0, 
[2025-07-28 00:56:25] INFO:     127.0.0.1:42234 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:25 DP1 TP0] Decode batch. #running-req: 1, #token: 941, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.96, #queue-req: 0, 
[2025-07-28 00:56:25 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 160, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:56:25 DP1 TP0] Decode batch. #running-req: 2, #token: 1177, token usage: 0.00, cuda graph: True, gen throughput (token/s): 415.07, #queue-req: 0, 
[2025-07-28 00:56:25] INFO:     127.0.0.1:42232 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:25 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 114, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:25 DP1 TP0] Decode batch. #running-req: 1, #token: 323, token usage: 0.00, cuda graph: True, gen throughput (token/s): 363.71, #queue-req: 0, 
[2025-07-28 00:56:25 DP0 TP0] Decode batch. #running-req: 1, #token: 220, token usage: 0.00, cuda graph: True, gen throughput (token/s): 95.57, #queue-req: 0, 
[2025-07-28 00:56:25 DP1 TP0] Decode batch. #running-req: 1, #token: 363, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.70, #queue-req: 0, 
[2025-07-28 00:56:25 DP0 TP0] Decode batch. #running-req: 1, #token: 260, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.50, #queue-req: 0, 
[2025-07-28 00:56:25 DP1 TP0] Decode batch. #running-req: 1, #token: 403, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.17, #queue-req: 0, 
[2025-07-28 00:56:26 DP0 TP0] Decode batch. #running-req: 1, #token: 300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.88, #queue-req: 0, 
[2025-07-28 00:56:26 DP1 TP0] Decode batch. #running-req: 1, #token: 443, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.22, #queue-req: 0, 
[2025-07-28 00:56:26 DP0 TP0] Decode batch. #running-req: 1, #token: 340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.12, #queue-req: 0, 
[2025-07-28 00:56:26 DP1 TP0] Decode batch. #running-req: 1, #token: 483, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.77, #queue-req: 0, 
[2025-07-28 00:56:26 DP0 TP0] Decode batch. #running-req: 1, #token: 380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.05, #queue-req: 0, 
[2025-07-28 00:56:26 DP1 TP0] Decode batch. #running-req: 1, #token: 523, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.99, #queue-req: 0, 
[2025-07-28 00:56:26 DP0 TP0] Decode batch. #running-req: 1, #token: 420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.82, #queue-req: 0, 
[2025-07-28 00:56:26 DP1 TP0] Decode batch. #running-req: 1, #token: 563, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.98, #queue-req: 0, 
[2025-07-28 00:56:26 DP0 TP0] Decode batch. #running-req: 1, #token: 460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.67, #queue-req: 0, 
[2025-07-28 00:56:26 DP1 TP0] Decode batch. #running-req: 1, #token: 603, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.06, #queue-req: 0, 
[2025-07-28 00:56:26 DP0 TP0] Decode batch. #running-req: 1, #token: 500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.07, #queue-req: 0, 
[2025-07-28 00:56:26 DP1 TP0] Decode batch. #running-req: 1, #token: 643, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.94, #queue-req: 0, 
[2025-07-28 00:56:26 DP0 TP0] Decode batch. #running-req: 1, #token: 540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.65, #queue-req: 0, 
[2025-07-28 00:56:26 DP1 TP0] Decode batch. #running-req: 1, #token: 683, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.53, #queue-req: 0, 
[2025-07-28 00:56:26 DP0 TP0] Decode batch. #running-req: 1, #token: 580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.15, #queue-req: 0, 
[2025-07-28 00:56:27 DP1 TP0] Decode batch. #running-req: 1, #token: 723, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.81, #queue-req: 0, 
[2025-07-28 00:56:27 DP0 TP0] Decode batch. #running-req: 1, #token: 620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.85, #queue-req: 0, 
[2025-07-28 00:56:27 DP1 TP0] Decode batch. #running-req: 1, #token: 763, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.39, #queue-req: 0, 
[2025-07-28 00:56:27 DP0 TP0] Decode batch. #running-req: 1, #token: 660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.99, #queue-req: 0, 
[2025-07-28 00:56:27 DP1 TP0] Decode batch. #running-req: 1, #token: 803, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.19, #queue-req: 0, 
[2025-07-28 00:56:27 DP0 TP0] Decode batch. #running-req: 1, #token: 700, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.01, #queue-req: 0, 
[2025-07-28 00:56:27 DP1 TP0] Decode batch. #running-req: 1, #token: 843, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.79, #queue-req: 0, 
[2025-07-28 00:56:27 DP0 TP0] Decode batch. #running-req: 1, #token: 740, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.69, #queue-req: 0, 
[2025-07-28 00:56:27 DP1 TP0] Decode batch. #running-req: 1, #token: 883, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.31, #queue-req: 0, 
[2025-07-28 00:56:27 DP0 TP0] Decode batch. #running-req: 1, #token: 780, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.20, #queue-req: 0, 
[2025-07-28 00:56:27 DP1 TP0] Decode batch. #running-req: 1, #token: 923, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.97, #queue-req: 0, 
[2025-07-28 00:56:27 DP0 TP0] Decode batch. #running-req: 1, #token: 820, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.22, #queue-req: 0, 
[2025-07-28 00:56:27 DP1 TP0] Decode batch. #running-req: 1, #token: 963, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.42, #queue-req: 0, 
[2025-07-28 00:56:27 DP0 TP0] Decode batch. #running-req: 1, #token: 860, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.61, #queue-req: 0, 
[2025-07-28 00:56:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1003, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.17, #queue-req: 0, 
[2025-07-28 00:56:28 DP0 TP0] Decode batch. #running-req: 1, #token: 900, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.97, #queue-req: 0, 
[2025-07-28 00:56:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1043, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.33, #queue-req: 0, 
[2025-07-28 00:56:28] INFO:     127.0.0.1:42244 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:28 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 80, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:56:28 DP1 TP0] Decode batch. #running-req: 2, #token: 1189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 359.80, #queue-req: 0, 
[2025-07-28 00:56:28 DP1 TP0] Decode batch. #running-req: 2, #token: 1269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 636.05, #queue-req: 0, 
[2025-07-28 00:56:28 DP1 TP0] Decode batch. #running-req: 2, #token: 1349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 638.24, #queue-req: 0, 
[2025-07-28 00:56:28 DP1 TP0] Decode batch. #running-req: 2, #token: 1429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 632.40, #queue-req: 0, 
[2025-07-28 00:56:28 DP1 TP0] Decode batch. #running-req: 2, #token: 1509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 635.49, #queue-req: 0, 
[2025-07-28 00:56:28 DP1 TP0] Decode batch. #running-req: 2, #token: 1589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 627.36, #queue-req: 0, 
[2025-07-28 00:56:29 DP1 TP0] Decode batch. #running-req: 2, #token: 1669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 610.77, #queue-req: 0, 
[2025-07-28 00:56:29 DP1 TP0] Decode batch. #running-req: 2, #token: 1749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 584.19, #queue-req: 0, 
[2025-07-28 00:56:29 DP1 TP0] Decode batch. #running-req: 2, #token: 1829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 594.80, #queue-req: 0, 
[2025-07-28 00:56:29 DP1 TP0] Decode batch. #running-req: 2, #token: 1909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 597.64, #queue-req: 0, 
[2025-07-28 00:56:29] INFO:     127.0.0.1:42242 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:29 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 136, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:29 DP1 TP0] Decode batch. #running-req: 1, #token: 591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 403.18, #queue-req: 0, 
[2025-07-28 00:56:29 DP0 TP0] Decode batch. #running-req: 1, #token: 239, token usage: 0.00, cuda graph: True, gen throughput (token/s): 25.18, #queue-req: 0, 
[2025-07-28 00:56:29 DP1 TP0] Decode batch. #running-req: 1, #token: 631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.38, #queue-req: 0, 
[2025-07-28 00:56:29 DP0 TP0] Decode batch. #running-req: 1, #token: 279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.38, #queue-req: 0, 
[2025-07-28 00:56:29 DP1 TP0] Decode batch. #running-req: 1, #token: 671, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.04, #queue-req: 0, 
[2025-07-28 00:56:29 DP0 TP0] Decode batch. #running-req: 1, #token: 319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.43, #queue-req: 0, 
[2025-07-28 00:56:29 DP1 TP0] Decode batch. #running-req: 1, #token: 711, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.11, #queue-req: 0, 
[2025-07-28 00:56:30 DP0 TP0] Decode batch. #running-req: 1, #token: 359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.21, #queue-req: 0, 
[2025-07-28 00:56:30 DP1 TP0] Decode batch. #running-req: 1, #token: 751, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.47, #queue-req: 0, 
[2025-07-28 00:56:30 DP0 TP0] Decode batch. #running-req: 1, #token: 399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.72, #queue-req: 0, 
[2025-07-28 00:56:30 DP1 TP0] Decode batch. #running-req: 1, #token: 791, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.58, #queue-req: 0, 
[2025-07-28 00:56:30 DP0 TP0] Decode batch. #running-req: 1, #token: 439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.29, #queue-req: 0, 
[2025-07-28 00:56:30 DP1 TP0] Decode batch. #running-req: 1, #token: 831, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.19, #queue-req: 0, 
[2025-07-28 00:56:30 DP0 TP0] Decode batch. #running-req: 1, #token: 479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.14, #queue-req: 0, 
[2025-07-28 00:56:30 DP1 TP0] Decode batch. #running-req: 1, #token: 871, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.81, #queue-req: 0, 
[2025-07-28 00:56:30 DP0 TP0] Decode batch. #running-req: 1, #token: 519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.18, #queue-req: 0, 
[2025-07-28 00:56:30 DP1 TP0] Decode batch. #running-req: 1, #token: 911, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.81, #queue-req: 0, 
[2025-07-28 00:56:30 DP0 TP0] Decode batch. #running-req: 1, #token: 559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.69, #queue-req: 0, 
[2025-07-28 00:56:30 DP1 TP0] Decode batch. #running-req: 1, #token: 951, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.82, #queue-req: 0, 
[2025-07-28 00:56:30 DP0 TP0] Decode batch. #running-req: 1, #token: 599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.27, #queue-req: 0, 
[2025-07-28 00:56:30 DP1 TP0] Decode batch. #running-req: 1, #token: 991, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.41, #queue-req: 0, 
[2025-07-28 00:56:30 DP0 TP0] Decode batch. #running-req: 1, #token: 639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.42, #queue-req: 0, 
[2025-07-28 00:56:30 DP1 TP0] Decode batch. #running-req: 1, #token: 1031, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.26, #queue-req: 0, 
[2025-07-28 00:56:31 DP0 TP0] Decode batch. #running-req: 1, #token: 679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.02, #queue-req: 0, 
[2025-07-28 00:56:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1071, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.86, #queue-req: 0, 
[2025-07-28 00:56:31] INFO:     127.0.0.1:42268 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:31 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 137, #cached-token: 88, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:56:31 DP1 TP0] Decode batch. #running-req: 2, #token: 1284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 392.49, #queue-req: 0, 
[2025-07-28 00:56:31 DP1 TP0] Decode batch. #running-req: 2, #token: 1364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 645.83, #queue-req: 0, 
[2025-07-28 00:56:31 DP1 TP0] Decode batch. #running-req: 2, #token: 1444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 634.14, #queue-req: 0, 
[2025-07-28 00:56:31 DP1 TP0] Decode batch. #running-req: 2, #token: 1524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 641.84, #queue-req: 0, 
[2025-07-28 00:56:31 DP1 TP0] Decode batch. #running-req: 2, #token: 1604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 608.14, #queue-req: 0, 
[2025-07-28 00:56:31 DP1 TP0] Decode batch. #running-req: 2, #token: 1684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 607.75, #queue-req: 0, 
[2025-07-28 00:56:31] INFO:     127.0.0.1:42252 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:31 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 85, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:32 DP1 TP0] Decode batch. #running-req: 1, #token: 498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 351.94, #queue-req: 0, 
[2025-07-28 00:56:32 DP0 TP0] Decode batch. #running-req: 1, #token: 198, token usage: 0.00, cuda graph: True, gen throughput (token/s): 38.04, #queue-req: 0, 
[2025-07-28 00:56:32 DP1 TP0] Decode batch. #running-req: 1, #token: 538, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.87, #queue-req: 0, 
[2025-07-28 00:56:32 DP0 TP0] Decode batch. #running-req: 1, #token: 238, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.95, #queue-req: 0, 
[2025-07-28 00:56:32 DP1 TP0] Decode batch. #running-req: 1, #token: 578, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.28, #queue-req: 0, 
[2025-07-28 00:56:32 DP0 TP0] Decode batch. #running-req: 1, #token: 278, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.22, #queue-req: 0, 
[2025-07-28 00:56:32 DP1 TP0] Decode batch. #running-req: 1, #token: 618, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.53, #queue-req: 0, 
[2025-07-28 00:56:32 DP0 TP0] Decode batch. #running-req: 1, #token: 318, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.57, #queue-req: 0, 
[2025-07-28 00:56:32 DP1 TP0] Decode batch. #running-req: 1, #token: 658, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.32, #queue-req: 0, 
[2025-07-28 00:56:32 DP0 TP0] Decode batch. #running-req: 1, #token: 358, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.49, #queue-req: 0, 
[2025-07-28 00:56:32 DP1 TP0] Decode batch. #running-req: 1, #token: 698, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.61, #queue-req: 0, 
[2025-07-28 00:56:32 DP0 TP0] Decode batch. #running-req: 1, #token: 398, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.96, #queue-req: 0, 
[2025-07-28 00:56:32 DP1 TP0] Decode batch. #running-req: 1, #token: 738, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.50, #queue-req: 0, 
[2025-07-28 00:56:32 DP0 TP0] Decode batch. #running-req: 1, #token: 438, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.92, #queue-req: 0, 
[2025-07-28 00:56:32 DP1 TP0] Decode batch. #running-req: 1, #token: 778, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.36, #queue-req: 0, 
[2025-07-28 00:56:33 DP0 TP0] Decode batch. #running-req: 1, #token: 478, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.12, #queue-req: 0, 
[2025-07-28 00:56:33 DP1 TP0] Decode batch. #running-req: 1, #token: 818, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.88, #queue-req: 0, 
[2025-07-28 00:56:33 DP0 TP0] Decode batch. #running-req: 1, #token: 518, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.12, #queue-req: 0, 
[2025-07-28 00:56:33 DP1 TP0] Decode batch. #running-req: 1, #token: 858, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.76, #queue-req: 0, 
[2025-07-28 00:56:33 DP0 TP0] Decode batch. #running-req: 1, #token: 558, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.85, #queue-req: 0, 
[2025-07-28 00:56:33 DP1 TP0] Decode batch. #running-req: 1, #token: 898, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.45, #queue-req: 0, 
[2025-07-28 00:56:33 DP0 TP0] Decode batch. #running-req: 1, #token: 598, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.30, #queue-req: 0, 
[2025-07-28 00:56:33 DP1 TP0] Decode batch. #running-req: 1, #token: 938, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.01, #queue-req: 0, 
[2025-07-28 00:56:33 DP0 TP0] Decode batch. #running-req: 1, #token: 638, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.14, #queue-req: 0, 
[2025-07-28 00:56:33 DP1 TP0] Decode batch. #running-req: 1, #token: 978, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.80, #queue-req: 0, 
[2025-07-28 00:56:33 DP0 TP0] Decode batch. #running-req: 1, #token: 678, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.40, #queue-req: 0, 
[2025-07-28 00:56:33] INFO:     127.0.0.1:42284 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:33 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 157, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:33 DP0 TP0] Decode batch. #running-req: 1, #token: 718, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.96, #queue-req: 0, 
[2025-07-28 00:56:33 DP1 TP0] Decode batch. #running-req: 1, #token: 263, token usage: 0.00, cuda graph: True, gen throughput (token/s): 195.61, #queue-req: 0, 
[2025-07-28 00:56:33 DP0 TP0] Decode batch. #running-req: 1, #token: 758, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.47, #queue-req: 0, 
[2025-07-28 00:56:33 DP1 TP0] Decode batch. #running-req: 1, #token: 303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 329.52, #queue-req: 0, 
[2025-07-28 00:56:34 DP0 TP0] Decode batch. #running-req: 1, #token: 798, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.58, #queue-req: 0, 
[2025-07-28 00:56:34 DP1 TP0] Decode batch. #running-req: 1, #token: 343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.68, #queue-req: 0, 
[2025-07-28 00:56:34 DP0 TP0] Decode batch. #running-req: 1, #token: 838, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.88, #queue-req: 0, 
[2025-07-28 00:56:34 DP1 TP0] Decode batch. #running-req: 1, #token: 383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.47, #queue-req: 0, 
[2025-07-28 00:56:34 DP0 TP0] Decode batch. #running-req: 1, #token: 878, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.78, #queue-req: 0, 
[2025-07-28 00:56:34 DP1 TP0] Decode batch. #running-req: 1, #token: 423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.31, #queue-req: 0, 
[2025-07-28 00:56:34 DP0 TP0] Decode batch. #running-req: 1, #token: 918, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.86, #queue-req: 0, 
[2025-07-28 00:56:34 DP1 TP0] Decode batch. #running-req: 1, #token: 463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.22, #queue-req: 0, 
[2025-07-28 00:56:34 DP0 TP0] Decode batch. #running-req: 1, #token: 958, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.61, #queue-req: 0, 
[2025-07-28 00:56:34 DP1 TP0] Decode batch. #running-req: 1, #token: 503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.35, #queue-req: 0, 
[2025-07-28 00:56:34 DP0 TP0] Decode batch. #running-req: 1, #token: 998, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.85, #queue-req: 0, 
[2025-07-28 00:56:34 DP1 TP0] Decode batch. #running-req: 1, #token: 543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.80, #queue-req: 0, 
[2025-07-28 00:56:34 DP0 TP0] Decode batch. #running-req: 1, #token: 1038, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.06, #queue-req: 0, 
[2025-07-28 00:56:34 DP1 TP0] Decode batch. #running-req: 1, #token: 583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.65, #queue-req: 0, 
[2025-07-28 00:56:34 DP0 TP0] Decode batch. #running-req: 1, #token: 1078, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.04, #queue-req: 0, 
[2025-07-28 00:56:35 DP1 TP0] Decode batch. #running-req: 1, #token: 623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.85, #queue-req: 0, 
[2025-07-28 00:56:35 DP0 TP0] Decode batch. #running-req: 1, #token: 1118, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.68, #queue-req: 0, 
[2025-07-28 00:56:35 DP1 TP0] Decode batch. #running-req: 1, #token: 663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.50, #queue-req: 0, 
[2025-07-28 00:56:35] INFO:     127.0.0.1:42296 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:35 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 150, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:35 DP1 TP0] Decode batch. #running-req: 1, #token: 703, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.47, #queue-req: 0, 
[2025-07-28 00:56:35 DP0 TP0] Decode batch. #running-req: 1, #token: 264, token usage: 0.00, cuda graph: True, gen throughput (token/s): 195.81, #queue-req: 0, 
[2025-07-28 00:56:35 DP1 TP0] Decode batch. #running-req: 1, #token: 743, token usage: 0.00, cuda graph: True, gen throughput (token/s): 288.78, #queue-req: 0, 
[2025-07-28 00:56:35 DP0 TP0] Decode batch. #running-req: 1, #token: 304, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.85, #queue-req: 0, 
[2025-07-28 00:56:35 DP0 TP0] Decode batch. #running-req: 1, #token: 344, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.05, #queue-req: 0, 
[2025-07-28 00:56:35 DP1 TP0] Decode batch. #running-req: 1, #token: 783, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.44, #queue-req: 0, 
[2025-07-28 00:56:35 DP0 TP0] Decode batch. #running-req: 1, #token: 384, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.06, #queue-req: 0, 
[2025-07-28 00:56:35 DP1 TP0] Decode batch. #running-req: 1, #token: 823, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.73, #queue-req: 0, 
[2025-07-28 00:56:35 DP0 TP0] Decode batch. #running-req: 1, #token: 424, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.62, #queue-req: 0, 
[2025-07-28 00:56:35 DP1 TP0] Decode batch. #running-req: 1, #token: 863, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.86, #queue-req: 0, 
[2025-07-28 00:56:35 DP0 TP0] Decode batch. #running-req: 1, #token: 464, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.93, #queue-req: 0, 
[2025-07-28 00:56:35 DP1 TP0] Decode batch. #running-req: 1, #token: 903, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.27, #queue-req: 0, 
[2025-07-28 00:56:36 DP0 TP0] Decode batch. #running-req: 1, #token: 504, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.48, #queue-req: 0, 
[2025-07-28 00:56:36 DP1 TP0] Decode batch. #running-req: 1, #token: 943, token usage: 0.00, cuda graph: True, gen throughput (token/s): 283.32, #queue-req: 0, 
[2025-07-28 00:56:36 DP0 TP0] Decode batch. #running-req: 1, #token: 544, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.52, #queue-req: 0, 
[2025-07-28 00:56:36 DP1 TP0] Decode batch. #running-req: 1, #token: 983, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.23, #queue-req: 0, 
[2025-07-28 00:56:36 DP0 TP0] Decode batch. #running-req: 1, #token: 584, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.50, #queue-req: 0, 
[2025-07-28 00:56:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1023, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.63, #queue-req: 0, 
[2025-07-28 00:56:36 DP0 TP0] Decode batch. #running-req: 1, #token: 624, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.68, #queue-req: 0, 
[2025-07-28 00:56:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1063, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.83, #queue-req: 0, 
[2025-07-28 00:56:36 DP0 TP0] Decode batch. #running-req: 1, #token: 664, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.47, #queue-req: 0, 
[2025-07-28 00:56:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1103, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.94, #queue-req: 0, 
[2025-07-28 00:56:36 DP0 TP0] Decode batch. #running-req: 1, #token: 704, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.91, #queue-req: 0, 
[2025-07-28 00:56:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1143, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.96, #queue-req: 0, 
[2025-07-28 00:56:36] INFO:     127.0.0.1:46156 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:36 DP0 TP0] Decode batch. #running-req: 1, #token: 744, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.25, #queue-req: 0, 
[2025-07-28 00:56:36 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 111, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:37 DP0 TP0] Decode batch. #running-req: 1, #token: 784, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.94, #queue-req: 0, 
[2025-07-28 00:56:37 DP1 TP0] Decode batch. #running-req: 1, #token: 216, token usage: 0.00, cuda graph: True, gen throughput (token/s): 194.19, #queue-req: 0, 
[2025-07-28 00:56:37 DP1 TP0] Decode batch. #running-req: 1, #token: 256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 321.40, #queue-req: 0, 
[2025-07-28 00:56:37 DP0 TP0] Decode batch. #running-req: 1, #token: 824, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.79, #queue-req: 0, 
[2025-07-28 00:56:37 DP1 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.20, #queue-req: 0, 
[2025-07-28 00:56:37 DP0 TP0] Decode batch. #running-req: 1, #token: 864, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.26, #queue-req: 0, 
[2025-07-28 00:56:37 DP1 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.85, #queue-req: 0, 
[2025-07-28 00:56:37 DP0 TP0] Decode batch. #running-req: 1, #token: 904, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.27, #queue-req: 0, 
[2025-07-28 00:56:37 DP1 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.15, #queue-req: 0, 
[2025-07-28 00:56:37 DP0 TP0] Decode batch. #running-req: 1, #token: 944, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.26, #queue-req: 0, 
[2025-07-28 00:56:37 DP1 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.43, #queue-req: 0, 
[2025-07-28 00:56:37 DP0 TP0] Decode batch. #running-req: 1, #token: 984, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.72, #queue-req: 0, 
[2025-07-28 00:56:37 DP1 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.73, #queue-req: 0, 
[2025-07-28 00:56:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1024, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.95, #queue-req: 0, 
[2025-07-28 00:56:37 DP1 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.72, #queue-req: 0, 
[2025-07-28 00:56:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1064, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.19, #queue-req: 0, 
[2025-07-28 00:56:38] INFO:     127.0.0.1:46168 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:38 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 95, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:38 DP1 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.90, #queue-req: 0, 
[2025-07-28 00:56:38 DP0 TP0] Decode batch. #running-req: 1, #token: 195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 192.38, #queue-req: 0, 
[2025-07-28 00:56:38 DP1 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.41, #queue-req: 0, 
[2025-07-28 00:56:38 DP0 TP0] Decode batch. #running-req: 1, #token: 235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 321.64, #queue-req: 0, 
[2025-07-28 00:56:38 DP1 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.27, #queue-req: 0, 
[2025-07-28 00:56:38 DP0 TP0] Decode batch. #running-req: 1, #token: 275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.79, #queue-req: 0, 
[2025-07-28 00:56:38 DP1 TP0] Decode batch. #running-req: 1, #token: 656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.46, #queue-req: 0, 
[2025-07-28 00:56:38 DP0 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.92, #queue-req: 0, 
[2025-07-28 00:56:38 DP1 TP0] Decode batch. #running-req: 1, #token: 696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.44, #queue-req: 0, 
[2025-07-28 00:56:38 DP0 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.43, #queue-req: 0, 
[2025-07-28 00:56:38 DP1 TP0] Decode batch. #running-req: 1, #token: 736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.44, #queue-req: 0, 
[2025-07-28 00:56:38 DP0 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.81, #queue-req: 0, 
[2025-07-28 00:56:38 DP1 TP0] Decode batch. #running-req: 1, #token: 776, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.45, #queue-req: 0, 
[2025-07-28 00:56:38 DP0 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.13, #queue-req: 0, 
[2025-07-28 00:56:38 DP1 TP0] Decode batch. #running-req: 1, #token: 816, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.95, #queue-req: 0, 
[2025-07-28 00:56:39 DP0 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.50, #queue-req: 0, 
[2025-07-28 00:56:39 DP1 TP0] Decode batch. #running-req: 1, #token: 856, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.29, #queue-req: 0, 
[2025-07-28 00:56:39 DP0 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.99, #queue-req: 0, 
[2025-07-28 00:56:39 DP1 TP0] Decode batch. #running-req: 1, #token: 896, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.38, #queue-req: 0, 
[2025-07-28 00:56:39 DP0 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.42, #queue-req: 0, 
[2025-07-28 00:56:39 DP1 TP0] Decode batch. #running-req: 1, #token: 936, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.88, #queue-req: 0, 
[2025-07-28 00:56:39 DP0 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.94, #queue-req: 0, 
[2025-07-28 00:56:39 DP1 TP0] Decode batch. #running-req: 1, #token: 976, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.07, #queue-req: 0, 
[2025-07-28 00:56:39 DP0 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.39, #queue-req: 0, 
[2025-07-28 00:56:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1016, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.54, #queue-req: 0, 
[2025-07-28 00:56:39 DP0 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.39, #queue-req: 0, 
[2025-07-28 00:56:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1056, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.12, #queue-req: 0, 
[2025-07-28 00:56:39 DP0 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.48, #queue-req: 0, 
[2025-07-28 00:56:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1096, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.91, #queue-req: 0, 
[2025-07-28 00:56:39 DP0 TP0] Decode batch. #running-req: 1, #token: 755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.30, #queue-req: 0, 
[2025-07-28 00:56:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1136, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.92, #queue-req: 0, 
[2025-07-28 00:56:40 DP0 TP0] Decode batch. #running-req: 1, #token: 795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.19, #queue-req: 0, 
[2025-07-28 00:56:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1176, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.52, #queue-req: 0, 
[2025-07-28 00:56:40] INFO:     127.0.0.1:46178 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:40 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 89, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:56:40] INFO:     127.0.0.1:46170 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:40 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 115, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:40 DP1 TP0] Decode batch. #running-req: 1, #token: 198, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.04, #queue-req: 0, 
[2025-07-28 00:56:40 DP0 TP0] Decode batch. #running-req: 1, #token: 220, token usage: 0.00, cuda graph: True, gen throughput (token/s): 127.65, #queue-req: 0, 
[2025-07-28 00:56:40 DP1 TP0] Decode batch. #running-req: 1, #token: 238, token usage: 0.00, cuda graph: True, gen throughput (token/s): 333.16, #queue-req: 0, 
[2025-07-28 00:56:40 DP0 TP0] Decode batch. #running-req: 1, #token: 260, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.52, #queue-req: 0, 
[2025-07-28 00:56:40 DP1 TP0] Decode batch. #running-req: 1, #token: 278, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.99, #queue-req: 0, 
[2025-07-28 00:56:40 DP0 TP0] Decode batch. #running-req: 1, #token: 300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.11, #queue-req: 0, 
[2025-07-28 00:56:40 DP1 TP0] Decode batch. #running-req: 1, #token: 318, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.66, #queue-req: 0, 
[2025-07-28 00:56:40 DP0 TP0] Decode batch. #running-req: 1, #token: 340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.79, #queue-req: 0, 
[2025-07-28 00:56:40 DP1 TP0] Decode batch. #running-req: 1, #token: 358, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.97, #queue-req: 0, 
[2025-07-28 00:56:40 DP0 TP0] Decode batch. #running-req: 1, #token: 380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.91, #queue-req: 0, 
[2025-07-28 00:56:40 DP1 TP0] Decode batch. #running-req: 1, #token: 398, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.98, #queue-req: 0, 
[2025-07-28 00:56:41 DP0 TP0] Decode batch. #running-req: 1, #token: 420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.41, #queue-req: 0, 
[2025-07-28 00:56:41 DP1 TP0] Decode batch. #running-req: 1, #token: 438, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.00, #queue-req: 0, 
[2025-07-28 00:56:41 DP0 TP0] Decode batch. #running-req: 1, #token: 460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.61, #queue-req: 0, 
[2025-07-28 00:56:41 DP1 TP0] Decode batch. #running-req: 1, #token: 478, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.49, #queue-req: 0, 
[2025-07-28 00:56:41 DP0 TP0] Decode batch. #running-req: 1, #token: 500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.81, #queue-req: 0, 
[2025-07-28 00:56:41 DP1 TP0] Decode batch. #running-req: 1, #token: 518, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.71, #queue-req: 0, 
[2025-07-28 00:56:41 DP0 TP0] Decode batch. #running-req: 1, #token: 540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.04, #queue-req: 0, 
[2025-07-28 00:56:41 DP1 TP0] Decode batch. #running-req: 1, #token: 558, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.24, #queue-req: 0, 
[2025-07-28 00:56:41 DP0 TP0] Decode batch. #running-req: 1, #token: 580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.45, #queue-req: 0, 
[2025-07-28 00:56:41 DP1 TP0] Decode batch. #running-req: 1, #token: 598, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.87, #queue-req: 0, 
[2025-07-28 00:56:41 DP0 TP0] Decode batch. #running-req: 1, #token: 620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.96, #queue-req: 0, 
[2025-07-28 00:56:41 DP1 TP0] Decode batch. #running-req: 1, #token: 638, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.56, #queue-req: 0, 
[2025-07-28 00:56:41 DP0 TP0] Decode batch. #running-req: 1, #token: 660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.16, #queue-req: 0, 
[2025-07-28 00:56:41 DP1 TP0] Decode batch. #running-req: 1, #token: 678, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.31, #queue-req: 0, 
[2025-07-28 00:56:41 DP0 TP0] Decode batch. #running-req: 1, #token: 700, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.66, #queue-req: 0, 
[2025-07-28 00:56:42 DP1 TP0] Decode batch. #running-req: 1, #token: 718, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.23, #queue-req: 0, 
[2025-07-28 00:56:42 DP0 TP0] Decode batch. #running-req: 1, #token: 740, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.64, #queue-req: 0, 
[2025-07-28 00:56:42 DP1 TP0] Decode batch. #running-req: 1, #token: 758, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.15, #queue-req: 0, 
[2025-07-28 00:56:42 DP0 TP0] Decode batch. #running-req: 1, #token: 780, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.88, #queue-req: 0, 
[2025-07-28 00:56:42 DP1 TP0] Decode batch. #running-req: 1, #token: 798, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.28, #queue-req: 0, 
[2025-07-28 00:56:42 DP0 TP0] Decode batch. #running-req: 1, #token: 820, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.81, #queue-req: 0, 
[2025-07-28 00:56:42 DP1 TP0] Decode batch. #running-req: 1, #token: 838, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.84, #queue-req: 0, 
[2025-07-28 00:56:42 DP0 TP0] Decode batch. #running-req: 1, #token: 860, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.13, #queue-req: 0, 
[2025-07-28 00:56:42 DP1 TP0] Decode batch. #running-req: 1, #token: 878, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.28, #queue-req: 0, 
[2025-07-28 00:56:42 DP0 TP0] Decode batch. #running-req: 1, #token: 900, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.11, #queue-req: 0, 
[2025-07-28 00:56:42 DP1 TP0] Decode batch. #running-req: 1, #token: 918, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.21, #queue-req: 0, 
[2025-07-28 00:56:42 DP0 TP0] Decode batch. #running-req: 1, #token: 940, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.15, #queue-req: 0, 
[2025-07-28 00:56:42 DP1 TP0] Decode batch. #running-req: 1, #token: 958, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.37, #queue-req: 0, 
[2025-07-28 00:56:42] INFO:     127.0.0.1:46180 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:42 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 164, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:42 DP0 TP0] Decode batch. #running-req: 1, #token: 980, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.11, #queue-req: 0, 
[2025-07-28 00:56:42 DP1 TP0] Decode batch. #running-req: 1, #token: 288, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.30, #queue-req: 0, 
[2025-07-28 00:56:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1020, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.88, #queue-req: 0, 
[2025-07-28 00:56:43 DP1 TP0] Decode batch. #running-req: 1, #token: 328, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.42, #queue-req: 0, 
[2025-07-28 00:56:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1060, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.28, #queue-req: 0, 
[2025-07-28 00:56:43 DP1 TP0] Decode batch. #running-req: 1, #token: 368, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.55, #queue-req: 0, 
[2025-07-28 00:56:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1100, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.44, #queue-req: 0, 
[2025-07-28 00:56:43 DP1 TP0] Decode batch. #running-req: 1, #token: 408, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.86, #queue-req: 0, 
[2025-07-28 00:56:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1140, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.66, #queue-req: 0, 
[2025-07-28 00:56:43 DP1 TP0] Decode batch. #running-req: 1, #token: 448, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.77, #queue-req: 0, 
[2025-07-28 00:56:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1180, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.51, #queue-req: 0, 
[2025-07-28 00:56:43 DP1 TP0] Decode batch. #running-req: 1, #token: 488, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.10, #queue-req: 0, 
[2025-07-28 00:56:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1220, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.98, #queue-req: 0, 
[2025-07-28 00:56:43 DP1 TP0] Decode batch. #running-req: 1, #token: 528, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.97, #queue-req: 0, 
[2025-07-28 00:56:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1260, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.92, #queue-req: 0, 
[2025-07-28 00:56:43 DP1 TP0] Decode batch. #running-req: 1, #token: 568, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.32, #queue-req: 0, 
[2025-07-28 00:56:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.52, #queue-req: 0, 
[2025-07-28 00:56:44 DP1 TP0] Decode batch. #running-req: 1, #token: 608, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.48, #queue-req: 0, 
[2025-07-28 00:56:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.27, #queue-req: 0, 
[2025-07-28 00:56:44 DP1 TP0] Decode batch. #running-req: 1, #token: 648, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.68, #queue-req: 0, 
[2025-07-28 00:56:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.24, #queue-req: 0, 
[2025-07-28 00:56:44 DP1 TP0] Decode batch. #running-req: 1, #token: 688, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.29, #queue-req: 0, 
[2025-07-28 00:56:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.96, #queue-req: 0, 
[2025-07-28 00:56:44 DP1 TP0] Decode batch. #running-req: 1, #token: 728, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.27, #queue-req: 0, 
[2025-07-28 00:56:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.98, #queue-req: 0, 
[2025-07-28 00:56:44 DP1 TP0] Decode batch. #running-req: 1, #token: 768, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.94, #queue-req: 0, 
[2025-07-28 00:56:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.39, #queue-req: 0, 
[2025-07-28 00:56:44 DP1 TP0] Decode batch. #running-req: 1, #token: 808, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.91, #queue-req: 0, 
[2025-07-28 00:56:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.62, #queue-req: 0, 
[2025-07-28 00:56:44 DP1 TP0] Decode batch. #running-req: 1, #token: 848, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.42, #queue-req: 0, 
[2025-07-28 00:56:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.53, #queue-req: 0, 
[2025-07-28 00:56:44 DP1 TP0] Decode batch. #running-req: 1, #token: 888, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.93, #queue-req: 0, 
[2025-07-28 00:56:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.89, #queue-req: 0, 
[2025-07-28 00:56:45 DP1 TP0] Decode batch. #running-req: 1, #token: 928, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.38, #queue-req: 0, 
[2025-07-28 00:56:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.67, #queue-req: 0, 
[2025-07-28 00:56:45 DP1 TP0] Decode batch. #running-req: 1, #token: 968, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.08, #queue-req: 0, 
[2025-07-28 00:56:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1700, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.43, #queue-req: 0, 
[2025-07-28 00:56:45 DP1 TP0] Decode batch. #running-req: 1, #token: 1008, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.47, #queue-req: 0, 
[2025-07-28 00:56:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1740, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.67, #queue-req: 0, 
[2025-07-28 00:56:45 DP1 TP0] Decode batch. #running-req: 1, #token: 1048, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.98, #queue-req: 0, 
[2025-07-28 00:56:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1780, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.12, #queue-req: 0, 
[2025-07-28 00:56:45 DP1 TP0] Decode batch. #running-req: 1, #token: 1088, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.47, #queue-req: 0, 
[2025-07-28 00:56:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1820, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.02, #queue-req: 0, 
[2025-07-28 00:56:45 DP1 TP0] Decode batch. #running-req: 1, #token: 1128, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.69, #queue-req: 0, 
[2025-07-28 00:56:45] INFO:     127.0.0.1:46182 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:45 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 143, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:45 DP1 TP0] Decode batch. #running-req: 1, #token: 1168, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.64, #queue-req: 0, 
[2025-07-28 00:56:45 DP0 TP0] Decode batch. #running-req: 1, #token: 230, token usage: 0.00, cuda graph: True, gen throughput (token/s): 190.99, #queue-req: 0, 
[2025-07-28 00:56:45] INFO:     127.0.0.1:38422 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:45 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 117, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:45 DP0 TP0] Decode batch. #running-req: 1, #token: 270, token usage: 0.00, cuda graph: True, gen throughput (token/s): 320.92, #queue-req: 0, 
[2025-07-28 00:56:46 DP1 TP0] Decode batch. #running-req: 1, #token: 227, token usage: 0.00, cuda graph: True, gen throughput (token/s): 183.57, #queue-req: 0, 
[2025-07-28 00:56:46 DP0 TP0] Decode batch. #running-req: 1, #token: 310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.37, #queue-req: 0, 
[2025-07-28 00:56:46 DP1 TP0] Decode batch. #running-req: 1, #token: 267, token usage: 0.00, cuda graph: True, gen throughput (token/s): 339.19, #queue-req: 0, 
[2025-07-28 00:56:46 DP0 TP0] Decode batch. #running-req: 1, #token: 350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.90, #queue-req: 0, 
[2025-07-28 00:56:46 DP1 TP0] Decode batch. #running-req: 1, #token: 307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 336.14, #queue-req: 0, 
[2025-07-28 00:56:46 DP0 TP0] Decode batch. #running-req: 1, #token: 390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.53, #queue-req: 0, 
[2025-07-28 00:56:46 DP1 TP0] Decode batch. #running-req: 1, #token: 347, token usage: 0.00, cuda graph: True, gen throughput (token/s): 335.88, #queue-req: 0, 
[2025-07-28 00:56:46 DP1 TP0] Decode batch. #running-req: 1, #token: 387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 332.13, #queue-req: 0, 
[2025-07-28 00:56:46 DP0 TP0] Decode batch. #running-req: 1, #token: 430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.34, #queue-req: 0, 
[2025-07-28 00:56:46 DP1 TP0] Decode batch. #running-req: 1, #token: 427, token usage: 0.00, cuda graph: True, gen throughput (token/s): 330.19, #queue-req: 0, 
[2025-07-28 00:56:46 DP0 TP0] Decode batch. #running-req: 1, #token: 470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.53, #queue-req: 0, 
[2025-07-28 00:56:46 DP1 TP0] Decode batch. #running-req: 1, #token: 467, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.43, #queue-req: 0, 
[2025-07-28 00:56:46 DP0 TP0] Decode batch. #running-req: 1, #token: 510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.05, #queue-req: 0, 
[2025-07-28 00:56:46 DP1 TP0] Decode batch. #running-req: 1, #token: 507, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.60, #queue-req: 0, 
[2025-07-28 00:56:46 DP0 TP0] Decode batch. #running-req: 1, #token: 550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.91, #queue-req: 0, 
[2025-07-28 00:56:46 DP1 TP0] Decode batch. #running-req: 1, #token: 547, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.25, #queue-req: 0, 
[2025-07-28 00:56:47 DP0 TP0] Decode batch. #running-req: 1, #token: 590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.35, #queue-req: 0, 
[2025-07-28 00:56:47 DP1 TP0] Decode batch. #running-req: 1, #token: 587, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.42, #queue-req: 0, 
[2025-07-28 00:56:47 DP0 TP0] Decode batch. #running-req: 1, #token: 630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.14, #queue-req: 0, 
[2025-07-28 00:56:47 DP1 TP0] Decode batch. #running-req: 1, #token: 627, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.75, #queue-req: 0, 
[2025-07-28 00:56:47 DP0 TP0] Decode batch. #running-req: 1, #token: 670, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.48, #queue-req: 0, 
[2025-07-28 00:56:47 DP1 TP0] Decode batch. #running-req: 1, #token: 667, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.02, #queue-req: 0, 
[2025-07-28 00:56:47 DP0 TP0] Decode batch. #running-req: 1, #token: 710, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.48, #queue-req: 0, 
[2025-07-28 00:56:47 DP1 TP0] Decode batch. #running-req: 1, #token: 707, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.56, #queue-req: 0, 
[2025-07-28 00:56:47 DP0 TP0] Decode batch. #running-req: 1, #token: 750, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.18, #queue-req: 0, 
[2025-07-28 00:56:47 DP1 TP0] Decode batch. #running-req: 1, #token: 747, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.92, #queue-req: 0, 
[2025-07-28 00:56:47 DP0 TP0] Decode batch. #running-req: 1, #token: 790, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.43, #queue-req: 0, 
[2025-07-28 00:56:47 DP1 TP0] Decode batch. #running-req: 1, #token: 787, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.99, #queue-req: 0, 
[2025-07-28 00:56:47 DP0 TP0] Decode batch. #running-req: 1, #token: 830, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.09, #queue-req: 0, 
[2025-07-28 00:56:47 DP1 TP0] Decode batch. #running-req: 1, #token: 827, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.88, #queue-req: 0, 
[2025-07-28 00:56:47 DP0 TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.20, #queue-req: 0, 
[2025-07-28 00:56:47] INFO:     127.0.0.1:38432 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:47 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 125, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:48 DP1 TP0] Decode batch. #running-req: 1, #token: 867, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.34, #queue-req: 0, 
[2025-07-28 00:56:48 DP0 TP0] Decode batch. #running-req: 1, #token: 252, token usage: 0.00, cuda graph: True, gen throughput (token/s): 190.26, #queue-req: 0, 
[2025-07-28 00:56:48 DP1 TP0] Decode batch. #running-req: 1, #token: 907, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.60, #queue-req: 0, 
[2025-07-28 00:56:48 DP0 TP0] Decode batch. #running-req: 1, #token: 292, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.10, #queue-req: 0, 
[2025-07-28 00:56:48 DP1 TP0] Decode batch. #running-req: 1, #token: 947, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.06, #queue-req: 0, 
[2025-07-28 00:56:48 DP0 TP0] Decode batch. #running-req: 1, #token: 332, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.70, #queue-req: 0, 
[2025-07-28 00:56:48 DP1 TP0] Decode batch. #running-req: 1, #token: 987, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.40, #queue-req: 0, 
[2025-07-28 00:56:48] INFO:     127.0.0.1:38440 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:48 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 196, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:48 DP0 TP0] Decode batch. #running-req: 1, #token: 372, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.28, #queue-req: 0, 
[2025-07-28 00:56:48 DP1 TP0] Decode batch. #running-req: 1, #token: 294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 188.73, #queue-req: 0, 
[2025-07-28 00:56:48 DP0 TP0] Decode batch. #running-req: 1, #token: 412, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.68, #queue-req: 0, 
[2025-07-28 00:56:48 DP1 TP0] Decode batch. #running-req: 1, #token: 334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 322.87, #queue-req: 0, 
[2025-07-28 00:56:48 DP0 TP0] Decode batch. #running-req: 1, #token: 452, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.75, #queue-req: 0, 
[2025-07-28 00:56:48 DP1 TP0] Decode batch. #running-req: 1, #token: 374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.24, #queue-req: 0, 
[2025-07-28 00:56:48 DP0 TP0] Decode batch. #running-req: 1, #token: 492, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.82, #queue-req: 0, 
[2025-07-28 00:56:48 DP1 TP0] Decode batch. #running-req: 1, #token: 414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.49, #queue-req: 0, 
[2025-07-28 00:56:49 DP0 TP0] Decode batch. #running-req: 1, #token: 532, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.89, #queue-req: 0, 
[2025-07-28 00:56:49 DP1 TP0] Decode batch. #running-req: 1, #token: 454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.05, #queue-req: 0, 
[2025-07-28 00:56:49 DP0 TP0] Decode batch. #running-req: 1, #token: 572, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.61, #queue-req: 0, 
[2025-07-28 00:56:49 DP1 TP0] Decode batch. #running-req: 1, #token: 494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.64, #queue-req: 0, 
[2025-07-28 00:56:49 DP0 TP0] Decode batch. #running-req: 1, #token: 612, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.81, #queue-req: 0, 
[2025-07-28 00:56:49 DP1 TP0] Decode batch. #running-req: 1, #token: 534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.69, #queue-req: 0, 
[2025-07-28 00:56:49 DP0 TP0] Decode batch. #running-req: 1, #token: 652, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.31, #queue-req: 0, 
[2025-07-28 00:56:49 DP1 TP0] Decode batch. #running-req: 1, #token: 574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.70, #queue-req: 0, 
[2025-07-28 00:56:49 DP0 TP0] Decode batch. #running-req: 1, #token: 692, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.43, #queue-req: 0, 
[2025-07-28 00:56:49 DP1 TP0] Decode batch. #running-req: 1, #token: 614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.14, #queue-req: 0, 
[2025-07-28 00:56:49 DP0 TP0] Decode batch. #running-req: 1, #token: 732, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.62, #queue-req: 0, 
[2025-07-28 00:56:49 DP1 TP0] Decode batch. #running-req: 1, #token: 654, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.55, #queue-req: 0, 
[2025-07-28 00:56:49 DP0 TP0] Decode batch. #running-req: 1, #token: 772, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.77, #queue-req: 0, 
[2025-07-28 00:56:49 DP1 TP0] Decode batch. #running-req: 1, #token: 694, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.75, #queue-req: 0, 
[2025-07-28 00:56:49 DP0 TP0] Decode batch. #running-req: 1, #token: 812, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.54, #queue-req: 0, 
[2025-07-28 00:56:50 DP1 TP0] Decode batch. #running-req: 1, #token: 734, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.85, #queue-req: 0, 
[2025-07-28 00:56:50 DP0 TP0] Decode batch. #running-req: 1, #token: 852, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.44, #queue-req: 0, 
[2025-07-28 00:56:50 DP1 TP0] Decode batch. #running-req: 1, #token: 774, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.30, #queue-req: 0, 
[2025-07-28 00:56:50 DP0 TP0] Decode batch. #running-req: 1, #token: 892, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.21, #queue-req: 0, 
[2025-07-28 00:56:50] INFO:     127.0.0.1:38450 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:50 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:50 DP1 TP0] Decode batch. #running-req: 1, #token: 814, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.99, #queue-req: 0, 
[2025-07-28 00:56:50 DP0 TP0] Decode batch. #running-req: 1, #token: 204, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.74, #queue-req: 0, 
[2025-07-28 00:56:50 DP1 TP0] Decode batch. #running-req: 1, #token: 854, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.64, #queue-req: 0, 
[2025-07-28 00:56:50 DP0 TP0] Decode batch. #running-req: 1, #token: 244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 334.18, #queue-req: 0, 
[2025-07-28 00:56:50 DP1 TP0] Decode batch. #running-req: 1, #token: 894, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.62, #queue-req: 0, 
[2025-07-28 00:56:50 DP0 TP0] Decode batch. #running-req: 1, #token: 284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 323.11, #queue-req: 0, 
[2025-07-28 00:56:50 DP1 TP0] Decode batch. #running-req: 1, #token: 934, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.34, #queue-req: 0, 
[2025-07-28 00:56:50] INFO:     127.0.0.1:38466 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:50 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 154, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:50 DP0 TP0] Decode batch. #running-req: 1, #token: 324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.51, #queue-req: 0, 
[2025-07-28 00:56:50 DP1 TP0] Decode batch. #running-req: 1, #token: 263, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.71, #queue-req: 0, 
[2025-07-28 00:56:50 DP0 TP0] Decode batch. #running-req: 1, #token: 364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.51, #queue-req: 0, 
[2025-07-28 00:56:50 DP1 TP0] Decode batch. #running-req: 1, #token: 303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 334.65, #queue-req: 0, 
[2025-07-28 00:56:51 DP0 TP0] Decode batch. #running-req: 1, #token: 404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.05, #queue-req: 0, 
[2025-07-28 00:56:51 DP1 TP0] Decode batch. #running-req: 1, #token: 343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.43, #queue-req: 0, 
[2025-07-28 00:56:51 DP0 TP0] Decode batch. #running-req: 1, #token: 444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.27, #queue-req: 0, 
[2025-07-28 00:56:51 DP1 TP0] Decode batch. #running-req: 1, #token: 383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.97, #queue-req: 0, 
[2025-07-28 00:56:51 DP0 TP0] Decode batch. #running-req: 1, #token: 484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.09, #queue-req: 0, 
[2025-07-28 00:56:51 DP1 TP0] Decode batch. #running-req: 1, #token: 423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.04, #queue-req: 0, 
[2025-07-28 00:56:51 DP0 TP0] Decode batch. #running-req: 1, #token: 524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.39, #queue-req: 0, 
[2025-07-28 00:56:51 DP1 TP0] Decode batch. #running-req: 1, #token: 463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.72, #queue-req: 0, 
[2025-07-28 00:56:51 DP0 TP0] Decode batch. #running-req: 1, #token: 564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.81, #queue-req: 0, 
[2025-07-28 00:56:51 DP1 TP0] Decode batch. #running-req: 1, #token: 503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.84, #queue-req: 0, 
[2025-07-28 00:56:51 DP0 TP0] Decode batch. #running-req: 1, #token: 604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.01, #queue-req: 0, 
[2025-07-28 00:56:51 DP1 TP0] Decode batch. #running-req: 1, #token: 543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.82, #queue-req: 0, 
[2025-07-28 00:56:51 DP0 TP0] Decode batch. #running-req: 1, #token: 644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.97, #queue-req: 0, 
[2025-07-28 00:56:51 DP1 TP0] Decode batch. #running-req: 1, #token: 583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.18, #queue-req: 0, 
[2025-07-28 00:56:51 DP0 TP0] Decode batch. #running-req: 1, #token: 684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.85, #queue-req: 0, 
[2025-07-28 00:56:52 DP1 TP0] Decode batch. #running-req: 1, #token: 623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.73, #queue-req: 0, 
[2025-07-28 00:56:52 DP0 TP0] Decode batch. #running-req: 1, #token: 724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.97, #queue-req: 0, 
[2025-07-28 00:56:52 DP1 TP0] Decode batch. #running-req: 1, #token: 663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.46, #queue-req: 0, 
[2025-07-28 00:56:52 DP0 TP0] Decode batch. #running-req: 1, #token: 764, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.86, #queue-req: 0, 
[2025-07-28 00:56:52 DP1 TP0] Decode batch. #running-req: 1, #token: 703, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.75, #queue-req: 0, 
[2025-07-28 00:56:52 DP0 TP0] Decode batch. #running-req: 1, #token: 804, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.79, #queue-req: 0, 
[2025-07-28 00:56:52 DP1 TP0] Decode batch. #running-req: 1, #token: 743, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.54, #queue-req: 0, 
[2025-07-28 00:56:52 DP0 TP0] Decode batch. #running-req: 1, #token: 844, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.31, #queue-req: 0, 
[2025-07-28 00:56:52 DP1 TP0] Decode batch. #running-req: 1, #token: 783, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.27, #queue-req: 0, 
[2025-07-28 00:56:52 DP0 TP0] Decode batch. #running-req: 1, #token: 884, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.75, #queue-req: 0, 
[2025-07-28 00:56:52 DP1 TP0] Decode batch. #running-req: 1, #token: 823, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.71, #queue-req: 0, 
[2025-07-28 00:56:52 DP0 TP0] Decode batch. #running-req: 1, #token: 924, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.59, #queue-req: 0, 
[2025-07-28 00:56:52] INFO:     127.0.0.1:38488 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:52 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 128, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:56:52 DP0 TP0] Decode batch. #running-req: 2, #token: 1119, token usage: 0.00, cuda graph: True, gen throughput (token/s): 357.91, #queue-req: 0, 
[2025-07-28 00:56:52] INFO:     127.0.0.1:38474 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:52 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:53 DP0 TP0] Decode batch. #running-req: 1, #token: 280, token usage: 0.00, cuda graph: True, gen throughput (token/s): 457.52, #queue-req: 0, 
[2025-07-28 00:56:53 DP1 TP0] Decode batch. #running-req: 1, #token: 245, token usage: 0.00, cuda graph: True, gen throughput (token/s): 95.09, #queue-req: 0, 
[2025-07-28 00:56:53 DP0 TP0] Decode batch. #running-req: 1, #token: 320, token usage: 0.00, cuda graph: True, gen throughput (token/s): 335.95, #queue-req: 0, 
[2025-07-28 00:56:53 DP1 TP0] Decode batch. #running-req: 1, #token: 285, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.30, #queue-req: 0, 
[2025-07-28 00:56:53 DP0 TP0] Decode batch. #running-req: 1, #token: 360, token usage: 0.00, cuda graph: True, gen throughput (token/s): 323.37, #queue-req: 0, 
[2025-07-28 00:56:53 DP1 TP0] Decode batch. #running-req: 1, #token: 325, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.38, #queue-req: 0, 
[2025-07-28 00:56:53 DP0 TP0] Decode batch. #running-req: 1, #token: 400, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.13, #queue-req: 0, 
[2025-07-28 00:56:53 DP1 TP0] Decode batch. #running-req: 1, #token: 365, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.37, #queue-req: 0, 
[2025-07-28 00:56:53 DP0 TP0] Decode batch. #running-req: 1, #token: 440, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.69, #queue-req: 0, 
[2025-07-28 00:56:53 DP1 TP0] Decode batch. #running-req: 1, #token: 405, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.96, #queue-req: 0, 
[2025-07-28 00:56:53 DP0 TP0] Decode batch. #running-req: 1, #token: 480, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.48, #queue-req: 0, 
[2025-07-28 00:56:53 DP1 TP0] Decode batch. #running-req: 1, #token: 445, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.85, #queue-req: 0, 
[2025-07-28 00:56:53 DP0 TP0] Decode batch. #running-req: 1, #token: 520, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.71, #queue-req: 0, 
[2025-07-28 00:56:53 DP1 TP0] Decode batch. #running-req: 1, #token: 485, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.47, #queue-req: 0, 
[2025-07-28 00:56:53 DP0 TP0] Decode batch. #running-req: 1, #token: 560, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.49, #queue-req: 0, 
[2025-07-28 00:56:53 DP1 TP0] Decode batch. #running-req: 1, #token: 525, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.13, #queue-req: 0, 
[2025-07-28 00:56:54 DP0 TP0] Decode batch. #running-req: 1, #token: 600, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.46, #queue-req: 0, 
[2025-07-28 00:56:54 DP1 TP0] Decode batch. #running-req: 1, #token: 565, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.89, #queue-req: 0, 
[2025-07-28 00:56:54 DP0 TP0] Decode batch. #running-req: 1, #token: 640, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.25, #queue-req: 0, 
[2025-07-28 00:56:54 DP1 TP0] Decode batch. #running-req: 1, #token: 605, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.12, #queue-req: 0, 
[2025-07-28 00:56:54 DP0 TP0] Decode batch. #running-req: 1, #token: 680, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.46, #queue-req: 0, 
[2025-07-28 00:56:54 DP1 TP0] Decode batch. #running-req: 1, #token: 645, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.34, #queue-req: 0, 
[2025-07-28 00:56:54 DP0 TP0] Decode batch. #running-req: 1, #token: 720, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.94, #queue-req: 0, 
[2025-07-28 00:56:54 DP1 TP0] Decode batch. #running-req: 1, #token: 685, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.71, #queue-req: 0, 
[2025-07-28 00:56:54 DP0 TP0] Decode batch. #running-req: 1, #token: 760, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.38, #queue-req: 0, 
[2025-07-28 00:56:54] INFO:     127.0.0.1:55384 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:54 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 88, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:56:54 DP0 TP0] Decode batch. #running-req: 2, #token: 958, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.76, #queue-req: 0, 
[2025-07-28 00:56:54 DP0 TP0] Decode batch. #running-req: 2, #token: 1038, token usage: 0.00, cuda graph: True, gen throughput (token/s): 631.05, #queue-req: 0, 
[2025-07-28 00:56:54 DP0 TP0] Decode batch. #running-req: 2, #token: 1118, token usage: 0.00, cuda graph: True, gen throughput (token/s): 638.26, #queue-req: 0, 
[2025-07-28 00:56:55 DP0 TP0] Decode batch. #running-req: 2, #token: 1198, token usage: 0.00, cuda graph: True, gen throughput (token/s): 637.03, #queue-req: 0, 
[2025-07-28 00:56:55 DP0 TP0] Decode batch. #running-req: 2, #token: 1278, token usage: 0.00, cuda graph: True, gen throughput (token/s): 615.07, #queue-req: 0, 
[2025-07-28 00:56:55 DP0 TP0] Decode batch. #running-req: 2, #token: 1358, token usage: 0.00, cuda graph: True, gen throughput (token/s): 605.39, #queue-req: 0, 
[2025-07-28 00:56:55 DP0 TP0] Decode batch. #running-req: 2, #token: 1438, token usage: 0.00, cuda graph: True, gen throughput (token/s): 590.29, #queue-req: 0, 
[2025-07-28 00:56:55 DP0 TP0] Decode batch. #running-req: 2, #token: 1518, token usage: 0.00, cuda graph: True, gen throughput (token/s): 587.87, #queue-req: 0, 
[2025-07-28 00:56:55 DP0 TP0] Decode batch. #running-req: 2, #token: 1598, token usage: 0.00, cuda graph: True, gen throughput (token/s): 593.21, #queue-req: 0, 
[2025-07-28 00:56:55 DP0 TP0] Decode batch. #running-req: 2, #token: 1678, token usage: 0.00, cuda graph: True, gen throughput (token/s): 588.45, #queue-req: 0, 
[2025-07-28 00:56:56 DP0 TP0] Decode batch. #running-req: 2, #token: 1758, token usage: 0.00, cuda graph: True, gen throughput (token/s): 583.21, #queue-req: 0, 
[2025-07-28 00:56:56 DP0 TP0] Decode batch. #running-req: 2, #token: 1838, token usage: 0.00, cuda graph: True, gen throughput (token/s): 592.97, #queue-req: 0, 
[2025-07-28 00:56:56 DP0 TP0] Decode batch. #running-req: 2, #token: 1918, token usage: 0.00, cuda graph: True, gen throughput (token/s): 594.11, #queue-req: 0, 
[2025-07-28 00:56:56 DP0 TP0] Decode batch. #running-req: 2, #token: 1998, token usage: 0.00, cuda graph: True, gen throughput (token/s): 584.97, #queue-req: 0, 
[2025-07-28 00:56:56 DP0 TP0] Decode batch. #running-req: 2, #token: 2078, token usage: 0.00, cuda graph: True, gen throughput (token/s): 587.48, #queue-req: 0, 
[2025-07-28 00:56:56 DP0 TP0] Decode batch. #running-req: 2, #token: 2158, token usage: 0.00, cuda graph: True, gen throughput (token/s): 597.86, #queue-req: 0, 
[2025-07-28 00:56:56 DP0 TP0] Decode batch. #running-req: 2, #token: 2238, token usage: 0.00, cuda graph: True, gen throughput (token/s): 600.37, #queue-req: 0, 
[2025-07-28 00:56:56 DP0 TP0] Decode batch. #running-req: 2, #token: 2318, token usage: 0.00, cuda graph: True, gen throughput (token/s): 606.73, #queue-req: 0, 
[2025-07-28 00:56:57 DP0 TP0] Decode batch. #running-req: 2, #token: 2398, token usage: 0.00, cuda graph: True, gen throughput (token/s): 599.32, #queue-req: 0, 
[2025-07-28 00:56:57 DP0 TP0] Decode batch. #running-req: 2, #token: 2478, token usage: 0.00, cuda graph: True, gen throughput (token/s): 600.47, #queue-req: 0, 
[2025-07-28 00:56:57 DP0 TP0] Decode batch. #running-req: 2, #token: 2558, token usage: 0.00, cuda graph: True, gen throughput (token/s): 601.37, #queue-req: 0, 
[2025-07-28 00:56:57] INFO:     127.0.0.1:55390 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:56:57 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 95, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:56:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1640, token usage: 0.00, cuda graph: True, gen throughput (token/s): 419.19, #queue-req: 0, 
[2025-07-28 00:56:57 DP1 TP0] Decode batch. #running-req: 1, #token: 187, token usage: 0.00, cuda graph: True, gen throughput (token/s): 13.03, #queue-req: 0, 
[2025-07-28 00:56:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1680, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.12, #queue-req: 0, 
[2025-07-28 00:56:57 DP1 TP0] Decode batch. #running-req: 1, #token: 227, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.82, #queue-req: 0, 
[2025-07-28 00:56:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1720, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.54, #queue-req: 0, 
[2025-07-28 00:56:57 DP1 TP0] Decode batch. #running-req: 1, #token: 267, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.54, #queue-req: 0, 
[2025-07-28 00:56:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1760, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.38, #queue-req: 0, 
[2025-07-28 00:56:57 DP1 TP0] Decode batch. #running-req: 1, #token: 307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.41, #queue-req: 0, 
[2025-07-28 00:56:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1800, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.91, #queue-req: 0, 
[2025-07-28 00:56:58 DP1 TP0] Decode batch. #running-req: 1, #token: 347, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.05, #queue-req: 0, 
[2025-07-28 00:56:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1840, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.58, #queue-req: 0, 
[2025-07-28 00:56:58 DP1 TP0] Decode batch. #running-req: 1, #token: 387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.53, #queue-req: 0, 
[2025-07-28 00:56:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1880, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.73, #queue-req: 0, 
[2025-07-28 00:56:58 DP1 TP0] Decode batch. #running-req: 1, #token: 427, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.92, #queue-req: 0, 
[2025-07-28 00:56:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1920, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.48, #queue-req: 0, 
[2025-07-28 00:56:58 DP1 TP0] Decode batch. #running-req: 1, #token: 467, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.33, #queue-req: 0, 
[2025-07-28 00:56:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1960, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.84, #queue-req: 0, 
[2025-07-28 00:56:58 DP1 TP0] Decode batch. #running-req: 1, #token: 507, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.48, #queue-req: 0, 
[2025-07-28 00:56:58 DP0 TP0] Decode batch. #running-req: 1, #token: 2000, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.53, #queue-req: 0, 
[2025-07-28 00:56:58 DP1 TP0] Decode batch. #running-req: 1, #token: 547, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.69, #queue-req: 0, 
[2025-07-28 00:56:58 DP0 TP0] Decode batch. #running-req: 1, #token: 2040, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.19, #queue-req: 0, 
[2025-07-28 00:56:58 DP1 TP0] Decode batch. #running-req: 1, #token: 587, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.24, #queue-req: 0, 
[2025-07-28 00:56:58 DP0 TP0] Decode batch. #running-req: 1, #token: 2080, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.49, #queue-req: 0, 
[2025-07-28 00:56:59 DP1 TP0] Decode batch. #running-req: 1, #token: 627, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.28, #queue-req: 0, 
[2025-07-28 00:56:59 DP0 TP0] Decode batch. #running-req: 1, #token: 2120, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.45, #queue-req: 0, 
[2025-07-28 00:56:59 DP1 TP0] Decode batch. #running-req: 1, #token: 667, token usage: 0.00, cuda graph: True, gen throughput (token/s): 271.52, #queue-req: 0, 
[2025-07-28 00:56:59 DP0 TP0] Decode batch. #running-req: 1, #token: 2160, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.74, #queue-req: 0, 
[2025-07-28 00:56:59 DP1 TP0] Decode batch. #running-req: 1, #token: 707, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.39, #queue-req: 0, 
[2025-07-28 00:56:59 DP0 TP0] Decode batch. #running-req: 1, #token: 2200, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.38, #queue-req: 0, 
[2025-07-28 00:56:59 DP1 TP0] Decode batch. #running-req: 1, #token: 747, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.40, #queue-req: 0, 
[2025-07-28 00:56:59 DP0 TP0] Decode batch. #running-req: 1, #token: 2240, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.10, #queue-req: 0, 
[2025-07-28 00:56:59 DP1 TP0] Decode batch. #running-req: 1, #token: 787, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.15, #queue-req: 0, 
[2025-07-28 00:56:59 DP0 TP0] Decode batch. #running-req: 1, #token: 2280, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.28, #queue-req: 0, 
[2025-07-28 00:56:59 DP1 TP0] Decode batch. #running-req: 1, #token: 827, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.22, #queue-req: 0, 
[2025-07-28 00:56:59 DP0 TP0] Decode batch. #running-req: 1, #token: 2320, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.52, #queue-req: 0, 
[2025-07-28 00:56:59 DP1 TP0] Decode batch. #running-req: 1, #token: 867, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.80, #queue-req: 0, 
[2025-07-28 00:56:59 DP0 TP0] Decode batch. #running-req: 1, #token: 2360, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.15, #queue-req: 0, 
[2025-07-28 00:56:59 DP1 TP0] Decode batch. #running-req: 1, #token: 907, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.39, #queue-req: 0, 
[2025-07-28 00:56:59 DP0 TP0] Decode batch. #running-req: 1, #token: 2400, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.34, #queue-req: 0, 
[2025-07-28 00:57:00 DP1 TP0] Decode batch. #running-req: 1, #token: 947, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.72, #queue-req: 0, 
[2025-07-28 00:57:00 DP0 TP0] Decode batch. #running-req: 1, #token: 2440, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.75, #queue-req: 0, 
[2025-07-28 00:57:00 DP0 TP0] Decode batch. #running-req: 1, #token: 2480, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.08, #queue-req: 0, 
[2025-07-28 00:57:00 DP1 TP0] Decode batch. #running-req: 1, #token: 987, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.70, #queue-req: 0, 
[2025-07-28 00:57:00 DP0 TP0] Decode batch. #running-req: 1, #token: 2520, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.34, #queue-req: 0, 
[2025-07-28 00:57:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1027, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.67, #queue-req: 0, 
[2025-07-28 00:57:00 DP0 TP0] Decode batch. #running-req: 1, #token: 2560, token usage: 0.00, cuda graph: True, gen throughput (token/s): 280.09, #queue-req: 0, 
[2025-07-28 00:57:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1067, token usage: 0.00, cuda graph: True, gen throughput (token/s): 280.80, #queue-req: 0, 
[2025-07-28 00:57:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1107, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.54, #queue-req: 0, 
[2025-07-28 00:57:00 DP0 TP0] Decode batch. #running-req: 1, #token: 2600, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.22, #queue-req: 0, 
[2025-07-28 00:57:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1147, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.44, #queue-req: 0, 
[2025-07-28 00:57:00 DP0 TP0] Decode batch. #running-req: 1, #token: 2640, token usage: 0.00, cuda graph: True, gen throughput (token/s): 278.23, #queue-req: 0, 
[2025-07-28 00:57:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1187, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.89, #queue-req: 0, 
[2025-07-28 00:57:00 DP0 TP0] Decode batch. #running-req: 1, #token: 2680, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.97, #queue-req: 0, 
[2025-07-28 00:57:01 DP0 TP0] Decode batch. #running-req: 1, #token: 2720, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.02, #queue-req: 0, 
[2025-07-28 00:57:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1227, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.71, #queue-req: 0, 
[2025-07-28 00:57:01 DP0 TP0] Decode batch. #running-req: 1, #token: 2760, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.97, #queue-req: 0, 
[2025-07-28 00:57:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1267, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.76, #queue-req: 0, 
[2025-07-28 00:57:01 DP0 TP0] Decode batch. #running-req: 1, #token: 2800, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.61, #queue-req: 0, 
[2025-07-28 00:57:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.15, #queue-req: 0, 
[2025-07-28 00:57:01 DP0 TP0] Decode batch. #running-req: 1, #token: 2840, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.09, #queue-req: 0, 
[2025-07-28 00:57:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1347, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.85, #queue-req: 0, 
[2025-07-28 00:57:01 DP0 TP0] Decode batch. #running-req: 1, #token: 2880, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.35, #queue-req: 0, 
[2025-07-28 00:57:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.27, #queue-req: 0, 
[2025-07-28 00:57:01 DP0 TP0] Decode batch. #running-req: 1, #token: 2920, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.02, #queue-req: 0, 
[2025-07-28 00:57:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1427, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.72, #queue-req: 0, 
[2025-07-28 00:57:01 DP0 TP0] Decode batch. #running-req: 1, #token: 2960, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.35, #queue-req: 0, 
[2025-07-28 00:57:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1467, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.42, #queue-req: 0, 
[2025-07-28 00:57:01 DP0 TP0] Decode batch. #running-req: 1, #token: 3000, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.37, #queue-req: 0, 
[2025-07-28 00:57:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1507, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.59, #queue-req: 0, 
[2025-07-28 00:57:02 DP0 TP0] Decode batch. #running-req: 1, #token: 3040, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.76, #queue-req: 0, 
[2025-07-28 00:57:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1547, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.25, #queue-req: 0, 
[2025-07-28 00:57:02 DP0 TP0] Decode batch. #running-req: 1, #token: 3080, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.36, #queue-req: 0, 
[2025-07-28 00:57:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1587, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.23, #queue-req: 0, 
[2025-07-28 00:57:02 DP0 TP0] Decode batch. #running-req: 1, #token: 3120, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.71, #queue-req: 0, 
[2025-07-28 00:57:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1627, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.39, #queue-req: 0, 
[2025-07-28 00:57:02 DP0 TP0] Decode batch. #running-req: 1, #token: 3160, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.43, #queue-req: 0, 
[2025-07-28 00:57:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1667, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.58, #queue-req: 0, 
[2025-07-28 00:57:02 DP0 TP0] Decode batch. #running-req: 1, #token: 3200, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.73, #queue-req: 0, 
[2025-07-28 00:57:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1707, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.04, #queue-req: 0, 
[2025-07-28 00:57:02 DP0 TP0] Decode batch. #running-req: 1, #token: 3240, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.35, #queue-req: 0, 
[2025-07-28 00:57:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1747, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.78, #queue-req: 0, 
[2025-07-28 00:57:02 DP0 TP0] Decode batch. #running-req: 1, #token: 3280, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.77, #queue-req: 0, 
[2025-07-28 00:57:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1787, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.25, #queue-req: 0, 
[2025-07-28 00:57:02] INFO:     127.0.0.1:55400 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:57:02 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 86, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:57:03 DP0 TP0] Decode batch. #running-req: 2, #token: 3471, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.32, #queue-req: 0, 
[2025-07-28 00:57:03 DP0 TP0] Decode batch. #running-req: 2, #token: 3551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 670.04, #queue-req: 0, 
[2025-07-28 00:57:03 DP0 TP0] Decode batch. #running-req: 2, #token: 3631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 645.06, #queue-req: 0, 
[2025-07-28 00:57:03 DP0 TP0] Decode batch. #running-req: 2, #token: 3711, token usage: 0.00, cuda graph: True, gen throughput (token/s): 644.69, #queue-req: 0, 
[2025-07-28 00:57:03 DP0 TP0] Decode batch. #running-req: 2, #token: 3791, token usage: 0.00, cuda graph: True, gen throughput (token/s): 646.47, #queue-req: 0, 
[2025-07-28 00:57:03 DP0 TP0] Decode batch. #running-req: 2, #token: 3871, token usage: 0.00, cuda graph: True, gen throughput (token/s): 634.24, #queue-req: 0, 
[2025-07-28 00:57:03 DP0 TP0] Decode batch. #running-req: 2, #token: 3951, token usage: 0.00, cuda graph: True, gen throughput (token/s): 612.78, #queue-req: 0, 
[2025-07-28 00:57:03 DP0 TP0] Decode batch. #running-req: 2, #token: 4031, token usage: 0.00, cuda graph: True, gen throughput (token/s): 593.38, #queue-req: 0, 
[2025-07-28 00:57:04 DP0 TP0] Decode batch. #running-req: 2, #token: 4111, token usage: 0.00, cuda graph: True, gen throughput (token/s): 592.36, #queue-req: 0, 
[2025-07-28 00:57:04 DP0 TP0] Decode batch. #running-req: 2, #token: 4191, token usage: 0.00, cuda graph: True, gen throughput (token/s): 586.28, #queue-req: 0, 
[2025-07-28 00:57:04 DP0 TP0] Decode batch. #running-req: 2, #token: 4271, token usage: 0.00, cuda graph: True, gen throughput (token/s): 597.48, #queue-req: 0, 
[2025-07-28 00:57:04 DP0 TP0] Decode batch. #running-req: 2, #token: 4351, token usage: 0.00, cuda graph: True, gen throughput (token/s): 601.64, #queue-req: 0, 
[2025-07-28 00:57:04 DP0 TP0] Decode batch. #running-req: 2, #token: 4431, token usage: 0.00, cuda graph: True, gen throughput (token/s): 603.00, #queue-req: 0, 
[2025-07-28 00:57:04 DP0 TP0] Decode batch. #running-req: 2, #token: 4511, token usage: 0.00, cuda graph: True, gen throughput (token/s): 587.30, #queue-req: 0, 
[2025-07-28 00:57:04 DP0 TP0] Decode batch. #running-req: 2, #token: 4591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 592.81, #queue-req: 0, 
[2025-07-28 00:57:04 DP0 TP0] Decode batch. #running-req: 2, #token: 4671, token usage: 0.00, cuda graph: True, gen throughput (token/s): 593.09, #queue-req: 0, 
[2025-07-28 00:57:05 DP0 TP0] Decode batch. #running-req: 2, #token: 4751, token usage: 0.00, cuda graph: True, gen throughput (token/s): 582.27, #queue-req: 0, 
[2025-07-28 00:57:05 DP0 TP0] Decode batch. #running-req: 2, #token: 4831, token usage: 0.00, cuda graph: True, gen throughput (token/s): 594.56, #queue-req: 0, 
[2025-07-28 00:57:05 DP0 TP0] Decode batch. #running-req: 2, #token: 4911, token usage: 0.00, cuda graph: True, gen throughput (token/s): 597.15, #queue-req: 0, 
[2025-07-28 00:57:05 DP0 TP0] Decode batch. #running-req: 2, #token: 4991, token usage: 0.00, cuda graph: True, gen throughput (token/s): 593.80, #queue-req: 0, 
[2025-07-28 00:57:05 DP0 TP0] Decode batch. #running-req: 2, #token: 5071, token usage: 0.00, cuda graph: True, gen throughput (token/s): 593.38, #queue-req: 0, 
[2025-07-28 00:57:05 DP0 TP0] Decode batch. #running-req: 2, #token: 5151, token usage: 0.00, cuda graph: True, gen throughput (token/s): 588.34, #queue-req: 0, 
[2025-07-28 00:57:05 DP0 TP0] Decode batch. #running-req: 2, #token: 5231, token usage: 0.00, cuda graph: True, gen throughput (token/s): 591.08, #queue-req: 0, 
[2025-07-28 00:57:06 DP0 TP0] Decode batch. #running-req: 2, #token: 5311, token usage: 0.00, cuda graph: True, gen throughput (token/s): 601.38, #queue-req: 0, 
[2025-07-28 00:57:06 DP0 TP0] Decode batch. #running-req: 2, #token: 5391, token usage: 0.00, cuda graph: True, gen throughput (token/s): 601.01, #queue-req: 0, 
[2025-07-28 00:57:06 DP0 TP0] Decode batch. #running-req: 2, #token: 5471, token usage: 0.00, cuda graph: True, gen throughput (token/s): 601.91, #queue-req: 0, 
[2025-07-28 00:57:06 DP0 TP0] Decode batch. #running-req: 2, #token: 5551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 593.41, #queue-req: 0, 
[2025-07-28 00:57:06 DP0 TP0] Decode batch. #running-req: 2, #token: 5631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 596.79, #queue-req: 0, 
[2025-07-28 00:57:06] INFO:     127.0.0.1:35710 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:57:06 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 78, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:57:06 DP0 TP0] Decode batch. #running-req: 1, #token: 4440, token usage: 0.00, cuda graph: True, gen throughput (token/s): 412.92, #queue-req: 0, 
[2025-07-28 00:57:06 DP1 TP0] Decode batch. #running-req: 1, #token: 191, token usage: 0.00, cuda graph: True, gen throughput (token/s): 10.04, #queue-req: 0, 
[2025-07-28 00:57:06 DP0 TP0] Decode batch. #running-req: 1, #token: 4480, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.70, #queue-req: 0, 
[2025-07-28 00:57:06 DP0 TP0] Decode batch. #running-req: 1, #token: 4520, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.48, #queue-req: 0, 
[2025-07-28 00:57:06 DP1 TP0] Decode batch. #running-req: 1, #token: 231, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.55, #queue-req: 0, 
[2025-07-28 00:57:07 DP0 TP0] Decode batch. #running-req: 1, #token: 4560, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.80, #queue-req: 0, 
[2025-07-28 00:57:07 DP1 TP0] Decode batch. #running-req: 1, #token: 271, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.03, #queue-req: 0, 
[2025-07-28 00:57:07 DP0 TP0] Decode batch. #running-req: 1, #token: 4600, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.41, #queue-req: 0, 
[2025-07-28 00:57:07 DP1 TP0] Decode batch. #running-req: 1, #token: 311, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.12, #queue-req: 0, 
[2025-07-28 00:57:07 DP0 TP0] Decode batch. #running-req: 1, #token: 4640, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.67, #queue-req: 0, 
[2025-07-28 00:57:07 DP1 TP0] Decode batch. #running-req: 1, #token: 351, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.33, #queue-req: 0, 
[2025-07-28 00:57:07 DP0 TP0] Decode batch. #running-req: 1, #token: 4680, token usage: 0.00, cuda graph: True, gen throughput (token/s): 278.40, #queue-req: 0, 
[2025-07-28 00:57:07 DP1 TP0] Decode batch. #running-req: 1, #token: 391, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.70, #queue-req: 0, 
[2025-07-28 00:57:07 DP0 TP0] Decode batch. #running-req: 1, #token: 4720, token usage: 0.00, cuda graph: True, gen throughput (token/s): 279.39, #queue-req: 0, 
[2025-07-28 00:57:07 DP1 TP0] Decode batch. #running-req: 1, #token: 431, token usage: 0.00, cuda graph: True, gen throughput (token/s): 282.90, #queue-req: 0, 
[2025-07-28 00:57:07 DP0 TP0] Decode batch. #running-req: 1, #token: 4760, token usage: 0.00, cuda graph: True, gen throughput (token/s): 279.99, #queue-req: 0, 
[2025-07-28 00:57:07 DP1 TP0] Decode batch. #running-req: 1, #token: 471, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.92, #queue-req: 0, 
[2025-07-28 00:57:07 DP0 TP0] Decode batch. #running-req: 1, #token: 4800, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.36, #queue-req: 0, 
[2025-07-28 00:57:07 DP1 TP0] Decode batch. #running-req: 1, #token: 511, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.05, #queue-req: 0, 
[2025-07-28 00:57:08 DP0 TP0] Decode batch. #running-req: 1, #token: 4840, token usage: 0.00, cuda graph: True, gen throughput (token/s): 278.61, #queue-req: 0, 
[2025-07-28 00:57:08 DP1 TP0] Decode batch. #running-req: 1, #token: 551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.92, #queue-req: 0, 
[2025-07-28 00:57:08 DP1 TP0] Decode batch. #running-req: 1, #token: 591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.63, #queue-req: 0, 
[2025-07-28 00:57:08 DP0 TP0] Decode batch. #running-req: 1, #token: 4880, token usage: 0.00, cuda graph: True, gen throughput (token/s): 279.60, #queue-req: 0, 
[2025-07-28 00:57:08 DP1 TP0] Decode batch. #running-req: 1, #token: 631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 275.14, #queue-req: 0, 
[2025-07-28 00:57:08 DP0 TP0] Decode batch. #running-req: 1, #token: 4920, token usage: 0.00, cuda graph: True, gen throughput (token/s): 278.59, #queue-req: 0, 
[2025-07-28 00:57:08 DP1 TP0] Decode batch. #running-req: 1, #token: 671, token usage: 0.00, cuda graph: True, gen throughput (token/s): 288.46, #queue-req: 0, 
[2025-07-28 00:57:08 DP0 TP0] Decode batch. #running-req: 1, #token: 4960, token usage: 0.00, cuda graph: True, gen throughput (token/s): 279.79, #queue-req: 0, 
[2025-07-28 00:57:08 DP1 TP0] Decode batch. #running-req: 1, #token: 711, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.27, #queue-req: 0, 
[2025-07-28 00:57:08 DP0 TP0] Decode batch. #running-req: 1, #token: 5000, token usage: 0.00, cuda graph: True, gen throughput (token/s): 275.82, #queue-req: 0, 
[2025-07-28 00:57:08 DP1 TP0] Decode batch. #running-req: 1, #token: 751, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.18, #queue-req: 0, 
[2025-07-28 00:57:08 DP0 TP0] Decode batch. #running-req: 1, #token: 5040, token usage: 0.00, cuda graph: True, gen throughput (token/s): 279.59, #queue-req: 0, 
[2025-07-28 00:57:08] INFO:     127.0.0.1:35720 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:57:08 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 146, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:57:09 DP0 TP0] Decode batch. #running-req: 2, #token: 5231, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.48, #queue-req: 0, 
[2025-07-28 00:57:09 DP0 TP0] Decode batch. #running-req: 2, #token: 5311, token usage: 0.00, cuda graph: True, gen throughput (token/s): 623.79, #queue-req: 0, 
[2025-07-28 00:57:09 DP0 TP0] Decode batch. #running-req: 2, #token: 5391, token usage: 0.00, cuda graph: True, gen throughput (token/s): 611.98, #queue-req: 0, 
[2025-07-28 00:57:09 DP0 TP0] Decode batch. #running-req: 2, #token: 5471, token usage: 0.00, cuda graph: True, gen throughput (token/s): 584.23, #queue-req: 0, 
[2025-07-28 00:57:09 DP0 TP0] Decode batch. #running-req: 2, #token: 5551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 585.57, #queue-req: 0, 
[2025-07-28 00:57:09 DP0 TP0] Decode batch. #running-req: 2, #token: 5631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 592.29, #queue-req: 0, 
[2025-07-28 00:57:09 DP0 TP0] Decode batch. #running-req: 2, #token: 5711, token usage: 0.00, cuda graph: True, gen throughput (token/s): 596.25, #queue-req: 0, 
[2025-07-28 00:57:09 DP0 TP0] Decode batch. #running-req: 2, #token: 5791, token usage: 0.00, cuda graph: True, gen throughput (token/s): 580.74, #queue-req: 0, 
[2025-07-28 00:57:10 DP0 TP0] Decode batch. #running-req: 2, #token: 5871, token usage: 0.00, cuda graph: True, gen throughput (token/s): 597.12, #queue-req: 0, 
[2025-07-28 00:57:10 DP0 TP0] Decode batch. #running-req: 2, #token: 5951, token usage: 0.00, cuda graph: True, gen throughput (token/s): 576.13, #queue-req: 0, 
[2025-07-28 00:57:10 DP0 TP0] Decode batch. #running-req: 2, #token: 6031, token usage: 0.00, cuda graph: True, gen throughput (token/s): 597.22, #queue-req: 0, 
[2025-07-28 00:57:10 DP0 TP0] Decode batch. #running-req: 2, #token: 6111, token usage: 0.00, cuda graph: True, gen throughput (token/s): 554.67, #queue-req: 0, 
[2025-07-28 00:57:10 DP0 TP0] Decode batch. #running-req: 2, #token: 6191, token usage: 0.00, cuda graph: True, gen throughput (token/s): 566.14, #queue-req: 0, 
[2025-07-28 00:57:10 DP0 TP0] Decode batch. #running-req: 2, #token: 6271, token usage: 0.00, cuda graph: True, gen throughput (token/s): 579.98, #queue-req: 0, 
[2025-07-28 00:57:10 DP0 TP0] Decode batch. #running-req: 2, #token: 6351, token usage: 0.00, cuda graph: True, gen throughput (token/s): 570.70, #queue-req: 0, 
[2025-07-28 00:57:11 DP0 TP0] Decode batch. #running-req: 2, #token: 6431, token usage: 0.00, cuda graph: True, gen throughput (token/s): 585.99, #queue-req: 0, 
[2025-07-28 00:57:11 DP0 TP0] Decode batch. #running-req: 2, #token: 6511, token usage: 0.00, cuda graph: True, gen throughput (token/s): 585.35, #queue-req: 0, 
[2025-07-28 00:57:11 DP0 TP0] Decode batch. #running-req: 2, #token: 6591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 601.10, #queue-req: 0, 
[2025-07-28 00:57:11 DP0 TP0] Decode batch. #running-req: 2, #token: 6671, token usage: 0.00, cuda graph: True, gen throughput (token/s): 545.82, #queue-req: 0, 
[2025-07-28 00:57:11] INFO:     127.0.0.1:35722 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:57:11 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:57:11 DP0 TP0] Decode batch. #running-req: 1, #token: 5840, token usage: 0.00, cuda graph: True, gen throughput (token/s): 444.33, #queue-req: 0, 
[2025-07-28 00:57:11 DP1 TP0] Decode batch. #running-req: 1, #token: 174, token usage: 0.00, cuda graph: True, gen throughput (token/s): 13.87, #queue-req: 0, 
[2025-07-28 00:57:11 DP0 TP0] Decode batch. #running-req: 1, #token: 5880, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.29, #queue-req: 0, 
[2025-07-28 00:57:11 DP1 TP0] Decode batch. #running-req: 1, #token: 214, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.82, #queue-req: 0, 
[2025-07-28 00:57:11 DP0 TP0] Decode batch. #running-req: 1, #token: 5920, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.35, #queue-req: 0, 
[2025-07-28 00:57:11 DP1 TP0] Decode batch. #running-req: 1, #token: 254, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.37, #queue-req: 0, 
[2025-07-28 00:57:12 DP0 TP0] Decode batch. #running-req: 1, #token: 5960, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.66, #queue-req: 0, 
[2025-07-28 00:57:12 DP1 TP0] Decode batch. #running-req: 1, #token: 294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.75, #queue-req: 0, 
[2025-07-28 00:57:12 DP0 TP0] Decode batch. #running-req: 1, #token: 6000, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.95, #queue-req: 0, 
[2025-07-28 00:57:12 DP1 TP0] Decode batch. #running-req: 1, #token: 334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.75, #queue-req: 0, 
[2025-07-28 00:57:12 DP0 TP0] Decode batch. #running-req: 1, #token: 6040, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.18, #queue-req: 0, 
[2025-07-28 00:57:12 DP1 TP0] Decode batch. #running-req: 1, #token: 374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.00, #queue-req: 0, 
[2025-07-28 00:57:12 DP0 TP0] Decode batch. #running-req: 1, #token: 6080, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.48, #queue-req: 0, 
[2025-07-28 00:57:12 DP1 TP0] Decode batch. #running-req: 1, #token: 414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.42, #queue-req: 0, 
[2025-07-28 00:57:12 DP0 TP0] Decode batch. #running-req: 1, #token: 6120, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.01, #queue-req: 0, 
[2025-07-28 00:57:12 DP1 TP0] Decode batch. #running-req: 1, #token: 454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.92, #queue-req: 0, 
[2025-07-28 00:57:12 DP0 TP0] Decode batch. #running-req: 1, #token: 6160, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.28, #queue-req: 0, 
[2025-07-28 00:57:12 DP1 TP0] Decode batch. #running-req: 1, #token: 494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.95, #queue-req: 0, 
[2025-07-28 00:57:12 DP0 TP0] Decode batch. #running-req: 1, #token: 6200, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.88, #queue-req: 0, 
[2025-07-28 00:57:12 DP1 TP0] Decode batch. #running-req: 1, #token: 534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.49, #queue-req: 0, 
[2025-07-28 00:57:12 DP0 TP0] Decode batch. #running-req: 1, #token: 6240, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.68, #queue-req: 0, 
[2025-07-28 00:57:12 DP1 TP0] Decode batch. #running-req: 1, #token: 574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.65, #queue-req: 0, 
[2025-07-28 00:57:13 DP0 TP0] Decode batch. #running-req: 1, #token: 6280, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.59, #queue-req: 0, 
[2025-07-28 00:57:13 DP1 TP0] Decode batch. #running-req: 1, #token: 614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.53, #queue-req: 0, 
[2025-07-28 00:57:13 DP0 TP0] Decode batch. #running-req: 1, #token: 6320, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.74, #queue-req: 0, 
[2025-07-28 00:57:13 DP1 TP0] Decode batch. #running-req: 1, #token: 654, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.42, #queue-req: 0, 
[2025-07-28 00:57:13 DP0 TP0] Decode batch. #running-req: 1, #token: 6360, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.18, #queue-req: 0, 
[2025-07-28 00:57:13 DP1 TP0] Decode batch. #running-req: 1, #token: 694, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.12, #queue-req: 0, 
[2025-07-28 00:57:13 DP0 TP0] Decode batch. #running-req: 1, #token: 6400, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.22, #queue-req: 0, 
[2025-07-28 00:57:13 DP1 TP0] Decode batch. #running-req: 1, #token: 734, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.77, #queue-req: 0, 
[2025-07-28 00:57:13 DP0 TP0] Decode batch. #running-req: 1, #token: 6440, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.27, #queue-req: 0, 
[2025-07-28 00:57:13 DP1 TP0] Decode batch. #running-req: 1, #token: 774, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.71, #queue-req: 0, 
[2025-07-28 00:57:13] INFO:     127.0.0.1:35732 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:57:13 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 173, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:57:13 DP0 TP0] Decode batch. #running-req: 2, #token: 6660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 233.05, #queue-req: 0, 
[2025-07-28 00:57:13 DP0 TP0] Decode batch. #running-req: 2, #token: 6740, token usage: 0.00, cuda graph: True, gen throughput (token/s): 662.74, #queue-req: 0, 
[2025-07-28 00:57:14 DP0 TP0] Decode batch. #running-req: 2, #token: 6820, token usage: 0.00, cuda graph: True, gen throughput (token/s): 641.42, #queue-req: 0, 
[2025-07-28 00:57:14 DP0 TP0] Decode batch. #running-req: 2, #token: 6900, token usage: 0.00, cuda graph: True, gen throughput (token/s): 636.14, #queue-req: 0, 
[2025-07-28 00:57:14 DP0 TP0] Decode batch. #running-req: 2, #token: 6980, token usage: 0.00, cuda graph: True, gen throughput (token/s): 639.75, #queue-req: 0, 
[2025-07-28 00:57:14 DP0 TP0] Decode batch. #running-req: 2, #token: 7060, token usage: 0.00, cuda graph: True, gen throughput (token/s): 633.85, #queue-req: 0, 
[2025-07-28 00:57:14 DP0 TP0] Decode batch. #running-req: 2, #token: 7140, token usage: 0.00, cuda graph: True, gen throughput (token/s): 618.13, #queue-req: 0, 
[2025-07-28 00:57:14 DP0 TP0] Decode batch. #running-req: 2, #token: 7220, token usage: 0.00, cuda graph: True, gen throughput (token/s): 594.16, #queue-req: 0, 
[2025-07-28 00:57:14 DP0 TP0] Decode batch. #running-req: 2, #token: 7300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 534.46, #queue-req: 0, 
[2025-07-28 00:57:14 DP0 TP0] Decode batch. #running-req: 2, #token: 7380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 542.63, #queue-req: 0, 
[2025-07-28 00:57:15 DP0 TP0] Decode batch. #running-req: 2, #token: 7460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 537.89, #queue-req: 0, 
[2025-07-28 00:57:15 DP0 TP0] Decode batch. #running-req: 2, #token: 7540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 563.04, #queue-req: 0, 
[2025-07-28 00:57:15 DP0 TP0] Decode batch. #running-req: 2, #token: 7620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 585.50, #queue-req: 0, 
[2025-07-28 00:57:15 DP0 TP0] Decode batch. #running-req: 2, #token: 7700, token usage: 0.00, cuda graph: True, gen throughput (token/s): 586.64, #queue-req: 0, 
[2025-07-28 00:57:15 DP0 TP0] Decode batch. #running-req: 2, #token: 7780, token usage: 0.00, cuda graph: True, gen throughput (token/s): 590.03, #queue-req: 0, 
[2025-07-28 00:57:15] INFO:     127.0.0.1:37700 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:57:15 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 178, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:57:15 DP0 TP0] Decode batch. #running-req: 1, #token: 7080, token usage: 0.00, cuda graph: True, gen throughput (token/s): 443.17, #queue-req: 0, 
[2025-07-28 00:57:15 DP1 TP0] Decode batch. #running-req: 1, #token: 293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 17.46, #queue-req: 0, 
[2025-07-28 00:57:15 DP0 TP0] Decode batch. #running-req: 1, #token: 7120, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.26, #queue-req: 0, 
[2025-07-28 00:57:16 DP0 TP0] Decode batch. #running-req: 1, #token: 7160, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.53, #queue-req: 0, 
[2025-07-28 00:57:16 DP1 TP0] Decode batch. #running-req: 1, #token: 333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.99, #queue-req: 0, 
[2025-07-28 00:57:16 DP0 TP0] Decode batch. #running-req: 1, #token: 7200, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.73, #queue-req: 0, 
[2025-07-28 00:57:16 DP1 TP0] Decode batch. #running-req: 1, #token: 373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.51, #queue-req: 0, 
[2025-07-28 00:57:16 DP0 TP0] Decode batch. #running-req: 1, #token: 7240, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.95, #queue-req: 0, 
[2025-07-28 00:57:16 DP1 TP0] Decode batch. #running-req: 1, #token: 413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.82, #queue-req: 0, 
[2025-07-28 00:57:16 DP1 TP0] Decode batch. #running-req: 1, #token: 453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.28, #queue-req: 0, 
[2025-07-28 00:57:16 DP0 TP0] Decode batch. #running-req: 1, #token: 7280, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.24, #queue-req: 0, 
[2025-07-28 00:57:16 DP0 TP0] Decode batch. #running-req: 1, #token: 7320, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.82, #queue-req: 0, 
[2025-07-28 00:57:16 DP1 TP0] Decode batch. #running-req: 1, #token: 493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.00, #queue-req: 0, 
[2025-07-28 00:57:16 DP0 TP0] Decode batch. #running-req: 1, #token: 7360, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.73, #queue-req: 0, 
[2025-07-28 00:57:16 DP1 TP0] Decode batch. #running-req: 1, #token: 533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.47, #queue-req: 0, 
[2025-07-28 00:57:16 DP0 TP0] Decode batch. #running-req: 1, #token: 7400, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.62, #queue-req: 0, 
[2025-07-28 00:57:16 DP1 TP0] Decode batch. #running-req: 1, #token: 573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.96, #queue-req: 0, 
[2025-07-28 00:57:16 DP0 TP0] Decode batch. #running-req: 1, #token: 7440, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.62, #queue-req: 0, 
[2025-07-28 00:57:16 DP1 TP0] Decode batch. #running-req: 1, #token: 613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.01, #queue-req: 0, 
[2025-07-28 00:57:17 DP0 TP0] Decode batch. #running-req: 1, #token: 7480, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.78, #queue-req: 0, 
[2025-07-28 00:57:17 DP1 TP0] Decode batch. #running-req: 1, #token: 653, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.80, #queue-req: 0, 
[2025-07-28 00:57:17 DP0 TP0] Decode batch. #running-req: 1, #token: 7520, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.00, #queue-req: 0, 
[2025-07-28 00:57:17 DP1 TP0] Decode batch. #running-req: 1, #token: 693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 288.47, #queue-req: 0, 
[2025-07-28 00:57:17 DP0 TP0] Decode batch. #running-req: 1, #token: 7560, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.17, #queue-req: 0, 
[2025-07-28 00:57:17 DP1 TP0] Decode batch. #running-req: 1, #token: 733, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.96, #queue-req: 0, 
[2025-07-28 00:57:17 DP0 TP0] Decode batch. #running-req: 1, #token: 7600, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.41, #queue-req: 0, 
[2025-07-28 00:57:17 DP1 TP0] Decode batch. #running-req: 1, #token: 773, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.79, #queue-req: 0, 
[2025-07-28 00:57:17 DP0 TP0] Decode batch. #running-req: 1, #token: 7640, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.08, #queue-req: 0, 
[2025-07-28 00:57:17 DP1 TP0] Decode batch. #running-req: 1, #token: 813, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.85, #queue-req: 0, 
[2025-07-28 00:57:17] INFO:     127.0.0.1:37704 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:57:17 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 88, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:57:17 DP0 TP0] Decode batch. #running-req: 2, #token: 7780, token usage: 0.00, cuda graph: True, gen throughput (token/s): 271.01, #queue-req: 0, 
[2025-07-28 00:57:17 DP0 TP0] Decode batch. #running-req: 2, #token: 7860, token usage: 0.00, cuda graph: True, gen throughput (token/s): 642.03, #queue-req: 0, 
[2025-07-28 00:57:18 DP0 TP0] Decode batch. #running-req: 2, #token: 7940, token usage: 0.00, cuda graph: True, gen throughput (token/s): 635.17, #queue-req: 0, 
[2025-07-28 00:57:18 DP0 TP0] Decode batch. #running-req: 2, #token: 8020, token usage: 0.00, cuda graph: True, gen throughput (token/s): 638.10, #queue-req: 0, 
[2025-07-28 00:57:18 DP0 TP0] Decode batch. #running-req: 2, #token: 8100, token usage: 0.00, cuda graph: True, gen throughput (token/s): 616.69, #queue-req: 0, 
[2025-07-28 00:57:18 DP0 TP0] Decode batch. #running-req: 2, #token: 8180, token usage: 0.00, cuda graph: True, gen throughput (token/s): 605.21, #queue-req: 0, 
[2025-07-28 00:57:18 DP0 TP0] Decode batch. #running-req: 2, #token: 8260, token usage: 0.00, cuda graph: True, gen throughput (token/s): 589.05, #queue-req: 0, 
[2025-07-28 00:57:18 DP0 TP0] Decode batch. #running-req: 2, #token: 8340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 589.20, #queue-req: 0, 
[2025-07-28 00:57:18 DP0 TP0] Decode batch. #running-req: 2, #token: 8420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 594.51, #queue-req: 0, 
[2025-07-28 00:57:18 DP0 TP0] Decode batch. #running-req: 2, #token: 8500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 588.27, #queue-req: 0, 
[2025-07-28 00:57:19 DP0 TP0] Decode batch. #running-req: 2, #token: 8580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 594.85, #queue-req: 0, 
[2025-07-28 00:57:19 DP0 TP0] Decode batch. #running-req: 2, #token: 8660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 592.05, #queue-req: 0, 
[2025-07-28 00:57:19 DP0 TP0] Decode batch. #running-req: 2, #token: 8740, token usage: 0.00, cuda graph: True, gen throughput (token/s): 591.42, #queue-req: 0, 
[2025-07-28 00:57:19 DP0 TP0] Decode batch. #running-req: 2, #token: 8820, token usage: 0.00, cuda graph: True, gen throughput (token/s): 590.41, #queue-req: 0, 
[2025-07-28 00:57:19 DP0 TP0] Decode batch. #running-req: 2, #token: 8900, token usage: 0.00, cuda graph: True, gen throughput (token/s): 595.08, #queue-req: 0, 
[2025-07-28 00:57:19 DP0 TP0] Decode batch. #running-req: 2, #token: 8980, token usage: 0.00, cuda graph: True, gen throughput (token/s): 592.59, #queue-req: 0, 
[2025-07-28 00:57:19 DP0 TP0] Decode batch. #running-req: 2, #token: 9060, token usage: 0.00, cuda graph: True, gen throughput (token/s): 600.14, #queue-req: 0, 
[2025-07-28 00:57:20 DP0 TP0] Decode batch. #running-req: 2, #token: 9140, token usage: 0.00, cuda graph: True, gen throughput (token/s): 601.58, #queue-req: 0, 
[2025-07-28 00:57:20 DP0 TP0] Decode batch. #running-req: 2, #token: 9220, token usage: 0.00, cuda graph: True, gen throughput (token/s): 600.18, #queue-req: 0, 
[2025-07-28 00:57:20] INFO:     127.0.0.1:55370 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:57:20 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 153, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:57:20 DP0 TP0] Decode batch. #running-req: 1, #token: 945, token usage: 0.00, cuda graph: True, gen throughput (token/s): 357.91, #queue-req: 0, 
[2025-07-28 00:57:20 DP1 TP0] Decode batch. #running-req: 1, #token: 264, token usage: 0.00, cuda graph: True, gen throughput (token/s): 14.49, #queue-req: 0, 
[2025-07-28 00:57:20 DP0 TP0] Decode batch. #running-req: 1, #token: 985, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.62, #queue-req: 0, 
[2025-07-28 00:57:20 DP1 TP0] Decode batch. #running-req: 1, #token: 304, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.73, #queue-req: 0, 
[2025-07-28 00:57:20] INFO:     127.0.0.1:37710 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:57:20 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 123, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:57:20 DP0 TP0] Decode batch. #running-req: 1, #token: 218, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.21, #queue-req: 0, 
[2025-07-28 00:57:20 DP1 TP0] Decode batch. #running-req: 1, #token: 344, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.54, #queue-req: 0, 
[2025-07-28 00:57:20 DP0 TP0] Decode batch. #running-req: 1, #token: 258, token usage: 0.00, cuda graph: True, gen throughput (token/s): 329.55, #queue-req: 0, 
[2025-07-28 00:57:20 DP1 TP0] Decode batch. #running-req: 1, #token: 384, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.17, #queue-req: 0, 
[2025-07-28 00:57:20 DP0 TP0] Decode batch. #running-req: 1, #token: 298, token usage: 0.00, cuda graph: True, gen throughput (token/s): 320.72, #queue-req: 0, 
[2025-07-28 00:57:20 DP1 TP0] Decode batch. #running-req: 1, #token: 424, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.17, #queue-req: 0, 
[2025-07-28 00:57:21 DP0 TP0] Decode batch. #running-req: 1, #token: 338, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.43, #queue-req: 0, 
[2025-07-28 00:57:21 DP1 TP0] Decode batch. #running-req: 1, #token: 464, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.38, #queue-req: 0, 
[2025-07-28 00:57:21 DP0 TP0] Decode batch. #running-req: 1, #token: 378, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.40, #queue-req: 0, 
[2025-07-28 00:57:21 DP1 TP0] Decode batch. #running-req: 1, #token: 504, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.05, #queue-req: 0, 
[2025-07-28 00:57:21 DP0 TP0] Decode batch. #running-req: 1, #token: 418, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.75, #queue-req: 0, 
[2025-07-28 00:57:21 DP1 TP0] Decode batch. #running-req: 1, #token: 544, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.36, #queue-req: 0, 
[2025-07-28 00:57:21 DP0 TP0] Decode batch. #running-req: 1, #token: 458, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.82, #queue-req: 0, 
[2025-07-28 00:57:21 DP1 TP0] Decode batch. #running-req: 1, #token: 584, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.21, #queue-req: 0, 
[2025-07-28 00:57:21 DP0 TP0] Decode batch. #running-req: 1, #token: 498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.92, #queue-req: 0, 
[2025-07-28 00:57:21 DP1 TP0] Decode batch. #running-req: 1, #token: 624, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.79, #queue-req: 0, 
[2025-07-28 00:57:21 DP0 TP0] Decode batch. #running-req: 1, #token: 538, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.60, #queue-req: 0, 
[2025-07-28 00:57:21 DP1 TP0] Decode batch. #running-req: 1, #token: 664, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.47, #queue-req: 0, 
[2025-07-28 00:57:21 DP0 TP0] Decode batch. #running-req: 1, #token: 578, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.18, #queue-req: 0, 
[2025-07-28 00:57:21 DP1 TP0] Decode batch. #running-req: 1, #token: 704, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.61, #queue-req: 0, 
[2025-07-28 00:57:21 DP0 TP0] Decode batch. #running-req: 1, #token: 618, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.03, #queue-req: 0, 
[2025-07-28 00:57:21 DP1 TP0] Decode batch. #running-req: 1, #token: 744, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.05, #queue-req: 0, 
[2025-07-28 00:57:22 DP0 TP0] Decode batch. #running-req: 1, #token: 658, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.90, #queue-req: 0, 
[2025-07-28 00:57:22 DP1 TP0] Decode batch. #running-req: 1, #token: 784, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.60, #queue-req: 0, 
[2025-07-28 00:57:22] INFO:     127.0.0.1:37720 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:57:22 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 129, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:57:22 DP0 TP0] Decode batch. #running-req: 1, #token: 698, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.44, #queue-req: 0, 
[2025-07-28 00:57:22 DP1 TP0] Decode batch. #running-req: 1, #token: 246, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.74, #queue-req: 0, 
[2025-07-28 00:57:22 DP0 TP0] Decode batch. #running-req: 1, #token: 738, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.90, #queue-req: 0, 
[2025-07-28 00:57:22 DP1 TP0] Decode batch. #running-req: 1, #token: 286, token usage: 0.00, cuda graph: True, gen throughput (token/s): 337.62, #queue-req: 0, 
[2025-07-28 00:57:22] INFO:     127.0.0.1:37722 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:57:22 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 91, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:57:22 DP1 TP0] Decode batch. #running-req: 1, #token: 326, token usage: 0.00, cuda graph: True, gen throughput (token/s): 339.00, #queue-req: 0, 
[2025-07-28 00:57:22 DP0 TP0] Decode batch. #running-req: 1, #token: 220, token usage: 0.00, cuda graph: True, gen throughput (token/s): 195.27, #queue-req: 0, 
[2025-07-28 00:57:22 DP1 TP0] Decode batch. #running-req: 1, #token: 366, token usage: 0.00, cuda graph: True, gen throughput (token/s): 333.36, #queue-req: 0, 
[2025-07-28 00:57:22 DP0 TP0] Decode batch. #running-req: 1, #token: 260, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.62, #queue-req: 0, 
[2025-07-28 00:57:22 DP1 TP0] Decode batch. #running-req: 1, #token: 406, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.27, #queue-req: 0, 
[2025-07-28 00:57:22 DP0 TP0] Decode batch. #running-req: 1, #token: 300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.16, #queue-req: 0, 
[2025-07-28 00:57:22 DP1 TP0] Decode batch. #running-req: 1, #token: 446, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.31, #queue-req: 0, 
[2025-07-28 00:57:22 DP0 TP0] Decode batch. #running-req: 1, #token: 340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.30, #queue-req: 0, 
[2025-07-28 00:57:23 DP1 TP0] Decode batch. #running-req: 1, #token: 486, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.38, #queue-req: 0, 
[2025-07-28 00:57:23 DP0 TP0] Decode batch. #running-req: 1, #token: 380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.87, #queue-req: 0, 
[2025-07-28 00:57:23 DP1 TP0] Decode batch. #running-req: 1, #token: 526, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.79, #queue-req: 0, 
[2025-07-28 00:57:23 DP0 TP0] Decode batch. #running-req: 1, #token: 420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.57, #queue-req: 0, 
[2025-07-28 00:57:23 DP1 TP0] Decode batch. #running-req: 1, #token: 566, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.29, #queue-req: 0, 
[2025-07-28 00:57:23 DP0 TP0] Decode batch. #running-req: 1, #token: 460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.90, #queue-req: 0, 
[2025-07-28 00:57:23 DP1 TP0] Decode batch. #running-req: 1, #token: 606, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.91, #queue-req: 0, 
[2025-07-28 00:57:23 DP0 TP0] Decode batch. #running-req: 1, #token: 500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.49, #queue-req: 0, 
[2025-07-28 00:57:23 DP1 TP0] Decode batch. #running-req: 1, #token: 646, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.33, #queue-req: 0, 
[2025-07-28 00:57:23 DP0 TP0] Decode batch. #running-req: 1, #token: 540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.61, #queue-req: 0, 
[2025-07-28 00:57:23 DP1 TP0] Decode batch. #running-req: 1, #token: 686, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.09, #queue-req: 0, 
[2025-07-28 00:57:23 DP0 TP0] Decode batch. #running-req: 1, #token: 580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.75, #queue-req: 0, 
[2025-07-28 00:57:23 DP1 TP0] Decode batch. #running-req: 1, #token: 726, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.57, #queue-req: 0, 
[2025-07-28 00:57:23 DP0 TP0] Decode batch. #running-req: 1, #token: 620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.51, #queue-req: 0, 
[2025-07-28 00:57:23 DP1 TP0] Decode batch. #running-req: 1, #token: 766, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.46, #queue-req: 0, 
[2025-07-28 00:57:23 DP0 TP0] Decode batch. #running-req: 1, #token: 660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.63, #queue-req: 0, 
[2025-07-28 00:57:24 DP1 TP0] Decode batch. #running-req: 1, #token: 806, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.14, #queue-req: 0, 
[2025-07-28 00:57:24 DP0 TP0] Decode batch. #running-req: 1, #token: 700, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.99, #queue-req: 0, 
[2025-07-28 00:57:24 DP1 TP0] Decode batch. #running-req: 1, #token: 846, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.63, #queue-req: 0, 
[2025-07-28 00:57:24 DP0 TP0] Decode batch. #running-req: 1, #token: 740, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.19, #queue-req: 0, 
[2025-07-28 00:57:24 DP1 TP0] Decode batch. #running-req: 1, #token: 886, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.85, #queue-req: 0, 
[2025-07-28 00:57:24 DP0 TP0] Decode batch. #running-req: 1, #token: 780, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.51, #queue-req: 0, 
[2025-07-28 00:57:24] INFO:     127.0.0.1:37736 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:57:24 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 92, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:57:24 DP0 TP0] Decode batch. #running-req: 1, #token: 820, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.70, #queue-req: 0, 
[2025-07-28 00:57:24 DP1 TP0] Decode batch. #running-req: 1, #token: 256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.27, #queue-req: 0, 
[2025-07-28 00:57:24 DP1 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 328.33, #queue-req: 0, 
[2025-07-28 00:57:24 DP0 TP0] Decode batch. #running-req: 1, #token: 860, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.77, #queue-req: 0, 
[2025-07-28 00:57:24 DP1 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.75, #queue-req: 0, 
[2025-07-28 00:57:24 DP0 TP0] Decode batch. #running-req: 1, #token: 900, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.57, #queue-req: 0, 
[2025-07-28 00:57:24 DP1 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.13, #queue-req: 0, 
[2025-07-28 00:57:24 DP0 TP0] Decode batch. #running-req: 1, #token: 0, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.88, #queue-req: 0, 
[2025-07-28 00:57:24] INFO:     127.0.0.1:37536 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:57:24 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 103, #cached-token: 91, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:57:24 DP1 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 320.03, #queue-req: 0, 
[2025-07-28 00:57:25 DP0 TP0] Decode batch. #running-req: 1, #token: 235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 203.68, #queue-req: 0, 
[2025-07-28 00:57:25 DP1 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.00, #queue-req: 0, 
[2025-07-28 00:57:25 DP0 TP0] Decode batch. #running-req: 1, #token: 275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 338.50, #queue-req: 0, 
[2025-07-28 00:57:25 DP1 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.58, #queue-req: 0, 
[2025-07-28 00:57:25 DP0 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 335.77, #queue-req: 0, 
[2025-07-28 00:57:25 DP1 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.32, #queue-req: 0, 
[2025-07-28 00:57:25 DP0 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.04, #queue-req: 0, 
[2025-07-28 00:57:25 DP1 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.45, #queue-req: 0, 
[2025-07-28 00:57:25 DP0 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.90, #queue-req: 0, 
[2025-07-28 00:57:25 DP1 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.53, #queue-req: 0, 
[2025-07-28 00:57:25 DP0 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.91, #queue-req: 0, 
[2025-07-28 00:57:25 DP1 TP0] Decode batch. #running-req: 1, #token: 656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.69, #queue-req: 0, 
[2025-07-28 00:57:25 DP0 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.96, #queue-req: 0, 
[2025-07-28 00:57:25 DP1 TP0] Decode batch. #running-req: 1, #token: 696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.44, #queue-req: 0, 
[2025-07-28 00:57:25 DP0 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.24, #queue-req: 0, 
[2025-07-28 00:57:26 DP1 TP0] Decode batch. #running-req: 1, #token: 736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.22, #queue-req: 0, 
[2025-07-28 00:57:26 DP0 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.44, #queue-req: 0, 
[2025-07-28 00:57:26 DP1 TP0] Decode batch. #running-req: 1, #token: 776, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.91, #queue-req: 0, 
[2025-07-28 00:57:26 DP0 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.94, #queue-req: 0, 
[2025-07-28 00:57:26 DP1 TP0] Decode batch. #running-req: 1, #token: 816, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.11, #queue-req: 0, 
[2025-07-28 00:57:26 DP0 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.89, #queue-req: 0, 
[2025-07-28 00:57:26 DP1 TP0] Decode batch. #running-req: 1, #token: 856, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.86, #queue-req: 0, 
[2025-07-28 00:57:26 DP0 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.08, #queue-req: 0, 
[2025-07-28 00:57:26 DP1 TP0] Decode batch. #running-req: 1, #token: 896, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.24, #queue-req: 0, 
[2025-07-28 00:57:26] INFO:     127.0.0.1:37552 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:57:26 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 89, #cached-token: 91, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:57:26] INFO:     127.0.0.1:37540 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:57:26 DP1 TP0] Decode batch. #running-req: 1, #token: 213, token usage: 0.00, cuda graph: True, gen throughput (token/s): 344.26, #queue-req: 0, 
[2025-07-28 00:57:26 DP1 TP0] Decode batch. #running-req: 1, #token: 253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 323.03, #queue-req: 0, 
[2025-07-28 00:57:26 DP1 TP0] Decode batch. #running-req: 1, #token: 293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 337.53, #queue-req: 0, 
[2025-07-28 00:57:27 DP1 TP0] Decode batch. #running-req: 1, #token: 333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 337.18, #queue-req: 0, 
[2025-07-28 00:57:27 DP1 TP0] Decode batch. #running-req: 1, #token: 373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 333.00, #queue-req: 0, 
[2025-07-28 00:57:27 DP1 TP0] Decode batch. #running-req: 1, #token: 413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 331.75, #queue-req: 0, 
[2025-07-28 00:57:27 DP1 TP0] Decode batch. #running-req: 1, #token: 453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.12, #queue-req: 0, 
[2025-07-28 00:57:27 DP1 TP0] Decode batch. #running-req: 1, #token: 493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.07, #queue-req: 0, 
[2025-07-28 00:57:27 DP1 TP0] Decode batch. #running-req: 1, #token: 533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.53, #queue-req: 0, 
[2025-07-28 00:57:27 DP1 TP0] Decode batch. #running-req: 1, #token: 573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.52, #queue-req: 0, 
[2025-07-28 00:57:27 DP1 TP0] Decode batch. #running-req: 1, #token: 613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.22, #queue-req: 0, 
[2025-07-28 00:57:28 DP1 TP0] Decode batch. #running-req: 1, #token: 653, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.05, #queue-req: 0, 
[2025-07-28 00:57:28 DP1 TP0] Decode batch. #running-req: 1, #token: 693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.29, #queue-req: 0, 
[2025-07-28 00:57:28 DP1 TP0] Decode batch. #running-req: 1, #token: 733, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.66, #queue-req: 0, 
[2025-07-28 00:57:28 DP1 TP0] Decode batch. #running-req: 1, #token: 773, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.83, #queue-req: 0, 
[2025-07-28 00:57:28 DP1 TP0] Decode batch. #running-req: 1, #token: 813, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.18, #queue-req: 0, 
[2025-07-28 00:57:28 DP1 TP0] Decode batch. #running-req: 1, #token: 853, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.01, #queue-req: 0, 
[2025-07-28 00:57:28 DP1 TP0] Decode batch. #running-req: 1, #token: 893, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.81, #queue-req: 0, 
[2025-07-28 00:57:28 DP1 TP0] Decode batch. #running-req: 1, #token: 933, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.87, #queue-req: 0, 
[2025-07-28 00:57:29] INFO:     127.0.0.1:37568 - "POST /v1/chat/completions HTTP/1.1" 200 OK

[2025-07-28 00:36:39] server_args=ServerArgs(model_path='/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-7B-Instruct', tokenizer_path='/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-7B-Instruct', tokenizer_mode='auto', skip_tokenizer_init=False, load_format='auto', model_loader_extra_config='{}', trust_remote_code=False, context_length=32768, is_embedding=False, enable_multimodal=None, revision=None, model_impl='auto', host='127.0.0.1', port=8004, skip_server_warmup=False, warmups=None, nccl_port=None, dtype='auto', quantization=None, quantization_param_path=None, kv_cache_dtype='auto', mem_fraction_static=0.85, max_running_requests=None, max_total_tokens=None, chunked_prefill_size=8192, max_prefill_tokens=16384, schedule_policy='fcfs', schedule_conservativeness=1.0, cpu_offload_gb=0, page_size=1, hybrid_kvcache_ratio=None, swa_full_tokens_ratio=0.8, disable_hybrid_swa_memory=False, device='cuda', tp_size=4, pp_size=1, max_micro_batch_size=None, stream_interval=1, stream_output=False, random_seed=33391795, constrained_json_whitespace_pattern=None, watchdog_timeout=300, dist_timeout=None, download_dir=None, base_gpu_id=0, gpu_id_step=1, sleep_on_idle=False, log_level='info', log_level_http=None, log_requests=False, log_requests_level=0, crash_dump_folder=None, show_time_cost=True, enable_metrics=True, enable_metrics_for_all_schedulers=False, bucket_time_to_first_token=None, bucket_inter_token_latency=None, bucket_e2e_request_latency=None, collect_tokens_histogram=False, decode_log_interval=40, enable_request_time_stats_logging=False, kv_events_config=None, api_key=None, served_model_name='/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-7B-Instruct', chat_template=None, completion_template=None, file_storage_path='sglang_storage', enable_cache_report=False, reasoning_parser=None, tool_call_parser=None, dp_size=2, load_balance_method='round_robin', dist_init_addr=None, nnodes=1, node_rank=0, json_model_override_args='{}', preferred_sampling_params=None, enable_lora=None, max_lora_rank=None, lora_target_modules=None, lora_paths=None, max_loras_per_batch=8, lora_backend='triton', attention_backend=None, sampling_backend='flashinfer', grammar_backend='xgrammar', mm_attention_backend=None, speculative_algorithm=None, speculative_draft_model_path=None, speculative_num_steps=None, speculative_eagle_topk=None, speculative_num_draft_tokens=None, speculative_accept_threshold_single=1.0, speculative_accept_threshold_acc=1.0, speculative_token_map=None, ep_size=1, enable_ep_moe=False, enable_deepep_moe=False, enable_flashinfer_moe=False, enable_flashinfer_allreduce_fusion=False, deepep_mode='auto', ep_num_redundant_experts=0, ep_dispatch_algorithm='static', init_expert_location='trivial', enable_eplb=False, eplb_algorithm='auto', eplb_rebalance_num_iterations=1000, eplb_rebalance_layers_per_chunk=None, expert_distribution_recorder_mode=None, expert_distribution_recorder_buffer_size=1000, enable_expert_distribution_metrics=False, deepep_config=None, moe_dense_tp_size=None, enable_hierarchical_cache=False, hicache_ratio=2.0, hicache_size=0, hicache_write_policy='write_through_selective', hicache_io_backend='', hicache_storage_backend=None, enable_double_sparsity=False, ds_channel_config_path=None, ds_heavy_channel_num=32, ds_heavy_token_num=256, ds_heavy_channel_type='qk', ds_sparse_decode_threshold=4096, disable_radix_cache=False, cuda_graph_max_bs=None, cuda_graph_bs=None, disable_cuda_graph=False, disable_cuda_graph_padding=False, enable_profile_cuda_graph=False, enable_nccl_nvls=False, enable_tokenizer_batch_encode=False, disable_outlines_disk_cache=False, disable_custom_all_reduce=False, enable_mscclpp=False, disable_overlap_schedule=False, enable_mixed_chunk=False, enable_dp_attention=False, enable_dp_lm_head=False, enable_two_batch_overlap=False, enable_torch_compile=False, torch_compile_max_bs=32, torchao_config='', enable_nan_detection=False, enable_p2p_check=False, triton_attention_reduce_in_fp32=False, triton_attention_num_kv_splits=8, num_continuous_decode_steps=1, delete_ckpt_after_loading=False, enable_memory_saver=False, allow_auto_truncate=False, enable_custom_logit_processor=False, flashinfer_mla_disable_ragged=False, disable_shared_experts_fusion=False, disable_chunked_prefix_cache=False, disable_fast_image_processor=False, enable_return_hidden_states=False, enable_triton_kernel_moe=False, debug_tensor_dump_output_folder=None, debug_tensor_dump_input_file=None, debug_tensor_dump_inject=False, debug_tensor_dump_prefill_only=False, disaggregation_mode='null', disaggregation_transfer_backend='mooncake', disaggregation_bootstrap_port=8998, disaggregation_decode_tp=None, disaggregation_decode_dp=None, disaggregation_prefill_pp=1, disaggregation_ib_device=None, num_reserved_decode_tokens=512, pdlb_url=None, custom_weight_loader=[], weight_loader_disable_mmap=False, enable_pdmux=False, sm_group_num=3)
[2025-07-28 00:36:45] Launch DP0 starting at GPU #0.
[2025-07-28 00:36:45] Launch DP1 starting at GPU #4.
[2025-07-28 00:36:52 DP1 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 00:36:52 DP1 TP0] Init torch distributed begin.
[2025-07-28 00:36:53 DP0 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 00:36:53 DP0 TP0] Init torch distributed begin.
[2025-07-28 00:36:54 DP1 TP0] sglang is using nccl==2.26.2
[2025-07-28 00:36:54 DP0 TP0] sglang is using nccl==2.26.2
[2025-07-28 00:36:55 DP1 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 00:36:56 DP0 TP0] Init torch distributed ends. mem usage=0.64 GB
[2025-07-28 00:36:56 DP1 TP0] Load weight begin. avail mem=78.21 GB
[2025-07-28 00:36:56 DP0 TP0] Load weight begin. avail mem=78.21 GB

Loading safetensors checkpoint shards:   0% Completed | 0/4 [00:00<?, ?it/s]

Loading safetensors checkpoint shards:   0% Completed | 0/4 [00:00<?, ?it/s]

Loading safetensors checkpoint shards:  25% Completed | 1/4 [00:01<00:03,  1.20s/it]

Loading safetensors checkpoint shards:  25% Completed | 1/4 [00:01<00:03,  1.25s/it]

Loading safetensors checkpoint shards:  50% Completed | 2/4 [00:02<00:02,  1.21s/it]

Loading safetensors checkpoint shards:  50% Completed | 2/4 [00:02<00:02,  1.19s/it]

Loading safetensors checkpoint shards:  75% Completed | 3/4 [00:03<00:01,  1.24s/it]

Loading safetensors checkpoint shards:  75% Completed | 3/4 [00:03<00:01,  1.25s/it]

Loading safetensors checkpoint shards: 100% Completed | 4/4 [00:05<00:00,  1.29s/it]

Loading safetensors checkpoint shards: 100% Completed | 4/4 [00:05<00:00,  1.29s/it]

Loading safetensors checkpoint shards: 100% Completed | 4/4 [00:05<00:00,  1.26s/it]


Loading safetensors checkpoint shards: 100% Completed | 4/4 [00:05<00:00,  1.27s/it]

[2025-07-28 00:37:01 DP1 TP0] Load weight end. type=Qwen2ForCausalLM, dtype=torch.bfloat16, avail mem=74.40 GB, mem usage=3.80 GB.
[2025-07-28 00:37:01 DP0 TP0] Load weight end. type=Qwen2ForCausalLM, dtype=torch.bfloat16, avail mem=74.40 GB, mem usage=3.80 GB.
[2025-07-28 00:37:01 DP1 TP3] KV Cache is allocated. #tokens: 4687919, K size: 31.30 GB, V size: 31.30 GB
[2025-07-28 00:37:01 DP1 TP0] KV Cache is allocated. #tokens: 4687919, K size: 31.30 GB, V size: 31.30 GB
[2025-07-28 00:37:01 DP0 TP0] KV Cache is allocated. #tokens: 4687919, K size: 31.30 GB, V size: 31.30 GB
[2025-07-28 00:37:01 DP1 TP0] Memory pool end. avail mem=11.19 GB
[2025-07-28 00:37:01 DP1 TP2] KV Cache is allocated. #tokens: 4687919, K size: 31.30 GB, V size: 31.30 GB
[2025-07-28 00:37:01 DP0 TP1] KV Cache is allocated. #tokens: 4687919, K size: 31.30 GB, V size: 31.30 GB
[2025-07-28 00:37:01 DP0 TP2] KV Cache is allocated. #tokens: 4687919, K size: 31.30 GB, V size: 31.30 GB
[2025-07-28 00:37:01 DP1 TP1] KV Cache is allocated. #tokens: 4687919, K size: 31.30 GB, V size: 31.30 GB
[2025-07-28 00:37:01 DP0 TP0] Memory pool end. avail mem=11.19 GB
[2025-07-28 00:37:01 DP0 TP3] KV Cache is allocated. #tokens: 4687919, K size: 31.30 GB, V size: 31.30 GB
[2025-07-28 00:37:02 DP0 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.58 GB
[2025-07-28 00:37:02 DP1 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.58 GB
[2025-07-28 00:37:02 DP0 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.56 GB):   0%|          | 0/23 [00:00<?, ?it/s][2025-07-28 00:37:02 DP1 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.56 GB):   0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.56 GB):   4%|▍         | 1/23 [00:00<00:21,  1.01it/s]
Capturing batches (bs=152 avail_mem=10.30 GB):   4%|▍         | 1/23 [00:00<00:21,  1.01it/s]
Capturing batches (bs=160 avail_mem=10.56 GB):   4%|▍         | 1/23 [00:01<00:23,  1.06s/it]
Capturing batches (bs=152 avail_mem=10.30 GB):   4%|▍         | 1/23 [00:01<00:23,  1.06s/it]
Capturing batches (bs=152 avail_mem=10.30 GB):   9%|▊         | 2/23 [00:01<00:12,  1.62it/s]
Capturing batches (bs=144 avail_mem=10.19 GB):   9%|▊         | 2/23 [00:01<00:12,  1.62it/s]
Capturing batches (bs=152 avail_mem=10.30 GB):   9%|▊         | 2/23 [00:01<00:14,  1.50it/s]
Capturing batches (bs=144 avail_mem=10.19 GB):   9%|▊         | 2/23 [00:01<00:14,  1.50it/s]
Capturing batches (bs=144 avail_mem=10.19 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.79it/s]
Capturing batches (bs=136 avail_mem=10.09 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.79it/s]
Capturing batches (bs=144 avail_mem=10.19 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.68it/s]
Capturing batches (bs=136 avail_mem=10.09 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.68it/s]
Capturing batches (bs=136 avail_mem=10.09 GB):  17%|█▋        | 4/23 [00:02<00:08,  2.19it/s]
Capturing batches (bs=128 avail_mem=9.99 GB):  17%|█▋        | 4/23 [00:02<00:08,  2.19it/s] 
Capturing batches (bs=136 avail_mem=10.09 GB):  17%|█▋        | 4/23 [00:02<00:09,  2.07it/s]
Capturing batches (bs=128 avail_mem=9.99 GB):  17%|█▋        | 4/23 [00:02<00:09,  2.07it/s] 
Capturing batches (bs=128 avail_mem=9.99 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.51it/s]
Capturing batches (bs=120 avail_mem=9.91 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.51it/s]
Capturing batches (bs=128 avail_mem=9.99 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.34it/s]
Capturing batches (bs=120 avail_mem=9.91 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.34it/s]
Capturing batches (bs=120 avail_mem=9.91 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.76it/s]
Capturing batches (bs=112 avail_mem=9.82 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.76it/s]
Capturing batches (bs=120 avail_mem=9.91 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.64it/s]
Capturing batches (bs=112 avail_mem=9.82 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.64it/s]
Capturing batches (bs=112 avail_mem=9.82 GB):  30%|███       | 7/23 [00:03<00:05,  2.93it/s]
Capturing batches (bs=104 avail_mem=9.75 GB):  30%|███       | 7/23 [00:03<00:05,  2.93it/s]
Capturing batches (bs=112 avail_mem=9.82 GB):  30%|███       | 7/23 [00:03<00:05,  2.78it/s]
Capturing batches (bs=104 avail_mem=9.75 GB):  30%|███       | 7/23 [00:03<00:05,  2.78it/s]
Capturing batches (bs=104 avail_mem=9.75 GB):  35%|███▍      | 8/23 [00:03<00:04,  3.04it/s]
Capturing batches (bs=96 avail_mem=9.66 GB):  35%|███▍      | 8/23 [00:03<00:04,  3.04it/s] 
Capturing batches (bs=104 avail_mem=9.75 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.95it/s]
Capturing batches (bs=96 avail_mem=9.66 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.95it/s] 
Capturing batches (bs=96 avail_mem=9.66 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.14it/s]
Capturing batches (bs=88 avail_mem=9.60 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.14it/s]
Capturing batches (bs=96 avail_mem=9.66 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.05it/s]
Capturing batches (bs=88 avail_mem=9.60 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.05it/s]
Capturing batches (bs=88 avail_mem=9.60 GB):  43%|████▎     | 10/23 [00:03<00:04,  3.21it/s]
Capturing batches (bs=80 avail_mem=9.53 GB):  43%|████▎     | 10/23 [00:03<00:04,  3.21it/s]
Capturing batches (bs=88 avail_mem=9.60 GB):  43%|████▎     | 10/23 [00:04<00:04,  3.12it/s]
Capturing batches (bs=80 avail_mem=9.53 GB):  43%|████▎     | 10/23 [00:04<00:04,  3.12it/s]
Capturing batches (bs=80 avail_mem=9.53 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.28it/s]
Capturing batches (bs=72 avail_mem=9.52 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.28it/s]
Capturing batches (bs=80 avail_mem=9.53 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.17it/s]
Capturing batches (bs=72 avail_mem=9.52 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.17it/s]
Capturing batches (bs=72 avail_mem=9.52 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.32it/s]
Capturing batches (bs=64 avail_mem=9.46 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.32it/s]
Capturing batches (bs=72 avail_mem=9.52 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.21it/s]
Capturing batches (bs=64 avail_mem=9.46 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.21it/s]
Capturing batches (bs=64 avail_mem=9.46 GB):  57%|█████▋    | 13/23 [00:04<00:02,  3.35it/s]
Capturing batches (bs=56 avail_mem=9.42 GB):  57%|█████▋    | 13/23 [00:04<00:02,  3.35it/s]
Capturing batches (bs=64 avail_mem=9.46 GB):  57%|█████▋    | 13/23 [00:05<00:03,  3.24it/s]
Capturing batches (bs=56 avail_mem=9.42 GB):  57%|█████▋    | 13/23 [00:05<00:03,  3.24it/s]
Capturing batches (bs=56 avail_mem=9.42 GB):  61%|██████    | 14/23 [00:05<00:02,  3.38it/s]
Capturing batches (bs=48 avail_mem=9.36 GB):  61%|██████    | 14/23 [00:05<00:02,  3.38it/s]
Capturing batches (bs=56 avail_mem=9.42 GB):  61%|██████    | 14/23 [00:05<00:02,  3.25it/s]
Capturing batches (bs=48 avail_mem=9.36 GB):  61%|██████    | 14/23 [00:05<00:02,  3.25it/s]
Capturing batches (bs=48 avail_mem=9.36 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.40it/s]
Capturing batches (bs=40 avail_mem=9.35 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.40it/s]
Capturing batches (bs=48 avail_mem=9.36 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.27it/s]
Capturing batches (bs=40 avail_mem=9.35 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.27it/s]
Capturing batches (bs=40 avail_mem=9.35 GB):  70%|██████▉   | 16/23 [00:05<00:02,  3.41it/s]
Capturing batches (bs=32 avail_mem=9.30 GB):  70%|██████▉   | 16/23 [00:05<00:02,  3.41it/s]
Capturing batches (bs=40 avail_mem=9.35 GB):  70%|██████▉   | 16/23 [00:05<00:02,  3.28it/s]
Capturing batches (bs=32 avail_mem=9.30 GB):  70%|██████▉   | 16/23 [00:05<00:02,  3.28it/s]
Capturing batches (bs=32 avail_mem=9.30 GB):  74%|███████▍  | 17/23 [00:05<00:01,  3.36it/s]
Capturing batches (bs=24 avail_mem=9.30 GB):  74%|███████▍  | 17/23 [00:05<00:01,  3.36it/s]
Capturing batches (bs=32 avail_mem=9.30 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.24it/s]
Capturing batches (bs=24 avail_mem=9.30 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.24it/s]
Capturing batches (bs=24 avail_mem=9.30 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.38it/s]
Capturing batches (bs=16 avail_mem=9.26 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.38it/s]
Capturing batches (bs=16 avail_mem=9.26 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.39it/s]
Capturing batches (bs=8 avail_mem=9.25 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.39it/s] 
Capturing batches (bs=24 avail_mem=9.30 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.18it/s]
Capturing batches (bs=16 avail_mem=9.26 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.18it/s]
Capturing batches (bs=16 avail_mem=9.26 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.19it/s]
Capturing batches (bs=8 avail_mem=9.25 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.19it/s] 
Capturing batches (bs=8 avail_mem=9.25 GB):  87%|████████▋ | 20/23 [00:06<00:00,  3.32it/s]
Capturing batches (bs=4 avail_mem=9.23 GB):  87%|████████▋ | 20/23 [00:06<00:00,  3.32it/s]
Capturing batches (bs=8 avail_mem=9.25 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.28it/s]
Capturing batches (bs=4 avail_mem=9.23 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.28it/s]
Capturing batches (bs=4 avail_mem=9.23 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.36it/s]
Capturing batches (bs=2 avail_mem=9.23 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.36it/s]
Capturing batches (bs=4 avail_mem=9.23 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.39it/s]
Capturing batches (bs=2 avail_mem=9.23 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.39it/s]
Capturing batches (bs=2 avail_mem=9.23 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.34it/s]
Capturing batches (bs=1 avail_mem=9.20 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.34it/s]
Capturing batches (bs=2 avail_mem=9.23 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.35it/s]
Capturing batches (bs=1 avail_mem=9.20 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.35it/s]
Capturing batches (bs=1 avail_mem=9.20 GB): 100%|██████████| 23/23 [00:07<00:00,  3.38it/s]
Capturing batches (bs=1 avail_mem=9.20 GB): 100%|██████████| 23/23 [00:07<00:00,  2.97it/s]
[2025-07-28 00:37:10 DP1 TP3] Registering 1311 cuda graph addresses
[2025-07-28 00:37:10 DP1 TP0] Registering 1311 cuda graph addresses
[2025-07-28 00:37:10 DP1 TP1] Registering 1311 cuda graph addresses
[2025-07-28 00:37:10 DP1 TP2] Registering 1311 cuda graph addresses
[2025-07-28 00:37:10 DP1 TP0] Capture cuda graph end. Time elapsed: 8.04 s. mem usage=1.38 GB. avail mem=9.20 GB.

Capturing batches (bs=1 avail_mem=9.20 GB): 100%|██████████| 23/23 [00:08<00:00,  3.35it/s]
Capturing batches (bs=1 avail_mem=9.20 GB): 100%|██████████| 23/23 [00:08<00:00,  2.86it/s]
[2025-07-28 00:37:10 DP0 TP0] Registering 1311 cuda graph addresses
[2025-07-28 00:37:10 DP0 TP3] Registering 1311 cuda graph addresses
[2025-07-28 00:37:10 DP0 TP2] Registering 1311 cuda graph addresses
[2025-07-28 00:37:10 DP0 TP1] Registering 1311 cuda graph addresses
[2025-07-28 00:37:10 DP0 TP0] Capture cuda graph end. Time elapsed: 8.29 s. mem usage=1.38 GB. avail mem=9.20 GB.
[2025-07-28 00:37:10 DP1 TP0] max_total_num_tokens=4687919, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=9.20 GB
[2025-07-28 00:37:10 DP0 TP0] max_total_num_tokens=4687919, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=9.20 GB
[2025-07-28 00:37:11] INFO:     Started server process [1763257]
[2025-07-28 00:37:11] INFO:     Waiting for application startup.
[2025-07-28 00:37:11] INFO:     Application startup complete.
[2025-07-28 00:37:11] INFO:     Uvicorn running on http://127.0.0.1:8004 (Press CTRL+C to quit)
[2025-07-28 00:37:12] INFO:     127.0.0.1:40776 - "GET /get_model_info HTTP/1.1" 200 OK
[2025-07-28 00:37:12 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:12 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:12] INFO:     127.0.0.1:42930 - "GET /health HTTP/1.1" 200 OK
[2025-07-28 00:37:13 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 215, #cached-token: 0, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:37:13 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 256, #cached-token: 0, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:37:13] INFO:     127.0.0.1:40790 - "POST /generate HTTP/1.1" 200 OK
[2025-07-28 00:37:13] The server is fired up and ready to roll!
[2025-07-28 00:37:13 DP1 TP0] Decode batch. #running-req: 1, #token: 255, token usage: 0.00, cuda graph: True, gen throughput (token/s): 15.13, #queue-req: 0, 
[2025-07-28 00:37:13 DP0 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 16.14, #queue-req: 0, 
[2025-07-28 00:37:13 DP1 TP0] Decode batch. #running-req: 1, #token: 295, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.59, #queue-req: 0, 
[2025-07-28 00:37:13 DP0 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 228.97, #queue-req: 0, 
[2025-07-28 00:37:13 DP1 TP0] Decode batch. #running-req: 1, #token: 335, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.74, #queue-req: 0, 
[2025-07-28 00:37:13 DP0 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 235.51, #queue-req: 0, 
[2025-07-28 00:37:14 DP1 TP0] Decode batch. #running-req: 1, #token: 375, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.52, #queue-req: 0, 
[2025-07-28 00:37:14 DP0 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.26, #queue-req: 0, 
[2025-07-28 00:37:14 DP1 TP0] Decode batch. #running-req: 1, #token: 415, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.87, #queue-req: 0, 
[2025-07-28 00:37:14 DP0 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.32, #queue-req: 0, 
[2025-07-28 00:37:14 DP1 TP0] Decode batch. #running-req: 1, #token: 455, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.85, #queue-req: 0, 
[2025-07-28 00:37:14 DP0 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.57, #queue-req: 0, 
[2025-07-28 00:37:14 DP1 TP0] Decode batch. #running-req: 1, #token: 495, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.65, #queue-req: 0, 
[2025-07-28 00:37:14 DP0 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.78, #queue-req: 0, 
[2025-07-28 00:37:14 DP1 TP0] Decode batch. #running-req: 1, #token: 535, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.82, #queue-req: 0, 
[2025-07-28 00:37:14 DP0 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 234.92, #queue-req: 0, 
[2025-07-28 00:37:14 DP1 TP0] Decode batch. #running-req: 1, #token: 575, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.77, #queue-req: 0, 
[2025-07-28 00:37:14 DP0 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.58, #queue-req: 0, 
[2025-07-28 00:37:15 DP1 TP0] Decode batch. #running-req: 1, #token: 615, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.22, #queue-req: 0, 
[2025-07-28 00:37:15 DP0 TP0] Decode batch. #running-req: 1, #token: 656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.66, #queue-req: 0, 
[2025-07-28 00:37:15 DP1 TP0] Decode batch. #running-req: 1, #token: 655, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.76, #queue-req: 0, 
[2025-07-28 00:37:15 DP0 TP0] Decode batch. #running-req: 1, #token: 696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.84, #queue-req: 0, 
[2025-07-28 00:37:15 DP1 TP0] Decode batch. #running-req: 1, #token: 695, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.42, #queue-req: 0, 
[2025-07-28 00:37:15 DP0 TP0] Decode batch. #running-req: 1, #token: 736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.39, #queue-req: 0, 
[2025-07-28 00:37:15 DP1 TP0] Decode batch. #running-req: 1, #token: 735, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.55, #queue-req: 0, 
[2025-07-28 00:37:15] INFO:     127.0.0.1:42944 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:15 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 106, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:37:15 DP0 TP0] Decode batch. #running-req: 2, #token: 867, token usage: 0.00, cuda graph: True, gen throughput (token/s): 110.67, #queue-req: 0, 
[2025-07-28 00:37:15] INFO:     127.0.0.1:42942 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:16 DP0 TP0] Decode batch. #running-req: 1, #token: 237, token usage: 0.00, cuda graph: True, gen throughput (token/s): 437.54, #queue-req: 0, 
[2025-07-28 00:37:16 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 207, #cached-token: 107, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:16 DP0 TP0] Decode batch. #running-req: 1, #token: 277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.75, #queue-req: 0, 
[2025-07-28 00:37:16 DP0 TP0] Decode batch. #running-req: 1, #token: 317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.03, #queue-req: 0, 
[2025-07-28 00:37:16 DP1 TP0] Decode batch. #running-req: 1, #token: 345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 45.98, #queue-req: 0, 
[2025-07-28 00:37:16 DP0 TP0] Decode batch. #running-req: 1, #token: 357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.99, #queue-req: 0, 
[2025-07-28 00:37:16 DP1 TP0] Decode batch. #running-req: 1, #token: 385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.66, #queue-req: 0, 
[2025-07-28 00:37:16 DP0 TP0] Decode batch. #running-req: 1, #token: 397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.57, #queue-req: 0, 
[2025-07-28 00:37:16 DP1 TP0] Decode batch. #running-req: 1, #token: 425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.52, #queue-req: 0, 
[2025-07-28 00:37:16 DP0 TP0] Decode batch. #running-req: 1, #token: 437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.52, #queue-req: 0, 
[2025-07-28 00:37:16 DP1 TP0] Decode batch. #running-req: 1, #token: 465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.80, #queue-req: 0, 
[2025-07-28 00:37:17 DP0 TP0] Decode batch. #running-req: 1, #token: 477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.14, #queue-req: 0, 
[2025-07-28 00:37:17 DP1 TP0] Decode batch. #running-req: 1, #token: 505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.69, #queue-req: 0, 
[2025-07-28 00:37:17 DP0 TP0] Decode batch. #running-req: 1, #token: 517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.81, #queue-req: 0, 
[2025-07-28 00:37:17 DP1 TP0] Decode batch. #running-req: 1, #token: 545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.92, #queue-req: 0, 
[2025-07-28 00:37:17 DP0 TP0] Decode batch. #running-req: 1, #token: 557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.85, #queue-req: 0, 
[2025-07-28 00:37:17 DP1 TP0] Decode batch. #running-req: 1, #token: 585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.09, #queue-req: 0, 
[2025-07-28 00:37:17 DP0 TP0] Decode batch. #running-req: 1, #token: 597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.13, #queue-req: 0, 
[2025-07-28 00:37:17 DP1 TP0] Decode batch. #running-req: 1, #token: 625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.28, #queue-req: 0, 
[2025-07-28 00:37:17] INFO:     127.0.0.1:42960 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:17 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 107, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:17 DP1 TP0] Decode batch. #running-req: 1, #token: 665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.13, #queue-req: 0, 
[2025-07-28 00:37:17 DP0 TP0] Decode batch. #running-req: 1, #token: 251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 172.33, #queue-req: 0, 
[2025-07-28 00:37:17 DP1 TP0] Decode batch. #running-req: 1, #token: 705, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.93, #queue-req: 0, 
[2025-07-28 00:37:17 DP0 TP0] Decode batch. #running-req: 1, #token: 291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.77, #queue-req: 0, 
[2025-07-28 00:37:18 DP1 TP0] Decode batch. #running-req: 1, #token: 745, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.83, #queue-req: 0, 
[2025-07-28 00:37:18 DP0 TP0] Decode batch. #running-req: 1, #token: 331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.12, #queue-req: 0, 
[2025-07-28 00:37:18 DP1 TP0] Decode batch. #running-req: 1, #token: 785, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.59, #queue-req: 0, 
[2025-07-28 00:37:18 DP0 TP0] Decode batch. #running-req: 1, #token: 371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.20, #queue-req: 0, 
[2025-07-28 00:37:18 DP1 TP0] Decode batch. #running-req: 1, #token: 825, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.77, #queue-req: 0, 
[2025-07-28 00:37:18 DP0 TP0] Decode batch. #running-req: 1, #token: 411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.02, #queue-req: 0, 
[2025-07-28 00:37:18 DP1 TP0] Decode batch. #running-req: 1, #token: 865, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.77, #queue-req: 0, 
[2025-07-28 00:37:18 DP0 TP0] Decode batch. #running-req: 1, #token: 451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.00, #queue-req: 0, 
[2025-07-28 00:37:18] INFO:     127.0.0.1:42974 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:18 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 133, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:18 DP0 TP0] Decode batch. #running-req: 1, #token: 491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.78, #queue-req: 0, 
[2025-07-28 00:37:18 DP1 TP0] Decode batch. #running-req: 1, #token: 241, token usage: 0.00, cuda graph: True, gen throughput (token/s): 184.87, #queue-req: 0, 
[2025-07-28 00:37:18 DP0 TP0] Decode batch. #running-req: 1, #token: 531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.74, #queue-req: 0, 
[2025-07-28 00:37:18 DP1 TP0] Decode batch. #running-req: 1, #token: 281, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.70, #queue-req: 0, 
[2025-07-28 00:37:19 DP0 TP0] Decode batch. #running-req: 1, #token: 571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.61, #queue-req: 0, 
[2025-07-28 00:37:19 DP1 TP0] Decode batch. #running-req: 1, #token: 321, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.16, #queue-req: 0, 
[2025-07-28 00:37:19 DP0 TP0] Decode batch. #running-req: 1, #token: 611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.65, #queue-req: 0, 
[2025-07-28 00:37:19 DP1 TP0] Decode batch. #running-req: 1, #token: 361, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.55, #queue-req: 0, 
[2025-07-28 00:37:19 DP0 TP0] Decode batch. #running-req: 1, #token: 651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.14, #queue-req: 0, 
[2025-07-28 00:37:19 DP1 TP0] Decode batch. #running-req: 1, #token: 401, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.66, #queue-req: 0, 
[2025-07-28 00:37:19 DP0 TP0] Decode batch. #running-req: 1, #token: 691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.22, #queue-req: 0, 
[2025-07-28 00:37:19 DP1 TP0] Decode batch. #running-req: 1, #token: 441, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.93, #queue-req: 0, 
[2025-07-28 00:37:19] INFO:     127.0.0.1:42986 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:19 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 186, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:19 DP1 TP0] Decode batch. #running-req: 1, #token: 481, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.44, #queue-req: 0, 
[2025-07-28 00:37:19 DP0 TP0] Decode batch. #running-req: 1, #token: 318, token usage: 0.00, cuda graph: True, gen throughput (token/s): 179.07, #queue-req: 0, 
[2025-07-28 00:37:19 DP1 TP0] Decode batch. #running-req: 1, #token: 521, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.15, #queue-req: 0, 
[2025-07-28 00:37:19 DP0 TP0] Decode batch. #running-req: 1, #token: 358, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.91, #queue-req: 0, 
[2025-07-28 00:37:20 DP1 TP0] Decode batch. #running-req: 1, #token: 561, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.29, #queue-req: 0, 
[2025-07-28 00:37:20 DP0 TP0] Decode batch. #running-req: 1, #token: 398, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.40, #queue-req: 0, 
[2025-07-28 00:37:20 DP1 TP0] Decode batch. #running-req: 1, #token: 601, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.47, #queue-req: 0, 
[2025-07-28 00:37:20 DP0 TP0] Decode batch. #running-req: 1, #token: 438, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.08, #queue-req: 0, 
[2025-07-28 00:37:20 DP1 TP0] Decode batch. #running-req: 1, #token: 641, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.36, #queue-req: 0, 
[2025-07-28 00:37:20 DP0 TP0] Decode batch. #running-req: 1, #token: 478, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.22, #queue-req: 0, 
[2025-07-28 00:37:20 DP1 TP0] Decode batch. #running-req: 1, #token: 681, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.00, #queue-req: 0, 
[2025-07-28 00:37:20 DP0 TP0] Decode batch. #running-req: 1, #token: 518, token usage: 0.00, cuda graph: True, gen throughput (token/s): 233.18, #queue-req: 0, 
[2025-07-28 00:37:20 DP1 TP0] Decode batch. #running-req: 1, #token: 721, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.07, #queue-req: 0, 
[2025-07-28 00:37:20] INFO:     127.0.0.1:43000 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:20 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 173, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:20 DP0 TP0] Decode batch. #running-req: 1, #token: 558, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.16, #queue-req: 0, 
[2025-07-28 00:37:20 DP1 TP0] Decode batch. #running-req: 1, #token: 310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 182.48, #queue-req: 0, 
[2025-07-28 00:37:20 DP0 TP0] Decode batch. #running-req: 1, #token: 598, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.75, #queue-req: 0, 
[2025-07-28 00:37:21 DP1 TP0] Decode batch. #running-req: 1, #token: 350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.57, #queue-req: 0, 
[2025-07-28 00:37:21 DP0 TP0] Decode batch. #running-req: 1, #token: 638, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.18, #queue-req: 0, 
[2025-07-28 00:37:21 DP1 TP0] Decode batch. #running-req: 1, #token: 390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.62, #queue-req: 0, 
[2025-07-28 00:37:21 DP0 TP0] Decode batch. #running-req: 1, #token: 678, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.52, #queue-req: 0, 
[2025-07-28 00:37:21 DP1 TP0] Decode batch. #running-req: 1, #token: 430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.68, #queue-req: 0, 
[2025-07-28 00:37:21 DP0 TP0] Decode batch. #running-req: 1, #token: 718, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.28, #queue-req: 0, 
[2025-07-28 00:37:21 DP1 TP0] Decode batch. #running-req: 1, #token: 470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.26, #queue-req: 0, 
[2025-07-28 00:37:21 DP0 TP0] Decode batch. #running-req: 1, #token: 758, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.63, #queue-req: 0, 
[2025-07-28 00:37:21 DP1 TP0] Decode batch. #running-req: 1, #token: 510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 233.03, #queue-req: 0, 
[2025-07-28 00:37:21 DP0 TP0] Decode batch. #running-req: 1, #token: 798, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.90, #queue-req: 0, 
[2025-07-28 00:37:21 DP1 TP0] Decode batch. #running-req: 1, #token: 550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.26, #queue-req: 0, 
[2025-07-28 00:37:21] INFO:     127.0.0.1:43006 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:21 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 226, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:22 DP0 TP0] Decode batch. #running-req: 1, #token: 341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 172.59, #queue-req: 0, 
[2025-07-28 00:37:22 DP1 TP0] Decode batch. #running-req: 1, #token: 590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.63, #queue-req: 0, 
[2025-07-28 00:37:22 DP0 TP0] Decode batch. #running-req: 1, #token: 381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 230.11, #queue-req: 0, 
[2025-07-28 00:37:22 DP1 TP0] Decode batch. #running-req: 1, #token: 630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.13, #queue-req: 0, 
[2025-07-28 00:37:22 DP0 TP0] Decode batch. #running-req: 1, #token: 421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.74, #queue-req: 0, 
[2025-07-28 00:37:22 DP1 TP0] Decode batch. #running-req: 1, #token: 670, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.49, #queue-req: 0, 
[2025-07-28 00:37:22 DP0 TP0] Decode batch. #running-req: 1, #token: 461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.36, #queue-req: 0, 
[2025-07-28 00:37:22 DP1 TP0] Decode batch. #running-req: 1, #token: 710, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.13, #queue-req: 0, 
[2025-07-28 00:37:22 DP0 TP0] Decode batch. #running-req: 1, #token: 501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.06, #queue-req: 0, 
[2025-07-28 00:37:22 DP1 TP0] Decode batch. #running-req: 1, #token: 750, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.69, #queue-req: 0, 
[2025-07-28 00:37:22 DP0 TP0] Decode batch. #running-req: 1, #token: 541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.69, #queue-req: 0, 
[2025-07-28 00:37:22 DP1 TP0] Decode batch. #running-req: 1, #token: 790, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.83, #queue-req: 0, 
[2025-07-28 00:37:23 DP0 TP0] Decode batch. #running-req: 1, #token: 581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.21, #queue-req: 0, 
[2025-07-28 00:37:23 DP1 TP0] Decode batch. #running-req: 1, #token: 830, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.26, #queue-req: 0, 
[2025-07-28 00:37:23] INFO:     127.0.0.1:43014 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:23 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 168, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:23 DP0 TP0] Decode batch. #running-req: 1, #token: 621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.46, #queue-req: 0, 
[2025-07-28 00:37:23 DP1 TP0] Decode batch. #running-req: 1, #token: 305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 179.43, #queue-req: 0, 
[2025-07-28 00:37:23 DP0 TP0] Decode batch. #running-req: 1, #token: 661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.32, #queue-req: 0, 
[2025-07-28 00:37:23 DP1 TP0] Decode batch. #running-req: 1, #token: 345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.39, #queue-req: 0, 
[2025-07-28 00:37:23 DP0 TP0] Decode batch. #running-req: 1, #token: 701, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.56, #queue-req: 0, 
[2025-07-28 00:37:23 DP1 TP0] Decode batch. #running-req: 1, #token: 385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.54, #queue-req: 0, 
[2025-07-28 00:37:23 DP0 TP0] Decode batch. #running-req: 1, #token: 741, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.61, #queue-req: 0, 
[2025-07-28 00:37:23 DP1 TP0] Decode batch. #running-req: 1, #token: 425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.51, #queue-req: 0, 
[2025-07-28 00:37:23 DP0 TP0] Decode batch. #running-req: 1, #token: 781, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.10, #queue-req: 0, 
[2025-07-28 00:37:23] INFO:     127.0.0.1:43026 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:23 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 154, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:23 DP1 TP0] Decode batch. #running-req: 1, #token: 465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.78, #queue-req: 0, 
[2025-07-28 00:37:24 DP0 TP0] Decode batch. #running-req: 1, #token: 284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 181.27, #queue-req: 0, 
[2025-07-28 00:37:24 DP1 TP0] Decode batch. #running-req: 1, #token: 505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.53, #queue-req: 0, 
[2025-07-28 00:37:24 DP0 TP0] Decode batch. #running-req: 1, #token: 324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.17, #queue-req: 0, 
[2025-07-28 00:37:24 DP1 TP0] Decode batch. #running-req: 1, #token: 545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.49, #queue-req: 0, 
[2025-07-28 00:37:24 DP0 TP0] Decode batch. #running-req: 1, #token: 364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.24, #queue-req: 0, 
[2025-07-28 00:37:24 DP1 TP0] Decode batch. #running-req: 1, #token: 585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.17, #queue-req: 0, 
[2025-07-28 00:37:24 DP0 TP0] Decode batch. #running-req: 1, #token: 404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.75, #queue-req: 0, 
[2025-07-28 00:37:24 DP1 TP0] Decode batch. #running-req: 1, #token: 625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.02, #queue-req: 0, 
[2025-07-28 00:37:24 DP0 TP0] Decode batch. #running-req: 1, #token: 444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.11, #queue-req: 0, 
[2025-07-28 00:37:24 DP1 TP0] Decode batch. #running-req: 1, #token: 665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.14, #queue-req: 0, 
[2025-07-28 00:37:24 DP0 TP0] Decode batch. #running-req: 1, #token: 484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.41, #queue-req: 0, 
[2025-07-28 00:37:24] INFO:     127.0.0.1:52646 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:24 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 141, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:24 DP1 TP0] Decode batch. #running-req: 1, #token: 257, token usage: 0.00, cuda graph: True, gen throughput (token/s): 176.37, #queue-req: 0, 
[2025-07-28 00:37:25 DP0 TP0] Decode batch. #running-req: 1, #token: 524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.50, #queue-req: 0, 
[2025-07-28 00:37:25 DP1 TP0] Decode batch. #running-req: 1, #token: 297, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.58, #queue-req: 0, 
[2025-07-28 00:37:25 DP0 TP0] Decode batch. #running-req: 1, #token: 564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.30, #queue-req: 0, 
[2025-07-28 00:37:25 DP1 TP0] Decode batch. #running-req: 1, #token: 337, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.92, #queue-req: 0, 
[2025-07-28 00:37:25 DP0 TP0] Decode batch. #running-req: 1, #token: 604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.35, #queue-req: 0, 
[2025-07-28 00:37:25 DP1 TP0] Decode batch. #running-req: 1, #token: 377, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.53, #queue-req: 0, 
[2025-07-28 00:37:25 DP0 TP0] Decode batch. #running-req: 1, #token: 644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.95, #queue-req: 0, 
[2025-07-28 00:37:25 DP1 TP0] Decode batch. #running-req: 1, #token: 417, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.94, #queue-req: 0, 
[2025-07-28 00:37:25 DP0 TP0] Decode batch. #running-req: 1, #token: 684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.31, #queue-req: 0, 
[2025-07-28 00:37:25] INFO:     127.0.0.1:52654 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:25 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 192, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:25 DP1 TP0] Decode batch. #running-req: 1, #token: 457, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.19, #queue-req: 0, 
[2025-07-28 00:37:25 DP0 TP0] Decode batch. #running-req: 1, #token: 332, token usage: 0.00, cuda graph: True, gen throughput (token/s): 167.16, #queue-req: 0, 
[2025-07-28 00:37:25 DP1 TP0] Decode batch. #running-req: 1, #token: 497, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.16, #queue-req: 0, 
[2025-07-28 00:37:26 DP0 TP0] Decode batch. #running-req: 1, #token: 372, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.69, #queue-req: 0, 
[2025-07-28 00:37:26 DP1 TP0] Decode batch. #running-req: 1, #token: 537, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.59, #queue-req: 0, 
[2025-07-28 00:37:26 DP0 TP0] Decode batch. #running-req: 1, #token: 412, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.70, #queue-req: 0, 
[2025-07-28 00:37:26 DP1 TP0] Decode batch. #running-req: 1, #token: 577, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.91, #queue-req: 0, 
[2025-07-28 00:37:26] INFO:     127.0.0.1:52656 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:26 DP0 TP0] Decode batch. #running-req: 1, #token: 452, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.56, #queue-req: 0, 
[2025-07-28 00:37:26 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 190, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:26 DP1 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 177.24, #queue-req: 0, 
[2025-07-28 00:37:26 DP0 TP0] Decode batch. #running-req: 1, #token: 492, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.94, #queue-req: 0, 
[2025-07-28 00:37:26 DP1 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.55, #queue-req: 0, 
[2025-07-28 00:37:26 DP0 TP0] Decode batch. #running-req: 1, #token: 532, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.68, #queue-req: 0, 
[2025-07-28 00:37:26 DP1 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.64, #queue-req: 0, 
[2025-07-28 00:37:26 DP0 TP0] Decode batch. #running-req: 1, #token: 572, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.23, #queue-req: 0, 
[2025-07-28 00:37:27 DP1 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.02, #queue-req: 0, 
[2025-07-28 00:37:27 DP0 TP0] Decode batch. #running-req: 1, #token: 612, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.79, #queue-req: 0, 
[2025-07-28 00:37:27 DP1 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.46, #queue-req: 0, 
[2025-07-28 00:37:27 DP0 TP0] Decode batch. #running-req: 1, #token: 652, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.41, #queue-req: 0, 
[2025-07-28 00:37:27 DP1 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.49, #queue-req: 0, 
[2025-07-28 00:37:27 DP0 TP0] Decode batch. #running-req: 1, #token: 692, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.85, #queue-req: 0, 
[2025-07-28 00:37:27 DP1 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.06, #queue-req: 0, 
[2025-07-28 00:37:27 DP0 TP0] Decode batch. #running-req: 1, #token: 732, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.29, #queue-req: 0, 
[2025-07-28 00:37:27 DP1 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.91, #queue-req: 0, 
[2025-07-28 00:37:27 DP0 TP0] Decode batch. #running-req: 1, #token: 772, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.91, #queue-req: 0, 
[2025-07-28 00:37:27 DP1 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.57, #queue-req: 0, 
[2025-07-28 00:37:27 DP0 TP0] Decode batch. #running-req: 1, #token: 812, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.39, #queue-req: 0, 
[2025-07-28 00:37:28 DP1 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.29, #queue-req: 0, 
[2025-07-28 00:37:28 DP0 TP0] Decode batch. #running-req: 1, #token: 852, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.52, #queue-req: 0, 
[2025-07-28 00:37:28 DP1 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.89, #queue-req: 0, 
[2025-07-28 00:37:28 DP0 TP0] Decode batch. #running-req: 1, #token: 892, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.69, #queue-req: 0, 
[2025-07-28 00:37:28] INFO:     127.0.0.1:52668 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:28 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 137, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:28 DP1 TP0] Decode batch. #running-req: 1, #token: 755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.80, #queue-req: 0, 
[2025-07-28 00:37:28 DP0 TP0] Decode batch. #running-req: 1, #token: 279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 164.25, #queue-req: 0, 
[2025-07-28 00:37:28 DP1 TP0] Decode batch. #running-req: 1, #token: 795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.41, #queue-req: 0, 
[2025-07-28 00:37:28] INFO:     127.0.0.1:52682 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:28 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 132, #cached-token: 110, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:28 DP0 TP0] Decode batch. #running-req: 1, #token: 319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.57, #queue-req: 0, 
[2025-07-28 00:37:28 DP1 TP0] Decode batch. #running-req: 1, #token: 256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 174.41, #queue-req: 0, 
[2025-07-28 00:37:28 DP0 TP0] Decode batch. #running-req: 1, #token: 359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.76, #queue-req: 0, 
[2025-07-28 00:37:28 DP1 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.55, #queue-req: 0, 
[2025-07-28 00:37:28 DP0 TP0] Decode batch. #running-req: 1, #token: 399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.77, #queue-req: 0, 
[2025-07-28 00:37:29 DP1 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.90, #queue-req: 0, 
[2025-07-28 00:37:29 DP0 TP0] Decode batch. #running-req: 1, #token: 439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.61, #queue-req: 0, 
[2025-07-28 00:37:29 DP1 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.75, #queue-req: 0, 
[2025-07-28 00:37:29 DP0 TP0] Decode batch. #running-req: 1, #token: 479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.66, #queue-req: 0, 
[2025-07-28 00:37:29 DP1 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.24, #queue-req: 0, 
[2025-07-28 00:37:29 DP0 TP0] Decode batch. #running-req: 1, #token: 519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.87, #queue-req: 0, 
[2025-07-28 00:37:29 DP1 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.38, #queue-req: 0, 
[2025-07-28 00:37:29 DP0 TP0] Decode batch. #running-req: 1, #token: 559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.69, #queue-req: 0, 
[2025-07-28 00:37:29 DP1 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.79, #queue-req: 0, 
[2025-07-28 00:37:29 DP0 TP0] Decode batch. #running-req: 1, #token: 599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 235.34, #queue-req: 0, 
[2025-07-28 00:37:29 DP1 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.83, #queue-req: 0, 
[2025-07-28 00:37:29 DP0 TP0] Decode batch. #running-req: 1, #token: 639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.14, #queue-req: 0, 
[2025-07-28 00:37:30 DP1 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.90, #queue-req: 0, 
[2025-07-28 00:37:30 DP0 TP0] Decode batch. #running-req: 1, #token: 679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.20, #queue-req: 0, 
[2025-07-28 00:37:30 DP1 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.22, #queue-req: 0, 
[2025-07-28 00:37:30 DP0 TP0] Decode batch. #running-req: 1, #token: 719, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.88, #queue-req: 0, 
[2025-07-28 00:37:30 DP1 TP0] Decode batch. #running-req: 1, #token: 656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.17, #queue-req: 0, 
[2025-07-28 00:37:30 DP0 TP0] Decode batch. #running-req: 1, #token: 759, token usage: 0.00, cuda graph: True, gen throughput (token/s): 232.19, #queue-req: 0, 
[2025-07-28 00:37:30 DP1 TP0] Decode batch. #running-req: 1, #token: 696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.78, #queue-req: 0, 
[2025-07-28 00:37:30 DP0 TP0] Decode batch. #running-req: 1, #token: 799, token usage: 0.00, cuda graph: True, gen throughput (token/s): 235.91, #queue-req: 0, 
[2025-07-28 00:37:30] INFO:     127.0.0.1:52692 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:30 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 199, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:30 DP1 TP0] Decode batch. #running-req: 1, #token: 736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.37, #queue-req: 0, 
[2025-07-28 00:37:30 DP1 TP0] Decode batch. #running-req: 1, #token: 776, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.43, #queue-req: 0, 
[2025-07-28 00:37:30 DP0 TP0] Decode batch. #running-req: 1, #token: 340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 160.86, #queue-req: 0, 
[2025-07-28 00:37:31 DP1 TP0] Decode batch. #running-req: 1, #token: 816, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.25, #queue-req: 0, 
[2025-07-28 00:37:31 DP0 TP0] Decode batch. #running-req: 1, #token: 380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.80, #queue-req: 0, 
[2025-07-28 00:37:31 DP1 TP0] Decode batch. #running-req: 1, #token: 856, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.45, #queue-req: 0, 
[2025-07-28 00:37:31 DP0 TP0] Decode batch. #running-req: 1, #token: 420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.21, #queue-req: 0, 
[2025-07-28 00:37:31 DP1 TP0] Decode batch. #running-req: 1, #token: 896, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.96, #queue-req: 0, 
[2025-07-28 00:37:31 DP0 TP0] Decode batch. #running-req: 1, #token: 460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.49, #queue-req: 0, 
[2025-07-28 00:37:31 DP1 TP0] Decode batch. #running-req: 1, #token: 936, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.97, #queue-req: 0, 
[2025-07-28 00:37:31 DP0 TP0] Decode batch. #running-req: 1, #token: 500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.51, #queue-req: 0, 
[2025-07-28 00:37:31 DP1 TP0] Decode batch. #running-req: 1, #token: 976, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.99, #queue-req: 0, 
[2025-07-28 00:37:31 DP0 TP0] Decode batch. #running-req: 1, #token: 540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.74, #queue-req: 0, 
[2025-07-28 00:37:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1016, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.41, #queue-req: 0, 
[2025-07-28 00:37:31 DP0 TP0] Decode batch. #running-req: 1, #token: 580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.74, #queue-req: 0, 
[2025-07-28 00:37:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1056, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.93, #queue-req: 0, 
[2025-07-28 00:37:32 DP0 TP0] Decode batch. #running-req: 1, #token: 620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.76, #queue-req: 0, 
[2025-07-28 00:37:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1096, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.72, #queue-req: 0, 
[2025-07-28 00:37:32 DP0 TP0] Decode batch. #running-req: 1, #token: 660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.86, #queue-req: 0, 
[2025-07-28 00:37:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1136, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.57, #queue-req: 0, 
[2025-07-28 00:37:32 DP0 TP0] Decode batch. #running-req: 1, #token: 700, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.91, #queue-req: 0, 
[2025-07-28 00:37:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1176, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.76, #queue-req: 0, 
[2025-07-28 00:37:32 DP0 TP0] Decode batch. #running-req: 1, #token: 740, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.28, #queue-req: 0, 
[2025-07-28 00:37:32] INFO:     127.0.0.1:52702 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:32 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 194, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:32 DP0 TP0] Decode batch. #running-req: 1, #token: 780, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.88, #queue-req: 0, 
[2025-07-28 00:37:32 DP1 TP0] Decode batch. #running-req: 1, #token: 324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 174.54, #queue-req: 0, 
[2025-07-28 00:37:32 DP0 TP0] Decode batch. #running-req: 1, #token: 820, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.98, #queue-req: 0, 
[2025-07-28 00:37:32 DP1 TP0] Decode batch. #running-req: 1, #token: 364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.09, #queue-req: 0, 
[2025-07-28 00:37:33 DP0 TP0] Decode batch. #running-req: 1, #token: 860, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.64, #queue-req: 0, 
[2025-07-28 00:37:33] INFO:     127.0.0.1:52716 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:33 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 167, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:33 DP1 TP0] Decode batch. #running-req: 1, #token: 404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.21, #queue-req: 0, 
[2025-07-28 00:37:33 DP0 TP0] Decode batch. #running-req: 1, #token: 309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 181.79, #queue-req: 0, 
[2025-07-28 00:37:33 DP1 TP0] Decode batch. #running-req: 1, #token: 444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.01, #queue-req: 0, 
[2025-07-28 00:37:33 DP0 TP0] Decode batch. #running-req: 1, #token: 349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.15, #queue-req: 0, 
[2025-07-28 00:37:33 DP1 TP0] Decode batch. #running-req: 1, #token: 484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.93, #queue-req: 0, 
[2025-07-28 00:37:33 DP0 TP0] Decode batch. #running-req: 1, #token: 389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.03, #queue-req: 0, 
[2025-07-28 00:37:33 DP1 TP0] Decode batch. #running-req: 1, #token: 524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.92, #queue-req: 0, 
[2025-07-28 00:37:33 DP0 TP0] Decode batch. #running-req: 1, #token: 429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.05, #queue-req: 0, 
[2025-07-28 00:37:33 DP1 TP0] Decode batch. #running-req: 1, #token: 564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.73, #queue-req: 0, 
[2025-07-28 00:37:33 DP0 TP0] Decode batch. #running-req: 1, #token: 469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.21, #queue-req: 0, 
[2025-07-28 00:37:33 DP1 TP0] Decode batch. #running-req: 1, #token: 604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.02, #queue-req: 0, 
[2025-07-28 00:37:34 DP0 TP0] Decode batch. #running-req: 1, #token: 509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.76, #queue-req: 0, 
[2025-07-28 00:37:34 DP1 TP0] Decode batch. #running-req: 1, #token: 644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.30, #queue-req: 0, 
[2025-07-28 00:37:34 DP0 TP0] Decode batch. #running-req: 1, #token: 549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.04, #queue-req: 0, 
[2025-07-28 00:37:34 DP1 TP0] Decode batch. #running-req: 1, #token: 684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.50, #queue-req: 0, 
[2025-07-28 00:37:34 DP0 TP0] Decode batch. #running-req: 1, #token: 589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.84, #queue-req: 0, 
[2025-07-28 00:37:34 DP1 TP0] Decode batch. #running-req: 1, #token: 724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.09, #queue-req: 0, 
[2025-07-28 00:37:34 DP0 TP0] Decode batch. #running-req: 1, #token: 629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.68, #queue-req: 0, 
[2025-07-28 00:37:34 DP1 TP0] Decode batch. #running-req: 1, #token: 764, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.94, #queue-req: 0, 
[2025-07-28 00:37:34 DP0 TP0] Decode batch. #running-req: 1, #token: 669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.51, #queue-req: 0, 
[2025-07-28 00:37:34 DP1 TP0] Decode batch. #running-req: 1, #token: 804, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.26, #queue-req: 0, 
[2025-07-28 00:37:34] INFO:     127.0.0.1:52978 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:34 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 172, #cached-token: 110, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:34 DP0 TP0] Decode batch. #running-req: 1, #token: 709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.87, #queue-req: 0, 
[2025-07-28 00:37:34 DP1 TP0] Decode batch. #running-req: 1, #token: 320, token usage: 0.00, cuda graph: True, gen throughput (token/s): 176.77, #queue-req: 0, 
[2025-07-28 00:37:35 DP0 TP0] Decode batch. #running-req: 1, #token: 749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.40, #queue-req: 0, 
[2025-07-28 00:37:35 DP1 TP0] Decode batch. #running-req: 1, #token: 360, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.77, #queue-req: 0, 
[2025-07-28 00:37:35 DP0 TP0] Decode batch. #running-req: 1, #token: 789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.40, #queue-req: 0, 
[2025-07-28 00:37:35 DP1 TP0] Decode batch. #running-req: 1, #token: 400, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.00, #queue-req: 0, 
[2025-07-28 00:37:35 DP0 TP0] Decode batch. #running-req: 1, #token: 829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.73, #queue-req: 0, 
[2025-07-28 00:37:35] INFO:     127.0.0.1:52984 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:35 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 219, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:35 DP1 TP0] Decode batch. #running-req: 1, #token: 440, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.36, #queue-req: 0, 
[2025-07-28 00:37:35 DP1 TP0] Decode batch. #running-req: 1, #token: 480, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.77, #queue-req: 0, 
[2025-07-28 00:37:35 DP0 TP0] Decode batch. #running-req: 1, #token: 362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 172.94, #queue-req: 0, 
[2025-07-28 00:37:35 DP1 TP0] Decode batch. #running-req: 1, #token: 520, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.94, #queue-req: 0, 
[2025-07-28 00:37:35 DP0 TP0] Decode batch. #running-req: 1, #token: 402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.28, #queue-req: 0, 
[2025-07-28 00:37:35 DP1 TP0] Decode batch. #running-req: 1, #token: 560, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.83, #queue-req: 0, 
[2025-07-28 00:37:35 DP0 TP0] Decode batch. #running-req: 1, #token: 442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.72, #queue-req: 0, 
[2025-07-28 00:37:36 DP1 TP0] Decode batch. #running-req: 1, #token: 600, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.29, #queue-req: 0, 
[2025-07-28 00:37:36 DP0 TP0] Decode batch. #running-req: 1, #token: 482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.17, #queue-req: 0, 
[2025-07-28 00:37:36 DP1 TP0] Decode batch. #running-req: 1, #token: 640, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.58, #queue-req: 0, 
[2025-07-28 00:37:36 DP0 TP0] Decode batch. #running-req: 1, #token: 522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.93, #queue-req: 0, 
[2025-07-28 00:37:36 DP1 TP0] Decode batch. #running-req: 1, #token: 680, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.65, #queue-req: 0, 
[2025-07-28 00:37:36 DP0 TP0] Decode batch. #running-req: 1, #token: 562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.68, #queue-req: 0, 
[2025-07-28 00:37:36 DP1 TP0] Decode batch. #running-req: 1, #token: 720, token usage: 0.00, cuda graph: True, gen throughput (token/s): 249.13, #queue-req: 0, 
[2025-07-28 00:37:36 DP0 TP0] Decode batch. #running-req: 1, #token: 602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.56, #queue-req: 0, 
[2025-07-28 00:37:36 DP1 TP0] Decode batch. #running-req: 1, #token: 760, token usage: 0.00, cuda graph: True, gen throughput (token/s): 249.41, #queue-req: 0, 
[2025-07-28 00:37:36 DP0 TP0] Decode batch. #running-req: 1, #token: 642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.99, #queue-req: 0, 
[2025-07-28 00:37:36 DP1 TP0] Decode batch. #running-req: 1, #token: 800, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.62, #queue-req: 0, 
[2025-07-28 00:37:36 DP0 TP0] Decode batch. #running-req: 1, #token: 682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.54, #queue-req: 0, 
[2025-07-28 00:37:37 DP1 TP0] Decode batch. #running-req: 1, #token: 840, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.00, #queue-req: 0, 
[2025-07-28 00:37:37] INFO:     127.0.0.1:52998 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:37 DP0 TP0] Decode batch. #running-req: 1, #token: 722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.19, #queue-req: 0, 
[2025-07-28 00:37:37 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 155, #cached-token: 111, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:37 DP0 TP0] Decode batch. #running-req: 1, #token: 762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.85, #queue-req: 0, 
[2025-07-28 00:37:37 DP1 TP0] Decode batch. #running-req: 1, #token: 302, token usage: 0.00, cuda graph: True, gen throughput (token/s): 181.09, #queue-req: 0, 
[2025-07-28 00:37:37 DP0 TP0] Decode batch. #running-req: 1, #token: 802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.77, #queue-req: 0, 
[2025-07-28 00:37:37 DP1 TP0] Decode batch. #running-req: 1, #token: 342, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.68, #queue-req: 0, 
[2025-07-28 00:37:37 DP0 TP0] Decode batch. #running-req: 1, #token: 842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.97, #queue-req: 0, 
[2025-07-28 00:37:37 DP1 TP0] Decode batch. #running-req: 1, #token: 382, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.83, #queue-req: 0, 
[2025-07-28 00:37:37 DP0 TP0] Decode batch. #running-req: 1, #token: 882, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.62, #queue-req: 0, 
[2025-07-28 00:37:37 DP1 TP0] Decode batch. #running-req: 1, #token: 422, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.71, #queue-req: 0, 
[2025-07-28 00:37:37] INFO:     127.0.0.1:53006 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:37 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 175, #cached-token: 110, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:37 DP1 TP0] Decode batch. #running-req: 1, #token: 462, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.05, #queue-req: 0, 
[2025-07-28 00:37:37 DP0 TP0] Decode batch. #running-req: 1, #token: 292, token usage: 0.00, cuda graph: True, gen throughput (token/s): 182.60, #queue-req: 0, 
[2025-07-28 00:37:38 DP1 TP0] Decode batch. #running-req: 1, #token: 502, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.68, #queue-req: 0, 
[2025-07-28 00:37:38 DP0 TP0] Decode batch. #running-req: 1, #token: 332, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.74, #queue-req: 0, 
[2025-07-28 00:37:38 DP1 TP0] Decode batch. #running-req: 1, #token: 542, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.58, #queue-req: 0, 
[2025-07-28 00:37:38 DP0 TP0] Decode batch. #running-req: 1, #token: 372, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.27, #queue-req: 0, 
[2025-07-28 00:37:38 DP1 TP0] Decode batch. #running-req: 1, #token: 582, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.54, #queue-req: 0, 
[2025-07-28 00:37:38 DP0 TP0] Decode batch. #running-req: 1, #token: 412, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.40, #queue-req: 0, 
[2025-07-28 00:37:38 DP1 TP0] Decode batch. #running-req: 1, #token: 622, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.30, #queue-req: 0, 
[2025-07-28 00:37:38 DP0 TP0] Decode batch. #running-req: 1, #token: 452, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.74, #queue-req: 0, 
[2025-07-28 00:37:38 DP1 TP0] Decode batch. #running-req: 1, #token: 662, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.58, #queue-req: 0, 
[2025-07-28 00:37:38 DP0 TP0] Decode batch. #running-req: 1, #token: 492, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.54, #queue-req: 0, 
[2025-07-28 00:37:38 DP1 TP0] Decode batch. #running-req: 1, #token: 702, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.66, #queue-req: 0, 
[2025-07-28 00:37:38 DP0 TP0] Decode batch. #running-req: 1, #token: 532, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.53, #queue-req: 0, 
[2025-07-28 00:37:39 DP1 TP0] Decode batch. #running-req: 1, #token: 742, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.12, #queue-req: 0, 
[2025-07-28 00:37:39 DP0 TP0] Decode batch. #running-req: 1, #token: 572, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.28, #queue-req: 0, 
[2025-07-28 00:37:39 DP1 TP0] Decode batch. #running-req: 1, #token: 782, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.31, #queue-req: 0, 
[2025-07-28 00:37:39 DP0 TP0] Decode batch. #running-req: 1, #token: 612, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.60, #queue-req: 0, 
[2025-07-28 00:37:39 DP1 TP0] Decode batch. #running-req: 1, #token: 822, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.33, #queue-req: 0, 
[2025-07-28 00:37:39 DP0 TP0] Decode batch. #running-req: 1, #token: 652, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.20, #queue-req: 0, 
[2025-07-28 00:37:39 DP1 TP0] Decode batch. #running-req: 1, #token: 862, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.20, #queue-req: 0, 
[2025-07-28 00:37:39] INFO:     127.0.0.1:53022 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:39 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 94, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:39 DP0 TP0] Decode batch. #running-req: 1, #token: 692, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.01, #queue-req: 0, 
[2025-07-28 00:37:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.20, #queue-req: 0, 
[2025-07-28 00:37:39] INFO:     127.0.0.1:53032 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:39 DP1 TP0] Decode batch. #running-req: 1, #token: 234, token usage: 0.00, cuda graph: True, gen throughput (token/s): 183.42, #queue-req: 0, 
[2025-07-28 00:37:39 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 115, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:39 DP1 TP0] Decode batch. #running-req: 1, #token: 274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.63, #queue-req: 0, 
[2025-07-28 00:37:40 DP0 TP0] Decode batch. #running-req: 1, #token: 261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 182.54, #queue-req: 0, 
[2025-07-28 00:37:40 DP1 TP0] Decode batch. #running-req: 1, #token: 314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.81, #queue-req: 0, 
[2025-07-28 00:37:40 DP0 TP0] Decode batch. #running-req: 1, #token: 301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.70, #queue-req: 0, 
[2025-07-28 00:37:40 DP1 TP0] Decode batch. #running-req: 1, #token: 354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.15, #queue-req: 0, 
[2025-07-28 00:37:40 DP0 TP0] Decode batch. #running-req: 1, #token: 341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.24, #queue-req: 0, 
[2025-07-28 00:37:40 DP1 TP0] Decode batch. #running-req: 1, #token: 394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.57, #queue-req: 0, 
[2025-07-28 00:37:40 DP0 TP0] Decode batch. #running-req: 1, #token: 381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.17, #queue-req: 0, 
[2025-07-28 00:37:40 DP1 TP0] Decode batch. #running-req: 1, #token: 434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.25, #queue-req: 0, 
[2025-07-28 00:37:40 DP0 TP0] Decode batch. #running-req: 1, #token: 421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.45, #queue-req: 0, 
[2025-07-28 00:37:40 DP1 TP0] Decode batch. #running-req: 1, #token: 474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.63, #queue-req: 0, 
[2025-07-28 00:37:40 DP0 TP0] Decode batch. #running-req: 1, #token: 461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.23, #queue-req: 0, 
[2025-07-28 00:37:40 DP1 TP0] Decode batch. #running-req: 1, #token: 514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.74, #queue-req: 0, 
[2025-07-28 00:37:40 DP0 TP0] Decode batch. #running-req: 1, #token: 501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.56, #queue-req: 0, 
[2025-07-28 00:37:41 DP1 TP0] Decode batch. #running-req: 1, #token: 554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.29, #queue-req: 0, 
[2025-07-28 00:37:41 DP0 TP0] Decode batch. #running-req: 1, #token: 541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.82, #queue-req: 0, 
[2025-07-28 00:37:41 DP1 TP0] Decode batch. #running-req: 1, #token: 594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.62, #queue-req: 0, 
[2025-07-28 00:37:41 DP0 TP0] Decode batch. #running-req: 1, #token: 581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.67, #queue-req: 0, 
[2025-07-28 00:37:41 DP1 TP0] Decode batch. #running-req: 1, #token: 634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.84, #queue-req: 0, 
[2025-07-28 00:37:41 DP0 TP0] Decode batch. #running-req: 1, #token: 621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.57, #queue-req: 0, 
[2025-07-28 00:37:41 DP1 TP0] Decode batch. #running-req: 1, #token: 674, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.80, #queue-req: 0, 
[2025-07-28 00:37:41 DP0 TP0] Decode batch. #running-req: 1, #token: 661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.54, #queue-req: 0, 
[2025-07-28 00:37:41 DP1 TP0] Decode batch. #running-req: 1, #token: 714, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.37, #queue-req: 0, 
[2025-07-28 00:37:41 DP0 TP0] Decode batch. #running-req: 1, #token: 701, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.78, #queue-req: 0, 
[2025-07-28 00:37:41] INFO:     127.0.0.1:53046 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:41 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 150, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:41 DP0 TP0] Decode batch. #running-req: 1, #token: 741, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.63, #queue-req: 0, 
[2025-07-28 00:37:41 DP1 TP0] Decode batch. #running-req: 1, #token: 268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 177.68, #queue-req: 0, 
[2025-07-28 00:37:42 DP0 TP0] Decode batch. #running-req: 1, #token: 781, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.10, #queue-req: 0, 
[2025-07-28 00:37:42 DP1 TP0] Decode batch. #running-req: 1, #token: 308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.40, #queue-req: 0, 
[2025-07-28 00:37:42 DP0 TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.83, #queue-req: 0, 
[2025-07-28 00:37:42] INFO:     127.0.0.1:53062 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:42 DP1 TP0] Decode batch. #running-req: 1, #token: 348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.52, #queue-req: 0, 
[2025-07-28 00:37:42 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 148, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:42 DP1 TP0] Decode batch. #running-req: 1, #token: 388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.39, #queue-req: 0, 
[2025-07-28 00:37:42 DP0 TP0] Decode batch. #running-req: 1, #token: 297, token usage: 0.00, cuda graph: True, gen throughput (token/s): 175.75, #queue-req: 0, 
[2025-07-28 00:37:42 DP1 TP0] Decode batch. #running-req: 1, #token: 428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.85, #queue-req: 0, 
[2025-07-28 00:37:42 DP0 TP0] Decode batch. #running-req: 1, #token: 337, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.55, #queue-req: 0, 
[2025-07-28 00:37:42 DP1 TP0] Decode batch. #running-req: 1, #token: 468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.62, #queue-req: 0, 
[2025-07-28 00:37:42 DP0 TP0] Decode batch. #running-req: 1, #token: 377, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.87, #queue-req: 0, 
[2025-07-28 00:37:42 DP1 TP0] Decode batch. #running-req: 1, #token: 508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.38, #queue-req: 0, 
[2025-07-28 00:37:43 DP0 TP0] Decode batch. #running-req: 1, #token: 417, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.26, #queue-req: 0, 
[2025-07-28 00:37:43 DP1 TP0] Decode batch. #running-req: 1, #token: 548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.54, #queue-req: 0, 
[2025-07-28 00:37:43 DP0 TP0] Decode batch. #running-req: 1, #token: 457, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.67, #queue-req: 0, 
[2025-07-28 00:37:43 DP1 TP0] Decode batch. #running-req: 1, #token: 588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.52, #queue-req: 0, 
[2025-07-28 00:37:43 DP0 TP0] Decode batch. #running-req: 1, #token: 497, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.34, #queue-req: 0, 
[2025-07-28 00:37:43 DP1 TP0] Decode batch. #running-req: 1, #token: 628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.62, #queue-req: 0, 
[2025-07-28 00:37:43 DP0 TP0] Decode batch. #running-req: 1, #token: 537, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.19, #queue-req: 0, 
[2025-07-28 00:37:43 DP1 TP0] Decode batch. #running-req: 1, #token: 668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.63, #queue-req: 0, 
[2025-07-28 00:37:43 DP0 TP0] Decode batch. #running-req: 1, #token: 577, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.87, #queue-req: 0, 
[2025-07-28 00:37:43 DP1 TP0] Decode batch. #running-req: 1, #token: 708, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.02, #queue-req: 0, 
[2025-07-28 00:37:43 DP0 TP0] Decode batch. #running-req: 1, #token: 617, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.65, #queue-req: 0, 
[2025-07-28 00:37:43 DP1 TP0] Decode batch. #running-req: 1, #token: 748, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.31, #queue-req: 0, 
[2025-07-28 00:37:44 DP0 TP0] Decode batch. #running-req: 1, #token: 657, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.30, #queue-req: 0, 
[2025-07-28 00:37:44 DP1 TP0] Decode batch. #running-req: 1, #token: 788, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.82, #queue-req: 0, 
[2025-07-28 00:37:44 DP0 TP0] Decode batch. #running-req: 1, #token: 697, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.28, #queue-req: 0, 
[2025-07-28 00:37:44 DP1 TP0] Decode batch. #running-req: 1, #token: 828, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.75, #queue-req: 0, 
[2025-07-28 00:37:44 DP0 TP0] Decode batch. #running-req: 1, #token: 737, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.98, #queue-req: 0, 
[2025-07-28 00:37:44 DP1 TP0] Decode batch. #running-req: 1, #token: 868, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.73, #queue-req: 0, 
[2025-07-28 00:37:44 DP0 TP0] Decode batch. #running-req: 1, #token: 777, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.41, #queue-req: 0, 
[2025-07-28 00:37:44 DP1 TP0] Decode batch. #running-req: 1, #token: 908, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.55, #queue-req: 0, 
[2025-07-28 00:37:44 DP0 TP0] Decode batch. #running-req: 1, #token: 817, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.07, #queue-req: 0, 
[2025-07-28 00:37:44] INFO:     127.0.0.1:53076 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:44 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 160, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:44 DP1 TP0] Decode batch. #running-req: 1, #token: 285, token usage: 0.00, cuda graph: True, gen throughput (token/s): 178.65, #queue-req: 0, 
[2025-07-28 00:37:44 DP0 TP0] Decode batch. #running-req: 1, #token: 857, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.41, #queue-req: 0, 
[2025-07-28 00:37:44 DP1 TP0] Decode batch. #running-req: 1, #token: 325, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.33, #queue-req: 0, 
[2025-07-28 00:37:44 DP0 TP0] Decode batch. #running-req: 1, #token: 897, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.98, #queue-req: 0, 
[2025-07-28 00:37:45] INFO:     127.0.0.1:46742 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:45 DP1 TP0] Decode batch. #running-req: 1, #token: 365, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.94, #queue-req: 0, 
[2025-07-28 00:37:45 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 114, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:45 DP0 TP0] Decode batch. #running-req: 1, #token: 225, token usage: 0.00, cuda graph: True, gen throughput (token/s): 181.86, #queue-req: 0, 
[2025-07-28 00:37:45 DP1 TP0] Decode batch. #running-req: 1, #token: 405, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.14, #queue-req: 0, 
[2025-07-28 00:37:45 DP0 TP0] Decode batch. #running-req: 1, #token: 265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.77, #queue-req: 0, 
[2025-07-28 00:37:45 DP1 TP0] Decode batch. #running-req: 1, #token: 445, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.80, #queue-req: 0, 
[2025-07-28 00:37:45 DP0 TP0] Decode batch. #running-req: 1, #token: 305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.87, #queue-req: 0, 
[2025-07-28 00:37:45 DP1 TP0] Decode batch. #running-req: 1, #token: 485, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.41, #queue-req: 0, 
[2025-07-28 00:37:45 DP0 TP0] Decode batch. #running-req: 1, #token: 345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.46, #queue-req: 0, 
[2025-07-28 00:37:45 DP1 TP0] Decode batch. #running-req: 1, #token: 525, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.05, #queue-req: 0, 
[2025-07-28 00:37:45 DP0 TP0] Decode batch. #running-req: 1, #token: 385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.01, #queue-req: 0, 
[2025-07-28 00:37:45 DP1 TP0] Decode batch. #running-req: 1, #token: 565, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.50, #queue-req: 0, 
[2025-07-28 00:37:46 DP0 TP0] Decode batch. #running-req: 1, #token: 425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.10, #queue-req: 0, 
[2025-07-28 00:37:46 DP1 TP0] Decode batch. #running-req: 1, #token: 605, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.34, #queue-req: 0, 
[2025-07-28 00:37:46 DP0 TP0] Decode batch. #running-req: 1, #token: 465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.08, #queue-req: 0, 
[2025-07-28 00:37:46 DP1 TP0] Decode batch. #running-req: 1, #token: 645, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.91, #queue-req: 0, 
[2025-07-28 00:37:46 DP0 TP0] Decode batch. #running-req: 1, #token: 505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.20, #queue-req: 0, 
[2025-07-28 00:37:46 DP1 TP0] Decode batch. #running-req: 1, #token: 685, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.21, #queue-req: 0, 
[2025-07-28 00:37:46 DP0 TP0] Decode batch. #running-req: 1, #token: 545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.17, #queue-req: 0, 
[2025-07-28 00:37:46 DP1 TP0] Decode batch. #running-req: 1, #token: 725, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.72, #queue-req: 0, 
[2025-07-28 00:37:46 DP0 TP0] Decode batch. #running-req: 1, #token: 585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.98, #queue-req: 0, 
[2025-07-28 00:37:46 DP1 TP0] Decode batch. #running-req: 1, #token: 765, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.90, #queue-req: 0, 
[2025-07-28 00:37:46 DP0 TP0] Decode batch. #running-req: 1, #token: 625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.62, #queue-req: 0, 
[2025-07-28 00:37:46 DP1 TP0] Decode batch. #running-req: 1, #token: 805, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.98, #queue-req: 0, 
[2025-07-28 00:37:46 DP0 TP0] Decode batch. #running-req: 1, #token: 665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.10, #queue-req: 0, 
[2025-07-28 00:37:47 DP1 TP0] Decode batch. #running-req: 1, #token: 845, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.16, #queue-req: 0, 
[2025-07-28 00:37:47 DP0 TP0] Decode batch. #running-req: 1, #token: 705, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.96, #queue-req: 0, 
[2025-07-28 00:37:47 DP1 TP0] Decode batch. #running-req: 1, #token: 885, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.53, #queue-req: 0, 
[2025-07-28 00:37:47] INFO:     127.0.0.1:46748 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:47 DP0 TP0] Decode batch. #running-req: 1, #token: 745, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.16, #queue-req: 0, 
[2025-07-28 00:37:47 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 80, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:47] INFO:     127.0.0.1:46764 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:47 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 136, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:47 DP1 TP0] Decode batch. #running-req: 1, #token: 218, token usage: 0.00, cuda graph: True, gen throughput (token/s): 182.17, #queue-req: 0, 
[2025-07-28 00:37:47 DP0 TP0] Decode batch. #running-req: 1, #token: 274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 189.64, #queue-req: 0, 
[2025-07-28 00:37:47 DP1 TP0] Decode batch. #running-req: 1, #token: 258, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.54, #queue-req: 0, 
[2025-07-28 00:37:47 DP0 TP0] Decode batch. #running-req: 1, #token: 314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.29, #queue-req: 0, 
[2025-07-28 00:37:47 DP1 TP0] Decode batch. #running-req: 1, #token: 298, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.63, #queue-req: 0, 
[2025-07-28 00:37:47 DP0 TP0] Decode batch. #running-req: 1, #token: 354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.43, #queue-req: 0, 
[2025-07-28 00:37:47 DP1 TP0] Decode batch. #running-req: 1, #token: 338, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.51, #queue-req: 0, 
[2025-07-28 00:37:48 DP0 TP0] Decode batch. #running-req: 1, #token: 394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.51, #queue-req: 0, 
[2025-07-28 00:37:48 DP1 TP0] Decode batch. #running-req: 1, #token: 378, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.67, #queue-req: 0, 
[2025-07-28 00:37:48 DP0 TP0] Decode batch. #running-req: 1, #token: 434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.71, #queue-req: 0, 
[2025-07-28 00:37:48 DP1 TP0] Decode batch. #running-req: 1, #token: 418, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.56, #queue-req: 0, 
[2025-07-28 00:37:48 DP0 TP0] Decode batch. #running-req: 1, #token: 474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.05, #queue-req: 0, 
[2025-07-28 00:37:48 DP1 TP0] Decode batch. #running-req: 1, #token: 458, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.05, #queue-req: 0, 
[2025-07-28 00:37:48 DP0 TP0] Decode batch. #running-req: 1, #token: 514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.59, #queue-req: 0, 
[2025-07-28 00:37:48 DP1 TP0] Decode batch. #running-req: 1, #token: 498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.06, #queue-req: 0, 
[2025-07-28 00:37:48 DP0 TP0] Decode batch. #running-req: 1, #token: 554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.64, #queue-req: 0, 
[2025-07-28 00:37:48 DP1 TP0] Decode batch. #running-req: 1, #token: 538, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.98, #queue-req: 0, 
[2025-07-28 00:37:48 DP0 TP0] Decode batch. #running-req: 1, #token: 594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.38, #queue-req: 0, 
[2025-07-28 00:37:48] INFO:     127.0.0.1:46786 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:48 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 137, #cached-token: 109, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:37:49 DP1 TP0] Decode batch. #running-req: 2, #token: 729, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.11, #queue-req: 0, 
[2025-07-28 00:37:49 DP1 TP0] Decode batch. #running-req: 2, #token: 809, token usage: 0.00, cuda graph: True, gen throughput (token/s): 470.82, #queue-req: 0, 
[2025-07-28 00:37:49 DP1 TP0] Decode batch. #running-req: 2, #token: 889, token usage: 0.00, cuda graph: True, gen throughput (token/s): 470.05, #queue-req: 0, 
[2025-07-28 00:37:49 DP1 TP0] Decode batch. #running-req: 2, #token: 969, token usage: 0.00, cuda graph: True, gen throughput (token/s): 471.82, #queue-req: 0, 
[2025-07-28 00:37:49 DP1 TP0] Decode batch. #running-req: 2, #token: 1049, token usage: 0.00, cuda graph: True, gen throughput (token/s): 471.04, #queue-req: 0, 
[2025-07-28 00:37:49 DP1 TP0] Decode batch. #running-req: 2, #token: 1129, token usage: 0.00, cuda graph: True, gen throughput (token/s): 471.87, #queue-req: 0, 
[2025-07-28 00:37:49] INFO:     127.0.0.1:46780 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:49 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 85, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:50 DP1 TP0] Decode batch. #running-req: 1, #token: 497, token usage: 0.00, cuda graph: True, gen throughput (token/s): 333.79, #queue-req: 0, 
[2025-07-28 00:37:50 DP0 TP0] Decode batch. #running-req: 1, #token: 212, token usage: 0.00, cuda graph: True, gen throughput (token/s): 31.67, #queue-req: 0, 
[2025-07-28 00:37:50 DP1 TP0] Decode batch. #running-req: 1, #token: 537, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.00, #queue-req: 0, 
[2025-07-28 00:37:50 DP0 TP0] Decode batch. #running-req: 1, #token: 252, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.26, #queue-req: 0, 
[2025-07-28 00:37:50 DP1 TP0] Decode batch. #running-req: 1, #token: 577, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.80, #queue-req: 0, 
[2025-07-28 00:37:50 DP0 TP0] Decode batch. #running-req: 1, #token: 292, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.24, #queue-req: 0, 
[2025-07-28 00:37:50 DP1 TP0] Decode batch. #running-req: 1, #token: 617, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.28, #queue-req: 0, 
[2025-07-28 00:37:50 DP0 TP0] Decode batch. #running-req: 1, #token: 332, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.16, #queue-req: 0, 
[2025-07-28 00:37:50 DP1 TP0] Decode batch. #running-req: 1, #token: 657, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.46, #queue-req: 0, 
[2025-07-28 00:37:50 DP0 TP0] Decode batch. #running-req: 1, #token: 372, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.90, #queue-req: 0, 
[2025-07-28 00:37:50 DP1 TP0] Decode batch. #running-req: 1, #token: 697, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.18, #queue-req: 0, 
[2025-07-28 00:37:50 DP0 TP0] Decode batch. #running-req: 1, #token: 412, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.63, #queue-req: 0, 
[2025-07-28 00:37:50 DP1 TP0] Decode batch. #running-req: 1, #token: 737, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.08, #queue-req: 0, 
[2025-07-28 00:37:51 DP0 TP0] Decode batch. #running-req: 1, #token: 452, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.47, #queue-req: 0, 
[2025-07-28 00:37:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.70, #queue-req: 0, 
[2025-07-28 00:37:51] INFO:     127.0.0.1:46794 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:51 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 157, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:51 DP0 TP0] Decode batch. #running-req: 1, #token: 492, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.79, #queue-req: 0, 
[2025-07-28 00:37:51 DP1 TP0] Decode batch. #running-req: 1, #token: 303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 180.23, #queue-req: 0, 
[2025-07-28 00:37:51 DP0 TP0] Decode batch. #running-req: 1, #token: 532, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.55, #queue-req: 0, 
[2025-07-28 00:37:51 DP1 TP0] Decode batch. #running-req: 1, #token: 343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.49, #queue-req: 0, 
[2025-07-28 00:37:51 DP0 TP0] Decode batch. #running-req: 1, #token: 572, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.50, #queue-req: 0, 
[2025-07-28 00:37:51 DP1 TP0] Decode batch. #running-req: 1, #token: 383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.48, #queue-req: 0, 
[2025-07-28 00:37:51 DP0 TP0] Decode batch. #running-req: 1, #token: 612, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.10, #queue-req: 0, 
[2025-07-28 00:37:51] INFO:     127.0.0.1:46806 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:51 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 150, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:51 DP1 TP0] Decode batch. #running-req: 1, #token: 423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.49, #queue-req: 0, 
[2025-07-28 00:37:51 DP0 TP0] Decode batch. #running-req: 1, #token: 281, token usage: 0.00, cuda graph: True, gen throughput (token/s): 181.17, #queue-req: 0, 
[2025-07-28 00:37:52 DP1 TP0] Decode batch. #running-req: 1, #token: 463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.10, #queue-req: 0, 
[2025-07-28 00:37:52 DP0 TP0] Decode batch. #running-req: 1, #token: 321, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.94, #queue-req: 0, 
[2025-07-28 00:37:52 DP1 TP0] Decode batch. #running-req: 1, #token: 503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.94, #queue-req: 0, 
[2025-07-28 00:37:52 DP0 TP0] Decode batch. #running-req: 1, #token: 361, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.13, #queue-req: 0, 
[2025-07-28 00:37:52 DP1 TP0] Decode batch. #running-req: 1, #token: 543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.06, #queue-req: 0, 
[2025-07-28 00:37:52 DP0 TP0] Decode batch. #running-req: 1, #token: 401, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.01, #queue-req: 0, 
[2025-07-28 00:37:52 DP1 TP0] Decode batch. #running-req: 1, #token: 583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.10, #queue-req: 0, 
[2025-07-28 00:37:52 DP0 TP0] Decode batch. #running-req: 1, #token: 441, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.71, #queue-req: 0, 
[2025-07-28 00:37:52 DP1 TP0] Decode batch. #running-req: 1, #token: 623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.00, #queue-req: 0, 
[2025-07-28 00:37:52 DP0 TP0] Decode batch. #running-req: 1, #token: 481, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.10, #queue-req: 0, 
[2025-07-28 00:37:52 DP1 TP0] Decode batch. #running-req: 1, #token: 663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.04, #queue-req: 0, 
[2025-07-28 00:37:52] INFO:     127.0.0.1:46820 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:52 DP0 TP0] Decode batch. #running-req: 1, #token: 521, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.92, #queue-req: 0, 
[2025-07-28 00:37:52 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 111, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:53 DP1 TP0] Decode batch. #running-req: 1, #token: 238, token usage: 0.00, cuda graph: True, gen throughput (token/s): 179.83, #queue-req: 0, 
[2025-07-28 00:37:53 DP0 TP0] Decode batch. #running-req: 1, #token: 561, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.50, #queue-req: 0, 
[2025-07-28 00:37:53 DP1 TP0] Decode batch. #running-req: 1, #token: 278, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.31, #queue-req: 0, 
[2025-07-28 00:37:53 DP0 TP0] Decode batch. #running-req: 1, #token: 601, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.03, #queue-req: 0, 
[2025-07-28 00:37:53 DP1 TP0] Decode batch. #running-req: 1, #token: 318, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.52, #queue-req: 0, 
[2025-07-28 00:37:53 DP0 TP0] Decode batch. #running-req: 1, #token: 641, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.71, #queue-req: 0, 
[2025-07-28 00:37:53 DP1 TP0] Decode batch. #running-req: 1, #token: 358, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.94, #queue-req: 0, 
[2025-07-28 00:37:53 DP0 TP0] Decode batch. #running-req: 1, #token: 681, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.35, #queue-req: 0, 
[2025-07-28 00:37:53 DP1 TP0] Decode batch. #running-req: 1, #token: 398, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.92, #queue-req: 0, 
[2025-07-28 00:37:53 DP0 TP0] Decode batch. #running-req: 1, #token: 721, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.22, #queue-req: 0, 
[2025-07-28 00:37:53 DP1 TP0] Decode batch. #running-req: 1, #token: 438, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.83, #queue-req: 0, 
[2025-07-28 00:37:53] INFO:     127.0.0.1:46830 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:53 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 95, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:53 DP0 TP0] Decode batch. #running-req: 1, #token: 208, token usage: 0.00, cuda graph: True, gen throughput (token/s): 181.52, #queue-req: 0, 
[2025-07-28 00:37:54 DP1 TP0] Decode batch. #running-req: 1, #token: 478, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.12, #queue-req: 0, 
[2025-07-28 00:37:54 DP0 TP0] Decode batch. #running-req: 1, #token: 248, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.55, #queue-req: 0, 
[2025-07-28 00:37:54 DP1 TP0] Decode batch. #running-req: 1, #token: 518, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.95, #queue-req: 0, 
[2025-07-28 00:37:54 DP0 TP0] Decode batch. #running-req: 1, #token: 288, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.23, #queue-req: 0, 
[2025-07-28 00:37:54 DP1 TP0] Decode batch. #running-req: 1, #token: 558, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.34, #queue-req: 0, 
[2025-07-28 00:37:54 DP0 TP0] Decode batch. #running-req: 1, #token: 328, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.50, #queue-req: 0, 
[2025-07-28 00:37:54 DP1 TP0] Decode batch. #running-req: 1, #token: 598, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.46, #queue-req: 0, 
[2025-07-28 00:37:54 DP0 TP0] Decode batch. #running-req: 1, #token: 368, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.52, #queue-req: 0, 
[2025-07-28 00:37:54] INFO:     127.0.0.1:34368 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:54 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 89, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:54 DP1 TP0] Decode batch. #running-req: 1, #token: 213, token usage: 0.00, cuda graph: True, gen throughput (token/s): 183.62, #queue-req: 0, 
[2025-07-28 00:37:54 DP0 TP0] Decode batch. #running-req: 1, #token: 408, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.37, #queue-req: 0, 
[2025-07-28 00:37:54 DP1 TP0] Decode batch. #running-req: 1, #token: 253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.86, #queue-req: 0, 
[2025-07-28 00:37:54 DP0 TP0] Decode batch. #running-req: 1, #token: 448, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.72, #queue-req: 0, 
[2025-07-28 00:37:55 DP1 TP0] Decode batch. #running-req: 1, #token: 293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.88, #queue-req: 0, 
[2025-07-28 00:37:55 DP0 TP0] Decode batch. #running-req: 1, #token: 488, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.58, #queue-req: 0, 
[2025-07-28 00:37:55 DP1 TP0] Decode batch. #running-req: 1, #token: 333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.36, #queue-req: 0, 
[2025-07-28 00:37:55 DP0 TP0] Decode batch. #running-req: 1, #token: 528, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.47, #queue-req: 0, 
[2025-07-28 00:37:55] INFO:     127.0.0.1:34384 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:55 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 115, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:55 DP1 TP0] Decode batch. #running-req: 1, #token: 373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.78, #queue-req: 0, 
[2025-07-28 00:37:55 DP0 TP0] Decode batch. #running-req: 1, #token: 253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 184.59, #queue-req: 0, 
[2025-07-28 00:37:55 DP1 TP0] Decode batch. #running-req: 1, #token: 413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.32, #queue-req: 0, 
[2025-07-28 00:37:55 DP0 TP0] Decode batch. #running-req: 1, #token: 293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.24, #queue-req: 0, 
[2025-07-28 00:37:55 DP1 TP0] Decode batch. #running-req: 1, #token: 453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.50, #queue-req: 0, 
[2025-07-28 00:37:55 DP0 TP0] Decode batch. #running-req: 1, #token: 333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.54, #queue-req: 0, 
[2025-07-28 00:37:55 DP1 TP0] Decode batch. #running-req: 1, #token: 493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.06, #queue-req: 0, 
[2025-07-28 00:37:55 DP0 TP0] Decode batch. #running-req: 1, #token: 373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.73, #queue-req: 0, 
[2025-07-28 00:37:56 DP1 TP0] Decode batch. #running-req: 1, #token: 533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.69, #queue-req: 0, 
[2025-07-28 00:37:56 DP0 TP0] Decode batch. #running-req: 1, #token: 413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.20, #queue-req: 0, 
[2025-07-28 00:37:56 DP1 TP0] Decode batch. #running-req: 1, #token: 573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.39, #queue-req: 0, 
[2025-07-28 00:37:56 DP0 TP0] Decode batch. #running-req: 1, #token: 453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.52, #queue-req: 0, 
[2025-07-28 00:37:56 DP1 TP0] Decode batch. #running-req: 1, #token: 613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.68, #queue-req: 0, 
[2025-07-28 00:37:56 DP0 TP0] Decode batch. #running-req: 1, #token: 493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.94, #queue-req: 0, 
[2025-07-28 00:37:56 DP1 TP0] Decode batch. #running-req: 1, #token: 653, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.43, #queue-req: 0, 
[2025-07-28 00:37:56 DP0 TP0] Decode batch. #running-req: 1, #token: 533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.91, #queue-req: 0, 
[2025-07-28 00:37:56 DP1 TP0] Decode batch. #running-req: 1, #token: 693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.14, #queue-req: 0, 
[2025-07-28 00:37:56 DP0 TP0] Decode batch. #running-req: 1, #token: 573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.73, #queue-req: 0, 
[2025-07-28 00:37:56 DP1 TP0] Decode batch. #running-req: 1, #token: 733, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.16, #queue-req: 0, 
[2025-07-28 00:37:56 DP0 TP0] Decode batch. #running-req: 1, #token: 613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.53, #queue-req: 0, 
[2025-07-28 00:37:57 DP1 TP0] Decode batch. #running-req: 1, #token: 773, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.76, #queue-req: 0, 
[2025-07-28 00:37:57 DP0 TP0] Decode batch. #running-req: 1, #token: 653, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.14, #queue-req: 0, 
[2025-07-28 00:37:57 DP1 TP0] Decode batch. #running-req: 1, #token: 813, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.15, #queue-req: 0, 
[2025-07-28 00:37:57 DP0 TP0] Decode batch. #running-req: 1, #token: 693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.49, #queue-req: 0, 
[2025-07-28 00:37:57 DP1 TP0] Decode batch. #running-req: 1, #token: 853, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.09, #queue-req: 0, 
[2025-07-28 00:37:57 DP0 TP0] Decode batch. #running-req: 1, #token: 733, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.91, #queue-req: 0, 
[2025-07-28 00:37:57 DP1 TP0] Decode batch. #running-req: 1, #token: 893, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.35, #queue-req: 0, 
[2025-07-28 00:37:57] INFO:     127.0.0.1:34398 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:57 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 164, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:57 DP0 TP0] Decode batch. #running-req: 1, #token: 773, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.97, #queue-req: 0, 
[2025-07-28 00:37:57 DP1 TP0] Decode batch. #running-req: 1, #token: 308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 181.46, #queue-req: 0, 
[2025-07-28 00:37:57 DP0 TP0] Decode batch. #running-req: 1, #token: 813, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.75, #queue-req: 0, 
[2025-07-28 00:37:57] INFO:     127.0.0.1:34408 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:57 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 143, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:37:57 DP1 TP0] Decode batch. #running-req: 1, #token: 348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.67, #queue-req: 0, 
[2025-07-28 00:37:57 DP0 TP0] Decode batch. #running-req: 1, #token: 286, token usage: 0.00, cuda graph: True, gen throughput (token/s): 190.87, #queue-req: 0, 
[2025-07-28 00:37:58 DP1 TP0] Decode batch. #running-req: 1, #token: 388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.81, #queue-req: 0, 
[2025-07-28 00:37:58 DP0 TP0] Decode batch. #running-req: 1, #token: 326, token usage: 0.00, cuda graph: True, gen throughput (token/s): 249.32, #queue-req: 0, 
[2025-07-28 00:37:58 DP1 TP0] Decode batch. #running-req: 1, #token: 428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.19, #queue-req: 0, 
[2025-07-28 00:37:58 DP0 TP0] Decode batch. #running-req: 1, #token: 366, token usage: 0.00, cuda graph: True, gen throughput (token/s): 249.21, #queue-req: 0, 
[2025-07-28 00:37:58 DP1 TP0] Decode batch. #running-req: 1, #token: 468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.44, #queue-req: 0, 
[2025-07-28 00:37:58 DP0 TP0] Decode batch. #running-req: 1, #token: 406, token usage: 0.00, cuda graph: True, gen throughput (token/s): 249.25, #queue-req: 0, 
[2025-07-28 00:37:58 DP1 TP0] Decode batch. #running-req: 1, #token: 508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.41, #queue-req: 0, 
[2025-07-28 00:37:58 DP0 TP0] Decode batch. #running-req: 1, #token: 446, token usage: 0.00, cuda graph: True, gen throughput (token/s): 249.25, #queue-req: 0, 
[2025-07-28 00:37:58 DP1 TP0] Decode batch. #running-req: 1, #token: 548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.45, #queue-req: 0, 
[2025-07-28 00:37:58 DP0 TP0] Decode batch. #running-req: 1, #token: 486, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.80, #queue-req: 0, 
[2025-07-28 00:37:58 DP1 TP0] Decode batch. #running-req: 1, #token: 588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.34, #queue-req: 0, 
[2025-07-28 00:37:58 DP0 TP0] Decode batch. #running-req: 1, #token: 526, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.43, #queue-req: 0, 
[2025-07-28 00:37:59 DP1 TP0] Decode batch. #running-req: 1, #token: 628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.73, #queue-req: 0, 
[2025-07-28 00:37:59 DP0 TP0] Decode batch. #running-req: 1, #token: 566, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.29, #queue-req: 0, 
[2025-07-28 00:37:59 DP1 TP0] Decode batch. #running-req: 1, #token: 668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.99, #queue-req: 0, 
[2025-07-28 00:37:59 DP0 TP0] Decode batch. #running-req: 1, #token: 606, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.14, #queue-req: 0, 
[2025-07-28 00:37:59 DP1 TP0] Decode batch. #running-req: 1, #token: 708, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.81, #queue-req: 0, 
[2025-07-28 00:37:59 DP0 TP0] Decode batch. #running-req: 1, #token: 646, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.14, #queue-req: 0, 
[2025-07-28 00:37:59] INFO:     127.0.0.1:34422 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:37:59 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 117, #cached-token: 109, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:37:59 DP1 TP0] Decode batch. #running-req: 2, #token: 883, token usage: 0.00, cuda graph: True, gen throughput (token/s): 279.74, #queue-req: 0, 
[2025-07-28 00:37:59 DP1 TP0] Decode batch. #running-req: 2, #token: 963, token usage: 0.00, cuda graph: True, gen throughput (token/s): 475.39, #queue-req: 0, 
[2025-07-28 00:37:59 DP1 TP0] Decode batch. #running-req: 2, #token: 1043, token usage: 0.00, cuda graph: True, gen throughput (token/s): 475.05, #queue-req: 0, 
[2025-07-28 00:38:00 DP1 TP0] Decode batch. #running-req: 2, #token: 1123, token usage: 0.00, cuda graph: True, gen throughput (token/s): 468.51, #queue-req: 0, 
[2025-07-28 00:38:00] INFO:     127.0.0.1:34418 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:38:00 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 125, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:38:00 DP1 TP0] Decode batch. #running-req: 1, #token: 403, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.27, #queue-req: 0, 
[2025-07-28 00:38:00 DP0 TP0] Decode batch. #running-req: 1, #token: 269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 43.86, #queue-req: 0, 
[2025-07-28 00:38:00 DP1 TP0] Decode batch. #running-req: 1, #token: 443, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.93, #queue-req: 0, 
[2025-07-28 00:38:00 DP0 TP0] Decode batch. #running-req: 1, #token: 309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.71, #queue-req: 0, 
[2025-07-28 00:38:00 DP1 TP0] Decode batch. #running-req: 1, #token: 483, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.71, #queue-req: 0, 
[2025-07-28 00:38:00 DP0 TP0] Decode batch. #running-req: 1, #token: 349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.26, #queue-req: 0, 
[2025-07-28 00:38:00 DP1 TP0] Decode batch. #running-req: 1, #token: 523, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.18, #queue-req: 0, 
[2025-07-28 00:38:00 DP0 TP0] Decode batch. #running-req: 1, #token: 389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.95, #queue-req: 0, 
[2025-07-28 00:38:00 DP1 TP0] Decode batch. #running-req: 1, #token: 563, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.46, #queue-req: 0, 
[2025-07-28 00:38:00 DP0 TP0] Decode batch. #running-req: 1, #token: 429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.91, #queue-req: 0, 
[2025-07-28 00:38:01 DP1 TP0] Decode batch. #running-req: 1, #token: 603, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.07, #queue-req: 0, 
[2025-07-28 00:38:01 DP0 TP0] Decode batch. #running-req: 1, #token: 469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.55, #queue-req: 0, 
[2025-07-28 00:38:01] INFO:     127.0.0.1:34434 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:38:01 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 196, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:38:01 DP1 TP0] Decode batch. #running-req: 1, #token: 312, token usage: 0.00, cuda graph: True, gen throughput (token/s): 182.63, #queue-req: 0, 
[2025-07-28 00:38:01 DP0 TP0] Decode batch. #running-req: 1, #token: 509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.10, #queue-req: 0, 
[2025-07-28 00:38:01 DP1 TP0] Decode batch. #running-req: 1, #token: 352, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.04, #queue-req: 0, 
[2025-07-28 00:38:01 DP0 TP0] Decode batch. #running-req: 1, #token: 549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.67, #queue-req: 0, 
[2025-07-28 00:38:01 DP1 TP0] Decode batch. #running-req: 1, #token: 392, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.54, #queue-req: 0, 
[2025-07-28 00:38:01 DP0 TP0] Decode batch. #running-req: 1, #token: 589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.60, #queue-req: 0, 
[2025-07-28 00:38:01 DP1 TP0] Decode batch. #running-req: 1, #token: 432, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.07, #queue-req: 0, 
[2025-07-28 00:38:01 DP0 TP0] Decode batch. #running-req: 1, #token: 629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.49, #queue-req: 0, 
[2025-07-28 00:38:01 DP1 TP0] Decode batch. #running-req: 1, #token: 472, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.30, #queue-req: 0, 
[2025-07-28 00:38:01 DP0 TP0] Decode batch. #running-req: 1, #token: 669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.70, #queue-req: 0, 
[2025-07-28 00:38:02 DP1 TP0] Decode batch. #running-req: 1, #token: 512, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.32, #queue-req: 0, 
[2025-07-28 00:38:02 DP0 TP0] Decode batch. #running-req: 1, #token: 709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.65, #queue-req: 0, 
[2025-07-28 00:38:02 DP1 TP0] Decode batch. #running-req: 1, #token: 552, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.03, #queue-req: 0, 
[2025-07-28 00:38:02 DP0 TP0] Decode batch. #running-req: 1, #token: 749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.33, #queue-req: 0, 
[2025-07-28 00:38:02 DP1 TP0] Decode batch. #running-req: 1, #token: 592, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.28, #queue-req: 0, 
[2025-07-28 00:38:02 DP0 TP0] Decode batch. #running-req: 1, #token: 789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.47, #queue-req: 0, 
[2025-07-28 00:38:02 DP1 TP0] Decode batch. #running-req: 1, #token: 632, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.34, #queue-req: 0, 
[2025-07-28 00:38:02 DP0 TP0] Decode batch. #running-req: 1, #token: 829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.65, #queue-req: 0, 
[2025-07-28 00:38:02] INFO:     127.0.0.1:34438 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:38:02 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:38:02 DP1 TP0] Decode batch. #running-req: 1, #token: 672, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.31, #queue-req: 0, 
[2025-07-28 00:38:02 DP0 TP0] Decode batch. #running-req: 1, #token: 216, token usage: 0.00, cuda graph: True, gen throughput (token/s): 190.98, #queue-req: 0, 
[2025-07-28 00:38:02 DP1 TP0] Decode batch. #running-req: 1, #token: 712, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.42, #queue-req: 0, 
[2025-07-28 00:38:02 DP0 TP0] Decode batch. #running-req: 1, #token: 256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.87, #queue-req: 0, 
[2025-07-28 00:38:03] INFO:     127.0.0.1:34444 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:38:03 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 154, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:38:03 DP1 TP0] Decode batch. #running-req: 1, #token: 275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 187.50, #queue-req: 0, 
[2025-07-28 00:38:03 DP0 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.77, #queue-req: 0, 
[2025-07-28 00:38:03 DP1 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.52, #queue-req: 0, 
[2025-07-28 00:38:03 DP0 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.39, #queue-req: 0, 
[2025-07-28 00:38:03 DP1 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.22, #queue-req: 0, 
[2025-07-28 00:38:03 DP0 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.81, #queue-req: 0, 
[2025-07-28 00:38:03 DP1 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.38, #queue-req: 0, 
[2025-07-28 00:38:03 DP0 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.32, #queue-req: 0, 
[2025-07-28 00:38:03 DP1 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.21, #queue-req: 0, 
[2025-07-28 00:38:03 DP0 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.03, #queue-req: 0, 
[2025-07-28 00:38:03 DP1 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.34, #queue-req: 0, 
[2025-07-28 00:38:03 DP0 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.28, #queue-req: 0, 
[2025-07-28 00:38:04 DP1 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.31, #queue-req: 0, 
[2025-07-28 00:38:04 DP0 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.91, #queue-req: 0, 
[2025-07-28 00:38:04 DP1 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.46, #queue-req: 0, 
[2025-07-28 00:38:04 DP0 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.90, #queue-req: 0, 
[2025-07-28 00:38:04 DP1 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.92, #queue-req: 0, 
[2025-07-28 00:38:04 DP0 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.97, #queue-req: 0, 
[2025-07-28 00:38:04 DP1 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.20, #queue-req: 0, 
[2025-07-28 00:38:04 DP0 TP0] Decode batch. #running-req: 1, #token: 656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.02, #queue-req: 0, 
[2025-07-28 00:38:04 DP1 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.05, #queue-req: 0, 
[2025-07-28 00:38:04 DP0 TP0] Decode batch. #running-req: 1, #token: 696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.71, #queue-req: 0, 
[2025-07-28 00:38:04] INFO:     127.0.0.1:58954 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:38:04 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 128, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:38:04 DP1 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.41, #queue-req: 0, 
[2025-07-28 00:38:04 DP0 TP0] Decode batch. #running-req: 1, #token: 270, token usage: 0.00, cuda graph: True, gen throughput (token/s): 188.85, #queue-req: 0, 
[2025-07-28 00:38:05 DP1 TP0] Decode batch. #running-req: 1, #token: 755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.56, #queue-req: 0, 
[2025-07-28 00:38:05 DP0 TP0] Decode batch. #running-req: 1, #token: 310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.14, #queue-req: 0, 
[2025-07-28 00:38:05 DP1 TP0] Decode batch. #running-req: 1, #token: 795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.92, #queue-req: 0, 
[2025-07-28 00:38:05 DP0 TP0] Decode batch. #running-req: 1, #token: 350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.39, #queue-req: 0, 
[2025-07-28 00:38:05 DP1 TP0] Decode batch. #running-req: 1, #token: 835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.37, #queue-req: 0, 
[2025-07-28 00:38:05 DP0 TP0] Decode batch. #running-req: 1, #token: 390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.19, #queue-req: 0, 
[2025-07-28 00:38:05 DP1 TP0] Decode batch. #running-req: 1, #token: 875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.49, #queue-req: 0, 
[2025-07-28 00:38:05 DP0 TP0] Decode batch. #running-req: 1, #token: 430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.45, #queue-req: 0, 
[2025-07-28 00:38:05 DP1 TP0] Decode batch. #running-req: 1, #token: 915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.85, #queue-req: 0, 
[2025-07-28 00:38:05] INFO:     127.0.0.1:58958 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:38:05 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:38:05 DP0 TP0] Decode batch. #running-req: 1, #token: 470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.19, #queue-req: 0, 
[2025-07-28 00:38:05 DP1 TP0] Decode batch. #running-req: 1, #token: 281, token usage: 0.00, cuda graph: True, gen throughput (token/s): 181.91, #queue-req: 0, 
[2025-07-28 00:38:05 DP0 TP0] Decode batch. #running-req: 1, #token: 510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.05, #queue-req: 0, 
[2025-07-28 00:38:06 DP1 TP0] Decode batch. #running-req: 1, #token: 321, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.16, #queue-req: 0, 
[2025-07-28 00:38:06 DP0 TP0] Decode batch. #running-req: 1, #token: 550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.80, #queue-req: 0, 
[2025-07-28 00:38:06 DP1 TP0] Decode batch. #running-req: 1, #token: 361, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.87, #queue-req: 0, 
[2025-07-28 00:38:06 DP0 TP0] Decode batch. #running-req: 1, #token: 590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.16, #queue-req: 0, 
[2025-07-28 00:38:06 DP1 TP0] Decode batch. #running-req: 1, #token: 401, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.64, #queue-req: 0, 
[2025-07-28 00:38:06 DP0 TP0] Decode batch. #running-req: 1, #token: 630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.05, #queue-req: 0, 
[2025-07-28 00:38:06 DP1 TP0] Decode batch. #running-req: 1, #token: 441, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.94, #queue-req: 0, 
[2025-07-28 00:38:06 DP0 TP0] Decode batch. #running-req: 1, #token: 670, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.09, #queue-req: 0, 
[2025-07-28 00:38:06 DP1 TP0] Decode batch. #running-req: 1, #token: 481, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.88, #queue-req: 0, 
[2025-07-28 00:38:06] INFO:     127.0.0.1:58964 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:38:06 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:38:06 DP0 TP0] Decode batch. #running-req: 1, #token: 251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 187.28, #queue-req: 0, 
[2025-07-28 00:38:06 DP1 TP0] Decode batch. #running-req: 1, #token: 521, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.61, #queue-req: 0, 
[2025-07-28 00:38:06 DP0 TP0] Decode batch. #running-req: 1, #token: 291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.34, #queue-req: 0, 
[2025-07-28 00:38:07 DP1 TP0] Decode batch. #running-req: 1, #token: 561, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.39, #queue-req: 0, 
[2025-07-28 00:38:07 DP0 TP0] Decode batch. #running-req: 1, #token: 331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.93, #queue-req: 0, 
[2025-07-28 00:38:07 DP1 TP0] Decode batch. #running-req: 1, #token: 601, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.16, #queue-req: 0, 
[2025-07-28 00:38:07] INFO:     127.0.0.1:58976 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:38:07 DP0 TP0] Decode batch. #running-req: 1, #token: 371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.51, #queue-req: 0, 
[2025-07-28 00:38:07 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 95, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:38:07 DP1 TP0] Decode batch. #running-req: 1, #token: 227, token usage: 0.00, cuda graph: True, gen throughput (token/s): 185.49, #queue-req: 0, 
[2025-07-28 00:38:07 DP0 TP0] Decode batch. #running-req: 1, #token: 411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.33, #queue-req: 0, 
[2025-07-28 00:38:07 DP1 TP0] Decode batch. #running-req: 1, #token: 267, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.85, #queue-req: 0, 
[2025-07-28 00:38:07 DP0 TP0] Decode batch. #running-req: 1, #token: 451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.39, #queue-req: 0, 
[2025-07-28 00:38:07 DP1 TP0] Decode batch. #running-req: 1, #token: 307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.18, #queue-req: 0, 
[2025-07-28 00:38:07 DP0 TP0] Decode batch. #running-req: 1, #token: 491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.96, #queue-req: 0, 
[2025-07-28 00:38:07 DP1 TP0] Decode batch. #running-req: 1, #token: 347, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.04, #queue-req: 0, 
[2025-07-28 00:38:07 DP0 TP0] Decode batch. #running-req: 1, #token: 531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.59, #queue-req: 0, 
[2025-07-28 00:38:08 DP1 TP0] Decode batch. #running-req: 1, #token: 387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.68, #queue-req: 0, 
[2025-07-28 00:38:08 DP0 TP0] Decode batch. #running-req: 1, #token: 571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.43, #queue-req: 0, 
[2025-07-28 00:38:08 DP1 TP0] Decode batch. #running-req: 1, #token: 427, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.92, #queue-req: 0, 
[2025-07-28 00:38:08 DP0 TP0] Decode batch. #running-req: 1, #token: 611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.25, #queue-req: 0, 
[2025-07-28 00:38:08 DP1 TP0] Decode batch. #running-req: 1, #token: 467, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.40, #queue-req: 0, 
[2025-07-28 00:38:08 DP0 TP0] Decode batch. #running-req: 1, #token: 651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.74, #queue-req: 0, 
[2025-07-28 00:38:08 DP1 TP0] Decode batch. #running-req: 1, #token: 507, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.70, #queue-req: 0, 
[2025-07-28 00:38:08 DP0 TP0] Decode batch. #running-req: 1, #token: 691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.80, #queue-req: 0, 
[2025-07-28 00:38:08] INFO:     127.0.0.1:58990 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:38:08 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 107, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:38:08 DP1 TP0] Decode batch. #running-req: 1, #token: 547, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.27, #queue-req: 0, 
[2025-07-28 00:38:08 DP0 TP0] Decode batch. #running-req: 1, #token: 269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 191.28, #queue-req: 0, 
[2025-07-28 00:38:08 DP1 TP0] Decode batch. #running-req: 1, #token: 587, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.38, #queue-req: 0, 
[2025-07-28 00:38:08 DP0 TP0] Decode batch. #running-req: 1, #token: 309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.44, #queue-req: 0, 
[2025-07-28 00:38:09 DP1 TP0] Decode batch. #running-req: 1, #token: 627, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.15, #queue-req: 0, 
[2025-07-28 00:38:09 DP0 TP0] Decode batch. #running-req: 1, #token: 349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.15, #queue-req: 0, 
[2025-07-28 00:38:09 DP1 TP0] Decode batch. #running-req: 1, #token: 667, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.40, #queue-req: 0, 
[2025-07-28 00:38:09 DP0 TP0] Decode batch. #running-req: 1, #token: 389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.80, #queue-req: 0, 
[2025-07-28 00:38:09 DP1 TP0] Decode batch. #running-req: 1, #token: 707, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.60, #queue-req: 0, 
[2025-07-28 00:38:09 DP0 TP0] Decode batch. #running-req: 1, #token: 429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.45, #queue-req: 0, 
[2025-07-28 00:38:09 DP1 TP0] Decode batch. #running-req: 1, #token: 747, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.51, #queue-req: 0, 
[2025-07-28 00:38:09 DP0 TP0] Decode batch. #running-req: 1, #token: 469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.55, #queue-req: 0, 
[2025-07-28 00:38:09 DP1 TP0] Decode batch. #running-req: 1, #token: 787, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.18, #queue-req: 0, 
[2025-07-28 00:38:09] INFO:     127.0.0.1:58998 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:38:09 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 78, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:38:09 DP0 TP0] Decode batch. #running-req: 1, #token: 509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.14, #queue-req: 0, 
[2025-07-28 00:38:09 DP1 TP0] Decode batch. #running-req: 1, #token: 216, token usage: 0.00, cuda graph: True, gen throughput (token/s): 183.58, #queue-req: 0, 
[2025-07-28 00:38:09 DP0 TP0] Decode batch. #running-req: 1, #token: 549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.30, #queue-req: 0, 
[2025-07-28 00:38:10 DP1 TP0] Decode batch. #running-req: 1, #token: 256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.87, #queue-req: 0, 
[2025-07-28 00:38:10 DP0 TP0] Decode batch. #running-req: 1, #token: 589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.02, #queue-req: 0, 
[2025-07-28 00:38:10 DP1 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.93, #queue-req: 0, 
[2025-07-28 00:38:10] INFO:     127.0.0.1:59012 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:38:10 DP0 TP0] Decode batch. #running-req: 1, #token: 0, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.13, #queue-req: 0, 
[2025-07-28 00:38:10 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 146, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:38:10 DP1 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.08, #queue-req: 0, 
[2025-07-28 00:38:10 DP0 TP0] Decode batch. #running-req: 1, #token: 295, token usage: 0.00, cuda graph: True, gen throughput (token/s): 188.72, #queue-req: 0, 
[2025-07-28 00:38:10 DP1 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.89, #queue-req: 0, 
[2025-07-28 00:38:10 DP0 TP0] Decode batch. #running-req: 1, #token: 335, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.68, #queue-req: 0, 
[2025-07-28 00:38:10 DP1 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.52, #queue-req: 0, 
[2025-07-28 00:38:10 DP0 TP0] Decode batch. #running-req: 1, #token: 375, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.96, #queue-req: 0, 
[2025-07-28 00:38:10 DP1 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.90, #queue-req: 0, 
[2025-07-28 00:38:10 DP0 TP0] Decode batch. #running-req: 1, #token: 415, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.31, #queue-req: 0, 
[2025-07-28 00:38:11 DP1 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.64, #queue-req: 0, 
[2025-07-28 00:38:11 DP0 TP0] Decode batch. #running-req: 1, #token: 455, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.81, #queue-req: 0, 
[2025-07-28 00:38:11 DP1 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.20, #queue-req: 0, 
[2025-07-28 00:38:11 DP0 TP0] Decode batch. #running-req: 1, #token: 495, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.44, #queue-req: 0, 
[2025-07-28 00:38:11 DP1 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.68, #queue-req: 0, 
[2025-07-28 00:38:11 DP0 TP0] Decode batch. #running-req: 1, #token: 535, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.37, #queue-req: 0, 
[2025-07-28 00:38:11 DP1 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.01, #queue-req: 0, 
[2025-07-28 00:38:11 DP0 TP0] Decode batch. #running-req: 1, #token: 575, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.25, #queue-req: 0, 
[2025-07-28 00:38:11 DP1 TP0] Decode batch. #running-req: 1, #token: 656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.07, #queue-req: 0, 
[2025-07-28 00:38:11 DP0 TP0] Decode batch. #running-req: 1, #token: 615, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.12, #queue-req: 0, 
[2025-07-28 00:38:11 DP1 TP0] Decode batch. #running-req: 1, #token: 696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.23, #queue-req: 0, 
[2025-07-28 00:38:11] INFO:     127.0.0.1:59026 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:38:11 DP0 TP0] Decode batch. #running-req: 1, #token: 0, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.10, #queue-req: 0, 
[2025-07-28 00:38:11 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 108, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:38:12 DP1 TP0] Decode batch. #running-req: 2, #token: 846, token usage: 0.00, cuda graph: True, gen throughput (token/s): 321.26, #queue-req: 0, 
[2025-07-28 00:38:12 DP1 TP0] Decode batch. #running-req: 2, #token: 926, token usage: 0.00, cuda graph: True, gen throughput (token/s): 475.81, #queue-req: 0, 
[2025-07-28 00:38:12] INFO:     127.0.0.1:59016 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:38:12 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 173, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:38:12 DP1 TP0] Decode batch. #running-req: 1, #token: 298, token usage: 0.00, cuda graph: True, gen throughput (token/s): 435.82, #queue-req: 0, 
[2025-07-28 00:38:12 DP1 TP0] Decode batch. #running-req: 1, #token: 338, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.37, #queue-req: 0, 
[2025-07-28 00:38:12 DP0 TP0] Decode batch. #running-req: 1, #token: 322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 56.29, #queue-req: 0, 
[2025-07-28 00:38:12 DP1 TP0] Decode batch. #running-req: 1, #token: 378, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.32, #queue-req: 0, 
[2025-07-28 00:38:12 DP0 TP0] Decode batch. #running-req: 1, #token: 362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.94, #queue-req: 0, 
[2025-07-28 00:38:12 DP1 TP0] Decode batch. #running-req: 1, #token: 418, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.33, #queue-req: 0, 
[2025-07-28 00:38:12 DP0 TP0] Decode batch. #running-req: 1, #token: 402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.52, #queue-req: 0, 
[2025-07-28 00:38:13 DP1 TP0] Decode batch. #running-req: 1, #token: 458, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.37, #queue-req: 0, 
[2025-07-28 00:38:13 DP0 TP0] Decode batch. #running-req: 1, #token: 442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.41, #queue-req: 0, 
[2025-07-28 00:38:13 DP1 TP0] Decode batch. #running-req: 1, #token: 498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.69, #queue-req: 0, 
[2025-07-28 00:38:13 DP0 TP0] Decode batch. #running-req: 1, #token: 482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.41, #queue-req: 0, 
[2025-07-28 00:38:13 DP1 TP0] Decode batch. #running-req: 1, #token: 538, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.49, #queue-req: 0, 
[2025-07-28 00:38:13 DP0 TP0] Decode batch. #running-req: 1, #token: 522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.80, #queue-req: 0, 
[2025-07-28 00:38:13 DP1 TP0] Decode batch. #running-req: 1, #token: 578, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.94, #queue-req: 0, 
[2025-07-28 00:38:13 DP0 TP0] Decode batch. #running-req: 1, #token: 562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.94, #queue-req: 0, 
[2025-07-28 00:38:13 DP1 TP0] Decode batch. #running-req: 1, #token: 618, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.22, #queue-req: 0, 
[2025-07-28 00:38:13 DP0 TP0] Decode batch. #running-req: 1, #token: 602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.06, #queue-req: 0, 
[2025-07-28 00:38:13 DP1 TP0] Decode batch. #running-req: 1, #token: 658, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.22, #queue-req: 0, 
[2025-07-28 00:38:13 DP0 TP0] Decode batch. #running-req: 1, #token: 642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.24, #queue-req: 0, 
[2025-07-28 00:38:14 DP1 TP0] Decode batch. #running-req: 1, #token: 698, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.07, #queue-req: 0, 
[2025-07-28 00:38:14 DP0 TP0] Decode batch. #running-req: 1, #token: 682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.09, #queue-req: 0, 
[2025-07-28 00:38:14 DP1 TP0] Decode batch. #running-req: 1, #token: 738, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.28, #queue-req: 0, 
[2025-07-28 00:38:14 DP0 TP0] Decode batch. #running-req: 1, #token: 722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.92, #queue-req: 0, 
[2025-07-28 00:38:14 DP1 TP0] Decode batch. #running-req: 1, #token: 778, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.90, #queue-req: 0, 
[2025-07-28 00:38:14 DP0 TP0] Decode batch. #running-req: 1, #token: 762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.50, #queue-req: 0, 
[2025-07-28 00:38:14 DP1 TP0] Decode batch. #running-req: 1, #token: 818, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.10, #queue-req: 0, 
[2025-07-28 00:38:14 DP0 TP0] Decode batch. #running-req: 1, #token: 802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.00, #queue-req: 0, 
[2025-07-28 00:38:14 DP1 TP0] Decode batch. #running-req: 1, #token: 858, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.42, #queue-req: 0, 
[2025-07-28 00:38:14 DP0 TP0] Decode batch. #running-req: 1, #token: 842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.08, #queue-req: 0, 
[2025-07-28 00:38:14] INFO:     127.0.0.1:59042 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:38:14 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 178, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:38:14] INFO:     127.0.0.1:47964 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:38:14 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 88, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:38:14 DP1 TP0] Decode batch. #running-req: 1, #token: 300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 179.60, #queue-req: 0, 
[2025-07-28 00:38:14 DP0 TP0] Decode batch. #running-req: 1, #token: 206, token usage: 0.00, cuda graph: True, gen throughput (token/s): 187.33, #queue-req: 0, 
[2025-07-28 00:38:15 DP1 TP0] Decode batch. #running-req: 1, #token: 340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.09, #queue-req: 0, 
[2025-07-28 00:38:15 DP0 TP0] Decode batch. #running-req: 1, #token: 246, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.18, #queue-req: 0, 
[2025-07-28 00:38:15 DP1 TP0] Decode batch. #running-req: 1, #token: 380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.45, #queue-req: 0, 
[2025-07-28 00:38:15 DP0 TP0] Decode batch. #running-req: 1, #token: 286, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.85, #queue-req: 0, 
[2025-07-28 00:38:15 DP1 TP0] Decode batch. #running-req: 1, #token: 420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.29, #queue-req: 0, 
[2025-07-28 00:38:15 DP0 TP0] Decode batch. #running-req: 1, #token: 326, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.31, #queue-req: 0, 
[2025-07-28 00:38:15 DP1 TP0] Decode batch. #running-req: 1, #token: 460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.48, #queue-req: 0, 
[2025-07-28 00:38:15 DP0 TP0] Decode batch. #running-req: 1, #token: 366, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.02, #queue-req: 0, 
[2025-07-28 00:38:15 DP1 TP0] Decode batch. #running-req: 1, #token: 500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.23, #queue-req: 0, 
[2025-07-28 00:38:15 DP0 TP0] Decode batch. #running-req: 1, #token: 406, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.19, #queue-req: 0, 
[2025-07-28 00:38:15 DP1 TP0] Decode batch. #running-req: 1, #token: 540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.00, #queue-req: 0, 
[2025-07-28 00:38:15 DP0 TP0] Decode batch. #running-req: 1, #token: 446, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.27, #queue-req: 0, 
[2025-07-28 00:38:16 DP1 TP0] Decode batch. #running-req: 1, #token: 580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.64, #queue-req: 0, 
[2025-07-28 00:38:16 DP0 TP0] Decode batch. #running-req: 1, #token: 486, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.66, #queue-req: 0, 
[2025-07-28 00:38:16 DP1 TP0] Decode batch. #running-req: 1, #token: 620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.18, #queue-req: 0, 
[2025-07-28 00:38:16 DP0 TP0] Decode batch. #running-req: 1, #token: 526, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.07, #queue-req: 0, 
[2025-07-28 00:38:16 DP1 TP0] Decode batch. #running-req: 1, #token: 660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.54, #queue-req: 0, 
[2025-07-28 00:38:16 DP0 TP0] Decode batch. #running-req: 1, #token: 566, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.07, #queue-req: 0, 
[2025-07-28 00:38:16 DP1 TP0] Decode batch. #running-req: 1, #token: 700, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.93, #queue-req: 0, 
[2025-07-28 00:38:16 DP0 TP0] Decode batch. #running-req: 1, #token: 606, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.18, #queue-req: 0, 
[2025-07-28 00:38:16 DP1 TP0] Decode batch. #running-req: 1, #token: 740, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.89, #queue-req: 0, 
[2025-07-28 00:38:16 DP0 TP0] Decode batch. #running-req: 1, #token: 646, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.22, #queue-req: 0, 
[2025-07-28 00:38:16 DP1 TP0] Decode batch. #running-req: 1, #token: 780, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.01, #queue-req: 0, 
[2025-07-28 00:38:16 DP0 TP0] Decode batch. #running-req: 1, #token: 686, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.08, #queue-req: 0, 
[2025-07-28 00:38:17 DP1 TP0] Decode batch. #running-req: 1, #token: 820, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.96, #queue-req: 0, 
[2025-07-28 00:38:17] INFO:     127.0.0.1:47966 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:38:17 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 153, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:38:17] INFO:     127.0.0.1:47980 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:38:17 DP0 TP0] Decode batch. #running-req: 1, #token: 0, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.28, #queue-req: 0, 
[2025-07-28 00:38:17 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 123, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:38:17 DP1 TP0] Decode batch. #running-req: 1, #token: 300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 183.38, #queue-req: 0, 
[2025-07-28 00:38:17 DP0 TP0] Decode batch. #running-req: 1, #token: 272, token usage: 0.00, cuda graph: True, gen throughput (token/s): 188.10, #queue-req: 0, 
[2025-07-28 00:38:17 DP1 TP0] Decode batch. #running-req: 1, #token: 340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.15, #queue-req: 0, 
[2025-07-28 00:38:17 DP0 TP0] Decode batch. #running-req: 1, #token: 312, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.46, #queue-req: 0, 
[2025-07-28 00:38:17 DP1 TP0] Decode batch. #running-req: 1, #token: 380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.09, #queue-req: 0, 
[2025-07-28 00:38:17 DP0 TP0] Decode batch. #running-req: 1, #token: 352, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.26, #queue-req: 0, 
[2025-07-28 00:38:17 DP1 TP0] Decode batch. #running-req: 1, #token: 420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.21, #queue-req: 0, 
[2025-07-28 00:38:17 DP0 TP0] Decode batch. #running-req: 1, #token: 392, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.70, #queue-req: 0, 
[2025-07-28 00:38:17 DP1 TP0] Decode batch. #running-req: 1, #token: 460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.91, #queue-req: 0, 
[2025-07-28 00:38:17 DP0 TP0] Decode batch. #running-req: 1, #token: 432, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.87, #queue-req: 0, 
[2025-07-28 00:38:18 DP1 TP0] Decode batch. #running-req: 1, #token: 500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.17, #queue-req: 0, 
[2025-07-28 00:38:18 DP0 TP0] Decode batch. #running-req: 1, #token: 472, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.15, #queue-req: 0, 
[2025-07-28 00:38:18 DP1 TP0] Decode batch. #running-req: 1, #token: 540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.82, #queue-req: 0, 
[2025-07-28 00:38:18 DP0 TP0] Decode batch. #running-req: 1, #token: 512, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.02, #queue-req: 0, 
[2025-07-28 00:38:18 DP1 TP0] Decode batch. #running-req: 1, #token: 580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.02, #queue-req: 0, 
[2025-07-28 00:38:18 DP0 TP0] Decode batch. #running-req: 1, #token: 552, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.79, #queue-req: 0, 
[2025-07-28 00:38:18 DP1 TP0] Decode batch. #running-req: 1, #token: 620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.18, #queue-req: 0, 
[2025-07-28 00:38:18 DP0 TP0] Decode batch. #running-req: 1, #token: 592, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.65, #queue-req: 0, 
[2025-07-28 00:38:18 DP1 TP0] Decode batch. #running-req: 1, #token: 660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.91, #queue-req: 0, 
[2025-07-28 00:38:18 DP0 TP0] Decode batch. #running-req: 1, #token: 632, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.86, #queue-req: 0, 
[2025-07-28 00:38:18 DP1 TP0] Decode batch. #running-req: 1, #token: 700, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.77, #queue-req: 0, 
[2025-07-28 00:38:18 DP0 TP0] Decode batch. #running-req: 1, #token: 672, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.86, #queue-req: 0, 
[2025-07-28 00:38:18] INFO:     127.0.0.1:47994 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:38:19 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 129, #cached-token: 108, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:38:19 DP1 TP0] Decode batch. #running-req: 1, #token: 868, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.47, #queue-req: 0, 
[2025-07-28 00:38:19 DP1 TP0] Decode batch. #running-req: 2, #token: 950, token usage: 0.00, cuda graph: True, gen throughput (token/s): 459.58, #queue-req: 0, 
[2025-07-28 00:38:19] INFO:     127.0.0.1:47990 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:38:19 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 112, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:38:19 DP1 TP0] Decode batch. #running-req: 1, #token: 318, token usage: 0.00, cuda graph: True, gen throughput (token/s): 419.38, #queue-req: 0, 
[2025-07-28 00:38:19 DP0 TP0] Decode batch. #running-req: 1, #token: 256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.10, #queue-req: 0, 
[2025-07-28 00:38:19 DP1 TP0] Decode batch. #running-req: 1, #token: 358, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.42, #queue-req: 0, 
[2025-07-28 00:38:19 DP0 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.62, #queue-req: 0, 
[2025-07-28 00:38:19 DP1 TP0] Decode batch. #running-req: 1, #token: 398, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.56, #queue-req: 0, 
[2025-07-28 00:38:19 DP0 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.24, #queue-req: 0, 
[2025-07-28 00:38:19 DP1 TP0] Decode batch. #running-req: 1, #token: 438, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.87, #queue-req: 0, 
[2025-07-28 00:38:19 DP0 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.47, #queue-req: 0, 
[2025-07-28 00:38:20 DP1 TP0] Decode batch. #running-req: 1, #token: 478, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.96, #queue-req: 0, 
[2025-07-28 00:38:20 DP0 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 233.98, #queue-req: 0, 
[2025-07-28 00:38:20 DP1 TP0] Decode batch. #running-req: 1, #token: 518, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.64, #queue-req: 0, 
[2025-07-28 00:38:20 DP0 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.35, #queue-req: 0, 
[2025-07-28 00:38:20 DP1 TP0] Decode batch. #running-req: 1, #token: 558, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.18, #queue-req: 0, 
[2025-07-28 00:38:20 DP0 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.67, #queue-req: 0, 
[2025-07-28 00:38:20 DP1 TP0] Decode batch. #running-req: 1, #token: 598, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.44, #queue-req: 0, 
[2025-07-28 00:38:20 DP0 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.42, #queue-req: 0, 
[2025-07-28 00:38:20 DP1 TP0] Decode batch. #running-req: 1, #token: 638, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.38, #queue-req: 0, 
[2025-07-28 00:38:20 DP0 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.83, #queue-req: 0, 
[2025-07-28 00:38:20 DP1 TP0] Decode batch. #running-req: 1, #token: 678, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.76, #queue-req: 0, 
[2025-07-28 00:38:20 DP0 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.51, #queue-req: 0, 
[2025-07-28 00:38:21 DP1 TP0] Decode batch. #running-req: 1, #token: 718, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.86, #queue-req: 0, 
[2025-07-28 00:38:21 DP0 TP0] Decode batch. #running-req: 1, #token: 656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.96, #queue-req: 0, 
[2025-07-28 00:38:21 DP1 TP0] Decode batch. #running-req: 1, #token: 758, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.75, #queue-req: 0, 
[2025-07-28 00:38:21 DP0 TP0] Decode batch. #running-req: 1, #token: 696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.81, #queue-req: 0, 
[2025-07-28 00:38:21 DP1 TP0] Decode batch. #running-req: 1, #token: 798, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.84, #queue-req: 0, 
[2025-07-28 00:38:21] INFO:     127.0.0.1:47998 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:38:21 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 113, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:38:21 DP0 TP0] Decode batch. #running-req: 1, #token: 736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.18, #queue-req: 0, 
[2025-07-28 00:38:21 DP1 TP0] Decode batch. #running-req: 1, #token: 274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 184.09, #queue-req: 0, 
[2025-07-28 00:38:21 DP0 TP0] Decode batch. #running-req: 1, #token: 776, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.32, #queue-req: 0, 
[2025-07-28 00:38:21] INFO:     127.0.0.1:48012 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:38:21 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 103, #cached-token: 112, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:38:21 DP1 TP0] Decode batch. #running-req: 1, #token: 314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.41, #queue-req: 0, 
[2025-07-28 00:38:21 DP0 TP0] Decode batch. #running-req: 1, #token: 237, token usage: 0.00, cuda graph: True, gen throughput (token/s): 184.66, #queue-req: 0, 
[2025-07-28 00:38:21 DP1 TP0] Decode batch. #running-req: 1, #token: 354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.40, #queue-req: 0, 
[2025-07-28 00:38:22 DP0 TP0] Decode batch. #running-req: 1, #token: 277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.86, #queue-req: 0, 
[2025-07-28 00:38:22 DP1 TP0] Decode batch. #running-req: 1, #token: 394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.63, #queue-req: 0, 
[2025-07-28 00:38:22 DP0 TP0] Decode batch. #running-req: 1, #token: 317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.13, #queue-req: 0, 
[2025-07-28 00:38:22 DP1 TP0] Decode batch. #running-req: 1, #token: 434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.92, #queue-req: 0, 
[2025-07-28 00:38:22 DP0 TP0] Decode batch. #running-req: 1, #token: 357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.26, #queue-req: 0, 
[2025-07-28 00:38:22 DP1 TP0] Decode batch. #running-req: 1, #token: 474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.32, #queue-req: 0, 
[2025-07-28 00:38:22 DP0 TP0] Decode batch. #running-req: 1, #token: 397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.92, #queue-req: 0, 
[2025-07-28 00:38:22 DP1 TP0] Decode batch. #running-req: 1, #token: 514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.30, #queue-req: 0, 
[2025-07-28 00:38:22 DP0 TP0] Decode batch. #running-req: 1, #token: 437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.03, #queue-req: 0, 
[2025-07-28 00:38:22 DP1 TP0] Decode batch. #running-req: 1, #token: 554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.37, #queue-req: 0, 
[2025-07-28 00:38:22 DP0 TP0] Decode batch. #running-req: 1, #token: 477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.87, #queue-req: 0, 
[2025-07-28 00:38:22 DP1 TP0] Decode batch. #running-req: 1, #token: 594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.51, #queue-req: 0, 
[2025-07-28 00:38:22 DP0 TP0] Decode batch. #running-req: 1, #token: 517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.97, #queue-req: 0, 
[2025-07-28 00:38:23 DP1 TP0] Decode batch. #running-req: 1, #token: 634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.95, #queue-req: 0, 
[2025-07-28 00:38:23 DP0 TP0] Decode batch. #running-req: 1, #token: 557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.57, #queue-req: 0, 
[2025-07-28 00:38:23 DP1 TP0] Decode batch. #running-req: 1, #token: 674, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.60, #queue-req: 0, 
[2025-07-28 00:38:23 DP0 TP0] Decode batch. #running-req: 1, #token: 597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.66, #queue-req: 0, 
[2025-07-28 00:38:23 DP1 TP0] Decode batch. #running-req: 1, #token: 714, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.66, #queue-req: 0, 
[2025-07-28 00:38:23 DP0 TP0] Decode batch. #running-req: 1, #token: 637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 233.81, #queue-req: 0, 
[2025-07-28 00:38:23 DP1 TP0] Decode batch. #running-req: 1, #token: 754, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.53, #queue-req: 0, 
[2025-07-28 00:38:23] INFO:     127.0.0.1:48028 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:38:23 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 89, #cached-token: 112, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:38:23 DP0 TP0] Decode batch. #running-req: 1, #token: 677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.05, #queue-req: 0, 
[2025-07-28 00:38:23 DP1 TP0] Decode batch. #running-req: 1, #token: 226, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.67, #queue-req: 0, 
[2025-07-28 00:38:23 DP0 TP0] Decode batch. #running-req: 1, #token: 717, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.94, #queue-req: 0, 
[2025-07-28 00:38:23 DP1 TP0] Decode batch. #running-req: 1, #token: 266, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.62, #queue-req: 0, 
[2025-07-28 00:38:23] INFO:     127.0.0.1:48034 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:38:24 DP1 TP0] Decode batch. #running-req: 1, #token: 306, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.59, #queue-req: 0, 
[2025-07-28 00:38:24 DP1 TP0] Decode batch. #running-req: 1, #token: 346, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.16, #queue-req: 0, 
[2025-07-28 00:38:24 DP1 TP0] Decode batch. #running-req: 1, #token: 386, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.76, #queue-req: 0, 
[2025-07-28 00:38:24 DP1 TP0] Decode batch. #running-req: 1, #token: 426, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.43, #queue-req: 0, 
[2025-07-28 00:38:24 DP1 TP0] Decode batch. #running-req: 1, #token: 466, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.77, #queue-req: 0, 
[2025-07-28 00:38:24 DP1 TP0] Decode batch. #running-req: 1, #token: 506, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.61, #queue-req: 0, 
[2025-07-28 00:38:24 DP1 TP0] Decode batch. #running-req: 1, #token: 546, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.60, #queue-req: 0, 
[2025-07-28 00:38:25 DP1 TP0] Decode batch. #running-req: 1, #token: 586, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.74, #queue-req: 0, 
[2025-07-28 00:38:25 DP1 TP0] Decode batch. #running-req: 1, #token: 626, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.38, #queue-req: 0, 
[2025-07-28 00:38:25] INFO:     127.0.0.1:44178 - "POST /v1/chat/completions HTTP/1.1" 200 OK

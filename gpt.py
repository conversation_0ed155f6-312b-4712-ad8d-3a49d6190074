from openai import OpenAI
client = OpenAI(api_key="********************************************************************************************************************************************************************")



batch = client.batches.retrieve("batch_6885f4cc8f0881909a015e5d89bda2f4")

if batch.status == "completed":
    content = client.files.content(batch.output_file_id).text
    with open("/data/home/<USER>/repos/anesbench/share/datasets/anesthesia/benchmark/fine/rebuttal/openqa/gpt4o_output.jsonl", "w") as f:
        f.write(content)




# batch_input_file = client.files.create(
#     file=open("/data/home/<USER>/repos/anesbench/share/datasets/anesthesia/benchmark/fine/rebuttal/openqa/system2_en_refined_w_prompt_gpt4o.jsonl", "rb"),
#     purpose="batch"
# )

# print(batch_input_file)
# batch_input_file_id = batch_input_file.id
# client.batches.create(
#     input_file_id=batch_input_file_id,
#     endpoint="/v1/chat/completions",
#     completion_window="24h",
#     metadata={
#         "description": "o1-64道文本输出题目"
#     },
# )

# print(list(client.batches.list())[0])

#!/usr/bin/env python3
"""
Script to merge JSON files from close-source and open-source directories into unified JSONL format.

This script processes JSON/JSONL files from both directories, extracts responses and metadata,
and outputs a unified JSONL file with consistent structure.
"""

import json
import os
import re
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class JSONMerger:
    def __init__(self, reference_file: str = "system2_en_2trans_refined.json"):
        """Initialize the merger with reference file for prompt/target lookup."""
        self.reference_file = reference_file
        self.reference_data = {}
        self.reference_data_by_index = {}
        self.load_reference_data()
        
    def load_reference_data(self):
        """Load reference data for prompt and target lookup."""
        try:
            with open(self.reference_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                for index, item in enumerate(data):
                    self.reference_data[item['id']] = {
                        'question': item['question'],
                        'target': item['target'],
                        'index': index
                    }
                    self.reference_data_by_index[index] = {
                        'id': item['id'],
                        'question': item['question'],
                        'target': item['target']
                    }
            logger.info(f"Loaded {len(self.reference_data)} reference entries")
        except Exception as e:
            logger.error(f"Error loading reference file: {e}")
            
    def extract_model_name(self, filename: str, content: Dict[str, Any]) -> str:
        """Extract model name from filename or content."""
        # Remove file extension
        base_name = Path(filename).stem

        # Special handling for Claude files - both claude.jsonl and claude_batch.jsonl
        if 'claude' in base_name.lower():
            return "claude-3-7-sonnet-20250219"

        # Check if model name is in content
        if isinstance(content, dict):
            # Claude batch format
            if 'result' in content and isinstance(content['result'], dict):
                if 'message' in content['result'] and isinstance(content['result']['message'], dict):
                    if 'model' in content['result']['message']:
                        return content['result']['message']['model']

            # Claude format
            if 'results' in content and isinstance(content['results'], dict):
                if 'model' in content['results']:
                    return content['results']['model']

            # Direct model field
            if 'model' in content:
                return content['model']

        # Extract from filename patterns
        model_patterns = {
            'gpt4o': 'gpt-4o-2024-11-20',
            'deepseekr1': 'deepseek-r1',
            'deepseekv3': 'deepseek-v3',
            'gemini-2.5-flash': 'gemini-2.5-flash',
            'gemini-2.5-pro': 'gemini-2.5-pro'
        }

        for pattern, model in model_patterns.items():
            if pattern in base_name.lower():
                return model

        # Default to filename if no pattern matches
        return base_name
        
    def extract_response_from_close_source(self, data: Dict[str, Any]) -> Optional[str]:
        """Extract response from close-source file formats."""
        try:
            # Claude batch format (claude_batch.jsonl)
            if 'result' in data and 'message' in data['result']:
                message = data['result']['message']
                if 'content' in message and isinstance(message['content'], list):
                    for content_item in message['content']:
                        if content_item.get('type') == 'text':
                            return content_item.get('text', '')

            # Claude format (claude.jsonl)
            if 'results' in data and 'content' in data['results']:
                content = data['results']['content']
                if isinstance(content, list) and len(content) > 0:
                    for content_item in content:
                        if content_item.get('type') == 'text':
                            return content_item.get('text', '')

            # GPT format
            if 'response' in data and 'body' in data['response']:
                body = data['response']['body']
                if 'choices' in body and len(body['choices']) > 0:
                    choice = body['choices'][0]
                    if 'message' in choice and 'content' in choice['message']:
                        return choice['message']['content']

            # Gemini format
            if 'response' in data and 'candidates' in data['response']:
                candidates = data['response']['candidates']
                if len(candidates) > 0:
                    candidate = candidates[0]
                    if 'content' in candidate and 'parts' in candidate['content']:
                        parts = candidate['content']['parts']
                        if len(parts) > 0 and 'text' in parts[0]:
                            return parts[0]['text']

            # Direct response field
            if 'response' in data:
                if isinstance(data['response'], str):
                    return data['response']
                elif isinstance(data['response'], dict) and 'content' in data['response']:
                    return data['response']['content']

            return None
        except Exception as e:
            logger.warning(f"Error extracting response from close-source data: {e}")
            return None
            
    def extract_index_from_close_source(self, data: Dict[str, Any]) -> Optional[int]:
        """Extract index from close-source file formats."""
        # Try custom_id first
        if 'custom_id' in data:
            custom_id = data['custom_id']
            # Extract number from patterns like "key-0", "request-0"
            match = re.search(r'(\d+)$', custom_id)
            if match:
                return int(match.group(1))

        # Try key field (Gemini format)
        if 'key' in data:
            key = data['key']
            # Extract number from patterns like "key-0"
            match = re.search(r'(\d+)$', key)
            if match:
                return int(match.group(1))

        return None
        
    def process_close_source_file(self, filepath: str) -> List[Dict[str, Any]]:
        """Process a single close-source file."""
        results = []
        filename = os.path.basename(filepath)
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                # Handle both JSON and JSONL formats
                if filepath.endswith('.jsonl'):
                    for line_num, line in enumerate(f, 1):
                        line = line.strip()
                        if not line:
                            continue
                        try:
                            data = json.loads(line)
                            result = self.process_single_entry(data, filename, line_num)
                            if result:
                                results.append(result)
                        except json.JSONDecodeError as e:
                            logger.warning(f"Invalid JSON in {filepath} line {line_num}: {e}")
                else:
                    data = json.load(f)
                    if isinstance(data, list):
                        for idx, item in enumerate(data):
                            result = self.process_single_entry(item, filename, idx)
                            if result:
                                results.append(result)
                    else:
                        result = self.process_single_entry(data, filename, 0)
                        if result:
                            results.append(result)
                            
        except Exception as e:
            logger.error(f"Error processing {filepath}: {e}")
            
        return results
        
    def process_open_source_file(self, filepath: str) -> List[Dict[str, Any]]:
        """Process a single open-source file."""
        results = []
        filename = os.path.basename(filepath)
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if isinstance(data, list):
                    for item in data:
                        result = self.process_single_entry(item, filename, item.get('index', 0))
                        if result:
                            results.append(result)
                            
        except Exception as e:
            logger.error(f"Error processing {filepath}: {e}")
            
        return results
        
    def process_single_entry(self, data: Dict[str, Any], filename: str, file_index: int) -> Optional[Dict[str, Any]]:
        """Process a single data entry and return standardized format."""
        try:
            # Extract response
            response = None
            if 'response' in data:
                if isinstance(data['response'], str):
                    response = data['response']
                else:
                    response = self.extract_response_from_close_source(data)
            else:
                response = self.extract_response_from_close_source(data)

            if not response:
                logger.warning(f"No response found in {filename} entry {file_index}")
                return None

            # Extract index from the data or file position
            entry_index = None

            # For open-source files, use the index field directly
            if 'index' in data:
                entry_index = data['index']
            else:
                # For close-source files, extract index from custom_id/key
                entry_index = self.extract_index_from_close_source(data)
                if entry_index is None:
                    entry_index = file_index

            # Always get ID, prompt, and target from reference data using index
            # This ensures consistency with original QA pairs
            if entry_index is not None and entry_index in self.reference_data_by_index:
                ref_data = self.reference_data_by_index[entry_index]
                entry_id = ref_data['id']
                prompt = ref_data['question']
                target = ref_data['target']
            else:
                # Fallback: try to get from data itself (for open-source files)
                entry_id = data.get('id', f"unknown_{file_index}")
                prompt = data.get('prompt', '')
                target = data.get('target', '')

                # If we have an ID but no index, try to find it in reference
                if entry_id in self.reference_data:
                    ref_data = self.reference_data[entry_id]
                    entry_index = ref_data['index']
                    prompt = ref_data['question']
                    target = ref_data['target']

            # Extract model name
            model = self.extract_model_name(filename, data)

            return {
                'id': entry_id,
                'index': entry_index if entry_index is not None else file_index,
                'prompt': prompt,
                'target': target,
                'response': response,
                'model': model
            }

        except Exception as e:
            logger.warning(f"Error processing entry in {filename}: {e}")
            return None

    def merge_all_files(self, close_source_dir: str = "close-source",
                       open_source_dir: str = "open-source",
                       output_file: str = "merged_output.jsonl") -> None:
        """Merge all files from both directories into unified JSONL format."""
        all_entries = []

        # Process close-source files
        if os.path.exists(close_source_dir):
            logger.info(f"Processing close-source directory: {close_source_dir}")
            for filename in os.listdir(close_source_dir):
                filepath = os.path.join(close_source_dir, filename)
                if os.path.isfile(filepath) and (filename.endswith('.json') or filename.endswith('.jsonl')):
                    logger.info(f"Processing close-source file: {filename}")
                    entries = self.process_close_source_file(filepath)
                    all_entries.extend(entries)
                    logger.info(f"Extracted {len(entries)} entries from {filename}")

        # Process open-source files
        if os.path.exists(open_source_dir):
            logger.info(f"Processing open-source directory: {open_source_dir}")
            for filename in os.listdir(open_source_dir):
                filepath = os.path.join(open_source_dir, filename)
                if os.path.isfile(filepath) and filename.endswith('.json'):
                    logger.info(f"Processing open-source file: {filename}")
                    entries = self.process_open_source_file(filepath)
                    all_entries.extend(entries)
                    logger.info(f"Extracted {len(entries)} entries from {filename}")

        # Sort by model name, then by index
        logger.info("Sorting entries by model and index...")
        all_entries.sort(key=lambda x: (x['model'], x['index']))

        # Add auto-incrementing custom_id
        for i, entry in enumerate(all_entries, 1):
            entry['custom_id'] = i

        # Write to output file
        logger.info(f"Writing {len(all_entries)} entries to {output_file}")
        with open(output_file, 'w', encoding='utf-8') as f:
            for entry in all_entries:
                f.write(json.dumps(entry, ensure_ascii=False) + '\n')

        logger.info(f"Successfully merged {len(all_entries)} entries into {output_file}")

        # Print summary statistics
        self.print_summary(all_entries)

        # Validate data consistency
        self.validate_data_consistency(all_entries)

    def print_summary(self, entries: List[Dict[str, Any]]) -> None:
        """Print summary statistics of the merged data."""
        model_counts = {}
        for entry in entries:
            model = entry['model']
            model_counts[model] = model_counts.get(model, 0) + 1

        logger.info("Summary by model:")
        for model, count in sorted(model_counts.items()):
            logger.info(f"  {model}: {count} entries")

        logger.info(f"Total entries: {len(entries)}")

    def validate_data_consistency(self, entries: List[Dict[str, Any]]) -> None:
        """Validate that all entries match the original QA pairs exactly."""
        logger.info("Validating data consistency with original QA pairs...")

        # Group entries by model
        entries_by_model = {}
        for entry in entries:
            model = entry['model']
            if model not in entries_by_model:
                entries_by_model[model] = []
            entries_by_model[model].append(entry)

        total_mismatches = 0
        total_missing = 0

        for model, model_entries in entries_by_model.items():
            logger.info(f"Validating {model} ({len(model_entries)} entries)...")

            # Check if we have all 64 entries (all models should have 64 now)
            expected_count = 64
            if len(model_entries) != expected_count:
                logger.warning(f"  Expected {expected_count} entries, got {len(model_entries)}")
                total_missing += abs(expected_count - len(model_entries))

            # Validate each entry
            model_mismatches = 0
            for entry in model_entries:
                entry_id = entry['id']
                entry_index = entry['index']
                entry_prompt = entry['prompt']
                entry_target = entry['target']

                # Check if ID exists in reference data
                if entry_id not in self.reference_data:
                    logger.warning(f"  ID {entry_id} not found in reference data")
                    model_mismatches += 1
                    continue

                # Check if index matches
                ref_data = self.reference_data[entry_id]
                expected_index = ref_data['index']
                if entry_index != expected_index:
                    logger.warning(f"  Index mismatch for ID {entry_id}: got {entry_index}, expected {expected_index}")
                    model_mismatches += 1

                # Check if prompt matches (extract question from prompt if needed)
                expected_question = ref_data['question']
                if expected_question not in entry_prompt:
                    logger.warning(f"  Prompt mismatch for ID {entry_id}: question not found in prompt")
                    model_mismatches += 1

                # Check if target matches exactly
                expected_target = ref_data['target']
                if entry_target != expected_target:
                    logger.warning(f"  Target mismatch for ID {entry_id}: got '{entry_target}', expected '{expected_target}'")
                    model_mismatches += 1

            if model_mismatches == 0:
                logger.info(f"  ✓ All {len(model_entries)} entries validated successfully")
            else:
                logger.warning(f"  ✗ Found {model_mismatches} mismatches")
                total_mismatches += model_mismatches

        # Final summary
        if total_mismatches == 0 and total_missing == 0:
            logger.info("✓ Data consistency validation passed - all entries match original QA pairs")
        else:
            logger.error(f"✗ Data consistency validation failed: {total_mismatches} mismatches, {total_missing} missing entries")


def main():
    """Main function to run the merger."""
    import argparse

    parser = argparse.ArgumentParser(description='Merge JSON files from close-source and open-source directories')
    parser.add_argument('--close-source', default='close-source', help='Close-source directory path')
    parser.add_argument('--open-source', default='open-source', help='Open-source directory path')
    parser.add_argument('--reference', default='system2_en_2trans_refined.json', help='Reference file path')
    parser.add_argument('--output', default='merged_output.jsonl', help='Output file path')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose logging')

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Initialize merger
    merger = JSONMerger(reference_file=args.reference)

    # Merge all files
    merger.merge_all_files(
        close_source_dir=args.close_source,
        open_source_dir=args.open_source,
        output_file=args.output
    )


if __name__ == "__main__":
    main()

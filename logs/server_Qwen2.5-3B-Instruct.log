[2025-07-28 00:34:47] server_args=ServerArgs(model_path='/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-3B-Instruct', tokenizer_path='/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-3B-Instruct', tokenizer_mode='auto', skip_tokenizer_init=False, load_format='auto', model_loader_extra_config='{}', trust_remote_code=False, context_length=32768, is_embedding=False, enable_multimodal=None, revision=None, model_impl='auto', host='127.0.0.1', port=8003, skip_server_warmup=False, warmups=None, nccl_port=None, dtype='auto', quantization=None, quantization_param_path=None, kv_cache_dtype='auto', mem_fraction_static=0.85, max_running_requests=None, max_total_tokens=None, chunked_prefill_size=8192, max_prefill_tokens=16384, schedule_policy='fcfs', schedule_conservativeness=1.0, cpu_offload_gb=0, page_size=1, hybrid_kvcache_ratio=None, swa_full_tokens_ratio=0.8, disable_hybrid_swa_memory=False, device='cuda', tp_size=4, pp_size=1, max_micro_batch_size=None, stream_interval=1, stream_output=False, random_seed=937491958, constrained_json_whitespace_pattern=None, watchdog_timeout=300, dist_timeout=None, download_dir=None, base_gpu_id=0, gpu_id_step=1, sleep_on_idle=False, log_level='info', log_level_http=None, log_requests=False, log_requests_level=0, crash_dump_folder=None, show_time_cost=True, enable_metrics=True, enable_metrics_for_all_schedulers=False, bucket_time_to_first_token=None, bucket_inter_token_latency=None, bucket_e2e_request_latency=None, collect_tokens_histogram=False, decode_log_interval=40, enable_request_time_stats_logging=False, kv_events_config=None, api_key=None, served_model_name='/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-3B-Instruct', chat_template=None, completion_template=None, file_storage_path='sglang_storage', enable_cache_report=False, reasoning_parser=None, tool_call_parser=None, dp_size=2, load_balance_method='round_robin', dist_init_addr=None, nnodes=1, node_rank=0, json_model_override_args='{}', preferred_sampling_params=None, enable_lora=None, max_lora_rank=None, lora_target_modules=None, lora_paths=None, max_loras_per_batch=8, lora_backend='triton', attention_backend=None, sampling_backend='flashinfer', grammar_backend='xgrammar', mm_attention_backend=None, speculative_algorithm=None, speculative_draft_model_path=None, speculative_num_steps=None, speculative_eagle_topk=None, speculative_num_draft_tokens=None, speculative_accept_threshold_single=1.0, speculative_accept_threshold_acc=1.0, speculative_token_map=None, ep_size=1, enable_ep_moe=False, enable_deepep_moe=False, enable_flashinfer_moe=False, enable_flashinfer_allreduce_fusion=False, deepep_mode='auto', ep_num_redundant_experts=0, ep_dispatch_algorithm='static', init_expert_location='trivial', enable_eplb=False, eplb_algorithm='auto', eplb_rebalance_num_iterations=1000, eplb_rebalance_layers_per_chunk=None, expert_distribution_recorder_mode=None, expert_distribution_recorder_buffer_size=1000, enable_expert_distribution_metrics=False, deepep_config=None, moe_dense_tp_size=None, enable_hierarchical_cache=False, hicache_ratio=2.0, hicache_size=0, hicache_write_policy='write_through_selective', hicache_io_backend='', hicache_storage_backend=None, enable_double_sparsity=False, ds_channel_config_path=None, ds_heavy_channel_num=32, ds_heavy_token_num=256, ds_heavy_channel_type='qk', ds_sparse_decode_threshold=4096, disable_radix_cache=False, cuda_graph_max_bs=None, cuda_graph_bs=None, disable_cuda_graph=False, disable_cuda_graph_padding=False, enable_profile_cuda_graph=False, enable_nccl_nvls=False, enable_tokenizer_batch_encode=False, disable_outlines_disk_cache=False, disable_custom_all_reduce=False, enable_mscclpp=False, disable_overlap_schedule=False, enable_mixed_chunk=False, enable_dp_attention=False, enable_dp_lm_head=False, enable_two_batch_overlap=False, enable_torch_compile=False, torch_compile_max_bs=32, torchao_config='', enable_nan_detection=False, enable_p2p_check=False, triton_attention_reduce_in_fp32=False, triton_attention_num_kv_splits=8, num_continuous_decode_steps=1, delete_ckpt_after_loading=False, enable_memory_saver=False, allow_auto_truncate=False, enable_custom_logit_processor=False, flashinfer_mla_disable_ragged=False, disable_shared_experts_fusion=False, disable_chunked_prefix_cache=False, disable_fast_image_processor=False, enable_return_hidden_states=False, enable_triton_kernel_moe=False, debug_tensor_dump_output_folder=None, debug_tensor_dump_input_file=None, debug_tensor_dump_inject=False, debug_tensor_dump_prefill_only=False, disaggregation_mode='null', disaggregation_transfer_backend='mooncake', disaggregation_bootstrap_port=8998, disaggregation_decode_tp=None, disaggregation_decode_dp=None, disaggregation_prefill_pp=1, disaggregation_ib_device=None, num_reserved_decode_tokens=512, pdlb_url=None, custom_weight_loader=[], weight_loader_disable_mmap=False, enable_pdmux=False, sm_group_num=3)
[2025-07-28 00:34:54] Launch DP0 starting at GPU #0.
[2025-07-28 00:34:54] Launch DP1 starting at GPU #4.
[2025-07-28 00:35:01 DP0 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 00:35:01 DP0 TP0] Init torch distributed begin.
[2025-07-28 00:35:01 DP1 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 00:35:01 DP1 TP0] Init torch distributed begin.
[2025-07-28 00:35:02 DP0 TP0] sglang is using nccl==2.26.2
[2025-07-28 00:35:02 DP1 TP0] sglang is using nccl==2.26.2
[2025-07-28 00:35:04 DP0 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 00:35:04 DP1 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 00:35:04 DP0 TP0] Load weight begin. avail mem=78.21 GB
[2025-07-28 00:35:04 DP1 TP0] Load weight begin. avail mem=78.21 GB

Loading safetensors checkpoint shards:   0% Completed | 0/2 [00:00<?, ?it/s]

Loading safetensors checkpoint shards:   0% Completed | 0/2 [00:00<?, ?it/s]

Loading safetensors checkpoint shards:  50% Completed | 1/2 [00:00<00:00,  1.15it/s]

Loading safetensors checkpoint shards:  50% Completed | 1/2 [00:00<00:00,  1.17it/s]

Loading safetensors checkpoint shards: 100% Completed | 2/2 [00:02<00:00,  1.23s/it]

Loading safetensors checkpoint shards: 100% Completed | 2/2 [00:02<00:00,  1.23s/it]

Loading safetensors checkpoint shards: 100% Completed | 2/2 [00:02<00:00,  1.18s/it]


Loading safetensors checkpoint shards: 100% Completed | 2/2 [00:02<00:00,  1.17s/it]

[2025-07-28 00:35:07 DP1 TP0] Load weight end. type=Qwen2ForCausalLM, dtype=torch.bfloat16, avail mem=76.68 GB, mem usage=1.52 GB.
[2025-07-28 00:35:07 DP0 TP0] Load weight end. type=Qwen2ForCausalLM, dtype=torch.bfloat16, avail mem=76.68 GB, mem usage=1.52 GB.
[2025-07-28 00:35:07 DP0 TP3] KV Cache is allocated. #tokens: 3779165, K size: 32.44 GB, V size: 32.44 GB
[2025-07-28 00:35:07 DP0 TP1] KV Cache is allocated. #tokens: 3779165, K size: 32.44 GB, V size: 32.44 GB
[2025-07-28 00:35:07 DP1 TP0] KV Cache is allocated. #tokens: 3779165, K size: 32.44 GB, V size: 32.44 GB
[2025-07-28 00:35:07 DP1 TP0] Memory pool end. avail mem=11.18 GB
[2025-07-28 00:35:07 DP0 TP0] KV Cache is allocated. #tokens: 3779165, K size: 32.44 GB, V size: 32.44 GB
[2025-07-28 00:35:07 DP0 TP0] Memory pool end. avail mem=11.18 GB
[2025-07-28 00:35:07 DP1 TP3] KV Cache is allocated. #tokens: 3779165, K size: 32.44 GB, V size: 32.44 GB
[2025-07-28 00:35:07 DP1 TP1] KV Cache is allocated. #tokens: 3779165, K size: 32.44 GB, V size: 32.44 GB
[2025-07-28 00:35:07 DP1 TP2] KV Cache is allocated. #tokens: 3779165, K size: 32.44 GB, V size: 32.44 GB
[2025-07-28 00:35:07 DP0 TP2] KV Cache is allocated. #tokens: 3779165, K size: 32.44 GB, V size: 32.44 GB
[2025-07-28 00:35:07 DP0 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.55 GB
[2025-07-28 00:35:07 DP1 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.55 GB
[2025-07-28 00:35:07 DP1 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.53 GB):   0%|          | 0/23 [00:00<?, ?it/s][2025-07-28 00:35:07 DP0 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.53 GB):   0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.53 GB):   4%|▍         | 1/23 [00:01<00:22,  1.03s/it]
Capturing batches (bs=152 avail_mem=10.29 GB):   4%|▍         | 1/23 [00:01<00:22,  1.03s/it]
Capturing batches (bs=160 avail_mem=10.53 GB):   4%|▍         | 1/23 [00:01<00:23,  1.09s/it]
Capturing batches (bs=152 avail_mem=10.29 GB):   4%|▍         | 1/23 [00:01<00:23,  1.09s/it]
Capturing batches (bs=152 avail_mem=10.29 GB):   9%|▊         | 2/23 [00:01<00:13,  1.53it/s]
Capturing batches (bs=144 avail_mem=10.17 GB):   9%|▊         | 2/23 [00:01<00:13,  1.53it/s]
Capturing batches (bs=152 avail_mem=10.29 GB):   9%|▊         | 2/23 [00:01<00:13,  1.53it/s]
Capturing batches (bs=144 avail_mem=10.17 GB):   9%|▊         | 2/23 [00:01<00:13,  1.53it/s]
Capturing batches (bs=144 avail_mem=10.17 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.74it/s]
Capturing batches (bs=144 avail_mem=10.17 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.71it/s]
Capturing batches (bs=136 avail_mem=10.08 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.74it/s]
Capturing batches (bs=136 avail_mem=10.08 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.71it/s]
Capturing batches (bs=136 avail_mem=10.08 GB):  17%|█▋        | 4/23 [00:02<00:08,  2.15it/s]
Capturing batches (bs=128 avail_mem=9.98 GB):  17%|█▋        | 4/23 [00:02<00:08,  2.15it/s] 
Capturing batches (bs=136 avail_mem=10.08 GB):  17%|█▋        | 4/23 [00:02<00:08,  2.16it/s]
Capturing batches (bs=128 avail_mem=9.98 GB):  17%|█▋        | 4/23 [00:02<00:08,  2.16it/s] 
Capturing batches (bs=128 avail_mem=9.98 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.50it/s]
Capturing batches (bs=120 avail_mem=9.90 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.50it/s]
Capturing batches (bs=128 avail_mem=9.98 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.46it/s]
Capturing batches (bs=120 avail_mem=9.90 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.46it/s]
Capturing batches (bs=120 avail_mem=9.90 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.79it/s]
Capturing batches (bs=112 avail_mem=9.81 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.79it/s]
Capturing batches (bs=120 avail_mem=9.90 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.73it/s]
Capturing batches (bs=112 avail_mem=9.81 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.73it/s]
Capturing batches (bs=112 avail_mem=9.81 GB):  30%|███       | 7/23 [00:03<00:05,  2.99it/s]
Capturing batches (bs=104 avail_mem=9.74 GB):  30%|███       | 7/23 [00:03<00:05,  2.99it/s]
Capturing batches (bs=112 avail_mem=9.81 GB):  30%|███       | 7/23 [00:03<00:05,  2.92it/s]
Capturing batches (bs=104 avail_mem=9.74 GB):  30%|███       | 7/23 [00:03<00:05,  2.92it/s]
Capturing batches (bs=104 avail_mem=9.74 GB):  35%|███▍      | 8/23 [00:03<00:04,  3.15it/s]
Capturing batches (bs=96 avail_mem=9.65 GB):  35%|███▍      | 8/23 [00:03<00:04,  3.15it/s] 
Capturing batches (bs=104 avail_mem=9.74 GB):  35%|███▍      | 8/23 [00:03<00:04,  3.05it/s]
Capturing batches (bs=96 avail_mem=9.65 GB):  35%|███▍      | 8/23 [00:03<00:04,  3.05it/s] 
Capturing batches (bs=96 avail_mem=9.65 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.27it/s]
Capturing batches (bs=88 avail_mem=9.59 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.27it/s]
Capturing batches (bs=96 avail_mem=9.65 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.15it/s]
Capturing batches (bs=88 avail_mem=9.59 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.15it/s]
Capturing batches (bs=88 avail_mem=9.59 GB):  43%|████▎     | 10/23 [00:03<00:03,  3.35it/s]
Capturing batches (bs=80 avail_mem=9.51 GB):  43%|████▎     | 10/23 [00:03<00:03,  3.35it/s]
Capturing batches (bs=88 avail_mem=9.59 GB):  43%|████▎     | 10/23 [00:03<00:04,  3.24it/s]
Capturing batches (bs=80 avail_mem=9.51 GB):  43%|████▎     | 10/23 [00:03<00:04,  3.24it/s]
Capturing batches (bs=80 avail_mem=9.51 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.42it/s]
Capturing batches (bs=72 avail_mem=9.51 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.42it/s]
Capturing batches (bs=80 avail_mem=9.51 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.28it/s]
Capturing batches (bs=72 avail_mem=9.51 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.28it/s]
Capturing batches (bs=72 avail_mem=9.51 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.47it/s]
Capturing batches (bs=64 avail_mem=9.44 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.47it/s]
Capturing batches (bs=72 avail_mem=9.51 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.36it/s]
Capturing batches (bs=64 avail_mem=9.44 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.36it/s]
Capturing batches (bs=64 avail_mem=9.44 GB):  57%|█████▋    | 13/23 [00:04<00:02,  3.44it/s]
Capturing batches (bs=56 avail_mem=9.40 GB):  57%|█████▋    | 13/23 [00:04<00:02,  3.44it/s]
Capturing batches (bs=64 avail_mem=9.44 GB):  57%|█████▋    | 13/23 [00:04<00:03,  3.33it/s]
Capturing batches (bs=56 avail_mem=9.40 GB):  57%|█████▋    | 13/23 [00:04<00:03,  3.33it/s]
Capturing batches (bs=56 avail_mem=9.40 GB):  61%|██████    | 14/23 [00:05<00:02,  3.48it/s]
Capturing batches (bs=48 avail_mem=9.34 GB):  61%|██████    | 14/23 [00:05<00:02,  3.48it/s]
Capturing batches (bs=56 avail_mem=9.40 GB):  61%|██████    | 14/23 [00:05<00:02,  3.36it/s]
Capturing batches (bs=48 avail_mem=9.34 GB):  61%|██████    | 14/23 [00:05<00:02,  3.36it/s]
Capturing batches (bs=48 avail_mem=9.34 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.52it/s]
Capturing batches (bs=40 avail_mem=9.33 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.52it/s]
Capturing batches (bs=48 avail_mem=9.34 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.38it/s]
Capturing batches (bs=40 avail_mem=9.33 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.38it/s]
Capturing batches (bs=40 avail_mem=9.33 GB):  70%|██████▉   | 16/23 [00:05<00:01,  3.54it/s]
Capturing batches (bs=32 avail_mem=9.28 GB):  70%|██████▉   | 16/23 [00:05<00:01,  3.54it/s]
Capturing batches (bs=40 avail_mem=9.33 GB):  70%|██████▉   | 16/23 [00:05<00:02,  3.38it/s]
Capturing batches (bs=32 avail_mem=9.28 GB):  70%|██████▉   | 16/23 [00:05<00:02,  3.38it/s]
Capturing batches (bs=32 avail_mem=9.28 GB):  74%|███████▍  | 17/23 [00:05<00:01,  3.57it/s]
Capturing batches (bs=24 avail_mem=9.26 GB):  74%|███████▍  | 17/23 [00:05<00:01,  3.57it/s]
Capturing batches (bs=32 avail_mem=9.28 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.39it/s]
Capturing batches (bs=24 avail_mem=9.26 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.39it/s]
Capturing batches (bs=24 avail_mem=9.26 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.59it/s]
Capturing batches (bs=16 avail_mem=9.23 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.59it/s]
Capturing batches (bs=24 avail_mem=9.26 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.39it/s]
Capturing batches (bs=16 avail_mem=9.23 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.39it/s]
Capturing batches (bs=16 avail_mem=9.23 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.60it/s]
Capturing batches (bs=8 avail_mem=9.23 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.60it/s] 
Capturing batches (bs=16 avail_mem=9.23 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.39it/s]
Capturing batches (bs=8 avail_mem=9.23 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.39it/s] 
Capturing batches (bs=8 avail_mem=9.23 GB):  87%|████████▋ | 20/23 [00:06<00:00,  3.61it/s]
Capturing batches (bs=4 avail_mem=9.20 GB):  87%|████████▋ | 20/23 [00:06<00:00,  3.61it/s]
Capturing batches (bs=8 avail_mem=9.23 GB):  87%|████████▋ | 20/23 [00:06<00:00,  3.39it/s]
Capturing batches (bs=4 avail_mem=9.20 GB):  87%|████████▋ | 20/23 [00:06<00:00,  3.39it/s]
Capturing batches (bs=4 avail_mem=9.20 GB):  91%|█████████▏| 21/23 [00:06<00:00,  3.61it/s]
Capturing batches (bs=2 avail_mem=9.19 GB):  91%|█████████▏| 21/23 [00:06<00:00,  3.61it/s]
Capturing batches (bs=4 avail_mem=9.20 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.40it/s]
Capturing batches (bs=2 avail_mem=9.19 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.40it/s]
Capturing batches (bs=2 avail_mem=9.19 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.62it/s]
Capturing batches (bs=1 avail_mem=9.17 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.62it/s]
Capturing batches (bs=2 avail_mem=9.19 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.41it/s]
Capturing batches (bs=1 avail_mem=9.17 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.41it/s]
Capturing batches (bs=1 avail_mem=9.17 GB): 100%|██████████| 23/23 [00:07<00:00,  3.61it/s]
Capturing batches (bs=1 avail_mem=9.17 GB): 100%|██████████| 23/23 [00:07<00:00,  3.06it/s]
[2025-07-28 00:35:15 DP0 TP2] Registering 1679 cuda graph addresses
[2025-07-28 00:35:15 DP0 TP0] Registering 1679 cuda graph addresses
[2025-07-28 00:35:15 DP0 TP3] Registering 1679 cuda graph addresses
[2025-07-28 00:35:15 DP0 TP1] Registering 1679 cuda graph addresses
[2025-07-28 00:35:15 DP0 TP0] Capture cuda graph end. Time elapsed: 7.76 s. mem usage=1.38 GB. avail mem=9.16 GB.
[2025-07-28 00:35:15 DP1 TP1] Registering 1679 cuda graph addresses

Capturing batches (bs=1 avail_mem=9.17 GB): 100%|██████████| 23/23 [00:07<00:00,  3.41it/s]
Capturing batches (bs=1 avail_mem=9.17 GB): 100%|██████████| 23/23 [00:07<00:00,  2.95it/s]
[2025-07-28 00:35:15 DP1 TP0] Registering 1679 cuda graph addresses
[2025-07-28 00:35:15 DP1 TP3] Registering 1679 cuda graph addresses
[2025-07-28 00:35:15 DP1 TP2] Registering 1679 cuda graph addresses
[2025-07-28 00:35:15 DP1 TP0] Capture cuda graph end. Time elapsed: 8.01 s. mem usage=1.38 GB. avail mem=9.16 GB.
[2025-07-28 00:35:15 DP0 TP0] max_total_num_tokens=3779165, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=9.16 GB
[2025-07-28 00:35:15 DP1 TP0] max_total_num_tokens=3779165, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=9.16 GB
[2025-07-28 00:35:16] INFO:     Started server process [1757772]
[2025-07-28 00:35:16] INFO:     Waiting for application startup.
[2025-07-28 00:35:16] INFO:     Application startup complete.
[2025-07-28 00:35:16] INFO:     Uvicorn running on http://127.0.0.1:8003 (Press CTRL+C to quit)
[2025-07-28 00:35:17] INFO:     127.0.0.1:51156 - "GET /get_model_info HTTP/1.1" 200 OK
[2025-07-28 00:35:17 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:35:17 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:35:18] INFO:     127.0.0.1:51174 - "GET /health HTTP/1.1" 200 OK
[2025-07-28 00:35:18 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 215, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:35:18] INFO:     127.0.0.1:51166 - "POST /generate HTTP/1.1" 200 OK
[2025-07-28 00:35:18 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 256, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:35:18] The server is fired up and ready to roll!
[2025-07-28 00:35:18 DP0 TP0] Decode batch. #running-req: 1, #token: 248, token usage: 0.00, cuda graph: True, gen throughput (token/s): 13.31, #queue-req: 0, 
[2025-07-28 00:35:18 DP1 TP0] Decode batch. #running-req: 1, #token: 289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 14.47, #queue-req: 0, 
[2025-07-28 00:35:18 DP1 TP0] Decode batch. #running-req: 1, #token: 329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.43, #queue-req: 0, 
[2025-07-28 00:35:18 DP0 TP0] Decode batch. #running-req: 1, #token: 288, token usage: 0.00, cuda graph: True, gen throughput (token/s): 272.56, #queue-req: 0, 
[2025-07-28 00:35:18 DP1 TP0] Decode batch. #running-req: 1, #token: 369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.10, #queue-req: 0, 
[2025-07-28 00:35:18 DP0 TP0] Decode batch. #running-req: 1, #token: 328, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.14, #queue-req: 0, 
[2025-07-28 00:35:19 DP1 TP0] Decode batch. #running-req: 1, #token: 409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.43, #queue-req: 0, 
[2025-07-28 00:35:19 DP0 TP0] Decode batch. #running-req: 1, #token: 368, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.04, #queue-req: 0, 
[2025-07-28 00:35:19 DP1 TP0] Decode batch. #running-req: 1, #token: 449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 283.43, #queue-req: 0, 
[2025-07-28 00:35:19 DP0 TP0] Decode batch. #running-req: 1, #token: 408, token usage: 0.00, cuda graph: True, gen throughput (token/s): 270.36, #queue-req: 0, 
[2025-07-28 00:35:19 DP1 TP0] Decode batch. #running-req: 1, #token: 489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 260.98, #queue-req: 0, 
[2025-07-28 00:35:19 DP0 TP0] Decode batch. #running-req: 1, #token: 448, token usage: 0.00, cuda graph: True, gen throughput (token/s): 228.18, #queue-req: 0, 
[2025-07-28 00:35:19 DP1 TP0] Decode batch. #running-req: 1, #token: 529, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.72, #queue-req: 0, 
[2025-07-28 00:35:19 DP0 TP0] Decode batch. #running-req: 1, #token: 488, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.65, #queue-req: 0, 
[2025-07-28 00:35:19 DP1 TP0] Decode batch. #running-req: 1, #token: 569, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.84, #queue-req: 0, 
[2025-07-28 00:35:19 DP0 TP0] Decode batch. #running-req: 1, #token: 528, token usage: 0.00, cuda graph: True, gen throughput (token/s): 283.16, #queue-req: 0, 
[2025-07-28 00:35:19] INFO:     127.0.0.1:51182 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:35:19 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:35:19 DP1 TP0] Decode batch. #running-req: 1, #token: 609, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.02, #queue-req: 0, 
[2025-07-28 00:35:19 DP1 TP0] Decode batch. #running-req: 1, #token: 649, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.37, #queue-req: 0, 
[2025-07-28 00:35:20 DP1 TP0] Decode batch. #running-req: 1, #token: 689, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.18, #queue-req: 0, 
[2025-07-28 00:35:20 DP0 TP0] Decode batch. #running-req: 1, #token: 222, token usage: 0.00, cuda graph: True, gen throughput (token/s): 99.32, #queue-req: 0, 
[2025-07-28 00:35:20 DP1 TP0] Decode batch. #running-req: 1, #token: 729, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.05, #queue-req: 0, 
[2025-07-28 00:35:20 DP0 TP0] Decode batch. #running-req: 1, #token: 262, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.14, #queue-req: 0, 
[2025-07-28 00:35:20 DP1 TP0] Decode batch. #running-req: 1, #token: 769, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.73, #queue-req: 0, 
[2025-07-28 00:35:20 DP0 TP0] Decode batch. #running-req: 1, #token: 302, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.10, #queue-req: 0, 
[2025-07-28 00:35:20 DP1 TP0] Decode batch. #running-req: 1, #token: 809, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.09, #queue-req: 0, 
[2025-07-28 00:35:20 DP0 TP0] Decode batch. #running-req: 1, #token: 342, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.27, #queue-req: 0, 
[2025-07-28 00:35:20 DP1 TP0] Decode batch. #running-req: 1, #token: 849, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.19, #queue-req: 0, 
[2025-07-28 00:35:20 DP0 TP0] Decode batch. #running-req: 1, #token: 382, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.14, #queue-req: 0, 
[2025-07-28 00:35:20 DP1 TP0] Decode batch. #running-req: 1, #token: 889, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.52, #queue-req: 0, 
[2025-07-28 00:35:20 DP0 TP0] Decode batch. #running-req: 1, #token: 422, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.94, #queue-req: 0, 
[2025-07-28 00:35:20] INFO:     127.0.0.1:51196 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:35:20 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 207, #cached-token: 107, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:35:20 DP0 TP0] Decode batch. #running-req: 1, #token: 462, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.62, #queue-req: 0, 
[2025-07-28 00:35:21 DP0 TP0] Decode batch. #running-req: 1, #token: 502, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.75, #queue-req: 0, 
[2025-07-28 00:35:21 DP1 TP0] Decode batch. #running-req: 1, #token: 328, token usage: 0.00, cuda graph: True, gen throughput (token/s): 99.09, #queue-req: 0, 
[2025-07-28 00:35:21 DP0 TP0] Decode batch. #running-req: 1, #token: 542, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.37, #queue-req: 0, 
[2025-07-28 00:35:21 DP1 TP0] Decode batch. #running-req: 1, #token: 368, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.92, #queue-req: 0, 
[2025-07-28 00:35:21 DP0 TP0] Decode batch. #running-req: 1, #token: 582, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.57, #queue-req: 0, 
[2025-07-28 00:35:21] INFO:     127.0.0.1:51212 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:35:21 DP1 TP0] Decode batch. #running-req: 1, #token: 408, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.77, #queue-req: 0, 
[2025-07-28 00:35:21 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 107, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:35:21 DP0 TP0] Decode batch. #running-req: 1, #token: 251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 189.11, #queue-req: 0, 
[2025-07-28 00:35:21 DP1 TP0] Decode batch. #running-req: 1, #token: 448, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.97, #queue-req: 0, 
[2025-07-28 00:35:21 DP0 TP0] Decode batch. #running-req: 1, #token: 291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.48, #queue-req: 0, 
[2025-07-28 00:35:21 DP1 TP0] Decode batch. #running-req: 1, #token: 488, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.98, #queue-req: 0, 
[2025-07-28 00:35:21 DP0 TP0] Decode batch. #running-req: 1, #token: 331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.62, #queue-req: 0, 
[2025-07-28 00:35:21 DP1 TP0] Decode batch. #running-req: 1, #token: 528, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.94, #queue-req: 0, 
[2025-07-28 00:35:21 DP0 TP0] Decode batch. #running-req: 1, #token: 371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.39, #queue-req: 0, 
[2025-07-28 00:35:21 DP1 TP0] Decode batch. #running-req: 1, #token: 568, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.88, #queue-req: 0, 
[2025-07-28 00:35:22 DP0 TP0] Decode batch. #running-req: 1, #token: 411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.47, #queue-req: 0, 
[2025-07-28 00:35:22 DP1 TP0] Decode batch. #running-req: 1, #token: 608, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.76, #queue-req: 0, 
[2025-07-28 00:35:22 DP0 TP0] Decode batch. #running-req: 1, #token: 451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.49, #queue-req: 0, 
[2025-07-28 00:35:22 DP1 TP0] Decode batch. #running-req: 1, #token: 648, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.15, #queue-req: 0, 
[2025-07-28 00:35:22 DP0 TP0] Decode batch. #running-req: 1, #token: 491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.60, #queue-req: 0, 
[2025-07-28 00:35:22 DP1 TP0] Decode batch. #running-req: 1, #token: 688, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.22, #queue-req: 0, 
[2025-07-28 00:35:22 DP0 TP0] Decode batch. #running-req: 1, #token: 531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.52, #queue-req: 0, 
[2025-07-28 00:35:22 DP1 TP0] Decode batch. #running-req: 1, #token: 728, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.18, #queue-req: 0, 
[2025-07-28 00:35:22 DP0 TP0] Decode batch. #running-req: 1, #token: 571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.55, #queue-req: 0, 
[2025-07-28 00:35:22 DP1 TP0] Decode batch. #running-req: 1, #token: 768, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.27, #queue-req: 0, 
[2025-07-28 00:35:22 DP0 TP0] Decode batch. #running-req: 1, #token: 611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.61, #queue-req: 0, 
[2025-07-28 00:35:22 DP1 TP0] Decode batch. #running-req: 1, #token: 808, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.10, #queue-req: 0, 
[2025-07-28 00:35:22] INFO:     127.0.0.1:51220 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:35:22 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 133, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:35:22 DP0 TP0] Decode batch. #running-req: 1, #token: 651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.03, #queue-req: 0, 
[2025-07-28 00:35:22] INFO:     127.0.0.1:51230 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:35:22 DP1 TP0] Decode batch. #running-req: 1, #token: 275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 188.61, #queue-req: 0, 
[2025-07-28 00:35:22 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 186, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:35:23 DP0 TP0] Decode batch. #running-req: 1, #token: 307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 190.66, #queue-req: 0, 
[2025-07-28 00:35:23 DP1 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.66, #queue-req: 0, 
[2025-07-28 00:35:23 DP0 TP0] Decode batch. #running-req: 1, #token: 347, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.89, #queue-req: 0, 
[2025-07-28 00:35:23 DP1 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.77, #queue-req: 0, 
[2025-07-28 00:35:23 DP0 TP0] Decode batch. #running-req: 1, #token: 387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.15, #queue-req: 0, 
[2025-07-28 00:35:23 DP1 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.86, #queue-req: 0, 
[2025-07-28 00:35:23 DP0 TP0] Decode batch. #running-req: 1, #token: 427, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.04, #queue-req: 0, 
[2025-07-28 00:35:23 DP1 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 282.36, #queue-req: 0, 
[2025-07-28 00:35:23 DP0 TP0] Decode batch. #running-req: 1, #token: 467, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.36, #queue-req: 0, 
[2025-07-28 00:35:23 DP1 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.91, #queue-req: 0, 
[2025-07-28 00:35:23 DP0 TP0] Decode batch. #running-req: 1, #token: 507, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.22, #queue-req: 0, 
[2025-07-28 00:35:23 DP1 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.46, #queue-req: 0, 
[2025-07-28 00:35:23 DP0 TP0] Decode batch. #running-req: 1, #token: 547, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.98, #queue-req: 0, 
[2025-07-28 00:35:23 DP1 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.81, #queue-req: 0, 
[2025-07-28 00:35:23 DP0 TP0] Decode batch. #running-req: 1, #token: 587, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.21, #queue-req: 0, 
[2025-07-28 00:35:24 DP1 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.94, #queue-req: 0, 
[2025-07-28 00:35:24 DP0 TP0] Decode batch. #running-req: 1, #token: 627, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.03, #queue-req: 0, 
[2025-07-28 00:35:24 DP1 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.79, #queue-req: 0, 
[2025-07-28 00:35:24 DP0 TP0] Decode batch. #running-req: 1, #token: 667, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.09, #queue-req: 0, 
[2025-07-28 00:35:24 DP1 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.14, #queue-req: 0, 
[2025-07-28 00:35:24 DP0 TP0] Decode batch. #running-req: 1, #token: 707, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.46, #queue-req: 0, 
[2025-07-28 00:35:24 DP1 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.73, #queue-req: 0, 
[2025-07-28 00:35:24] INFO:     127.0.0.1:54514 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:35:24 DP0 TP0] Decode batch. #running-req: 1, #token: 747, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.88, #queue-req: 0, 
[2025-07-28 00:35:24 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 173, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:35:24 DP0 TP0] Decode batch. #running-req: 1, #token: 787, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.90, #queue-req: 0, 
[2025-07-28 00:35:24 DP1 TP0] Decode batch. #running-req: 1, #token: 299, token usage: 0.00, cuda graph: True, gen throughput (token/s): 183.60, #queue-req: 0, 
[2025-07-28 00:35:24 DP0 TP0] Decode batch. #running-req: 1, #token: 827, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.62, #queue-req: 0, 
[2025-07-28 00:35:24 DP1 TP0] Decode batch. #running-req: 1, #token: 339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.84, #queue-req: 0, 
[2025-07-28 00:35:24 DP0 TP0] Decode batch. #running-req: 1, #token: 867, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.03, #queue-req: 0, 
[2025-07-28 00:35:24 DP1 TP0] Decode batch. #running-req: 1, #token: 379, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.73, #queue-req: 0, 
[2025-07-28 00:35:25 DP0 TP0] Decode batch. #running-req: 1, #token: 907, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.91, #queue-req: 0, 
[2025-07-28 00:35:25 DP1 TP0] Decode batch. #running-req: 1, #token: 419, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.34, #queue-req: 0, 
[2025-07-28 00:35:25 DP0 TP0] Decode batch. #running-req: 1, #token: 947, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.68, #queue-req: 0, 
[2025-07-28 00:35:25 DP1 TP0] Decode batch. #running-req: 1, #token: 459, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.61, #queue-req: 0, 
[2025-07-28 00:35:25 DP0 TP0] Decode batch. #running-req: 1, #token: 987, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.90, #queue-req: 0, 
[2025-07-28 00:35:25 DP1 TP0] Decode batch. #running-req: 1, #token: 499, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.36, #queue-req: 0, 
[2025-07-28 00:35:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1027, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.67, #queue-req: 0, 
[2025-07-28 00:35:25 DP1 TP0] Decode batch. #running-req: 1, #token: 539, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.47, #queue-req: 0, 
[2025-07-28 00:35:25 DP1 TP0] Decode batch. #running-req: 1, #token: 579, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.38, #queue-req: 0, 
[2025-07-28 00:35:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1067, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.50, #queue-req: 0, 
[2025-07-28 00:35:25 DP1 TP0] Decode batch. #running-req: 1, #token: 619, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.97, #queue-req: 0, 
[2025-07-28 00:35:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1107, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.88, #queue-req: 0, 
[2025-07-28 00:35:25 DP1 TP0] Decode batch. #running-req: 1, #token: 659, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.58, #queue-req: 0, 
[2025-07-28 00:35:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1147, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.99, #queue-req: 0, 
[2025-07-28 00:35:26 DP1 TP0] Decode batch. #running-req: 1, #token: 699, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.79, #queue-req: 0, 
[2025-07-28 00:35:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1187, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.61, #queue-req: 0, 
[2025-07-28 00:35:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1227, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.78, #queue-req: 0, 
[2025-07-28 00:35:26 DP1 TP0] Decode batch. #running-req: 1, #token: 739, token usage: 0.00, cuda graph: True, gen throughput (token/s): 278.75, #queue-req: 0, 
[2025-07-28 00:35:26 DP1 TP0] Decode batch. #running-req: 1, #token: 779, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.85, #queue-req: 0, 
[2025-07-28 00:35:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1267, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.08, #queue-req: 0, 
[2025-07-28 00:35:26] INFO:     127.0.0.1:54540 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:35:26 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 226, #cached-token: 108, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:35:26 DP0 TP0] Decode batch. #running-req: 2, #token: 1564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.32, #queue-req: 0, 
[2025-07-28 00:35:26 DP0 TP0] Decode batch. #running-req: 2, #token: 1644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 534.05, #queue-req: 0, 
[2025-07-28 00:35:26 DP0 TP0] Decode batch. #running-req: 2, #token: 1724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 533.33, #queue-req: 0, 
[2025-07-28 00:35:27 DP0 TP0] Decode batch. #running-req: 2, #token: 1804, token usage: 0.00, cuda graph: True, gen throughput (token/s): 531.72, #queue-req: 0, 
[2025-07-28 00:35:27 DP0 TP0] Decode batch. #running-req: 2, #token: 1884, token usage: 0.00, cuda graph: True, gen throughput (token/s): 516.55, #queue-req: 0, 
[2025-07-28 00:35:27 DP0 TP0] Decode batch. #running-req: 2, #token: 1964, token usage: 0.00, cuda graph: True, gen throughput (token/s): 502.77, #queue-req: 0, 
[2025-07-28 00:35:27 DP0 TP0] Decode batch. #running-req: 2, #token: 2044, token usage: 0.00, cuda graph: True, gen throughput (token/s): 518.27, #queue-req: 0, 
[2025-07-28 00:35:27 DP0 TP0] Decode batch. #running-req: 2, #token: 2124, token usage: 0.00, cuda graph: True, gen throughput (token/s): 516.53, #queue-req: 0, 
[2025-07-28 00:35:27 DP0 TP0] Decode batch. #running-req: 2, #token: 2204, token usage: 0.00, cuda graph: True, gen throughput (token/s): 526.43, #queue-req: 0, 
[2025-07-28 00:35:28 DP0 TP0] Decode batch. #running-req: 2, #token: 2284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 527.09, #queue-req: 0, 
[2025-07-28 00:35:28 DP0 TP0] Decode batch. #running-req: 2, #token: 2364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 526.75, #queue-req: 0, 
[2025-07-28 00:35:28 DP0 TP0] Decode batch. #running-req: 2, #token: 2444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 525.93, #queue-req: 0, 
[2025-07-28 00:35:28 DP0 TP0] Decode batch. #running-req: 2, #token: 2524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 524.95, #queue-req: 0, 
[2025-07-28 00:35:28 DP0 TP0] Decode batch. #running-req: 2, #token: 2604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 523.48, #queue-req: 0, 
[2025-07-28 00:35:28] INFO:     127.0.0.1:54546 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:35:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1867, token usage: 0.00, cuda graph: True, gen throughput (token/s): 502.08, #queue-req: 0, 
[2025-07-28 00:35:28 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 168, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:35:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1907, token usage: 0.00, cuda graph: True, gen throughput (token/s): 281.89, #queue-req: 0, 
[2025-07-28 00:35:28 DP1 TP0] Decode batch. #running-req: 1, #token: 311, token usage: 0.00, cuda graph: True, gen throughput (token/s): 14.75, #queue-req: 0, 
[2025-07-28 00:35:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1947, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.12, #queue-req: 0, 
[2025-07-28 00:35:29 DP1 TP0] Decode batch. #running-req: 1, #token: 351, token usage: 0.00, cuda graph: True, gen throughput (token/s): 272.79, #queue-req: 0, 
[2025-07-28 00:35:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1987, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.28, #queue-req: 0, 
[2025-07-28 00:35:29 DP1 TP0] Decode batch. #running-req: 1, #token: 391, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.49, #queue-req: 0, 
[2025-07-28 00:35:29 DP0 TP0] Decode batch. #running-req: 1, #token: 2027, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.15, #queue-req: 0, 
[2025-07-28 00:35:29 DP1 TP0] Decode batch. #running-req: 1, #token: 431, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.05, #queue-req: 0, 
[2025-07-28 00:35:29 DP0 TP0] Decode batch. #running-req: 1, #token: 2067, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.06, #queue-req: 0, 
[2025-07-28 00:35:29 DP1 TP0] Decode batch. #running-req: 1, #token: 471, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.98, #queue-req: 0, 
[2025-07-28 00:35:29 DP0 TP0] Decode batch. #running-req: 1, #token: 2107, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.47, #queue-req: 0, 
[2025-07-28 00:35:29 DP1 TP0] Decode batch. #running-req: 1, #token: 511, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.15, #queue-req: 0, 
[2025-07-28 00:35:29 DP0 TP0] Decode batch. #running-req: 1, #token: 2147, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.15, #queue-req: 0, 
[2025-07-28 00:35:29 DP1 TP0] Decode batch. #running-req: 1, #token: 551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.62, #queue-req: 0, 
[2025-07-28 00:35:29 DP0 TP0] Decode batch. #running-req: 1, #token: 2187, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.94, #queue-req: 0, 
[2025-07-28 00:35:29 DP1 TP0] Decode batch. #running-req: 1, #token: 591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.79, #queue-req: 0, 
[2025-07-28 00:35:30 DP0 TP0] Decode batch. #running-req: 1, #token: 2227, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.37, #queue-req: 0, 
[2025-07-28 00:35:30 DP1 TP0] Decode batch. #running-req: 1, #token: 631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.55, #queue-req: 0, 
[2025-07-28 00:35:30 DP0 TP0] Decode batch. #running-req: 1, #token: 2267, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.03, #queue-req: 0, 
[2025-07-28 00:35:30 DP1 TP0] Decode batch. #running-req: 1, #token: 671, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.20, #queue-req: 0, 
[2025-07-28 00:35:30 DP0 TP0] Decode batch. #running-req: 1, #token: 2307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.17, #queue-req: 0, 
[2025-07-28 00:35:30 DP1 TP0] Decode batch. #running-req: 1, #token: 711, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.56, #queue-req: 0, 
[2025-07-28 00:35:30 DP0 TP0] Decode batch. #running-req: 1, #token: 2347, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.34, #queue-req: 0, 
[2025-07-28 00:35:30 DP1 TP0] Decode batch. #running-req: 1, #token: 751, token usage: 0.00, cuda graph: True, gen throughput (token/s): 282.40, #queue-req: 0, 
[2025-07-28 00:35:30 DP0 TP0] Decode batch. #running-req: 1, #token: 2387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 278.32, #queue-req: 0, 
[2025-07-28 00:35:30 DP1 TP0] Decode batch. #running-req: 1, #token: 791, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.24, #queue-req: 0, 
[2025-07-28 00:35:30 DP0 TP0] Decode batch. #running-req: 1, #token: 2427, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.22, #queue-req: 0, 
[2025-07-28 00:35:30 DP1 TP0] Decode batch. #running-req: 1, #token: 831, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.51, #queue-req: 0, 
[2025-07-28 00:35:30 DP0 TP0] Decode batch. #running-req: 1, #token: 2467, token usage: 0.00, cuda graph: True, gen throughput (token/s): 282.78, #queue-req: 0, 
[2025-07-28 00:35:30 DP1 TP0] Decode batch. #running-req: 1, #token: 871, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.70, #queue-req: 0, 
[2025-07-28 00:35:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2507, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.13, #queue-req: 0, 
[2025-07-28 00:35:31 DP1 TP0] Decode batch. #running-req: 1, #token: 911, token usage: 0.00, cuda graph: True, gen throughput (token/s): 288.09, #queue-req: 0, 
[2025-07-28 00:35:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2547, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.28, #queue-req: 0, 
[2025-07-28 00:35:31 DP1 TP0] Decode batch. #running-req: 1, #token: 951, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.59, #queue-req: 0, 
[2025-07-28 00:35:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2587, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.40, #queue-req: 0, 
[2025-07-28 00:35:31 DP1 TP0] Decode batch. #running-req: 1, #token: 991, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.87, #queue-req: 0, 
[2025-07-28 00:35:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2627, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.81, #queue-req: 0, 
[2025-07-28 00:35:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1031, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.04, #queue-req: 0, 
[2025-07-28 00:35:31] INFO:     127.0.0.1:54550 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:35:31 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 154, #cached-token: 108, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:35:31 DP0 TP0] Decode batch. #running-req: 2, #token: 2832, token usage: 0.00, cuda graph: True, gen throughput (token/s): 229.14, #queue-req: 0, 
[2025-07-28 00:35:31 DP0 TP0] Decode batch. #running-req: 2, #token: 2912, token usage: 0.00, cuda graph: True, gen throughput (token/s): 526.67, #queue-req: 0, 
[2025-07-28 00:35:31 DP0 TP0] Decode batch. #running-req: 2, #token: 2992, token usage: 0.00, cuda graph: True, gen throughput (token/s): 521.73, #queue-req: 0, 
[2025-07-28 00:35:32 DP0 TP0] Decode batch. #running-req: 2, #token: 3072, token usage: 0.00, cuda graph: True, gen throughput (token/s): 523.09, #queue-req: 0, 
[2025-07-28 00:35:32 DP0 TP0] Decode batch. #running-req: 2, #token: 3152, token usage: 0.00, cuda graph: True, gen throughput (token/s): 519.53, #queue-req: 0, 
[2025-07-28 00:35:32 DP0 TP0] Decode batch. #running-req: 2, #token: 3232, token usage: 0.00, cuda graph: True, gen throughput (token/s): 518.62, #queue-req: 0, 
[2025-07-28 00:35:32 DP0 TP0] Decode batch. #running-req: 2, #token: 3312, token usage: 0.00, cuda graph: True, gen throughput (token/s): 518.28, #queue-req: 0, 
[2025-07-28 00:35:32 DP0 TP0] Decode batch. #running-req: 2, #token: 3392, token usage: 0.00, cuda graph: True, gen throughput (token/s): 518.81, #queue-req: 0, 
[2025-07-28 00:35:32 DP0 TP0] Decode batch. #running-req: 2, #token: 3472, token usage: 0.00, cuda graph: True, gen throughput (token/s): 522.40, #queue-req: 0, 
[2025-07-28 00:35:33 DP0 TP0] Decode batch. #running-req: 2, #token: 3552, token usage: 0.00, cuda graph: True, gen throughput (token/s): 517.62, #queue-req: 0, 
[2025-07-28 00:35:33] INFO:     127.0.0.1:54558 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:35:33 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 141, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:35:33 DP0 TP0] Decode batch. #running-req: 1, #token: 3067, token usage: 0.00, cuda graph: True, gen throughput (token/s): 398.00, #queue-req: 0, 
[2025-07-28 00:35:33 DP0 TP0] Decode batch. #running-req: 1, #token: 3107, token usage: 0.00, cuda graph: True, gen throughput (token/s): 288.39, #queue-req: 0, 
[2025-07-28 00:35:33 DP1 TP0] Decode batch. #running-req: 1, #token: 280, token usage: 0.00, cuda graph: True, gen throughput (token/s): 21.90, #queue-req: 0, 
[2025-07-28 00:35:33 DP0 TP0] Decode batch. #running-req: 1, #token: 3147, token usage: 0.00, cuda graph: True, gen throughput (token/s): 262.22, #queue-req: 0, 
[2025-07-28 00:35:33 DP1 TP0] Decode batch. #running-req: 1, #token: 320, token usage: 0.00, cuda graph: True, gen throughput (token/s): 271.37, #queue-req: 0, 
[2025-07-28 00:35:33 DP1 TP0] Decode batch. #running-req: 1, #token: 360, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.78, #queue-req: 0, 
[2025-07-28 00:35:33 DP0 TP0] Decode batch. #running-req: 1, #token: 3187, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.61, #queue-req: 0, 
[2025-07-28 00:35:33 DP1 TP0] Decode batch. #running-req: 1, #token: 400, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.50, #queue-req: 0, 
[2025-07-28 00:35:33 DP0 TP0] Decode batch. #running-req: 1, #token: 3227, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.39, #queue-req: 0, 
[2025-07-28 00:35:33 DP1 TP0] Decode batch. #running-req: 1, #token: 440, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.47, #queue-req: 0, 
[2025-07-28 00:35:33 DP0 TP0] Decode batch. #running-req: 1, #token: 3267, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.55, #queue-req: 0, 
[2025-07-28 00:35:33 DP1 TP0] Decode batch. #running-req: 1, #token: 480, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.88, #queue-req: 0, 
[2025-07-28 00:35:34 DP0 TP0] Decode batch. #running-req: 1, #token: 3307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.79, #queue-req: 0, 
[2025-07-28 00:35:34 DP1 TP0] Decode batch. #running-req: 1, #token: 520, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.77, #queue-req: 0, 
[2025-07-28 00:35:34 DP0 TP0] Decode batch. #running-req: 1, #token: 3347, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.39, #queue-req: 0, 
[2025-07-28 00:35:34 DP1 TP0] Decode batch. #running-req: 1, #token: 560, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.14, #queue-req: 0, 
[2025-07-28 00:35:34 DP0 TP0] Decode batch. #running-req: 1, #token: 3387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.41, #queue-req: 0, 
[2025-07-28 00:35:34] INFO:     127.0.0.1:41558 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:35:34 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 192, #cached-token: 109, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:35:34 DP0 TP0] Decode batch. #running-req: 2, #token: 3625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.03, #queue-req: 0, 
[2025-07-28 00:35:34 DP0 TP0] Decode batch. #running-req: 2, #token: 3705, token usage: 0.00, cuda graph: True, gen throughput (token/s): 520.45, #queue-req: 0, 
[2025-07-28 00:35:34 DP0 TP0] Decode batch. #running-req: 2, #token: 3785, token usage: 0.00, cuda graph: True, gen throughput (token/s): 526.11, #queue-req: 0, 
[2025-07-28 00:35:34 DP0 TP0] Decode batch. #running-req: 2, #token: 3865, token usage: 0.00, cuda graph: True, gen throughput (token/s): 527.67, #queue-req: 0, 
[2025-07-28 00:35:35 DP0 TP0] Decode batch. #running-req: 2, #token: 3945, token usage: 0.00, cuda graph: True, gen throughput (token/s): 510.21, #queue-req: 0, 
[2025-07-28 00:35:35 DP0 TP0] Decode batch. #running-req: 2, #token: 4025, token usage: 0.00, cuda graph: True, gen throughput (token/s): 526.11, #queue-req: 0, 
[2025-07-28 00:35:35 DP0 TP0] Decode batch. #running-req: 2, #token: 4105, token usage: 0.00, cuda graph: True, gen throughput (token/s): 525.85, #queue-req: 0, 
[2025-07-28 00:35:35 DP0 TP0] Decode batch. #running-req: 2, #token: 4185, token usage: 0.00, cuda graph: True, gen throughput (token/s): 526.98, #queue-req: 0, 
[2025-07-28 00:35:35 DP0 TP0] Decode batch. #running-req: 2, #token: 4265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 520.42, #queue-req: 0, 
[2025-07-28 00:35:35 DP0 TP0] Decode batch. #running-req: 2, #token: 4345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 526.30, #queue-req: 0, 
[2025-07-28 00:35:36 DP0 TP0] Decode batch. #running-req: 2, #token: 4425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 527.11, #queue-req: 0, 
[2025-07-28 00:35:36 DP0 TP0] Decode batch. #running-req: 2, #token: 4505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 525.82, #queue-req: 0, 
[2025-07-28 00:35:36 DP0 TP0] Decode batch. #running-req: 2, #token: 4585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 526.72, #queue-req: 0, 
[2025-07-28 00:35:36 DP0 TP0] Decode batch. #running-req: 2, #token: 4665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 526.81, #queue-req: 0, 
[2025-07-28 00:35:36 DP0 TP0] Decode batch. #running-req: 2, #token: 4745, token usage: 0.00, cuda graph: True, gen throughput (token/s): 526.18, #queue-req: 0, 
[2025-07-28 00:35:36 DP0 TP0] Decode batch. #running-req: 2, #token: 4825, token usage: 0.00, cuda graph: True, gen throughput (token/s): 526.92, #queue-req: 0, 
[2025-07-28 00:35:36 DP0 TP0] Decode batch. #running-req: 2, #token: 4905, token usage: 0.00, cuda graph: True, gen throughput (token/s): 526.88, #queue-req: 0, 
[2025-07-28 00:35:37 DP0 TP0] Decode batch. #running-req: 2, #token: 4985, token usage: 0.00, cuda graph: True, gen throughput (token/s): 526.12, #queue-req: 0, 
[2025-07-28 00:35:37 DP0 TP0] Decode batch. #running-req: 2, #token: 5065, token usage: 0.00, cuda graph: True, gen throughput (token/s): 525.64, #queue-req: 0, 
[2025-07-28 00:35:37] INFO:     127.0.0.1:41566 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:35:37 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 190, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:35:37 DP0 TP0] Decode batch. #running-req: 1, #token: 4187, token usage: 0.00, cuda graph: True, gen throughput (token/s): 335.37, #queue-req: 0, 
[2025-07-28 00:35:37 DP1 TP0] Decode batch. #running-req: 1, #token: 304, token usage: 0.00, cuda graph: True, gen throughput (token/s): 12.83, #queue-req: 0, 
[2025-07-28 00:35:37 DP0 TP0] Decode batch. #running-req: 1, #token: 4227, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.95, #queue-req: 0, 
[2025-07-28 00:35:37 DP1 TP0] Decode batch. #running-req: 1, #token: 344, token usage: 0.00, cuda graph: True, gen throughput (token/s): 250.37, #queue-req: 0, 
[2025-07-28 00:35:37 DP0 TP0] Decode batch. #running-req: 1, #token: 4267, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.49, #queue-req: 0, 
[2025-07-28 00:35:37 DP1 TP0] Decode batch. #running-req: 1, #token: 384, token usage: 0.00, cuda graph: True, gen throughput (token/s): 260.48, #queue-req: 0, 
[2025-07-28 00:35:37 DP0 TP0] Decode batch. #running-req: 1, #token: 4307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.97, #queue-req: 0, 
[2025-07-28 00:35:37 DP1 TP0] Decode batch. #running-req: 1, #token: 424, token usage: 0.00, cuda graph: True, gen throughput (token/s): 260.08, #queue-req: 0, 
[2025-07-28 00:35:37 DP0 TP0] Decode batch. #running-req: 1, #token: 4347, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.90, #queue-req: 0, 
[2025-07-28 00:35:37 DP1 TP0] Decode batch. #running-req: 1, #token: 464, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.42, #queue-req: 0, 
[2025-07-28 00:35:38 DP0 TP0] Decode batch. #running-req: 1, #token: 4387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.22, #queue-req: 0, 
[2025-07-28 00:35:38 DP1 TP0] Decode batch. #running-req: 1, #token: 504, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.49, #queue-req: 0, 
[2025-07-28 00:35:38 DP0 TP0] Decode batch. #running-req: 1, #token: 4427, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.61, #queue-req: 0, 
[2025-07-28 00:35:38 DP1 TP0] Decode batch. #running-req: 1, #token: 544, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.29, #queue-req: 0, 
[2025-07-28 00:35:38 DP0 TP0] Decode batch. #running-req: 1, #token: 4467, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.45, #queue-req: 0, 
[2025-07-28 00:35:38 DP1 TP0] Decode batch. #running-req: 1, #token: 584, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.69, #queue-req: 0, 
[2025-07-28 00:35:38 DP0 TP0] Decode batch. #running-req: 1, #token: 4507, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.47, #queue-req: 0, 
[2025-07-28 00:35:38 DP1 TP0] Decode batch. #running-req: 1, #token: 624, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.84, #queue-req: 0, 
[2025-07-28 00:35:38 DP0 TP0] Decode batch. #running-req: 1, #token: 4547, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.11, #queue-req: 0, 
[2025-07-28 00:35:38 DP1 TP0] Decode batch. #running-req: 1, #token: 664, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.04, #queue-req: 0, 
[2025-07-28 00:35:38 DP0 TP0] Decode batch. #running-req: 1, #token: 4587, token usage: 0.00, cuda graph: True, gen throughput (token/s): 288.53, #queue-req: 0, 
[2025-07-28 00:35:38 DP1 TP0] Decode batch. #running-req: 1, #token: 704, token usage: 0.00, cuda graph: True, gen throughput (token/s): 280.49, #queue-req: 0, 
[2025-07-28 00:35:38 DP0 TP0] Decode batch. #running-req: 1, #token: 4627, token usage: 0.00, cuda graph: True, gen throughput (token/s): 288.19, #queue-req: 0, 
[2025-07-28 00:35:38 DP1 TP0] Decode batch. #running-req: 1, #token: 744, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.04, #queue-req: 0, 
[2025-07-28 00:35:39 DP0 TP0] Decode batch. #running-req: 1, #token: 4667, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.88, #queue-req: 0, 
[2025-07-28 00:35:39 DP1 TP0] Decode batch. #running-req: 1, #token: 784, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.86, #queue-req: 0, 
[2025-07-28 00:35:39 DP0 TP0] Decode batch. #running-req: 1, #token: 4707, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.95, #queue-req: 0, 
[2025-07-28 00:35:39 DP1 TP0] Decode batch. #running-req: 1, #token: 824, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.98, #queue-req: 0, 
[2025-07-28 00:35:39 DP0 TP0] Decode batch. #running-req: 1, #token: 4747, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.22, #queue-req: 0, 
[2025-07-28 00:35:39 DP1 TP0] Decode batch. #running-req: 1, #token: 864, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.69, #queue-req: 0, 
[2025-07-28 00:35:39 DP0 TP0] Decode batch. #running-req: 1, #token: 4787, token usage: 0.00, cuda graph: True, gen throughput (token/s): 281.83, #queue-req: 0, 
[2025-07-28 00:35:39] INFO:     127.0.0.1:41574 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:35:39 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 137, #cached-token: 108, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:35:39 DP0 TP0] Decode batch. #running-req: 2, #token: 4981, token usage: 0.00, cuda graph: True, gen throughput (token/s): 270.67, #queue-req: 0, 
[2025-07-28 00:35:39 DP0 TP0] Decode batch. #running-req: 2, #token: 5061, token usage: 0.00, cuda graph: True, gen throughput (token/s): 525.45, #queue-req: 0, 
[2025-07-28 00:35:39 DP0 TP0] Decode batch. #running-req: 2, #token: 5141, token usage: 0.00, cuda graph: True, gen throughput (token/s): 515.72, #queue-req: 0, 
[2025-07-28 00:35:40 DP0 TP0] Decode batch. #running-req: 2, #token: 5221, token usage: 0.00, cuda graph: True, gen throughput (token/s): 524.06, #queue-req: 0, 
[2025-07-28 00:35:40 DP0 TP0] Decode batch. #running-req: 2, #token: 5301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 523.03, #queue-req: 0, 
[2025-07-28 00:35:40 DP0 TP0] Decode batch. #running-req: 2, #token: 5381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 517.87, #queue-req: 0, 
[2025-07-28 00:35:40 DP0 TP0] Decode batch. #running-req: 2, #token: 5461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 524.03, #queue-req: 0, 
[2025-07-28 00:35:40 DP0 TP0] Decode batch. #running-req: 2, #token: 5541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 523.32, #queue-req: 0, 
[2025-07-28 00:35:40 DP0 TP0] Decode batch. #running-req: 2, #token: 5621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 521.04, #queue-req: 0, 
[2025-07-28 00:35:41 DP0 TP0] Decode batch. #running-req: 2, #token: 5701, token usage: 0.00, cuda graph: True, gen throughput (token/s): 518.25, #queue-req: 0, 
[2025-07-28 00:35:41 DP0 TP0] Decode batch. #running-req: 2, #token: 5781, token usage: 0.00, cuda graph: True, gen throughput (token/s): 515.59, #queue-req: 0, 
[2025-07-28 00:35:41 DP0 TP0] Decode batch. #running-req: 2, #token: 5861, token usage: 0.00, cuda graph: True, gen throughput (token/s): 510.60, #queue-req: 0, 
[2025-07-28 00:35:41 DP0 TP0] Decode batch. #running-req: 2, #token: 5941, token usage: 0.00, cuda graph: True, gen throughput (token/s): 519.39, #queue-req: 0, 
[2025-07-28 00:35:41 DP0 TP0] Decode batch. #running-req: 2, #token: 6021, token usage: 0.00, cuda graph: True, gen throughput (token/s): 521.57, #queue-req: 0, 
[2025-07-28 00:35:41] INFO:     127.0.0.1:41578 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:35:41 DP0 TP0] Decode batch. #running-req: 1, #token: 5387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 510.70, #queue-req: 0, 
[2025-07-28 00:35:41 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 132, #cached-token: 110, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:35:41 DP1 TP0] Decode batch. #running-req: 1, #token: 249, token usage: 0.00, cuda graph: True, gen throughput (token/s): 15.76, #queue-req: 0, 
[2025-07-28 00:35:41 DP0 TP0] Decode batch. #running-req: 1, #token: 5427, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.72, #queue-req: 0, 
[2025-07-28 00:35:42 DP1 TP0] Decode batch. #running-req: 1, #token: 289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 262.77, #queue-req: 0, 
[2025-07-28 00:35:42 DP0 TP0] Decode batch. #running-req: 1, #token: 5467, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.38, #queue-req: 0, 
[2025-07-28 00:35:42 DP1 TP0] Decode batch. #running-req: 1, #token: 329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.40, #queue-req: 0, 
[2025-07-28 00:35:42 DP0 TP0] Decode batch. #running-req: 1, #token: 5507, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.03, #queue-req: 0, 
[2025-07-28 00:35:42 DP1 TP0] Decode batch. #running-req: 1, #token: 369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.21, #queue-req: 0, 
[2025-07-28 00:35:42 DP0 TP0] Decode batch. #running-req: 1, #token: 5547, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.41, #queue-req: 0, 
[2025-07-28 00:35:42 DP1 TP0] Decode batch. #running-req: 1, #token: 409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.15, #queue-req: 0, 
[2025-07-28 00:35:42 DP0 TP0] Decode batch. #running-req: 1, #token: 5587, token usage: 0.00, cuda graph: True, gen throughput (token/s): 272.61, #queue-req: 0, 
[2025-07-28 00:35:42 DP1 TP0] Decode batch. #running-req: 1, #token: 449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.19, #queue-req: 0, 
[2025-07-28 00:35:42 DP0 TP0] Decode batch. #running-req: 1, #token: 5627, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.85, #queue-req: 0, 
[2025-07-28 00:35:42 DP1 TP0] Decode batch. #running-req: 1, #token: 489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 274.75, #queue-req: 0, 
[2025-07-28 00:35:42 DP0 TP0] Decode batch. #running-req: 1, #token: 5667, token usage: 0.00, cuda graph: True, gen throughput (token/s): 272.63, #queue-req: 0, 
[2025-07-28 00:35:42 DP1 TP0] Decode batch. #running-req: 1, #token: 529, token usage: 0.00, cuda graph: True, gen throughput (token/s): 275.37, #queue-req: 0, 
[2025-07-28 00:35:42 DP0 TP0] Decode batch. #running-req: 1, #token: 5707, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.63, #queue-req: 0, 
[2025-07-28 00:35:43 DP1 TP0] Decode batch. #running-req: 1, #token: 569, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.48, #queue-req: 0, 
[2025-07-28 00:35:43 DP0 TP0] Decode batch. #running-req: 1, #token: 5747, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.67, #queue-req: 0, 
[2025-07-28 00:35:43 DP1 TP0] Decode batch. #running-req: 1, #token: 609, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.76, #queue-req: 0, 
[2025-07-28 00:35:43 DP0 TP0] Decode batch. #running-req: 1, #token: 5787, token usage: 0.00, cuda graph: True, gen throughput (token/s): 283.66, #queue-req: 0, 
[2025-07-28 00:35:43 DP1 TP0] Decode batch. #running-req: 1, #token: 649, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.51, #queue-req: 0, 
[2025-07-28 00:35:43 DP0 TP0] Decode batch. #running-req: 1, #token: 5827, token usage: 0.00, cuda graph: True, gen throughput (token/s): 282.66, #queue-req: 0, 
[2025-07-28 00:35:43 DP1 TP0] Decode batch. #running-req: 1, #token: 689, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.06, #queue-req: 0, 
[2025-07-28 00:35:43 DP0 TP0] Decode batch. #running-req: 1, #token: 5867, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.34, #queue-req: 0, 
[2025-07-28 00:35:43 DP1 TP0] Decode batch. #running-req: 1, #token: 729, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.23, #queue-req: 0, 
[2025-07-28 00:35:43 DP0 TP0] Decode batch. #running-req: 1, #token: 5907, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.08, #queue-req: 0, 
[2025-07-28 00:35:43 DP1 TP0] Decode batch. #running-req: 1, #token: 769, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.42, #queue-req: 0, 
[2025-07-28 00:35:43 DP0 TP0] Decode batch. #running-req: 1, #token: 5947, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.54, #queue-req: 0, 
[2025-07-28 00:35:43 DP1 TP0] Decode batch. #running-req: 1, #token: 809, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.84, #queue-req: 0, 
[2025-07-28 00:35:43 DP0 TP0] Decode batch. #running-req: 1, #token: 5987, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.16, #queue-req: 0, 
[2025-07-28 00:35:44 DP1 TP0] Decode batch. #running-req: 1, #token: 849, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.17, #queue-req: 0, 
[2025-07-28 00:35:44 DP0 TP0] Decode batch. #running-req: 1, #token: 6027, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.28, #queue-req: 0, 
[2025-07-28 00:35:44 DP1 TP0] Decode batch. #running-req: 1, #token: 889, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.76, #queue-req: 0, 
[2025-07-28 00:35:44 DP0 TP0] Decode batch. #running-req: 1, #token: 6067, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.14, #queue-req: 0, 
[2025-07-28 00:35:44 DP1 TP0] Decode batch. #running-req: 1, #token: 929, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.91, #queue-req: 0, 
[2025-07-28 00:35:44 DP0 TP0] Decode batch. #running-req: 1, #token: 6107, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.37, #queue-req: 0, 
[2025-07-28 00:35:44 DP1 TP0] Decode batch. #running-req: 1, #token: 969, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.98, #queue-req: 0, 
[2025-07-28 00:35:44 DP0 TP0] Decode batch. #running-req: 1, #token: 6147, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.27, #queue-req: 0, 
[2025-07-28 00:35:44 DP1 TP0] Decode batch. #running-req: 1, #token: 1009, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.54, #queue-req: 0, 
[2025-07-28 00:35:44 DP0 TP0] Decode batch. #running-req: 1, #token: 6187, token usage: 0.00, cuda graph: True, gen throughput (token/s): 281.01, #queue-req: 0, 
[2025-07-28 00:35:44 DP1 TP0] Decode batch. #running-req: 1, #token: 1049, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.60, #queue-req: 0, 
[2025-07-28 00:35:44 DP0 TP0] Decode batch. #running-req: 1, #token: 6227, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.28, #queue-req: 0, 
[2025-07-28 00:35:44 DP1 TP0] Decode batch. #running-req: 1, #token: 1089, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.65, #queue-req: 0, 
[2025-07-28 00:35:44 DP0 TP0] Decode batch. #running-req: 1, #token: 6267, token usage: 0.00, cuda graph: True, gen throughput (token/s): 281.78, #queue-req: 0, 
[2025-07-28 00:35:44 DP1 TP0] Decode batch. #running-req: 1, #token: 1129, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.52, #queue-req: 0, 
[2025-07-28 00:35:45] INFO:     127.0.0.1:41588 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:35:45 DP0 TP0] Decode batch. #running-req: 1, #token: 6307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.34, #queue-req: 0, 
[2025-07-28 00:35:45 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 199, #cached-token: 109, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:35:45 DP0 TP0] Decode batch. #running-req: 2, #token: 6586, token usage: 0.00, cuda graph: True, gen throughput (token/s): 341.11, #queue-req: 0, 
[2025-07-28 00:35:45 DP0 TP0] Decode batch. #running-req: 2, #token: 6666, token usage: 0.00, cuda graph: True, gen throughput (token/s): 517.86, #queue-req: 0, 
[2025-07-28 00:35:45 DP0 TP0] Decode batch. #running-req: 2, #token: 6746, token usage: 0.00, cuda graph: True, gen throughput (token/s): 506.66, #queue-req: 0, 
[2025-07-28 00:35:45 DP0 TP0] Decode batch. #running-req: 2, #token: 6826, token usage: 0.00, cuda graph: True, gen throughput (token/s): 519.86, #queue-req: 0, 
[2025-07-28 00:35:45 DP0 TP0] Decode batch. #running-req: 2, #token: 6906, token usage: 0.00, cuda graph: True, gen throughput (token/s): 519.42, #queue-req: 0, 
[2025-07-28 00:35:46 DP0 TP0] Decode batch. #running-req: 2, #token: 6986, token usage: 0.00, cuda graph: True, gen throughput (token/s): 509.64, #queue-req: 0, 
[2025-07-28 00:35:46 DP0 TP0] Decode batch. #running-req: 2, #token: 7066, token usage: 0.00, cuda graph: True, gen throughput (token/s): 512.16, #queue-req: 0, 
[2025-07-28 00:35:46 DP0 TP0] Decode batch. #running-req: 2, #token: 7146, token usage: 0.00, cuda graph: True, gen throughput (token/s): 518.89, #queue-req: 0, 
[2025-07-28 00:35:46 DP0 TP0] Decode batch. #running-req: 2, #token: 7226, token usage: 0.00, cuda graph: True, gen throughput (token/s): 520.69, #queue-req: 0, 
[2025-07-28 00:35:46 DP0 TP0] Decode batch. #running-req: 2, #token: 7306, token usage: 0.00, cuda graph: True, gen throughput (token/s): 520.52, #queue-req: 0, 
[2025-07-28 00:35:46] INFO:     127.0.0.1:41862 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:35:46 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 194, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:35:46 DP0 TP0] Decode batch. #running-req: 1, #token: 6747, token usage: 0.00, cuda graph: True, gen throughput (token/s): 430.94, #queue-req: 0, 
[2025-07-28 00:35:46 DP0 TP0] Decode batch. #running-req: 1, #token: 6787, token usage: 0.00, cuda graph: True, gen throughput (token/s): 288.42, #queue-req: 0, 
[2025-07-28 00:35:46 DP1 TP0] Decode batch. #running-req: 1, #token: 327, token usage: 0.00, cuda graph: True, gen throughput (token/s): 20.28, #queue-req: 0, 
[2025-07-28 00:35:47 DP0 TP0] Decode batch. #running-req: 1, #token: 6827, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.02, #queue-req: 0, 
[2025-07-28 00:35:47 DP1 TP0] Decode batch. #running-req: 1, #token: 367, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.60, #queue-req: 0, 
[2025-07-28 00:35:47 DP0 TP0] Decode batch. #running-req: 1, #token: 6867, token usage: 0.00, cuda graph: True, gen throughput (token/s): 275.69, #queue-req: 0, 
[2025-07-28 00:35:47 DP1 TP0] Decode batch. #running-req: 1, #token: 407, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.04, #queue-req: 0, 
[2025-07-28 00:35:47 DP0 TP0] Decode batch. #running-req: 1, #token: 6907, token usage: 0.00, cuda graph: True, gen throughput (token/s): 273.87, #queue-req: 0, 
[2025-07-28 00:35:47 DP1 TP0] Decode batch. #running-req: 1, #token: 447, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.41, #queue-req: 0, 
[2025-07-28 00:35:47 DP1 TP0] Decode batch. #running-req: 1, #token: 487, token usage: 0.00, cuda graph: True, gen throughput (token/s): 279.63, #queue-req: 0, 
[2025-07-28 00:35:47 DP0 TP0] Decode batch. #running-req: 1, #token: 6947, token usage: 0.00, cuda graph: True, gen throughput (token/s): 272.81, #queue-req: 0, 
[2025-07-28 00:35:47 DP1 TP0] Decode batch. #running-req: 1, #token: 527, token usage: 0.00, cuda graph: True, gen throughput (token/s): 272.97, #queue-req: 0, 
[2025-07-28 00:35:47 DP0 TP0] Decode batch. #running-req: 1, #token: 6987, token usage: 0.00, cuda graph: True, gen throughput (token/s): 273.23, #queue-req: 0, 
[2025-07-28 00:35:47 DP1 TP0] Decode batch. #running-req: 1, #token: 567, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.80, #queue-req: 0, 
[2025-07-28 00:35:47 DP0 TP0] Decode batch. #running-req: 1, #token: 7027, token usage: 0.00, cuda graph: True, gen throughput (token/s): 280.23, #queue-req: 0, 
[2025-07-28 00:35:47 DP1 TP0] Decode batch. #running-req: 1, #token: 607, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.57, #queue-req: 0, 
[2025-07-28 00:35:47 DP0 TP0] Decode batch. #running-req: 1, #token: 7067, token usage: 0.00, cuda graph: True, gen throughput (token/s): 282.63, #queue-req: 0, 
[2025-07-28 00:35:48 DP1 TP0] Decode batch. #running-req: 1, #token: 647, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.70, #queue-req: 0, 
[2025-07-28 00:35:48 DP0 TP0] Decode batch. #running-req: 1, #token: 7107, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.81, #queue-req: 0, 
[2025-07-28 00:35:48 DP1 TP0] Decode batch. #running-req: 1, #token: 687, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.45, #queue-req: 0, 
[2025-07-28 00:35:48 DP0 TP0] Decode batch. #running-req: 1, #token: 7147, token usage: 0.00, cuda graph: True, gen throughput (token/s): 278.74, #queue-req: 0, 
[2025-07-28 00:35:48 DP1 TP0] Decode batch. #running-req: 1, #token: 727, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.73, #queue-req: 0, 
[2025-07-28 00:35:48 DP0 TP0] Decode batch. #running-req: 1, #token: 7187, token usage: 0.00, cuda graph: True, gen throughput (token/s): 282.81, #queue-req: 0, 
[2025-07-28 00:35:48] INFO:     127.0.0.1:41872 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:35:48 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 167, #cached-token: 108, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:35:48 DP0 TP0] Decode batch. #running-req: 2, #token: 7417, token usage: 0.00, cuda graph: True, gen throughput (token/s): 278.34, #queue-req: 0, 
[2025-07-28 00:35:48 DP0 TP0] Decode batch. #running-req: 2, #token: 7497, token usage: 0.00, cuda graph: True, gen throughput (token/s): 521.04, #queue-req: 0, 
[2025-07-28 00:35:48 DP0 TP0] Decode batch. #running-req: 2, #token: 7577, token usage: 0.00, cuda graph: True, gen throughput (token/s): 520.60, #queue-req: 0, 
[2025-07-28 00:35:49 DP0 TP0] Decode batch. #running-req: 2, #token: 7657, token usage: 0.00, cuda graph: True, gen throughput (token/s): 503.00, #queue-req: 0, 
[2025-07-28 00:35:49 DP0 TP0] Decode batch. #running-req: 2, #token: 7737, token usage: 0.00, cuda graph: True, gen throughput (token/s): 519.29, #queue-req: 0, 
[2025-07-28 00:35:49 DP0 TP0] Decode batch. #running-req: 2, #token: 7817, token usage: 0.00, cuda graph: True, gen throughput (token/s): 518.37, #queue-req: 0, 
[2025-07-28 00:35:49 DP0 TP0] Decode batch. #running-req: 2, #token: 7897, token usage: 0.00, cuda graph: True, gen throughput (token/s): 520.14, #queue-req: 0, 
[2025-07-28 00:35:49 DP0 TP0] Decode batch. #running-req: 2, #token: 7977, token usage: 0.00, cuda graph: True, gen throughput (token/s): 519.53, #queue-req: 0, 
[2025-07-28 00:35:49 DP0 TP0] Decode batch. #running-req: 2, #token: 8057, token usage: 0.00, cuda graph: True, gen throughput (token/s): 518.25, #queue-req: 0, 
[2025-07-28 00:35:50 DP0 TP0] Decode batch. #running-req: 2, #token: 8137, token usage: 0.00, cuda graph: True, gen throughput (token/s): 517.28, #queue-req: 0, 
[2025-07-28 00:35:50 DP0 TP0] Decode batch. #running-req: 2, #token: 8217, token usage: 0.00, cuda graph: True, gen throughput (token/s): 515.79, #queue-req: 0, 
[2025-07-28 00:35:50 DP0 TP0] Decode batch. #running-req: 2, #token: 8297, token usage: 0.00, cuda graph: True, gen throughput (token/s): 513.74, #queue-req: 0, 
[2025-07-28 00:35:50 DP0 TP0] Decode batch. #running-req: 2, #token: 8377, token usage: 0.00, cuda graph: True, gen throughput (token/s): 513.79, #queue-req: 0, 
[2025-07-28 00:35:50 DP0 TP0] Decode batch. #running-req: 2, #token: 8457, token usage: 0.00, cuda graph: True, gen throughput (token/s): 516.56, #queue-req: 0, 
[2025-07-28 00:35:50 DP0 TP0] Decode batch. #running-req: 2, #token: 8537, token usage: 0.00, cuda graph: True, gen throughput (token/s): 517.18, #queue-req: 0, 
[2025-07-28 00:35:50] INFO:     127.0.0.1:41880 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:35:50 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 172, #cached-token: 110, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:35:50 DP0 TP0] Decode batch. #running-req: 1, #token: 7827, token usage: 0.00, cuda graph: True, gen throughput (token/s): 375.81, #queue-req: 0, 
[2025-07-28 00:35:51 DP1 TP0] Decode batch. #running-req: 1, #token: 298, token usage: 0.00, cuda graph: True, gen throughput (token/s): 15.13, #queue-req: 0, 
[2025-07-28 00:35:51 DP0 TP0] Decode batch. #running-req: 1, #token: 7867, token usage: 0.00, cuda graph: True, gen throughput (token/s): 281.31, #queue-req: 0, 
[2025-07-28 00:35:51 DP1 TP0] Decode batch. #running-req: 1, #token: 338, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.19, #queue-req: 0, 
[2025-07-28 00:35:51 DP0 TP0] Decode batch. #running-req: 1, #token: 7907, token usage: 0.00, cuda graph: True, gen throughput (token/s): 281.65, #queue-req: 0, 
[2025-07-28 00:35:51 DP1 TP0] Decode batch. #running-req: 1, #token: 378, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.86, #queue-req: 0, 
[2025-07-28 00:35:51 DP0 TP0] Decode batch. #running-req: 1, #token: 7947, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.39, #queue-req: 0, 
[2025-07-28 00:35:51 DP1 TP0] Decode batch. #running-req: 1, #token: 418, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.61, #queue-req: 0, 
[2025-07-28 00:35:51 DP0 TP0] Decode batch. #running-req: 1, #token: 7987, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.01, #queue-req: 0, 
[2025-07-28 00:35:51 DP1 TP0] Decode batch. #running-req: 1, #token: 458, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.64, #queue-req: 0, 
[2025-07-28 00:35:51 DP0 TP0] Decode batch. #running-req: 1, #token: 8027, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.33, #queue-req: 0, 
[2025-07-28 00:35:51 DP1 TP0] Decode batch. #running-req: 1, #token: 498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.75, #queue-req: 0, 
[2025-07-28 00:35:51 DP0 TP0] Decode batch. #running-req: 1, #token: 8067, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.40, #queue-req: 0, 
[2025-07-28 00:35:51 DP1 TP0] Decode batch. #running-req: 1, #token: 538, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.30, #queue-req: 0, 
[2025-07-28 00:35:51 DP0 TP0] Decode batch. #running-req: 1, #token: 8107, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.95, #queue-req: 0, 
[2025-07-28 00:35:51 DP1 TP0] Decode batch. #running-req: 1, #token: 578, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.21, #queue-req: 0, 
[2025-07-28 00:35:52 DP0 TP0] Decode batch. #running-req: 1, #token: 8147, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.94, #queue-req: 0, 
[2025-07-28 00:35:52 DP1 TP0] Decode batch. #running-req: 1, #token: 618, token usage: 0.00, cuda graph: True, gen throughput (token/s): 283.64, #queue-req: 0, 
[2025-07-28 00:35:52 DP0 TP0] Decode batch. #running-req: 1, #token: 8187, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.01, #queue-req: 0, 
[2025-07-28 00:35:52 DP1 TP0] Decode batch. #running-req: 1, #token: 658, token usage: 0.00, cuda graph: True, gen throughput (token/s): 279.94, #queue-req: 0, 
[2025-07-28 00:35:52 DP0 TP0] Decode batch. #running-req: 1, #token: 8227, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.27, #queue-req: 0, 
[2025-07-28 00:35:52 DP1 TP0] Decode batch. #running-req: 1, #token: 698, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.67, #queue-req: 0, 
[2025-07-28 00:35:52 DP0 TP0] Decode batch. #running-req: 1, #token: 8267, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.03, #queue-req: 0, 
[2025-07-28 00:35:52 DP1 TP0] Decode batch. #running-req: 1, #token: 738, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.43, #queue-req: 0, 
[2025-07-28 00:35:52 DP0 TP0] Decode batch. #running-req: 1, #token: 8307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.26, #queue-req: 0, 
[2025-07-28 00:35:52 DP1 TP0] Decode batch. #running-req: 1, #token: 778, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.00, #queue-req: 0, 
[2025-07-28 00:35:52 DP0 TP0] Decode batch. #running-req: 1, #token: 8347, token usage: 0.00, cuda graph: True, gen throughput (token/s): 282.48, #queue-req: 0, 
[2025-07-28 00:35:52 DP1 TP0] Decode batch. #running-req: 1, #token: 818, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.54, #queue-req: 0, 
[2025-07-28 00:35:52 DP0 TP0] Decode batch. #running-req: 1, #token: 8387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.81, #queue-req: 0, 
[2025-07-28 00:35:52 DP1 TP0] Decode batch. #running-req: 1, #token: 858, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.30, #queue-req: 0, 
[2025-07-28 00:35:53 DP0 TP0] Decode batch. #running-req: 1, #token: 8427, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.71, #queue-req: 0, 
[2025-07-28 00:35:53 DP1 TP0] Decode batch. #running-req: 1, #token: 898, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.34, #queue-req: 0, 
[2025-07-28 00:35:53 DP0 TP0] Decode batch. #running-req: 1, #token: 8467, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.90, #queue-req: 0, 
[2025-07-28 00:35:53 DP1 TP0] Decode batch. #running-req: 1, #token: 938, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.01, #queue-req: 0, 
[2025-07-28 00:35:53] INFO:     127.0.0.1:54526 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:35:53] INFO:     127.0.0.1:41884 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:35:53 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 219, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:35:53 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 155, #cached-token: 111, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:35:53 DP0 TP0] Decode batch. #running-req: 1, #token: 350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 181.96, #queue-req: 0, 
[2025-07-28 00:35:53 DP1 TP0] Decode batch. #running-req: 1, #token: 299, token usage: 0.00, cuda graph: True, gen throughput (token/s): 186.43, #queue-req: 0, 
[2025-07-28 00:35:53 DP0 TP0] Decode batch. #running-req: 1, #token: 390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.07, #queue-req: 0, 
[2025-07-28 00:35:53 DP1 TP0] Decode batch. #running-req: 1, #token: 339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.91, #queue-req: 0, 
[2025-07-28 00:35:53 DP0 TP0] Decode batch. #running-req: 1, #token: 430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.44, #queue-req: 0, 
[2025-07-28 00:35:53 DP1 TP0] Decode batch. #running-req: 1, #token: 379, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.74, #queue-req: 0, 
[2025-07-28 00:35:53 DP0 TP0] Decode batch. #running-req: 1, #token: 470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 278.29, #queue-req: 0, 
[2025-07-28 00:35:53 DP1 TP0] Decode batch. #running-req: 1, #token: 419, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.94, #queue-req: 0, 
[2025-07-28 00:35:53 DP0 TP0] Decode batch. #running-req: 1, #token: 510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.24, #queue-req: 0, 
[2025-07-28 00:35:53 DP1 TP0] Decode batch. #running-req: 1, #token: 459, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.93, #queue-req: 0, 
[2025-07-28 00:35:54 DP0 TP0] Decode batch. #running-req: 1, #token: 550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.18, #queue-req: 0, 
[2025-07-28 00:35:54 DP1 TP0] Decode batch. #running-req: 1, #token: 499, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.59, #queue-req: 0, 
[2025-07-28 00:35:54 DP0 TP0] Decode batch. #running-req: 1, #token: 590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.53, #queue-req: 0, 
[2025-07-28 00:35:54 DP1 TP0] Decode batch. #running-req: 1, #token: 539, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.56, #queue-req: 0, 
[2025-07-28 00:35:54 DP0 TP0] Decode batch. #running-req: 1, #token: 630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.55, #queue-req: 0, 
[2025-07-28 00:35:54 DP1 TP0] Decode batch. #running-req: 1, #token: 579, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.14, #queue-req: 0, 
[2025-07-28 00:35:54 DP0 TP0] Decode batch. #running-req: 1, #token: 670, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.40, #queue-req: 0, 
[2025-07-28 00:35:54 DP1 TP0] Decode batch. #running-req: 1, #token: 619, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.62, #queue-req: 0, 
[2025-07-28 00:35:54 DP0 TP0] Decode batch. #running-req: 1, #token: 710, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.40, #queue-req: 0, 
[2025-07-28 00:35:54 DP1 TP0] Decode batch. #running-req: 1, #token: 659, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.58, #queue-req: 0, 
[2025-07-28 00:35:54 DP0 TP0] Decode batch. #running-req: 1, #token: 750, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.64, #queue-req: 0, 
[2025-07-28 00:35:54 DP1 TP0] Decode batch. #running-req: 1, #token: 699, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.14, #queue-req: 0, 
[2025-07-28 00:35:54 DP0 TP0] Decode batch. #running-req: 1, #token: 790, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.51, #queue-req: 0, 
[2025-07-28 00:35:54 DP1 TP0] Decode batch. #running-req: 1, #token: 739, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.89, #queue-req: 0, 
[2025-07-28 00:35:55 DP0 TP0] Decode batch. #running-req: 1, #token: 830, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.43, #queue-req: 0, 
[2025-07-28 00:35:55 DP1 TP0] Decode batch. #running-req: 1, #token: 779, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.59, #queue-req: 0, 
[2025-07-28 00:35:55 DP0 TP0] Decode batch. #running-req: 1, #token: 870, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.37, #queue-req: 0, 
[2025-07-28 00:35:55] INFO:     127.0.0.1:53054 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:35:55 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 175, #cached-token: 110, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:35:55 DP1 TP0] Decode batch. #running-req: 1, #token: 819, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.68, #queue-req: 0, 
[2025-07-28 00:35:55] INFO:     127.0.0.1:53062 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:35:55 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 94, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:35:55 DP0 TP0] Decode batch. #running-req: 1, #token: 322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 189.27, #queue-req: 0, 
[2025-07-28 00:35:55 DP1 TP0] Decode batch. #running-req: 1, #token: 235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 186.11, #queue-req: 0, 
[2025-07-28 00:35:55 DP0 TP0] Decode batch. #running-req: 1, #token: 362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.65, #queue-req: 0, 
[2025-07-28 00:35:55 DP1 TP0] Decode batch. #running-req: 1, #token: 275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.38, #queue-req: 0, 
[2025-07-28 00:35:55 DP0 TP0] Decode batch. #running-req: 1, #token: 402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.27, #queue-req: 0, 
[2025-07-28 00:35:55 DP1 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.57, #queue-req: 0, 
[2025-07-28 00:35:55 DP0 TP0] Decode batch. #running-req: 1, #token: 442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.38, #queue-req: 0, 
[2025-07-28 00:35:55 DP1 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.01, #queue-req: 0, 
[2025-07-28 00:35:55 DP0 TP0] Decode batch. #running-req: 1, #token: 482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.44, #queue-req: 0, 
[2025-07-28 00:35:55 DP1 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.40, #queue-req: 0, 
[2025-07-28 00:35:56 DP0 TP0] Decode batch. #running-req: 1, #token: 522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.66, #queue-req: 0, 
[2025-07-28 00:35:56 DP1 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.40, #queue-req: 0, 
[2025-07-28 00:35:56 DP0 TP0] Decode batch. #running-req: 1, #token: 562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.54, #queue-req: 0, 
[2025-07-28 00:35:56 DP1 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.53, #queue-req: 0, 
[2025-07-28 00:35:56 DP0 TP0] Decode batch. #running-req: 1, #token: 602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.04, #queue-req: 0, 
[2025-07-28 00:35:56 DP1 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.17, #queue-req: 0, 
[2025-07-28 00:35:56 DP0 TP0] Decode batch. #running-req: 1, #token: 642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.81, #queue-req: 0, 
[2025-07-28 00:35:56 DP1 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.05, #queue-req: 0, 
[2025-07-28 00:35:56 DP0 TP0] Decode batch. #running-req: 1, #token: 682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.04, #queue-req: 0, 
[2025-07-28 00:35:56 DP1 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.68, #queue-req: 0, 
[2025-07-28 00:35:56 DP0 TP0] Decode batch. #running-req: 1, #token: 722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.37, #queue-req: 0, 
[2025-07-28 00:35:56 DP1 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.58, #queue-req: 0, 
[2025-07-28 00:35:56 DP0 TP0] Decode batch. #running-req: 1, #token: 762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.53, #queue-req: 0, 
[2025-07-28 00:35:56 DP1 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.50, #queue-req: 0, 
[2025-07-28 00:35:56] INFO:     127.0.0.1:53076 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:35:56 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 115, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:35:57 DP1 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 288.10, #queue-req: 0, 
[2025-07-28 00:35:57 DP0 TP0] Decode batch. #running-req: 1, #token: 231, token usage: 0.00, cuda graph: True, gen throughput (token/s): 184.49, #queue-req: 0, 
[2025-07-28 00:35:57] INFO:     127.0.0.1:53084 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:35:57 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 150, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:35:57 DP0 TP0] Decode batch. #running-req: 1, #token: 271, token usage: 0.00, cuda graph: True, gen throughput (token/s): 282.70, #queue-req: 0, 
[2025-07-28 00:35:57 DP1 TP0] Decode batch. #running-req: 1, #token: 275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 173.61, #queue-req: 0, 
[2025-07-28 00:35:57 DP0 TP0] Decode batch. #running-req: 1, #token: 311, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.91, #queue-req: 0, 
[2025-07-28 00:35:57 DP1 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 275.38, #queue-req: 0, 
[2025-07-28 00:35:57 DP0 TP0] Decode batch. #running-req: 1, #token: 351, token usage: 0.00, cuda graph: True, gen throughput (token/s): 274.10, #queue-req: 0, 
[2025-07-28 00:35:57 DP1 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 275.32, #queue-req: 0, 
[2025-07-28 00:35:57 DP0 TP0] Decode batch. #running-req: 1, #token: 391, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.27, #queue-req: 0, 
[2025-07-28 00:35:57 DP1 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.22, #queue-req: 0, 
[2025-07-28 00:35:57 DP0 TP0] Decode batch. #running-req: 1, #token: 431, token usage: 0.00, cuda graph: True, gen throughput (token/s): 282.59, #queue-req: 0, 
[2025-07-28 00:35:57 DP1 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.93, #queue-req: 0, 
[2025-07-28 00:35:57 DP0 TP0] Decode batch. #running-req: 1, #token: 471, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.87, #queue-req: 0, 
[2025-07-28 00:35:57 DP1 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.90, #queue-req: 0, 
[2025-07-28 00:35:58 DP0 TP0] Decode batch. #running-req: 1, #token: 511, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.47, #queue-req: 0, 
[2025-07-28 00:35:58 DP1 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 275.24, #queue-req: 0, 
[2025-07-28 00:35:58 DP0 TP0] Decode batch. #running-req: 1, #token: 551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.48, #queue-req: 0, 
[2025-07-28 00:35:58 DP1 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.76, #queue-req: 0, 
[2025-07-28 00:35:58 DP0 TP0] Decode batch. #running-req: 1, #token: 591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.88, #queue-req: 0, 
[2025-07-28 00:35:58 DP1 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.93, #queue-req: 0, 
[2025-07-28 00:35:58 DP0 TP0] Decode batch. #running-req: 1, #token: 631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.73, #queue-req: 0, 
[2025-07-28 00:35:58 DP1 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.92, #queue-req: 0, 
[2025-07-28 00:35:58] INFO:     127.0.0.1:53090 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:35:58 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 148, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:35:58 DP1 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.11, #queue-req: 0, 
[2025-07-28 00:35:58 DP0 TP0] Decode batch. #running-req: 1, #token: 280, token usage: 0.00, cuda graph: True, gen throughput (token/s): 186.16, #queue-req: 0, 
[2025-07-28 00:35:58 DP1 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.50, #queue-req: 0, 
[2025-07-28 00:35:58 DP0 TP0] Decode batch. #running-req: 1, #token: 320, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.50, #queue-req: 0, 
[2025-07-28 00:35:58 DP1 TP0] Decode batch. #running-req: 1, #token: 755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.01, #queue-req: 0, 
[2025-07-28 00:35:58 DP0 TP0] Decode batch. #running-req: 1, #token: 360, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.92, #queue-req: 0, 
[2025-07-28 00:35:59 DP1 TP0] Decode batch. #running-req: 1, #token: 795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.93, #queue-req: 0, 
[2025-07-28 00:35:59 DP0 TP0] Decode batch. #running-req: 1, #token: 400, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.94, #queue-req: 0, 
[2025-07-28 00:35:59] INFO:     127.0.0.1:53094 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:35:59 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 160, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:35:59 DP0 TP0] Decode batch. #running-req: 1, #token: 440, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.63, #queue-req: 0, 
[2025-07-28 00:35:59 DP1 TP0] Decode batch. #running-req: 1, #token: 276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 186.18, #queue-req: 0, 
[2025-07-28 00:35:59 DP0 TP0] Decode batch. #running-req: 1, #token: 480, token usage: 0.00, cuda graph: True, gen throughput (token/s): 288.60, #queue-req: 0, 
[2025-07-28 00:35:59 DP1 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.68, #queue-req: 0, 
[2025-07-28 00:35:59 DP0 TP0] Decode batch. #running-req: 1, #token: 520, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.78, #queue-req: 0, 
[2025-07-28 00:35:59 DP1 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.24, #queue-req: 0, 
[2025-07-28 00:35:59 DP0 TP0] Decode batch. #running-req: 1, #token: 560, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.69, #queue-req: 0, 
[2025-07-28 00:35:59 DP1 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.65, #queue-req: 0, 
[2025-07-28 00:35:59 DP0 TP0] Decode batch. #running-req: 1, #token: 600, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.87, #queue-req: 0, 
[2025-07-28 00:35:59 DP1 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.03, #queue-req: 0, 
[2025-07-28 00:35:59 DP0 TP0] Decode batch. #running-req: 1, #token: 640, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.72, #queue-req: 0, 
[2025-07-28 00:35:59 DP1 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.13, #queue-req: 0, 
[2025-07-28 00:36:00 DP0 TP0] Decode batch. #running-req: 1, #token: 680, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.24, #queue-req: 0, 
[2025-07-28 00:36:00 DP1 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.37, #queue-req: 0, 
[2025-07-28 00:36:00 DP0 TP0] Decode batch. #running-req: 1, #token: 720, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.09, #queue-req: 0, 
[2025-07-28 00:36:00 DP1 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.37, #queue-req: 0, 
[2025-07-28 00:36:00 DP0 TP0] Decode batch. #running-req: 1, #token: 760, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.18, #queue-req: 0, 
[2025-07-28 00:36:00 DP1 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.74, #queue-req: 0, 
[2025-07-28 00:36:00 DP0 TP0] Decode batch. #running-req: 1, #token: 800, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.60, #queue-req: 0, 
[2025-07-28 00:36:00 DP1 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.61, #queue-req: 0, 
[2025-07-28 00:36:00 DP0 TP0] Decode batch. #running-req: 1, #token: 840, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.80, #queue-req: 0, 
[2025-07-28 00:36:00 DP1 TP0] Decode batch. #running-req: 1, #token: 676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 274.74, #queue-req: 0, 
[2025-07-28 00:36:00 DP0 TP0] Decode batch. #running-req: 1, #token: 880, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.14, #queue-req: 0, 
[2025-07-28 00:36:00 DP1 TP0] Decode batch. #running-req: 1, #token: 716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 261.37, #queue-req: 0, 
[2025-07-28 00:36:00 DP0 TP0] Decode batch. #running-req: 1, #token: 920, token usage: 0.00, cuda graph: True, gen throughput (token/s): 275.99, #queue-req: 0, 
[2025-07-28 00:36:00 DP1 TP0] Decode batch. #running-req: 1, #token: 756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.40, #queue-req: 0, 
[2025-07-28 00:36:01 DP0 TP0] Decode batch. #running-req: 1, #token: 960, token usage: 0.00, cuda graph: True, gen throughput (token/s): 288.71, #queue-req: 0, 
[2025-07-28 00:36:01 DP1 TP0] Decode batch. #running-req: 1, #token: 796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.98, #queue-req: 0, 
[2025-07-28 00:36:01] INFO:     127.0.0.1:53114 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1000, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.64, #queue-req: 0, 
[2025-07-28 00:36:01 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 114, #cached-token: 109, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:36:01 DP0 TP0] Decode batch. #running-req: 2, #token: 1195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 363.50, #queue-req: 0, 
[2025-07-28 00:36:01 DP0 TP0] Decode batch. #running-req: 2, #token: 1275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 527.72, #queue-req: 0, 
[2025-07-28 00:36:01] INFO:     127.0.0.1:53100 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:01 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 80, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:36:01 DP0 TP0] Decode batch. #running-req: 1, #token: 343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 402.00, #queue-req: 0, 
[2025-07-28 00:36:01 DP1 TP0] Decode batch. #running-req: 1, #token: 202, token usage: 0.00, cuda graph: True, gen throughput (token/s): 57.88, #queue-req: 0, 
[2025-07-28 00:36:01 DP0 TP0] Decode batch. #running-req: 1, #token: 383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.19, #queue-req: 0, 
[2025-07-28 00:36:01 DP1 TP0] Decode batch. #running-req: 1, #token: 242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.11, #queue-req: 0, 
[2025-07-28 00:36:01 DP0 TP0] Decode batch. #running-req: 1, #token: 423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.52, #queue-req: 0, 
[2025-07-28 00:36:02 DP1 TP0] Decode batch. #running-req: 1, #token: 282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.60, #queue-req: 0, 
[2025-07-28 00:36:02 DP0 TP0] Decode batch. #running-req: 1, #token: 463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.69, #queue-req: 0, 
[2025-07-28 00:36:02 DP1 TP0] Decode batch. #running-req: 1, #token: 322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 270.04, #queue-req: 0, 
[2025-07-28 00:36:02 DP0 TP0] Decode batch. #running-req: 1, #token: 503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.89, #queue-req: 0, 
[2025-07-28 00:36:02 DP1 TP0] Decode batch. #running-req: 1, #token: 362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.63, #queue-req: 0, 
[2025-07-28 00:36:02 DP0 TP0] Decode batch. #running-req: 1, #token: 543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.77, #queue-req: 0, 
[2025-07-28 00:36:02 DP1 TP0] Decode batch. #running-req: 1, #token: 402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.74, #queue-req: 0, 
[2025-07-28 00:36:02 DP0 TP0] Decode batch. #running-req: 1, #token: 583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.59, #queue-req: 0, 
[2025-07-28 00:36:02 DP1 TP0] Decode batch. #running-req: 1, #token: 442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.01, #queue-req: 0, 
[2025-07-28 00:36:02 DP0 TP0] Decode batch. #running-req: 1, #token: 623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.86, #queue-req: 0, 
[2025-07-28 00:36:02 DP1 TP0] Decode batch. #running-req: 1, #token: 482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 278.97, #queue-req: 0, 
[2025-07-28 00:36:02 DP0 TP0] Decode batch. #running-req: 1, #token: 663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.92, #queue-req: 0, 
[2025-07-28 00:36:02] INFO:     127.0.0.1:53122 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:02 DP1 TP0] Decode batch. #running-req: 1, #token: 522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.23, #queue-req: 0, 
[2025-07-28 00:36:02 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 136, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:36:02] INFO:     127.0.0.1:53132 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:02 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 137, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:36:03 DP0 TP0] Decode batch. #running-req: 1, #token: 250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 189.20, #queue-req: 0, 
[2025-07-28 00:36:03 DP1 TP0] Decode batch. #running-req: 1, #token: 276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 184.80, #queue-req: 0, 
[2025-07-28 00:36:03 DP0 TP0] Decode batch. #running-req: 1, #token: 290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.74, #queue-req: 0, 
[2025-07-28 00:36:03 DP1 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.29, #queue-req: 0, 
[2025-07-28 00:36:03 DP0 TP0] Decode batch. #running-req: 1, #token: 330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.95, #queue-req: 0, 
[2025-07-28 00:36:03 DP1 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.16, #queue-req: 0, 
[2025-07-28 00:36:03 DP0 TP0] Decode batch. #running-req: 1, #token: 370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.04, #queue-req: 0, 
[2025-07-28 00:36:03 DP1 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.88, #queue-req: 0, 
[2025-07-28 00:36:03 DP0 TP0] Decode batch. #running-req: 1, #token: 410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.63, #queue-req: 0, 
[2025-07-28 00:36:03 DP1 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.26, #queue-req: 0, 
[2025-07-28 00:36:03 DP0 TP0] Decode batch. #running-req: 1, #token: 450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.93, #queue-req: 0, 
[2025-07-28 00:36:03 DP1 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.55, #queue-req: 0, 
[2025-07-28 00:36:03 DP0 TP0] Decode batch. #running-req: 1, #token: 490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.95, #queue-req: 0, 
[2025-07-28 00:36:03 DP1 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.18, #queue-req: 0, 
[2025-07-28 00:36:03 DP0 TP0] Decode batch. #running-req: 1, #token: 530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.47, #queue-req: 0, 
[2025-07-28 00:36:04 DP1 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.07, #queue-req: 0, 
[2025-07-28 00:36:04 DP0 TP0] Decode batch. #running-req: 1, #token: 570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 283.13, #queue-req: 0, 
[2025-07-28 00:36:04 DP1 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.38, #queue-req: 0, 
[2025-07-28 00:36:04 DP0 TP0] Decode batch. #running-req: 1, #token: 610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.96, #queue-req: 0, 
[2025-07-28 00:36:04 DP1 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.57, #queue-req: 0, 
[2025-07-28 00:36:04 DP0 TP0] Decode batch. #running-req: 1, #token: 650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.03, #queue-req: 0, 
[2025-07-28 00:36:04] INFO:     127.0.0.1:42872 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:04 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 85, #cached-token: 106, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:36:04 DP0 TP0] Decode batch. #running-req: 2, #token: 796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.01, #queue-req: 0, 
[2025-07-28 00:36:04 DP0 TP0] Decode batch. #running-req: 2, #token: 876, token usage: 0.00, cuda graph: True, gen throughput (token/s): 530.64, #queue-req: 0, 
[2025-07-28 00:36:04] INFO:     127.0.0.1:42860 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:04 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 157, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:36:04 DP0 TP0] Decode batch. #running-req: 1, #token: 292, token usage: 0.00, cuda graph: True, gen throughput (token/s): 453.55, #queue-req: 0, 
[2025-07-28 00:36:04 DP1 TP0] Decode batch. #running-req: 1, #token: 281, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.53, #queue-req: 0, 
[2025-07-28 00:36:05 DP0 TP0] Decode batch. #running-req: 1, #token: 332, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.05, #queue-req: 0, 
[2025-07-28 00:36:05 DP1 TP0] Decode batch. #running-req: 1, #token: 321, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.25, #queue-req: 0, 
[2025-07-28 00:36:05 DP0 TP0] Decode batch. #running-req: 1, #token: 372, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.01, #queue-req: 0, 
[2025-07-28 00:36:05 DP1 TP0] Decode batch. #running-req: 1, #token: 361, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.04, #queue-req: 0, 
[2025-07-28 00:36:05 DP0 TP0] Decode batch. #running-req: 1, #token: 412, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.29, #queue-req: 0, 
[2025-07-28 00:36:05 DP1 TP0] Decode batch. #running-req: 1, #token: 401, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.44, #queue-req: 0, 
[2025-07-28 00:36:05 DP0 TP0] Decode batch. #running-req: 1, #token: 452, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.55, #queue-req: 0, 
[2025-07-28 00:36:05 DP1 TP0] Decode batch. #running-req: 1, #token: 441, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.31, #queue-req: 0, 
[2025-07-28 00:36:05 DP0 TP0] Decode batch. #running-req: 1, #token: 492, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.38, #queue-req: 0, 
[2025-07-28 00:36:05 DP1 TP0] Decode batch. #running-req: 1, #token: 481, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.42, #queue-req: 0, 
[2025-07-28 00:36:05 DP0 TP0] Decode batch. #running-req: 1, #token: 532, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.71, #queue-req: 0, 
[2025-07-28 00:36:05 DP1 TP0] Decode batch. #running-req: 1, #token: 521, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.54, #queue-req: 0, 
[2025-07-28 00:36:05 DP0 TP0] Decode batch. #running-req: 1, #token: 572, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.99, #queue-req: 0, 
[2025-07-28 00:36:05 DP1 TP0] Decode batch. #running-req: 1, #token: 561, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.40, #queue-req: 0, 
[2025-07-28 00:36:05 DP0 TP0] Decode batch. #running-req: 1, #token: 612, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.07, #queue-req: 0, 
[2025-07-28 00:36:05] INFO:     127.0.0.1:42876 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:06 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 150, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:36:06 DP1 TP0] Decode batch. #running-req: 1, #token: 601, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.68, #queue-req: 0, 
[2025-07-28 00:36:06 DP0 TP0] Decode batch. #running-req: 1, #token: 286, token usage: 0.00, cuda graph: True, gen throughput (token/s): 188.60, #queue-req: 0, 
[2025-07-28 00:36:06 DP1 TP0] Decode batch. #running-req: 1, #token: 641, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.13, #queue-req: 0, 
[2025-07-28 00:36:06 DP0 TP0] Decode batch. #running-req: 1, #token: 326, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.02, #queue-req: 0, 
[2025-07-28 00:36:06 DP1 TP0] Decode batch. #running-req: 1, #token: 681, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.91, #queue-req: 0, 
[2025-07-28 00:36:06 DP0 TP0] Decode batch. #running-req: 1, #token: 366, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.71, #queue-req: 0, 
[2025-07-28 00:36:06 DP1 TP0] Decode batch. #running-req: 1, #token: 721, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.32, #queue-req: 0, 
[2025-07-28 00:36:06 DP0 TP0] Decode batch. #running-req: 1, #token: 406, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.94, #queue-req: 0, 
[2025-07-28 00:36:06 DP1 TP0] Decode batch. #running-req: 1, #token: 761, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.01, #queue-req: 0, 
[2025-07-28 00:36:06 DP0 TP0] Decode batch. #running-req: 1, #token: 446, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.89, #queue-req: 0, 
[2025-07-28 00:36:06 DP1 TP0] Decode batch. #running-req: 1, #token: 801, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.00, #queue-req: 0, 
[2025-07-28 00:36:06 DP0 TP0] Decode batch. #running-req: 1, #token: 486, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.64, #queue-req: 0, 
[2025-07-28 00:36:06 DP1 TP0] Decode batch. #running-req: 1, #token: 841, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.35, #queue-req: 0, 
[2025-07-28 00:36:06 DP0 TP0] Decode batch. #running-req: 1, #token: 526, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.65, #queue-req: 0, 
[2025-07-28 00:36:07 DP1 TP0] Decode batch. #running-req: 1, #token: 881, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.67, #queue-req: 0, 
[2025-07-28 00:36:07 DP0 TP0] Decode batch. #running-req: 1, #token: 566, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.74, #queue-req: 0, 
[2025-07-28 00:36:07 DP1 TP0] Decode batch. #running-req: 1, #token: 921, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.34, #queue-req: 0, 
[2025-07-28 00:36:07 DP0 TP0] Decode batch. #running-req: 1, #token: 606, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.27, #queue-req: 0, 
[2025-07-28 00:36:07 DP1 TP0] Decode batch. #running-req: 1, #token: 961, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.04, #queue-req: 0, 
[2025-07-28 00:36:07 DP0 TP0] Decode batch. #running-req: 1, #token: 646, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.35, #queue-req: 0, 
[2025-07-28 00:36:07 DP1 TP0] Decode batch. #running-req: 1, #token: 1001, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.71, #queue-req: 0, 
[2025-07-28 00:36:07 DP0 TP0] Decode batch. #running-req: 1, #token: 686, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.61, #queue-req: 0, 
[2025-07-28 00:36:07 DP1 TP0] Decode batch. #running-req: 1, #token: 1041, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.12, #queue-req: 0, 
[2025-07-28 00:36:07] INFO:     127.0.0.1:42886 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:07] INFO:     127.0.0.1:42894 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:07 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 111, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:36:07 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 95, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:36:07 DP0 TP0] Decode batch. #running-req: 1, #token: 221, token usage: 0.00, cuda graph: True, gen throughput (token/s): 190.67, #queue-req: 0, 
[2025-07-28 00:36:07 DP1 TP0] Decode batch. #running-req: 1, #token: 249, token usage: 0.00, cuda graph: True, gen throughput (token/s): 187.06, #queue-req: 0, 
[2025-07-28 00:36:07 DP0 TP0] Decode batch. #running-req: 1, #token: 261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.42, #queue-req: 0, 
[2025-07-28 00:36:07 DP1 TP0] Decode batch. #running-req: 1, #token: 289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.49, #queue-req: 0, 
[2025-07-28 00:36:07 DP0 TP0] Decode batch. #running-req: 1, #token: 301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.82, #queue-req: 0, 
[2025-07-28 00:36:08 DP1 TP0] Decode batch. #running-req: 1, #token: 329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.01, #queue-req: 0, 
[2025-07-28 00:36:08 DP0 TP0] Decode batch. #running-req: 1, #token: 341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.46, #queue-req: 0, 
[2025-07-28 00:36:08 DP1 TP0] Decode batch. #running-req: 1, #token: 369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.92, #queue-req: 0, 
[2025-07-28 00:36:08 DP0 TP0] Decode batch. #running-req: 1, #token: 381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.60, #queue-req: 0, 
[2025-07-28 00:36:08 DP1 TP0] Decode batch. #running-req: 1, #token: 409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.46, #queue-req: 0, 
[2025-07-28 00:36:08 DP0 TP0] Decode batch. #running-req: 1, #token: 421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.58, #queue-req: 0, 
[2025-07-28 00:36:08 DP1 TP0] Decode batch. #running-req: 1, #token: 449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.26, #queue-req: 0, 
[2025-07-28 00:36:08 DP0 TP0] Decode batch. #running-req: 1, #token: 461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.10, #queue-req: 0, 
[2025-07-28 00:36:08] INFO:     127.0.0.1:42912 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:08 DP1 TP0] Decode batch. #running-req: 1, #token: 489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.76, #queue-req: 0, 
[2025-07-28 00:36:08 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 89, #cached-token: 106, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:36:08 DP1 TP0] Decode batch. #running-req: 2, #token: 655, token usage: 0.00, cuda graph: True, gen throughput (token/s): 185.45, #queue-req: 0, 
[2025-07-28 00:36:09 DP1 TP0] Decode batch. #running-req: 2, #token: 735, token usage: 0.00, cuda graph: True, gen throughput (token/s): 530.31, #queue-req: 0, 
[2025-07-28 00:36:09 DP1 TP0] Decode batch. #running-req: 2, #token: 815, token usage: 0.00, cuda graph: True, gen throughput (token/s): 531.57, #queue-req: 0, 
[2025-07-28 00:36:09] INFO:     127.0.0.1:42910 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:09 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 115, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:36:09 DP1 TP0] Decode batch. #running-req: 1, #token: 352, token usage: 0.00, cuda graph: True, gen throughput (token/s): 447.51, #queue-req: 0, 
[2025-07-28 00:36:09 DP0 TP0] Decode batch. #running-req: 1, #token: 249, token usage: 0.00, cuda graph: True, gen throughput (token/s): 38.55, #queue-req: 0, 
[2025-07-28 00:36:09 DP1 TP0] Decode batch. #running-req: 1, #token: 392, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.14, #queue-req: 0, 
[2025-07-28 00:36:09 DP1 TP0] Decode batch. #running-req: 1, #token: 432, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.86, #queue-req: 0, 
[2025-07-28 00:36:09 DP0 TP0] Decode batch. #running-req: 1, #token: 289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.06, #queue-req: 0, 
[2025-07-28 00:36:09 DP1 TP0] Decode batch. #running-req: 1, #token: 472, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.05, #queue-req: 0, 
[2025-07-28 00:36:09 DP0 TP0] Decode batch. #running-req: 1, #token: 329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.81, #queue-req: 0, 
[2025-07-28 00:36:09 DP1 TP0] Decode batch. #running-req: 1, #token: 512, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.60, #queue-req: 0, 
[2025-07-28 00:36:09 DP0 TP0] Decode batch. #running-req: 1, #token: 369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.44, #queue-req: 0, 
[2025-07-28 00:36:10 DP1 TP0] Decode batch. #running-req: 1, #token: 552, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.50, #queue-req: 0, 
[2025-07-28 00:36:10 DP0 TP0] Decode batch. #running-req: 1, #token: 409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.17, #queue-req: 0, 
[2025-07-28 00:36:10 DP1 TP0] Decode batch. #running-req: 1, #token: 592, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.14, #queue-req: 0, 
[2025-07-28 00:36:10 DP0 TP0] Decode batch. #running-req: 1, #token: 449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.61, #queue-req: 0, 
[2025-07-28 00:36:10 DP1 TP0] Decode batch. #running-req: 1, #token: 632, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.34, #queue-req: 0, 
[2025-07-28 00:36:10 DP0 TP0] Decode batch. #running-req: 1, #token: 489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.79, #queue-req: 0, 
[2025-07-28 00:36:10 DP1 TP0] Decode batch. #running-req: 1, #token: 672, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.18, #queue-req: 0, 
[2025-07-28 00:36:10 DP0 TP0] Decode batch. #running-req: 1, #token: 529, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.25, #queue-req: 0, 
[2025-07-28 00:36:10 DP1 TP0] Decode batch. #running-req: 1, #token: 712, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.00, #queue-req: 0, 
[2025-07-28 00:36:10 DP0 TP0] Decode batch. #running-req: 1, #token: 569, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.52, #queue-req: 0, 
[2025-07-28 00:36:10 DP1 TP0] Decode batch. #running-req: 1, #token: 752, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.08, #queue-req: 0, 
[2025-07-28 00:36:10 DP0 TP0] Decode batch. #running-req: 1, #token: 609, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.98, #queue-req: 0, 
[2025-07-28 00:36:10 DP1 TP0] Decode batch. #running-req: 1, #token: 792, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.15, #queue-req: 0, 
[2025-07-28 00:36:10 DP0 TP0] Decode batch. #running-req: 1, #token: 649, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.52, #queue-req: 0, 
[2025-07-28 00:36:11 DP1 TP0] Decode batch. #running-req: 1, #token: 832, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.21, #queue-req: 0, 
[2025-07-28 00:36:11 DP0 TP0] Decode batch. #running-req: 1, #token: 689, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.41, #queue-req: 0, 
[2025-07-28 00:36:11] INFO:     127.0.0.1:42928 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:11 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 164, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:36:11] INFO:     127.0.0.1:42936 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:11 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 143, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:36:11 DP1 TP0] Decode batch. #running-req: 1, #token: 283, token usage: 0.00, cuda graph: True, gen throughput (token/s): 191.77, #queue-req: 0, 
[2025-07-28 00:36:11 DP0 TP0] Decode batch. #running-req: 1, #token: 256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 186.57, #queue-req: 0, 
[2025-07-28 00:36:11 DP1 TP0] Decode batch. #running-req: 1, #token: 323, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.14, #queue-req: 0, 
[2025-07-28 00:36:11 DP0 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.96, #queue-req: 0, 
[2025-07-28 00:36:11 DP1 TP0] Decode batch. #running-req: 1, #token: 363, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.06, #queue-req: 0, 
[2025-07-28 00:36:11 DP0 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.45, #queue-req: 0, 
[2025-07-28 00:36:11 DP1 TP0] Decode batch. #running-req: 1, #token: 403, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.70, #queue-req: 0, 
[2025-07-28 00:36:11 DP0 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.99, #queue-req: 0, 
[2025-07-28 00:36:11 DP1 TP0] Decode batch. #running-req: 1, #token: 443, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.51, #queue-req: 0, 
[2025-07-28 00:36:11 DP0 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.71, #queue-req: 0, 
[2025-07-28 00:36:11 DP1 TP0] Decode batch. #running-req: 1, #token: 483, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.63, #queue-req: 0, 
[2025-07-28 00:36:11 DP0 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.87, #queue-req: 0, 
[2025-07-28 00:36:12 DP1 TP0] Decode batch. #running-req: 1, #token: 523, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.53, #queue-req: 0, 
[2025-07-28 00:36:12 DP0 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.98, #queue-req: 0, 
[2025-07-28 00:36:12 DP1 TP0] Decode batch. #running-req: 1, #token: 563, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.18, #queue-req: 0, 
[2025-07-28 00:36:12 DP0 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.82, #queue-req: 0, 
[2025-07-28 00:36:12 DP1 TP0] Decode batch. #running-req: 1, #token: 603, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.80, #queue-req: 0, 
[2025-07-28 00:36:12 DP0 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.67, #queue-req: 0, 
[2025-07-28 00:36:12 DP1 TP0] Decode batch. #running-req: 1, #token: 643, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.94, #queue-req: 0, 
[2025-07-28 00:36:12 DP0 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.77, #queue-req: 0, 
[2025-07-28 00:36:12 DP1 TP0] Decode batch. #running-req: 1, #token: 683, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.97, #queue-req: 0, 
[2025-07-28 00:36:12 DP0 TP0] Decode batch. #running-req: 1, #token: 656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.58, #queue-req: 0, 
[2025-07-28 00:36:12 DP1 TP0] Decode batch. #running-req: 1, #token: 723, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.23, #queue-req: 0, 
[2025-07-28 00:36:12 DP0 TP0] Decode batch. #running-req: 1, #token: 696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.05, #queue-req: 0, 
[2025-07-28 00:36:12 DP1 TP0] Decode batch. #running-req: 1, #token: 763, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.68, #queue-req: 0, 
[2025-07-28 00:36:12 DP0 TP0] Decode batch. #running-req: 1, #token: 736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.20, #queue-req: 0, 
[2025-07-28 00:36:13 DP1 TP0] Decode batch. #running-req: 1, #token: 803, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.10, #queue-req: 0, 
[2025-07-28 00:36:13 DP0 TP0] Decode batch. #running-req: 1, #token: 776, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.96, #queue-req: 0, 
[2025-07-28 00:36:13] INFO:     127.0.0.1:42956 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:13 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 117, #cached-token: 109, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:36:13] INFO:     127.0.0.1:42952 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:13 DP1 TP0] Decode batch. #running-req: 1, #token: 238, token usage: 0.00, cuda graph: True, gen throughput (token/s): 263.90, #queue-req: 0, 
[2025-07-28 00:36:13 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 125, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:36:13 DP1 TP0] Decode batch. #running-req: 1, #token: 278, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.35, #queue-req: 0, 
[2025-07-28 00:36:13 DP0 TP0] Decode batch. #running-req: 1, #token: 265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 120.44, #queue-req: 0, 
[2025-07-28 00:36:13 DP1 TP0] Decode batch. #running-req: 1, #token: 318, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.16, #queue-req: 0, 
[2025-07-28 00:36:13 DP0 TP0] Decode batch. #running-req: 1, #token: 305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.58, #queue-req: 0, 
[2025-07-28 00:36:13 DP1 TP0] Decode batch. #running-req: 1, #token: 358, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.58, #queue-req: 0, 
[2025-07-28 00:36:13 DP0 TP0] Decode batch. #running-req: 1, #token: 345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.55, #queue-req: 0, 
[2025-07-28 00:36:13 DP1 TP0] Decode batch. #running-req: 1, #token: 398, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.44, #queue-req: 0, 
[2025-07-28 00:36:13 DP0 TP0] Decode batch. #running-req: 1, #token: 385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.26, #queue-req: 0, 
[2025-07-28 00:36:13 DP1 TP0] Decode batch. #running-req: 1, #token: 438, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.41, #queue-req: 0, 
[2025-07-28 00:36:13 DP0 TP0] Decode batch. #running-req: 1, #token: 425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.56, #queue-req: 0, 
[2025-07-28 00:36:13 DP1 TP0] Decode batch. #running-req: 1, #token: 478, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.05, #queue-req: 0, 
[2025-07-28 00:36:14 DP0 TP0] Decode batch. #running-req: 1, #token: 465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.79, #queue-req: 0, 
[2025-07-28 00:36:14 DP1 TP0] Decode batch. #running-req: 1, #token: 518, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.42, #queue-req: 0, 
[2025-07-28 00:36:14 DP0 TP0] Decode batch. #running-req: 1, #token: 505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.79, #queue-req: 0, 
[2025-07-28 00:36:14 DP1 TP0] Decode batch. #running-req: 1, #token: 558, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.28, #queue-req: 0, 
[2025-07-28 00:36:14 DP0 TP0] Decode batch. #running-req: 1, #token: 545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.20, #queue-req: 0, 
[2025-07-28 00:36:14] INFO:     127.0.0.1:53082 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:14 DP1 TP0] Decode batch. #running-req: 1, #token: 598, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.59, #queue-req: 0, 
[2025-07-28 00:36:14 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 196, #cached-token: 109, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:36:14] INFO:     127.0.0.1:53068 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:14 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:36:14 DP1 TP0] Decode batch. #running-req: 1, #token: 345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 271.74, #queue-req: 0, 
[2025-07-28 00:36:14 DP0 TP0] Decode batch. #running-req: 1, #token: 221, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.61, #queue-req: 0, 
[2025-07-28 00:36:14 DP1 TP0] Decode batch. #running-req: 1, #token: 385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.74, #queue-req: 0, 
[2025-07-28 00:36:14 DP0 TP0] Decode batch. #running-req: 1, #token: 261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.41, #queue-req: 0, 
[2025-07-28 00:36:14 DP1 TP0] Decode batch. #running-req: 1, #token: 425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.68, #queue-req: 0, 
[2025-07-28 00:36:14 DP0 TP0] Decode batch. #running-req: 1, #token: 301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.89, #queue-req: 0, 
[2025-07-28 00:36:14 DP1 TP0] Decode batch. #running-req: 1, #token: 465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.84, #queue-req: 0, 
[2025-07-28 00:36:15 DP0 TP0] Decode batch. #running-req: 1, #token: 341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.04, #queue-req: 0, 
[2025-07-28 00:36:15 DP1 TP0] Decode batch. #running-req: 1, #token: 505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.08, #queue-req: 0, 
[2025-07-28 00:36:15 DP0 TP0] Decode batch. #running-req: 1, #token: 381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.23, #queue-req: 0, 
[2025-07-28 00:36:15 DP1 TP0] Decode batch. #running-req: 1, #token: 545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.76, #queue-req: 0, 
[2025-07-28 00:36:15 DP0 TP0] Decode batch. #running-req: 1, #token: 421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.10, #queue-req: 0, 
[2025-07-28 00:36:15 DP1 TP0] Decode batch. #running-req: 1, #token: 585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.55, #queue-req: 0, 
[2025-07-28 00:36:15 DP0 TP0] Decode batch. #running-req: 1, #token: 461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.56, #queue-req: 0, 
[2025-07-28 00:36:15 DP1 TP0] Decode batch. #running-req: 1, #token: 625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.17, #queue-req: 0, 
[2025-07-28 00:36:15 DP0 TP0] Decode batch. #running-req: 1, #token: 501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.50, #queue-req: 0, 
[2025-07-28 00:36:15 DP1 TP0] Decode batch. #running-req: 1, #token: 665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.09, #queue-req: 0, 
[2025-07-28 00:36:15 DP0 TP0] Decode batch. #running-req: 1, #token: 541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.71, #queue-req: 0, 
[2025-07-28 00:36:15 DP1 TP0] Decode batch. #running-req: 1, #token: 705, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.57, #queue-req: 0, 
[2025-07-28 00:36:15 DP0 TP0] Decode batch. #running-req: 1, #token: 581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.75, #queue-req: 0, 
[2025-07-28 00:36:15 DP1 TP0] Decode batch. #running-req: 1, #token: 745, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.86, #queue-req: 0, 
[2025-07-28 00:36:16 DP0 TP0] Decode batch. #running-req: 1, #token: 621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.46, #queue-req: 0, 
[2025-07-28 00:36:16 DP1 TP0] Decode batch. #running-req: 1, #token: 785, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.98, #queue-req: 0, 
[2025-07-28 00:36:16] INFO:     127.0.0.1:53114 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:16 DP0 TP0] Decode batch. #running-req: 1, #token: 0, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.09, #queue-req: 0, 
[2025-07-28 00:36:16 DP1 TP0] Decode batch. #running-req: 1, #token: 825, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.90, #queue-req: 0, 
[2025-07-28 00:36:16 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 154, #cached-token: 109, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:36:16 DP1 TP0] Decode batch. #running-req: 2, #token: 302, token usage: 0.00, cuda graph: True, gen throughput (token/s): 371.51, #queue-req: 0, 
[2025-07-28 00:36:16] INFO:     127.0.0.1:53098 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:16 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 128, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:36:16 DP1 TP0] Decode batch. #running-req: 1, #token: 341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.02, #queue-req: 0, 
[2025-07-28 00:36:16 DP0 TP0] Decode batch. #running-req: 1, #token: 277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 90.32, #queue-req: 0, 
[2025-07-28 00:36:16 DP1 TP0] Decode batch. #running-req: 1, #token: 381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.17, #queue-req: 0, 
[2025-07-28 00:36:16 DP0 TP0] Decode batch. #running-req: 1, #token: 317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.93, #queue-req: 0, 
[2025-07-28 00:36:16 DP1 TP0] Decode batch. #running-req: 1, #token: 421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.60, #queue-req: 0, 
[2025-07-28 00:36:16 DP0 TP0] Decode batch. #running-req: 1, #token: 357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.83, #queue-req: 0, 
[2025-07-28 00:36:16 DP1 TP0] Decode batch. #running-req: 1, #token: 461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.64, #queue-req: 0, 
[2025-07-28 00:36:17 DP0 TP0] Decode batch. #running-req: 1, #token: 397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.14, #queue-req: 0, 
[2025-07-28 00:36:17 DP1 TP0] Decode batch. #running-req: 1, #token: 501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.77, #queue-req: 0, 
[2025-07-28 00:36:17 DP0 TP0] Decode batch. #running-req: 1, #token: 437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.82, #queue-req: 0, 
[2025-07-28 00:36:17 DP1 TP0] Decode batch. #running-req: 1, #token: 541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.53, #queue-req: 0, 
[2025-07-28 00:36:17 DP0 TP0] Decode batch. #running-req: 1, #token: 477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.04, #queue-req: 0, 
[2025-07-28 00:36:17 DP1 TP0] Decode batch. #running-req: 1, #token: 581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.01, #queue-req: 0, 
[2025-07-28 00:36:17 DP0 TP0] Decode batch. #running-req: 1, #token: 517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.32, #queue-req: 0, 
[2025-07-28 00:36:17 DP1 TP0] Decode batch. #running-req: 1, #token: 621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.22, #queue-req: 0, 
[2025-07-28 00:36:17] INFO:     127.0.0.1:53120 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:17 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:36:17 DP0 TP0] Decode batch. #running-req: 1, #token: 557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.69, #queue-req: 0, 
[2025-07-28 00:36:17 DP1 TP0] Decode batch. #running-req: 1, #token: 267, token usage: 0.00, cuda graph: True, gen throughput (token/s): 194.83, #queue-req: 0, 
[2025-07-28 00:36:17 DP0 TP0] Decode batch. #running-req: 1, #token: 597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.98, #queue-req: 0, 
[2025-07-28 00:36:17 DP1 TP0] Decode batch. #running-req: 1, #token: 307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.81, #queue-req: 0, 
[2025-07-28 00:36:17 DP0 TP0] Decode batch. #running-req: 1, #token: 637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.96, #queue-req: 0, 
[2025-07-28 00:36:17 DP1 TP0] Decode batch. #running-req: 1, #token: 347, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.45, #queue-req: 0, 
[2025-07-28 00:36:17] INFO:     127.0.0.1:53124 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:17 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:36:18 DP1 TP0] Decode batch. #running-req: 1, #token: 387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.01, #queue-req: 0, 
[2025-07-28 00:36:18 DP0 TP0] Decode batch. #running-req: 1, #token: 257, token usage: 0.00, cuda graph: True, gen throughput (token/s): 185.37, #queue-req: 0, 
[2025-07-28 00:36:18 DP1 TP0] Decode batch. #running-req: 1, #token: 427, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.99, #queue-req: 0, 
[2025-07-28 00:36:18 DP0 TP0] Decode batch. #running-req: 1, #token: 297, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.45, #queue-req: 0, 
[2025-07-28 00:36:18 DP1 TP0] Decode batch. #running-req: 1, #token: 467, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.55, #queue-req: 0, 
[2025-07-28 00:36:18 DP0 TP0] Decode batch. #running-req: 1, #token: 337, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.87, #queue-req: 0, 
[2025-07-28 00:36:18 DP1 TP0] Decode batch. #running-req: 1, #token: 507, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.18, #queue-req: 0, 
[2025-07-28 00:36:18 DP0 TP0] Decode batch. #running-req: 1, #token: 377, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.11, #queue-req: 0, 
[2025-07-28 00:36:18 DP1 TP0] Decode batch. #running-req: 1, #token: 547, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.25, #queue-req: 0, 
[2025-07-28 00:36:18 DP0 TP0] Decode batch. #running-req: 1, #token: 417, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.71, #queue-req: 0, 
[2025-07-28 00:36:18] INFO:     127.0.0.1:53128 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:18 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 95, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:36:18 DP0 TP0] Decode batch. #running-req: 1, #token: 457, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.78, #queue-req: 0, 
[2025-07-28 00:36:18 DP1 TP0] Decode batch. #running-req: 1, #token: 217, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.64, #queue-req: 0, 
[2025-07-28 00:36:18 DP0 TP0] Decode batch. #running-req: 1, #token: 497, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.21, #queue-req: 0, 
[2025-07-28 00:36:18 DP1 TP0] Decode batch. #running-req: 1, #token: 257, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.31, #queue-req: 0, 
[2025-07-28 00:36:19 DP0 TP0] Decode batch. #running-req: 1, #token: 537, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.31, #queue-req: 0, 
[2025-07-28 00:36:19 DP1 TP0] Decode batch. #running-req: 1, #token: 297, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.08, #queue-req: 0, 
[2025-07-28 00:36:19 DP0 TP0] Decode batch. #running-req: 1, #token: 577, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.11, #queue-req: 0, 
[2025-07-28 00:36:19 DP1 TP0] Decode batch. #running-req: 1, #token: 337, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.95, #queue-req: 0, 
[2025-07-28 00:36:19 DP0 TP0] Decode batch. #running-req: 1, #token: 617, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.16, #queue-req: 0, 
[2025-07-28 00:36:19 DP1 TP0] Decode batch. #running-req: 1, #token: 377, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.26, #queue-req: 0, 
[2025-07-28 00:36:19 DP0 TP0] Decode batch. #running-req: 1, #token: 657, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.69, #queue-req: 0, 
[2025-07-28 00:36:19 DP1 TP0] Decode batch. #running-req: 1, #token: 417, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.13, #queue-req: 0, 
[2025-07-28 00:36:19 DP0 TP0] Decode batch. #running-req: 1, #token: 697, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.50, #queue-req: 0, 
[2025-07-28 00:36:19 DP1 TP0] Decode batch. #running-req: 1, #token: 457, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.24, #queue-req: 0, 
[2025-07-28 00:36:19 DP0 TP0] Decode batch. #running-req: 1, #token: 737, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.11, #queue-req: 0, 
[2025-07-28 00:36:19 DP1 TP0] Decode batch. #running-req: 1, #token: 497, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.45, #queue-req: 0, 
[2025-07-28 00:36:19] INFO:     127.0.0.1:53136 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:19 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 107, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:36:19 DP1 TP0] Decode batch. #running-req: 1, #token: 537, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.54, #queue-req: 0, 
[2025-07-28 00:36:19 DP0 TP0] Decode batch. #running-req: 1, #token: 259, token usage: 0.00, cuda graph: True, gen throughput (token/s): 194.63, #queue-req: 0, 
[2025-07-28 00:36:20 DP1 TP0] Decode batch. #running-req: 1, #token: 577, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.03, #queue-req: 0, 
[2025-07-28 00:36:20 DP0 TP0] Decode batch. #running-req: 1, #token: 299, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.06, #queue-req: 0, 
[2025-07-28 00:36:20 DP1 TP0] Decode batch. #running-req: 1, #token: 617, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.13, #queue-req: 0, 
[2025-07-28 00:36:20 DP0 TP0] Decode batch. #running-req: 1, #token: 339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.25, #queue-req: 0, 
[2025-07-28 00:36:20 DP1 TP0] Decode batch. #running-req: 1, #token: 657, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.37, #queue-req: 0, 
[2025-07-28 00:36:20 DP0 TP0] Decode batch. #running-req: 1, #token: 379, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.74, #queue-req: 0, 
[2025-07-28 00:36:20 DP1 TP0] Decode batch. #running-req: 1, #token: 697, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.41, #queue-req: 0, 
[2025-07-28 00:36:20 DP0 TP0] Decode batch. #running-req: 1, #token: 419, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.55, #queue-req: 0, 
[2025-07-28 00:36:20 DP1 TP0] Decode batch. #running-req: 1, #token: 737, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.47, #queue-req: 0, 
[2025-07-28 00:36:20 DP0 TP0] Decode batch. #running-req: 1, #token: 459, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.97, #queue-req: 0, 
[2025-07-28 00:36:20 DP1 TP0] Decode batch. #running-req: 1, #token: 777, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.41, #queue-req: 0, 
[2025-07-28 00:36:20 DP0 TP0] Decode batch. #running-req: 1, #token: 499, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.38, #queue-req: 0, 
[2025-07-28 00:36:20 DP1 TP0] Decode batch. #running-req: 1, #token: 817, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.09, #queue-req: 0, 
[2025-07-28 00:36:20] INFO:     127.0.0.1:53140 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:20 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 78, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:36:20 DP0 TP0] Decode batch. #running-req: 1, #token: 539, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.93, #queue-req: 0, 
[2025-07-28 00:36:20 DP0 TP0] Decode batch. #running-req: 1, #token: 579, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.39, #queue-req: 0, 
[2025-07-28 00:36:21 DP1 TP0] Decode batch. #running-req: 1, #token: 221, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.34, #queue-req: 0, 
[2025-07-28 00:36:21 DP0 TP0] Decode batch. #running-req: 1, #token: 619, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.94, #queue-req: 0, 
[2025-07-28 00:36:21 DP1 TP0] Decode batch. #running-req: 1, #token: 261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.97, #queue-req: 0, 
[2025-07-28 00:36:21 DP0 TP0] Decode batch. #running-req: 1, #token: 659, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.87, #queue-req: 0, 
[2025-07-28 00:36:21 DP1 TP0] Decode batch. #running-req: 1, #token: 301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.49, #queue-req: 0, 
[2025-07-28 00:36:21 DP0 TP0] Decode batch. #running-req: 1, #token: 699, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.04, #queue-req: 0, 
[2025-07-28 00:36:21 DP1 TP0] Decode batch. #running-req: 1, #token: 341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.99, #queue-req: 0, 
[2025-07-28 00:36:21 DP0 TP0] Decode batch. #running-req: 1, #token: 739, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.76, #queue-req: 0, 
[2025-07-28 00:36:21 DP1 TP0] Decode batch. #running-req: 1, #token: 381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.83, #queue-req: 0, 
[2025-07-28 00:36:21 DP0 TP0] Decode batch. #running-req: 1, #token: 779, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.49, #queue-req: 0, 
[2025-07-28 00:36:21 DP1 TP0] Decode batch. #running-req: 1, #token: 421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.78, #queue-req: 0, 
[2025-07-28 00:36:21] INFO:     127.0.0.1:53148 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:21 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 146, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:36:21 DP1 TP0] Decode batch. #running-req: 1, #token: 461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.74, #queue-req: 0, 
[2025-07-28 00:36:21 DP0 TP0] Decode batch. #running-req: 1, #token: 262, token usage: 0.00, cuda graph: True, gen throughput (token/s): 193.33, #queue-req: 0, 
[2025-07-28 00:36:21 DP1 TP0] Decode batch. #running-req: 1, #token: 501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.07, #queue-req: 0, 
[2025-07-28 00:36:21 DP0 TP0] Decode batch. #running-req: 1, #token: 302, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.38, #queue-req: 0, 
[2025-07-28 00:36:22 DP1 TP0] Decode batch. #running-req: 1, #token: 541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.42, #queue-req: 0, 
[2025-07-28 00:36:22 DP0 TP0] Decode batch. #running-req: 1, #token: 342, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.31, #queue-req: 0, 
[2025-07-28 00:36:22 DP1 TP0] Decode batch. #running-req: 1, #token: 581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.75, #queue-req: 0, 
[2025-07-28 00:36:22 DP0 TP0] Decode batch. #running-req: 1, #token: 382, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.60, #queue-req: 0, 
[2025-07-28 00:36:22] INFO:     127.0.0.1:53150 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:22 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:36:22 DP0 TP0] Decode batch. #running-req: 1, #token: 422, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.65, #queue-req: 0, 
[2025-07-28 00:36:22 DP1 TP0] Decode batch. #running-req: 1, #token: 205, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.40, #queue-req: 0, 
[2025-07-28 00:36:22 DP0 TP0] Decode batch. #running-req: 1, #token: 462, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.88, #queue-req: 0, 
[2025-07-28 00:36:22 DP1 TP0] Decode batch. #running-req: 1, #token: 245, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.24, #queue-req: 0, 
[2025-07-28 00:36:22 DP0 TP0] Decode batch. #running-req: 1, #token: 502, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.95, #queue-req: 0, 
[2025-07-28 00:36:22 DP1 TP0] Decode batch. #running-req: 1, #token: 285, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.23, #queue-req: 0, 
[2025-07-28 00:36:22 DP0 TP0] Decode batch. #running-req: 1, #token: 542, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.21, #queue-req: 0, 
[2025-07-28 00:36:22 DP1 TP0] Decode batch. #running-req: 1, #token: 325, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.40, #queue-req: 0, 
[2025-07-28 00:36:22 DP0 TP0] Decode batch. #running-req: 1, #token: 582, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.55, #queue-req: 0, 
[2025-07-28 00:36:22 DP1 TP0] Decode batch. #running-req: 1, #token: 365, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.17, #queue-req: 0, 
[2025-07-28 00:36:23 DP0 TP0] Decode batch. #running-req: 1, #token: 622, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.33, #queue-req: 0, 
[2025-07-28 00:36:23 DP1 TP0] Decode batch. #running-req: 1, #token: 405, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.86, #queue-req: 0, 
[2025-07-28 00:36:23 DP0 TP0] Decode batch. #running-req: 1, #token: 662, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.99, #queue-req: 0, 
[2025-07-28 00:36:23 DP1 TP0] Decode batch. #running-req: 1, #token: 445, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.82, #queue-req: 0, 
[2025-07-28 00:36:23 DP0 TP0] Decode batch. #running-req: 1, #token: 702, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.80, #queue-req: 0, 
[2025-07-28 00:36:23 DP1 TP0] Decode batch. #running-req: 1, #token: 485, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.97, #queue-req: 0, 
[2025-07-28 00:36:23 DP0 TP0] Decode batch. #running-req: 1, #token: 742, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.39, #queue-req: 0, 
[2025-07-28 00:36:23] INFO:     127.0.0.1:53162 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:23 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 173, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:36:23 DP1 TP0] Decode batch. #running-req: 1, #token: 525, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.06, #queue-req: 0, 
[2025-07-28 00:36:23 DP1 TP0] Decode batch. #running-req: 1, #token: 565, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.59, #queue-req: 0, 
[2025-07-28 00:36:23 DP0 TP0] Decode batch. #running-req: 1, #token: 320, token usage: 0.00, cuda graph: True, gen throughput (token/s): 192.66, #queue-req: 0, 
[2025-07-28 00:36:23 DP1 TP0] Decode batch. #running-req: 1, #token: 605, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.45, #queue-req: 0, 
[2025-07-28 00:36:23 DP0 TP0] Decode batch. #running-req: 1, #token: 360, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.24, #queue-req: 0, 
[2025-07-28 00:36:23 DP1 TP0] Decode batch. #running-req: 1, #token: 645, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.94, #queue-req: 0, 
[2025-07-28 00:36:23 DP0 TP0] Decode batch. #running-req: 1, #token: 400, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.77, #queue-req: 0, 
[2025-07-28 00:36:24 DP1 TP0] Decode batch. #running-req: 1, #token: 685, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.45, #queue-req: 0, 
[2025-07-28 00:36:24 DP0 TP0] Decode batch. #running-req: 1, #token: 440, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.00, #queue-req: 0, 
[2025-07-28 00:36:24 DP1 TP0] Decode batch. #running-req: 1, #token: 725, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.34, #queue-req: 0, 
[2025-07-28 00:36:24] INFO:     127.0.0.1:33662 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:24 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 178, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:36:24 DP0 TP0] Decode batch. #running-req: 1, #token: 480, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.91, #queue-req: 0, 
[2025-07-28 00:36:24 DP0 TP0] Decode batch. #running-req: 1, #token: 520, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.50, #queue-req: 0, 
[2025-07-28 00:36:24 DP1 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 190.03, #queue-req: 0, 
[2025-07-28 00:36:24 DP0 TP0] Decode batch. #running-req: 1, #token: 560, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.74, #queue-req: 0, 
[2025-07-28 00:36:24 DP1 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.49, #queue-req: 0, 
[2025-07-28 00:36:24 DP0 TP0] Decode batch. #running-req: 1, #token: 600, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.66, #queue-req: 0, 
[2025-07-28 00:36:24 DP1 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.32, #queue-req: 0, 
[2025-07-28 00:36:24 DP0 TP0] Decode batch. #running-req: 1, #token: 640, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.71, #queue-req: 0, 
[2025-07-28 00:36:24 DP1 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.62, #queue-req: 0, 
[2025-07-28 00:36:24 DP0 TP0] Decode batch. #running-req: 1, #token: 680, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.23, #queue-req: 0, 
[2025-07-28 00:36:24 DP1 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.42, #queue-req: 0, 
[2025-07-28 00:36:25 DP0 TP0] Decode batch. #running-req: 1, #token: 720, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.90, #queue-req: 0, 
[2025-07-28 00:36:25 DP1 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.83, #queue-req: 0, 
[2025-07-28 00:36:25] INFO:     127.0.0.1:33666 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:25 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 88, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:36:25 DP1 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.34, #queue-req: 0, 
[2025-07-28 00:36:25 DP0 TP0] Decode batch. #running-req: 1, #token: 217, token usage: 0.00, cuda graph: True, gen throughput (token/s): 195.04, #queue-req: 0, 
[2025-07-28 00:36:25 DP1 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.05, #queue-req: 0, 
[2025-07-28 00:36:25 DP0 TP0] Decode batch. #running-req: 1, #token: 257, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.12, #queue-req: 0, 
[2025-07-28 00:36:25] INFO:     127.0.0.1:33672 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:25 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 153, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:36:25 DP0 TP0] Decode batch. #running-req: 1, #token: 297, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.30, #queue-req: 0, 
[2025-07-28 00:36:25 DP1 TP0] Decode batch. #running-req: 1, #token: 287, token usage: 0.00, cuda graph: True, gen throughput (token/s): 190.85, #queue-req: 0, 
[2025-07-28 00:36:25 DP0 TP0] Decode batch. #running-req: 1, #token: 337, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.12, #queue-req: 0, 
[2025-07-28 00:36:25 DP1 TP0] Decode batch. #running-req: 1, #token: 327, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.73, #queue-req: 0, 
[2025-07-28 00:36:25 DP0 TP0] Decode batch. #running-req: 1, #token: 377, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.14, #queue-req: 0, 
[2025-07-28 00:36:25 DP1 TP0] Decode batch. #running-req: 1, #token: 367, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.03, #queue-req: 0, 
[2025-07-28 00:36:25 DP0 TP0] Decode batch. #running-req: 1, #token: 417, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.13, #queue-req: 0, 
[2025-07-28 00:36:25 DP1 TP0] Decode batch. #running-req: 1, #token: 407, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.11, #queue-req: 0, 
[2025-07-28 00:36:26 DP0 TP0] Decode batch. #running-req: 1, #token: 457, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.09, #queue-req: 0, 
[2025-07-28 00:36:26 DP1 TP0] Decode batch. #running-req: 1, #token: 447, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.59, #queue-req: 0, 
[2025-07-28 00:36:26 DP0 TP0] Decode batch. #running-req: 1, #token: 497, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.78, #queue-req: 0, 
[2025-07-28 00:36:26 DP1 TP0] Decode batch. #running-req: 1, #token: 487, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.64, #queue-req: 0, 
[2025-07-28 00:36:26 DP0 TP0] Decode batch. #running-req: 1, #token: 537, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.68, #queue-req: 0, 
[2025-07-28 00:36:26 DP1 TP0] Decode batch. #running-req: 1, #token: 527, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.55, #queue-req: 0, 
[2025-07-28 00:36:26 DP0 TP0] Decode batch. #running-req: 1, #token: 577, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.77, #queue-req: 0, 
[2025-07-28 00:36:26 DP1 TP0] Decode batch. #running-req: 1, #token: 567, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.45, #queue-req: 0, 
[2025-07-28 00:36:26 DP0 TP0] Decode batch. #running-req: 1, #token: 617, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.63, #queue-req: 0, 
[2025-07-28 00:36:26 DP1 TP0] Decode batch. #running-req: 1, #token: 607, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.42, #queue-req: 0, 
[2025-07-28 00:36:26 DP0 TP0] Decode batch. #running-req: 1, #token: 657, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.77, #queue-req: 0, 
[2025-07-28 00:36:26] INFO:     127.0.0.1:33688 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:26 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 123, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:36:26 DP1 TP0] Decode batch. #running-req: 1, #token: 647, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.01, #queue-req: 0, 
[2025-07-28 00:36:26 DP1 TP0] Decode batch. #running-req: 1, #token: 687, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.48, #queue-req: 0, 
[2025-07-28 00:36:26 DP0 TP0] Decode batch. #running-req: 1, #token: 269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 195.80, #queue-req: 0, 
[2025-07-28 00:36:26 DP1 TP0] Decode batch. #running-req: 1, #token: 727, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.40, #queue-req: 0, 
[2025-07-28 00:36:27] INFO:     127.0.0.1:33692 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:27 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 129, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:36:27 DP0 TP0] Decode batch. #running-req: 1, #token: 309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.05, #queue-req: 0, 
[2025-07-28 00:36:27 DP0 TP0] Decode batch. #running-req: 1, #token: 349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.95, #queue-req: 0, 
[2025-07-28 00:36:27 DP1 TP0] Decode batch. #running-req: 1, #token: 272, token usage: 0.00, cuda graph: True, gen throughput (token/s): 191.67, #queue-req: 0, 
[2025-07-28 00:36:27 DP0 TP0] Decode batch. #running-req: 1, #token: 389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.12, #queue-req: 0, 
[2025-07-28 00:36:27 DP1 TP0] Decode batch. #running-req: 1, #token: 312, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.74, #queue-req: 0, 
[2025-07-28 00:36:27 DP0 TP0] Decode batch. #running-req: 1, #token: 429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.10, #queue-req: 0, 
[2025-07-28 00:36:27 DP1 TP0] Decode batch. #running-req: 1, #token: 352, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.79, #queue-req: 0, 
[2025-07-28 00:36:27 DP0 TP0] Decode batch. #running-req: 1, #token: 469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.99, #queue-req: 0, 
[2025-07-28 00:36:27 DP1 TP0] Decode batch. #running-req: 1, #token: 392, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.04, #queue-req: 0, 
[2025-07-28 00:36:27 DP0 TP0] Decode batch. #running-req: 1, #token: 509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.82, #queue-req: 0, 
[2025-07-28 00:36:27 DP1 TP0] Decode batch. #running-req: 1, #token: 432, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.68, #queue-req: 0, 
[2025-07-28 00:36:27 DP0 TP0] Decode batch. #running-req: 1, #token: 549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.28, #queue-req: 0, 
[2025-07-28 00:36:27 DP1 TP0] Decode batch. #running-req: 1, #token: 472, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.57, #queue-req: 0, 
[2025-07-28 00:36:27 DP0 TP0] Decode batch. #running-req: 1, #token: 589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.54, #queue-req: 0, 
[2025-07-28 00:36:27 DP1 TP0] Decode batch. #running-req: 1, #token: 512, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.55, #queue-req: 0, 
[2025-07-28 00:36:28 DP0 TP0] Decode batch. #running-req: 1, #token: 629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.11, #queue-req: 0, 
[2025-07-28 00:36:28 DP1 TP0] Decode batch. #running-req: 1, #token: 552, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.17, #queue-req: 0, 
[2025-07-28 00:36:28 DP0 TP0] Decode batch. #running-req: 1, #token: 669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.52, #queue-req: 0, 
[2025-07-28 00:36:28 DP1 TP0] Decode batch. #running-req: 1, #token: 592, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.30, #queue-req: 0, 
[2025-07-28 00:36:28] INFO:     127.0.0.1:33698 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:28 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 112, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:36:28] INFO:     127.0.0.1:33700 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:28 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 113, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:36:28 DP0 TP0] Decode batch. #running-req: 1, #token: 257, token usage: 0.00, cuda graph: True, gen throughput (token/s): 190.13, #queue-req: 0, 
[2025-07-28 00:36:28 DP1 TP0] Decode batch. #running-req: 1, #token: 263, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.56, #queue-req: 0, 
[2025-07-28 00:36:28 DP0 TP0] Decode batch. #running-req: 1, #token: 297, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.52, #queue-req: 0, 
[2025-07-28 00:36:28 DP1 TP0] Decode batch. #running-req: 1, #token: 303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.42, #queue-req: 0, 
[2025-07-28 00:36:28 DP0 TP0] Decode batch. #running-req: 1, #token: 337, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.80, #queue-req: 0, 
[2025-07-28 00:36:28 DP1 TP0] Decode batch. #running-req: 1, #token: 343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.17, #queue-req: 0, 
[2025-07-28 00:36:28 DP0 TP0] Decode batch. #running-req: 1, #token: 377, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.66, #queue-req: 0, 
[2025-07-28 00:36:28 DP1 TP0] Decode batch. #running-req: 1, #token: 383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.17, #queue-req: 0, 
[2025-07-28 00:36:28 DP0 TP0] Decode batch. #running-req: 1, #token: 417, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.70, #queue-req: 0, 
[2025-07-28 00:36:28 DP1 TP0] Decode batch. #running-req: 1, #token: 423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.58, #queue-req: 0, 
[2025-07-28 00:36:29 DP0 TP0] Decode batch. #running-req: 1, #token: 457, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.46, #queue-req: 0, 
[2025-07-28 00:36:29 DP1 TP0] Decode batch. #running-req: 1, #token: 463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.49, #queue-req: 0, 
[2025-07-28 00:36:29 DP0 TP0] Decode batch. #running-req: 1, #token: 497, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.69, #queue-req: 0, 
[2025-07-28 00:36:29 DP1 TP0] Decode batch. #running-req: 1, #token: 503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.46, #queue-req: 0, 
[2025-07-28 00:36:29 DP0 TP0] Decode batch. #running-req: 1, #token: 537, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.09, #queue-req: 0, 
[2025-07-28 00:36:29 DP1 TP0] Decode batch. #running-req: 1, #token: 543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.92, #queue-req: 0, 
[2025-07-28 00:36:29 DP0 TP0] Decode batch. #running-req: 1, #token: 577, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.47, #queue-req: 0, 
[2025-07-28 00:36:29 DP1 TP0] Decode batch. #running-req: 1, #token: 583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.88, #queue-req: 0, 
[2025-07-28 00:36:29 DP0 TP0] Decode batch. #running-req: 1, #token: 617, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.47, #queue-req: 0, 
[2025-07-28 00:36:29 DP1 TP0] Decode batch. #running-req: 1, #token: 623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.79, #queue-req: 0, 
[2025-07-28 00:36:29 DP1 TP0] Decode batch. #running-req: 1, #token: 663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.07, #queue-req: 0, 
[2025-07-28 00:36:29 DP0 TP0] Decode batch. #running-req: 1, #token: 657, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.47, #queue-req: 0, 
[2025-07-28 00:36:29 DP0 TP0] Decode batch. #running-req: 1, #token: 697, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.46, #queue-req: 0, 
[2025-07-28 00:36:29 DP1 TP0] Decode batch. #running-req: 1, #token: 703, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.88, #queue-req: 0, 
[2025-07-28 00:36:30 DP1 TP0] Decode batch. #running-req: 1, #token: 743, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.99, #queue-req: 0, 
[2025-07-28 00:36:30 DP0 TP0] Decode batch. #running-req: 1, #token: 737, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.60, #queue-req: 0, 
[2025-07-28 00:36:30] INFO:     127.0.0.1:33728 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:30 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 103, #cached-token: 112, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:36:30 DP0 TP0] Decode batch. #running-req: 2, #token: 901, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.03, #queue-req: 0, 
[2025-07-28 00:36:30] INFO:     127.0.0.1:33712 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:30 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 89, #cached-token: 112, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:36:30 DP0 TP0] Decode batch. #running-req: 1, #token: 276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 412.29, #queue-req: 0, 
[2025-07-28 00:36:30 DP1 TP0] Decode batch. #running-req: 1, #token: 226, token usage: 0.00, cuda graph: True, gen throughput (token/s): 89.84, #queue-req: 0, 
[2025-07-28 00:36:30 DP0 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.32, #queue-req: 0, 
[2025-07-28 00:36:30 DP1 TP0] Decode batch. #running-req: 1, #token: 266, token usage: 0.00, cuda graph: True, gen throughput (token/s): 274.47, #queue-req: 0, 
[2025-07-28 00:36:30 DP0 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.06, #queue-req: 0, 
[2025-07-28 00:36:30 DP1 TP0] Decode batch. #running-req: 1, #token: 306, token usage: 0.00, cuda graph: True, gen throughput (token/s): 279.36, #queue-req: 0, 
[2025-07-28 00:36:30 DP0 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.36, #queue-req: 0, 
[2025-07-28 00:36:30 DP0 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.17, #queue-req: 0, 
[2025-07-28 00:36:30 DP1 TP0] Decode batch. #running-req: 1, #token: 346, token usage: 0.00, cuda graph: True, gen throughput (token/s): 281.86, #queue-req: 0, 
[2025-07-28 00:36:31 DP0 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.59, #queue-req: 0, 
[2025-07-28 00:36:31 DP1 TP0] Decode batch. #running-req: 1, #token: 386, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.56, #queue-req: 0, 
[2025-07-28 00:36:31 DP0 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.42, #queue-req: 0, 
[2025-07-28 00:36:31 DP1 TP0] Decode batch. #running-req: 1, #token: 426, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.72, #queue-req: 0, 
[2025-07-28 00:36:31 DP0 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.12, #queue-req: 0, 
[2025-07-28 00:36:31 DP1 TP0] Decode batch. #running-req: 1, #token: 466, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.28, #queue-req: 0, 
[2025-07-28 00:36:31 DP0 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.41, #queue-req: 0, 
[2025-07-28 00:36:31 DP1 TP0] Decode batch. #running-req: 1, #token: 506, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.03, #queue-req: 0, 
[2025-07-28 00:36:31] INFO:     127.0.0.1:33730 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:36:31 DP1 TP0] Decode batch. #running-req: 1, #token: 546, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.53, #queue-req: 0, 
[2025-07-28 00:36:31 DP1 TP0] Decode batch. #running-req: 1, #token: 586, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.36, #queue-req: 0, 
[2025-07-28 00:36:31] INFO:     127.0.0.1:33734 - "POST /v1/chat/completions HTTP/1.1" 200 OK

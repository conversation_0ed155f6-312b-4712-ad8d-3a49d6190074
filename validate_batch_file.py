#!/usr/bin/env python3
"""
Validation script for OpenAI Batch API requests file.
Checks format, required fields, and provides statistics.
"""

import json
import sys

def validate_batch_file(filename):
    """Validate the batch requests file."""
    print(f"Validating {filename}...")
    
    total_requests = 0
    valid_requests = 0
    custom_ids = set()
    errors = []
    
    required_fields = ['custom_id', 'method', 'url', 'body']
    required_body_fields = ['model', 'messages', 'max_tokens']
    
    with open(filename, 'r') as f:
        for line_num, line in enumerate(f, 1):
            total_requests += 1
            
            try:
                request = json.loads(line.strip())
                
                # Check required top-level fields
                missing_fields = [field for field in required_fields if field not in request]
                if missing_fields:
                    errors.append(f"Line {line_num}: Missing fields: {missing_fields}")
                    continue
                
                # Check custom_id uniqueness
                custom_id = request.get('custom_id')
                if custom_id in custom_ids:
                    errors.append(f"Line {line_num}: Duplicate custom_id: {custom_id}")
                else:
                    custom_ids.add(custom_id)
                
                # Check method and URL
                if request.get('method') != 'POST':
                    errors.append(f"Line {line_num}: Invalid method: {request.get('method')}")
                
                if request.get('url') != '/v1/chat/completions':
                    errors.append(f"Line {line_num}: Invalid URL: {request.get('url')}")
                
                # Check body structure
                body = request.get('body', {})
                missing_body_fields = [field for field in required_body_fields if field not in body]
                if missing_body_fields:
                    errors.append(f"Line {line_num}: Missing body fields: {missing_body_fields}")
                    continue
                
                # Check messages structure
                messages = body.get('messages', [])
                if not isinstance(messages, list) or len(messages) != 2:
                    errors.append(f"Line {line_num}: Invalid messages structure")
                    continue
                
                # Check message roles
                if messages[0].get('role') != 'system' or messages[1].get('role') != 'user':
                    errors.append(f"Line {line_num}: Invalid message roles")
                    continue
                
                # Check for evaluation content
                user_content = messages[1].get('content', '')
                if 'Question:' not in user_content or 'Target Answer:' not in user_content or 'Model Response:' not in user_content:
                    errors.append(f"Line {line_num}: Missing evaluation content structure")
                    continue
                
                valid_requests += 1
                
            except json.JSONDecodeError as e:
                errors.append(f"Line {line_num}: JSON decode error: {e}")
            except Exception as e:
                errors.append(f"Line {line_num}: Unexpected error: {e}")
    
    # Print results
    print(f"\n=== Validation Results ===")
    print(f"Total requests: {total_requests}")
    print(f"Valid requests: {valid_requests}")
    print(f"Invalid requests: {total_requests - valid_requests}")
    print(f"Unique custom_ids: {len(custom_ids)}")
    
    if errors:
        print(f"\n=== Errors Found ({len(errors)}) ===")
        for error in errors[:10]:  # Show first 10 errors
            print(f"  {error}")
        if len(errors) > 10:
            print(f"  ... and {len(errors) - 10} more errors")
    else:
        print("\n✅ All requests are valid!")
    
    # Sample request info
    if valid_requests > 0:
        print(f"\n=== Sample Request Info ===")
        with open(filename, 'r') as f:
            first_line = f.readline()
            sample = json.loads(first_line)
            print(f"Model: {sample['body']['model']}")
            print(f"Max tokens: {sample['body']['max_tokens']}")
            print(f"Temperature: {sample['body'].get('temperature', 'Not set')}")
            print(f"Custom ID format: {sample['custom_id']}")
    
    return valid_requests == total_requests and len(errors) == 0

if __name__ == "__main__":
    filename = "batch_evaluation_requests.jsonl"
    is_valid = validate_batch_file(filename)
    
    if is_valid:
        print(f"\n🎉 {filename} is ready for OpenAI Batch API submission!")
        sys.exit(0)
    else:
        print(f"\n❌ {filename} has validation errors. Please fix before submission.")
        sys.exit(1)

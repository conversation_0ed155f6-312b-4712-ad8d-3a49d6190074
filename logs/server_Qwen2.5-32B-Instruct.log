[2025-07-28 00:41:04] server_args=ServerArgs(model_path='/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-32B-Instruct', tokenizer_path='/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-32B-Instruct', tokenizer_mode='auto', skip_tokenizer_init=False, load_format='auto', model_loader_extra_config='{}', trust_remote_code=False, context_length=32768, is_embedding=False, enable_multimodal=None, revision=None, model_impl='auto', host='127.0.0.1', port=8004, skip_server_warmup=False, warmups=None, nccl_port=None, dtype='auto', quantization=None, quantization_param_path=None, kv_cache_dtype='auto', mem_fraction_static=0.85, max_running_requests=None, max_total_tokens=None, chunked_prefill_size=8192, max_prefill_tokens=16384, schedule_policy='fcfs', schedule_conservativeness=1.0, cpu_offload_gb=0, page_size=1, hybrid_kvcache_ratio=None, swa_full_tokens_ratio=0.8, disable_hybrid_swa_memory=False, device='cuda', tp_size=4, pp_size=1, max_micro_batch_size=None, stream_interval=1, stream_output=False, random_seed=663328706, constrained_json_whitespace_pattern=None, watchdog_timeout=300, dist_timeout=None, download_dir=None, base_gpu_id=0, gpu_id_step=1, sleep_on_idle=False, log_level='info', log_level_http=None, log_requests=False, log_requests_level=0, crash_dump_folder=None, show_time_cost=True, enable_metrics=True, enable_metrics_for_all_schedulers=False, bucket_time_to_first_token=None, bucket_inter_token_latency=None, bucket_e2e_request_latency=None, collect_tokens_histogram=False, decode_log_interval=40, enable_request_time_stats_logging=False, kv_events_config=None, api_key=None, served_model_name='/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-32B-Instruct', chat_template=None, completion_template=None, file_storage_path='sglang_storage', enable_cache_report=False, reasoning_parser=None, tool_call_parser=None, dp_size=2, load_balance_method='round_robin', dist_init_addr=None, nnodes=1, node_rank=0, json_model_override_args='{}', preferred_sampling_params=None, enable_lora=None, max_lora_rank=None, lora_target_modules=None, lora_paths=None, max_loras_per_batch=8, lora_backend='triton', attention_backend=None, sampling_backend='flashinfer', grammar_backend='xgrammar', mm_attention_backend=None, speculative_algorithm=None, speculative_draft_model_path=None, speculative_num_steps=None, speculative_eagle_topk=None, speculative_num_draft_tokens=None, speculative_accept_threshold_single=1.0, speculative_accept_threshold_acc=1.0, speculative_token_map=None, ep_size=1, enable_ep_moe=False, enable_deepep_moe=False, enable_flashinfer_moe=False, enable_flashinfer_allreduce_fusion=False, deepep_mode='auto', ep_num_redundant_experts=0, ep_dispatch_algorithm='static', init_expert_location='trivial', enable_eplb=False, eplb_algorithm='auto', eplb_rebalance_num_iterations=1000, eplb_rebalance_layers_per_chunk=None, expert_distribution_recorder_mode=None, expert_distribution_recorder_buffer_size=1000, enable_expert_distribution_metrics=False, deepep_config=None, moe_dense_tp_size=None, enable_hierarchical_cache=False, hicache_ratio=2.0, hicache_size=0, hicache_write_policy='write_through_selective', hicache_io_backend='', hicache_storage_backend=None, enable_double_sparsity=False, ds_channel_config_path=None, ds_heavy_channel_num=32, ds_heavy_token_num=256, ds_heavy_channel_type='qk', ds_sparse_decode_threshold=4096, disable_radix_cache=False, cuda_graph_max_bs=None, cuda_graph_bs=None, disable_cuda_graph=False, disable_cuda_graph_padding=False, enable_profile_cuda_graph=False, enable_nccl_nvls=False, enable_tokenizer_batch_encode=False, disable_outlines_disk_cache=False, disable_custom_all_reduce=False, enable_mscclpp=False, disable_overlap_schedule=False, enable_mixed_chunk=False, enable_dp_attention=False, enable_dp_lm_head=False, enable_two_batch_overlap=False, enable_torch_compile=False, torch_compile_max_bs=32, torchao_config='', enable_nan_detection=False, enable_p2p_check=False, triton_attention_reduce_in_fp32=False, triton_attention_num_kv_splits=8, num_continuous_decode_steps=1, delete_ckpt_after_loading=False, enable_memory_saver=False, allow_auto_truncate=False, enable_custom_logit_processor=False, flashinfer_mla_disable_ragged=False, disable_shared_experts_fusion=False, disable_chunked_prefix_cache=False, disable_fast_image_processor=False, enable_return_hidden_states=False, enable_triton_kernel_moe=False, debug_tensor_dump_output_folder=None, debug_tensor_dump_input_file=None, debug_tensor_dump_inject=False, debug_tensor_dump_prefill_only=False, disaggregation_mode='null', disaggregation_transfer_backend='mooncake', disaggregation_bootstrap_port=8998, disaggregation_decode_tp=None, disaggregation_decode_dp=None, disaggregation_prefill_pp=1, disaggregation_ib_device=None, num_reserved_decode_tokens=512, pdlb_url=None, custom_weight_loader=[], weight_loader_disable_mmap=False, enable_pdmux=False, sm_group_num=3)
[2025-07-28 00:41:10] Launch DP0 starting at GPU #0.
[2025-07-28 00:41:10] Launch DP1 starting at GPU #4.
[2025-07-28 00:41:17 DP1 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 00:41:17 DP1 TP0] Init torch distributed begin.
[2025-07-28 00:41:17 DP0 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 00:41:17 DP0 TP0] Init torch distributed begin.
[2025-07-28 00:41:19 DP0 TP0] sglang is using nccl==2.26.2
[2025-07-28 00:41:19 DP1 TP0] sglang is using nccl==2.26.2
[2025-07-28 00:41:20 DP1 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 00:41:20 DP0 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 00:41:20 DP1 TP0] Load weight begin. avail mem=78.21 GB
[2025-07-28 00:41:20 DP0 TP0] Load weight begin. avail mem=78.21 GB

Loading safetensors checkpoint shards:   0% Completed | 0/17 [00:00<?, ?it/s]

Loading safetensors checkpoint shards:   0% Completed | 0/17 [00:00<?, ?it/s]

Loading safetensors checkpoint shards:   6% Completed | 1/17 [00:01<00:24,  1.55s/it]

Loading safetensors checkpoint shards:   6% Completed | 1/17 [00:01<00:24,  1.51s/it]

Loading safetensors checkpoint shards:  12% Completed | 2/17 [00:03<00:23,  1.54s/it]

Loading safetensors checkpoint shards:  12% Completed | 2/17 [00:03<00:23,  1.55s/it]

Loading safetensors checkpoint shards:  18% Completed | 3/17 [00:04<00:22,  1.58s/it]

Loading safetensors checkpoint shards:  18% Completed | 3/17 [00:04<00:21,  1.57s/it]

Loading safetensors checkpoint shards:  24% Completed | 4/17 [00:05<00:17,  1.37s/it]

Loading safetensors checkpoint shards:  24% Completed | 4/17 [00:05<00:17,  1.37s/it]

Loading safetensors checkpoint shards:  29% Completed | 5/17 [00:07<00:16,  1.34s/it]

Loading safetensors checkpoint shards:  29% Completed | 5/17 [00:07<00:16,  1.34s/it]

Loading safetensors checkpoint shards:  35% Completed | 6/17 [00:08<00:15,  1.41s/it]

Loading safetensors checkpoint shards:  35% Completed | 6/17 [00:08<00:15,  1.40s/it]

Loading safetensors checkpoint shards:  41% Completed | 7/17 [00:10<00:14,  1.46s/it]

Loading safetensors checkpoint shards:  41% Completed | 7/17 [00:10<00:14,  1.46s/it]

Loading safetensors checkpoint shards:  47% Completed | 8/17 [00:11<00:13,  1.48s/it]

Loading safetensors checkpoint shards:  47% Completed | 8/17 [00:11<00:13,  1.48s/it]

Loading safetensors checkpoint shards:  53% Completed | 9/17 [00:13<00:12,  1.51s/it]

Loading safetensors checkpoint shards:  53% Completed | 9/17 [00:13<00:12,  1.51s/it]

Loading safetensors checkpoint shards:  59% Completed | 10/17 [00:14<00:10,  1.52s/it]

Loading safetensors checkpoint shards:  59% Completed | 10/17 [00:14<00:10,  1.52s/it]

Loading safetensors checkpoint shards:  65% Completed | 11/17 [00:16<00:09,  1.53s/it]

Loading safetensors checkpoint shards:  65% Completed | 11/17 [00:16<00:09,  1.53s/it]

Loading safetensors checkpoint shards:  71% Completed | 12/17 [00:17<00:07,  1.54s/it]

Loading safetensors checkpoint shards:  71% Completed | 12/17 [00:17<00:07,  1.54s/it]

Loading safetensors checkpoint shards:  76% Completed | 13/17 [00:19<00:06,  1.54s/it]

Loading safetensors checkpoint shards:  76% Completed | 13/17 [00:19<00:06,  1.54s/it]

Loading safetensors checkpoint shards:  82% Completed | 14/17 [00:21<00:04,  1.55s/it]

Loading safetensors checkpoint shards:  82% Completed | 14/17 [00:21<00:04,  1.55s/it]

Loading safetensors checkpoint shards:  88% Completed | 15/17 [00:22<00:03,  1.52s/it]

Loading safetensors checkpoint shards:  88% Completed | 15/17 [00:22<00:03,  1.52s/it]

Loading safetensors checkpoint shards:  94% Completed | 16/17 [00:23<00:01,  1.52s/it]

Loading safetensors checkpoint shards:  94% Completed | 16/17 [00:23<00:01,  1.52s/it]

Loading safetensors checkpoint shards: 100% Completed | 17/17 [00:25<00:00,  1.53s/it]

Loading safetensors checkpoint shards: 100% Completed | 17/17 [00:25<00:00,  1.53s/it]

Loading safetensors checkpoint shards: 100% Completed | 17/17 [00:25<00:00,  1.50s/it]


Loading safetensors checkpoint shards: 100% Completed | 17/17 [00:25<00:00,  1.50s/it]

[2025-07-28 00:41:46 DP0 TP0] Load weight end. type=Qwen2ForCausalLM, dtype=torch.bfloat16, avail mem=62.71 GB, mem usage=15.49 GB.
[2025-07-28 00:41:46 DP1 TP0] Load weight end. type=Qwen2ForCausalLM, dtype=torch.bfloat16, avail mem=62.71 GB, mem usage=15.49 GB.
[2025-07-28 00:41:47 DP1 TP2] KV Cache is allocated. #tokens: 833962, K size: 25.45 GB, V size: 25.45 GB
[2025-07-28 00:41:47 DP0 TP2] KV Cache is allocated. #tokens: 833962, K size: 25.45 GB, V size: 25.45 GB
[2025-07-28 00:41:47 DP1 TP0] KV Cache is allocated. #tokens: 833962, K size: 25.45 GB, V size: 25.45 GB
[2025-07-28 00:41:47 DP1 TP0] Memory pool end. avail mem=11.21 GB
[2025-07-28 00:41:47 DP0 TP3] KV Cache is allocated. #tokens: 833962, K size: 25.45 GB, V size: 25.45 GB
[2025-07-28 00:41:47 DP0 TP0] KV Cache is allocated. #tokens: 833962, K size: 25.45 GB, V size: 25.45 GB
[2025-07-28 00:41:47 DP0 TP0] Memory pool end. avail mem=11.21 GB
[2025-07-28 00:41:47 DP0 TP1] KV Cache is allocated. #tokens: 833962, K size: 25.45 GB, V size: 25.45 GB
[2025-07-28 00:41:47 DP1 TP1] KV Cache is allocated. #tokens: 833962, K size: 25.45 GB, V size: 25.45 GB
[2025-07-28 00:41:47 DP1 TP3] KV Cache is allocated. #tokens: 833962, K size: 25.45 GB, V size: 25.45 GB
[2025-07-28 00:41:47 DP0 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.57 GB
[2025-07-28 00:41:47 DP1 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.57 GB
[2025-07-28 00:41:47 DP0 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]
[2025-07-28 00:41:47 DP1 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.55 GB):   0%|          | 0/23 [00:00<?, ?it/s]
  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.55 GB):   0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.55 GB):   4%|▍         | 1/23 [00:01<00:28,  1.28s/it]
Capturing batches (bs=152 avail_mem=10.29 GB):   4%|▍         | 1/23 [00:01<00:28,  1.28s/it]
Capturing batches (bs=160 avail_mem=10.55 GB):   4%|▍         | 1/23 [00:01<00:28,  1.30s/it]
Capturing batches (bs=152 avail_mem=10.29 GB):   4%|▍         | 1/23 [00:01<00:28,  1.30s/it]
Capturing batches (bs=152 avail_mem=10.29 GB):   9%|▊         | 2/23 [00:01<00:15,  1.36it/s]
Capturing batches (bs=144 avail_mem=10.17 GB):   9%|▊         | 2/23 [00:01<00:15,  1.36it/s]
Capturing batches (bs=152 avail_mem=10.29 GB):   9%|▊         | 2/23 [00:01<00:16,  1.27it/s]
Capturing batches (bs=144 avail_mem=10.17 GB):   9%|▊         | 2/23 [00:01<00:16,  1.27it/s]
Capturing batches (bs=144 avail_mem=10.17 GB):  13%|█▎        | 3/23 [00:02<00:12,  1.58it/s]
Capturing batches (bs=136 avail_mem=10.08 GB):  13%|█▎        | 3/23 [00:02<00:12,  1.58it/s]
Capturing batches (bs=144 avail_mem=10.17 GB):  13%|█▎        | 3/23 [00:02<00:13,  1.52it/s]
Capturing batches (bs=136 avail_mem=10.08 GB):  13%|█▎        | 3/23 [00:02<00:13,  1.52it/s]
Capturing batches (bs=136 avail_mem=10.08 GB):  17%|█▋        | 4/23 [00:02<00:09,  1.98it/s]
Capturing batches (bs=128 avail_mem=9.96 GB):  17%|█▋        | 4/23 [00:02<00:09,  1.98it/s] 
Capturing batches (bs=136 avail_mem=10.08 GB):  17%|█▋        | 4/23 [00:02<00:09,  1.93it/s]
Capturing batches (bs=128 avail_mem=9.96 GB):  17%|█▋        | 4/23 [00:02<00:09,  1.93it/s] 
Capturing batches (bs=128 avail_mem=9.96 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.29it/s]
Capturing batches (bs=120 avail_mem=9.87 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.29it/s]
Capturing batches (bs=128 avail_mem=9.96 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.26it/s]
Capturing batches (bs=120 avail_mem=9.87 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.26it/s]
Capturing batches (bs=120 avail_mem=9.87 GB):  26%|██▌       | 6/23 [00:03<00:06,  2.52it/s]
Capturing batches (bs=112 avail_mem=9.77 GB):  26%|██▌       | 6/23 [00:03<00:06,  2.52it/s]
Capturing batches (bs=120 avail_mem=9.87 GB):  26%|██▌       | 6/23 [00:03<00:06,  2.52it/s]
Capturing batches (bs=112 avail_mem=9.77 GB):  26%|██▌       | 6/23 [00:03<00:06,  2.52it/s]
Capturing batches (bs=112 avail_mem=9.77 GB):  30%|███       | 7/23 [00:03<00:05,  2.67it/s]
Capturing batches (bs=104 avail_mem=9.69 GB):  30%|███       | 7/23 [00:03<00:05,  2.67it/s]
Capturing batches (bs=112 avail_mem=9.77 GB):  30%|███       | 7/23 [00:03<00:05,  2.71it/s]
Capturing batches (bs=104 avail_mem=9.69 GB):  30%|███       | 7/23 [00:03<00:05,  2.71it/s]
Capturing batches (bs=104 avail_mem=9.69 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.82it/s]
Capturing batches (bs=96 avail_mem=9.59 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.82it/s] 
Capturing batches (bs=104 avail_mem=9.69 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.84it/s]
Capturing batches (bs=96 avail_mem=9.59 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.84it/s] 
Capturing batches (bs=96 avail_mem=9.59 GB):  39%|███▉      | 9/23 [00:04<00:04,  2.95it/s]
Capturing batches (bs=88 avail_mem=9.52 GB):  39%|███▉      | 9/23 [00:04<00:04,  2.95it/s]
Capturing batches (bs=96 avail_mem=9.59 GB):  39%|███▉      | 9/23 [00:04<00:04,  2.93it/s]
Capturing batches (bs=88 avail_mem=9.52 GB):  39%|███▉      | 9/23 [00:04<00:04,  2.93it/s]
Capturing batches (bs=88 avail_mem=9.52 GB):  43%|████▎     | 10/23 [00:04<00:04,  3.01it/s]
Capturing batches (bs=80 avail_mem=9.43 GB):  43%|████▎     | 10/23 [00:04<00:04,  3.01it/s]
Capturing batches (bs=88 avail_mem=9.52 GB):  43%|████▎     | 10/23 [00:04<00:04,  3.00it/s]
Capturing batches (bs=80 avail_mem=9.43 GB):  43%|████▎     | 10/23 [00:04<00:04,  3.00it/s]
Capturing batches (bs=80 avail_mem=9.43 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.07it/s]
Capturing batches (bs=72 avail_mem=9.42 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.07it/s]
Capturing batches (bs=80 avail_mem=9.43 GB):  48%|████▊     | 11/23 [00:04<00:04,  2.99it/s]
Capturing batches (bs=72 avail_mem=9.42 GB):  48%|████▊     | 11/23 [00:04<00:04,  2.99it/s]
Capturing batches (bs=72 avail_mem=9.42 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.11it/s]
Capturing batches (bs=64 avail_mem=9.34 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.11it/s]
Capturing batches (bs=72 avail_mem=9.42 GB):  52%|█████▏    | 12/23 [00:05<00:03,  2.98it/s]
Capturing batches (bs=64 avail_mem=9.34 GB):  52%|█████▏    | 12/23 [00:05<00:03,  2.98it/s]
Capturing batches (bs=64 avail_mem=9.34 GB):  57%|█████▋    | 13/23 [00:05<00:03,  3.13it/s]
Capturing batches (bs=56 avail_mem=9.29 GB):  57%|█████▋    | 13/23 [00:05<00:03,  3.13it/s]
Capturing batches (bs=64 avail_mem=9.34 GB):  57%|█████▋    | 13/23 [00:05<00:03,  2.97it/s]
Capturing batches (bs=56 avail_mem=9.29 GB):  57%|█████▋    | 13/23 [00:05<00:03,  2.97it/s]
Capturing batches (bs=56 avail_mem=9.29 GB):  61%|██████    | 14/23 [00:05<00:02,  3.14it/s]
Capturing batches (bs=48 avail_mem=9.23 GB):  61%|██████    | 14/23 [00:05<00:02,  3.14it/s]
Capturing batches (bs=56 avail_mem=9.29 GB):  61%|██████    | 14/23 [00:05<00:02,  3.03it/s]
Capturing batches (bs=48 avail_mem=9.23 GB):  61%|██████    | 14/23 [00:05<00:02,  3.03it/s]
Capturing batches (bs=48 avail_mem=9.23 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.17it/s]
Capturing batches (bs=40 avail_mem=9.22 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.17it/s]
Capturing batches (bs=48 avail_mem=9.23 GB):  65%|██████▌   | 15/23 [00:06<00:02,  3.06it/s]
Capturing batches (bs=40 avail_mem=9.22 GB):  65%|██████▌   | 15/23 [00:06<00:02,  3.06it/s]
Capturing batches (bs=40 avail_mem=9.22 GB):  70%|██████▉   | 16/23 [00:06<00:02,  3.12it/s]
Capturing batches (bs=32 avail_mem=9.17 GB):  70%|██████▉   | 16/23 [00:06<00:02,  3.12it/s]
Capturing batches (bs=40 avail_mem=9.22 GB):  70%|██████▉   | 16/23 [00:06<00:02,  3.08it/s]
Capturing batches (bs=32 avail_mem=9.17 GB):  70%|██████▉   | 16/23 [00:06<00:02,  3.08it/s]
Capturing batches (bs=32 avail_mem=9.17 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.17it/s]
Capturing batches (bs=24 avail_mem=9.15 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.17it/s]
Capturing batches (bs=32 avail_mem=9.17 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.12it/s]
Capturing batches (bs=24 avail_mem=9.15 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.12it/s]
Capturing batches (bs=24 avail_mem=9.15 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.14it/s]
Capturing batches (bs=16 avail_mem=9.11 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.14it/s]
Capturing batches (bs=24 avail_mem=9.15 GB):  78%|███████▊  | 18/23 [00:07<00:01,  3.15it/s]
Capturing batches (bs=16 avail_mem=9.11 GB):  78%|███████▊  | 18/23 [00:07<00:01,  3.15it/s]
Capturing batches (bs=16 avail_mem=9.11 GB):  83%|████████▎ | 19/23 [00:07<00:01,  3.19it/s]
Capturing batches (bs=8 avail_mem=9.10 GB):  83%|████████▎ | 19/23 [00:07<00:01,  3.19it/s] 
Capturing batches (bs=16 avail_mem=9.11 GB):  83%|████████▎ | 19/23 [00:07<00:01,  3.15it/s]
Capturing batches (bs=8 avail_mem=9.10 GB):  83%|████████▎ | 19/23 [00:07<00:01,  3.15it/s] 
Capturing batches (bs=8 avail_mem=9.10 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.22it/s]
Capturing batches (bs=4 avail_mem=9.07 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.22it/s]
Capturing batches (bs=8 avail_mem=9.10 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.16it/s]
Capturing batches (bs=4 avail_mem=9.07 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.16it/s]
Capturing batches (bs=4 avail_mem=9.07 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.26it/s]
Capturing batches (bs=2 avail_mem=9.06 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.26it/s]
Capturing batches (bs=4 avail_mem=9.07 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.18it/s]
Capturing batches (bs=2 avail_mem=9.06 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.18it/s]
Capturing batches (bs=2 avail_mem=9.06 GB):  96%|█████████▌| 22/23 [00:08<00:00,  3.23it/s]
Capturing batches (bs=1 avail_mem=9.03 GB):  96%|█████████▌| 22/23 [00:08<00:00,  3.23it/s]
Capturing batches (bs=2 avail_mem=9.06 GB):  96%|█████████▌| 22/23 [00:08<00:00,  3.16it/s]
Capturing batches (bs=1 avail_mem=9.03 GB):  96%|█████████▌| 22/23 [00:08<00:00,  3.16it/s][2025-07-28 00:41:55 DP0 TP2] Registering 2967 cuda graph addresses
[2025-07-28 00:41:55 DP0 TP3] Registering 2967 cuda graph addresses
[2025-07-28 00:41:55 DP0 TP1] Registering 2967 cuda graph addresses

Capturing batches (bs=1 avail_mem=9.03 GB): 100%|██████████| 23/23 [00:08<00:00,  3.19it/s]
Capturing batches (bs=1 avail_mem=9.03 GB): 100%|██████████| 23/23 [00:08<00:00,  2.73it/s]
[2025-07-28 00:41:55 DP0 TP0] Registering 2967 cuda graph addresses
[2025-07-28 00:41:55 DP0 TP0] Capture cuda graph end. Time elapsed: 8.67 s. mem usage=1.56 GB. avail mem=9.01 GB.
[2025-07-28 00:41:56 DP1 TP2] Registering 2967 cuda graph addresses

Capturing batches (bs=1 avail_mem=9.03 GB): 100%|██████████| 23/23 [00:08<00:00,  3.16it/s]
Capturing batches (bs=1 avail_mem=9.03 GB): 100%|██████████| 23/23 [00:08<00:00,  2.68it/s]
[2025-07-28 00:41:56 DP1 TP0] Registering 2967 cuda graph addresses
[2025-07-28 00:41:56 DP1 TP3] Registering 2967 cuda graph addresses
[2025-07-28 00:41:56 DP1 TP1] Registering 2967 cuda graph addresses
[2025-07-28 00:41:56 DP1 TP0] Capture cuda graph end. Time elapsed: 8.84 s. mem usage=1.56 GB. avail mem=9.01 GB.
[2025-07-28 00:41:56 DP0 TP0] max_total_num_tokens=833962, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=9.01 GB
[2025-07-28 00:41:56 DP1 TP0] max_total_num_tokens=833962, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=9.01 GB
[2025-07-28 00:41:56] INFO:     Started server process [1774729]
[2025-07-28 00:41:56] INFO:     Waiting for application startup.
[2025-07-28 00:41:56] INFO:     Application startup complete.
[2025-07-28 00:41:56] INFO:     Uvicorn running on http://127.0.0.1:8004 (Press CTRL+C to quit)
[2025-07-28 00:41:57] INFO:     127.0.0.1:56536 - "GET /health HTTP/1.1" 200 OK
[2025-07-28 00:41:57 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 256, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:41:57 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 215, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:41:57] INFO:     127.0.0.1:56566 - "GET /get_model_info HTTP/1.1" 200 OK
[2025-07-28 00:41:57 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 0, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:41:58 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 0, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:41:58] INFO:     127.0.0.1:56570 - "POST /generate HTTP/1.1" 200 OK
[2025-07-28 00:41:58] The server is fired up and ready to roll!
[2025-07-28 00:41:58 DP0 TP0] Decode batch. #running-req: 1, #token: 297, token usage: 0.00, cuda graph: True, gen throughput (token/s): 18.25, #queue-req: 0, 
[2025-07-28 00:41:58 DP1 TP0] Decode batch. #running-req: 1, #token: 256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 19.15, #queue-req: 0, 
[2025-07-28 00:41:59 DP0 TP0] Decode batch. #running-req: 1, #token: 337, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.63, #queue-req: 0, 
[2025-07-28 00:41:59 DP1 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.79, #queue-req: 0, 
[2025-07-28 00:41:59 DP0 TP0] Decode batch. #running-req: 1, #token: 377, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 00:41:59 DP1 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.67, #queue-req: 0, 
[2025-07-28 00:42:00 DP0 TP0] Decode batch. #running-req: 1, #token: 417, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 00:42:00 DP1 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.44, #queue-req: 0, 
[2025-07-28 00:42:01 DP0 TP0] Decode batch. #running-req: 1, #token: 457, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 00:42:01 DP1 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.04, #queue-req: 0, 
[2025-07-28 00:42:01 DP0 TP0] Decode batch. #running-req: 1, #token: 497, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 00:42:01 DP1 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 00:42:02 DP0 TP0] Decode batch. #running-req: 1, #token: 537, token usage: 0.00, cuda graph: True, gen throughput (token/s): 67.77, #queue-req: 0, 
[2025-07-28 00:42:02 DP1 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:42:02 DP0 TP0] Decode batch. #running-req: 1, #token: 577, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:42:02 DP1 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.15, #queue-req: 0, 
[2025-07-28 00:42:03 DP0 TP0] Decode batch. #running-req: 1, #token: 617, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 00:42:03 DP1 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:42:04 DP0 TP0] Decode batch. #running-req: 1, #token: 657, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 00:42:04 DP1 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:42:04] INFO:     127.0.0.1:56560 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:42:04 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 106, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:42:04 DP0 TP0] Decode batch. #running-req: 2, #token: 805, token usage: 0.00, cuda graph: True, gen throughput (token/s): 76.18, #queue-req: 0, 
[2025-07-28 00:42:05 DP0 TP0] Decode batch. #running-req: 2, #token: 885, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.27, #queue-req: 0, 
[2025-07-28 00:42:05] INFO:     127.0.0.1:56550 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:42:05 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 207, #cached-token: 107, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:42:06 DP0 TP0] Decode batch. #running-req: 1, #token: 294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 108.25, #queue-req: 0, 
[2025-07-28 00:42:06 DP1 TP0] Decode batch. #running-req: 1, #token: 341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 16.79, #queue-req: 0, 
[2025-07-28 00:42:06 DP0 TP0] Decode batch. #running-req: 1, #token: 334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.65, #queue-req: 0, 
[2025-07-28 00:42:06 DP1 TP0] Decode batch. #running-req: 1, #token: 381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.48, #queue-req: 0, 
[2025-07-28 00:42:07 DP0 TP0] Decode batch. #running-req: 1, #token: 374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.49, #queue-req: 0, 
[2025-07-28 00:42:07 DP1 TP0] Decode batch. #running-req: 1, #token: 421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:42:07 DP0 TP0] Decode batch. #running-req: 1, #token: 414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 00:42:08 DP1 TP0] Decode batch. #running-req: 1, #token: 461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 00:42:08 DP0 TP0] Decode batch. #running-req: 1, #token: 454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 00:42:08 DP1 TP0] Decode batch. #running-req: 1, #token: 501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:42:08 DP0 TP0] Decode batch. #running-req: 1, #token: 494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:42:09 DP1 TP0] Decode batch. #running-req: 1, #token: 541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:42:09 DP0 TP0] Decode batch. #running-req: 1, #token: 534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:42:09 DP1 TP0] Decode batch. #running-req: 1, #token: 581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:42:10 DP0 TP0] Decode batch. #running-req: 1, #token: 574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 00:42:10 DP1 TP0] Decode batch. #running-req: 1, #token: 621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 00:42:10] INFO:     127.0.0.1:41094 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:42:10 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 107, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:42:10 DP0 TP0] Decode batch. #running-req: 1, #token: 242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.43, #queue-req: 0, 
[2025-07-28 00:42:11 DP1 TP0] Decode batch. #running-req: 1, #token: 661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:42:11 DP0 TP0] Decode batch. #running-req: 1, #token: 282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.90, #queue-req: 0, 
[2025-07-28 00:42:11 DP1 TP0] Decode batch. #running-req: 1, #token: 701, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:42:11 DP0 TP0] Decode batch. #running-req: 1, #token: 322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.66, #queue-req: 0, 
[2025-07-28 00:42:12 DP1 TP0] Decode batch. #running-req: 1, #token: 741, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 00:42:12 DP0 TP0] Decode batch. #running-req: 1, #token: 362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.52, #queue-req: 0, 
[2025-07-28 00:42:12 DP1 TP0] Decode batch. #running-req: 1, #token: 781, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:42:12 DP0 TP0] Decode batch. #running-req: 1, #token: 402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 00:42:13 DP1 TP0] Decode batch. #running-req: 1, #token: 821, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 00:42:13 DP0 TP0] Decode batch. #running-req: 1, #token: 442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:42:13 DP1 TP0] Decode batch. #running-req: 1, #token: 861, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:42:14 DP0 TP0] Decode batch. #running-req: 1, #token: 482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:42:14] INFO:     127.0.0.1:41106 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:42:14 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 133, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:42:14 DP1 TP0] Decode batch. #running-req: 1, #token: 256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.56, #queue-req: 0, 
[2025-07-28 00:42:14 DP0 TP0] Decode batch. #running-req: 1, #token: 522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:42:15 DP1 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.70, #queue-req: 0, 
[2025-07-28 00:42:15 DP0 TP0] Decode batch. #running-req: 1, #token: 562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:42:15 DP1 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.71, #queue-req: 0, 
[2025-07-28 00:42:15 DP0 TP0] Decode batch. #running-req: 1, #token: 602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:42:16 DP1 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.57, #queue-req: 0, 
[2025-07-28 00:42:16 DP0 TP0] Decode batch. #running-req: 1, #token: 642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:42:16 DP1 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.41, #queue-req: 0, 
[2025-07-28 00:42:17 DP0 TP0] Decode batch. #running-req: 1, #token: 682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:42:17] INFO:     127.0.0.1:41122 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:42:17 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 186, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:42:17 DP1 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 00:42:17 DP0 TP0] Decode batch. #running-req: 1, #token: 319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.58, #queue-req: 0, 
[2025-07-28 00:42:18 DP1 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 00:42:18 DP0 TP0] Decode batch. #running-req: 1, #token: 359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.58, #queue-req: 0, 
[2025-07-28 00:42:18 DP1 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:42:18 DP0 TP0] Decode batch. #running-req: 1, #token: 399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.42, #queue-req: 0, 
[2025-07-28 00:42:19 DP1 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 00:42:19 DP0 TP0] Decode batch. #running-req: 1, #token: 439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 00:42:19 DP1 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 00:42:19 DP0 TP0] Decode batch. #running-req: 1, #token: 479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:42:20 DP1 TP0] Decode batch. #running-req: 1, #token: 656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 00:42:20 DP0 TP0] Decode batch. #running-req: 1, #token: 519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:42:20 DP1 TP0] Decode batch. #running-req: 1, #token: 696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 00:42:21 DP0 TP0] Decode batch. #running-req: 1, #token: 559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:42:21 DP1 TP0] Decode batch. #running-req: 1, #token: 736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 00:42:21 DP0 TP0] Decode batch. #running-req: 1, #token: 599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:42:22 DP1 TP0] Decode batch. #running-req: 1, #token: 776, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 00:42:22 DP0 TP0] Decode batch. #running-req: 1, #token: 639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 00:42:22] INFO:     127.0.0.1:56252 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:42:22 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 173, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:42:22 DP1 TP0] Decode batch. #running-req: 1, #token: 303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 62.41, #queue-req: 0, 
[2025-07-28 00:42:22 DP0 TP0] Decode batch. #running-req: 1, #token: 679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:42:23 DP1 TP0] Decode batch. #running-req: 1, #token: 343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.69, #queue-req: 0, 
[2025-07-28 00:42:23 DP0 TP0] Decode batch. #running-req: 1, #token: 719, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:42:23] INFO:     127.0.0.1:56266 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:42:23 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 226, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:42:23 DP1 TP0] Decode batch. #running-req: 1, #token: 383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.47, #queue-req: 0, 
[2025-07-28 00:42:24 DP0 TP0] Decode batch. #running-req: 1, #token: 368, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.33, #queue-req: 0, 
[2025-07-28 00:42:24 DP1 TP0] Decode batch. #running-req: 1, #token: 423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 00:42:24 DP0 TP0] Decode batch. #running-req: 1, #token: 408, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 00:42:24 DP1 TP0] Decode batch. #running-req: 1, #token: 463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 00:42:25 DP0 TP0] Decode batch. #running-req: 1, #token: 448, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 00:42:25 DP1 TP0] Decode batch. #running-req: 1, #token: 503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.15, #queue-req: 0, 
[2025-07-28 00:42:25 DP0 TP0] Decode batch. #running-req: 1, #token: 488, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:42:26 DP1 TP0] Decode batch. #running-req: 1, #token: 543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 00:42:26 DP0 TP0] Decode batch. #running-req: 1, #token: 528, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 00:42:26 DP1 TP0] Decode batch. #running-req: 1, #token: 583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 00:42:26 DP0 TP0] Decode batch. #running-req: 1, #token: 568, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:42:27 DP1 TP0] Decode batch. #running-req: 1, #token: 623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 00:42:27 DP0 TP0] Decode batch. #running-req: 1, #token: 608, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:42:27 DP1 TP0] Decode batch. #running-req: 1, #token: 663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 00:42:28 DP0 TP0] Decode batch. #running-req: 1, #token: 648, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:42:28 DP1 TP0] Decode batch. #running-req: 1, #token: 703, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.17, #queue-req: 0, 
[2025-07-28 00:42:28 DP0 TP0] Decode batch. #running-req: 1, #token: 688, token usage: 0.00, cuda graph: True, gen throughput (token/s): 67.76, #queue-req: 0, 
[2025-07-28 00:42:29 DP1 TP0] Decode batch. #running-req: 1, #token: 743, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 00:42:29 DP0 TP0] Decode batch. #running-req: 1, #token: 728, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:42:29 DP1 TP0] Decode batch. #running-req: 1, #token: 783, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:42:29 DP0 TP0] Decode batch. #running-req: 1, #token: 768, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 00:42:30 DP1 TP0] Decode batch. #running-req: 1, #token: 823, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:42:30 DP0 TP0] Decode batch. #running-req: 1, #token: 808, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 00:42:30 DP1 TP0] Decode batch. #running-req: 1, #token: 863, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 00:42:30 DP0 TP0] Decode batch. #running-req: 1, #token: 848, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 00:42:31 DP1 TP0] Decode batch. #running-req: 1, #token: 903, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 00:42:31 DP0 TP0] Decode batch. #running-req: 1, #token: 888, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 00:42:31 DP1 TP0] Decode batch. #running-req: 1, #token: 943, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 00:42:32 DP0 TP0] Decode batch. #running-req: 1, #token: 928, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 00:42:32] INFO:     127.0.0.1:38196 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:42:32 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 168, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:42:32 DP1 TP0] Decode batch. #running-req: 1, #token: 299, token usage: 0.00, cuda graph: True, gen throughput (token/s): 62.80, #queue-req: 0, 
[2025-07-28 00:42:32 DP0 TP0] Decode batch. #running-req: 1, #token: 968, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 00:42:32] INFO:     127.0.0.1:38204 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:42:32 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 154, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:42:33 DP1 TP0] Decode batch. #running-req: 1, #token: 339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.76, #queue-req: 0, 
[2025-07-28 00:42:33 DP0 TP0] Decode batch. #running-req: 1, #token: 289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.62, #queue-req: 0, 
[2025-07-28 00:42:33 DP1 TP0] Decode batch. #running-req: 1, #token: 379, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.57, #queue-req: 0, 
[2025-07-28 00:42:33 DP0 TP0] Decode batch. #running-req: 1, #token: 329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.64, #queue-req: 0, 
[2025-07-28 00:42:34 DP1 TP0] Decode batch. #running-req: 1, #token: 419, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.41, #queue-req: 0, 
[2025-07-28 00:42:34 DP0 TP0] Decode batch. #running-req: 1, #token: 369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.53, #queue-req: 0, 
[2025-07-28 00:42:34 DP1 TP0] Decode batch. #running-req: 1, #token: 459, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.38, #queue-req: 0, 
[2025-07-28 00:42:35 DP0 TP0] Decode batch. #running-req: 1, #token: 409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.40, #queue-req: 0, 
[2025-07-28 00:42:35 DP1 TP0] Decode batch. #running-req: 1, #token: 499, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.38, #queue-req: 0, 
[2025-07-28 00:42:35 DP0 TP0] Decode batch. #running-req: 1, #token: 449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 00:42:36 DP1 TP0] Decode batch. #running-req: 1, #token: 539, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 00:42:36 DP0 TP0] Decode batch. #running-req: 1, #token: 489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:42:36 DP1 TP0] Decode batch. #running-req: 1, #token: 579, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 00:42:36 DP0 TP0] Decode batch. #running-req: 1, #token: 529, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:42:37 DP1 TP0] Decode batch. #running-req: 1, #token: 619, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 00:42:37 DP0 TP0] Decode batch. #running-req: 1, #token: 569, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:42:37 DP1 TP0] Decode batch. #running-req: 1, #token: 659, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 00:42:37 DP0 TP0] Decode batch. #running-req: 1, #token: 609, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 00:42:38 DP1 TP0] Decode batch. #running-req: 1, #token: 699, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:42:38 DP0 TP0] Decode batch. #running-req: 1, #token: 649, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:42:38 DP1 TP0] Decode batch. #running-req: 1, #token: 739, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 00:42:39 DP0 TP0] Decode batch. #running-req: 1, #token: 689, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:42:39 DP1 TP0] Decode batch. #running-req: 1, #token: 779, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 00:42:39 DP0 TP0] Decode batch. #running-req: 1, #token: 729, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:42:40 DP1 TP0] Decode batch. #running-req: 1, #token: 819, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:42:40] INFO:     127.0.0.1:39726 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:42:40 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 141, #cached-token: 106, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:42:40] INFO:     127.0.0.1:38208 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:42:40 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 192, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:42:40 DP0 TP0] Decode batch. #running-req: 1, #token: 311, token usage: 0.00, cuda graph: True, gen throughput (token/s): 47.69, #queue-req: 0, 
[2025-07-28 00:42:40 DP1 TP0] Decode batch. #running-req: 1, #token: 281, token usage: 0.00, cuda graph: True, gen throughput (token/s): 75.97, #queue-req: 0, 
[2025-07-28 00:42:41 DP0 TP0] Decode batch. #running-req: 1, #token: 351, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.62, #queue-req: 0, 
[2025-07-28 00:42:41 DP1 TP0] Decode batch. #running-req: 1, #token: 321, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.76, #queue-req: 0, 
[2025-07-28 00:42:41 DP0 TP0] Decode batch. #running-req: 1, #token: 391, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.50, #queue-req: 0, 
[2025-07-28 00:42:41 DP1 TP0] Decode batch. #running-req: 1, #token: 361, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.66, #queue-req: 0, 
[2025-07-28 00:42:42 DP0 TP0] Decode batch. #running-req: 1, #token: 431, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:42:42 DP1 TP0] Decode batch. #running-req: 1, #token: 401, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.50, #queue-req: 0, 
[2025-07-28 00:42:42 DP0 TP0] Decode batch. #running-req: 1, #token: 471, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:42:42 DP1 TP0] Decode batch. #running-req: 1, #token: 441, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 00:42:43 DP0 TP0] Decode batch. #running-req: 1, #token: 511, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:42:43 DP1 TP0] Decode batch. #running-req: 1, #token: 481, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:42:43 DP0 TP0] Decode batch. #running-req: 1, #token: 551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:42:44 DP1 TP0] Decode batch. #running-req: 1, #token: 521, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 00:42:44 DP0 TP0] Decode batch. #running-req: 1, #token: 591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:42:44 DP1 TP0] Decode batch. #running-req: 1, #token: 561, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:42:45 DP0 TP0] Decode batch. #running-req: 1, #token: 631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:42:45 DP1 TP0] Decode batch. #running-req: 1, #token: 601, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:42:45 DP0 TP0] Decode batch. #running-req: 1, #token: 671, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:42:45 DP1 TP0] Decode batch. #running-req: 1, #token: 641, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 00:42:46] INFO:     127.0.0.1:39730 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:42:46 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 190, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:42:46 DP0 TP0] Decode batch. #running-req: 1, #token: 711, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 00:42:46 DP1 TP0] Decode batch. #running-req: 1, #token: 322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.43, #queue-req: 0, 
[2025-07-28 00:42:46 DP0 TP0] Decode batch. #running-req: 1, #token: 751, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:42:47 DP1 TP0] Decode batch. #running-req: 1, #token: 362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.63, #queue-req: 0, 
[2025-07-28 00:42:47 DP0 TP0] Decode batch. #running-req: 1, #token: 791, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:42:47 DP1 TP0] Decode batch. #running-req: 1, #token: 402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.40, #queue-req: 0, 
[2025-07-28 00:42:48 DP0 TP0] Decode batch. #running-req: 1, #token: 831, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:42:48 DP1 TP0] Decode batch. #running-req: 1, #token: 442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 00:42:48 DP0 TP0] Decode batch. #running-req: 1, #token: 871, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:42:48 DP1 TP0] Decode batch. #running-req: 1, #token: 482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 00:42:49 DP0 TP0] Decode batch. #running-req: 1, #token: 911, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 00:42:49 DP1 TP0] Decode batch. #running-req: 1, #token: 522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 00:42:49 DP0 TP0] Decode batch. #running-req: 1, #token: 951, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 00:42:49 DP1 TP0] Decode batch. #running-req: 1, #token: 562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 00:42:50] INFO:     127.0.0.1:39736 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:42:50 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 137, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:42:50 DP0 TP0] Decode batch. #running-req: 1, #token: 247, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.42, #queue-req: 0, 
[2025-07-28 00:42:50 DP1 TP0] Decode batch. #running-req: 1, #token: 602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 00:42:50 DP0 TP0] Decode batch. #running-req: 1, #token: 287, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.88, #queue-req: 0, 
[2025-07-28 00:42:51 DP1 TP0] Decode batch. #running-req: 1, #token: 642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 00:42:51 DP0 TP0] Decode batch. #running-req: 1, #token: 327, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.70, #queue-req: 0, 
[2025-07-28 00:42:51 DP1 TP0] Decode batch. #running-req: 1, #token: 682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 00:42:52 DP0 TP0] Decode batch. #running-req: 1, #token: 367, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.55, #queue-req: 0, 
[2025-07-28 00:42:52 DP1 TP0] Decode batch. #running-req: 1, #token: 722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 00:42:52 DP0 TP0] Decode batch. #running-req: 1, #token: 407, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.40, #queue-req: 0, 
[2025-07-28 00:42:52 DP1 TP0] Decode batch. #running-req: 1, #token: 762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 00:42:53 DP0 TP0] Decode batch. #running-req: 1, #token: 447, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:42:53 DP1 TP0] Decode batch. #running-req: 1, #token: 802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 00:42:53 DP0 TP0] Decode batch. #running-req: 1, #token: 487, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 00:42:54 DP1 TP0] Decode batch. #running-req: 1, #token: 842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 00:42:54] INFO:     127.0.0.1:53098 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:42:54 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 132, #cached-token: 110, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:42:54 DP0 TP0] Decode batch. #running-req: 1, #token: 527, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:42:54 DP1 TP0] Decode batch. #running-req: 1, #token: 262, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.75, #queue-req: 0, 
[2025-07-28 00:42:55 DP0 TP0] Decode batch. #running-req: 1, #token: 567, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:42:55 DP1 TP0] Decode batch. #running-req: 1, #token: 302, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.77, #queue-req: 0, 
[2025-07-28 00:42:55 DP0 TP0] Decode batch. #running-req: 1, #token: 607, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:42:55 DP1 TP0] Decode batch. #running-req: 1, #token: 342, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.72, #queue-req: 0, 
[2025-07-28 00:42:56 DP0 TP0] Decode batch. #running-req: 1, #token: 647, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 00:42:56 DP1 TP0] Decode batch. #running-req: 1, #token: 382, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.57, #queue-req: 0, 
[2025-07-28 00:42:56 DP0 TP0] Decode batch. #running-req: 1, #token: 687, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:42:56 DP1 TP0] Decode batch. #running-req: 1, #token: 422, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.38, #queue-req: 0, 
[2025-07-28 00:42:57 DP0 TP0] Decode batch. #running-req: 1, #token: 727, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:42:57 DP1 TP0] Decode batch. #running-req: 1, #token: 462, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 00:42:57] INFO:     127.0.0.1:53104 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:42:57 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 199, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:42:57 DP0 TP0] Decode batch. #running-req: 1, #token: 323, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.40, #queue-req: 0, 
[2025-07-28 00:42:58 DP1 TP0] Decode batch. #running-req: 1, #token: 502, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 00:42:58 DP0 TP0] Decode batch. #running-req: 1, #token: 363, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.59, #queue-req: 0, 
[2025-07-28 00:42:58 DP1 TP0] Decode batch. #running-req: 1, #token: 542, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 00:42:59 DP0 TP0] Decode batch. #running-req: 1, #token: 403, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.40, #queue-req: 0, 
[2025-07-28 00:42:59 DP1 TP0] Decode batch. #running-req: 1, #token: 582, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:42:59 DP0 TP0] Decode batch. #running-req: 1, #token: 443, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:42:59 DP1 TP0] Decode batch. #running-req: 1, #token: 622, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 00:43:00 DP0 TP0] Decode batch. #running-req: 1, #token: 483, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:43:00 DP1 TP0] Decode batch. #running-req: 1, #token: 662, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 00:43:00 DP0 TP0] Decode batch. #running-req: 1, #token: 523, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:43:00 DP1 TP0] Decode batch. #running-req: 1, #token: 702, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 00:43:01] INFO:     127.0.0.1:42702 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:43:01 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 194, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:43:01 DP0 TP0] Decode batch. #running-req: 1, #token: 563, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:43:01 DP1 TP0] Decode batch. #running-req: 1, #token: 313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.36, #queue-req: 0, 
[2025-07-28 00:43:01 DP0 TP0] Decode batch. #running-req: 1, #token: 603, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:43:02 DP1 TP0] Decode batch. #running-req: 1, #token: 353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.71, #queue-req: 0, 
[2025-07-28 00:43:02 DP0 TP0] Decode batch. #running-req: 1, #token: 643, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:43:02 DP1 TP0] Decode batch. #running-req: 1, #token: 393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.54, #queue-req: 0, 
[2025-07-28 00:43:03 DP0 TP0] Decode batch. #running-req: 1, #token: 683, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:43:03 DP1 TP0] Decode batch. #running-req: 1, #token: 433, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:43:03 DP0 TP0] Decode batch. #running-req: 1, #token: 723, token usage: 0.00, cuda graph: True, gen throughput (token/s): 67.82, #queue-req: 0, 
[2025-07-28 00:43:03 DP1 TP0] Decode batch. #running-req: 1, #token: 473, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.40, #queue-req: 0, 
[2025-07-28 00:43:04] INFO:     127.0.0.1:42712 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:43:04 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 167, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:43:04 DP0 TP0] Decode batch. #running-req: 1, #token: 292, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.53, #queue-req: 0, 
[2025-07-28 00:43:04 DP1 TP0] Decode batch. #running-req: 1, #token: 513, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.38, #queue-req: 0, 
[2025-07-28 00:43:04 DP0 TP0] Decode batch. #running-req: 1, #token: 332, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.68, #queue-req: 0, 
[2025-07-28 00:43:05 DP1 TP0] Decode batch. #running-req: 1, #token: 553, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 00:43:05 DP0 TP0] Decode batch. #running-req: 1, #token: 372, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.52, #queue-req: 0, 
[2025-07-28 00:43:05 DP1 TP0] Decode batch. #running-req: 1, #token: 593, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:43:06 DP0 TP0] Decode batch. #running-req: 1, #token: 412, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 00:43:06 DP1 TP0] Decode batch. #running-req: 1, #token: 633, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 00:43:06 DP0 TP0] Decode batch. #running-req: 1, #token: 452, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:43:06 DP1 TP0] Decode batch. #running-req: 1, #token: 673, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:43:07] INFO:     127.0.0.1:42716 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:43:07 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 172, #cached-token: 110, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:43:07 DP0 TP0] Decode batch. #running-req: 1, #token: 492, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:43:07 DP1 TP0] Decode batch. #running-req: 1, #token: 302, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.85, #queue-req: 0, 
[2025-07-28 00:43:07 DP0 TP0] Decode batch. #running-req: 1, #token: 532, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:43:07 DP1 TP0] Decode batch. #running-req: 1, #token: 342, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.72, #queue-req: 0, 
[2025-07-28 00:43:08 DP0 TP0] Decode batch. #running-req: 1, #token: 572, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:43:08 DP1 TP0] Decode batch. #running-req: 1, #token: 382, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.46, #queue-req: 0, 
[2025-07-28 00:43:08 DP0 TP0] Decode batch. #running-req: 1, #token: 612, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:43:09 DP1 TP0] Decode batch. #running-req: 1, #token: 422, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.44, #queue-req: 0, 
[2025-07-28 00:43:09 DP0 TP0] Decode batch. #running-req: 1, #token: 652, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:43:09 DP1 TP0] Decode batch. #running-req: 1, #token: 462, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 00:43:10 DP0 TP0] Decode batch. #running-req: 1, #token: 692, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:43:10 DP1 TP0] Decode batch. #running-req: 1, #token: 502, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:43:10 DP0 TP0] Decode batch. #running-req: 1, #token: 732, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 00:43:10 DP1 TP0] Decode batch. #running-req: 1, #token: 542, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:43:11 DP0 TP0] Decode batch. #running-req: 1, #token: 772, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:43:11 DP1 TP0] Decode batch. #running-req: 1, #token: 582, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 00:43:11 DP0 TP0] Decode batch. #running-req: 1, #token: 812, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:43:12 DP1 TP0] Decode batch. #running-req: 1, #token: 622, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 00:43:12 DP0 TP0] Decode batch. #running-req: 1, #token: 852, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 00:43:12 DP1 TP0] Decode batch. #running-req: 1, #token: 662, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:43:12] INFO:     127.0.0.1:56144 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:43:12 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 219, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:43:13 DP0 TP0] Decode batch. #running-req: 1, #token: 332, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.51, #queue-req: 0, 
[2025-07-28 00:43:13 DP1 TP0] Decode batch. #running-req: 1, #token: 702, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 00:43:13 DP0 TP0] Decode batch. #running-req: 1, #token: 372, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.54, #queue-req: 0, 
[2025-07-28 00:43:13 DP1 TP0] Decode batch. #running-req: 1, #token: 742, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 00:43:14 DP0 TP0] Decode batch. #running-req: 1, #token: 412, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 00:43:14 DP1 TP0] Decode batch. #running-req: 1, #token: 782, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 00:43:14 DP0 TP0] Decode batch. #running-req: 1, #token: 452, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:43:14] INFO:     127.0.0.1:56156 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:43:14 DP1 TP0] Decode batch. #running-req: 1, #token: 0, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 00:43:14 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 155, #cached-token: 111, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:43:15 DP0 TP0] Decode batch. #running-req: 1, #token: 492, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:43:15 DP1 TP0] Decode batch. #running-req: 1, #token: 307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.41, #queue-req: 0, 
[2025-07-28 00:43:15 DP0 TP0] Decode batch. #running-req: 1, #token: 532, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:43:16 DP1 TP0] Decode batch. #running-req: 1, #token: 347, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.62, #queue-req: 0, 
[2025-07-28 00:43:16 DP0 TP0] Decode batch. #running-req: 1, #token: 572, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:43:16 DP1 TP0] Decode batch. #running-req: 1, #token: 387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.42, #queue-req: 0, 
[2025-07-28 00:43:17 DP0 TP0] Decode batch. #running-req: 1, #token: 612, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:43:17 DP1 TP0] Decode batch. #running-req: 1, #token: 427, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:43:17 DP0 TP0] Decode batch. #running-req: 1, #token: 652, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 00:43:17 DP1 TP0] Decode batch. #running-req: 1, #token: 467, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 00:43:18 DP0 TP0] Decode batch. #running-req: 1, #token: 692, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:43:18 DP1 TP0] Decode batch. #running-req: 1, #token: 507, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:43:18 DP0 TP0] Decode batch. #running-req: 1, #token: 732, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 00:43:19 DP1 TP0] Decode batch. #running-req: 1, #token: 547, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 00:43:19 DP0 TP0] Decode batch. #running-req: 1, #token: 772, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:43:19 DP1 TP0] Decode batch. #running-req: 1, #token: 587, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 00:43:20 DP0 TP0] Decode batch. #running-req: 1, #token: 812, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:43:20 DP1 TP0] Decode batch. #running-req: 1, #token: 627, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 00:43:20 DP0 TP0] Decode batch. #running-req: 1, #token: 852, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 00:43:20] INFO:     127.0.0.1:42880 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:43:20 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 175, #cached-token: 110, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:43:20 DP1 TP0] Decode batch. #running-req: 1, #token: 667, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.18, #queue-req: 0, 
[2025-07-28 00:43:21 DP0 TP0] Decode batch. #running-req: 1, #token: 323, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.78, #queue-req: 0, 
[2025-07-28 00:43:21 DP1 TP0] Decode batch. #running-req: 1, #token: 707, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 00:43:21 DP0 TP0] Decode batch. #running-req: 1, #token: 363, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.57, #queue-req: 0, 
[2025-07-28 00:43:21 DP1 TP0] Decode batch. #running-req: 1, #token: 747, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:43:22 DP0 TP0] Decode batch. #running-req: 1, #token: 403, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.40, #queue-req: 0, 
[2025-07-28 00:43:22 DP1 TP0] Decode batch. #running-req: 1, #token: 787, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 00:43:22 DP0 TP0] Decode batch. #running-req: 1, #token: 443, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:43:23 DP1 TP0] Decode batch. #running-req: 1, #token: 827, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 00:43:23 DP0 TP0] Decode batch. #running-req: 1, #token: 483, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:43:23 DP1 TP0] Decode batch. #running-req: 1, #token: 867, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:43:24 DP0 TP0] Decode batch. #running-req: 1, #token: 523, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:43:24 DP1 TP0] Decode batch. #running-req: 1, #token: 907, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 00:43:24] INFO:     127.0.0.1:42888 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:43:24 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 94, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:43:24 DP0 TP0] Decode batch. #running-req: 1, #token: 563, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:43:24 DP1 TP0] Decode batch. #running-req: 1, #token: 237, token usage: 0.00, cuda graph: True, gen throughput (token/s): 64.09, #queue-req: 0, 
[2025-07-28 00:43:25 DP0 TP0] Decode batch. #running-req: 1, #token: 603, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:43:25 DP1 TP0] Decode batch. #running-req: 1, #token: 277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.10, #queue-req: 0, 
[2025-07-28 00:43:25 DP0 TP0] Decode batch. #running-req: 1, #token: 643, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:43:25 DP1 TP0] Decode batch. #running-req: 1, #token: 317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.78, #queue-req: 0, 
[2025-07-28 00:43:26 DP0 TP0] Decode batch. #running-req: 1, #token: 683, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:43:26 DP1 TP0] Decode batch. #running-req: 1, #token: 357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.69, #queue-req: 0, 
[2025-07-28 00:43:26 DP0 TP0] Decode batch. #running-req: 1, #token: 723, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:43:27 DP1 TP0] Decode batch. #running-req: 1, #token: 397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.50, #queue-req: 0, 
[2025-07-28 00:43:27] INFO:     127.0.0.1:42892 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:43:27 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 115, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:43:27 DP0 TP0] Decode batch. #running-req: 1, #token: 243, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.95, #queue-req: 0, 
[2025-07-28 00:43:27 DP1 TP0] Decode batch. #running-req: 1, #token: 437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 00:43:28 DP0 TP0] Decode batch. #running-req: 1, #token: 283, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.91, #queue-req: 0, 
[2025-07-28 00:43:28 DP1 TP0] Decode batch. #running-req: 1, #token: 477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 00:43:28 DP0 TP0] Decode batch. #running-req: 1, #token: 323, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.70, #queue-req: 0, 
[2025-07-28 00:43:28 DP1 TP0] Decode batch. #running-req: 1, #token: 517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:43:29 DP0 TP0] Decode batch. #running-req: 1, #token: 363, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.59, #queue-req: 0, 
[2025-07-28 00:43:29 DP1 TP0] Decode batch. #running-req: 1, #token: 557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 00:43:29 DP0 TP0] Decode batch. #running-req: 1, #token: 403, token usage: 0.00, cuda graph: True, gen throughput (token/s): 67.95, #queue-req: 0, 
[2025-07-28 00:43:30 DP1 TP0] Decode batch. #running-req: 1, #token: 597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 00:43:30 DP0 TP0] Decode batch. #running-req: 1, #token: 443, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 00:43:30 DP1 TP0] Decode batch. #running-req: 1, #token: 637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:43:31 DP0 TP0] Decode batch. #running-req: 1, #token: 483, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 00:43:31 DP1 TP0] Decode batch. #running-req: 1, #token: 677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 00:43:31 DP0 TP0] Decode batch. #running-req: 1, #token: 523, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:43:31 DP1 TP0] Decode batch. #running-req: 1, #token: 717, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 00:43:32] INFO:     127.0.0.1:42664 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:43:32 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 150, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:43:32 DP0 TP0] Decode batch. #running-req: 1, #token: 563, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 00:43:32 DP1 TP0] Decode batch. #running-req: 1, #token: 270, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.57, #queue-req: 0, 
[2025-07-28 00:43:32 DP0 TP0] Decode batch. #running-req: 1, #token: 603, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 00:43:32 DP1 TP0] Decode batch. #running-req: 1, #token: 310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.77, #queue-req: 0, 
[2025-07-28 00:43:33] INFO:     127.0.0.1:42674 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:43:33 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 148, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:43:33 DP0 TP0] Decode batch. #running-req: 1, #token: 280, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.96, #queue-req: 0, 
[2025-07-28 00:43:33 DP1 TP0] Decode batch. #running-req: 1, #token: 350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.72, #queue-req: 0, 
[2025-07-28 00:43:33 DP0 TP0] Decode batch. #running-req: 1, #token: 320, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.68, #queue-req: 0, 
[2025-07-28 00:43:34 DP1 TP0] Decode batch. #running-req: 1, #token: 390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.58, #queue-req: 0, 
[2025-07-28 00:43:34 DP0 TP0] Decode batch. #running-req: 1, #token: 360, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.59, #queue-req: 0, 
[2025-07-28 00:43:34 DP1 TP0] Decode batch. #running-req: 1, #token: 430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.39, #queue-req: 0, 
[2025-07-28 00:43:35 DP0 TP0] Decode batch. #running-req: 1, #token: 400, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.42, #queue-req: 0, 
[2025-07-28 00:43:35 DP1 TP0] Decode batch. #running-req: 1, #token: 470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:43:35 DP0 TP0] Decode batch. #running-req: 1, #token: 440, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:43:35 DP1 TP0] Decode batch. #running-req: 1, #token: 510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:43:36 DP0 TP0] Decode batch. #running-req: 1, #token: 480, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 00:43:36 DP1 TP0] Decode batch. #running-req: 1, #token: 550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:43:36 DP0 TP0] Decode batch. #running-req: 1, #token: 520, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:43:36 DP1 TP0] Decode batch. #running-req: 1, #token: 590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 00:43:37 DP0 TP0] Decode batch. #running-req: 1, #token: 560, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:43:37 DP1 TP0] Decode batch. #running-req: 1, #token: 630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 00:43:38 DP0 TP0] Decode batch. #running-req: 1, #token: 600, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:43:38 DP1 TP0] Decode batch. #running-req: 1, #token: 670, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 00:43:38 DP0 TP0] Decode batch. #running-req: 1, #token: 640, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:43:38 DP1 TP0] Decode batch. #running-req: 1, #token: 710, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 00:43:39 DP0 TP0] Decode batch. #running-req: 1, #token: 680, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 00:43:39 DP1 TP0] Decode batch. #running-req: 1, #token: 750, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:43:39 DP0 TP0] Decode batch. #running-req: 1, #token: 720, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 00:43:39 DP1 TP0] Decode batch. #running-req: 1, #token: 790, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:43:40] INFO:     127.0.0.1:42690 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:43:40 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 160, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:43:40 DP0 TP0] Decode batch. #running-req: 1, #token: 760, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 00:43:40 DP1 TP0] Decode batch. #running-req: 1, #token: 276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.47, #queue-req: 0, 
[2025-07-28 00:43:40 DP0 TP0] Decode batch. #running-req: 1, #token: 800, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:43:41 DP1 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.77, #queue-req: 0, 
[2025-07-28 00:43:41 DP0 TP0] Decode batch. #running-req: 1, #token: 840, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:43:41 DP1 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.72, #queue-req: 0, 
[2025-07-28 00:43:42 DP0 TP0] Decode batch. #running-req: 1, #token: 880, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 00:43:42 DP1 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.56, #queue-req: 0, 
[2025-07-28 00:43:42] INFO:     127.0.0.1:43552 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:43:42 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 114, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:43:42 DP0 TP0] Decode batch. #running-req: 1, #token: 247, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.96, #queue-req: 0, 
[2025-07-28 00:43:42 DP1 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.39, #queue-req: 0, 
[2025-07-28 00:43:43 DP0 TP0] Decode batch. #running-req: 1, #token: 287, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.87, #queue-req: 0, 
[2025-07-28 00:43:43 DP1 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.39, #queue-req: 0, 
[2025-07-28 00:43:43 DP0 TP0] Decode batch. #running-req: 1, #token: 327, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.71, #queue-req: 0, 
[2025-07-28 00:43:43 DP1 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.39, #queue-req: 0, 
[2025-07-28 00:43:44 DP0 TP0] Decode batch. #running-req: 1, #token: 367, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.56, #queue-req: 0, 
[2025-07-28 00:43:44 DP1 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.38, #queue-req: 0, 
[2025-07-28 00:43:45 DP0 TP0] Decode batch. #running-req: 1, #token: 407, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.40, #queue-req: 0, 
[2025-07-28 00:43:45 DP1 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 00:43:45 DP0 TP0] Decode batch. #running-req: 1, #token: 447, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 00:43:45 DP1 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:43:46 DP0 TP0] Decode batch. #running-req: 1, #token: 487, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 00:43:46 DP1 TP0] Decode batch. #running-req: 1, #token: 676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:43:46 DP0 TP0] Decode batch. #running-req: 1, #token: 527, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:43:46 DP1 TP0] Decode batch. #running-req: 1, #token: 716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:43:47 DP0 TP0] Decode batch. #running-req: 1, #token: 567, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:43:47 DP1 TP0] Decode batch. #running-req: 1, #token: 756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:43:47 DP0 TP0] Decode batch. #running-req: 1, #token: 607, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:43:47 DP1 TP0] Decode batch. #running-req: 1, #token: 796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:43:48 DP0 TP0] Decode batch. #running-req: 1, #token: 647, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:43:48 DP1 TP0] Decode batch. #running-req: 1, #token: 836, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:43:48] INFO:     127.0.0.1:40466 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:43:48 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 80, #cached-token: 106, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:43:49 DP1 TP0] Decode batch. #running-req: 2, #token: 979, token usage: 0.00, cuda graph: True, gen throughput (token/s): 96.92, #queue-req: 0, 
[2025-07-28 00:43:49] INFO:     127.0.0.1:43566 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:43:49 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 136, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:43:49 DP1 TP0] Decode batch. #running-req: 1, #token: 249, token usage: 0.00, cuda graph: True, gen throughput (token/s): 111.88, #queue-req: 0, 
[2025-07-28 00:43:49 DP0 TP0] Decode batch. #running-req: 1, #token: 260, token usage: 0.00, cuda graph: True, gen throughput (token/s): 28.36, #queue-req: 0, 
[2025-07-28 00:43:50 DP1 TP0] Decode batch. #running-req: 1, #token: 289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.79, #queue-req: 0, 
[2025-07-28 00:43:50 DP0 TP0] Decode batch. #running-req: 1, #token: 300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.71, #queue-req: 0, 
[2025-07-28 00:43:50 DP1 TP0] Decode batch. #running-req: 1, #token: 329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.68, #queue-req: 0, 
[2025-07-28 00:43:51 DP0 TP0] Decode batch. #running-req: 1, #token: 340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.65, #queue-req: 0, 
[2025-07-28 00:43:51 DP1 TP0] Decode batch. #running-req: 1, #token: 369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.52, #queue-req: 0, 
[2025-07-28 00:43:51 DP0 TP0] Decode batch. #running-req: 1, #token: 380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.49, #queue-req: 0, 
[2025-07-28 00:43:52 DP1 TP0] Decode batch. #running-req: 1, #token: 409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 00:43:52 DP0 TP0] Decode batch. #running-req: 1, #token: 420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 00:43:52 DP1 TP0] Decode batch. #running-req: 1, #token: 449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:43:52 DP0 TP0] Decode batch. #running-req: 1, #token: 460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:43:53 DP1 TP0] Decode batch. #running-req: 1, #token: 489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 00:43:53 DP0 TP0] Decode batch. #running-req: 1, #token: 500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:43:53 DP1 TP0] Decode batch. #running-req: 1, #token: 529, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:43:53 DP0 TP0] Decode batch. #running-req: 1, #token: 540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:43:54 DP1 TP0] Decode batch. #running-req: 1, #token: 569, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 00:43:54 DP0 TP0] Decode batch. #running-req: 1, #token: 580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:43:54 DP1 TP0] Decode batch. #running-req: 1, #token: 609, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:43:55 DP0 TP0] Decode batch. #running-req: 1, #token: 620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:43:55 DP1 TP0] Decode batch. #running-req: 1, #token: 649, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:43:55 DP0 TP0] Decode batch. #running-req: 1, #token: 660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:43:56] INFO:     127.0.0.1:40480 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:43:56 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 137, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:43:56 DP1 TP0] Decode batch. #running-req: 1, #token: 254, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.20, #queue-req: 0, 
[2025-07-28 00:43:56 DP0 TP0] Decode batch. #running-req: 1, #token: 700, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 00:43:56 DP1 TP0] Decode batch. #running-req: 1, #token: 294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.73, #queue-req: 0, 
[2025-07-28 00:43:56 DP0 TP0] Decode batch. #running-req: 1, #token: 740, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 00:43:56] INFO:     127.0.0.1:40484 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:43:56 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 85, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:43:57 DP1 TP0] Decode batch. #running-req: 1, #token: 334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.68, #queue-req: 0, 
[2025-07-28 00:43:57 DP0 TP0] Decode batch. #running-req: 1, #token: 220, token usage: 0.00, cuda graph: True, gen throughput (token/s): 64.26, #queue-req: 0, 
[2025-07-28 00:43:57 DP1 TP0] Decode batch. #running-req: 1, #token: 374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.50, #queue-req: 0, 
[2025-07-28 00:43:57 DP0 TP0] Decode batch. #running-req: 1, #token: 260, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.27, #queue-req: 0, 
[2025-07-28 00:43:58 DP1 TP0] Decode batch. #running-req: 1, #token: 414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 00:43:58 DP0 TP0] Decode batch. #running-req: 1, #token: 300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.73, #queue-req: 0, 
[2025-07-28 00:43:59 DP1 TP0] Decode batch. #running-req: 1, #token: 454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 00:43:59 DP0 TP0] Decode batch. #running-req: 1, #token: 340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.70, #queue-req: 0, 
[2025-07-28 00:43:59 DP1 TP0] Decode batch. #running-req: 1, #token: 494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 00:43:59 DP0 TP0] Decode batch. #running-req: 1, #token: 380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.53, #queue-req: 0, 
[2025-07-28 00:44:00 DP1 TP0] Decode batch. #running-req: 1, #token: 534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:44:00 DP0 TP0] Decode batch. #running-req: 1, #token: 420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 00:44:00 DP1 TP0] Decode batch. #running-req: 1, #token: 574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:44:00 DP0 TP0] Decode batch. #running-req: 1, #token: 460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 00:44:01 DP1 TP0] Decode batch. #running-req: 1, #token: 614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 00:44:01 DP0 TP0] Decode batch. #running-req: 1, #token: 500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:44:01 DP1 TP0] Decode batch. #running-req: 1, #token: 654, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:44:02 DP0 TP0] Decode batch. #running-req: 1, #token: 540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:44:02 DP1 TP0] Decode batch. #running-req: 1, #token: 694, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:44:02 DP0 TP0] Decode batch. #running-req: 1, #token: 580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:44:02] INFO:     127.0.0.1:53744 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:44:02 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 157, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:44:03 DP1 TP0] Decode batch. #running-req: 1, #token: 286, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.65, #queue-req: 0, 
[2025-07-28 00:44:03 DP0 TP0] Decode batch. #running-req: 1, #token: 620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:44:03 DP1 TP0] Decode batch. #running-req: 1, #token: 326, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.67, #queue-req: 0, 
[2025-07-28 00:44:03 DP0 TP0] Decode batch. #running-req: 1, #token: 660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:44:03] INFO:     127.0.0.1:53750 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:44:03 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 150, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:44:04 DP1 TP0] Decode batch. #running-req: 1, #token: 366, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.56, #queue-req: 0, 
[2025-07-28 00:44:04 DP0 TP0] Decode batch. #running-req: 1, #token: 286, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.91, #queue-req: 0, 
[2025-07-28 00:44:04 DP1 TP0] Decode batch. #running-req: 1, #token: 406, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.39, #queue-req: 0, 
[2025-07-28 00:44:04 DP0 TP0] Decode batch. #running-req: 1, #token: 326, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.15, #queue-req: 0, 
[2025-07-28 00:44:05 DP1 TP0] Decode batch. #running-req: 1, #token: 446, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:44:05 DP0 TP0] Decode batch. #running-req: 1, #token: 366, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.57, #queue-req: 0, 
[2025-07-28 00:44:06 DP1 TP0] Decode batch. #running-req: 1, #token: 486, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:44:06 DP0 TP0] Decode batch. #running-req: 1, #token: 406, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.38, #queue-req: 0, 
[2025-07-28 00:44:06 DP1 TP0] Decode batch. #running-req: 1, #token: 526, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:44:06 DP0 TP0] Decode batch. #running-req: 1, #token: 446, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:44:07 DP1 TP0] Decode batch. #running-req: 1, #token: 566, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 00:44:07 DP0 TP0] Decode batch. #running-req: 1, #token: 486, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:44:07 DP1 TP0] Decode batch. #running-req: 1, #token: 606, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 00:44:07 DP0 TP0] Decode batch. #running-req: 1, #token: 526, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:44:08] INFO:     127.0.0.1:56762 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:44:08 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 111, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:44:08 DP1 TP0] Decode batch. #running-req: 1, #token: 225, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.57, #queue-req: 0, 
[2025-07-28 00:44:08 DP0 TP0] Decode batch. #running-req: 1, #token: 566, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:44:08 DP1 TP0] Decode batch. #running-req: 1, #token: 265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.13, #queue-req: 0, 
[2025-07-28 00:44:09 DP0 TP0] Decode batch. #running-req: 1, #token: 606, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:44:09 DP1 TP0] Decode batch. #running-req: 1, #token: 305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.67, #queue-req: 0, 
[2025-07-28 00:44:09 DP0 TP0] Decode batch. #running-req: 1, #token: 646, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:44:10 DP1 TP0] Decode batch. #running-req: 1, #token: 345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.58, #queue-req: 0, 
[2025-07-28 00:44:10 DP0 TP0] Decode batch. #running-req: 1, #token: 686, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 00:44:10 DP1 TP0] Decode batch. #running-req: 1, #token: 385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.44, #queue-req: 0, 
[2025-07-28 00:44:10 DP0 TP0] Decode batch. #running-req: 1, #token: 726, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:44:11] INFO:     127.0.0.1:56772 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:44:11 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 95, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:44:11 DP1 TP0] Decode batch. #running-req: 1, #token: 425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:44:11 DP0 TP0] Decode batch. #running-req: 1, #token: 218, token usage: 0.00, cuda graph: True, gen throughput (token/s): 64.01, #queue-req: 0, 
[2025-07-28 00:44:11 DP1 TP0] Decode batch. #running-req: 1, #token: 465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:44:11 DP0 TP0] Decode batch. #running-req: 1, #token: 258, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.29, #queue-req: 0, 
[2025-07-28 00:44:12 DP1 TP0] Decode batch. #running-req: 1, #token: 505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 00:44:12 DP0 TP0] Decode batch. #running-req: 1, #token: 298, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.75, #queue-req: 0, 
[2025-07-28 00:44:12 DP1 TP0] Decode batch. #running-req: 1, #token: 545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:44:13 DP0 TP0] Decode batch. #running-req: 1, #token: 338, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.69, #queue-req: 0, 
[2025-07-28 00:44:13 DP1 TP0] Decode batch. #running-req: 1, #token: 585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:44:13 DP0 TP0] Decode batch. #running-req: 1, #token: 378, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.52, #queue-req: 0, 
[2025-07-28 00:44:14 DP1 TP0] Decode batch. #running-req: 1, #token: 625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 00:44:14 DP0 TP0] Decode batch. #running-req: 1, #token: 418, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 00:44:14 DP1 TP0] Decode batch. #running-req: 1, #token: 665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 00:44:14 DP0 TP0] Decode batch. #running-req: 1, #token: 458, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 00:44:14] INFO:     127.0.0.1:56780 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:44:14 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 89, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:44:15 DP1 TP0] Decode batch. #running-req: 1, #token: 226, token usage: 0.00, cuda graph: True, gen throughput (token/s): 64.23, #queue-req: 0, 
[2025-07-28 00:44:15 DP0 TP0] Decode batch. #running-req: 1, #token: 498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 00:44:15] INFO:     127.0.0.1:56784 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:44:15 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 115, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:44:15 DP1 TP0] Decode batch. #running-req: 1, #token: 266, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.12, #queue-req: 0, 
[2025-07-28 00:44:16 DP0 TP0] Decode batch. #running-req: 1, #token: 255, token usage: 0.00, cuda graph: True, gen throughput (token/s): 64.54, #queue-req: 0, 
[2025-07-28 00:44:16 DP1 TP0] Decode batch. #running-req: 1, #token: 306, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.78, #queue-req: 0, 
[2025-07-28 00:44:16 DP0 TP0] Decode batch. #running-req: 1, #token: 295, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.77, #queue-req: 0, 
[2025-07-28 00:44:17 DP1 TP0] Decode batch. #running-req: 1, #token: 346, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.72, #queue-req: 0, 
[2025-07-28 00:44:17 DP0 TP0] Decode batch. #running-req: 1, #token: 335, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.70, #queue-req: 0, 
[2025-07-28 00:44:17 DP1 TP0] Decode batch. #running-req: 1, #token: 386, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.56, #queue-req: 0, 
[2025-07-28 00:44:17 DP0 TP0] Decode batch. #running-req: 1, #token: 375, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.53, #queue-req: 0, 
[2025-07-28 00:44:18 DP1 TP0] Decode batch. #running-req: 1, #token: 426, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.38, #queue-req: 0, 
[2025-07-28 00:44:18 DP0 TP0] Decode batch. #running-req: 1, #token: 415, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:44:18 DP1 TP0] Decode batch. #running-req: 1, #token: 466, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.39, #queue-req: 0, 
[2025-07-28 00:44:18 DP0 TP0] Decode batch. #running-req: 1, #token: 455, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 00:44:19 DP1 TP0] Decode batch. #running-req: 1, #token: 506, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.39, #queue-req: 0, 
[2025-07-28 00:44:19 DP0 TP0] Decode batch. #running-req: 1, #token: 495, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 00:44:19 DP1 TP0] Decode batch. #running-req: 1, #token: 546, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 00:44:20 DP0 TP0] Decode batch. #running-req: 1, #token: 535, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:44:20 DP1 TP0] Decode batch. #running-req: 1, #token: 586, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:44:20 DP0 TP0] Decode batch. #running-req: 1, #token: 575, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:44:21 DP1 TP0] Decode batch. #running-req: 1, #token: 626, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 00:44:21 DP0 TP0] Decode batch. #running-req: 1, #token: 615, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:44:21] INFO:     127.0.0.1:57274 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:44:21 DP1 TP0] Decode batch. #running-req: 1, #token: 666, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 00:44:21 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 164, #cached-token: 108, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:44:22 DP1 TP0] Decode batch. #running-req: 2, #token: 912, token usage: 0.00, cuda graph: True, gen throughput (token/s): 124.71, #queue-req: 0, 
[2025-07-28 00:44:22 DP1 TP0] Decode batch. #running-req: 2, #token: 992, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.38, #queue-req: 0, 
[2025-07-28 00:44:23] INFO:     127.0.0.1:57270 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:44:23 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 143, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:44:23 DP0 TP0] Decode batch. #running-req: 1, #token: 257, token usage: 0.00, cuda graph: True, gen throughput (token/s): 19.55, #queue-req: 0, 
[2025-07-28 00:44:23 DP1 TP0] Decode batch. #running-req: 1, #token: 392, token usage: 0.00, cuda graph: True, gen throughput (token/s): 86.43, #queue-req: 0, 
[2025-07-28 00:44:23 DP0 TP0] Decode batch. #running-req: 1, #token: 297, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.70, #queue-req: 0, 
[2025-07-28 00:44:24 DP1 TP0] Decode batch. #running-req: 1, #token: 432, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:44:24 DP0 TP0] Decode batch. #running-req: 1, #token: 337, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.69, #queue-req: 0, 
[2025-07-28 00:44:24 DP1 TP0] Decode batch. #running-req: 1, #token: 472, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 00:44:24 DP0 TP0] Decode batch. #running-req: 1, #token: 377, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.54, #queue-req: 0, 
[2025-07-28 00:44:25 DP1 TP0] Decode batch. #running-req: 1, #token: 512, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:44:25 DP0 TP0] Decode batch. #running-req: 1, #token: 417, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 00:44:25 DP1 TP0] Decode batch. #running-req: 1, #token: 552, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 00:44:26 DP0 TP0] Decode batch. #running-req: 1, #token: 457, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:44:26 DP1 TP0] Decode batch. #running-req: 1, #token: 592, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:44:26 DP0 TP0] Decode batch. #running-req: 1, #token: 497, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 00:44:26 DP1 TP0] Decode batch. #running-req: 1, #token: 632, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 00:44:27 DP0 TP0] Decode batch. #running-req: 1, #token: 537, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 00:44:27 DP1 TP0] Decode batch. #running-req: 1, #token: 672, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 00:44:27 DP0 TP0] Decode batch. #running-req: 1, #token: 577, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 00:44:28 DP1 TP0] Decode batch. #running-req: 1, #token: 712, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:44:28 DP0 TP0] Decode batch. #running-req: 1, #token: 617, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:44:28 DP1 TP0] Decode batch. #running-req: 1, #token: 752, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 00:44:28] INFO:     127.0.0.1:57282 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:44:28 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 117, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:44:28] INFO:     127.0.0.1:41570 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:44:28 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 125, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:44:29 DP0 TP0] Decode batch. #running-req: 1, #token: 243, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.76, #queue-req: 0, 
[2025-07-28 00:44:29 DP1 TP0] Decode batch. #running-req: 1, #token: 263, token usage: 0.00, cuda graph: True, gen throughput (token/s): 64.12, #queue-req: 0, 
[2025-07-28 00:44:29 DP0 TP0] Decode batch. #running-req: 1, #token: 283, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.91, #queue-req: 0, 
[2025-07-28 00:44:29 DP1 TP0] Decode batch. #running-req: 1, #token: 303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.78, #queue-req: 0, 
[2025-07-28 00:44:30 DP0 TP0] Decode batch. #running-req: 1, #token: 323, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.74, #queue-req: 0, 
[2025-07-28 00:44:30 DP1 TP0] Decode batch. #running-req: 1, #token: 343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.74, #queue-req: 0, 
[2025-07-28 00:44:30 DP0 TP0] Decode batch. #running-req: 1, #token: 363, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.59, #queue-req: 0, 
[2025-07-28 00:44:31 DP1 TP0] Decode batch. #running-req: 1, #token: 383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.57, #queue-req: 0, 
[2025-07-28 00:44:31 DP0 TP0] Decode batch. #running-req: 1, #token: 403, token usage: 0.00, cuda graph: True, gen throughput (token/s): 67.86, #queue-req: 0, 
[2025-07-28 00:44:31 DP1 TP0] Decode batch. #running-req: 1, #token: 423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.40, #queue-req: 0, 
[2025-07-28 00:44:31 DP0 TP0] Decode batch. #running-req: 1, #token: 443, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 00:44:32 DP1 TP0] Decode batch. #running-req: 1, #token: 463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.39, #queue-req: 0, 
[2025-07-28 00:44:32 DP0 TP0] Decode batch. #running-req: 1, #token: 483, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 00:44:32 DP1 TP0] Decode batch. #running-req: 1, #token: 503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 00:44:33 DP0 TP0] Decode batch. #running-req: 1, #token: 523, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 00:44:33 DP1 TP0] Decode batch. #running-req: 1, #token: 543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:44:33 DP0 TP0] Decode batch. #running-req: 1, #token: 563, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:44:33 DP1 TP0] Decode batch. #running-req: 1, #token: 583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 00:44:34 DP0 TP0] Decode batch. #running-req: 1, #token: 603, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:44:34 DP1 TP0] Decode batch. #running-req: 1, #token: 623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:44:34] INFO:     127.0.0.1:41578 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:44:34 DP0 TP0] Decode batch. #running-req: 1, #token: 643, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:44:34 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 196, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:44:35 DP1 TP0] Decode batch. #running-req: 1, #token: 321, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.82, #queue-req: 0, 
[2025-07-28 00:44:35 DP0 TP0] Decode batch. #running-req: 1, #token: 683, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:44:35 DP1 TP0] Decode batch. #running-req: 1, #token: 361, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.65, #queue-req: 0, 
[2025-07-28 00:44:35] INFO:     127.0.0.1:41590 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:44:35 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:44:36 DP0 TP0] Decode batch. #running-req: 1, #token: 204, token usage: 0.00, cuda graph: True, gen throughput (token/s): 64.08, #queue-req: 0, 
[2025-07-28 00:44:36 DP1 TP0] Decode batch. #running-req: 1, #token: 401, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.50, #queue-req: 0, 
[2025-07-28 00:44:36 DP0 TP0] Decode batch. #running-req: 1, #token: 244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.28, #queue-req: 0, 
[2025-07-28 00:44:36 DP1 TP0] Decode batch. #running-req: 1, #token: 441, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 00:44:37 DP0 TP0] Decode batch. #running-req: 1, #token: 284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.92, #queue-req: 0, 
[2025-07-28 00:44:37 DP1 TP0] Decode batch. #running-req: 1, #token: 481, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:44:37 DP0 TP0] Decode batch. #running-req: 1, #token: 324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.73, #queue-req: 0, 
[2025-07-28 00:44:37 DP1 TP0] Decode batch. #running-req: 1, #token: 521, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:44:38 DP0 TP0] Decode batch. #running-req: 1, #token: 364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.57, #queue-req: 0, 
[2025-07-28 00:44:38 DP1 TP0] Decode batch. #running-req: 1, #token: 561, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 00:44:38 DP0 TP0] Decode batch. #running-req: 1, #token: 404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.42, #queue-req: 0, 
[2025-07-28 00:44:39 DP1 TP0] Decode batch. #running-req: 1, #token: 601, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 00:44:39 DP0 TP0] Decode batch. #running-req: 1, #token: 444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 00:44:39 DP1 TP0] Decode batch. #running-req: 1, #token: 641, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:44:40 DP0 TP0] Decode batch. #running-req: 1, #token: 484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:44:40 DP1 TP0] Decode batch. #running-req: 1, #token: 681, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 00:44:40 DP0 TP0] Decode batch. #running-req: 1, #token: 524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:44:40 DP1 TP0] Decode batch. #running-req: 1, #token: 721, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 00:44:41 DP0 TP0] Decode batch. #running-req: 1, #token: 564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:44:41 DP1 TP0] Decode batch. #running-req: 1, #token: 761, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 00:44:41 DP0 TP0] Decode batch. #running-req: 1, #token: 604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:44:42 DP1 TP0] Decode batch. #running-req: 1, #token: 801, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:44:42 DP0 TP0] Decode batch. #running-req: 1, #token: 644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:44:42] INFO:     127.0.0.1:47044 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:44:42 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 154, #cached-token: 109, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:44:42 DP1 TP0] Decode batch. #running-req: 2, #token: 1009, token usage: 0.00, cuda graph: True, gen throughput (token/s): 82.98, #queue-req: 0, 
[2025-07-28 00:44:42] INFO:     127.0.0.1:47030 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:44:42 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 128, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:44:43 DP1 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 86.65, #queue-req: 0, 
[2025-07-28 00:44:43 DP0 TP0] Decode batch. #running-req: 1, #token: 274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 37.86, #queue-req: 0, 
[2025-07-28 00:44:43 DP1 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.68, #queue-req: 0, 
[2025-07-28 00:44:43 DP0 TP0] Decode batch. #running-req: 1, #token: 314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.71, #queue-req: 0, 
[2025-07-28 00:44:44 DP1 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.53, #queue-req: 0, 
[2025-07-28 00:44:44 DP0 TP0] Decode batch. #running-req: 1, #token: 354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.61, #queue-req: 0, 
[2025-07-28 00:44:44 DP1 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:44:45 DP0 TP0] Decode batch. #running-req: 1, #token: 394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.49, #queue-req: 0, 
[2025-07-28 00:44:45 DP1 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:44:45 DP0 TP0] Decode batch. #running-req: 1, #token: 434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 00:44:46 DP1 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:44:46 DP0 TP0] Decode batch. #running-req: 1, #token: 474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:44:46 DP1 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:44:46 DP0 TP0] Decode batch. #running-req: 1, #token: 514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:44:47 DP1 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:44:47 DP0 TP0] Decode batch. #running-req: 1, #token: 554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 00:44:47 DP1 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 00:44:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 00:44:48] INFO:     127.0.0.1:34122 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:44:48 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 108, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:44:48 DP1 TP0] Decode batch. #running-req: 2, #token: 845, token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.71, #queue-req: 0, 
[2025-07-28 00:44:49 DP1 TP0] Decode batch. #running-req: 2, #token: 925, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.41, #queue-req: 0, 
[2025-07-28 00:44:49 DP1 TP0] Decode batch. #running-req: 2, #token: 1005, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.42, #queue-req: 0, 
[2025-07-28 00:44:50 DP1 TP0] Decode batch. #running-req: 2, #token: 1085, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.35, #queue-req: 0, 
[2025-07-28 00:44:50] INFO:     127.0.0.1:34110 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:44:50 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:44:50 DP1 TP0] Decode batch. #running-req: 1, #token: 437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 94.75, #queue-req: 0, 
[2025-07-28 00:44:51 DP0 TP0] Decode batch. #running-req: 1, #token: 289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 13.10, #queue-req: 0, 
[2025-07-28 00:44:51 DP1 TP0] Decode batch. #running-req: 1, #token: 477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:44:51 DP0 TP0] Decode batch. #running-req: 1, #token: 329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.70, #queue-req: 0, 
[2025-07-28 00:44:51 DP1 TP0] Decode batch. #running-req: 1, #token: 517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 00:44:52 DP0 TP0] Decode batch. #running-req: 1, #token: 369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.56, #queue-req: 0, 
[2025-07-28 00:44:52 DP1 TP0] Decode batch. #running-req: 1, #token: 557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:44:52 DP0 TP0] Decode batch. #running-req: 1, #token: 409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:44:53 DP1 TP0] Decode batch. #running-req: 1, #token: 597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 00:44:53 DP0 TP0] Decode batch. #running-req: 1, #token: 449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:44:53 DP1 TP0] Decode batch. #running-req: 1, #token: 637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 00:44:53] INFO:     127.0.0.1:34126 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:44:53 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 95, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:44:53 DP0 TP0] Decode batch. #running-req: 1, #token: 489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:44:54 DP1 TP0] Decode batch. #running-req: 1, #token: 241, token usage: 0.00, cuda graph: True, gen throughput (token/s): 64.27, #queue-req: 0, 
[2025-07-28 00:44:54 DP0 TP0] Decode batch. #running-req: 1, #token: 529, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:44:54 DP1 TP0] Decode batch. #running-req: 1, #token: 281, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.04, #queue-req: 0, 
[2025-07-28 00:44:55 DP0 TP0] Decode batch. #running-req: 1, #token: 569, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:44:55 DP1 TP0] Decode batch. #running-req: 1, #token: 321, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.78, #queue-req: 0, 
[2025-07-28 00:44:55 DP0 TP0] Decode batch. #running-req: 1, #token: 609, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:44:56 DP1 TP0] Decode batch. #running-req: 1, #token: 361, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.67, #queue-req: 0, 
[2025-07-28 00:44:56 DP0 TP0] Decode batch. #running-req: 1, #token: 649, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 00:44:56 DP1 TP0] Decode batch. #running-req: 1, #token: 401, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.49, #queue-req: 0, 
[2025-07-28 00:44:56 DP0 TP0] Decode batch. #running-req: 1, #token: 689, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 00:44:57 DP1 TP0] Decode batch. #running-req: 1, #token: 441, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.38, #queue-req: 0, 
[2025-07-28 00:44:57 DP0 TP0] Decode batch. #running-req: 1, #token: 729, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:44:57] INFO:     127.0.0.1:34140 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:44:57 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 107, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:44:57 DP1 TP0] Decode batch. #running-req: 1, #token: 481, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:44:58 DP0 TP0] Decode batch. #running-req: 1, #token: 268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 64.08, #queue-req: 0, 
[2025-07-28 00:44:58 DP1 TP0] Decode batch. #running-req: 1, #token: 521, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:44:58 DP0 TP0] Decode batch. #running-req: 1, #token: 308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.71, #queue-req: 0, 
[2025-07-28 00:44:58 DP1 TP0] Decode batch. #running-req: 1, #token: 561, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 00:44:59 DP0 TP0] Decode batch. #running-req: 1, #token: 348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.65, #queue-req: 0, 
[2025-07-28 00:44:59 DP1 TP0] Decode batch. #running-req: 1, #token: 601, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 00:44:59 DP0 TP0] Decode batch. #running-req: 1, #token: 388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.50, #queue-req: 0, 
[2025-07-28 00:45:00 DP1 TP0] Decode batch. #running-req: 1, #token: 641, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 00:45:00 DP0 TP0] Decode batch. #running-req: 1, #token: 428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 00:45:00 DP1 TP0] Decode batch. #running-req: 1, #token: 681, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 00:45:00 DP0 TP0] Decode batch. #running-req: 1, #token: 468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 00:45:01 DP1 TP0] Decode batch. #running-req: 1, #token: 721, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 00:45:01] INFO:     127.0.0.1:42234 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:45:01 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 78, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:45:01 DP0 TP0] Decode batch. #running-req: 1, #token: 508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:45:01 DP1 TP0] Decode batch. #running-req: 1, #token: 222, token usage: 0.00, cuda graph: True, gen throughput (token/s): 64.61, #queue-req: 0, 
[2025-07-28 00:45:02 DP0 TP0] Decode batch. #running-req: 1, #token: 548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:45:02 DP1 TP0] Decode batch. #running-req: 1, #token: 262, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.27, #queue-req: 0, 
[2025-07-28 00:45:02 DP0 TP0] Decode batch. #running-req: 1, #token: 588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:45:02 DP1 TP0] Decode batch. #running-req: 1, #token: 302, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.80, #queue-req: 0, 
[2025-07-28 00:45:03 DP0 TP0] Decode batch. #running-req: 1, #token: 628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:45:03 DP1 TP0] Decode batch. #running-req: 1, #token: 342, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.73, #queue-req: 0, 
[2025-07-28 00:45:03 DP0 TP0] Decode batch. #running-req: 1, #token: 668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:45:04 DP1 TP0] Decode batch. #running-req: 1, #token: 382, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.60, #queue-req: 0, 
[2025-07-28 00:45:04 DP0 TP0] Decode batch. #running-req: 1, #token: 708, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:45:04 DP1 TP0] Decode batch. #running-req: 1, #token: 422, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.39, #queue-req: 0, 
[2025-07-28 00:45:04] INFO:     127.0.0.1:42250 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:45:04 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 146, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:45:05 DP0 TP0] Decode batch. #running-req: 1, #token: 259, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.74, #queue-req: 0, 
[2025-07-28 00:45:05 DP1 TP0] Decode batch. #running-req: 1, #token: 462, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 00:45:05 DP0 TP0] Decode batch. #running-req: 1, #token: 299, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.72, #queue-req: 0, 
[2025-07-28 00:45:05 DP1 TP0] Decode batch. #running-req: 1, #token: 502, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.38, #queue-req: 0, 
[2025-07-28 00:45:06 DP0 TP0] Decode batch. #running-req: 1, #token: 339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.18, #queue-req: 0, 
[2025-07-28 00:45:06 DP1 TP0] Decode batch. #running-req: 1, #token: 542, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:45:06 DP0 TP0] Decode batch. #running-req: 1, #token: 379, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.46, #queue-req: 0, 
[2025-07-28 00:45:07 DP1 TP0] Decode batch. #running-req: 1, #token: 582, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:45:07] INFO:     127.0.0.1:42254 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:45:07 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:45:07 DP0 TP0] Decode batch. #running-req: 1, #token: 419, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 00:45:07 DP1 TP0] Decode batch. #running-req: 1, #token: 217, token usage: 0.00, cuda graph: True, gen throughput (token/s): 64.14, #queue-req: 0, 
[2025-07-28 00:45:07 DP0 TP0] Decode batch. #running-req: 1, #token: 459, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:45:08 DP1 TP0] Decode batch. #running-req: 1, #token: 257, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.34, #queue-req: 0, 
[2025-07-28 00:45:08 DP0 TP0] Decode batch. #running-req: 1, #token: 499, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:45:08 DP1 TP0] Decode batch. #running-req: 1, #token: 297, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.82, #queue-req: 0, 
[2025-07-28 00:45:09 DP0 TP0] Decode batch. #running-req: 1, #token: 539, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:45:09 DP1 TP0] Decode batch. #running-req: 1, #token: 337, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.75, #queue-req: 0, 
[2025-07-28 00:45:09 DP0 TP0] Decode batch. #running-req: 1, #token: 579, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:45:09 DP1 TP0] Decode batch. #running-req: 1, #token: 377, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.63, #queue-req: 0, 
[2025-07-28 00:45:10 DP0 TP0] Decode batch. #running-req: 1, #token: 619, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:45:10 DP1 TP0] Decode batch. #running-req: 1, #token: 417, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.42, #queue-req: 0, 
[2025-07-28 00:45:10 DP0 TP0] Decode batch. #running-req: 1, #token: 659, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 00:45:11 DP1 TP0] Decode batch. #running-req: 1, #token: 457, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.39, #queue-req: 0, 
[2025-07-28 00:45:11] INFO:     127.0.0.1:55766 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:45:11 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 173, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:45:11 DP0 TP0] Decode batch. #running-req: 1, #token: 295, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.69, #queue-req: 0, 
[2025-07-28 00:45:11 DP1 TP0] Decode batch. #running-req: 1, #token: 497, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 00:45:11 DP0 TP0] Decode batch. #running-req: 1, #token: 335, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.69, #queue-req: 0, 
[2025-07-28 00:45:12 DP1 TP0] Decode batch. #running-req: 1, #token: 537, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:45:12 DP0 TP0] Decode batch. #running-req: 1, #token: 375, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.53, #queue-req: 0, 
[2025-07-28 00:45:12 DP1 TP0] Decode batch. #running-req: 1, #token: 577, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:45:13 DP0 TP0] Decode batch. #running-req: 1, #token: 415, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:45:13 DP1 TP0] Decode batch. #running-req: 1, #token: 617, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:45:13 DP0 TP0] Decode batch. #running-req: 1, #token: 455, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:45:13 DP1 TP0] Decode batch. #running-req: 1, #token: 657, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:45:14 DP0 TP0] Decode batch. #running-req: 1, #token: 495, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:45:14 DP1 TP0] Decode batch. #running-req: 1, #token: 697, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:45:14] INFO:     127.0.0.1:55778 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:45:14 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 178, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:45:14 DP0 TP0] Decode batch. #running-req: 1, #token: 535, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:45:15 DP1 TP0] Decode batch. #running-req: 1, #token: 305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.76, #queue-req: 0, 
[2025-07-28 00:45:15 DP0 TP0] Decode batch. #running-req: 1, #token: 575, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.12, #queue-req: 0, 
[2025-07-28 00:45:15 DP1 TP0] Decode batch. #running-req: 1, #token: 345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.74, #queue-req: 0, 
[2025-07-28 00:45:16 DP0 TP0] Decode batch. #running-req: 1, #token: 615, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 00:45:16 DP1 TP0] Decode batch. #running-req: 1, #token: 385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.59, #queue-req: 0, 
[2025-07-28 00:45:16 DP0 TP0] Decode batch. #running-req: 1, #token: 655, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:45:16 DP1 TP0] Decode batch. #running-req: 1, #token: 425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.40, #queue-req: 0, 
[2025-07-28 00:45:17 DP0 TP0] Decode batch. #running-req: 1, #token: 695, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 00:45:17 DP1 TP0] Decode batch. #running-req: 1, #token: 465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.40, #queue-req: 0, 
[2025-07-28 00:45:17 DP0 TP0] Decode batch. #running-req: 1, #token: 735, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:45:17] INFO:     127.0.0.1:55790 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:45:17 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 88, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:45:18 DP1 TP0] Decode batch. #running-req: 1, #token: 505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:45:18 DP0 TP0] Decode batch. #running-req: 1, #token: 223, token usage: 0.00, cuda graph: True, gen throughput (token/s): 64.30, #queue-req: 0, 
[2025-07-28 00:45:18 DP1 TP0] Decode batch. #running-req: 1, #token: 545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.38, #queue-req: 0, 
[2025-07-28 00:45:18 DP0 TP0] Decode batch. #running-req: 1, #token: 263, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.20, #queue-req: 0, 
[2025-07-28 00:45:19 DP1 TP0] Decode batch. #running-req: 1, #token: 585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:45:19 DP0 TP0] Decode batch. #running-req: 1, #token: 303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.71, #queue-req: 0, 
[2025-07-28 00:45:19 DP1 TP0] Decode batch. #running-req: 1, #token: 625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:45:20 DP0 TP0] Decode batch. #running-req: 1, #token: 343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.66, #queue-req: 0, 
[2025-07-28 00:45:20 DP1 TP0] Decode batch. #running-req: 1, #token: 665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:45:20] INFO:     127.0.0.1:43148 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:45:20 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 153, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:45:20 DP0 TP0] Decode batch. #running-req: 1, #token: 383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.50, #queue-req: 0, 
[2025-07-28 00:45:20 DP1 TP0] Decode batch. #running-req: 1, #token: 279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.84, #queue-req: 0, 
[2025-07-28 00:45:21 DP0 TP0] Decode batch. #running-req: 1, #token: 423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 00:45:21 DP1 TP0] Decode batch. #running-req: 1, #token: 319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.78, #queue-req: 0, 
[2025-07-28 00:45:21 DP0 TP0] Decode batch. #running-req: 1, #token: 463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:45:22 DP1 TP0] Decode batch. #running-req: 1, #token: 359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.68, #queue-req: 0, 
[2025-07-28 00:45:22 DP0 TP0] Decode batch. #running-req: 1, #token: 503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:45:22 DP1 TP0] Decode batch. #running-req: 1, #token: 399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.52, #queue-req: 0, 
[2025-07-28 00:45:22 DP0 TP0] Decode batch. #running-req: 1, #token: 543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:45:23 DP1 TP0] Decode batch. #running-req: 1, #token: 439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.40, #queue-req: 0, 
[2025-07-28 00:45:23] INFO:     127.0.0.1:43160 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:45:23 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 123, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:45:23 DP0 TP0] Decode batch. #running-req: 1, #token: 251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 64.22, #queue-req: 0, 
[2025-07-28 00:45:23 DP1 TP0] Decode batch. #running-req: 1, #token: 479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:45:24 DP0 TP0] Decode batch. #running-req: 1, #token: 291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.78, #queue-req: 0, 
[2025-07-28 00:45:24 DP1 TP0] Decode batch. #running-req: 1, #token: 519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.39, #queue-req: 0, 
[2025-07-28 00:45:24 DP0 TP0] Decode batch. #running-req: 1, #token: 331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.65, #queue-req: 0, 
[2025-07-28 00:45:25 DP1 TP0] Decode batch. #running-req: 1, #token: 559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:45:25 DP0 TP0] Decode batch. #running-req: 1, #token: 371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.54, #queue-req: 0, 
[2025-07-28 00:45:25 DP1 TP0] Decode batch. #running-req: 1, #token: 599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 00:45:25 DP0 TP0] Decode batch. #running-req: 1, #token: 411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 00:45:26 DP1 TP0] Decode batch. #running-req: 1, #token: 639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:45:26 DP0 TP0] Decode batch. #running-req: 1, #token: 451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:45:26 DP1 TP0] Decode batch. #running-req: 1, #token: 679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 00:45:27 DP0 TP0] Decode batch. #running-req: 1, #token: 491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 00:45:27 DP1 TP0] Decode batch. #running-req: 1, #token: 719, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 00:45:27 DP0 TP0] Decode batch. #running-req: 1, #token: 531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 00:45:27] INFO:     127.0.0.1:43172 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:45:27 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 129, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:45:27 DP1 TP0] Decode batch. #running-req: 1, #token: 250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.94, #queue-req: 0, 
[2025-07-28 00:45:28 DP0 TP0] Decode batch. #running-req: 1, #token: 571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 00:45:28 DP1 TP0] Decode batch. #running-req: 1, #token: 290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.90, #queue-req: 0, 
[2025-07-28 00:45:28 DP0 TP0] Decode batch. #running-req: 1, #token: 611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:45:29 DP1 TP0] Decode batch. #running-req: 1, #token: 330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.80, #queue-req: 0, 
[2025-07-28 00:45:29 DP0 TP0] Decode batch. #running-req: 1, #token: 651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 00:45:29 DP1 TP0] Decode batch. #running-req: 1, #token: 370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.55, #queue-req: 0, 
[2025-07-28 00:45:29 DP0 TP0] Decode batch. #running-req: 1, #token: 691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:45:30 DP1 TP0] Decode batch. #running-req: 1, #token: 410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.46, #queue-req: 0, 
[2025-07-28 00:45:30 DP0 TP0] Decode batch. #running-req: 1, #token: 731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:45:30 DP1 TP0] Decode batch. #running-req: 1, #token: 450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.40, #queue-req: 0, 
[2025-07-28 00:45:31 DP0 TP0] Decode batch. #running-req: 1, #token: 771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:45:31 DP1 TP0] Decode batch. #running-req: 1, #token: 490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.39, #queue-req: 0, 
[2025-07-28 00:45:31 DP0 TP0] Decode batch. #running-req: 1, #token: 811, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:45:31 DP1 TP0] Decode batch. #running-req: 1, #token: 530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 00:45:32] INFO:     127.0.0.1:44114 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:45:32 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 112, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:45:32 DP0 TP0] Decode batch. #running-req: 1, #token: 257, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.31, #queue-req: 0, 
[2025-07-28 00:45:32 DP1 TP0] Decode batch. #running-req: 1, #token: 570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 00:45:32 DP0 TP0] Decode batch. #running-req: 1, #token: 297, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 00:45:33 DP1 TP0] Decode batch. #running-req: 1, #token: 610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:45:33 DP0 TP0] Decode batch. #running-req: 1, #token: 337, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.68, #queue-req: 0, 
[2025-07-28 00:45:33 DP1 TP0] Decode batch. #running-req: 1, #token: 650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:45:33] INFO:     127.0.0.1:44122 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:45:33 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 113, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:45:34 DP0 TP0] Decode batch. #running-req: 1, #token: 377, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.53, #queue-req: 0, 
[2025-07-28 00:45:34 DP1 TP0] Decode batch. #running-req: 1, #token: 277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 64.00, #queue-req: 0, 
[2025-07-28 00:45:34 DP0 TP0] Decode batch. #running-req: 1, #token: 417, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:45:34 DP1 TP0] Decode batch. #running-req: 1, #token: 317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.76, #queue-req: 0, 
[2025-07-28 00:45:35 DP0 TP0] Decode batch. #running-req: 1, #token: 457, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:45:35 DP1 TP0] Decode batch. #running-req: 1, #token: 357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.68, #queue-req: 0, 
[2025-07-28 00:45:35 DP0 TP0] Decode batch. #running-req: 1, #token: 497, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 00:45:36 DP1 TP0] Decode batch. #running-req: 1, #token: 397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.58, #queue-req: 0, 
[2025-07-28 00:45:36 DP0 TP0] Decode batch. #running-req: 1, #token: 537, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:45:36 DP1 TP0] Decode batch. #running-req: 1, #token: 437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:45:36 DP0 TP0] Decode batch. #running-req: 1, #token: 577, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:45:37 DP1 TP0] Decode batch. #running-req: 1, #token: 477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.41, #queue-req: 0, 
[2025-07-28 00:45:37 DP0 TP0] Decode batch. #running-req: 1, #token: 617, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:45:37 DP1 TP0] Decode batch. #running-req: 1, #token: 517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.40, #queue-req: 0, 
[2025-07-28 00:45:38 DP0 TP0] Decode batch. #running-req: 1, #token: 657, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:45:38 DP1 TP0] Decode batch. #running-req: 1, #token: 557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 00:45:38 DP0 TP0] Decode batch. #running-req: 1, #token: 697, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 00:45:38 DP1 TP0] Decode batch. #running-req: 1, #token: 597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.38, #queue-req: 0, 
[2025-07-28 00:45:38] INFO:     127.0.0.1:44128 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:45:39 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 103, #cached-token: 112, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:45:39 DP0 TP0] Decode batch. #running-req: 1, #token: 233, token usage: 0.00, cuda graph: True, gen throughput (token/s): 64.19, #queue-req: 0, 
[2025-07-28 00:45:39 DP1 TP0] Decode batch. #running-req: 1, #token: 637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:45:39 DP0 TP0] Decode batch. #running-req: 1, #token: 273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.08, #queue-req: 0, 
[2025-07-28 00:45:40 DP1 TP0] Decode batch. #running-req: 1, #token: 677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:45:40 DP0 TP0] Decode batch. #running-req: 1, #token: 313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.70, #queue-req: 0, 
[2025-07-28 00:45:40 DP1 TP0] Decode batch. #running-req: 1, #token: 717, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 00:45:41 DP0 TP0] Decode batch. #running-req: 1, #token: 353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.61, #queue-req: 0, 
[2025-07-28 00:45:41 DP1 TP0] Decode batch. #running-req: 1, #token: 757, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 00:45:41 DP0 TP0] Decode batch. #running-req: 1, #token: 393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.46, #queue-req: 0, 
[2025-07-28 00:45:41 DP1 TP0] Decode batch. #running-req: 1, #token: 797, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:45:42 DP0 TP0] Decode batch. #running-req: 1, #token: 433, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 00:45:42] INFO:     127.0.0.1:50964 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:45:42 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 89, #cached-token: 112, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:45:42 DP1 TP0] Decode batch. #running-req: 1, #token: 210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.89, #queue-req: 0, 
[2025-07-28 00:45:42 DP0 TP0] Decode batch. #running-req: 1, #token: 473, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 00:45:42 DP1 TP0] Decode batch. #running-req: 1, #token: 250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.36, #queue-req: 0, 
[2025-07-28 00:45:43 DP0 TP0] Decode batch. #running-req: 1, #token: 513, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:45:43 DP1 TP0] Decode batch. #running-req: 1, #token: 290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.91, #queue-req: 0, 
[2025-07-28 00:45:43 DP0 TP0] Decode batch. #running-req: 1, #token: 553, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:45:44 DP1 TP0] Decode batch. #running-req: 1, #token: 330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.76, #queue-req: 0, 
[2025-07-28 00:45:44 DP0 TP0] Decode batch. #running-req: 1, #token: 593, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 00:45:44 DP1 TP0] Decode batch. #running-req: 1, #token: 370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.64, #queue-req: 0, 
[2025-07-28 00:45:45 DP0 TP0] Decode batch. #running-req: 1, #token: 633, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 00:45:45 DP1 TP0] Decode batch. #running-req: 1, #token: 410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.47, #queue-req: 0, 
[2025-07-28 00:45:45 DP0 TP0] Decode batch. #running-req: 1, #token: 673, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 00:45:45] INFO:     127.0.0.1:50974 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:45:45 DP1 TP0] Decode batch. #running-req: 1, #token: 450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.38, #queue-req: 0, 
[2025-07-28 00:45:46 DP1 TP0] Decode batch. #running-req: 1, #token: 490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.38, #queue-req: 0, 
[2025-07-28 00:45:47 DP1 TP0] Decode batch. #running-req: 1, #token: 530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 00:45:47 DP1 TP0] Decode batch. #running-req: 1, #token: 570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 00:45:48 DP1 TP0] Decode batch. #running-req: 1, #token: 610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 00:45:48] INFO:     127.0.0.1:50986 - "POST /v1/chat/completions HTTP/1.1" 200 OK

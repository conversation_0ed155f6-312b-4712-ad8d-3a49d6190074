[2025-07-28 01:18:45] server_args=ServerArgs(model_path='/data/home/<USER>/repos/anesbench/share/models/Qwen3-30B-A3B', tokenizer_path='/data/home/<USER>/repos/anesbench/share/models/Qwen3-30B-A3B', tokenizer_mode='auto', skip_tokenizer_init=False, load_format='auto', model_loader_extra_config='{}', trust_remote_code=False, context_length=32768, is_embedding=False, enable_multimodal=None, revision=None, model_impl='auto', host='127.0.0.1', port=8003, skip_server_warmup=False, warmups=None, nccl_port=None, dtype='auto', quantization=None, quantization_param_path=None, kv_cache_dtype='auto', mem_fraction_static=0.85, max_running_requests=None, max_total_tokens=None, chunked_prefill_size=8192, max_prefill_tokens=16384, schedule_policy='fcfs', schedule_conservativeness=1.0, cpu_offload_gb=0, page_size=1, hybrid_kvcache_ratio=None, swa_full_tokens_ratio=0.8, disable_hybrid_swa_memory=False, device='cuda', tp_size=4, pp_size=1, max_micro_batch_size=None, stream_interval=1, stream_output=False, random_seed=944589117, constrained_json_whitespace_pattern=None, watchdog_timeout=300, dist_timeout=None, download_dir=None, base_gpu_id=0, gpu_id_step=1, sleep_on_idle=False, log_level='info', log_level_http=None, log_requests=False, log_requests_level=0, crash_dump_folder=None, show_time_cost=True, enable_metrics=True, enable_metrics_for_all_schedulers=False, bucket_time_to_first_token=None, bucket_inter_token_latency=None, bucket_e2e_request_latency=None, collect_tokens_histogram=False, decode_log_interval=40, enable_request_time_stats_logging=False, kv_events_config=None, api_key=None, served_model_name='/data/home/<USER>/repos/anesbench/share/models/Qwen3-30B-A3B', chat_template=None, completion_template=None, file_storage_path='sglang_storage', enable_cache_report=False, reasoning_parser=None, tool_call_parser=None, dp_size=2, load_balance_method='round_robin', dist_init_addr=None, nnodes=1, node_rank=0, json_model_override_args='{}', preferred_sampling_params=None, enable_lora=None, max_lora_rank=None, lora_target_modules=None, lora_paths=None, max_loras_per_batch=8, lora_backend='triton', attention_backend=None, sampling_backend='flashinfer', grammar_backend='xgrammar', mm_attention_backend=None, speculative_algorithm=None, speculative_draft_model_path=None, speculative_num_steps=None, speculative_eagle_topk=None, speculative_num_draft_tokens=None, speculative_accept_threshold_single=1.0, speculative_accept_threshold_acc=1.0, speculative_token_map=None, ep_size=1, enable_ep_moe=False, enable_deepep_moe=False, enable_flashinfer_moe=False, enable_flashinfer_allreduce_fusion=False, deepep_mode='auto', ep_num_redundant_experts=0, ep_dispatch_algorithm='static', init_expert_location='trivial', enable_eplb=False, eplb_algorithm='auto', eplb_rebalance_num_iterations=1000, eplb_rebalance_layers_per_chunk=None, expert_distribution_recorder_mode=None, expert_distribution_recorder_buffer_size=1000, enable_expert_distribution_metrics=False, deepep_config=None, moe_dense_tp_size=None, enable_hierarchical_cache=False, hicache_ratio=2.0, hicache_size=0, hicache_write_policy='write_through_selective', hicache_io_backend='', hicache_storage_backend=None, enable_double_sparsity=False, ds_channel_config_path=None, ds_heavy_channel_num=32, ds_heavy_token_num=256, ds_heavy_channel_type='qk', ds_sparse_decode_threshold=4096, disable_radix_cache=False, cuda_graph_max_bs=None, cuda_graph_bs=None, disable_cuda_graph=False, disable_cuda_graph_padding=False, enable_profile_cuda_graph=False, enable_nccl_nvls=False, enable_tokenizer_batch_encode=False, disable_outlines_disk_cache=False, disable_custom_all_reduce=False, enable_mscclpp=False, disable_overlap_schedule=False, enable_mixed_chunk=False, enable_dp_attention=False, enable_dp_lm_head=False, enable_two_batch_overlap=False, enable_torch_compile=False, torch_compile_max_bs=32, torchao_config='', enable_nan_detection=False, enable_p2p_check=False, triton_attention_reduce_in_fp32=False, triton_attention_num_kv_splits=8, num_continuous_decode_steps=1, delete_ckpt_after_loading=False, enable_memory_saver=False, allow_auto_truncate=False, enable_custom_logit_processor=False, flashinfer_mla_disable_ragged=False, disable_shared_experts_fusion=False, disable_chunked_prefix_cache=False, disable_fast_image_processor=False, enable_return_hidden_states=False, enable_triton_kernel_moe=False, debug_tensor_dump_output_folder=None, debug_tensor_dump_input_file=None, debug_tensor_dump_inject=False, debug_tensor_dump_prefill_only=False, disaggregation_mode='null', disaggregation_transfer_backend='mooncake', disaggregation_bootstrap_port=8998, disaggregation_decode_tp=None, disaggregation_decode_dp=None, disaggregation_prefill_pp=1, disaggregation_ib_device=None, num_reserved_decode_tokens=512, pdlb_url=None, custom_weight_loader=[], weight_loader_disable_mmap=False, enable_pdmux=False, sm_group_num=3)
[2025-07-28 01:18:51] Launch DP0 starting at GPU #0.
[2025-07-28 01:18:51] Launch DP1 starting at GPU #4.
[2025-07-28 01:18:58 DP0 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 01:18:58 DP0 TP0] Init torch distributed begin.
[2025-07-28 01:18:59 DP1 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 01:18:59 DP1 TP0] Init torch distributed begin.
[2025-07-28 01:18:59 DP0 TP0] sglang is using nccl==2.26.2
[2025-07-28 01:19:00 DP1 TP0] sglang is using nccl==2.26.2
[2025-07-28 01:19:01 DP0 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 01:19:01 DP1 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 01:19:02 DP0 TP0] Load weight begin. avail mem=78.21 GB
[2025-07-28 01:19:02 DP1 TP0] Load weight begin. avail mem=78.21 GB

Loading safetensors checkpoint shards:   0% Completed | 0/16 [00:00<?, ?it/s]

Loading safetensors checkpoint shards:   0% Completed | 0/16 [00:00<?, ?it/s]

Loading safetensors checkpoint shards:   6% Completed | 1/16 [00:00<00:07,  2.04it/s]

Loading safetensors checkpoint shards:   6% Completed | 1/16 [00:00<00:07,  1.99it/s]

Loading safetensors checkpoint shards:  12% Completed | 2/16 [00:01<00:07,  1.88it/s]

Loading safetensors checkpoint shards:  12% Completed | 2/16 [00:01<00:07,  1.91it/s]

Loading safetensors checkpoint shards:  19% Completed | 3/16 [00:01<00:06,  1.90it/s]

Loading safetensors checkpoint shards:  19% Completed | 3/16 [00:01<00:06,  1.92it/s]

Loading safetensors checkpoint shards:  25% Completed | 4/16 [00:02<00:06,  1.85it/s]

Loading safetensors checkpoint shards:  25% Completed | 4/16 [00:02<00:06,  1.94it/s]

Loading safetensors checkpoint shards:  31% Completed | 5/16 [00:02<00:05,  1.87it/s]

Loading safetensors checkpoint shards:  31% Completed | 5/16 [00:02<00:05,  1.94it/s]

Loading safetensors checkpoint shards:  38% Completed | 6/16 [00:03<00:05,  1.97it/s]

Loading safetensors checkpoint shards:  38% Completed | 6/16 [00:03<00:04,  2.02it/s]

Loading safetensors checkpoint shards:  44% Completed | 7/16 [00:03<00:04,  1.97it/s]

Loading safetensors checkpoint shards:  44% Completed | 7/16 [00:03<00:04,  2.00it/s]

Loading safetensors checkpoint shards:  50% Completed | 8/16 [00:04<00:04,  1.96it/s]

Loading safetensors checkpoint shards:  50% Completed | 8/16 [00:04<00:04,  1.98it/s]

Loading safetensors checkpoint shards:  56% Completed | 9/16 [00:04<00:03,  1.97it/s]

Loading safetensors checkpoint shards:  56% Completed | 9/16 [00:04<00:03,  1.97it/s]

Loading safetensors checkpoint shards:  62% Completed | 10/16 [00:04<00:02,  2.55it/s]

Loading safetensors checkpoint shards:  62% Completed | 10/16 [00:04<00:02,  2.56it/s]

Loading safetensors checkpoint shards:  69% Completed | 11/16 [00:05<00:02,  2.38it/s]

Loading safetensors checkpoint shards:  69% Completed | 11/16 [00:05<00:02,  2.41it/s]

Loading safetensors checkpoint shards:  75% Completed | 12/16 [00:05<00:01,  2.21it/s]

Loading safetensors checkpoint shards:  75% Completed | 12/16 [00:05<00:01,  2.26it/s]

Loading safetensors checkpoint shards:  81% Completed | 13/16 [00:06<00:01,  2.11it/s]

Loading safetensors checkpoint shards:  81% Completed | 13/16 [00:06<00:01,  2.17it/s]

Loading safetensors checkpoint shards:  88% Completed | 14/16 [00:06<00:00,  2.01it/s]

Loading safetensors checkpoint shards:  88% Completed | 14/16 [00:06<00:01,  1.97it/s]

Loading safetensors checkpoint shards:  94% Completed | 15/16 [00:07<00:00,  1.90it/s]

Loading safetensors checkpoint shards:  94% Completed | 15/16 [00:07<00:00,  1.88it/s]

Loading safetensors checkpoint shards: 100% Completed | 16/16 [00:07<00:00,  1.83it/s]

Loading safetensors checkpoint shards: 100% Completed | 16/16 [00:08<00:00,  1.81it/s]

Loading safetensors checkpoint shards: 100% Completed | 16/16 [00:08<00:00,  1.98it/s]


Loading safetensors checkpoint shards: 100% Completed | 16/16 [00:07<00:00,  2.01it/s]

[2025-07-28 01:19:10 DP0 TP0] Load weight end. type=Qwen3MoeForCausalLM, dtype=torch.bfloat16, avail mem=63.90 GB, mem usage=14.30 GB.
[2025-07-28 01:19:10 DP1 TP0] Load weight end. type=Qwen3MoeForCausalLM, dtype=torch.bfloat16, avail mem=63.90 GB, mem usage=14.30 GB.
[2025-07-28 01:19:11 DP1 TP1] KV Cache is allocated. #tokens: 2275867, K size: 26.05 GB, V size: 26.05 GB
[2025-07-28 01:19:11 DP0 TP1] KV Cache is allocated. #tokens: 2275867, K size: 26.05 GB, V size: 26.05 GB
[2025-07-28 01:19:11 DP0 TP0] KV Cache is allocated. #tokens: 2275867, K size: 26.05 GB, V size: 26.05 GB
[2025-07-28 01:19:11 DP1 TP2] KV Cache is allocated. #tokens: 2275867, K size: 26.05 GB, V size: 26.05 GB
[2025-07-28 01:19:11 DP0 TP2] KV Cache is allocated. #tokens: 2275867, K size: 26.05 GB, V size: 26.05 GB
[2025-07-28 01:19:11 DP0 TP0] Memory pool end. avail mem=11.26 GB
[2025-07-28 01:19:11 DP1 TP0] KV Cache is allocated. #tokens: 2275867, K size: 26.05 GB, V size: 26.05 GB
[2025-07-28 01:19:11 DP1 TP3] KV Cache is allocated. #tokens: 2275867, K size: 26.05 GB, V size: 26.05 GB
[2025-07-28 01:19:11 DP1 TP0] Memory pool end. avail mem=11.26 GB
[2025-07-28 01:19:11 DP0 TP3] KV Cache is allocated. #tokens: 2275867, K size: 26.05 GB, V size: 26.05 GB
[2025-07-28 01:19:11 DP0 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.75 GB
[2025-07-28 01:19:11 DP1 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.75 GB
[2025-07-28 01:19:11 DP1 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.73 GB):   0%|          | 0/23 [00:00<?, ?it/s][2025-07-28 01:19:11 DP0 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.73 GB):   0%|          | 0/23 [00:00<?, ?it/s][2025-07-28 01:19:12 DP1 TP2] Config file not found at /data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/layers/moe/fused_moe_triton/configs/triton_3_3_1/E=128,N=192,device_name=NVIDIA_A800-SXM4-80GB.json. Fallback to triton version 3.2.0 and use MoE kernel config from /data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/layers/moe/fused_moe_triton/configs/triton_3_2_0/E=128,N=192,device_name=NVIDIA_A800-SXM4-80GB.json. Performance might be sub-optimal!
[2025-07-28 01:19:12 DP1 TP0] Config file not found at /data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/layers/moe/fused_moe_triton/configs/triton_3_3_1/E=128,N=192,device_name=NVIDIA_A800-SXM4-80GB.json. Fallback to triton version 3.2.0 and use MoE kernel config from /data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/layers/moe/fused_moe_triton/configs/triton_3_2_0/E=128,N=192,device_name=NVIDIA_A800-SXM4-80GB.json. Performance might be sub-optimal!
[2025-07-28 01:19:12 DP1 TP3] Config file not found at /data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/layers/moe/fused_moe_triton/configs/triton_3_3_1/E=128,N=192,device_name=NVIDIA_A800-SXM4-80GB.json. Fallback to triton version 3.2.0 and use MoE kernel config from /data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/layers/moe/fused_moe_triton/configs/triton_3_2_0/E=128,N=192,device_name=NVIDIA_A800-SXM4-80GB.json. Performance might be sub-optimal!
[2025-07-28 01:19:12 DP1 TP1] Config file not found at /data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/layers/moe/fused_moe_triton/configs/triton_3_3_1/E=128,N=192,device_name=NVIDIA_A800-SXM4-80GB.json. Fallback to triton version 3.2.0 and use MoE kernel config from /data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/layers/moe/fused_moe_triton/configs/triton_3_2_0/E=128,N=192,device_name=NVIDIA_A800-SXM4-80GB.json. Performance might be sub-optimal!
[2025-07-28 01:19:12 DP0 TP2] Config file not found at /data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/layers/moe/fused_moe_triton/configs/triton_3_3_1/E=128,N=192,device_name=NVIDIA_A800-SXM4-80GB.json. Fallback to triton version 3.2.0 and use MoE kernel config from /data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/layers/moe/fused_moe_triton/configs/triton_3_2_0/E=128,N=192,device_name=NVIDIA_A800-SXM4-80GB.json. Performance might be sub-optimal!
[2025-07-28 01:19:12 DP0 TP0] Config file not found at /data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/layers/moe/fused_moe_triton/configs/triton_3_3_1/E=128,N=192,device_name=NVIDIA_A800-SXM4-80GB.json. Fallback to triton version 3.2.0 and use MoE kernel config from /data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/layers/moe/fused_moe_triton/configs/triton_3_2_0/E=128,N=192,device_name=NVIDIA_A800-SXM4-80GB.json. Performance might be sub-optimal!
[2025-07-28 01:19:12 DP0 TP3] Config file not found at /data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/layers/moe/fused_moe_triton/configs/triton_3_3_1/E=128,N=192,device_name=NVIDIA_A800-SXM4-80GB.json. Fallback to triton version 3.2.0 and use MoE kernel config from /data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/layers/moe/fused_moe_triton/configs/triton_3_2_0/E=128,N=192,device_name=NVIDIA_A800-SXM4-80GB.json. Performance might be sub-optimal!
[2025-07-28 01:19:12 DP0 TP1] Config file not found at /data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/layers/moe/fused_moe_triton/configs/triton_3_3_1/E=128,N=192,device_name=NVIDIA_A800-SXM4-80GB.json. Fallback to triton version 3.2.0 and use MoE kernel config from /data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/layers/moe/fused_moe_triton/configs/triton_3_2_0/E=128,N=192,device_name=NVIDIA_A800-SXM4-80GB.json. Performance might be sub-optimal!

Capturing batches (bs=160 avail_mem=10.73 GB):   4%|▍         | 1/23 [00:01<00:29,  1.32s/it]
Capturing batches (bs=152 avail_mem=10.48 GB):   4%|▍         | 1/23 [00:01<00:29,  1.32s/it]
Capturing batches (bs=160 avail_mem=10.73 GB):   4%|▍         | 1/23 [00:01<00:29,  1.35s/it]
Capturing batches (bs=152 avail_mem=10.48 GB):   4%|▍         | 1/23 [00:01<00:29,  1.35s/it]
Capturing batches (bs=152 avail_mem=10.48 GB):   9%|▊         | 2/23 [00:01<00:17,  1.21it/s]
Capturing batches (bs=144 avail_mem=10.36 GB):   9%|▊         | 2/23 [00:01<00:17,  1.21it/s]
Capturing batches (bs=152 avail_mem=10.48 GB):   9%|▊         | 2/23 [00:01<00:17,  1.20it/s]
Capturing batches (bs=144 avail_mem=10.36 GB):   9%|▊         | 2/23 [00:01<00:17,  1.20it/s]
Capturing batches (bs=144 avail_mem=10.36 GB):  13%|█▎        | 3/23 [00:02<00:14,  1.35it/s]
Capturing batches (bs=136 avail_mem=10.27 GB):  13%|█▎        | 3/23 [00:02<00:14,  1.35it/s]
Capturing batches (bs=144 avail_mem=10.36 GB):  13%|█▎        | 3/23 [00:02<00:15,  1.33it/s]
Capturing batches (bs=136 avail_mem=10.27 GB):  13%|█▎        | 3/23 [00:02<00:15,  1.33it/s]
Capturing batches (bs=136 avail_mem=10.27 GB):  17%|█▋        | 4/23 [00:02<00:11,  1.67it/s]
Capturing batches (bs=128 avail_mem=10.16 GB):  17%|█▋        | 4/23 [00:02<00:11,  1.67it/s]
Capturing batches (bs=136 avail_mem=10.27 GB):  17%|█▋        | 4/23 [00:02<00:11,  1.63it/s]
Capturing batches (bs=128 avail_mem=10.16 GB):  17%|█▋        | 4/23 [00:02<00:11,  1.63it/s]
Capturing batches (bs=128 avail_mem=10.16 GB):  22%|██▏       | 5/23 [00:03<00:09,  1.92it/s]
Capturing batches (bs=120 avail_mem=10.08 GB):  22%|██▏       | 5/23 [00:03<00:09,  1.92it/s]
Capturing batches (bs=128 avail_mem=10.16 GB):  22%|██▏       | 5/23 [00:03<00:09,  1.87it/s]
Capturing batches (bs=120 avail_mem=10.08 GB):  22%|██▏       | 5/23 [00:03<00:09,  1.87it/s]
Capturing batches (bs=120 avail_mem=10.08 GB):  26%|██▌       | 6/23 [00:03<00:08,  2.05it/s]
Capturing batches (bs=112 avail_mem=9.98 GB):  26%|██▌       | 6/23 [00:03<00:08,  2.05it/s] 
Capturing batches (bs=120 avail_mem=10.08 GB):  26%|██▌       | 6/23 [00:03<00:08,  2.05it/s]
Capturing batches (bs=112 avail_mem=9.98 GB):  26%|██▌       | 6/23 [00:03<00:08,  2.05it/s] 
Capturing batches (bs=112 avail_mem=9.98 GB):  30%|███       | 7/23 [00:04<00:07,  2.19it/s]
Capturing batches (bs=104 avail_mem=9.90 GB):  30%|███       | 7/23 [00:04<00:07,  2.19it/s]
Capturing batches (bs=112 avail_mem=9.98 GB):  30%|███       | 7/23 [00:04<00:07,  2.19it/s]
Capturing batches (bs=104 avail_mem=9.90 GB):  30%|███       | 7/23 [00:04<00:07,  2.19it/s]
Capturing batches (bs=104 avail_mem=9.90 GB):  35%|███▍      | 8/23 [00:04<00:06,  2.29it/s]
Capturing batches (bs=96 avail_mem=9.81 GB):  35%|███▍      | 8/23 [00:04<00:06,  2.29it/s] 
Capturing batches (bs=104 avail_mem=9.90 GB):  35%|███▍      | 8/23 [00:04<00:06,  2.29it/s]
Capturing batches (bs=96 avail_mem=9.81 GB):  35%|███▍      | 8/23 [00:04<00:06,  2.29it/s] 
Capturing batches (bs=96 avail_mem=9.81 GB):  39%|███▉      | 9/23 [00:04<00:05,  2.40it/s]
Capturing batches (bs=88 avail_mem=9.75 GB):  39%|███▉      | 9/23 [00:04<00:05,  2.40it/s]
Capturing batches (bs=96 avail_mem=9.81 GB):  39%|███▉      | 9/23 [00:04<00:05,  2.37it/s]
Capturing batches (bs=88 avail_mem=9.75 GB):  39%|███▉      | 9/23 [00:04<00:05,  2.37it/s]
Capturing batches (bs=88 avail_mem=9.75 GB):  43%|████▎     | 10/23 [00:05<00:05,  2.49it/s]
Capturing batches (bs=80 avail_mem=9.67 GB):  43%|████▎     | 10/23 [00:05<00:05,  2.49it/s]
Capturing batches (bs=88 avail_mem=9.75 GB):  43%|████▎     | 10/23 [00:05<00:05,  2.42it/s]
Capturing batches (bs=80 avail_mem=9.67 GB):  43%|████▎     | 10/23 [00:05<00:05,  2.42it/s]
Capturing batches (bs=80 avail_mem=9.67 GB):  48%|████▊     | 11/23 [00:05<00:04,  2.53it/s]
Capturing batches (bs=72 avail_mem=9.66 GB):  48%|████▊     | 11/23 [00:05<00:04,  2.53it/s]
Capturing batches (bs=80 avail_mem=9.67 GB):  48%|████▊     | 11/23 [00:05<00:04,  2.45it/s]
Capturing batches (bs=72 avail_mem=9.66 GB):  48%|████▊     | 11/23 [00:05<00:04,  2.45it/s]
Capturing batches (bs=72 avail_mem=9.66 GB):  52%|█████▏    | 12/23 [00:05<00:04,  2.58it/s]
Capturing batches (bs=64 avail_mem=9.58 GB):  52%|█████▏    | 12/23 [00:05<00:04,  2.58it/s]
Capturing batches (bs=72 avail_mem=9.66 GB):  52%|█████▏    | 12/23 [00:06<00:04,  2.48it/s]
Capturing batches (bs=64 avail_mem=9.58 GB):  52%|█████▏    | 12/23 [00:06<00:04,  2.48it/s]
Capturing batches (bs=64 avail_mem=9.58 GB):  57%|█████▋    | 13/23 [00:06<00:03,  2.61it/s]
Capturing batches (bs=56 avail_mem=9.54 GB):  57%|█████▋    | 13/23 [00:06<00:03,  2.61it/s]
Capturing batches (bs=64 avail_mem=9.58 GB):  57%|█████▋    | 13/23 [00:06<00:04,  2.49it/s]
Capturing batches (bs=56 avail_mem=9.54 GB):  57%|█████▋    | 13/23 [00:06<00:04,  2.49it/s]
Capturing batches (bs=56 avail_mem=9.54 GB):  61%|██████    | 14/23 [00:06<00:03,  2.65it/s]
Capturing batches (bs=48 avail_mem=9.47 GB):  61%|██████    | 14/23 [00:06<00:03,  2.65it/s]
Capturing batches (bs=56 avail_mem=9.54 GB):  61%|██████    | 14/23 [00:06<00:03,  2.51it/s]
Capturing batches (bs=48 avail_mem=9.47 GB):  61%|██████    | 14/23 [00:06<00:03,  2.51it/s]
Capturing batches (bs=48 avail_mem=9.47 GB):  65%|██████▌   | 15/23 [00:07<00:02,  2.67it/s]
Capturing batches (bs=40 avail_mem=9.46 GB):  65%|██████▌   | 15/23 [00:07<00:02,  2.67it/s]
Capturing batches (bs=48 avail_mem=9.47 GB):  65%|██████▌   | 15/23 [00:07<00:03,  2.51it/s]
Capturing batches (bs=40 avail_mem=9.46 GB):  65%|██████▌   | 15/23 [00:07<00:03,  2.51it/s]
Capturing batches (bs=40 avail_mem=9.46 GB):  70%|██████▉   | 16/23 [00:07<00:02,  2.68it/s]
Capturing batches (bs=32 avail_mem=9.41 GB):  70%|██████▉   | 16/23 [00:07<00:02,  2.68it/s]
Capturing batches (bs=40 avail_mem=9.46 GB):  70%|██████▉   | 16/23 [00:07<00:02,  2.51it/s]
Capturing batches (bs=32 avail_mem=9.41 GB):  70%|██████▉   | 16/23 [00:07<00:02,  2.51it/s]
Capturing batches (bs=32 avail_mem=9.41 GB):  74%|███████▍  | 17/23 [00:08<00:02,  2.15it/s]
Capturing batches (bs=24 avail_mem=9.38 GB):  74%|███████▍  | 17/23 [00:08<00:02,  2.15it/s]
Capturing batches (bs=32 avail_mem=9.41 GB):  74%|███████▍  | 17/23 [00:08<00:02,  2.10it/s]
Capturing batches (bs=24 avail_mem=9.38 GB):  74%|███████▍  | 17/23 [00:08<00:02,  2.10it/s]
Capturing batches (bs=24 avail_mem=9.38 GB):  78%|███████▊  | 18/23 [00:08<00:02,  1.96it/s]
Capturing batches (bs=16 avail_mem=9.35 GB):  78%|███████▊  | 18/23 [00:08<00:02,  1.96it/s]
Capturing batches (bs=24 avail_mem=9.38 GB):  78%|███████▊  | 18/23 [00:08<00:02,  1.90it/s]
Capturing batches (bs=16 avail_mem=9.35 GB):  78%|███████▊  | 18/23 [00:08<00:02,  1.90it/s]
Capturing batches (bs=16 avail_mem=9.35 GB):  83%|████████▎ | 19/23 [00:09<00:01,  2.07it/s]
Capturing batches (bs=8 avail_mem=9.34 GB):  83%|████████▎ | 19/23 [00:09<00:01,  2.07it/s] 
Capturing batches (bs=16 avail_mem=9.35 GB):  83%|████████▎ | 19/23 [00:09<00:01,  2.03it/s]
Capturing batches (bs=8 avail_mem=9.34 GB):  83%|████████▎ | 19/23 [00:09<00:01,  2.03it/s] 
Capturing batches (bs=8 avail_mem=9.34 GB):  87%|████████▋ | 20/23 [00:09<00:01,  2.19it/s]
Capturing batches (bs=4 avail_mem=9.31 GB):  87%|████████▋ | 20/23 [00:09<00:01,  2.19it/s]
Capturing batches (bs=8 avail_mem=9.34 GB):  87%|████████▋ | 20/23 [00:09<00:01,  2.14it/s]
Capturing batches (bs=4 avail_mem=9.31 GB):  87%|████████▋ | 20/23 [00:09<00:01,  2.14it/s]
Capturing batches (bs=4 avail_mem=9.31 GB):  91%|█████████▏| 21/23 [00:09<00:00,  2.29it/s]
Capturing batches (bs=2 avail_mem=9.30 GB):  91%|█████████▏| 21/23 [00:09<00:00,  2.29it/s]
Capturing batches (bs=4 avail_mem=9.31 GB):  91%|█████████▏| 21/23 [00:10<00:00,  2.22it/s]
Capturing batches (bs=2 avail_mem=9.30 GB):  91%|█████████▏| 21/23 [00:10<00:00,  2.22it/s]
Capturing batches (bs=2 avail_mem=9.30 GB):  96%|█████████▌| 22/23 [00:10<00:00,  2.37it/s]
Capturing batches (bs=1 avail_mem=9.27 GB):  96%|█████████▌| 22/23 [00:10<00:00,  2.37it/s]
Capturing batches (bs=2 avail_mem=9.30 GB):  96%|█████████▌| 22/23 [00:10<00:00,  2.29it/s]
Capturing batches (bs=1 avail_mem=9.27 GB):  96%|█████████▌| 22/23 [00:10<00:00,  2.29it/s][2025-07-28 01:19:22 DP0 TP2] Registering 2231 cuda graph addresses
[2025-07-28 01:19:22 DP0 TP1] Registering 2231 cuda graph addresses

Capturing batches (bs=1 avail_mem=9.27 GB): 100%|██████████| 23/23 [00:10<00:00,  2.20it/s]
Capturing batches (bs=1 avail_mem=9.27 GB): 100%|██████████| 23/23 [00:10<00:00,  2.13it/s]
[2025-07-28 01:19:22 DP0 TP3] Registering 2231 cuda graph addresses
[2025-07-28 01:19:22 DP0 TP0] Registering 2231 cuda graph addresses
[2025-07-28 01:19:22 DP0 TP0] Capture cuda graph end. Time elapsed: 11.02 s. mem usage=1.48 GB. avail mem=9.26 GB.
[2025-07-28 01:19:22 DP1 TP2] Registering 2231 cuda graph addresses
[2025-07-28 01:19:22 DP1 TP3] Registering 2231 cuda graph addresses
[2025-07-28 01:19:22 DP1 TP1] Registering 2231 cuda graph addresses

Capturing batches (bs=1 avail_mem=9.27 GB): 100%|██████████| 23/23 [00:11<00:00,  2.10it/s]
Capturing batches (bs=1 avail_mem=9.27 GB): 100%|██████████| 23/23 [00:11<00:00,  2.07it/s]
[2025-07-28 01:19:22 DP1 TP0] Registering 2231 cuda graph addresses
[2025-07-28 01:19:22 DP1 TP0] Capture cuda graph end. Time elapsed: 11.33 s. mem usage=1.48 GB. avail mem=9.26 GB.
[2025-07-28 01:19:23 DP0 TP0] max_total_num_tokens=2275867, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=9.26 GB
[2025-07-28 01:19:23 DP1 TP0] max_total_num_tokens=2275867, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=9.26 GB
[2025-07-28 01:19:23] INFO:     Started server process [1836903]
[2025-07-28 01:19:23] INFO:     Waiting for application startup.
[2025-07-28 01:19:23] INFO:     Application startup complete.
[2025-07-28 01:19:23] INFO:     Uvicorn running on http://127.0.0.1:8003 (Press CTRL+C to quit)
[2025-07-28 01:19:24] INFO:     127.0.0.1:49376 - "GET /get_model_info HTTP/1.1" 200 OK
[2025-07-28 01:19:25 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:19:25 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:19:25] INFO:     127.0.0.1:49382 - "POST /generate HTTP/1.1" 200 OK
[2025-07-28 01:19:25] The server is fired up and ready to roll!
[2025-07-28 01:19:25] INFO:     127.0.0.1:49396 - "GET /health HTTP/1.1" 200 OK
[2025-07-28 01:19:25 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 235, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:19:25 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 194, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:19:26 DP0 TP0] Decode batch. #running-req: 1, #token: 268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 12.53, #queue-req: 0, 
[2025-07-28 01:19:26 DP0 TP0] Decode batch. #running-req: 1, #token: 308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.90, #queue-req: 0, 
[2025-07-28 01:19:26 DP0 TP0] Decode batch. #running-req: 1, #token: 348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.81, #queue-req: 0, 
[2025-07-28 01:19:26 DP0 TP0] Decode batch. #running-req: 1, #token: 388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.35, #queue-req: 0, 
[2025-07-28 01:19:26 DP1 TP0] Decode batch. #running-req: 1, #token: 227, token usage: 0.00, cuda graph: True, gen throughput (token/s): 11.33, #queue-req: 0, 
[2025-07-28 01:19:27 DP0 TP0] Decode batch. #running-req: 1, #token: 428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.71, #queue-req: 0, 
[2025-07-28 01:19:27 DP1 TP0] Decode batch. #running-req: 1, #token: 267, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.33, #queue-req: 0, 
[2025-07-28 01:19:27 DP0 TP0] Decode batch. #running-req: 1, #token: 468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.69, #queue-req: 0, 
[2025-07-28 01:19:27 DP1 TP0] Decode batch. #running-req: 1, #token: 307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.79, #queue-req: 0, 
[2025-07-28 01:19:27 DP0 TP0] Decode batch. #running-req: 1, #token: 508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.82, #queue-req: 0, 
[2025-07-28 01:19:27 DP1 TP0] Decode batch. #running-req: 1, #token: 347, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.30, #queue-req: 0, 
[2025-07-28 01:19:27 DP0 TP0] Decode batch. #running-req: 1, #token: 548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.94, #queue-req: 0, 
[2025-07-28 01:19:27 DP1 TP0] Decode batch. #running-req: 1, #token: 387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.46, #queue-req: 0, 
[2025-07-28 01:19:27 DP0 TP0] Decode batch. #running-req: 1, #token: 588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.16, #queue-req: 0, 
[2025-07-28 01:19:27 DP1 TP0] Decode batch. #running-req: 1, #token: 427, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.92, #queue-req: 0, 
[2025-07-28 01:19:27 DP0 TP0] Decode batch. #running-req: 1, #token: 628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.99, #queue-req: 0, 
[2025-07-28 01:19:28 DP1 TP0] Decode batch. #running-req: 1, #token: 467, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.13, #queue-req: 0, 
[2025-07-28 01:19:28 DP0 TP0] Decode batch. #running-req: 1, #token: 668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.79, #queue-req: 0, 
[2025-07-28 01:19:28 DP1 TP0] Decode batch. #running-req: 1, #token: 507, token usage: 0.00, cuda graph: True, gen throughput (token/s): 206.87, #queue-req: 0, 
[2025-07-28 01:19:28 DP0 TP0] Decode batch. #running-req: 1, #token: 708, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.51, #queue-req: 0, 
[2025-07-28 01:19:28 DP1 TP0] Decode batch. #running-req: 1, #token: 547, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.84, #queue-req: 0, 
[2025-07-28 01:19:28 DP0 TP0] Decode batch. #running-req: 1, #token: 748, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.68, #queue-req: 0, 
[2025-07-28 01:19:28 DP1 TP0] Decode batch. #running-req: 1, #token: 587, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.93, #queue-req: 0, 
[2025-07-28 01:19:28 DP0 TP0] Decode batch. #running-req: 1, #token: 788, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.43, #queue-req: 0, 
[2025-07-28 01:19:28 DP1 TP0] Decode batch. #running-req: 1, #token: 627, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.08, #queue-req: 0, 
[2025-07-28 01:19:28 DP0 TP0] Decode batch. #running-req: 1, #token: 828, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.84, #queue-req: 0, 
[2025-07-28 01:19:28 DP1 TP0] Decode batch. #running-req: 1, #token: 667, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.28, #queue-req: 0, 
[2025-07-28 01:19:29 DP0 TP0] Decode batch. #running-req: 1, #token: 868, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.14, #queue-req: 0, 
[2025-07-28 01:19:29 DP1 TP0] Decode batch. #running-req: 1, #token: 707, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.15, #queue-req: 0, 
[2025-07-28 01:19:29 DP0 TP0] Decode batch. #running-req: 1, #token: 908, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.45, #queue-req: 0, 
[2025-07-28 01:19:29 DP1 TP0] Decode batch. #running-req: 1, #token: 747, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.53, #queue-req: 0, 
[2025-07-28 01:19:29 DP0 TP0] Decode batch. #running-req: 1, #token: 948, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.13, #queue-req: 0, 
[2025-07-28 01:19:29 DP1 TP0] Decode batch. #running-req: 1, #token: 787, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.19, #queue-req: 0, 
[2025-07-28 01:19:29] INFO:     127.0.0.1:49400 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:19:29 DP0 TP0] Decode batch. #running-req: 1, #token: 0, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.81, #queue-req: 0, 
[2025-07-28 01:19:29 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:19:29 DP1 TP0] Decode batch. #running-req: 1, #token: 827, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.78, #queue-req: 0, 
[2025-07-28 01:19:29 DP1 TP0] Decode batch. #running-req: 1, #token: 867, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.02, #queue-req: 0, 
[2025-07-28 01:19:30 DP1 TP0] Decode batch. #running-req: 1, #token: 907, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.84, #queue-req: 0, 
[2025-07-28 01:19:30 DP0 TP0] Decode batch. #running-req: 1, #token: 210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 84.79, #queue-req: 0, 
[2025-07-28 01:19:30 DP1 TP0] Decode batch. #running-req: 1, #token: 947, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.86, #queue-req: 0, 
[2025-07-28 01:19:30 DP0 TP0] Decode batch. #running-req: 1, #token: 250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.86, #queue-req: 0, 
[2025-07-28 01:19:30] INFO:     127.0.0.1:49402 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:19:30 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 207, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:19:30 DP0 TP0] Decode batch. #running-req: 1, #token: 290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.35, #queue-req: 0, 
[2025-07-28 01:19:30 DP0 TP0] Decode batch. #running-req: 1, #token: 330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.69, #queue-req: 0, 
[2025-07-28 01:19:30 DP1 TP0] Decode batch. #running-req: 1, #token: 328, token usage: 0.00, cuda graph: True, gen throughput (token/s): 85.35, #queue-req: 0, 
[2025-07-28 01:19:30 DP0 TP0] Decode batch. #running-req: 1, #token: 370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.79, #queue-req: 0, 
[2025-07-28 01:19:30 DP1 TP0] Decode batch. #running-req: 1, #token: 368, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.68, #queue-req: 0, 
[2025-07-28 01:19:31 DP0 TP0] Decode batch. #running-req: 1, #token: 410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.82, #queue-req: 0, 
[2025-07-28 01:19:31 DP1 TP0] Decode batch. #running-req: 1, #token: 408, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.89, #queue-req: 0, 
[2025-07-28 01:19:31 DP0 TP0] Decode batch. #running-req: 1, #token: 450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.38, #queue-req: 0, 
[2025-07-28 01:19:31 DP1 TP0] Decode batch. #running-req: 1, #token: 448, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.18, #queue-req: 0, 
[2025-07-28 01:19:31 DP0 TP0] Decode batch. #running-req: 1, #token: 490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.11, #queue-req: 0, 
[2025-07-28 01:19:31 DP1 TP0] Decode batch. #running-req: 1, #token: 488, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.17, #queue-req: 0, 
[2025-07-28 01:19:31 DP0 TP0] Decode batch. #running-req: 1, #token: 530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.79, #queue-req: 0, 
[2025-07-28 01:19:31 DP1 TP0] Decode batch. #running-req: 1, #token: 528, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.18, #queue-req: 0, 
[2025-07-28 01:19:31 DP0 TP0] Decode batch. #running-req: 1, #token: 570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.45, #queue-req: 0, 
[2025-07-28 01:19:31 DP1 TP0] Decode batch. #running-req: 1, #token: 568, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.18, #queue-req: 0, 
[2025-07-28 01:19:32 DP0 TP0] Decode batch. #running-req: 1, #token: 610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.56, #queue-req: 0, 
[2025-07-28 01:19:32 DP1 TP0] Decode batch. #running-req: 1, #token: 608, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.78, #queue-req: 0, 
[2025-07-28 01:19:32 DP0 TP0] Decode batch. #running-req: 1, #token: 650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.40, #queue-req: 0, 
[2025-07-28 01:19:32 DP1 TP0] Decode batch. #running-req: 1, #token: 648, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.15, #queue-req: 0, 
[2025-07-28 01:19:32 DP0 TP0] Decode batch. #running-req: 1, #token: 690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.53, #queue-req: 0, 
[2025-07-28 01:19:32 DP1 TP0] Decode batch. #running-req: 1, #token: 688, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.97, #queue-req: 0, 
[2025-07-28 01:19:32 DP0 TP0] Decode batch. #running-req: 1, #token: 730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.13, #queue-req: 0, 
[2025-07-28 01:19:32 DP1 TP0] Decode batch. #running-req: 1, #token: 728, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.10, #queue-req: 0, 
[2025-07-28 01:19:32 DP0 TP0] Decode batch. #running-req: 1, #token: 770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.69, #queue-req: 0, 
[2025-07-28 01:19:32 DP1 TP0] Decode batch. #running-req: 1, #token: 768, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.11, #queue-req: 0, 
[2025-07-28 01:19:32] INFO:     127.0.0.1:49412 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:19:32 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:19:33 DP1 TP0] Decode batch. #running-req: 1, #token: 808, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.02, #queue-req: 0, 
[2025-07-28 01:19:33 DP1 TP0] Decode batch. #running-req: 1, #token: 848, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.10, #queue-req: 0, 
[2025-07-28 01:19:33 DP1 TP0] Decode batch. #running-req: 1, #token: 888, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.17, #queue-req: 0, 
[2025-07-28 01:19:33 DP1 TP0] Decode batch. #running-req: 1, #token: 928, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.79, #queue-req: 0, 
[2025-07-28 01:19:33 DP0 TP0] Decode batch. #running-req: 1, #token: 233, token usage: 0.00, cuda graph: True, gen throughput (token/s): 41.44, #queue-req: 0, 
[2025-07-28 01:19:33 DP1 TP0] Decode batch. #running-req: 1, #token: 968, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.91, #queue-req: 0, 
[2025-07-28 01:19:33 DP0 TP0] Decode batch. #running-req: 1, #token: 273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.41, #queue-req: 0, 
[2025-07-28 01:19:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1008, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.31, #queue-req: 0, 
[2025-07-28 01:19:34 DP0 TP0] Decode batch. #running-req: 1, #token: 313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.76, #queue-req: 0, 
[2025-07-28 01:19:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1048, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.79, #queue-req: 0, 
[2025-07-28 01:19:34 DP0 TP0] Decode batch. #running-req: 1, #token: 353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.60, #queue-req: 0, 
[2025-07-28 01:19:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1088, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.43, #queue-req: 0, 
[2025-07-28 01:19:34 DP0 TP0] Decode batch. #running-req: 1, #token: 393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.06, #queue-req: 0, 
[2025-07-28 01:19:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1128, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.56, #queue-req: 0, 
[2025-07-28 01:19:34 DP0 TP0] Decode batch. #running-req: 1, #token: 433, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.74, #queue-req: 0, 
[2025-07-28 01:19:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1168, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.55, #queue-req: 0, 
[2025-07-28 01:19:34 DP0 TP0] Decode batch. #running-req: 1, #token: 473, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.75, #queue-req: 0, 
[2025-07-28 01:19:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1208, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.56, #queue-req: 0, 
[2025-07-28 01:19:35 DP0 TP0] Decode batch. #running-req: 1, #token: 513, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.98, #queue-req: 0, 
[2025-07-28 01:19:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1248, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.08, #queue-req: 0, 
[2025-07-28 01:19:35] INFO:     127.0.0.1:49418 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:19:35 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 133, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:19:35 DP0 TP0] Decode batch. #running-req: 1, #token: 553, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.07, #queue-req: 0, 
[2025-07-28 01:19:35 DP1 TP0] Decode batch. #running-req: 1, #token: 234, token usage: 0.00, cuda graph: True, gen throughput (token/s): 142.12, #queue-req: 0, 
[2025-07-28 01:19:35 DP0 TP0] Decode batch. #running-req: 1, #token: 593, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.63, #queue-req: 0, 
[2025-07-28 01:19:35 DP1 TP0] Decode batch. #running-req: 1, #token: 274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.38, #queue-req: 0, 
[2025-07-28 01:19:35 DP0 TP0] Decode batch. #running-req: 1, #token: 633, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.07, #queue-req: 0, 
[2025-07-28 01:19:35 DP1 TP0] Decode batch. #running-req: 1, #token: 314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.97, #queue-req: 0, 
[2025-07-28 01:19:35 DP0 TP0] Decode batch. #running-req: 1, #token: 673, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.14, #queue-req: 0, 
[2025-07-28 01:19:35 DP1 TP0] Decode batch. #running-req: 1, #token: 354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.35, #queue-req: 0, 
[2025-07-28 01:19:36 DP0 TP0] Decode batch. #running-req: 1, #token: 713, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.08, #queue-req: 0, 
[2025-07-28 01:19:36 DP1 TP0] Decode batch. #running-req: 1, #token: 394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.02, #queue-req: 0, 
[2025-07-28 01:19:36 DP0 TP0] Decode batch. #running-req: 1, #token: 753, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.71, #queue-req: 0, 
[2025-07-28 01:19:36 DP1 TP0] Decode batch. #running-req: 1, #token: 434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.26, #queue-req: 0, 
[2025-07-28 01:19:36 DP0 TP0] Decode batch. #running-req: 1, #token: 793, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.76, #queue-req: 0, 
[2025-07-28 01:19:36 DP1 TP0] Decode batch. #running-req: 1, #token: 474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.14, #queue-req: 0, 
[2025-07-28 01:19:36 DP0 TP0] Decode batch. #running-req: 1, #token: 833, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.90, #queue-req: 0, 
[2025-07-28 01:19:36 DP1 TP0] Decode batch. #running-req: 1, #token: 514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.98, #queue-req: 0, 
[2025-07-28 01:19:36 DP0 TP0] Decode batch. #running-req: 1, #token: 873, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.15, #queue-req: 0, 
[2025-07-28 01:19:36 DP1 TP0] Decode batch. #running-req: 1, #token: 554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.74, #queue-req: 0, 
[2025-07-28 01:19:37 DP0 TP0] Decode batch. #running-req: 1, #token: 913, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.46, #queue-req: 0, 
[2025-07-28 01:19:37 DP1 TP0] Decode batch. #running-req: 1, #token: 594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.00, #queue-req: 0, 
[2025-07-28 01:19:37 DP0 TP0] Decode batch. #running-req: 1, #token: 953, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.54, #queue-req: 0, 
[2025-07-28 01:19:37 DP1 TP0] Decode batch. #running-req: 1, #token: 634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.08, #queue-req: 0, 
[2025-07-28 01:19:37] INFO:     127.0.0.1:51518 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:19:37 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 186, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:19:37 DP0 TP0] Decode batch. #running-req: 1, #token: 274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 140.42, #queue-req: 0, 
[2025-07-28 01:19:37 DP1 TP0] Decode batch. #running-req: 1, #token: 674, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.08, #queue-req: 0, 
[2025-07-28 01:19:37 DP0 TP0] Decode batch. #running-req: 1, #token: 314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.07, #queue-req: 0, 
[2025-07-28 01:19:37 DP1 TP0] Decode batch. #running-req: 1, #token: 714, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.94, #queue-req: 0, 
[2025-07-28 01:19:37 DP0 TP0] Decode batch. #running-req: 1, #token: 354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.12, #queue-req: 0, 
[2025-07-28 01:19:37 DP1 TP0] Decode batch. #running-req: 1, #token: 754, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.62, #queue-req: 0, 
[2025-07-28 01:19:38 DP0 TP0] Decode batch. #running-req: 1, #token: 394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.45, #queue-req: 0, 
[2025-07-28 01:19:38 DP1 TP0] Decode batch. #running-req: 1, #token: 794, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.06, #queue-req: 0, 
[2025-07-28 01:19:38 DP0 TP0] Decode batch. #running-req: 1, #token: 434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.05, #queue-req: 0, 
[2025-07-28 01:19:38 DP1 TP0] Decode batch. #running-req: 1, #token: 834, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.39, #queue-req: 0, 
[2025-07-28 01:19:38 DP0 TP0] Decode batch. #running-req: 1, #token: 474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.96, #queue-req: 0, 
[2025-07-28 01:19:38 DP1 TP0] Decode batch. #running-req: 1, #token: 874, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.24, #queue-req: 0, 
[2025-07-28 01:19:38 DP0 TP0] Decode batch. #running-req: 1, #token: 514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.24, #queue-req: 0, 
[2025-07-28 01:19:38 DP1 TP0] Decode batch. #running-req: 1, #token: 914, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.21, #queue-req: 0, 
[2025-07-28 01:19:38 DP0 TP0] Decode batch. #running-req: 1, #token: 554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.95, #queue-req: 0, 
[2025-07-28 01:19:38 DP1 TP0] Decode batch. #running-req: 1, #token: 954, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.82, #queue-req: 0, 
[2025-07-28 01:19:39 DP0 TP0] Decode batch. #running-req: 1, #token: 594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.74, #queue-req: 0, 
[2025-07-28 01:19:39 DP1 TP0] Decode batch. #running-req: 1, #token: 994, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.96, #queue-req: 0, 
[2025-07-28 01:19:39 DP0 TP0] Decode batch. #running-req: 1, #token: 634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.74, #queue-req: 0, 
[2025-07-28 01:19:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1034, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.67, #queue-req: 0, 
[2025-07-28 01:19:39 DP0 TP0] Decode batch. #running-req: 1, #token: 674, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.78, #queue-req: 0, 
[2025-07-28 01:19:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1074, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.92, #queue-req: 0, 
[2025-07-28 01:19:39 DP0 TP0] Decode batch. #running-req: 1, #token: 714, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.84, #queue-req: 0, 
[2025-07-28 01:19:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1114, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.93, #queue-req: 0, 
[2025-07-28 01:19:39 DP0 TP0] Decode batch. #running-req: 1, #token: 754, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.63, #queue-req: 0, 
[2025-07-28 01:19:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1154, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.22, #queue-req: 0, 
[2025-07-28 01:19:39 DP0 TP0] Decode batch. #running-req: 1, #token: 794, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.84, #queue-req: 0, 
[2025-07-28 01:19:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.22, #queue-req: 0, 
[2025-07-28 01:19:39] INFO:     127.0.0.1:51528 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:19:40 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 173, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:19:40] INFO:     127.0.0.1:51544 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:19:40 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 226, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:19:40 DP0 TP0] Decode batch. #running-req: 1, #token: 323, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:19:40 DP1 TP0] Decode batch. #running-req: 1, #token: 300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.85, #queue-req: 0, 
[2025-07-28 01:19:40 DP0 TP0] Decode batch. #running-req: 1, #token: 363, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.79, #queue-req: 0, 
[2025-07-28 01:19:40 DP1 TP0] Decode batch. #running-req: 1, #token: 340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.68, #queue-req: 0, 
[2025-07-28 01:19:40 DP0 TP0] Decode batch. #running-req: 1, #token: 403, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.84, #queue-req: 0, 
[2025-07-28 01:19:40 DP1 TP0] Decode batch. #running-req: 1, #token: 380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.03, #queue-req: 0, 
[2025-07-28 01:19:40 DP0 TP0] Decode batch. #running-req: 1, #token: 443, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.80, #queue-req: 0, 
[2025-07-28 01:19:40 DP1 TP0] Decode batch. #running-req: 1, #token: 420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.17, #queue-req: 0, 
[2025-07-28 01:19:41 DP0 TP0] Decode batch. #running-req: 1, #token: 483, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.74, #queue-req: 0, 
[2025-07-28 01:19:41 DP1 TP0] Decode batch. #running-req: 1, #token: 460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.76, #queue-req: 0, 
[2025-07-28 01:19:41 DP0 TP0] Decode batch. #running-req: 1, #token: 523, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.61, #queue-req: 0, 
[2025-07-28 01:19:41 DP1 TP0] Decode batch. #running-req: 1, #token: 500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.31, #queue-req: 0, 
[2025-07-28 01:19:41 DP0 TP0] Decode batch. #running-req: 1, #token: 563, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.29, #queue-req: 0, 
[2025-07-28 01:19:41 DP1 TP0] Decode batch. #running-req: 1, #token: 540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.03, #queue-req: 0, 
[2025-07-28 01:19:41 DP0 TP0] Decode batch. #running-req: 1, #token: 603, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.54, #queue-req: 0, 
[2025-07-28 01:19:41 DP1 TP0] Decode batch. #running-req: 1, #token: 580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.92, #queue-req: 0, 
[2025-07-28 01:19:41 DP0 TP0] Decode batch. #running-req: 1, #token: 643, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.45, #queue-req: 0, 
[2025-07-28 01:19:41 DP1 TP0] Decode batch. #running-req: 1, #token: 620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.94, #queue-req: 0, 
[2025-07-28 01:19:41 DP0 TP0] Decode batch. #running-req: 1, #token: 683, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.57, #queue-req: 0, 
[2025-07-28 01:19:41 DP1 TP0] Decode batch. #running-req: 1, #token: 660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.77, #queue-req: 0, 
[2025-07-28 01:19:42 DP0 TP0] Decode batch. #running-req: 1, #token: 723, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.75, #queue-req: 0, 
[2025-07-28 01:19:42 DP1 TP0] Decode batch. #running-req: 1, #token: 700, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.99, #queue-req: 0, 
[2025-07-28 01:19:42 DP0 TP0] Decode batch. #running-req: 1, #token: 763, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.21, #queue-req: 0, 
[2025-07-28 01:19:42 DP1 TP0] Decode batch. #running-req: 1, #token: 740, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.52, #queue-req: 0, 
[2025-07-28 01:19:42 DP0 TP0] Decode batch. #running-req: 1, #token: 803, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.86, #queue-req: 0, 
[2025-07-28 01:19:42 DP1 TP0] Decode batch. #running-req: 1, #token: 780, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.78, #queue-req: 0, 
[2025-07-28 01:19:42 DP0 TP0] Decode batch. #running-req: 1, #token: 843, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.61, #queue-req: 0, 
[2025-07-28 01:19:42 DP1 TP0] Decode batch. #running-req: 1, #token: 820, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.76, #queue-req: 0, 
[2025-07-28 01:19:42 DP0 TP0] Decode batch. #running-req: 1, #token: 883, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.50, #queue-req: 0, 
[2025-07-28 01:19:42 DP1 TP0] Decode batch. #running-req: 1, #token: 860, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.66, #queue-req: 0, 
[2025-07-28 01:19:43 DP0 TP0] Decode batch. #running-req: 1, #token: 923, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.65, #queue-req: 0, 
[2025-07-28 01:19:43 DP1 TP0] Decode batch. #running-req: 1, #token: 900, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.01, #queue-req: 0, 
[2025-07-28 01:19:43 DP0 TP0] Decode batch. #running-req: 1, #token: 963, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.79, #queue-req: 0, 
[2025-07-28 01:19:43 DP1 TP0] Decode batch. #running-req: 1, #token: 940, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.77, #queue-req: 0, 
[2025-07-28 01:19:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1003, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.79, #queue-req: 0, 
[2025-07-28 01:19:43 DP1 TP0] Decode batch. #running-req: 1, #token: 980, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.91, #queue-req: 0, 
[2025-07-28 01:19:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1043, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.29, #queue-req: 0, 
[2025-07-28 01:19:43 DP1 TP0] Decode batch. #running-req: 1, #token: 1020, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.96, #queue-req: 0, 
[2025-07-28 01:19:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1083, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.47, #queue-req: 0, 
[2025-07-28 01:19:43 DP1 TP0] Decode batch. #running-req: 1, #token: 1060, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.57, #queue-req: 0, 
[2025-07-28 01:19:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1123, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.78, #queue-req: 0, 
[2025-07-28 01:19:44 DP1 TP0] Decode batch. #running-req: 1, #token: 1100, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.56, #queue-req: 0, 
[2025-07-28 01:19:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1163, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.66, #queue-req: 0, 
[2025-07-28 01:19:44 DP1 TP0] Decode batch. #running-req: 1, #token: 1140, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.06, #queue-req: 0, 
[2025-07-28 01:19:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1203, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.86, #queue-req: 0, 
[2025-07-28 01:19:44 DP1 TP0] Decode batch. #running-req: 1, #token: 1180, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.64, #queue-req: 0, 
[2025-07-28 01:19:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1243, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.65, #queue-req: 0, 
[2025-07-28 01:19:44 DP1 TP0] Decode batch. #running-req: 1, #token: 1220, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.72, #queue-req: 0, 
[2025-07-28 01:19:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1283, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.71, #queue-req: 0, 
[2025-07-28 01:19:44 DP1 TP0] Decode batch. #running-req: 1, #token: 1260, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.50, #queue-req: 0, 
[2025-07-28 01:19:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1323, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.99, #queue-req: 0, 
[2025-07-28 01:19:45 DP1 TP0] Decode batch. #running-req: 1, #token: 1300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.50, #queue-req: 0, 
[2025-07-28 01:19:45] INFO:     127.0.0.1:51548 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:19:45 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 168, #cached-token: 88, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:19:45 DP1 TP0] Decode batch. #running-req: 2, #token: 1519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 114.63, #queue-req: 0, 
[2025-07-28 01:19:45 DP1 TP0] Decode batch. #running-req: 2, #token: 1599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.11, #queue-req: 0, 
[2025-07-28 01:19:45 DP1 TP0] Decode batch. #running-req: 2, #token: 1679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.55, #queue-req: 0, 
[2025-07-28 01:19:46 DP1 TP0] Decode batch. #running-req: 2, #token: 1759, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.32, #queue-req: 0, 
[2025-07-28 01:19:46 DP1 TP0] Decode batch. #running-req: 2, #token: 1839, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.04, #queue-req: 0, 
[2025-07-28 01:19:46 DP1 TP0] Decode batch. #running-req: 2, #token: 1919, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.59, #queue-req: 0, 
[2025-07-28 01:19:46 DP1 TP0] Decode batch. #running-req: 2, #token: 1999, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.57, #queue-req: 0, 
[2025-07-28 01:19:47] INFO:     127.0.0.1:51546 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:19:47 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 154, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:19:47 DP1 TP0] Decode batch. #running-req: 1, #token: 546, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.82, #queue-req: 0, 
[2025-07-28 01:19:47 DP0 TP0] Decode batch. #running-req: 1, #token: 250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 16.69, #queue-req: 0, 
[2025-07-28 01:19:47 DP1 TP0] Decode batch. #running-req: 1, #token: 586, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.85, #queue-req: 0, 
[2025-07-28 01:19:47 DP0 TP0] Decode batch. #running-req: 1, #token: 290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.99, #queue-req: 0, 
[2025-07-28 01:19:47 DP1 TP0] Decode batch. #running-req: 1, #token: 626, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.44, #queue-req: 0, 
[2025-07-28 01:19:47 DP0 TP0] Decode batch. #running-req: 1, #token: 330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.27, #queue-req: 0, 
[2025-07-28 01:19:47 DP1 TP0] Decode batch. #running-req: 1, #token: 666, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.60, #queue-req: 0, 
[2025-07-28 01:19:47 DP0 TP0] Decode batch. #running-req: 1, #token: 370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.00, #queue-req: 0, 
[2025-07-28 01:19:48 DP1 TP0] Decode batch. #running-req: 1, #token: 706, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.62, #queue-req: 0, 
[2025-07-28 01:19:48 DP0 TP0] Decode batch. #running-req: 1, #token: 410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.23, #queue-req: 0, 
[2025-07-28 01:19:48 DP1 TP0] Decode batch. #running-req: 1, #token: 746, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.93, #queue-req: 0, 
[2025-07-28 01:19:48 DP0 TP0] Decode batch. #running-req: 1, #token: 450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.93, #queue-req: 0, 
[2025-07-28 01:19:48 DP1 TP0] Decode batch. #running-req: 1, #token: 786, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.80, #queue-req: 0, 
[2025-07-28 01:19:48 DP0 TP0] Decode batch. #running-req: 1, #token: 490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.70, #queue-req: 0, 
[2025-07-28 01:19:48 DP1 TP0] Decode batch. #running-req: 1, #token: 826, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.64, #queue-req: 0, 
[2025-07-28 01:19:48 DP0 TP0] Decode batch. #running-req: 1, #token: 530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.03, #queue-req: 0, 
[2025-07-28 01:19:48 DP1 TP0] Decode batch. #running-req: 1, #token: 866, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.69, #queue-req: 0, 
[2025-07-28 01:19:48 DP0 TP0] Decode batch. #running-req: 1, #token: 570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.13, #queue-req: 0, 
[2025-07-28 01:19:48 DP1 TP0] Decode batch. #running-req: 1, #token: 906, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.90, #queue-req: 0, 
[2025-07-28 01:19:49 DP0 TP0] Decode batch. #running-req: 1, #token: 610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.59, #queue-req: 0, 
[2025-07-28 01:19:49 DP1 TP0] Decode batch. #running-req: 1, #token: 946, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.81, #queue-req: 0, 
[2025-07-28 01:19:49 DP0 TP0] Decode batch. #running-req: 1, #token: 650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.41, #queue-req: 0, 
[2025-07-28 01:19:49 DP1 TP0] Decode batch. #running-req: 1, #token: 986, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.68, #queue-req: 0, 
[2025-07-28 01:19:49 DP0 TP0] Decode batch. #running-req: 1, #token: 690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.59, #queue-req: 0, 
[2025-07-28 01:19:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1026, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.13, #queue-req: 0, 
[2025-07-28 01:19:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1066, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.20, #queue-req: 0, 
[2025-07-28 01:19:49 DP0 TP0] Decode batch. #running-req: 1, #token: 730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 203.85, #queue-req: 0, 
[2025-07-28 01:19:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1106, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.19, #queue-req: 0, 
[2025-07-28 01:19:49 DP0 TP0] Decode batch. #running-req: 1, #token: 770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.11, #queue-req: 0, 
[2025-07-28 01:19:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1146, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.26, #queue-req: 0, 
[2025-07-28 01:19:50 DP0 TP0] Decode batch. #running-req: 1, #token: 810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.42, #queue-req: 0, 
[2025-07-28 01:19:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1186, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.12, #queue-req: 0, 
[2025-07-28 01:19:50 DP0 TP0] Decode batch. #running-req: 1, #token: 850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.49, #queue-req: 0, 
[2025-07-28 01:19:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1226, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.93, #queue-req: 0, 
[2025-07-28 01:19:50 DP0 TP0] Decode batch. #running-req: 1, #token: 890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.36, #queue-req: 0, 
[2025-07-28 01:19:50 DP0 TP0] Decode batch. #running-req: 1, #token: 930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.74, #queue-req: 0, 
[2025-07-28 01:19:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1266, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.20, #queue-req: 0, 
[2025-07-28 01:19:50 DP0 TP0] Decode batch. #running-req: 1, #token: 970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.34, #queue-req: 0, 
[2025-07-28 01:19:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1306, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.69, #queue-req: 0, 
[2025-07-28 01:19:51 DP0 TP0] Decode batch. #running-req: 1, #token: 1010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.72, #queue-req: 0, 
[2025-07-28 01:19:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1346, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.78, #queue-req: 0, 
[2025-07-28 01:19:51 DP0 TP0] Decode batch. #running-req: 1, #token: 1050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.33, #queue-req: 0, 
[2025-07-28 01:19:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1386, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.66, #queue-req: 0, 
[2025-07-28 01:19:51 DP0 TP0] Decode batch. #running-req: 1, #token: 1090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.74, #queue-req: 0, 
[2025-07-28 01:19:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1426, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.67, #queue-req: 0, 
[2025-07-28 01:19:51 DP0 TP0] Decode batch. #running-req: 1, #token: 1130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.92, #queue-req: 0, 
[2025-07-28 01:19:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1466, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.71, #queue-req: 0, 
[2025-07-28 01:19:51 DP0 TP0] Decode batch. #running-req: 1, #token: 1170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.79, #queue-req: 0, 
[2025-07-28 01:19:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1506, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.57, #queue-req: 0, 
[2025-07-28 01:19:51] INFO:     127.0.0.1:50438 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:19:51 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 141, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:19:52 DP1 TP0] Decode batch. #running-req: 2, #token: 1712, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.04, #queue-req: 0, 
[2025-07-28 01:19:52 DP1 TP0] Decode batch. #running-req: 2, #token: 1792, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.71, #queue-req: 0, 
[2025-07-28 01:19:52 DP1 TP0] Decode batch. #running-req: 2, #token: 1872, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.47, #queue-req: 0, 
[2025-07-28 01:19:52 DP1 TP0] Decode batch. #running-req: 2, #token: 1952, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.89, #queue-req: 0, 
[2025-07-28 01:19:53 DP1 TP0] Decode batch. #running-req: 2, #token: 2032, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.96, #queue-req: 0, 
[2025-07-28 01:19:53 DP1 TP0] Decode batch. #running-req: 2, #token: 2112, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.76, #queue-req: 0, 
[2025-07-28 01:19:53 DP1 TP0] Decode batch. #running-req: 2, #token: 2192, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.08, #queue-req: 0, 
[2025-07-28 01:19:53] INFO:     127.0.0.1:50434 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:19:53 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 192, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:19:53 DP1 TP0] Decode batch. #running-req: 1, #token: 531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.94, #queue-req: 0, 
[2025-07-28 01:19:54 DP0 TP0] Decode batch. #running-req: 1, #token: 308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 18.28, #queue-req: 0, 
[2025-07-28 01:19:54 DP1 TP0] Decode batch. #running-req: 1, #token: 571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.30, #queue-req: 0, 
[2025-07-28 01:19:54 DP0 TP0] Decode batch. #running-req: 1, #token: 348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.50, #queue-req: 0, 
[2025-07-28 01:19:54 DP1 TP0] Decode batch. #running-req: 1, #token: 611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.29, #queue-req: 0, 
[2025-07-28 01:19:54 DP0 TP0] Decode batch. #running-req: 1, #token: 388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.54, #queue-req: 0, 
[2025-07-28 01:19:54 DP1 TP0] Decode batch. #running-req: 1, #token: 651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.29, #queue-req: 0, 
[2025-07-28 01:19:54 DP0 TP0] Decode batch. #running-req: 1, #token: 428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.16, #queue-req: 0, 
[2025-07-28 01:19:54 DP1 TP0] Decode batch. #running-req: 1, #token: 691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.18, #queue-req: 0, 
[2025-07-28 01:19:54 DP0 TP0] Decode batch. #running-req: 1, #token: 468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.60, #queue-req: 0, 
[2025-07-28 01:19:54 DP1 TP0] Decode batch. #running-req: 1, #token: 731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.26, #queue-req: 0, 
[2025-07-28 01:19:54 DP0 TP0] Decode batch. #running-req: 1, #token: 508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.81, #queue-req: 0, 
[2025-07-28 01:19:54 DP1 TP0] Decode batch. #running-req: 1, #token: 771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.06, #queue-req: 0, 
[2025-07-28 01:19:55 DP0 TP0] Decode batch. #running-req: 1, #token: 548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.13, #queue-req: 0, 
[2025-07-28 01:19:55 DP1 TP0] Decode batch. #running-req: 1, #token: 811, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.59, #queue-req: 0, 
[2025-07-28 01:19:55 DP0 TP0] Decode batch. #running-req: 1, #token: 588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.90, #queue-req: 0, 
[2025-07-28 01:19:55 DP1 TP0] Decode batch. #running-req: 1, #token: 851, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.17, #queue-req: 0, 
[2025-07-28 01:19:55 DP0 TP0] Decode batch. #running-req: 1, #token: 628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.51, #queue-req: 0, 
[2025-07-28 01:19:55 DP1 TP0] Decode batch. #running-req: 1, #token: 891, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.14, #queue-req: 0, 
[2025-07-28 01:19:55 DP0 TP0] Decode batch. #running-req: 1, #token: 668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.00, #queue-req: 0, 
[2025-07-28 01:19:55 DP1 TP0] Decode batch. #running-req: 1, #token: 931, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.08, #queue-req: 0, 
[2025-07-28 01:19:55 DP0 TP0] Decode batch. #running-req: 1, #token: 708, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.71, #queue-req: 0, 
[2025-07-28 01:19:55 DP1 TP0] Decode batch. #running-req: 1, #token: 971, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.72, #queue-req: 0, 
[2025-07-28 01:19:56 DP0 TP0] Decode batch. #running-req: 1, #token: 748, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.84, #queue-req: 0, 
[2025-07-28 01:19:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1011, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.00, #queue-req: 0, 
[2025-07-28 01:19:56 DP0 TP0] Decode batch. #running-req: 1, #token: 788, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.90, #queue-req: 0, 
[2025-07-28 01:19:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1051, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.33, #queue-req: 0, 
[2025-07-28 01:19:56 DP0 TP0] Decode batch. #running-req: 1, #token: 828, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.13, #queue-req: 0, 
[2025-07-28 01:19:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1091, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.31, #queue-req: 0, 
[2025-07-28 01:19:56] INFO:     127.0.0.1:50442 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:19:56 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 190, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:19:56 DP0 TP0] Decode batch. #running-req: 1, #token: 868, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.00, #queue-req: 0, 
[2025-07-28 01:19:56 DP1 TP0] Decode batch. #running-req: 1, #token: 294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 149.72, #queue-req: 0, 
[2025-07-28 01:19:56 DP0 TP0] Decode batch. #running-req: 1, #token: 908, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.06, #queue-req: 0, 
[2025-07-28 01:19:56 DP1 TP0] Decode batch. #running-req: 1, #token: 334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.53, #queue-req: 0, 
[2025-07-28 01:19:57 DP0 TP0] Decode batch. #running-req: 1, #token: 948, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.78, #queue-req: 0, 
[2025-07-28 01:19:57 DP1 TP0] Decode batch. #running-req: 1, #token: 374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.23, #queue-req: 0, 
[2025-07-28 01:19:57 DP0 TP0] Decode batch. #running-req: 1, #token: 988, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.15, #queue-req: 0, 
[2025-07-28 01:19:57 DP1 TP0] Decode batch. #running-req: 1, #token: 414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.41, #queue-req: 0, 
[2025-07-28 01:19:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1028, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.42, #queue-req: 0, 
[2025-07-28 01:19:57 DP1 TP0] Decode batch. #running-req: 1, #token: 454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.41, #queue-req: 0, 
[2025-07-28 01:19:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1068, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.69, #queue-req: 0, 
[2025-07-28 01:19:57 DP1 TP0] Decode batch. #running-req: 1, #token: 494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.52, #queue-req: 0, 
[2025-07-28 01:19:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1108, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.92, #queue-req: 0, 
[2025-07-28 01:19:57 DP1 TP0] Decode batch. #running-req: 1, #token: 534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.47, #queue-req: 0, 
[2025-07-28 01:19:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1148, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.12, #queue-req: 0, 
[2025-07-28 01:19:58 DP1 TP0] Decode batch. #running-req: 1, #token: 574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.27, #queue-req: 0, 
[2025-07-28 01:19:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1188, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.67, #queue-req: 0, 
[2025-07-28 01:19:58 DP1 TP0] Decode batch. #running-req: 1, #token: 614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.27, #queue-req: 0, 
[2025-07-28 01:19:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1228, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.86, #queue-req: 0, 
[2025-07-28 01:19:58 DP1 TP0] Decode batch. #running-req: 1, #token: 654, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.32, #queue-req: 0, 
[2025-07-28 01:19:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.46, #queue-req: 0, 
[2025-07-28 01:19:58 DP1 TP0] Decode batch. #running-req: 1, #token: 694, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.11, #queue-req: 0, 
[2025-07-28 01:19:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.57, #queue-req: 0, 
[2025-07-28 01:19:58 DP1 TP0] Decode batch. #running-req: 1, #token: 734, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.55, #queue-req: 0, 
[2025-07-28 01:19:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.26, #queue-req: 0, 
[2025-07-28 01:19:59 DP1 TP0] Decode batch. #running-req: 1, #token: 774, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.97, #queue-req: 0, 
[2025-07-28 01:19:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.32, #queue-req: 0, 
[2025-07-28 01:19:59 DP1 TP0] Decode batch. #running-req: 1, #token: 814, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.21, #queue-req: 0, 
[2025-07-28 01:19:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.13, #queue-req: 0, 
[2025-07-28 01:19:59 DP1 TP0] Decode batch. #running-req: 1, #token: 854, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.15, #queue-req: 0, 
[2025-07-28 01:19:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.33, #queue-req: 0, 
[2025-07-28 01:19:59 DP1 TP0] Decode batch. #running-req: 1, #token: 894, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.92, #queue-req: 0, 
[2025-07-28 01:19:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.49, #queue-req: 0, 
[2025-07-28 01:19:59 DP1 TP0] Decode batch. #running-req: 1, #token: 934, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.08, #queue-req: 0, 
[2025-07-28 01:19:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.80, #queue-req: 0, 
[2025-07-28 01:20:00 DP1 TP0] Decode batch. #running-req: 1, #token: 974, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.01, #queue-req: 0, 
[2025-07-28 01:20:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.21, #queue-req: 0, 
[2025-07-28 01:20:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1014, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.04, #queue-req: 0, 
[2025-07-28 01:20:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.95, #queue-req: 0, 
[2025-07-28 01:20:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1054, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.56, #queue-req: 0, 
[2025-07-28 01:20:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.23, #queue-req: 0, 
[2025-07-28 01:20:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1094, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.59, #queue-req: 0, 
[2025-07-28 01:20:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1708, token usage: 0.00, cuda graph: True, gen throughput (token/s): 205.91, #queue-req: 0, 
[2025-07-28 01:20:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1134, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.45, #queue-req: 0, 
[2025-07-28 01:20:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1748, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.57, #queue-req: 0, 
[2025-07-28 01:20:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1174, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.12, #queue-req: 0, 
[2025-07-28 01:20:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1788, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.37, #queue-req: 0, 
[2025-07-28 01:20:01] INFO:     127.0.0.1:57336 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:20:01 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 137, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:20:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1214, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.44, #queue-req: 0, 
[2025-07-28 01:20:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1254, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.98, #queue-req: 0, 
[2025-07-28 01:20:01 DP0 TP0] Decode batch. #running-req: 1, #token: 253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 140.97, #queue-req: 0, 
[2025-07-28 01:20:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.18, #queue-req: 0, 
[2025-07-28 01:20:01 DP0 TP0] Decode batch. #running-req: 1, #token: 293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.75, #queue-req: 0, 
[2025-07-28 01:20:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.15, #queue-req: 0, 
[2025-07-28 01:20:01 DP0 TP0] Decode batch. #running-req: 1, #token: 333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.64, #queue-req: 0, 
[2025-07-28 01:20:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.93, #queue-req: 0, 
[2025-07-28 01:20:01 DP0 TP0] Decode batch. #running-req: 1, #token: 373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.69, #queue-req: 0, 
[2025-07-28 01:20:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.21, #queue-req: 0, 
[2025-07-28 01:20:02 DP0 TP0] Decode batch. #running-req: 1, #token: 413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.63, #queue-req: 0, 
[2025-07-28 01:20:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.27, #queue-req: 0, 
[2025-07-28 01:20:02 DP0 TP0] Decode batch. #running-req: 1, #token: 453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.70, #queue-req: 0, 
[2025-07-28 01:20:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.96, #queue-req: 0, 
[2025-07-28 01:20:02 DP0 TP0] Decode batch. #running-req: 1, #token: 493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.58, #queue-req: 0, 
[2025-07-28 01:20:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.53, #queue-req: 0, 
[2025-07-28 01:20:02 DP0 TP0] Decode batch. #running-req: 1, #token: 533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.41, #queue-req: 0, 
[2025-07-28 01:20:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.07, #queue-req: 0, 
[2025-07-28 01:20:02 DP0 TP0] Decode batch. #running-req: 1, #token: 573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.40, #queue-req: 0, 
[2025-07-28 01:20:03 DP1 TP0] Decode batch. #running-req: 1, #token: 1614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.15, #queue-req: 0, 
[2025-07-28 01:20:03 DP0 TP0] Decode batch. #running-req: 1, #token: 613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.45, #queue-req: 0, 
[2025-07-28 01:20:03 DP1 TP0] Decode batch. #running-req: 1, #token: 1654, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.00, #queue-req: 0, 
[2025-07-28 01:20:03 DP0 TP0] Decode batch. #running-req: 1, #token: 653, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.41, #queue-req: 0, 
[2025-07-28 01:20:03 DP1 TP0] Decode batch. #running-req: 1, #token: 1694, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.04, #queue-req: 0, 
[2025-07-28 01:20:03 DP0 TP0] Decode batch. #running-req: 1, #token: 693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.41, #queue-req: 0, 
[2025-07-28 01:20:03 DP1 TP0] Decode batch. #running-req: 1, #token: 1734, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.98, #queue-req: 0, 
[2025-07-28 01:20:03 DP0 TP0] Decode batch. #running-req: 1, #token: 733, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.32, #queue-req: 0, 
[2025-07-28 01:20:03 DP1 TP0] Decode batch. #running-req: 1, #token: 1774, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.76, #queue-req: 0, 
[2025-07-28 01:20:03 DP0 TP0] Decode batch. #running-req: 1, #token: 773, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.51, #queue-req: 0, 
[2025-07-28 01:20:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1814, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.87, #queue-req: 0, 
[2025-07-28 01:20:04 DP0 TP0] Decode batch. #running-req: 1, #token: 813, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.38, #queue-req: 0, 
[2025-07-28 01:20:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1854, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.70, #queue-req: 0, 
[2025-07-28 01:20:04 DP0 TP0] Decode batch. #running-req: 1, #token: 853, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.43, #queue-req: 0, 
[2025-07-28 01:20:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1894, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.91, #queue-req: 0, 
[2025-07-28 01:20:04 DP0 TP0] Decode batch. #running-req: 1, #token: 893, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.28, #queue-req: 0, 
[2025-07-28 01:20:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1934, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.87, #queue-req: 0, 
[2025-07-28 01:20:04 DP0 TP0] Decode batch. #running-req: 1, #token: 933, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.56, #queue-req: 0, 
[2025-07-28 01:20:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1974, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.75, #queue-req: 0, 
[2025-07-28 01:20:04 DP0 TP0] Decode batch. #running-req: 1, #token: 973, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.62, #queue-req: 0, 
[2025-07-28 01:20:04 DP1 TP0] Decode batch. #running-req: 1, #token: 2014, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.86, #queue-req: 0, 
[2025-07-28 01:20:04 DP0 TP0] Decode batch. #running-req: 1, #token: 1013, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.59, #queue-req: 0, 
[2025-07-28 01:20:05 DP1 TP0] Decode batch. #running-req: 1, #token: 2054, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.77, #queue-req: 0, 
[2025-07-28 01:20:05 DP0 TP0] Decode batch. #running-req: 1, #token: 1053, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.16, #queue-req: 0, 
[2025-07-28 01:20:05 DP1 TP0] Decode batch. #running-req: 1, #token: 2094, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.00, #queue-req: 0, 
[2025-07-28 01:20:05 DP0 TP0] Decode batch. #running-req: 1, #token: 1093, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.86, #queue-req: 0, 
[2025-07-28 01:20:05 DP1 TP0] Decode batch. #running-req: 1, #token: 2134, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.22, #queue-req: 0, 
[2025-07-28 01:20:05 DP0 TP0] Decode batch. #running-req: 1, #token: 1133, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.92, #queue-req: 0, 
[2025-07-28 01:20:05 DP1 TP0] Decode batch. #running-req: 1, #token: 2174, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.16, #queue-req: 0, 
[2025-07-28 01:20:05 DP0 TP0] Decode batch. #running-req: 1, #token: 1173, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.93, #queue-req: 0, 
[2025-07-28 01:20:05 DP1 TP0] Decode batch. #running-req: 1, #token: 2214, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.12, #queue-req: 0, 
[2025-07-28 01:20:05 DP0 TP0] Decode batch. #running-req: 1, #token: 1213, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.04, #queue-req: 0, 
[2025-07-28 01:20:06 DP1 TP0] Decode batch. #running-req: 1, #token: 2254, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.98, #queue-req: 0, 
[2025-07-28 01:20:06 DP0 TP0] Decode batch. #running-req: 1, #token: 1253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.02, #queue-req: 0, 
[2025-07-28 01:20:06 DP0 TP0] Decode batch. #running-req: 1, #token: 1293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.04, #queue-req: 0, 
[2025-07-28 01:20:06 DP1 TP0] Decode batch. #running-req: 1, #token: 2294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.95, #queue-req: 0, 
[2025-07-28 01:20:06] INFO:     127.0.0.1:57344 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:20:06 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 132, #cached-token: 89, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:20:06 DP0 TP0] Decode batch. #running-req: 1, #token: 1333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.78, #queue-req: 0, 
[2025-07-28 01:20:06 DP1 TP0] Decode batch. #running-req: 1, #token: 226, token usage: 0.00, cuda graph: True, gen throughput (token/s): 146.79, #queue-req: 0, 
[2025-07-28 01:20:06 DP0 TP0] Decode batch. #running-req: 1, #token: 1373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.82, #queue-req: 0, 
[2025-07-28 01:20:06 DP1 TP0] Decode batch. #running-req: 1, #token: 266, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.54, #queue-req: 0, 
[2025-07-28 01:20:06 DP0 TP0] Decode batch. #running-req: 1, #token: 1413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.79, #queue-req: 0, 
[2025-07-28 01:20:06 DP1 TP0] Decode batch. #running-req: 1, #token: 306, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.50, #queue-req: 0, 
[2025-07-28 01:20:07 DP0 TP0] Decode batch. #running-req: 1, #token: 1453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.71, #queue-req: 0, 
[2025-07-28 01:20:07 DP1 TP0] Decode batch. #running-req: 1, #token: 346, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.48, #queue-req: 0, 
[2025-07-28 01:20:07 DP0 TP0] Decode batch. #running-req: 1, #token: 1493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.79, #queue-req: 0, 
[2025-07-28 01:20:07 DP1 TP0] Decode batch. #running-req: 1, #token: 386, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.59, #queue-req: 0, 
[2025-07-28 01:20:07 DP0 TP0] Decode batch. #running-req: 1, #token: 1533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.57, #queue-req: 0, 
[2025-07-28 01:20:07 DP1 TP0] Decode batch. #running-req: 1, #token: 426, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.54, #queue-req: 0, 
[2025-07-28 01:20:07 DP0 TP0] Decode batch. #running-req: 1, #token: 1573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.68, #queue-req: 0, 
[2025-07-28 01:20:07 DP1 TP0] Decode batch. #running-req: 1, #token: 466, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.45, #queue-req: 0, 
[2025-07-28 01:20:07 DP0 TP0] Decode batch. #running-req: 1, #token: 1613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.65, #queue-req: 0, 
[2025-07-28 01:20:07 DP1 TP0] Decode batch. #running-req: 1, #token: 506, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.46, #queue-req: 0, 
[2025-07-28 01:20:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1653, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.69, #queue-req: 0, 
[2025-07-28 01:20:08 DP1 TP0] Decode batch. #running-req: 1, #token: 546, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.32, #queue-req: 0, 
[2025-07-28 01:20:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.59, #queue-req: 0, 
[2025-07-28 01:20:08 DP1 TP0] Decode batch. #running-req: 1, #token: 586, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.36, #queue-req: 0, 
[2025-07-28 01:20:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1733, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.61, #queue-req: 0, 
[2025-07-28 01:20:08 DP1 TP0] Decode batch. #running-req: 1, #token: 626, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.96, #queue-req: 0, 
[2025-07-28 01:20:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1773, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.53, #queue-req: 0, 
[2025-07-28 01:20:08 DP1 TP0] Decode batch. #running-req: 1, #token: 666, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.34, #queue-req: 0, 
[2025-07-28 01:20:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1813, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.53, #queue-req: 0, 
[2025-07-28 01:20:08 DP1 TP0] Decode batch. #running-req: 1, #token: 706, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.29, #queue-req: 0, 
[2025-07-28 01:20:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1853, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.38, #queue-req: 0, 
[2025-07-28 01:20:09 DP1 TP0] Decode batch. #running-req: 1, #token: 746, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.36, #queue-req: 0, 
[2025-07-28 01:20:09 DP0 TP0] Decode batch. #running-req: 1, #token: 1893, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.47, #queue-req: 0, 
[2025-07-28 01:20:09 DP1 TP0] Decode batch. #running-req: 1, #token: 786, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.22, #queue-req: 0, 
[2025-07-28 01:20:09 DP0 TP0] Decode batch. #running-req: 1, #token: 1933, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.41, #queue-req: 0, 
[2025-07-28 01:20:09 DP1 TP0] Decode batch. #running-req: 1, #token: 826, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.14, #queue-req: 0, 
[2025-07-28 01:20:09] INFO:     127.0.0.1:57350 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:20:09 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 199, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:20:09 DP1 TP0] Decode batch. #running-req: 1, #token: 866, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.24, #queue-req: 0, 
[2025-07-28 01:20:09 DP0 TP0] Decode batch. #running-req: 1, #token: 304, token usage: 0.00, cuda graph: True, gen throughput (token/s): 140.45, #queue-req: 0, 
[2025-07-28 01:20:09 DP1 TP0] Decode batch. #running-req: 1, #token: 906, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.65, #queue-req: 0, 
[2025-07-28 01:20:09 DP0 TP0] Decode batch. #running-req: 1, #token: 344, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.98, #queue-req: 0, 
[2025-07-28 01:20:09 DP1 TP0] Decode batch. #running-req: 1, #token: 946, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.08, #queue-req: 0, 
[2025-07-28 01:20:10 DP0 TP0] Decode batch. #running-req: 1, #token: 384, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.19, #queue-req: 0, 
[2025-07-28 01:20:10 DP1 TP0] Decode batch. #running-req: 1, #token: 986, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.96, #queue-req: 0, 
[2025-07-28 01:20:10 DP0 TP0] Decode batch. #running-req: 1, #token: 424, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.48, #queue-req: 0, 
[2025-07-28 01:20:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1026, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.07, #queue-req: 0, 
[2025-07-28 01:20:10 DP0 TP0] Decode batch. #running-req: 1, #token: 464, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.88, #queue-req: 0, 
[2025-07-28 01:20:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1066, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.38, #queue-req: 0, 
[2025-07-28 01:20:10 DP0 TP0] Decode batch. #running-req: 1, #token: 504, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.66, #queue-req: 0, 
[2025-07-28 01:20:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1106, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.58, #queue-req: 0, 
[2025-07-28 01:20:10 DP0 TP0] Decode batch. #running-req: 1, #token: 544, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.59, #queue-req: 0, 
[2025-07-28 01:20:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1146, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.93, #queue-req: 0, 
[2025-07-28 01:20:10 DP0 TP0] Decode batch. #running-req: 1, #token: 584, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.44, #queue-req: 0, 
[2025-07-28 01:20:11 DP1 TP0] Decode batch. #running-req: 1, #token: 1186, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.88, #queue-req: 0, 
[2025-07-28 01:20:11] INFO:     127.0.0.1:38288 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:20:11 DP0 TP0] Decode batch. #running-req: 1, #token: 624, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.72, #queue-req: 0, 
[2025-07-28 01:20:11 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 194, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:20:11 DP0 TP0] Decode batch. #running-req: 1, #token: 664, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.93, #queue-req: 0, 
[2025-07-28 01:20:11 DP1 TP0] Decode batch. #running-req: 1, #token: 317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 146.37, #queue-req: 0, 
[2025-07-28 01:20:11 DP0 TP0] Decode batch. #running-req: 1, #token: 704, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.85, #queue-req: 0, 
[2025-07-28 01:20:11 DP1 TP0] Decode batch. #running-req: 1, #token: 357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.81, #queue-req: 0, 
[2025-07-28 01:20:11 DP0 TP0] Decode batch. #running-req: 1, #token: 744, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.74, #queue-req: 0, 
[2025-07-28 01:20:11 DP1 TP0] Decode batch. #running-req: 1, #token: 397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.12, #queue-req: 0, 
[2025-07-28 01:20:11 DP0 TP0] Decode batch. #running-req: 1, #token: 784, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.91, #queue-req: 0, 
[2025-07-28 01:20:11 DP1 TP0] Decode batch. #running-req: 1, #token: 437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.23, #queue-req: 0, 
[2025-07-28 01:20:12 DP0 TP0] Decode batch. #running-req: 1, #token: 824, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.65, #queue-req: 0, 
[2025-07-28 01:20:12 DP1 TP0] Decode batch. #running-req: 1, #token: 477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.98, #queue-req: 0, 
[2025-07-28 01:20:12 DP0 TP0] Decode batch. #running-req: 1, #token: 864, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.73, #queue-req: 0, 
[2025-07-28 01:20:12 DP1 TP0] Decode batch. #running-req: 1, #token: 517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.96, #queue-req: 0, 
[2025-07-28 01:20:12 DP0 TP0] Decode batch. #running-req: 1, #token: 904, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.66, #queue-req: 0, 
[2025-07-28 01:20:12 DP1 TP0] Decode batch. #running-req: 1, #token: 557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.88, #queue-req: 0, 
[2025-07-28 01:20:12 DP0 TP0] Decode batch. #running-req: 1, #token: 944, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.25, #queue-req: 0, 
[2025-07-28 01:20:12 DP1 TP0] Decode batch. #running-req: 1, #token: 597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.91, #queue-req: 0, 
[2025-07-28 01:20:12 DP0 TP0] Decode batch. #running-req: 1, #token: 984, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.63, #queue-req: 0, 
[2025-07-28 01:20:12 DP1 TP0] Decode batch. #running-req: 1, #token: 637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.03, #queue-req: 0, 
[2025-07-28 01:20:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1024, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.69, #queue-req: 0, 
[2025-07-28 01:20:13 DP1 TP0] Decode batch. #running-req: 1, #token: 677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.80, #queue-req: 0, 
[2025-07-28 01:20:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1064, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.86, #queue-req: 0, 
[2025-07-28 01:20:13 DP1 TP0] Decode batch. #running-req: 1, #token: 717, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.11, #queue-req: 0, 
[2025-07-28 01:20:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1104, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.75, #queue-req: 0, 
[2025-07-28 01:20:13 DP1 TP0] Decode batch. #running-req: 1, #token: 757, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.21, #queue-req: 0, 
[2025-07-28 01:20:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1144, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.73, #queue-req: 0, 
[2025-07-28 01:20:13 DP1 TP0] Decode batch. #running-req: 1, #token: 797, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.11, #queue-req: 0, 
[2025-07-28 01:20:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1184, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.85, #queue-req: 0, 
[2025-07-28 01:20:13 DP1 TP0] Decode batch. #running-req: 1, #token: 837, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.16, #queue-req: 0, 
[2025-07-28 01:20:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1224, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.92, #queue-req: 0, 
[2025-07-28 01:20:14 DP1 TP0] Decode batch. #running-req: 1, #token: 877, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.16, #queue-req: 0, 
[2025-07-28 01:20:14] INFO:     127.0.0.1:38292 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:20:14 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 167, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:20:14 DP1 TP0] Decode batch. #running-req: 1, #token: 917, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.21, #queue-req: 0, 
[2025-07-28 01:20:14 DP0 TP0] Decode batch. #running-req: 1, #token: 262, token usage: 0.00, cuda graph: True, gen throughput (token/s): 146.22, #queue-req: 0, 
[2025-07-28 01:20:14 DP1 TP0] Decode batch. #running-req: 1, #token: 957, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.11, #queue-req: 0, 
[2025-07-28 01:20:14 DP0 TP0] Decode batch. #running-req: 1, #token: 302, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.00, #queue-req: 0, 
[2025-07-28 01:20:14 DP1 TP0] Decode batch. #running-req: 1, #token: 997, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.58, #queue-req: 0, 
[2025-07-28 01:20:14 DP0 TP0] Decode batch. #running-req: 1, #token: 342, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.92, #queue-req: 0, 
[2025-07-28 01:20:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1037, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.23, #queue-req: 0, 
[2025-07-28 01:20:14 DP0 TP0] Decode batch. #running-req: 1, #token: 382, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.99, #queue-req: 0, 
[2025-07-28 01:20:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1077, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.55, #queue-req: 0, 
[2025-07-28 01:20:15 DP0 TP0] Decode batch. #running-req: 1, #token: 422, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.23, #queue-req: 0, 
[2025-07-28 01:20:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1117, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.47, #queue-req: 0, 
[2025-07-28 01:20:15 DP0 TP0] Decode batch. #running-req: 1, #token: 462, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.98, #queue-req: 0, 
[2025-07-28 01:20:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1157, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.44, #queue-req: 0, 
[2025-07-28 01:20:15 DP0 TP0] Decode batch. #running-req: 1, #token: 502, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.12, #queue-req: 0, 
[2025-07-28 01:20:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1197, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.38, #queue-req: 0, 
[2025-07-28 01:20:15 DP0 TP0] Decode batch. #running-req: 1, #token: 542, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.37, #queue-req: 0, 
[2025-07-28 01:20:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1237, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.44, #queue-req: 0, 
[2025-07-28 01:20:15 DP0 TP0] Decode batch. #running-req: 1, #token: 582, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.24, #queue-req: 0, 
[2025-07-28 01:20:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.48, #queue-req: 0, 
[2025-07-28 01:20:15 DP0 TP0] Decode batch. #running-req: 1, #token: 622, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.38, #queue-req: 0, 
[2025-07-28 01:20:16 DP1 TP0] Decode batch. #running-req: 1, #token: 1317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.44, #queue-req: 0, 
[2025-07-28 01:20:16 DP0 TP0] Decode batch. #running-req: 1, #token: 662, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.45, #queue-req: 0, 
[2025-07-28 01:20:16 DP1 TP0] Decode batch. #running-req: 1, #token: 1357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.27, #queue-req: 0, 
[2025-07-28 01:20:16 DP0 TP0] Decode batch. #running-req: 1, #token: 702, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.72, #queue-req: 0, 
[2025-07-28 01:20:16 DP1 TP0] Decode batch. #running-req: 1, #token: 1397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.19, #queue-req: 0, 
[2025-07-28 01:20:16 DP0 TP0] Decode batch. #running-req: 1, #token: 742, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.67, #queue-req: 0, 
[2025-07-28 01:20:16 DP1 TP0] Decode batch. #running-req: 1, #token: 1437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.24, #queue-req: 0, 
[2025-07-28 01:20:16 DP0 TP0] Decode batch. #running-req: 1, #token: 782, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.62, #queue-req: 0, 
[2025-07-28 01:20:16 DP1 TP0] Decode batch. #running-req: 1, #token: 1477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.23, #queue-req: 0, 
[2025-07-28 01:20:16 DP0 TP0] Decode batch. #running-req: 1, #token: 822, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.55, #queue-req: 0, 
[2025-07-28 01:20:17 DP1 TP0] Decode batch. #running-req: 1, #token: 1517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.19, #queue-req: 0, 
[2025-07-28 01:20:17 DP0 TP0] Decode batch. #running-req: 1, #token: 862, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.55, #queue-req: 0, 
[2025-07-28 01:20:17 DP1 TP0] Decode batch. #running-req: 1, #token: 1557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.18, #queue-req: 0, 
[2025-07-28 01:20:17 DP0 TP0] Decode batch. #running-req: 1, #token: 902, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.60, #queue-req: 0, 
[2025-07-28 01:20:17 DP1 TP0] Decode batch. #running-req: 1, #token: 1597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.93, #queue-req: 0, 
[2025-07-28 01:20:17 DP0 TP0] Decode batch. #running-req: 1, #token: 942, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.66, #queue-req: 0, 
[2025-07-28 01:20:17 DP1 TP0] Decode batch. #running-req: 1, #token: 1637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.06, #queue-req: 0, 
[2025-07-28 01:20:17 DP0 TP0] Decode batch. #running-req: 1, #token: 982, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.67, #queue-req: 0, 
[2025-07-28 01:20:17 DP1 TP0] Decode batch. #running-req: 1, #token: 1677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.00, #queue-req: 0, 
[2025-07-28 01:20:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1022, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.74, #queue-req: 0, 
[2025-07-28 01:20:18 DP1 TP0] Decode batch. #running-req: 1, #token: 1717, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.15, #queue-req: 0, 
[2025-07-28 01:20:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1062, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.03, #queue-req: 0, 
[2025-07-28 01:20:18 DP1 TP0] Decode batch. #running-req: 1, #token: 1757, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.15, #queue-req: 0, 
[2025-07-28 01:20:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1102, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.95, #queue-req: 0, 
[2025-07-28 01:20:18 DP1 TP0] Decode batch. #running-req: 1, #token: 1797, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.01, #queue-req: 0, 
[2025-07-28 01:20:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1142, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.06, #queue-req: 0, 
[2025-07-28 01:20:18 DP1 TP0] Decode batch. #running-req: 1, #token: 1837, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.71, #queue-req: 0, 
[2025-07-28 01:20:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1182, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.90, #queue-req: 0, 
[2025-07-28 01:20:18 DP1 TP0] Decode batch. #running-req: 1, #token: 1877, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.66, #queue-req: 0, 
[2025-07-28 01:20:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1222, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.82, #queue-req: 0, 
[2025-07-28 01:20:18 DP1 TP0] Decode batch. #running-req: 1, #token: 1917, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.81, #queue-req: 0, 
[2025-07-28 01:20:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1262, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.93, #queue-req: 0, 
[2025-07-28 01:20:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1957, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.80, #queue-req: 0, 
[2025-07-28 01:20:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1302, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.78, #queue-req: 0, 
[2025-07-28 01:20:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1997, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.71, #queue-req: 0, 
[2025-07-28 01:20:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1342, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.82, #queue-req: 0, 
[2025-07-28 01:20:19 DP1 TP0] Decode batch. #running-req: 1, #token: 2037, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.55, #queue-req: 0, 
[2025-07-28 01:20:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1382, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.40, #queue-req: 0, 
[2025-07-28 01:20:19 DP1 TP0] Decode batch. #running-req: 1, #token: 2077, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.26, #queue-req: 0, 
[2025-07-28 01:20:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1422, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.47, #queue-req: 0, 
[2025-07-28 01:20:19 DP1 TP0] Decode batch. #running-req: 1, #token: 2117, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.09, #queue-req: 0, 
[2025-07-28 01:20:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1462, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.13, #queue-req: 0, 
[2025-07-28 01:20:20 DP1 TP0] Decode batch. #running-req: 1, #token: 2157, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.23, #queue-req: 0, 
[2025-07-28 01:20:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1502, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.66, #queue-req: 0, 
[2025-07-28 01:20:20 DP1 TP0] Decode batch. #running-req: 1, #token: 2197, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.05, #queue-req: 0, 
[2025-07-28 01:20:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1542, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.44, #queue-req: 0, 
[2025-07-28 01:20:20 DP1 TP0] Decode batch. #running-req: 1, #token: 2237, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.08, #queue-req: 0, 
[2025-07-28 01:20:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1582, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.08, #queue-req: 0, 
[2025-07-28 01:20:20 DP1 TP0] Decode batch. #running-req: 1, #token: 2277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.14, #queue-req: 0, 
[2025-07-28 01:20:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1622, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.29, #queue-req: 0, 
[2025-07-28 01:20:20 DP1 TP0] Decode batch. #running-req: 1, #token: 2317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.62, #queue-req: 0, 
[2025-07-28 01:20:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1662, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.12, #queue-req: 0, 
[2025-07-28 01:20:21 DP1 TP0] Decode batch. #running-req: 1, #token: 2357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.82, #queue-req: 0, 
[2025-07-28 01:20:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1702, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.39, #queue-req: 0, 
[2025-07-28 01:20:21 DP1 TP0] Decode batch. #running-req: 1, #token: 2397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.78, #queue-req: 0, 
[2025-07-28 01:20:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1742, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.34, #queue-req: 0, 
[2025-07-28 01:20:21 DP1 TP0] Decode batch. #running-req: 1, #token: 2437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.71, #queue-req: 0, 
[2025-07-28 01:20:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1782, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.04, #queue-req: 0, 
[2025-07-28 01:20:21 DP1 TP0] Decode batch. #running-req: 1, #token: 2477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.79, #queue-req: 0, 
[2025-07-28 01:20:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1822, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.17, #queue-req: 0, 
[2025-07-28 01:20:21 DP1 TP0] Decode batch. #running-req: 1, #token: 2517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.09, #queue-req: 0, 
[2025-07-28 01:20:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1862, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.26, #queue-req: 0, 
[2025-07-28 01:20:22 DP1 TP0] Decode batch. #running-req: 1, #token: 2557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.41, #queue-req: 0, 
[2025-07-28 01:20:22 DP0 TP0] Decode batch. #running-req: 1, #token: 1902, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.36, #queue-req: 0, 
[2025-07-28 01:20:22 DP1 TP0] Decode batch. #running-req: 1, #token: 2597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.72, #queue-req: 0, 
[2025-07-28 01:20:22 DP0 TP0] Decode batch. #running-req: 1, #token: 1942, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.45, #queue-req: 0, 
[2025-07-28 01:20:22 DP1 TP0] Decode batch. #running-req: 1, #token: 2637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.50, #queue-req: 0, 
[2025-07-28 01:20:22 DP0 TP0] Decode batch. #running-req: 1, #token: 1982, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.34, #queue-req: 0, 
[2025-07-28 01:20:22] INFO:     127.0.0.1:38308 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:20:22 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 172, #cached-token: 89, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:20:22 DP0 TP0] Decode batch. #running-req: 1, #token: 2022, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.91, #queue-req: 0, 
[2025-07-28 01:20:22 DP1 TP0] Decode batch. #running-req: 1, #token: 289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 146.18, #queue-req: 0, 
[2025-07-28 01:20:22 DP0 TP0] Decode batch. #running-req: 1, #token: 2062, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.02, #queue-req: 0, 
[2025-07-28 01:20:22 DP1 TP0] Decode batch. #running-req: 1, #token: 329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.50, #queue-req: 0, 
[2025-07-28 01:20:23 DP0 TP0] Decode batch. #running-req: 1, #token: 2102, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.69, #queue-req: 0, 
[2025-07-28 01:20:23 DP1 TP0] Decode batch. #running-req: 1, #token: 369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.32, #queue-req: 0, 
[2025-07-28 01:20:23 DP0 TP0] Decode batch. #running-req: 1, #token: 2142, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.75, #queue-req: 0, 
[2025-07-28 01:20:23 DP1 TP0] Decode batch. #running-req: 1, #token: 409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.45, #queue-req: 0, 
[2025-07-28 01:20:23 DP0 TP0] Decode batch. #running-req: 1, #token: 2182, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.72, #queue-req: 0, 
[2025-07-28 01:20:23 DP1 TP0] Decode batch. #running-req: 1, #token: 449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.30, #queue-req: 0, 
[2025-07-28 01:20:23 DP0 TP0] Decode batch. #running-req: 1, #token: 2222, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.68, #queue-req: 0, 
[2025-07-28 01:20:23 DP1 TP0] Decode batch. #running-req: 1, #token: 489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.37, #queue-req: 0, 
[2025-07-28 01:20:23 DP0 TP0] Decode batch. #running-req: 1, #token: 2262, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.63, #queue-req: 0, 
[2025-07-28 01:20:23 DP1 TP0] Decode batch. #running-req: 1, #token: 529, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.04, #queue-req: 0, 
[2025-07-28 01:20:24 DP0 TP0] Decode batch. #running-req: 1, #token: 2302, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.59, #queue-req: 0, 
[2025-07-28 01:20:24 DP1 TP0] Decode batch. #running-req: 1, #token: 569, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.15, #queue-req: 0, 
[2025-07-28 01:20:24 DP0 TP0] Decode batch. #running-req: 1, #token: 2342, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.26, #queue-req: 0, 
[2025-07-28 01:20:24 DP1 TP0] Decode batch. #running-req: 1, #token: 609, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.04, #queue-req: 0, 
[2025-07-28 01:20:24 DP0 TP0] Decode batch. #running-req: 1, #token: 2382, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.41, #queue-req: 0, 
[2025-07-28 01:20:24 DP1 TP0] Decode batch. #running-req: 1, #token: 649, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.28, #queue-req: 0, 
[2025-07-28 01:20:24 DP0 TP0] Decode batch. #running-req: 1, #token: 2422, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.56, #queue-req: 0, 
[2025-07-28 01:20:24 DP1 TP0] Decode batch. #running-req: 1, #token: 689, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.34, #queue-req: 0, 
[2025-07-28 01:20:24 DP0 TP0] Decode batch. #running-req: 1, #token: 2462, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.41, #queue-req: 0, 
[2025-07-28 01:20:24 DP1 TP0] Decode batch. #running-req: 1, #token: 729, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.35, #queue-req: 0, 
[2025-07-28 01:20:24 DP0 TP0] Decode batch. #running-req: 1, #token: 2502, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.40, #queue-req: 0, 
[2025-07-28 01:20:24 DP1 TP0] Decode batch. #running-req: 1, #token: 769, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.14, #queue-req: 0, 
[2025-07-28 01:20:25 DP0 TP0] Decode batch. #running-req: 1, #token: 2542, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.47, #queue-req: 0, 
[2025-07-28 01:20:25 DP1 TP0] Decode batch. #running-req: 1, #token: 809, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.21, #queue-req: 0, 
[2025-07-28 01:20:25 DP0 TP0] Decode batch. #running-req: 1, #token: 2582, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.32, #queue-req: 0, 
[2025-07-28 01:20:25 DP1 TP0] Decode batch. #running-req: 1, #token: 849, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.45, #queue-req: 0, 
[2025-07-28 01:20:25 DP0 TP0] Decode batch. #running-req: 1, #token: 2622, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.34, #queue-req: 0, 
[2025-07-28 01:20:25 DP1 TP0] Decode batch. #running-req: 1, #token: 889, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.18, #queue-req: 0, 
[2025-07-28 01:20:25 DP1 TP0] Decode batch. #running-req: 1, #token: 929, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.19, #queue-req: 0, 
[2025-07-28 01:20:25 DP0 TP0] Decode batch. #running-req: 1, #token: 2662, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.44, #queue-req: 0, 
[2025-07-28 01:20:25 DP1 TP0] Decode batch. #running-req: 1, #token: 969, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.13, #queue-req: 0, 
[2025-07-28 01:20:25 DP0 TP0] Decode batch. #running-req: 1, #token: 2702, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.35, #queue-req: 0, 
[2025-07-28 01:20:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1009, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.05, #queue-req: 0, 
[2025-07-28 01:20:26 DP0 TP0] Decode batch. #running-req: 1, #token: 2742, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.29, #queue-req: 0, 
[2025-07-28 01:20:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1049, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.59, #queue-req: 0, 
[2025-07-28 01:20:26 DP0 TP0] Decode batch. #running-req: 1, #token: 2782, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.06, #queue-req: 0, 
[2025-07-28 01:20:26] INFO:     127.0.0.1:36078 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:20:26 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 219, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:20:26 DP0 TP0] Decode batch. #running-req: 2, #token: 3058, token usage: 0.00, cuda graph: True, gen throughput (token/s): 120.62, #queue-req: 0, 
[2025-07-28 01:20:27 DP0 TP0] Decode batch. #running-req: 2, #token: 3138, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.95, #queue-req: 0, 
[2025-07-28 01:20:27 DP0 TP0] Decode batch. #running-req: 2, #token: 3218, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.00, #queue-req: 0, 
[2025-07-28 01:20:27 DP0 TP0] Decode batch. #running-req: 2, #token: 3298, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.17, #queue-req: 0, 
[2025-07-28 01:20:27] INFO:     127.0.0.1:43942 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:20:27 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 155, #cached-token: 90, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:20:27 DP0 TP0] Decode batch. #running-req: 1, #token: 483, token usage: 0.00, cuda graph: True, gen throughput (token/s): 227.83, #queue-req: 0, 
[2025-07-28 01:20:27 DP1 TP0] Decode batch. #running-req: 1, #token: 264, token usage: 0.00, cuda graph: True, gen throughput (token/s): 26.81, #queue-req: 0, 
[2025-07-28 01:20:27 DP0 TP0] Decode batch. #running-req: 1, #token: 523, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.75, #queue-req: 0, 
[2025-07-28 01:20:27 DP1 TP0] Decode batch. #running-req: 1, #token: 304, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.82, #queue-req: 0, 
[2025-07-28 01:20:28 DP0 TP0] Decode batch. #running-req: 1, #token: 563, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.60, #queue-req: 0, 
[2025-07-28 01:20:28 DP1 TP0] Decode batch. #running-req: 1, #token: 344, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.83, #queue-req: 0, 
[2025-07-28 01:20:28 DP0 TP0] Decode batch. #running-req: 1, #token: 603, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.69, #queue-req: 0, 
[2025-07-28 01:20:28 DP1 TP0] Decode batch. #running-req: 1, #token: 384, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.09, #queue-req: 0, 
[2025-07-28 01:20:28 DP0 TP0] Decode batch. #running-req: 1, #token: 643, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.74, #queue-req: 0, 
[2025-07-28 01:20:28 DP1 TP0] Decode batch. #running-req: 1, #token: 424, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.54, #queue-req: 0, 
[2025-07-28 01:20:28 DP0 TP0] Decode batch. #running-req: 1, #token: 683, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.65, #queue-req: 0, 
[2025-07-28 01:20:28 DP1 TP0] Decode batch. #running-req: 1, #token: 464, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.65, #queue-req: 0, 
[2025-07-28 01:20:28 DP0 TP0] Decode batch. #running-req: 1, #token: 723, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.69, #queue-req: 0, 
[2025-07-28 01:20:28 DP1 TP0] Decode batch. #running-req: 1, #token: 504, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.83, #queue-req: 0, 
[2025-07-28 01:20:29 DP0 TP0] Decode batch. #running-req: 1, #token: 763, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.35, #queue-req: 0, 
[2025-07-28 01:20:29 DP1 TP0] Decode batch. #running-req: 1, #token: 544, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.13, #queue-req: 0, 
[2025-07-28 01:20:29 DP0 TP0] Decode batch. #running-req: 1, #token: 803, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.45, #queue-req: 0, 
[2025-07-28 01:20:29 DP1 TP0] Decode batch. #running-req: 1, #token: 584, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.95, #queue-req: 0, 
[2025-07-28 01:20:29 DP0 TP0] Decode batch. #running-req: 1, #token: 843, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.23, #queue-req: 0, 
[2025-07-28 01:20:29 DP1 TP0] Decode batch. #running-req: 1, #token: 624, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.41, #queue-req: 0, 
[2025-07-28 01:20:29 DP0 TP0] Decode batch. #running-req: 1, #token: 883, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.15, #queue-req: 0, 
[2025-07-28 01:20:29 DP1 TP0] Decode batch. #running-req: 1, #token: 664, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.22, #queue-req: 0, 
[2025-07-28 01:20:29 DP0 TP0] Decode batch. #running-req: 1, #token: 923, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.28, #queue-req: 0, 
[2025-07-28 01:20:29 DP1 TP0] Decode batch. #running-req: 1, #token: 704, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.83, #queue-req: 0, 
[2025-07-28 01:20:30 DP0 TP0] Decode batch. #running-req: 1, #token: 963, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.19, #queue-req: 0, 
[2025-07-28 01:20:30 DP1 TP0] Decode batch. #running-req: 1, #token: 744, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.97, #queue-req: 0, 
[2025-07-28 01:20:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1003, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.19, #queue-req: 0, 
[2025-07-28 01:20:30 DP1 TP0] Decode batch. #running-req: 1, #token: 784, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.74, #queue-req: 0, 
[2025-07-28 01:20:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1043, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.63, #queue-req: 0, 
[2025-07-28 01:20:30 DP1 TP0] Decode batch. #running-req: 1, #token: 824, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.98, #queue-req: 0, 
[2025-07-28 01:20:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1083, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.65, #queue-req: 0, 
[2025-07-28 01:20:30 DP1 TP0] Decode batch. #running-req: 1, #token: 864, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.94, #queue-req: 0, 
[2025-07-28 01:20:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1123, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.72, #queue-req: 0, 
[2025-07-28 01:20:30 DP1 TP0] Decode batch. #running-req: 1, #token: 904, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.85, #queue-req: 0, 
[2025-07-28 01:20:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1163, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.36, #queue-req: 0, 
[2025-07-28 01:20:31 DP1 TP0] Decode batch. #running-req: 1, #token: 944, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.82, #queue-req: 0, 
[2025-07-28 01:20:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1203, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.80, #queue-req: 0, 
[2025-07-28 01:20:31 DP1 TP0] Decode batch. #running-req: 1, #token: 984, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.91, #queue-req: 0, 
[2025-07-28 01:20:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1243, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.74, #queue-req: 0, 
[2025-07-28 01:20:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1024, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.49, #queue-req: 0, 
[2025-07-28 01:20:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1283, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.07, #queue-req: 0, 
[2025-07-28 01:20:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1064, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.56, #queue-req: 0, 
[2025-07-28 01:20:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1323, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.20, #queue-req: 0, 
[2025-07-28 01:20:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1104, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.04, #queue-req: 0, 
[2025-07-28 01:20:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1363, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.05, #queue-req: 0, 
[2025-07-28 01:20:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1144, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.99, #queue-req: 0, 
[2025-07-28 01:20:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1403, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.12, #queue-req: 0, 
[2025-07-28 01:20:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1184, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.23, #queue-req: 0, 
[2025-07-28 01:20:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1443, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.16, #queue-req: 0, 
[2025-07-28 01:20:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1224, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.78, #queue-req: 0, 
[2025-07-28 01:20:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1483, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.13, #queue-req: 0, 
[2025-07-28 01:20:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1264, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.16, #queue-req: 0, 
[2025-07-28 01:20:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1523, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.15, #queue-req: 0, 
[2025-07-28 01:20:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1304, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.15, #queue-req: 0, 
[2025-07-28 01:20:32] INFO:     127.0.0.1:36106 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:20:32 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 175, #cached-token: 89, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:20:33 DP0 TP0] Decode batch. #running-req: 2, #token: 1760, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.45, #queue-req: 0, 
[2025-07-28 01:20:33 DP0 TP0] Decode batch. #running-req: 2, #token: 1840, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.85, #queue-req: 0, 
[2025-07-28 01:20:33 DP0 TP0] Decode batch. #running-req: 2, #token: 1920, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.89, #queue-req: 0, 
[2025-07-28 01:20:33 DP0 TP0] Decode batch. #running-req: 2, #token: 2000, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.94, #queue-req: 0, 
[2025-07-28 01:20:34 DP0 TP0] Decode batch. #running-req: 2, #token: 2080, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.25, #queue-req: 0, 
[2025-07-28 01:20:34 DP0 TP0] Decode batch. #running-req: 2, #token: 2160, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.66, #queue-req: 0, 
[2025-07-28 01:20:34 DP0 TP0] Decode batch. #running-req: 2, #token: 2240, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.62, #queue-req: 0, 
[2025-07-28 01:20:34 DP0 TP0] Decode batch. #running-req: 2, #token: 2320, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.54, #queue-req: 0, 
[2025-07-28 01:20:35 DP0 TP0] Decode batch. #running-req: 2, #token: 2400, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.81, #queue-req: 0, 
[2025-07-28 01:20:35 DP0 TP0] Decode batch. #running-req: 2, #token: 2480, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.54, #queue-req: 0, 
[2025-07-28 01:20:35 DP0 TP0] Decode batch. #running-req: 2, #token: 2560, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.71, #queue-req: 0, 
[2025-07-28 01:20:35 DP0 TP0] Decode batch. #running-req: 2, #token: 2640, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.14, #queue-req: 0, 
[2025-07-28 01:20:36 DP0 TP0] Decode batch. #running-req: 2, #token: 2720, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.88, #queue-req: 0, 
[2025-07-28 01:20:36 DP0 TP0] Decode batch. #running-req: 2, #token: 2800, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.66, #queue-req: 0, 
[2025-07-28 01:20:36 DP0 TP0] Decode batch. #running-req: 2, #token: 2880, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.00, #queue-req: 0, 
[2025-07-28 01:20:36 DP0 TP0] Decode batch. #running-req: 2, #token: 2960, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.97, #queue-req: 0, 
[2025-07-28 01:20:37 DP0 TP0] Decode batch. #running-req: 2, #token: 3040, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.83, #queue-req: 0, 
[2025-07-28 01:20:37 DP0 TP0] Decode batch. #running-req: 2, #token: 3120, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.37, #queue-req: 0, 
[2025-07-28 01:20:37 DP0 TP0] Decode batch. #running-req: 2, #token: 3200, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.12, #queue-req: 0, 
[2025-07-28 01:20:37 DP0 TP0] Decode batch. #running-req: 2, #token: 3280, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.26, #queue-req: 0, 
[2025-07-28 01:20:38 DP0 TP0] Decode batch. #running-req: 2, #token: 3360, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.08, #queue-req: 0, 
[2025-07-28 01:20:38 DP0 TP0] Decode batch. #running-req: 2, #token: 3440, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.48, #queue-req: 0, 
[2025-07-28 01:20:38 DP0 TP0] Decode batch. #running-req: 2, #token: 3520, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.57, #queue-req: 0, 
[2025-07-28 01:20:38 DP0 TP0] Decode batch. #running-req: 2, #token: 3600, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.11, #queue-req: 0, 
[2025-07-28 01:20:39 DP0 TP0] Decode batch. #running-req: 2, #token: 3680, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.27, #queue-req: 0, 
[2025-07-28 01:20:39 DP0 TP0] Decode batch. #running-req: 2, #token: 3760, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.80, #queue-req: 0, 
[2025-07-28 01:20:39 DP0 TP0] Decode batch. #running-req: 2, #token: 3840, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.47, #queue-req: 0, 
[2025-07-28 01:20:39 DP0 TP0] Decode batch. #running-req: 2, #token: 3920, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.40, #queue-req: 0, 
[2025-07-28 01:20:40 DP0 TP0] Decode batch. #running-req: 2, #token: 4000, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.74, #queue-req: 0, 
[2025-07-28 01:20:40 DP0 TP0] Decode batch. #running-req: 2, #token: 4080, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.69, #queue-req: 0, 
[2025-07-28 01:20:40 DP0 TP0] Decode batch. #running-req: 2, #token: 4160, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.07, #queue-req: 0, 
[2025-07-28 01:20:40 DP0 TP0] Decode batch. #running-req: 2, #token: 4240, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.56, #queue-req: 0, 
[2025-07-28 01:20:41 DP0 TP0] Decode batch. #running-req: 2, #token: 4320, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.95, #queue-req: 0, 
[2025-07-28 01:20:41 DP0 TP0] Decode batch. #running-req: 2, #token: 4400, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.12, #queue-req: 0, 
[2025-07-28 01:20:41 DP0 TP0] Decode batch. #running-req: 2, #token: 4480, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.17, #queue-req: 0, 
[2025-07-28 01:20:41 DP0 TP0] Decode batch. #running-req: 2, #token: 4560, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.75, #queue-req: 0, 
[2025-07-28 01:20:42 DP0 TP0] Decode batch. #running-req: 2, #token: 4640, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.13, #queue-req: 0, 
[2025-07-28 01:20:42 DP0 TP0] Decode batch. #running-req: 2, #token: 4720, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.50, #queue-req: 0, 
[2025-07-28 01:20:42 DP0 TP0] Decode batch. #running-req: 2, #token: 4800, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.21, #queue-req: 0, 
[2025-07-28 01:20:42 DP0 TP0] Decode batch. #running-req: 2, #token: 4880, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.66, #queue-req: 0, 
[2025-07-28 01:20:43 DP0 TP0] Decode batch. #running-req: 2, #token: 4960, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.23, #queue-req: 0, 
[2025-07-28 01:20:43 DP0 TP0] Decode batch. #running-req: 2, #token: 5040, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.93, #queue-req: 0, 
[2025-07-28 01:20:43 DP0 TP0] Decode batch. #running-req: 2, #token: 5120, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.18, #queue-req: 0, 
[2025-07-28 01:20:43 DP0 TP0] Decode batch. #running-req: 2, #token: 5200, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.52, #queue-req: 0, 
[2025-07-28 01:20:44 DP0 TP0] Decode batch. #running-req: 2, #token: 5280, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.61, #queue-req: 0, 
[2025-07-28 01:20:44] INFO:     127.0.0.1:36092 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:20:44 DP0 TP0] Decode batch. #running-req: 1, #token: 2084, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.53, #queue-req: 0, 
[2025-07-28 01:20:44 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 94, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:20:44 DP0 TP0] Decode batch. #running-req: 1, #token: 2124, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.69, #queue-req: 0, 
[2025-07-28 01:20:44] INFO:     127.0.0.1:41888 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:20:44 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 115, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:20:44 DP1 TP0] Decode batch. #running-req: 1, #token: 211, token usage: 0.00, cuda graph: True, gen throughput (token/s): 3.32, #queue-req: 0, 
[2025-07-28 01:20:44 DP0 TP0] Decode batch. #running-req: 1, #token: 221, token usage: 0.00, cuda graph: True, gen throughput (token/s): 146.57, #queue-req: 0, 
[2025-07-28 01:20:44 DP1 TP0] Decode batch. #running-req: 1, #token: 251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.48, #queue-req: 0, 
[2025-07-28 01:20:45 DP0 TP0] Decode batch. #running-req: 1, #token: 261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.35, #queue-req: 0, 
[2025-07-28 01:20:45 DP1 TP0] Decode batch. #running-req: 1, #token: 291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.61, #queue-req: 0, 
[2025-07-28 01:20:45 DP0 TP0] Decode batch. #running-req: 1, #token: 301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.22, #queue-req: 0, 
[2025-07-28 01:20:45 DP1 TP0] Decode batch. #running-req: 1, #token: 331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.53, #queue-req: 0, 
[2025-07-28 01:20:45 DP0 TP0] Decode batch. #running-req: 1, #token: 341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.23, #queue-req: 0, 
[2025-07-28 01:20:45 DP1 TP0] Decode batch. #running-req: 1, #token: 371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.98, #queue-req: 0, 
[2025-07-28 01:20:45 DP0 TP0] Decode batch. #running-req: 1, #token: 381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.02, #queue-req: 0, 
[2025-07-28 01:20:45 DP1 TP0] Decode batch. #running-req: 1, #token: 411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.22, #queue-req: 0, 
[2025-07-28 01:20:45 DP0 TP0] Decode batch. #running-req: 1, #token: 421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.04, #queue-req: 0, 
[2025-07-28 01:20:45 DP1 TP0] Decode batch. #running-req: 1, #token: 451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.88, #queue-req: 0, 
[2025-07-28 01:20:46 DP0 TP0] Decode batch. #running-req: 1, #token: 461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.97, #queue-req: 0, 
[2025-07-28 01:20:46 DP1 TP0] Decode batch. #running-req: 1, #token: 491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.97, #queue-req: 0, 
[2025-07-28 01:20:46 DP0 TP0] Decode batch. #running-req: 1, #token: 501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.07, #queue-req: 0, 
[2025-07-28 01:20:46 DP1 TP0] Decode batch. #running-req: 1, #token: 531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.99, #queue-req: 0, 
[2025-07-28 01:20:46 DP0 TP0] Decode batch. #running-req: 1, #token: 541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.77, #queue-req: 0, 
[2025-07-28 01:20:46 DP1 TP0] Decode batch. #running-req: 1, #token: 571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.99, #queue-req: 0, 
[2025-07-28 01:20:46 DP0 TP0] Decode batch. #running-req: 1, #token: 581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.82, #queue-req: 0, 
[2025-07-28 01:20:46 DP1 TP0] Decode batch. #running-req: 1, #token: 611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.83, #queue-req: 0, 
[2025-07-28 01:20:46 DP0 TP0] Decode batch. #running-req: 1, #token: 621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.77, #queue-req: 0, 
[2025-07-28 01:20:46 DP1 TP0] Decode batch. #running-req: 1, #token: 651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.93, #queue-req: 0, 
[2025-07-28 01:20:47 DP0 TP0] Decode batch. #running-req: 1, #token: 661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.86, #queue-req: 0, 
[2025-07-28 01:20:47 DP1 TP0] Decode batch. #running-req: 1, #token: 691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.06, #queue-req: 0, 
[2025-07-28 01:20:47 DP0 TP0] Decode batch. #running-req: 1, #token: 701, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.79, #queue-req: 0, 
[2025-07-28 01:20:47 DP1 TP0] Decode batch. #running-req: 1, #token: 731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.03, #queue-req: 0, 
[2025-07-28 01:20:47 DP0 TP0] Decode batch. #running-req: 1, #token: 741, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.84, #queue-req: 0, 
[2025-07-28 01:20:47 DP1 TP0] Decode batch. #running-req: 1, #token: 771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.90, #queue-req: 0, 
[2025-07-28 01:20:47 DP0 TP0] Decode batch. #running-req: 1, #token: 781, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.76, #queue-req: 0, 
[2025-07-28 01:20:47 DP1 TP0] Decode batch. #running-req: 1, #token: 811, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.89, #queue-req: 0, 
[2025-07-28 01:20:47 DP0 TP0] Decode batch. #running-req: 1, #token: 821, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.80, #queue-req: 0, 
[2025-07-28 01:20:47 DP1 TP0] Decode batch. #running-req: 1, #token: 851, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.79, #queue-req: 0, 
[2025-07-28 01:20:47 DP0 TP0] Decode batch. #running-req: 1, #token: 861, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.82, #queue-req: 0, 
[2025-07-28 01:20:48 DP1 TP0] Decode batch. #running-req: 1, #token: 891, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.98, #queue-req: 0, 
[2025-07-28 01:20:48 DP0 TP0] Decode batch. #running-req: 1, #token: 901, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.73, #queue-req: 0, 
[2025-07-28 01:20:48 DP1 TP0] Decode batch. #running-req: 1, #token: 931, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.85, #queue-req: 0, 
[2025-07-28 01:20:48 DP0 TP0] Decode batch. #running-req: 1, #token: 941, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.59, #queue-req: 0, 
[2025-07-28 01:20:48 DP1 TP0] Decode batch. #running-req: 1, #token: 971, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.63, #queue-req: 0, 
[2025-07-28 01:20:48] INFO:     127.0.0.1:48150 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:20:48 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 150, #cached-token: 88, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:20:48 DP1 TP0] Decode batch. #running-req: 2, #token: 1185, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.38, #queue-req: 0, 
[2025-07-28 01:20:48 DP1 TP0] Decode batch. #running-req: 2, #token: 1265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.25, #queue-req: 0, 
[2025-07-28 01:20:49 DP1 TP0] Decode batch. #running-req: 2, #token: 1345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.34, #queue-req: 0, 
[2025-07-28 01:20:49 DP1 TP0] Decode batch. #running-req: 2, #token: 1425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.06, #queue-req: 0, 
[2025-07-28 01:20:49 DP1 TP0] Decode batch. #running-req: 2, #token: 1505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.49, #queue-req: 0, 
[2025-07-28 01:20:49 DP1 TP0] Decode batch. #running-req: 2, #token: 1585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.88, #queue-req: 0, 
[2025-07-28 01:20:50 DP1 TP0] Decode batch. #running-req: 2, #token: 1665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.21, #queue-req: 0, 
[2025-07-28 01:20:50 DP1 TP0] Decode batch. #running-req: 2, #token: 1745, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.60, #queue-req: 0, 
[2025-07-28 01:20:50 DP1 TP0] Decode batch. #running-req: 2, #token: 1825, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.41, #queue-req: 0, 
[2025-07-28 01:20:50 DP1 TP0] Decode batch. #running-req: 2, #token: 1905, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.55, #queue-req: 0, 
[2025-07-28 01:20:51] INFO:     127.0.0.1:48134 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:20:51 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 148, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:20:51 DP1 TP0] Decode batch. #running-req: 1, #token: 659, token usage: 0.00, cuda graph: True, gen throughput (token/s): 262.48, #queue-req: 0, 
[2025-07-28 01:20:51 DP0 TP0] Decode batch. #running-req: 1, #token: 252, token usage: 0.00, cuda graph: True, gen throughput (token/s): 13.71, #queue-req: 0, 
[2025-07-28 01:20:51 DP1 TP0] Decode batch. #running-req: 1, #token: 699, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.17, #queue-req: 0, 
[2025-07-28 01:20:51 DP0 TP0] Decode batch. #running-req: 1, #token: 292, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.28, #queue-req: 0, 
[2025-07-28 01:20:51 DP1 TP0] Decode batch. #running-req: 1, #token: 739, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.04, #queue-req: 0, 
[2025-07-28 01:20:51 DP0 TP0] Decode batch. #running-req: 1, #token: 332, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.45, #queue-req: 0, 
[2025-07-28 01:20:51 DP1 TP0] Decode batch. #running-req: 1, #token: 779, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.35, #queue-req: 0, 
[2025-07-28 01:20:51 DP0 TP0] Decode batch. #running-req: 1, #token: 372, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.43, #queue-req: 0, 
[2025-07-28 01:20:51 DP1 TP0] Decode batch. #running-req: 1, #token: 819, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.16, #queue-req: 0, 
[2025-07-28 01:20:52 DP0 TP0] Decode batch. #running-req: 1, #token: 412, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.75, #queue-req: 0, 
[2025-07-28 01:20:52 DP1 TP0] Decode batch. #running-req: 1, #token: 859, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.99, #queue-req: 0, 
[2025-07-28 01:20:52 DP0 TP0] Decode batch. #running-req: 1, #token: 452, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.75, #queue-req: 0, 
[2025-07-28 01:20:52 DP1 TP0] Decode batch. #running-req: 1, #token: 899, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.93, #queue-req: 0, 
[2025-07-28 01:20:52 DP0 TP0] Decode batch. #running-req: 1, #token: 492, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.62, #queue-req: 0, 
[2025-07-28 01:20:52 DP1 TP0] Decode batch. #running-req: 1, #token: 939, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.77, #queue-req: 0, 
[2025-07-28 01:20:52 DP0 TP0] Decode batch. #running-req: 1, #token: 532, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.87, #queue-req: 0, 
[2025-07-28 01:20:52 DP1 TP0] Decode batch. #running-req: 1, #token: 979, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.81, #queue-req: 0, 
[2025-07-28 01:20:52 DP0 TP0] Decode batch. #running-req: 1, #token: 572, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.84, #queue-req: 0, 
[2025-07-28 01:20:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1019, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.81, #queue-req: 0, 
[2025-07-28 01:20:53 DP0 TP0] Decode batch. #running-req: 1, #token: 612, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.67, #queue-req: 0, 
[2025-07-28 01:20:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1059, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.11, #queue-req: 0, 
[2025-07-28 01:20:53 DP0 TP0] Decode batch. #running-req: 1, #token: 652, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.60, #queue-req: 0, 
[2025-07-28 01:20:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1099, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.19, #queue-req: 0, 
[2025-07-28 01:20:53 DP0 TP0] Decode batch. #running-req: 1, #token: 692, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.82, #queue-req: 0, 
[2025-07-28 01:20:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1139, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.12, #queue-req: 0, 
[2025-07-28 01:20:53 DP0 TP0] Decode batch. #running-req: 1, #token: 732, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.95, #queue-req: 0, 
[2025-07-28 01:20:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1179, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.01, #queue-req: 0, 
[2025-07-28 01:20:53 DP0 TP0] Decode batch. #running-req: 1, #token: 772, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.89, #queue-req: 0, 
[2025-07-28 01:20:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1219, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.00, #queue-req: 0, 
[2025-07-28 01:20:53 DP0 TP0] Decode batch. #running-req: 1, #token: 812, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.84, #queue-req: 0, 
[2025-07-28 01:20:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1259, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.01, #queue-req: 0, 
[2025-07-28 01:20:54 DP0 TP0] Decode batch. #running-req: 1, #token: 852, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.78, #queue-req: 0, 
[2025-07-28 01:20:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1299, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.73, #queue-req: 0, 
[2025-07-28 01:20:54 DP0 TP0] Decode batch. #running-req: 1, #token: 892, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.77, #queue-req: 0, 
[2025-07-28 01:20:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.48, #queue-req: 0, 
[2025-07-28 01:20:54 DP0 TP0] Decode batch. #running-req: 1, #token: 932, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.38, #queue-req: 0, 
[2025-07-28 01:20:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1379, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.75, #queue-req: 0, 
[2025-07-28 01:20:54 DP0 TP0] Decode batch. #running-req: 1, #token: 972, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.75, #queue-req: 0, 
[2025-07-28 01:20:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1419, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.68, #queue-req: 0, 
[2025-07-28 01:20:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1012, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.69, #queue-req: 0, 
[2025-07-28 01:20:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1459, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.68, #queue-req: 0, 
[2025-07-28 01:20:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1052, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.18, #queue-req: 0, 
[2025-07-28 01:20:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1499, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.64, #queue-req: 0, 
[2025-07-28 01:20:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1092, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.99, #queue-req: 0, 
[2025-07-28 01:20:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1539, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.55, #queue-req: 0, 
[2025-07-28 01:20:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1132, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.88, #queue-req: 0, 
[2025-07-28 01:20:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1579, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.41, #queue-req: 0, 
[2025-07-28 01:20:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1172, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.02, #queue-req: 0, 
[2025-07-28 01:20:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1619, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.43, #queue-req: 0, 
[2025-07-28 01:20:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1212, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.79, #queue-req: 0, 
[2025-07-28 01:20:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1659, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.52, #queue-req: 0, 
[2025-07-28 01:20:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1252, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.72, #queue-req: 0, 
[2025-07-28 01:20:56] INFO:     127.0.0.1:48174 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:20:56 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 160, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:20:56] INFO:     127.0.0.1:48162 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:20:56 DP1 TP0] Decode batch. #running-req: 2, #token: 260, token usage: 0.00, cuda graph: True, gen throughput (token/s): 189.36, #queue-req: 0, 
[2025-07-28 01:20:56 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 114, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:20:56 DP1 TP0] Decode batch. #running-req: 1, #token: 300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.83, #queue-req: 0, 
[2025-07-28 01:20:56 DP0 TP0] Decode batch. #running-req: 1, #token: 235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 87.50, #queue-req: 0, 
[2025-07-28 01:20:56 DP1 TP0] Decode batch. #running-req: 1, #token: 340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.56, #queue-req: 0, 
[2025-07-28 01:20:56 DP0 TP0] Decode batch. #running-req: 1, #token: 275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.81, #queue-req: 0, 
[2025-07-28 01:20:56 DP1 TP0] Decode batch. #running-req: 1, #token: 380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.52, #queue-req: 0, 
[2025-07-28 01:20:56 DP0 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.15, #queue-req: 0, 
[2025-07-28 01:20:56 DP1 TP0] Decode batch. #running-req: 1, #token: 420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.39, #queue-req: 0, 
[2025-07-28 01:20:57 DP0 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.70, #queue-req: 0, 
[2025-07-28 01:20:57 DP1 TP0] Decode batch. #running-req: 1, #token: 460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.30, #queue-req: 0, 
[2025-07-28 01:20:57 DP0 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.89, #queue-req: 0, 
[2025-07-28 01:20:57 DP1 TP0] Decode batch. #running-req: 1, #token: 500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.29, #queue-req: 0, 
[2025-07-28 01:20:57 DP0 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.89, #queue-req: 0, 
[2025-07-28 01:20:57 DP1 TP0] Decode batch. #running-req: 1, #token: 540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.09, #queue-req: 0, 
[2025-07-28 01:20:57 DP0 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.04, #queue-req: 0, 
[2025-07-28 01:20:57 DP1 TP0] Decode batch. #running-req: 1, #token: 580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.25, #queue-req: 0, 
[2025-07-28 01:20:57 DP0 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.99, #queue-req: 0, 
[2025-07-28 01:20:57 DP1 TP0] Decode batch. #running-req: 1, #token: 620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.10, #queue-req: 0, 
[2025-07-28 01:20:58 DP0 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.84, #queue-req: 0, 
[2025-07-28 01:20:58 DP1 TP0] Decode batch. #running-req: 1, #token: 660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.01, #queue-req: 0, 
[2025-07-28 01:20:58 DP0 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.74, #queue-req: 0, 
[2025-07-28 01:20:58 DP1 TP0] Decode batch. #running-req: 1, #token: 700, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.12, #queue-req: 0, 
[2025-07-28 01:20:58 DP0 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.82, #queue-req: 0, 
[2025-07-28 01:20:58 DP1 TP0] Decode batch. #running-req: 1, #token: 740, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.04, #queue-req: 0, 
[2025-07-28 01:20:58 DP0 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.81, #queue-req: 0, 
[2025-07-28 01:20:58 DP1 TP0] Decode batch. #running-req: 1, #token: 780, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.21, #queue-req: 0, 
[2025-07-28 01:20:58 DP0 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.94, #queue-req: 0, 
[2025-07-28 01:20:58 DP1 TP0] Decode batch. #running-req: 1, #token: 820, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.13, #queue-req: 0, 
[2025-07-28 01:20:58 DP0 TP0] Decode batch. #running-req: 1, #token: 755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.86, #queue-req: 0, 
[2025-07-28 01:20:59 DP1 TP0] Decode batch. #running-req: 1, #token: 860, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.04, #queue-req: 0, 
[2025-07-28 01:20:59 DP0 TP0] Decode batch. #running-req: 1, #token: 795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.96, #queue-req: 0, 
[2025-07-28 01:20:59 DP1 TP0] Decode batch. #running-req: 1, #token: 900, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.04, #queue-req: 0, 
[2025-07-28 01:20:59 DP0 TP0] Decode batch. #running-req: 1, #token: 835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.92, #queue-req: 0, 
[2025-07-28 01:20:59 DP1 TP0] Decode batch. #running-req: 1, #token: 940, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.05, #queue-req: 0, 
[2025-07-28 01:20:59 DP0 TP0] Decode batch. #running-req: 1, #token: 875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.79, #queue-req: 0, 
[2025-07-28 01:20:59 DP1 TP0] Decode batch. #running-req: 1, #token: 980, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.93, #queue-req: 0, 
[2025-07-28 01:20:59 DP0 TP0] Decode batch. #running-req: 1, #token: 915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.92, #queue-req: 0, 
[2025-07-28 01:20:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1020, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.86, #queue-req: 0, 
[2025-07-28 01:20:59 DP0 TP0] Decode batch. #running-req: 1, #token: 955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.08, #queue-req: 0, 
[2025-07-28 01:21:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1060, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.43, #queue-req: 0, 
[2025-07-28 01:21:00 DP0 TP0] Decode batch. #running-req: 1, #token: 995, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.90, #queue-req: 0, 
[2025-07-28 01:21:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1100, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.46, #queue-req: 0, 
[2025-07-28 01:21:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.60, #queue-req: 0, 
[2025-07-28 01:21:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1140, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.33, #queue-req: 0, 
[2025-07-28 01:21:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1075, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.09, #queue-req: 0, 
[2025-07-28 01:21:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1180, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.28, #queue-req: 0, 
[2025-07-28 01:21:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.99, #queue-req: 0, 
[2025-07-28 01:21:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1220, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.27, #queue-req: 0, 
[2025-07-28 01:21:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1155, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.79, #queue-req: 0, 
[2025-07-28 01:21:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1260, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.17, #queue-req: 0, 
[2025-07-28 01:21:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.83, #queue-req: 0, 
[2025-07-28 01:21:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.69, #queue-req: 0, 
[2025-07-28 01:21:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.87, #queue-req: 0, 
[2025-07-28 01:21:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.04, #queue-req: 0, 
[2025-07-28 01:21:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.86, #queue-req: 0, 
[2025-07-28 01:21:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.00, #queue-req: 0, 
[2025-07-28 01:21:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.63, #queue-req: 0, 
[2025-07-28 01:21:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.96, #queue-req: 0, 
[2025-07-28 01:21:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.59, #queue-req: 0, 
[2025-07-28 01:21:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.91, #queue-req: 0, 
[2025-07-28 01:21:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.77, #queue-req: 0, 
[2025-07-28 01:21:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.97, #queue-req: 0, 
[2025-07-28 01:21:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.70, #queue-req: 0, 
[2025-07-28 01:21:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.06, #queue-req: 0, 
[2025-07-28 01:21:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.37, #queue-req: 0, 
[2025-07-28 01:21:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.95, #queue-req: 0, 
[2025-07-28 01:21:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.49, #queue-req: 0, 
[2025-07-28 01:21:02] INFO:     127.0.0.1:60402 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:21:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.97, #queue-req: 0, 
[2025-07-28 01:21:02 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 80, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:21:03 DP1 TP0] Decode batch. #running-req: 2, #token: 1778, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.31, #queue-req: 0, 
[2025-07-28 01:21:03 DP1 TP0] Decode batch. #running-req: 2, #token: 1858, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.03, #queue-req: 0, 
[2025-07-28 01:21:03 DP1 TP0] Decode batch. #running-req: 2, #token: 1938, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.67, #queue-req: 0, 
[2025-07-28 01:21:03 DP1 TP0] Decode batch. #running-req: 2, #token: 2018, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.83, #queue-req: 0, 
[2025-07-28 01:21:04 DP1 TP0] Decode batch. #running-req: 2, #token: 2098, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.68, #queue-req: 0, 
[2025-07-28 01:21:04 DP1 TP0] Decode batch. #running-req: 2, #token: 2178, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.25, #queue-req: 0, 
[2025-07-28 01:21:04 DP1 TP0] Decode batch. #running-req: 2, #token: 2258, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.16, #queue-req: 0, 
[2025-07-28 01:21:04 DP1 TP0] Decode batch. #running-req: 2, #token: 2338, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.63, #queue-req: 0, 
[2025-07-28 01:21:05 DP1 TP0] Decode batch. #running-req: 2, #token: 2418, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.98, #queue-req: 0, 
[2025-07-28 01:21:05 DP1 TP0] Decode batch. #running-req: 2, #token: 2498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.19, #queue-req: 0, 
[2025-07-28 01:21:05] INFO:     127.0.0.1:60392 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:21:05 DP1 TP0] Decode batch. #running-req: 1, #token: 603, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.22, #queue-req: 0, 
[2025-07-28 01:21:05 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 136, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:21:05 DP1 TP0] Decode batch. #running-req: 1, #token: 643, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.33, #queue-req: 0, 
[2025-07-28 01:21:05 DP0 TP0] Decode batch. #running-req: 1, #token: 240, token usage: 0.00, cuda graph: True, gen throughput (token/s): 12.56, #queue-req: 0, 
[2025-07-28 01:21:05 DP1 TP0] Decode batch. #running-req: 1, #token: 683, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.94, #queue-req: 0, 
[2025-07-28 01:21:05 DP0 TP0] Decode batch. #running-req: 1, #token: 280, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.29, #queue-req: 0, 
[2025-07-28 01:21:06 DP1 TP0] Decode batch. #running-req: 1, #token: 723, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.00, #queue-req: 0, 
[2025-07-28 01:21:06 DP0 TP0] Decode batch. #running-req: 1, #token: 320, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.49, #queue-req: 0, 
[2025-07-28 01:21:06 DP1 TP0] Decode batch. #running-req: 1, #token: 763, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.83, #queue-req: 0, 
[2025-07-28 01:21:06 DP0 TP0] Decode batch. #running-req: 1, #token: 360, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.90, #queue-req: 0, 
[2025-07-28 01:21:06 DP1 TP0] Decode batch. #running-req: 1, #token: 803, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.07, #queue-req: 0, 
[2025-07-28 01:21:06 DP0 TP0] Decode batch. #running-req: 1, #token: 400, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.63, #queue-req: 0, 
[2025-07-28 01:21:06 DP1 TP0] Decode batch. #running-req: 1, #token: 843, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.37, #queue-req: 0, 
[2025-07-28 01:21:06 DP0 TP0] Decode batch. #running-req: 1, #token: 440, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.42, #queue-req: 0, 
[2025-07-28 01:21:06 DP1 TP0] Decode batch. #running-req: 1, #token: 883, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.72, #queue-req: 0, 
[2025-07-28 01:21:06 DP0 TP0] Decode batch. #running-req: 1, #token: 480, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.58, #queue-req: 0, 
[2025-07-28 01:21:07 DP1 TP0] Decode batch. #running-req: 1, #token: 923, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.80, #queue-req: 0, 
[2025-07-28 01:21:07 DP0 TP0] Decode batch. #running-req: 1, #token: 520, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.63, #queue-req: 0, 
[2025-07-28 01:21:07 DP1 TP0] Decode batch. #running-req: 1, #token: 963, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.81, #queue-req: 0, 
[2025-07-28 01:21:07 DP0 TP0] Decode batch. #running-req: 1, #token: 560, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.48, #queue-req: 0, 
[2025-07-28 01:21:07 DP1 TP0] Decode batch. #running-req: 1, #token: 1003, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.90, #queue-req: 0, 
[2025-07-28 01:21:07 DP0 TP0] Decode batch. #running-req: 1, #token: 600, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.65, #queue-req: 0, 
[2025-07-28 01:21:07 DP1 TP0] Decode batch. #running-req: 1, #token: 1043, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.67, #queue-req: 0, 
[2025-07-28 01:21:07 DP0 TP0] Decode batch. #running-req: 1, #token: 640, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.67, #queue-req: 0, 
[2025-07-28 01:21:07 DP1 TP0] Decode batch. #running-req: 1, #token: 1083, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.34, #queue-req: 0, 
[2025-07-28 01:21:07 DP0 TP0] Decode batch. #running-req: 1, #token: 680, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.72, #queue-req: 0, 
[2025-07-28 01:21:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1123, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.40, #queue-req: 0, 
[2025-07-28 01:21:08 DP0 TP0] Decode batch. #running-req: 1, #token: 720, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.68, #queue-req: 0, 
[2025-07-28 01:21:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1163, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.09, #queue-req: 0, 
[2025-07-28 01:21:08 DP0 TP0] Decode batch. #running-req: 1, #token: 760, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.74, #queue-req: 0, 
[2025-07-28 01:21:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1203, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.42, #queue-req: 0, 
[2025-07-28 01:21:08 DP0 TP0] Decode batch. #running-req: 1, #token: 800, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.71, #queue-req: 0, 
[2025-07-28 01:21:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1243, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.29, #queue-req: 0, 
[2025-07-28 01:21:08 DP0 TP0] Decode batch. #running-req: 1, #token: 840, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.57, #queue-req: 0, 
[2025-07-28 01:21:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1283, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.28, #queue-req: 0, 
[2025-07-28 01:21:08 DP0 TP0] Decode batch. #running-req: 1, #token: 880, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.32, #queue-req: 0, 
[2025-07-28 01:21:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1323, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.10, #queue-req: 0, 
[2025-07-28 01:21:09 DP0 TP0] Decode batch. #running-req: 1, #token: 920, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.51, #queue-req: 0, 
[2025-07-28 01:21:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1363, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.09, #queue-req: 0, 
[2025-07-28 01:21:09 DP0 TP0] Decode batch. #running-req: 1, #token: 960, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.59, #queue-req: 0, 
[2025-07-28 01:21:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1403, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.24, #queue-req: 0, 
[2025-07-28 01:21:09 DP0 TP0] Decode batch. #running-req: 1, #token: 1000, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.59, #queue-req: 0, 
[2025-07-28 01:21:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1443, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.83, #queue-req: 0, 
[2025-07-28 01:21:09 DP0 TP0] Decode batch. #running-req: 1, #token: 1040, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.01, #queue-req: 0, 
[2025-07-28 01:21:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1483, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.97, #queue-req: 0, 
[2025-07-28 01:21:09 DP0 TP0] Decode batch. #running-req: 1, #token: 1080, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.11, #queue-req: 0, 
[2025-07-28 01:21:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1523, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.98, #queue-req: 0, 
[2025-07-28 01:21:09 DP0 TP0] Decode batch. #running-req: 1, #token: 1120, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.72, #queue-req: 0, 
[2025-07-28 01:21:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1563, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.83, #queue-req: 0, 
[2025-07-28 01:21:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1160, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.84, #queue-req: 0, 
[2025-07-28 01:21:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1603, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.80, #queue-req: 0, 
[2025-07-28 01:21:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1200, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.97, #queue-req: 0, 
[2025-07-28 01:21:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1643, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.69, #queue-req: 0, 
[2025-07-28 01:21:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1240, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.58, #queue-req: 0, 
[2025-07-28 01:21:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1683, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.72, #queue-req: 0, 
[2025-07-28 01:21:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1280, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.72, #queue-req: 0, 
[2025-07-28 01:21:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1723, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.83, #queue-req: 0, 
[2025-07-28 01:21:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1320, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.64, #queue-req: 0, 
[2025-07-28 01:21:11 DP1 TP0] Decode batch. #running-req: 1, #token: 1763, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.77, #queue-req: 0, 
[2025-07-28 01:21:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1360, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.59, #queue-req: 0, 
[2025-07-28 01:21:11] INFO:     127.0.0.1:54002 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:21:11 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 137, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:21:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1400, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.50, #queue-req: 0, 
[2025-07-28 01:21:11 DP1 TP0] Decode batch. #running-req: 1, #token: 253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 146.90, #queue-req: 0, 
[2025-07-28 01:21:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1440, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.38, #queue-req: 0, 
[2025-07-28 01:21:11 DP1 TP0] Decode batch. #running-req: 1, #token: 293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.45, #queue-req: 0, 
[2025-07-28 01:21:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1480, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.54, #queue-req: 0, 
[2025-07-28 01:21:11 DP1 TP0] Decode batch. #running-req: 1, #token: 333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.62, #queue-req: 0, 
[2025-07-28 01:21:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1520, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.39, #queue-req: 0, 
[2025-07-28 01:21:11 DP1 TP0] Decode batch. #running-req: 1, #token: 373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.37, #queue-req: 0, 
[2025-07-28 01:21:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1560, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.45, #queue-req: 0, 
[2025-07-28 01:21:12 DP1 TP0] Decode batch. #running-req: 1, #token: 413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.22, #queue-req: 0, 
[2025-07-28 01:21:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1600, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.19, #queue-req: 0, 
[2025-07-28 01:21:12 DP1 TP0] Decode batch. #running-req: 1, #token: 453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.35, #queue-req: 0, 
[2025-07-28 01:21:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1640, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.27, #queue-req: 0, 
[2025-07-28 01:21:12 DP1 TP0] Decode batch. #running-req: 1, #token: 493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.46, #queue-req: 0, 
[2025-07-28 01:21:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1680, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.43, #queue-req: 0, 
[2025-07-28 01:21:12 DP1 TP0] Decode batch. #running-req: 1, #token: 533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.27, #queue-req: 0, 
[2025-07-28 01:21:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1720, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.42, #queue-req: 0, 
[2025-07-28 01:21:12 DP1 TP0] Decode batch. #running-req: 1, #token: 573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.06, #queue-req: 0, 
[2025-07-28 01:21:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1760, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.87, #queue-req: 0, 
[2025-07-28 01:21:13 DP1 TP0] Decode batch. #running-req: 1, #token: 613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.94, #queue-req: 0, 
[2025-07-28 01:21:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1800, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.34, #queue-req: 0, 
[2025-07-28 01:21:13 DP1 TP0] Decode batch. #running-req: 1, #token: 653, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.24, #queue-req: 0, 
[2025-07-28 01:21:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1840, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.35, #queue-req: 0, 
[2025-07-28 01:21:13 DP1 TP0] Decode batch. #running-req: 1, #token: 693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.28, #queue-req: 0, 
[2025-07-28 01:21:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1880, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.96, #queue-req: 0, 
[2025-07-28 01:21:13 DP1 TP0] Decode batch. #running-req: 1, #token: 733, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.15, #queue-req: 0, 
[2025-07-28 01:21:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1920, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.54, #queue-req: 0, 
[2025-07-28 01:21:13 DP1 TP0] Decode batch. #running-req: 1, #token: 773, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.28, #queue-req: 0, 
[2025-07-28 01:21:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1960, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.21, #queue-req: 0, 
[2025-07-28 01:21:13 DP1 TP0] Decode batch. #running-req: 1, #token: 813, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.19, #queue-req: 0, 
[2025-07-28 01:21:14 DP0 TP0] Decode batch. #running-req: 1, #token: 2000, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.51, #queue-req: 0, 
[2025-07-28 01:21:14 DP1 TP0] Decode batch. #running-req: 1, #token: 853, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.07, #queue-req: 0, 
[2025-07-28 01:21:14 DP0 TP0] Decode batch. #running-req: 1, #token: 2040, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.41, #queue-req: 0, 
[2025-07-28 01:21:14 DP1 TP0] Decode batch. #running-req: 1, #token: 893, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.22, #queue-req: 0, 
[2025-07-28 01:21:14 DP0 TP0] Decode batch. #running-req: 1, #token: 2080, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.87, #queue-req: 0, 
[2025-07-28 01:21:14 DP1 TP0] Decode batch. #running-req: 1, #token: 933, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.33, #queue-req: 0, 
[2025-07-28 01:21:14 DP1 TP0] Decode batch. #running-req: 1, #token: 973, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.16, #queue-req: 0, 
[2025-07-28 01:21:14 DP0 TP0] Decode batch. #running-req: 1, #token: 2120, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.98, #queue-req: 0, 
[2025-07-28 01:21:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1013, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.10, #queue-req: 0, 
[2025-07-28 01:21:14 DP0 TP0] Decode batch. #running-req: 1, #token: 2160, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.55, #queue-req: 0, 
[2025-07-28 01:21:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1053, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.64, #queue-req: 0, 
[2025-07-28 01:21:15 DP0 TP0] Decode batch. #running-req: 1, #token: 2200, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.47, #queue-req: 0, 
[2025-07-28 01:21:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1093, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.39, #queue-req: 0, 
[2025-07-28 01:21:15 DP0 TP0] Decode batch. #running-req: 1, #token: 2240, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.51, #queue-req: 0, 
[2025-07-28 01:21:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1133, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.33, #queue-req: 0, 
[2025-07-28 01:21:15 DP0 TP0] Decode batch. #running-req: 1, #token: 2280, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.62, #queue-req: 0, 
[2025-07-28 01:21:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1173, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.45, #queue-req: 0, 
[2025-07-28 01:21:15 DP0 TP0] Decode batch. #running-req: 1, #token: 2320, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.30, #queue-req: 0, 
[2025-07-28 01:21:15] INFO:     127.0.0.1:54028 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:21:15 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 85, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:21:15 DP0 TP0] Decode batch. #running-req: 2, #token: 2455, token usage: 0.00, cuda graph: True, gen throughput (token/s): 181.28, #queue-req: 0, 
[2025-07-28 01:21:16 DP0 TP0] Decode batch. #running-req: 2, #token: 2535, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.54, #queue-req: 0, 
[2025-07-28 01:21:16 DP0 TP0] Decode batch. #running-req: 2, #token: 2615, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.15, #queue-req: 0, 
[2025-07-28 01:21:16 DP0 TP0] Decode batch. #running-req: 2, #token: 2695, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.93, #queue-req: 0, 
[2025-07-28 01:21:17 DP0 TP0] Decode batch. #running-req: 2, #token: 2775, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.15, #queue-req: 0, 
[2025-07-28 01:21:17 DP0 TP0] Decode batch. #running-req: 2, #token: 2855, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.12, #queue-req: 0, 
[2025-07-28 01:21:17 DP0 TP0] Decode batch. #running-req: 2, #token: 2935, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.23, #queue-req: 0, 
[2025-07-28 01:21:17 DP0 TP0] Decode batch. #running-req: 2, #token: 3015, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.13, #queue-req: 0, 
[2025-07-28 01:21:18 DP0 TP0] Decode batch. #running-req: 2, #token: 3095, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.70, #queue-req: 0, 
[2025-07-28 01:21:18 DP0 TP0] Decode batch. #running-req: 2, #token: 3175, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.67, #queue-req: 0, 
[2025-07-28 01:21:18 DP0 TP0] Decode batch. #running-req: 2, #token: 3255, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.22, #queue-req: 0, 
[2025-07-28 01:21:18 DP0 TP0] Decode batch. #running-req: 2, #token: 3335, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.99, #queue-req: 0, 
[2025-07-28 01:21:19 DP0 TP0] Decode batch. #running-req: 2, #token: 3415, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.06, #queue-req: 0, 
[2025-07-28 01:21:19 DP0 TP0] Decode batch. #running-req: 2, #token: 3495, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.10, #queue-req: 0, 
[2025-07-28 01:21:19 DP0 TP0] Decode batch. #running-req: 2, #token: 3575, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.93, #queue-req: 0, 
[2025-07-28 01:21:19 DP0 TP0] Decode batch. #running-req: 2, #token: 3655, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.96, #queue-req: 0, 
[2025-07-28 01:21:20 DP0 TP0] Decode batch. #running-req: 2, #token: 3735, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.37, #queue-req: 0, 
[2025-07-28 01:21:20 DP0 TP0] Decode batch. #running-req: 2, #token: 3815, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.59, #queue-req: 0, 
[2025-07-28 01:21:20 DP0 TP0] Decode batch. #running-req: 2, #token: 3895, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.43, #queue-req: 0, 
[2025-07-28 01:21:20 DP0 TP0] Decode batch. #running-req: 2, #token: 3975, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.87, #queue-req: 0, 
[2025-07-28 01:21:21 DP0 TP0] Decode batch. #running-req: 2, #token: 4055, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.92, #queue-req: 0, 
[2025-07-28 01:21:21 DP0 TP0] Decode batch. #running-req: 2, #token: 4135, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.74, #queue-req: 0, 
[2025-07-28 01:21:21 DP0 TP0] Decode batch. #running-req: 2, #token: 4215, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.26, #queue-req: 0, 
[2025-07-28 01:21:21 DP0 TP0] Decode batch. #running-req: 2, #token: 4295, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.57, #queue-req: 0, 
[2025-07-28 01:21:22 DP0 TP0] Decode batch. #running-req: 2, #token: 4375, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.57, #queue-req: 0, 
[2025-07-28 01:21:22 DP0 TP0] Decode batch. #running-req: 2, #token: 4455, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.37, #queue-req: 0, 
[2025-07-28 01:21:22 DP0 TP0] Decode batch. #running-req: 2, #token: 4535, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.79, #queue-req: 0, 
[2025-07-28 01:21:22 DP0 TP0] Decode batch. #running-req: 2, #token: 4615, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.50, #queue-req: 0, 
[2025-07-28 01:21:23 DP0 TP0] Decode batch. #running-req: 2, #token: 4695, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.70, #queue-req: 0, 
[2025-07-28 01:21:23 DP0 TP0] Decode batch. #running-req: 2, #token: 4775, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.67, #queue-req: 0, 
[2025-07-28 01:21:23 DP0 TP0] Decode batch. #running-req: 2, #token: 4855, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.23, #queue-req: 0, 
[2025-07-28 01:21:23 DP0 TP0] Decode batch. #running-req: 2, #token: 4935, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.17, #queue-req: 0, 
[2025-07-28 01:21:24 DP0 TP0] Decode batch. #running-req: 2, #token: 5015, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.11, #queue-req: 0, 
[2025-07-28 01:21:24 DP0 TP0] Decode batch. #running-req: 2, #token: 5095, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.94, #queue-req: 0, 
[2025-07-28 01:21:24] INFO:     127.0.0.1:54016 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:21:24 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 157, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:21:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 270.77, #queue-req: 0, 
[2025-07-28 01:21:24 DP1 TP0] Decode batch. #running-req: 1, #token: 253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 4.38, #queue-req: 0, 
[2025-07-28 01:21:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.68, #queue-req: 0, 
[2025-07-28 01:21:25 DP1 TP0] Decode batch. #running-req: 1, #token: 293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 186.42, #queue-req: 0, 
[2025-07-28 01:21:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.26, #queue-req: 0, 
[2025-07-28 01:21:25 DP1 TP0] Decode batch. #running-req: 1, #token: 333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.37, #queue-req: 0, 
[2025-07-28 01:21:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.44, #queue-req: 0, 
[2025-07-28 01:21:25 DP1 TP0] Decode batch. #running-req: 1, #token: 373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.97, #queue-req: 0, 
[2025-07-28 01:21:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1700, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.33, #queue-req: 0, 
[2025-07-28 01:21:25 DP1 TP0] Decode batch. #running-req: 1, #token: 413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.32, #queue-req: 0, 
[2025-07-28 01:21:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1740, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.28, #queue-req: 0, 
[2025-07-28 01:21:25 DP1 TP0] Decode batch. #running-req: 1, #token: 453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.33, #queue-req: 0, 
[2025-07-28 01:21:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1780, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.30, #queue-req: 0, 
[2025-07-28 01:21:25 DP1 TP0] Decode batch. #running-req: 1, #token: 493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.41, #queue-req: 0, 
[2025-07-28 01:21:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1820, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.04, #queue-req: 0, 
[2025-07-28 01:21:26 DP1 TP0] Decode batch. #running-req: 1, #token: 533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.22, #queue-req: 0, 
[2025-07-28 01:21:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1860, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.36, #queue-req: 0, 
[2025-07-28 01:21:26 DP1 TP0] Decode batch. #running-req: 1, #token: 573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.16, #queue-req: 0, 
[2025-07-28 01:21:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1900, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.18, #queue-req: 0, 
[2025-07-28 01:21:26 DP1 TP0] Decode batch. #running-req: 1, #token: 613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.11, #queue-req: 0, 
[2025-07-28 01:21:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1940, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.29, #queue-req: 0, 
[2025-07-28 01:21:26 DP1 TP0] Decode batch. #running-req: 1, #token: 653, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.28, #queue-req: 0, 
[2025-07-28 01:21:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1980, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.18, #queue-req: 0, 
[2025-07-28 01:21:26 DP1 TP0] Decode batch. #running-req: 1, #token: 693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.25, #queue-req: 0, 
[2025-07-28 01:21:26 DP0 TP0] Decode batch. #running-req: 1, #token: 2020, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.91, #queue-req: 0, 
[2025-07-28 01:21:27 DP1 TP0] Decode batch. #running-req: 1, #token: 733, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.28, #queue-req: 0, 
[2025-07-28 01:21:27 DP0 TP0] Decode batch. #running-req: 1, #token: 2060, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.16, #queue-req: 0, 
[2025-07-28 01:21:27 DP1 TP0] Decode batch. #running-req: 1, #token: 773, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.17, #queue-req: 0, 
[2025-07-28 01:21:27 DP0 TP0] Decode batch. #running-req: 1, #token: 2100, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.55, #queue-req: 0, 
[2025-07-28 01:21:27 DP1 TP0] Decode batch. #running-req: 1, #token: 813, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.05, #queue-req: 0, 
[2025-07-28 01:21:27 DP0 TP0] Decode batch. #running-req: 1, #token: 2140, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.28, #queue-req: 0, 
[2025-07-28 01:21:27 DP1 TP0] Decode batch. #running-req: 1, #token: 853, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.27, #queue-req: 0, 
[2025-07-28 01:21:27 DP0 TP0] Decode batch. #running-req: 1, #token: 2180, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.51, #queue-req: 0, 
[2025-07-28 01:21:27 DP1 TP0] Decode batch. #running-req: 1, #token: 893, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.22, #queue-req: 0, 
[2025-07-28 01:21:27 DP0 TP0] Decode batch. #running-req: 1, #token: 2220, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.14, #queue-req: 0, 
[2025-07-28 01:21:28 DP1 TP0] Decode batch. #running-req: 1, #token: 933, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.16, #queue-req: 0, 
[2025-07-28 01:21:28 DP0 TP0] Decode batch. #running-req: 1, #token: 2260, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.52, #queue-req: 0, 
[2025-07-28 01:21:28] INFO:     127.0.0.1:51562 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:21:28 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 150, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:21:28 DP1 TP0] Decode batch. #running-req: 1, #token: 973, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.17, #queue-req: 0, 
[2025-07-28 01:21:28 DP0 TP0] Decode batch. #running-req: 1, #token: 275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 140.93, #queue-req: 0, 
[2025-07-28 01:21:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1013, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.28, #queue-req: 0, 
[2025-07-28 01:21:28 DP0 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.01, #queue-req: 0, 
[2025-07-28 01:21:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1053, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.70, #queue-req: 0, 
[2025-07-28 01:21:28 DP0 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.42, #queue-req: 0, 
[2025-07-28 01:21:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1093, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.43, #queue-req: 0, 
[2025-07-28 01:21:28 DP0 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.71, #queue-req: 0, 
[2025-07-28 01:21:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1133, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.42, #queue-req: 0, 
[2025-07-28 01:21:29 DP0 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.55, #queue-req: 0, 
[2025-07-28 01:21:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1173, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.28, #queue-req: 0, 
[2025-07-28 01:21:29 DP0 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.90, #queue-req: 0, 
[2025-07-28 01:21:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1213, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.37, #queue-req: 0, 
[2025-07-28 01:21:29 DP0 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.82, #queue-req: 0, 
[2025-07-28 01:21:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.20, #queue-req: 0, 
[2025-07-28 01:21:29 DP0 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.74, #queue-req: 0, 
[2025-07-28 01:21:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.87, #queue-req: 0, 
[2025-07-28 01:21:29 DP0 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.83, #queue-req: 0, 
[2025-07-28 01:21:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.13, #queue-req: 0, 
[2025-07-28 01:21:30 DP0 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.88, #queue-req: 0, 
[2025-07-28 01:21:30 DP1 TP0] Decode batch. #running-req: 1, #token: 1373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.11, #queue-req: 0, 
[2025-07-28 01:21:30 DP0 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.14, #queue-req: 0, 
[2025-07-28 01:21:30 DP1 TP0] Decode batch. #running-req: 1, #token: 1413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.53, #queue-req: 0, 
[2025-07-28 01:21:30 DP0 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.60, #queue-req: 0, 
[2025-07-28 01:21:30 DP1 TP0] Decode batch. #running-req: 1, #token: 1453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.88, #queue-req: 0, 
[2025-07-28 01:21:30 DP0 TP0] Decode batch. #running-req: 1, #token: 755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.66, #queue-req: 0, 
[2025-07-28 01:21:30 DP1 TP0] Decode batch. #running-req: 1, #token: 1493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.86, #queue-req: 0, 
[2025-07-28 01:21:30 DP0 TP0] Decode batch. #running-req: 1, #token: 795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.55, #queue-req: 0, 
[2025-07-28 01:21:30 DP1 TP0] Decode batch. #running-req: 1, #token: 1533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.49, #queue-req: 0, 
[2025-07-28 01:21:31 DP0 TP0] Decode batch. #running-req: 1, #token: 835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.74, #queue-req: 0, 
[2025-07-28 01:21:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.73, #queue-req: 0, 
[2025-07-28 01:21:31 DP0 TP0] Decode batch. #running-req: 1, #token: 875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.60, #queue-req: 0, 
[2025-07-28 01:21:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.69, #queue-req: 0, 
[2025-07-28 01:21:31 DP0 TP0] Decode batch. #running-req: 1, #token: 915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.56, #queue-req: 0, 
[2025-07-28 01:21:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1653, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.64, #queue-req: 0, 
[2025-07-28 01:21:31 DP0 TP0] Decode batch. #running-req: 1, #token: 955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.66, #queue-req: 0, 
[2025-07-28 01:21:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.77, #queue-req: 0, 
[2025-07-28 01:21:31 DP0 TP0] Decode batch. #running-req: 1, #token: 995, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.63, #queue-req: 0, 
[2025-07-28 01:21:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1733, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.96, #queue-req: 0, 
[2025-07-28 01:21:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.42, #queue-req: 0, 
[2025-07-28 01:21:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1773, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.95, #queue-req: 0, 
[2025-07-28 01:21:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1075, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.02, #queue-req: 0, 
[2025-07-28 01:21:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1813, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.90, #queue-req: 0, 
[2025-07-28 01:21:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.86, #queue-req: 0, 
[2025-07-28 01:21:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1853, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.56, #queue-req: 0, 
[2025-07-28 01:21:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1155, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.82, #queue-req: 0, 
[2025-07-28 01:21:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1893, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.83, #queue-req: 0, 
[2025-07-28 01:21:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.03, #queue-req: 0, 
[2025-07-28 01:21:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1933, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.78, #queue-req: 0, 
[2025-07-28 01:21:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.74, #queue-req: 0, 
[2025-07-28 01:21:33 DP1 TP0] Decode batch. #running-req: 1, #token: 1973, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.64, #queue-req: 0, 
[2025-07-28 01:21:33 DP0 TP0] Decode batch. #running-req: 1, #token: 1275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.87, #queue-req: 0, 
[2025-07-28 01:21:33 DP1 TP0] Decode batch. #running-req: 1, #token: 2013, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.66, #queue-req: 0, 
[2025-07-28 01:21:33 DP0 TP0] Decode batch. #running-req: 1, #token: 1315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.80, #queue-req: 0, 
[2025-07-28 01:21:33 DP1 TP0] Decode batch. #running-req: 1, #token: 2053, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.53, #queue-req: 0, 
[2025-07-28 01:21:33 DP0 TP0] Decode batch. #running-req: 1, #token: 1355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.75, #queue-req: 0, 
[2025-07-28 01:21:33 DP1 TP0] Decode batch. #running-req: 1, #token: 2093, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.96, #queue-req: 0, 
[2025-07-28 01:21:33 DP0 TP0] Decode batch. #running-req: 1, #token: 1395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.67, #queue-req: 0, 
[2025-07-28 01:21:33 DP1 TP0] Decode batch. #running-req: 1, #token: 2133, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.07, #queue-req: 0, 
[2025-07-28 01:21:33 DP0 TP0] Decode batch. #running-req: 1, #token: 1435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.34, #queue-req: 0, 
[2025-07-28 01:21:33 DP1 TP0] Decode batch. #running-req: 1, #token: 2173, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.98, #queue-req: 0, 
[2025-07-28 01:21:34 DP0 TP0] Decode batch. #running-req: 1, #token: 1475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.66, #queue-req: 0, 
[2025-07-28 01:21:34 DP1 TP0] Decode batch. #running-req: 1, #token: 2213, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.92, #queue-req: 0, 
[2025-07-28 01:21:34 DP0 TP0] Decode batch. #running-req: 1, #token: 1515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.53, #queue-req: 0, 
[2025-07-28 01:21:34 DP1 TP0] Decode batch. #running-req: 1, #token: 2253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.91, #queue-req: 0, 
[2025-07-28 01:21:34 DP0 TP0] Decode batch. #running-req: 1, #token: 1555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.57, #queue-req: 0, 
[2025-07-28 01:21:34 DP1 TP0] Decode batch. #running-req: 1, #token: 2293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.94, #queue-req: 0, 
[2025-07-28 01:21:34] INFO:     127.0.0.1:49816 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:21:34 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 111, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:21:34 DP1 TP0] Decode batch. #running-req: 2, #token: 2474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 228.19, #queue-req: 0, 
[2025-07-28 01:21:35 DP1 TP0] Decode batch. #running-req: 2, #token: 2554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.18, #queue-req: 0, 
[2025-07-28 01:21:35 DP1 TP0] Decode batch. #running-req: 2, #token: 2634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.32, #queue-req: 0, 
[2025-07-28 01:21:35 DP1 TP0] Decode batch. #running-req: 2, #token: 2714, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.61, #queue-req: 0, 
[2025-07-28 01:21:35 DP1 TP0] Decode batch. #running-req: 2, #token: 2794, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.97, #queue-req: 0, 
[2025-07-28 01:21:36 DP1 TP0] Decode batch. #running-req: 2, #token: 2874, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.42, #queue-req: 0, 
[2025-07-28 01:21:36 DP1 TP0] Decode batch. #running-req: 2, #token: 2954, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.43, #queue-req: 0, 
[2025-07-28 01:21:36 DP1 TP0] Decode batch. #running-req: 2, #token: 3034, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.57, #queue-req: 0, 
[2025-07-28 01:21:36 DP1 TP0] Decode batch. #running-req: 2, #token: 3114, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.92, #queue-req: 0, 
[2025-07-28 01:21:37 DP1 TP0] Decode batch. #running-req: 2, #token: 3194, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.03, #queue-req: 0, 
[2025-07-28 01:21:37 DP1 TP0] Decode batch. #running-req: 2, #token: 3274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.27, #queue-req: 0, 
[2025-07-28 01:21:37 DP1 TP0] Decode batch. #running-req: 2, #token: 3354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.03, #queue-req: 0, 
[2025-07-28 01:21:37] INFO:     127.0.0.1:49802 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:21:37 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 95, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:21:37 DP1 TP0] Decode batch. #running-req: 1, #token: 706, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.65, #queue-req: 0, 
[2025-07-28 01:21:38 DP0 TP0] Decode batch. #running-req: 1, #token: 205, token usage: 0.00, cuda graph: True, gen throughput (token/s): 11.43, #queue-req: 0, 
[2025-07-28 01:21:38 DP1 TP0] Decode batch. #running-req: 1, #token: 746, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.16, #queue-req: 0, 
[2025-07-28 01:21:38 DP0 TP0] Decode batch. #running-req: 1, #token: 245, token usage: 0.00, cuda graph: True, gen throughput (token/s): 202.43, #queue-req: 0, 
[2025-07-28 01:21:38 DP1 TP0] Decode batch. #running-req: 1, #token: 786, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.26, #queue-req: 0, 
[2025-07-28 01:21:38 DP0 TP0] Decode batch. #running-req: 1, #token: 285, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.91, #queue-req: 0, 
[2025-07-28 01:21:38 DP1 TP0] Decode batch. #running-req: 1, #token: 826, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.14, #queue-req: 0, 
[2025-07-28 01:21:38 DP0 TP0] Decode batch. #running-req: 1, #token: 325, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.49, #queue-req: 0, 
[2025-07-28 01:21:38 DP1 TP0] Decode batch. #running-req: 1, #token: 866, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.19, #queue-req: 0, 
[2025-07-28 01:21:38 DP0 TP0] Decode batch. #running-req: 1, #token: 365, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.31, #queue-req: 0, 
[2025-07-28 01:21:38 DP1 TP0] Decode batch. #running-req: 1, #token: 906, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.25, #queue-req: 0, 
[2025-07-28 01:21:38 DP0 TP0] Decode batch. #running-req: 1, #token: 405, token usage: 0.00, cuda graph: True, gen throughput (token/s): 206.75, #queue-req: 0, 
[2025-07-28 01:21:39 DP1 TP0] Decode batch. #running-req: 1, #token: 946, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.06, #queue-req: 0, 
[2025-07-28 01:21:39 DP0 TP0] Decode batch. #running-req: 1, #token: 445, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.98, #queue-req: 0, 
[2025-07-28 01:21:39 DP1 TP0] Decode batch. #running-req: 1, #token: 986, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.31, #queue-req: 0, 
[2025-07-28 01:21:39 DP0 TP0] Decode batch. #running-req: 1, #token: 485, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.86, #queue-req: 0, 
[2025-07-28 01:21:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1026, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.67, #queue-req: 0, 
[2025-07-28 01:21:39 DP0 TP0] Decode batch. #running-req: 1, #token: 525, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.90, #queue-req: 0, 
[2025-07-28 01:21:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1066, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.49, #queue-req: 0, 
[2025-07-28 01:21:39 DP0 TP0] Decode batch. #running-req: 1, #token: 565, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.16, #queue-req: 0, 
[2025-07-28 01:21:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1106, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.83, #queue-req: 0, 
[2025-07-28 01:21:39 DP0 TP0] Decode batch. #running-req: 1, #token: 605, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.12, #queue-req: 0, 
[2025-07-28 01:21:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1146, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.66, #queue-req: 0, 
[2025-07-28 01:21:40 DP0 TP0] Decode batch. #running-req: 1, #token: 645, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.35, #queue-req: 0, 
[2025-07-28 01:21:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1186, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.12, #queue-req: 0, 
[2025-07-28 01:21:40 DP0 TP0] Decode batch. #running-req: 1, #token: 685, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.58, #queue-req: 0, 
[2025-07-28 01:21:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1226, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.76, #queue-req: 0, 
[2025-07-28 01:21:40 DP0 TP0] Decode batch. #running-req: 1, #token: 725, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.35, #queue-req: 0, 
[2025-07-28 01:21:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1266, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.93, #queue-req: 0, 
[2025-07-28 01:21:40 DP0 TP0] Decode batch. #running-req: 1, #token: 765, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.67, #queue-req: 0, 
[2025-07-28 01:21:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1306, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.94, #queue-req: 0, 
[2025-07-28 01:21:40 DP0 TP0] Decode batch. #running-req: 1, #token: 805, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.54, #queue-req: 0, 
[2025-07-28 01:21:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1346, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.64, #queue-req: 0, 
[2025-07-28 01:21:41 DP0 TP0] Decode batch. #running-req: 1, #token: 845, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.60, #queue-req: 0, 
[2025-07-28 01:21:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1386, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.60, #queue-req: 0, 
[2025-07-28 01:21:41 DP0 TP0] Decode batch. #running-req: 1, #token: 885, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.62, #queue-req: 0, 
[2025-07-28 01:21:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1426, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.66, #queue-req: 0, 
[2025-07-28 01:21:41 DP0 TP0] Decode batch. #running-req: 1, #token: 925, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.06, #queue-req: 0, 
[2025-07-28 01:21:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1466, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.92, #queue-req: 0, 
[2025-07-28 01:21:41 DP0 TP0] Decode batch. #running-req: 1, #token: 965, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.68, #queue-req: 0, 
[2025-07-28 01:21:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1506, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.05, #queue-req: 0, 
[2025-07-28 01:21:41 DP0 TP0] Decode batch. #running-req: 1, #token: 1005, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.67, #queue-req: 0, 
[2025-07-28 01:21:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1546, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.80, #queue-req: 0, 
[2025-07-28 01:21:42 DP0 TP0] Decode batch. #running-req: 1, #token: 1045, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.30, #queue-req: 0, 
[2025-07-28 01:21:42 DP1 TP0] Decode batch. #running-req: 1, #token: 1586, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.05, #queue-req: 0, 
[2025-07-28 01:21:42] INFO:     127.0.0.1:36362 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:21:42 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 89, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:21:42 DP0 TP0] Decode batch. #running-req: 1, #token: 1085, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.86, #queue-req: 0, 
[2025-07-28 01:21:42 DP1 TP0] Decode batch. #running-req: 1, #token: 195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 148.20, #queue-req: 0, 
[2025-07-28 01:21:42 DP0 TP0] Decode batch. #running-req: 1, #token: 1125, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.93, #queue-req: 0, 
[2025-07-28 01:21:42 DP1 TP0] Decode batch. #running-req: 1, #token: 235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.91, #queue-req: 0, 
[2025-07-28 01:21:42 DP0 TP0] Decode batch. #running-req: 1, #token: 1165, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.04, #queue-req: 0, 
[2025-07-28 01:21:42 DP1 TP0] Decode batch. #running-req: 1, #token: 275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.79, #queue-req: 0, 
[2025-07-28 01:21:42 DP0 TP0] Decode batch. #running-req: 1, #token: 1205, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.91, #queue-req: 0, 
[2025-07-28 01:21:42 DP1 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.56, #queue-req: 0, 
[2025-07-28 01:21:42 DP0 TP0] Decode batch. #running-req: 1, #token: 1245, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.76, #queue-req: 0, 
[2025-07-28 01:21:43 DP1 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.59, #queue-req: 0, 
[2025-07-28 01:21:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1285, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.84, #queue-req: 0, 
[2025-07-28 01:21:43 DP1 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.48, #queue-req: 0, 
[2025-07-28 01:21:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1325, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.76, #queue-req: 0, 
[2025-07-28 01:21:43 DP1 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.48, #queue-req: 0, 
[2025-07-28 01:21:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1365, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.85, #queue-req: 0, 
[2025-07-28 01:21:43 DP1 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.35, #queue-req: 0, 
[2025-07-28 01:21:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1405, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.84, #queue-req: 0, 
[2025-07-28 01:21:43 DP1 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.30, #queue-req: 0, 
[2025-07-28 01:21:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1445, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.82, #queue-req: 0, 
[2025-07-28 01:21:44 DP1 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.32, #queue-req: 0, 
[2025-07-28 01:21:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1485, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.61, #queue-req: 0, 
[2025-07-28 01:21:44 DP1 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.20, #queue-req: 0, 
[2025-07-28 01:21:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1525, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.67, #queue-req: 0, 
[2025-07-28 01:21:44 DP1 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.08, #queue-req: 0, 
[2025-07-28 01:21:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1565, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.79, #queue-req: 0, 
[2025-07-28 01:21:44 DP1 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.25, #queue-req: 0, 
[2025-07-28 01:21:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1605, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.61, #queue-req: 0, 
[2025-07-28 01:21:44 DP1 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.06, #queue-req: 0, 
[2025-07-28 01:21:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1645, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.52, #queue-req: 0, 
[2025-07-28 01:21:45 DP1 TP0] Decode batch. #running-req: 1, #token: 755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.96, #queue-req: 0, 
[2025-07-28 01:21:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1685, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.49, #queue-req: 0, 
[2025-07-28 01:21:45 DP1 TP0] Decode batch. #running-req: 1, #token: 795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.98, #queue-req: 0, 
[2025-07-28 01:21:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1725, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.54, #queue-req: 0, 
[2025-07-28 01:21:45 DP1 TP0] Decode batch. #running-req: 1, #token: 835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.24, #queue-req: 0, 
[2025-07-28 01:21:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1765, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.30, #queue-req: 0, 
[2025-07-28 01:21:45 DP1 TP0] Decode batch. #running-req: 1, #token: 875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.98, #queue-req: 0, 
[2025-07-28 01:21:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1805, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.31, #queue-req: 0, 
[2025-07-28 01:21:45 DP1 TP0] Decode batch. #running-req: 1, #token: 915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.98, #queue-req: 0, 
[2025-07-28 01:21:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1845, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.29, #queue-req: 0, 
[2025-07-28 01:21:45 DP1 TP0] Decode batch. #running-req: 1, #token: 955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.09, #queue-req: 0, 
[2025-07-28 01:21:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1885, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.11, #queue-req: 0, 
[2025-07-28 01:21:46 DP1 TP0] Decode batch. #running-req: 1, #token: 995, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.92, #queue-req: 0, 
[2025-07-28 01:21:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1925, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.21, #queue-req: 0, 
[2025-07-28 01:21:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.71, #queue-req: 0, 
[2025-07-28 01:21:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1965, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.43, #queue-req: 0, 
[2025-07-28 01:21:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1075, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.19, #queue-req: 0, 
[2025-07-28 01:21:46 DP0 TP0] Decode batch. #running-req: 1, #token: 2005, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.34, #queue-req: 0, 
[2025-07-28 01:21:46] INFO:     127.0.0.1:36372 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:21:46 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 115, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:21:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.32, #queue-req: 0, 
[2025-07-28 01:21:46 DP0 TP0] Decode batch. #running-req: 1, #token: 237, token usage: 0.00, cuda graph: True, gen throughput (token/s): 146.47, #queue-req: 0, 
[2025-07-28 01:21:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1155, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.38, #queue-req: 0, 
[2025-07-28 01:21:47 DP0 TP0] Decode batch. #running-req: 1, #token: 277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.96, #queue-req: 0, 
[2025-07-28 01:21:47 DP1 TP0] Decode batch. #running-req: 1, #token: 1195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.02, #queue-req: 0, 
[2025-07-28 01:21:47 DP0 TP0] Decode batch. #running-req: 1, #token: 317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.17, #queue-req: 0, 
[2025-07-28 01:21:47 DP1 TP0] Decode batch. #running-req: 1, #token: 1235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.26, #queue-req: 0, 
[2025-07-28 01:21:47] INFO:     127.0.0.1:36384 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:21:47 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 164, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:21:47 DP0 TP0] Decode batch. #running-req: 1, #token: 357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.23, #queue-req: 0, 
[2025-07-28 01:21:47 DP1 TP0] Decode batch. #running-req: 1, #token: 290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 148.14, #queue-req: 0, 
[2025-07-28 01:21:47 DP0 TP0] Decode batch. #running-req: 1, #token: 397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.26, #queue-req: 0, 
[2025-07-28 01:21:47 DP1 TP0] Decode batch. #running-req: 1, #token: 330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.55, #queue-req: 0, 
[2025-07-28 01:21:47 DP0 TP0] Decode batch. #running-req: 1, #token: 437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.17, #queue-req: 0, 
[2025-07-28 01:21:47 DP1 TP0] Decode batch. #running-req: 1, #token: 370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.51, #queue-req: 0, 
[2025-07-28 01:21:48 DP0 TP0] Decode batch. #running-req: 1, #token: 477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.19, #queue-req: 0, 
[2025-07-28 01:21:48 DP1 TP0] Decode batch. #running-req: 1, #token: 410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.34, #queue-req: 0, 
[2025-07-28 01:21:48 DP0 TP0] Decode batch. #running-req: 1, #token: 517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.97, #queue-req: 0, 
[2025-07-28 01:21:48 DP1 TP0] Decode batch. #running-req: 1, #token: 450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.45, #queue-req: 0, 
[2025-07-28 01:21:48 DP0 TP0] Decode batch. #running-req: 1, #token: 557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.95, #queue-req: 0, 
[2025-07-28 01:21:48 DP1 TP0] Decode batch. #running-req: 1, #token: 490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.37, #queue-req: 0, 
[2025-07-28 01:21:48 DP0 TP0] Decode batch. #running-req: 1, #token: 597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.77, #queue-req: 0, 
[2025-07-28 01:21:48 DP1 TP0] Decode batch. #running-req: 1, #token: 530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.36, #queue-req: 0, 
[2025-07-28 01:21:48 DP0 TP0] Decode batch. #running-req: 1, #token: 637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.37, #queue-req: 0, 
[2025-07-28 01:21:48 DP1 TP0] Decode batch. #running-req: 1, #token: 570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.27, #queue-req: 0, 
[2025-07-28 01:21:48 DP0 TP0] Decode batch. #running-req: 1, #token: 677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.86, #queue-req: 0, 
[2025-07-28 01:21:49 DP1 TP0] Decode batch. #running-req: 1, #token: 610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.50, #queue-req: 0, 
[2025-07-28 01:21:49 DP0 TP0] Decode batch. #running-req: 1, #token: 717, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.38, #queue-req: 0, 
[2025-07-28 01:21:49 DP1 TP0] Decode batch. #running-req: 1, #token: 650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.90, #queue-req: 0, 
[2025-07-28 01:21:49 DP0 TP0] Decode batch. #running-req: 1, #token: 757, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.97, #queue-req: 0, 
[2025-07-28 01:21:49 DP1 TP0] Decode batch. #running-req: 1, #token: 690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.48, #queue-req: 0, 
[2025-07-28 01:21:49 DP0 TP0] Decode batch. #running-req: 1, #token: 797, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.45, #queue-req: 0, 
[2025-07-28 01:21:49 DP1 TP0] Decode batch. #running-req: 1, #token: 730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.03, #queue-req: 0, 
[2025-07-28 01:21:49 DP0 TP0] Decode batch. #running-req: 1, #token: 837, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.52, #queue-req: 0, 
[2025-07-28 01:21:49 DP1 TP0] Decode batch. #running-req: 1, #token: 770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.99, #queue-req: 0, 
[2025-07-28 01:21:49 DP0 TP0] Decode batch. #running-req: 1, #token: 877, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.79, #queue-req: 0, 
[2025-07-28 01:21:50 DP1 TP0] Decode batch. #running-req: 1, #token: 810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.92, #queue-req: 0, 
[2025-07-28 01:21:50 DP0 TP0] Decode batch. #running-req: 1, #token: 917, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.72, #queue-req: 0, 
[2025-07-28 01:21:50 DP1 TP0] Decode batch. #running-req: 1, #token: 850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.96, #queue-req: 0, 
[2025-07-28 01:21:50 DP0 TP0] Decode batch. #running-req: 1, #token: 957, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.76, #queue-req: 0, 
[2025-07-28 01:21:50 DP1 TP0] Decode batch. #running-req: 1, #token: 890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.74, #queue-req: 0, 
[2025-07-28 01:21:50 DP0 TP0] Decode batch. #running-req: 1, #token: 997, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.71, #queue-req: 0, 
[2025-07-28 01:21:50 DP1 TP0] Decode batch. #running-req: 1, #token: 930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.91, #queue-req: 0, 
[2025-07-28 01:21:50 DP0 TP0] Decode batch. #running-req: 1, #token: 1037, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.47, #queue-req: 0, 
[2025-07-28 01:21:50 DP1 TP0] Decode batch. #running-req: 1, #token: 970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.85, #queue-req: 0, 
[2025-07-28 01:21:50 DP0 TP0] Decode batch. #running-req: 1, #token: 1077, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.89, #queue-req: 0, 
[2025-07-28 01:21:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.79, #queue-req: 0, 
[2025-07-28 01:21:51 DP0 TP0] Decode batch. #running-req: 1, #token: 1117, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.72, #queue-req: 0, 
[2025-07-28 01:21:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.54, #queue-req: 0, 
[2025-07-28 01:21:51 DP0 TP0] Decode batch. #running-req: 1, #token: 1157, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.90, #queue-req: 0, 
[2025-07-28 01:21:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.19, #queue-req: 0, 
[2025-07-28 01:21:51 DP0 TP0] Decode batch. #running-req: 1, #token: 1197, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.73, #queue-req: 0, 
[2025-07-28 01:21:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.41, #queue-req: 0, 
[2025-07-28 01:21:51 DP0 TP0] Decode batch. #running-req: 1, #token: 1237, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.32, #queue-req: 0, 
[2025-07-28 01:21:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.32, #queue-req: 0, 
[2025-07-28 01:21:51 DP0 TP0] Decode batch. #running-req: 1, #token: 1277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.01, #queue-req: 0, 
[2025-07-28 01:21:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.23, #queue-req: 0, 
[2025-07-28 01:21:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.94, #queue-req: 0, 
[2025-07-28 01:21:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.28, #queue-req: 0, 
[2025-07-28 01:21:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.98, #queue-req: 0, 
[2025-07-28 01:21:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.26, #queue-req: 0, 
[2025-07-28 01:21:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.92, #queue-req: 0, 
[2025-07-28 01:21:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.17, #queue-req: 0, 
[2025-07-28 01:21:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.63, #queue-req: 0, 
[2025-07-28 01:21:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.19, #queue-req: 0, 
[2025-07-28 01:21:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.70, #queue-req: 0, 
[2025-07-28 01:21:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.07, #queue-req: 0, 
[2025-07-28 01:21:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.62, #queue-req: 0, 
[2025-07-28 01:21:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.07, #queue-req: 0, 
[2025-07-28 01:21:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.64, #queue-req: 0, 
[2025-07-28 01:21:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.12, #queue-req: 0, 
[2025-07-28 01:21:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.62, #queue-req: 0, 
[2025-07-28 01:21:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.00, #queue-req: 0, 
[2025-07-28 01:21:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.58, #queue-req: 0, 
[2025-07-28 01:21:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.03, #queue-req: 0, 
[2025-07-28 01:21:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.50, #queue-req: 0, 
[2025-07-28 01:21:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.97, #queue-req: 0, 
[2025-07-28 01:21:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1717, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.57, #queue-req: 0, 
[2025-07-28 01:21:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.01, #queue-req: 0, 
[2025-07-28 01:21:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1757, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.61, #queue-req: 0, 
[2025-07-28 01:21:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.91, #queue-req: 0, 
[2025-07-28 01:21:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1797, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.59, #queue-req: 0, 
[2025-07-28 01:21:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.87, #queue-req: 0, 
[2025-07-28 01:21:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1837, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.58, #queue-req: 0, 
[2025-07-28 01:21:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.80, #queue-req: 0, 
[2025-07-28 01:21:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1877, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.47, #queue-req: 0, 
[2025-07-28 01:21:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.94, #queue-req: 0, 
[2025-07-28 01:21:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1917, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.47, #queue-req: 0, 
[2025-07-28 01:21:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.81, #queue-req: 0, 
[2025-07-28 01:21:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1957, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.42, #queue-req: 0, 
[2025-07-28 01:21:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.74, #queue-req: 0, 
[2025-07-28 01:21:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1997, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.42, #queue-req: 0, 
[2025-07-28 01:21:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.78, #queue-req: 0, 
[2025-07-28 01:21:55 DP0 TP0] Decode batch. #running-req: 1, #token: 2037, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.39, #queue-req: 0, 
[2025-07-28 01:21:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.67, #queue-req: 0, 
[2025-07-28 01:21:55 DP0 TP0] Decode batch. #running-req: 1, #token: 2077, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.96, #queue-req: 0, 
[2025-07-28 01:21:55 DP1 TP0] Decode batch. #running-req: 1, #token: 2010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.86, #queue-req: 0, 
[2025-07-28 01:21:55 DP0 TP0] Decode batch. #running-req: 1, #token: 2117, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.72, #queue-req: 0, 
[2025-07-28 01:21:55 DP1 TP0] Decode batch. #running-req: 1, #token: 2050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.73, #queue-req: 0, 
[2025-07-28 01:21:56 DP0 TP0] Decode batch. #running-req: 1, #token: 2157, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.88, #queue-req: 0, 
[2025-07-28 01:21:56] INFO:     127.0.0.1:46456 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:21:56 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 143, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:21:56 DP1 TP0] Decode batch. #running-req: 1, #token: 2090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.26, #queue-req: 0, 
[2025-07-28 01:21:56 DP1 TP0] Decode batch. #running-req: 1, #token: 2130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.16, #queue-req: 0, 
[2025-07-28 01:21:56 DP0 TP0] Decode batch. #running-req: 1, #token: 260, token usage: 0.00, cuda graph: True, gen throughput (token/s): 143.90, #queue-req: 0, 
[2025-07-28 01:21:56 DP1 TP0] Decode batch. #running-req: 1, #token: 2170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.09, #queue-req: 0, 
[2025-07-28 01:21:56 DP0 TP0] Decode batch. #running-req: 1, #token: 300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.29, #queue-req: 0, 
[2025-07-28 01:21:56 DP1 TP0] Decode batch. #running-req: 1, #token: 2210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.89, #queue-req: 0, 
[2025-07-28 01:21:56 DP0 TP0] Decode batch. #running-req: 1, #token: 340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.68, #queue-req: 0, 
[2025-07-28 01:21:56 DP0 TP0] Decode batch. #running-req: 1, #token: 380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.62, #queue-req: 0, 
[2025-07-28 01:21:56 DP1 TP0] Decode batch. #running-req: 1, #token: 2250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.51, #queue-req: 0, 
[2025-07-28 01:21:57 DP0 TP0] Decode batch. #running-req: 1, #token: 420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.04, #queue-req: 0, 
[2025-07-28 01:21:57 DP1 TP0] Decode batch. #running-req: 1, #token: 2290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.97, #queue-req: 0, 
[2025-07-28 01:21:57 DP0 TP0] Decode batch. #running-req: 1, #token: 460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.92, #queue-req: 0, 
[2025-07-28 01:21:57 DP1 TP0] Decode batch. #running-req: 1, #token: 2330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.89, #queue-req: 0, 
[2025-07-28 01:21:57 DP0 TP0] Decode batch. #running-req: 1, #token: 500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.22, #queue-req: 0, 
[2025-07-28 01:21:57 DP1 TP0] Decode batch. #running-req: 1, #token: 2370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.80, #queue-req: 0, 
[2025-07-28 01:21:57 DP0 TP0] Decode batch. #running-req: 1, #token: 540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.21, #queue-req: 0, 
[2025-07-28 01:21:57 DP1 TP0] Decode batch. #running-req: 1, #token: 2410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.63, #queue-req: 0, 
[2025-07-28 01:21:57 DP0 TP0] Decode batch. #running-req: 1, #token: 580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.73, #queue-req: 0, 
[2025-07-28 01:21:57 DP1 TP0] Decode batch. #running-req: 1, #token: 2450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.79, #queue-req: 0, 
[2025-07-28 01:21:58 DP0 TP0] Decode batch. #running-req: 1, #token: 620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.84, #queue-req: 0, 
[2025-07-28 01:21:58 DP1 TP0] Decode batch. #running-req: 1, #token: 2490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.67, #queue-req: 0, 
[2025-07-28 01:21:58 DP0 TP0] Decode batch. #running-req: 1, #token: 660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.84, #queue-req: 0, 
[2025-07-28 01:21:58 DP1 TP0] Decode batch. #running-req: 1, #token: 2530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.76, #queue-req: 0, 
[2025-07-28 01:21:58 DP0 TP0] Decode batch. #running-req: 1, #token: 700, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.03, #queue-req: 0, 
[2025-07-28 01:21:58 DP1 TP0] Decode batch. #running-req: 1, #token: 2570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.61, #queue-req: 0, 
[2025-07-28 01:21:58 DP0 TP0] Decode batch. #running-req: 1, #token: 740, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.84, #queue-req: 0, 
[2025-07-28 01:21:58 DP1 TP0] Decode batch. #running-req: 1, #token: 2610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.75, #queue-req: 0, 
[2025-07-28 01:21:58 DP0 TP0] Decode batch. #running-req: 1, #token: 780, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.76, #queue-req: 0, 
[2025-07-28 01:21:58 DP1 TP0] Decode batch. #running-req: 1, #token: 2650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.69, #queue-req: 0, 
[2025-07-28 01:21:58 DP0 TP0] Decode batch. #running-req: 1, #token: 820, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.81, #queue-req: 0, 
[2025-07-28 01:21:58 DP1 TP0] Decode batch. #running-req: 1, #token: 2690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.54, #queue-req: 0, 
[2025-07-28 01:21:59 DP0 TP0] Decode batch. #running-req: 1, #token: 860, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.70, #queue-req: 0, 
[2025-07-28 01:21:59 DP1 TP0] Decode batch. #running-req: 1, #token: 2730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.54, #queue-req: 0, 
[2025-07-28 01:21:59 DP0 TP0] Decode batch. #running-req: 1, #token: 900, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.63, #queue-req: 0, 
[2025-07-28 01:21:59 DP1 TP0] Decode batch. #running-req: 1, #token: 2770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.42, #queue-req: 0, 
[2025-07-28 01:21:59 DP0 TP0] Decode batch. #running-req: 1, #token: 940, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.56, #queue-req: 0, 
[2025-07-28 01:21:59 DP1 TP0] Decode batch. #running-req: 1, #token: 2810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.62, #queue-req: 0, 
[2025-07-28 01:21:59 DP0 TP0] Decode batch. #running-req: 1, #token: 980, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.67, #queue-req: 0, 
[2025-07-28 01:21:59 DP1 TP0] Decode batch. #running-req: 1, #token: 2850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.47, #queue-req: 0, 
[2025-07-28 01:21:59] INFO:     127.0.0.1:41966 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:21:59 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 117, #cached-token: 88, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:22:00 DP1 TP0] Decode batch. #running-req: 2, #token: 3036, token usage: 0.00, cuda graph: True, gen throughput (token/s): 223.68, #queue-req: 0, 
[2025-07-28 01:22:00 DP1 TP0] Decode batch. #running-req: 2, #token: 3116, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.12, #queue-req: 0, 
[2025-07-28 01:22:00 DP1 TP0] Decode batch. #running-req: 2, #token: 3196, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.02, #queue-req: 0, 
[2025-07-28 01:22:00 DP1 TP0] Decode batch. #running-req: 2, #token: 3276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.02, #queue-req: 0, 
[2025-07-28 01:22:01 DP1 TP0] Decode batch. #running-req: 2, #token: 3356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.08, #queue-req: 0, 
[2025-07-28 01:22:01 DP1 TP0] Decode batch. #running-req: 2, #token: 3436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.67, #queue-req: 0, 
[2025-07-28 01:22:01 DP1 TP0] Decode batch. #running-req: 2, #token: 3516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.95, #queue-req: 0, 
[2025-07-28 01:22:01 DP1 TP0] Decode batch. #running-req: 2, #token: 3596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.59, #queue-req: 0, 
[2025-07-28 01:22:02 DP1 TP0] Decode batch. #running-req: 2, #token: 3676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.24, #queue-req: 0, 
[2025-07-28 01:22:02 DP1 TP0] Decode batch. #running-req: 2, #token: 3756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.33, #queue-req: 0, 
[2025-07-28 01:22:02 DP1 TP0] Decode batch. #running-req: 2, #token: 3836, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.22, #queue-req: 0, 
[2025-07-28 01:22:02] INFO:     127.0.0.1:46466 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:22:02 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 125, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:22:02 DP1 TP0] Decode batch. #running-req: 1, #token: 673, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.49, #queue-req: 0, 
[2025-07-28 01:22:02 DP0 TP0] Decode batch. #running-req: 1, #token: 237, token usage: 0.00, cuda graph: True, gen throughput (token/s): 12.40, #queue-req: 0, 
[2025-07-28 01:22:03 DP1 TP0] Decode batch. #running-req: 1, #token: 713, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.21, #queue-req: 0, 
[2025-07-28 01:22:03 DP0 TP0] Decode batch. #running-req: 1, #token: 277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 203.78, #queue-req: 0, 
[2025-07-28 01:22:03 DP1 TP0] Decode batch. #running-req: 1, #token: 753, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.61, #queue-req: 0, 
[2025-07-28 01:22:03 DP0 TP0] Decode batch. #running-req: 1, #token: 317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.88, #queue-req: 0, 
[2025-07-28 01:22:03 DP1 TP0] Decode batch. #running-req: 1, #token: 793, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.21, #queue-req: 0, 
[2025-07-28 01:22:03 DP0 TP0] Decode batch. #running-req: 1, #token: 357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.32, #queue-req: 0, 
[2025-07-28 01:22:03 DP1 TP0] Decode batch. #running-req: 1, #token: 833, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.11, #queue-req: 0, 
[2025-07-28 01:22:03 DP0 TP0] Decode batch. #running-req: 1, #token: 397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.77, #queue-req: 0, 
[2025-07-28 01:22:03 DP1 TP0] Decode batch. #running-req: 1, #token: 873, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.92, #queue-req: 0, 
[2025-07-28 01:22:03 DP0 TP0] Decode batch. #running-req: 1, #token: 437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.93, #queue-req: 0, 
[2025-07-28 01:22:03 DP1 TP0] Decode batch. #running-req: 1, #token: 913, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.01, #queue-req: 0, 
[2025-07-28 01:22:04 DP0 TP0] Decode batch. #running-req: 1, #token: 477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.95, #queue-req: 0, 
[2025-07-28 01:22:04 DP1 TP0] Decode batch. #running-req: 1, #token: 953, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.01, #queue-req: 0, 
[2025-07-28 01:22:04 DP0 TP0] Decode batch. #running-req: 1, #token: 517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.93, #queue-req: 0, 
[2025-07-28 01:22:04 DP1 TP0] Decode batch. #running-req: 1, #token: 993, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.05, #queue-req: 0, 
[2025-07-28 01:22:04 DP0 TP0] Decode batch. #running-req: 1, #token: 557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.86, #queue-req: 0, 
[2025-07-28 01:22:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1033, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.94, #queue-req: 0, 
[2025-07-28 01:22:04 DP0 TP0] Decode batch. #running-req: 1, #token: 597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.69, #queue-req: 0, 
[2025-07-28 01:22:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1073, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.28, #queue-req: 0, 
[2025-07-28 01:22:04 DP0 TP0] Decode batch. #running-req: 1, #token: 637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.69, #queue-req: 0, 
[2025-07-28 01:22:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1113, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.38, #queue-req: 0, 
[2025-07-28 01:22:04] INFO:     127.0.0.1:41982 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:22:05 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 196, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:22:05 DP0 TP0] Decode batch. #running-req: 1, #token: 677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.90, #queue-req: 0, 
[2025-07-28 01:22:05 DP1 TP0] Decode batch. #running-req: 1, #token: 307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 147.67, #queue-req: 0, 
[2025-07-28 01:22:05 DP0 TP0] Decode batch. #running-req: 1, #token: 717, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.78, #queue-req: 0, 
[2025-07-28 01:22:05 DP1 TP0] Decode batch. #running-req: 1, #token: 347, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.57, #queue-req: 0, 
[2025-07-28 01:22:05 DP0 TP0] Decode batch. #running-req: 1, #token: 757, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.88, #queue-req: 0, 
[2025-07-28 01:22:05 DP1 TP0] Decode batch. #running-req: 1, #token: 387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.46, #queue-req: 0, 
[2025-07-28 01:22:05 DP0 TP0] Decode batch. #running-req: 1, #token: 797, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.76, #queue-req: 0, 
[2025-07-28 01:22:05 DP1 TP0] Decode batch. #running-req: 1, #token: 427, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.40, #queue-req: 0, 
[2025-07-28 01:22:05 DP0 TP0] Decode batch. #running-req: 1, #token: 837, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.71, #queue-req: 0, 
[2025-07-28 01:22:05 DP1 TP0] Decode batch. #running-req: 1, #token: 467, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.39, #queue-req: 0, 
[2025-07-28 01:22:05 DP0 TP0] Decode batch. #running-req: 1, #token: 877, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.78, #queue-req: 0, 
[2025-07-28 01:22:06 DP1 TP0] Decode batch. #running-req: 1, #token: 507, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.53, #queue-req: 0, 
[2025-07-28 01:22:06 DP0 TP0] Decode batch. #running-req: 1, #token: 917, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.73, #queue-req: 0, 
[2025-07-28 01:22:06 DP1 TP0] Decode batch. #running-req: 1, #token: 547, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.22, #queue-req: 0, 
[2025-07-28 01:22:06 DP0 TP0] Decode batch. #running-req: 1, #token: 957, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.48, #queue-req: 0, 
[2025-07-28 01:22:06 DP1 TP0] Decode batch. #running-req: 1, #token: 587, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.55, #queue-req: 0, 
[2025-07-28 01:22:06 DP0 TP0] Decode batch. #running-req: 1, #token: 997, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.65, #queue-req: 0, 
[2025-07-28 01:22:06 DP1 TP0] Decode batch. #running-req: 1, #token: 627, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.47, #queue-req: 0, 
[2025-07-28 01:22:06] INFO:     127.0.0.1:59774 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:22:06 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:22:06 DP0 TP0] Decode batch. #running-req: 1, #token: 174, token usage: 0.00, cuda graph: True, gen throughput (token/s): 148.70, #queue-req: 0, 
[2025-07-28 01:22:06 DP1 TP0] Decode batch. #running-req: 1, #token: 667, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.18, #queue-req: 0, 
[2025-07-28 01:22:07 DP0 TP0] Decode batch. #running-req: 1, #token: 214, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.21, #queue-req: 0, 
[2025-07-28 01:22:07 DP1 TP0] Decode batch. #running-req: 1, #token: 707, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.34, #queue-req: 0, 
[2025-07-28 01:22:07 DP0 TP0] Decode batch. #running-req: 1, #token: 254, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.20, #queue-req: 0, 
[2025-07-28 01:22:07 DP1 TP0] Decode batch. #running-req: 1, #token: 747, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.39, #queue-req: 0, 
[2025-07-28 01:22:07 DP0 TP0] Decode batch. #running-req: 1, #token: 294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.25, #queue-req: 0, 
[2025-07-28 01:22:07 DP1 TP0] Decode batch. #running-req: 1, #token: 787, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.33, #queue-req: 0, 
[2025-07-28 01:22:07 DP0 TP0] Decode batch. #running-req: 1, #token: 334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.09, #queue-req: 0, 
[2025-07-28 01:22:07 DP1 TP0] Decode batch. #running-req: 1, #token: 827, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.21, #queue-req: 0, 
[2025-07-28 01:22:07 DP0 TP0] Decode batch. #running-req: 1, #token: 374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.14, #queue-req: 0, 
[2025-07-28 01:22:07 DP1 TP0] Decode batch. #running-req: 1, #token: 867, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.20, #queue-req: 0, 
[2025-07-28 01:22:07 DP0 TP0] Decode batch. #running-req: 1, #token: 414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.05, #queue-req: 0, 
[2025-07-28 01:22:08 DP1 TP0] Decode batch. #running-req: 1, #token: 907, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.32, #queue-req: 0, 
[2025-07-28 01:22:08 DP0 TP0] Decode batch. #running-req: 1, #token: 454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.02, #queue-req: 0, 
[2025-07-28 01:22:08 DP1 TP0] Decode batch. #running-req: 1, #token: 947, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.20, #queue-req: 0, 
[2025-07-28 01:22:08 DP0 TP0] Decode batch. #running-req: 1, #token: 494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.87, #queue-req: 0, 
[2025-07-28 01:22:08 DP1 TP0] Decode batch. #running-req: 1, #token: 987, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.23, #queue-req: 0, 
[2025-07-28 01:22:08 DP0 TP0] Decode batch. #running-req: 1, #token: 534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.93, #queue-req: 0, 
[2025-07-28 01:22:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1027, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.97, #queue-req: 0, 
[2025-07-28 01:22:08 DP0 TP0] Decode batch. #running-req: 1, #token: 574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.76, #queue-req: 0, 
[2025-07-28 01:22:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1067, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.47, #queue-req: 0, 
[2025-07-28 01:22:08 DP0 TP0] Decode batch. #running-req: 1, #token: 614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.36, #queue-req: 0, 
[2025-07-28 01:22:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1107, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.43, #queue-req: 0, 
[2025-07-28 01:22:09 DP0 TP0] Decode batch. #running-req: 1, #token: 654, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.42, #queue-req: 0, 
[2025-07-28 01:22:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1147, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.39, #queue-req: 0, 
[2025-07-28 01:22:09 DP0 TP0] Decode batch. #running-req: 1, #token: 694, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.42, #queue-req: 0, 
[2025-07-28 01:22:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1187, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.41, #queue-req: 0, 
[2025-07-28 01:22:09 DP0 TP0] Decode batch. #running-req: 1, #token: 734, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.39, #queue-req: 0, 
[2025-07-28 01:22:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1227, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.04, #queue-req: 0, 
[2025-07-28 01:22:09 DP0 TP0] Decode batch. #running-req: 1, #token: 774, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.78, #queue-req: 0, 
[2025-07-28 01:22:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1267, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.26, #queue-req: 0, 
[2025-07-28 01:22:09 DP0 TP0] Decode batch. #running-req: 1, #token: 814, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.23, #queue-req: 0, 
[2025-07-28 01:22:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.73, #queue-req: 0, 
[2025-07-28 01:22:10 DP0 TP0] Decode batch. #running-req: 1, #token: 854, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.89, #queue-req: 0, 
[2025-07-28 01:22:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1347, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.10, #queue-req: 0, 
[2025-07-28 01:22:10 DP0 TP0] Decode batch. #running-req: 1, #token: 894, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.16, #queue-req: 0, 
[2025-07-28 01:22:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.23, #queue-req: 0, 
[2025-07-28 01:22:10 DP0 TP0] Decode batch. #running-req: 1, #token: 934, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.29, #queue-req: 0, 
[2025-07-28 01:22:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1427, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.25, #queue-req: 0, 
[2025-07-28 01:22:10 DP0 TP0] Decode batch. #running-req: 1, #token: 974, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.27, #queue-req: 0, 
[2025-07-28 01:22:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1467, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.17, #queue-req: 0, 
[2025-07-28 01:22:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1014, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.30, #queue-req: 0, 
[2025-07-28 01:22:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1507, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.00, #queue-req: 0, 
[2025-07-28 01:22:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1054, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.15, #queue-req: 0, 
[2025-07-28 01:22:11 DP1 TP0] Decode batch. #running-req: 1, #token: 1547, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.13, #queue-req: 0, 
[2025-07-28 01:22:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1094, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.99, #queue-req: 0, 
[2025-07-28 01:22:11 DP1 TP0] Decode batch. #running-req: 1, #token: 1587, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.05, #queue-req: 0, 
[2025-07-28 01:22:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1134, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.05, #queue-req: 0, 
[2025-07-28 01:22:11 DP1 TP0] Decode batch. #running-req: 1, #token: 1627, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.93, #queue-req: 0, 
[2025-07-28 01:22:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1174, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.14, #queue-req: 0, 
[2025-07-28 01:22:11 DP1 TP0] Decode batch. #running-req: 1, #token: 1667, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.91, #queue-req: 0, 
[2025-07-28 01:22:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1214, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.51, #queue-req: 0, 
[2025-07-28 01:22:11 DP1 TP0] Decode batch. #running-req: 1, #token: 1707, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.71, #queue-req: 0, 
[2025-07-28 01:22:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1254, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.00, #queue-req: 0, 
[2025-07-28 01:22:12 DP1 TP0] Decode batch. #running-req: 1, #token: 1747, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.90, #queue-req: 0, 
[2025-07-28 01:22:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.30, #queue-req: 0, 
[2025-07-28 01:22:12 DP1 TP0] Decode batch. #running-req: 1, #token: 1787, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.20, #queue-req: 0, 
[2025-07-28 01:22:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.33, #queue-req: 0, 
[2025-07-28 01:22:12 DP1 TP0] Decode batch. #running-req: 1, #token: 1827, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.40, #queue-req: 0, 
[2025-07-28 01:22:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.35, #queue-req: 0, 
[2025-07-28 01:22:12 DP1 TP0] Decode batch. #running-req: 1, #token: 1867, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.36, #queue-req: 0, 
[2025-07-28 01:22:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.50, #queue-req: 0, 
[2025-07-28 01:22:12 DP1 TP0] Decode batch. #running-req: 1, #token: 1907, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.65, #queue-req: 0, 
[2025-07-28 01:22:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.46, #queue-req: 0, 
[2025-07-28 01:22:12 DP1 TP0] Decode batch. #running-req: 1, #token: 1947, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.66, #queue-req: 0, 
[2025-07-28 01:22:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.56, #queue-req: 0, 
[2025-07-28 01:22:13 DP1 TP0] Decode batch. #running-req: 1, #token: 1987, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.58, #queue-req: 0, 
[2025-07-28 01:22:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.61, #queue-req: 0, 
[2025-07-28 01:22:13 DP1 TP0] Decode batch. #running-req: 1, #token: 2027, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.56, #queue-req: 0, 
[2025-07-28 01:22:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.43, #queue-req: 0, 
[2025-07-28 01:22:13 DP1 TP0] Decode batch. #running-req: 1, #token: 2067, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.46, #queue-req: 0, 
[2025-07-28 01:22:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.54, #queue-req: 0, 
[2025-07-28 01:22:13 DP1 TP0] Decode batch. #running-req: 1, #token: 2107, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.30, #queue-req: 0, 
[2025-07-28 01:22:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1654, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.37, #queue-req: 0, 
[2025-07-28 01:22:13 DP1 TP0] Decode batch. #running-req: 1, #token: 2147, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.20, #queue-req: 0, 
[2025-07-28 01:22:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1694, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.32, #queue-req: 0, 
[2025-07-28 01:22:14 DP1 TP0] Decode batch. #running-req: 1, #token: 2187, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.19, #queue-req: 0, 
[2025-07-28 01:22:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1734, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.33, #queue-req: 0, 
[2025-07-28 01:22:14 DP1 TP0] Decode batch. #running-req: 1, #token: 2227, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.07, #queue-req: 0, 
[2025-07-28 01:22:14] INFO:     127.0.0.1:59780 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:22:14 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 154, #cached-token: 88, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:22:14 DP1 TP0] Decode batch. #running-req: 2, #token: 2443, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.28, #queue-req: 0, 
[2025-07-28 01:22:14 DP1 TP0] Decode batch. #running-req: 2, #token: 2523, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.93, #queue-req: 0, 
[2025-07-28 01:22:15 DP1 TP0] Decode batch. #running-req: 2, #token: 2603, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.75, #queue-req: 0, 
[2025-07-28 01:22:15 DP1 TP0] Decode batch. #running-req: 2, #token: 2683, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.32, #queue-req: 0, 
[2025-07-28 01:22:15 DP1 TP0] Decode batch. #running-req: 2, #token: 2763, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.46, #queue-req: 0, 
[2025-07-28 01:22:15 DP1 TP0] Decode batch. #running-req: 2, #token: 2843, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.06, #queue-req: 0, 
[2025-07-28 01:22:16 DP1 TP0] Decode batch. #running-req: 2, #token: 2923, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.87, #queue-req: 0, 
[2025-07-28 01:22:16 DP1 TP0] Decode batch. #running-req: 2, #token: 3003, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.95, #queue-req: 0, 
[2025-07-28 01:22:16 DP1 TP0] Decode batch. #running-req: 2, #token: 3083, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.68, #queue-req: 0, 
[2025-07-28 01:22:16 DP1 TP0] Decode batch. #running-req: 2, #token: 3163, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.54, #queue-req: 0, 
[2025-07-28 01:22:17 DP1 TP0] Decode batch. #running-req: 2, #token: 3243, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.68, #queue-req: 0, 
[2025-07-28 01:22:17 DP1 TP0] Decode batch. #running-req: 2, #token: 3323, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.74, #queue-req: 0, 
[2025-07-28 01:22:17 DP1 TP0] Decode batch. #running-req: 2, #token: 3403, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.31, #queue-req: 0, 
[2025-07-28 01:22:17 DP1 TP0] Decode batch. #running-req: 2, #token: 3483, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.69, #queue-req: 0, 
[2025-07-28 01:22:18 DP1 TP0] Decode batch. #running-req: 2, #token: 3563, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.34, #queue-req: 0, 
[2025-07-28 01:22:18 DP1 TP0] Decode batch. #running-req: 2, #token: 3643, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.62, #queue-req: 0, 
[2025-07-28 01:22:18 DP1 TP0] Decode batch. #running-req: 2, #token: 3723, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.52, #queue-req: 0, 
[2025-07-28 01:22:18 DP1 TP0] Decode batch. #running-req: 2, #token: 3803, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.84, #queue-req: 0, 
[2025-07-28 01:22:19 DP1 TP0] Decode batch. #running-req: 2, #token: 3883, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.24, #queue-req: 0, 
[2025-07-28 01:22:19 DP1 TP0] Decode batch. #running-req: 2, #token: 3963, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.86, #queue-req: 0, 
[2025-07-28 01:22:19 DP1 TP0] Decode batch. #running-req: 2, #token: 4043, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.21, #queue-req: 0, 
[2025-07-28 01:22:19 DP1 TP0] Decode batch. #running-req: 2, #token: 4123, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.93, #queue-req: 0, 
[2025-07-28 01:22:20 DP1 TP0] Decode batch. #running-req: 2, #token: 4203, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.01, #queue-req: 0, 
[2025-07-28 01:22:20 DP1 TP0] Decode batch. #running-req: 2, #token: 4283, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.27, #queue-req: 0, 
[2025-07-28 01:22:20] INFO:     127.0.0.1:60284 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:22:20 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 128, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:22:20 DP1 TP0] Decode batch. #running-req: 1, #token: 3227, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.48, #queue-req: 0, 
[2025-07-28 01:22:20 DP0 TP0] Decode batch. #running-req: 1, #token: 225, token usage: 0.00, cuda graph: True, gen throughput (token/s): 6.17, #queue-req: 0, 
[2025-07-28 01:22:20 DP1 TP0] Decode batch. #running-req: 1, #token: 3267, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.06, #queue-req: 0, 
[2025-07-28 01:22:20 DP0 TP0] Decode batch. #running-req: 1, #token: 265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 187.02, #queue-req: 0, 
[2025-07-28 01:22:21 DP1 TP0] Decode batch. #running-req: 1, #token: 3307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.09, #queue-req: 0, 
[2025-07-28 01:22:21 DP0 TP0] Decode batch. #running-req: 1, #token: 305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.67, #queue-req: 0, 
[2025-07-28 01:22:21 DP1 TP0] Decode batch. #running-req: 1, #token: 3347, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.02, #queue-req: 0, 
[2025-07-28 01:22:21 DP0 TP0] Decode batch. #running-req: 1, #token: 345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.03, #queue-req: 0, 
[2025-07-28 01:22:21 DP1 TP0] Decode batch. #running-req: 1, #token: 3387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.01, #queue-req: 0, 
[2025-07-28 01:22:21 DP0 TP0] Decode batch. #running-req: 1, #token: 385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.76, #queue-req: 0, 
[2025-07-28 01:22:21 DP1 TP0] Decode batch. #running-req: 1, #token: 3427, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.88, #queue-req: 0, 
[2025-07-28 01:22:21 DP0 TP0] Decode batch. #running-req: 1, #token: 425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.95, #queue-req: 0, 
[2025-07-28 01:22:21 DP1 TP0] Decode batch. #running-req: 1, #token: 3467, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.29, #queue-req: 0, 
[2025-07-28 01:22:21 DP0 TP0] Decode batch. #running-req: 1, #token: 465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.88, #queue-req: 0, 
[2025-07-28 01:22:22 DP1 TP0] Decode batch. #running-req: 1, #token: 3507, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.34, #queue-req: 0, 
[2025-07-28 01:22:22 DP0 TP0] Decode batch. #running-req: 1, #token: 505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.47, #queue-req: 0, 
[2025-07-28 01:22:22 DP1 TP0] Decode batch. #running-req: 1, #token: 3547, token usage: 0.00, cuda graph: True, gen throughput (token/s): 206.87, #queue-req: 0, 
[2025-07-28 01:22:22 DP0 TP0] Decode batch. #running-req: 1, #token: 545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.10, #queue-req: 0, 
[2025-07-28 01:22:22 DP1 TP0] Decode batch. #running-req: 1, #token: 3587, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.54, #queue-req: 0, 
[2025-07-28 01:22:22 DP0 TP0] Decode batch. #running-req: 1, #token: 585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.39, #queue-req: 0, 
[2025-07-28 01:22:22 DP1 TP0] Decode batch. #running-req: 1, #token: 3627, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.73, #queue-req: 0, 
[2025-07-28 01:22:22 DP0 TP0] Decode batch. #running-req: 1, #token: 625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.60, #queue-req: 0, 
[2025-07-28 01:22:22 DP1 TP0] Decode batch. #running-req: 1, #token: 3667, token usage: 0.00, cuda graph: True, gen throughput (token/s): 206.71, #queue-req: 0, 
[2025-07-28 01:22:22 DP0 TP0] Decode batch. #running-req: 1, #token: 665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.96, #queue-req: 0, 
[2025-07-28 01:22:23 DP1 TP0] Decode batch. #running-req: 1, #token: 3707, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.64, #queue-req: 0, 
[2025-07-28 01:22:23 DP0 TP0] Decode batch. #running-req: 1, #token: 705, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.22, #queue-req: 0, 
[2025-07-28 01:22:23 DP1 TP0] Decode batch. #running-req: 1, #token: 3747, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.59, #queue-req: 0, 
[2025-07-28 01:22:23 DP0 TP0] Decode batch. #running-req: 1, #token: 745, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.05, #queue-req: 0, 
[2025-07-28 01:22:23 DP1 TP0] Decode batch. #running-req: 1, #token: 3787, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.83, #queue-req: 0, 
[2025-07-28 01:22:23 DP0 TP0] Decode batch. #running-req: 1, #token: 785, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.01, #queue-req: 0, 
[2025-07-28 01:22:23 DP1 TP0] Decode batch. #running-req: 1, #token: 3827, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.79, #queue-req: 0, 
[2025-07-28 01:22:23 DP0 TP0] Decode batch. #running-req: 1, #token: 825, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.04, #queue-req: 0, 
[2025-07-28 01:22:23 DP1 TP0] Decode batch. #running-req: 1, #token: 3867, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.55, #queue-req: 0, 
[2025-07-28 01:22:23 DP0 TP0] Decode batch. #running-req: 1, #token: 865, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.86, #queue-req: 0, 
[2025-07-28 01:22:23 DP1 TP0] Decode batch. #running-req: 1, #token: 3907, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.45, #queue-req: 0, 
[2025-07-28 01:22:23 DP0 TP0] Decode batch. #running-req: 1, #token: 905, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.12, #queue-req: 0, 
[2025-07-28 01:22:24 DP1 TP0] Decode batch. #running-req: 1, #token: 3947, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.01, #queue-req: 0, 
[2025-07-28 01:22:24 DP0 TP0] Decode batch. #running-req: 1, #token: 945, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.14, #queue-req: 0, 
[2025-07-28 01:22:24 DP1 TP0] Decode batch. #running-req: 1, #token: 3987, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.12, #queue-req: 0, 
[2025-07-28 01:22:24 DP0 TP0] Decode batch. #running-req: 1, #token: 985, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.70, #queue-req: 0, 
[2025-07-28 01:22:24 DP1 TP0] Decode batch. #running-req: 1, #token: 4027, token usage: 0.00, cuda graph: True, gen throughput (token/s): 206.49, #queue-req: 0, 
[2025-07-28 01:22:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1025, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.75, #queue-req: 0, 
[2025-07-28 01:22:24 DP1 TP0] Decode batch. #running-req: 1, #token: 4067, token usage: 0.00, cuda graph: True, gen throughput (token/s): 206.53, #queue-req: 0, 
[2025-07-28 01:22:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1065, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.81, #queue-req: 0, 
[2025-07-28 01:22:24 DP1 TP0] Decode batch. #running-req: 1, #token: 4107, token usage: 0.00, cuda graph: True, gen throughput (token/s): 206.67, #queue-req: 0, 
[2025-07-28 01:22:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1105, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.97, #queue-req: 0, 
[2025-07-28 01:22:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1145, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.87, #queue-req: 0, 
[2025-07-28 01:22:25 DP1 TP0] Decode batch. #running-req: 1, #token: 4147, token usage: 0.00, cuda graph: True, gen throughput (token/s): 205.94, #queue-req: 0, 
[2025-07-28 01:22:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1185, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.64, #queue-req: 0, 
[2025-07-28 01:22:25 DP1 TP0] Decode batch. #running-req: 1, #token: 4187, token usage: 0.00, cuda graph: True, gen throughput (token/s): 205.74, #queue-req: 0, 
[2025-07-28 01:22:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1225, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.27, #queue-req: 0, 
[2025-07-28 01:22:25 DP1 TP0] Decode batch. #running-req: 1, #token: 4227, token usage: 0.00, cuda graph: True, gen throughput (token/s): 204.83, #queue-req: 0, 
[2025-07-28 01:22:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.59, #queue-req: 0, 
[2025-07-28 01:22:25 DP1 TP0] Decode batch. #running-req: 1, #token: 4267, token usage: 0.00, cuda graph: True, gen throughput (token/s): 204.44, #queue-req: 0, 
[2025-07-28 01:22:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.91, #queue-req: 0, 
[2025-07-28 01:22:25 DP1 TP0] Decode batch. #running-req: 1, #token: 4307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 204.46, #queue-req: 0, 
[2025-07-28 01:22:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.22, #queue-req: 0, 
[2025-07-28 01:22:26 DP1 TP0] Decode batch. #running-req: 1, #token: 4347, token usage: 0.00, cuda graph: True, gen throughput (token/s): 204.11, #queue-req: 0, 
[2025-07-28 01:22:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.42, #queue-req: 0, 
[2025-07-28 01:22:26 DP1 TP0] Decode batch. #running-req: 1, #token: 4387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 203.85, #queue-req: 0, 
[2025-07-28 01:22:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.23, #queue-req: 0, 
[2025-07-28 01:22:26 DP1 TP0] Decode batch. #running-req: 1, #token: 4427, token usage: 0.00, cuda graph: True, gen throughput (token/s): 203.91, #queue-req: 0, 
[2025-07-28 01:22:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 206.87, #queue-req: 0, 
[2025-07-28 01:22:26 DP1 TP0] Decode batch. #running-req: 1, #token: 4467, token usage: 0.00, cuda graph: True, gen throughput (token/s): 203.54, #queue-req: 0, 
[2025-07-28 01:22:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.36, #queue-req: 0, 
[2025-07-28 01:22:26 DP1 TP0] Decode batch. #running-req: 1, #token: 4507, token usage: 0.00, cuda graph: True, gen throughput (token/s): 203.46, #queue-req: 0, 
[2025-07-28 01:22:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.22, #queue-req: 0, 
[2025-07-28 01:22:27 DP1 TP0] Decode batch. #running-req: 1, #token: 4547, token usage: 0.00, cuda graph: True, gen throughput (token/s): 203.72, #queue-req: 0, 
[2025-07-28 01:22:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 206.81, #queue-req: 0, 
[2025-07-28 01:22:27 DP1 TP0] Decode batch. #running-req: 1, #token: 4587, token usage: 0.00, cuda graph: True, gen throughput (token/s): 203.91, #queue-req: 0, 
[2025-07-28 01:22:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.24, #queue-req: 0, 
[2025-07-28 01:22:27 DP1 TP0] Decode batch. #running-req: 1, #token: 4627, token usage: 0.00, cuda graph: True, gen throughput (token/s): 203.83, #queue-req: 0, 
[2025-07-28 01:22:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 206.98, #queue-req: 0, 
[2025-07-28 01:22:27 DP1 TP0] Decode batch. #running-req: 1, #token: 4667, token usage: 0.00, cuda graph: True, gen throughput (token/s): 203.97, #queue-req: 0, 
[2025-07-28 01:22:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1705, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.08, #queue-req: 0, 
[2025-07-28 01:22:27 DP1 TP0] Decode batch. #running-req: 1, #token: 4707, token usage: 0.00, cuda graph: True, gen throughput (token/s): 203.71, #queue-req: 0, 
[2025-07-28 01:22:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1745, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.21, #queue-req: 0, 
[2025-07-28 01:22:28 DP1 TP0] Decode batch. #running-req: 1, #token: 4747, token usage: 0.00, cuda graph: True, gen throughput (token/s): 203.93, #queue-req: 0, 
[2025-07-28 01:22:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1785, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.14, #queue-req: 0, 
[2025-07-28 01:22:28 DP1 TP0] Decode batch. #running-req: 1, #token: 4787, token usage: 0.00, cuda graph: True, gen throughput (token/s): 203.91, #queue-req: 0, 
[2025-07-28 01:22:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1825, token usage: 0.00, cuda graph: True, gen throughput (token/s): 206.78, #queue-req: 0, 
[2025-07-28 01:22:28 DP1 TP0] Decode batch. #running-req: 1, #token: 4827, token usage: 0.00, cuda graph: True, gen throughput (token/s): 203.75, #queue-req: 0, 
[2025-07-28 01:22:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1865, token usage: 0.00, cuda graph: True, gen throughput (token/s): 206.74, #queue-req: 0, 
[2025-07-28 01:22:28 DP1 TP0] Decode batch. #running-req: 1, #token: 4867, token usage: 0.00, cuda graph: True, gen throughput (token/s): 204.10, #queue-req: 0, 
[2025-07-28 01:22:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1905, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.07, #queue-req: 0, 
[2025-07-28 01:22:28 DP1 TP0] Decode batch. #running-req: 1, #token: 4907, token usage: 0.00, cuda graph: True, gen throughput (token/s): 204.22, #queue-req: 0, 
[2025-07-28 01:22:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1945, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.86, #queue-req: 0, 
[2025-07-28 01:22:29 DP1 TP0] Decode batch. #running-req: 1, #token: 4947, token usage: 0.00, cuda graph: True, gen throughput (token/s): 205.24, #queue-req: 0, 
[2025-07-28 01:22:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1985, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.63, #queue-req: 0, 
[2025-07-28 01:22:29 DP1 TP0] Decode batch. #running-req: 1, #token: 4987, token usage: 0.00, cuda graph: True, gen throughput (token/s): 205.85, #queue-req: 0, 
[2025-07-28 01:22:29 DP0 TP0] Decode batch. #running-req: 1, #token: 2025, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.03, #queue-req: 0, 
[2025-07-28 01:22:29 DP1 TP0] Decode batch. #running-req: 1, #token: 5027, token usage: 0.00, cuda graph: True, gen throughput (token/s): 206.19, #queue-req: 0, 
[2025-07-28 01:22:29 DP0 TP0] Decode batch. #running-req: 1, #token: 2065, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.96, #queue-req: 0, 
[2025-07-28 01:22:29 DP1 TP0] Decode batch. #running-req: 1, #token: 5067, token usage: 0.00, cuda graph: True, gen throughput (token/s): 206.33, #queue-req: 0, 
[2025-07-28 01:22:29 DP0 TP0] Decode batch. #running-req: 1, #token: 2105, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.99, #queue-req: 0, 
[2025-07-28 01:22:29 DP1 TP0] Decode batch. #running-req: 1, #token: 5107, token usage: 0.00, cuda graph: True, gen throughput (token/s): 206.45, #queue-req: 0, 
[2025-07-28 01:22:29 DP0 TP0] Decode batch. #running-req: 1, #token: 2145, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.99, #queue-req: 0, 
[2025-07-28 01:22:30 DP1 TP0] Decode batch. #running-req: 1, #token: 5147, token usage: 0.00, cuda graph: True, gen throughput (token/s): 205.09, #queue-req: 0, 
[2025-07-28 01:22:30 DP0 TP0] Decode batch. #running-req: 1, #token: 2185, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.70, #queue-req: 0, 
[2025-07-28 01:22:30 DP1 TP0] Decode batch. #running-req: 1, #token: 5187, token usage: 0.00, cuda graph: True, gen throughput (token/s): 205.66, #queue-req: 0, 
[2025-07-28 01:22:30 DP0 TP0] Decode batch. #running-req: 1, #token: 2225, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.79, #queue-req: 0, 
[2025-07-28 01:22:30 DP1 TP0] Decode batch. #running-req: 1, #token: 5227, token usage: 0.00, cuda graph: True, gen throughput (token/s): 205.69, #queue-req: 0, 
[2025-07-28 01:22:30 DP0 TP0] Decode batch. #running-req: 1, #token: 2265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.60, #queue-req: 0, 
[2025-07-28 01:22:30 DP1 TP0] Decode batch. #running-req: 1, #token: 5267, token usage: 0.00, cuda graph: True, gen throughput (token/s): 205.62, #queue-req: 0, 
[2025-07-28 01:22:30 DP0 TP0] Decode batch. #running-req: 1, #token: 2305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.87, #queue-req: 0, 
[2025-07-28 01:22:30 DP1 TP0] Decode batch. #running-req: 1, #token: 5307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 205.41, #queue-req: 0, 
[2025-07-28 01:22:30 DP0 TP0] Decode batch. #running-req: 1, #token: 2345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.54, #queue-req: 0, 
[2025-07-28 01:22:30 DP1 TP0] Decode batch. #running-req: 1, #token: 5347, token usage: 0.00, cuda graph: True, gen throughput (token/s): 205.44, #queue-req: 0, 
[2025-07-28 01:22:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.19, #queue-req: 0, 
[2025-07-28 01:22:31] INFO:     127.0.0.1:60286 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:22:31 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:22:31 DP1 TP0] Decode batch. #running-req: 2, #token: 5539, token usage: 0.00, cuda graph: True, gen throughput (token/s): 174.15, #queue-req: 0, 
[2025-07-28 01:22:31 DP1 TP0] Decode batch. #running-req: 2, #token: 5619, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.14, #queue-req: 0, 
[2025-07-28 01:22:31 DP1 TP0] Decode batch. #running-req: 2, #token: 5699, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.20, #queue-req: 0, 
[2025-07-28 01:22:32 DP1 TP0] Decode batch. #running-req: 2, #token: 5779, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.55, #queue-req: 0, 
[2025-07-28 01:22:32 DP1 TP0] Decode batch. #running-req: 2, #token: 5859, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.46, #queue-req: 0, 
[2025-07-28 01:22:32 DP1 TP0] Decode batch. #running-req: 2, #token: 5939, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.52, #queue-req: 0, 
[2025-07-28 01:22:32 DP1 TP0] Decode batch. #running-req: 2, #token: 6019, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.65, #queue-req: 0, 
[2025-07-28 01:22:33 DP1 TP0] Decode batch. #running-req: 2, #token: 6099, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.71, #queue-req: 0, 
[2025-07-28 01:22:33 DP1 TP0] Decode batch. #running-req: 2, #token: 6179, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.84, #queue-req: 0, 
[2025-07-28 01:22:33 DP1 TP0] Decode batch. #running-req: 2, #token: 6259, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.67, #queue-req: 0, 
[2025-07-28 01:22:33 DP1 TP0] Decode batch. #running-req: 2, #token: 6339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.49, #queue-req: 0, 
[2025-07-28 01:22:34 DP1 TP0] Decode batch. #running-req: 2, #token: 6419, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.38, #queue-req: 0, 
[2025-07-28 01:22:34 DP1 TP0] Decode batch. #running-req: 2, #token: 6499, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.25, #queue-req: 0, 
[2025-07-28 01:22:34 DP1 TP0] Decode batch. #running-req: 2, #token: 6579, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.91, #queue-req: 0, 
[2025-07-28 01:22:34 DP1 TP0] Decode batch. #running-req: 2, #token: 6659, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.64, #queue-req: 0, 
[2025-07-28 01:22:35 DP1 TP0] Decode batch. #running-req: 2, #token: 6739, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.91, #queue-req: 0, 
[2025-07-28 01:22:35 DP1 TP0] Decode batch. #running-req: 2, #token: 6819, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.07, #queue-req: 0, 
[2025-07-28 01:22:35 DP1 TP0] Decode batch. #running-req: 2, #token: 6899, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.26, #queue-req: 0, 
[2025-07-28 01:22:35 DP1 TP0] Decode batch. #running-req: 2, #token: 6979, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.92, #queue-req: 0, 
[2025-07-28 01:22:36 DP1 TP0] Decode batch. #running-req: 2, #token: 7059, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.32, #queue-req: 0, 
[2025-07-28 01:22:36] INFO:     127.0.0.1:59778 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:22:36 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:22:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1039, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.68, #queue-req: 0, 
[2025-07-28 01:22:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1079, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.12, #queue-req: 0, 
[2025-07-28 01:22:36 DP0 TP0] Decode batch. #running-req: 1, #token: 260, token usage: 0.00, cuda graph: True, gen throughput (token/s): 7.16, #queue-req: 0, 
[2025-07-28 01:22:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1119, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.83, #queue-req: 0, 
[2025-07-28 01:22:36 DP0 TP0] Decode batch. #running-req: 1, #token: 300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.30, #queue-req: 0, 
[2025-07-28 01:22:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1159, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.94, #queue-req: 0, 
[2025-07-28 01:22:37 DP0 TP0] Decode batch. #running-req: 1, #token: 340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.78, #queue-req: 0, 
[2025-07-28 01:22:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1199, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.95, #queue-req: 0, 
[2025-07-28 01:22:37 DP0 TP0] Decode batch. #running-req: 1, #token: 380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.44, #queue-req: 0, 
[2025-07-28 01:22:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1239, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.87, #queue-req: 0, 
[2025-07-28 01:22:37 DP0 TP0] Decode batch. #running-req: 1, #token: 420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.75, #queue-req: 0, 
[2025-07-28 01:22:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.08, #queue-req: 0, 
[2025-07-28 01:22:37 DP0 TP0] Decode batch. #running-req: 1, #token: 460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.99, #queue-req: 0, 
[2025-07-28 01:22:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.02, #queue-req: 0, 
[2025-07-28 01:22:37 DP0 TP0] Decode batch. #running-req: 1, #token: 500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.90, #queue-req: 0, 
[2025-07-28 01:22:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.81, #queue-req: 0, 
[2025-07-28 01:22:38 DP0 TP0] Decode batch. #running-req: 1, #token: 540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.10, #queue-req: 0, 
[2025-07-28 01:22:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.45, #queue-req: 0, 
[2025-07-28 01:22:38 DP0 TP0] Decode batch. #running-req: 1, #token: 580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.90, #queue-req: 0, 
[2025-07-28 01:22:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.66, #queue-req: 0, 
[2025-07-28 01:22:38 DP0 TP0] Decode batch. #running-req: 1, #token: 620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.83, #queue-req: 0, 
[2025-07-28 01:22:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.91, #queue-req: 0, 
[2025-07-28 01:22:38 DP0 TP0] Decode batch. #running-req: 1, #token: 660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.87, #queue-req: 0, 
[2025-07-28 01:22:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.57, #queue-req: 0, 
[2025-07-28 01:22:38 DP0 TP0] Decode batch. #running-req: 1, #token: 700, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.71, #queue-req: 0, 
[2025-07-28 01:22:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.79, #queue-req: 0, 
[2025-07-28 01:22:38 DP0 TP0] Decode batch. #running-req: 1, #token: 740, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.77, #queue-req: 0, 
[2025-07-28 01:22:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.65, #queue-req: 0, 
[2025-07-28 01:22:39 DP0 TP0] Decode batch. #running-req: 1, #token: 780, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.84, #queue-req: 0, 
[2025-07-28 01:22:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.80, #queue-req: 0, 
[2025-07-28 01:22:39 DP0 TP0] Decode batch. #running-req: 1, #token: 820, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.79, #queue-req: 0, 
[2025-07-28 01:22:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.63, #queue-req: 0, 
[2025-07-28 01:22:39 DP0 TP0] Decode batch. #running-req: 1, #token: 860, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.81, #queue-req: 0, 
[2025-07-28 01:22:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1719, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.61, #queue-req: 0, 
[2025-07-28 01:22:39 DP0 TP0] Decode batch. #running-req: 1, #token: 900, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.80, #queue-req: 0, 
[2025-07-28 01:22:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1759, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.52, #queue-req: 0, 
[2025-07-28 01:22:39 DP0 TP0] Decode batch. #running-req: 1, #token: 940, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.75, #queue-req: 0, 
[2025-07-28 01:22:39] INFO:     127.0.0.1:49370 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:22:39 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 95, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:22:40 DP0 TP0] Decode batch. #running-req: 1, #token: 980, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.47, #queue-req: 0, 
[2025-07-28 01:22:40 DP1 TP0] Decode batch. #running-req: 1, #token: 204, token usage: 0.00, cuda graph: True, gen throughput (token/s): 148.73, #queue-req: 0, 
[2025-07-28 01:22:40 DP0 TP0] Decode batch. #running-req: 1, #token: 1020, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.62, #queue-req: 0, 
[2025-07-28 01:22:40 DP1 TP0] Decode batch. #running-req: 1, #token: 244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.54, #queue-req: 0, 
[2025-07-28 01:22:40 DP0 TP0] Decode batch. #running-req: 1, #token: 1060, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.06, #queue-req: 0, 
[2025-07-28 01:22:40 DP1 TP0] Decode batch. #running-req: 1, #token: 284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.46, #queue-req: 0, 
[2025-07-28 01:22:40 DP0 TP0] Decode batch. #running-req: 1, #token: 1100, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.84, #queue-req: 0, 
[2025-07-28 01:22:40 DP1 TP0] Decode batch. #running-req: 1, #token: 324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.26, #queue-req: 0, 
[2025-07-28 01:22:40 DP0 TP0] Decode batch. #running-req: 1, #token: 1140, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.99, #queue-req: 0, 
[2025-07-28 01:22:40 DP1 TP0] Decode batch. #running-req: 1, #token: 364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.30, #queue-req: 0, 
[2025-07-28 01:22:41 DP0 TP0] Decode batch. #running-req: 1, #token: 1180, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.84, #queue-req: 0, 
[2025-07-28 01:22:41 DP1 TP0] Decode batch. #running-req: 1, #token: 404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.22, #queue-req: 0, 
[2025-07-28 01:22:41] INFO:     127.0.0.1:33654 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:22:41 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:22:41 DP1 TP0] Decode batch. #running-req: 1, #token: 444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.27, #queue-req: 0, 
[2025-07-28 01:22:41 DP0 TP0] Decode batch. #running-req: 1, #token: 234, token usage: 0.00, cuda graph: True, gen throughput (token/s): 147.74, #queue-req: 0, 
[2025-07-28 01:22:41 DP1 TP0] Decode batch. #running-req: 1, #token: 484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.02, #queue-req: 0, 
[2025-07-28 01:22:41 DP0 TP0] Decode batch. #running-req: 1, #token: 274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.95, #queue-req: 0, 
[2025-07-28 01:22:41 DP1 TP0] Decode batch. #running-req: 1, #token: 524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.17, #queue-req: 0, 
[2025-07-28 01:22:41 DP0 TP0] Decode batch. #running-req: 1, #token: 314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.71, #queue-req: 0, 
[2025-07-28 01:22:41 DP1 TP0] Decode batch. #running-req: 1, #token: 564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.97, #queue-req: 0, 
[2025-07-28 01:22:41 DP0 TP0] Decode batch. #running-req: 1, #token: 354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.58, #queue-req: 0, 
[2025-07-28 01:22:42 DP1 TP0] Decode batch. #running-req: 1, #token: 604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.83, #queue-req: 0, 
[2025-07-28 01:22:42 DP0 TP0] Decode batch. #running-req: 1, #token: 394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.84, #queue-req: 0, 
[2025-07-28 01:22:42 DP1 TP0] Decode batch. #running-req: 1, #token: 644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.94, #queue-req: 0, 
[2025-07-28 01:22:42 DP0 TP0] Decode batch. #running-req: 1, #token: 434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.19, #queue-req: 0, 
[2025-07-28 01:22:42 DP1 TP0] Decode batch. #running-req: 1, #token: 684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.23, #queue-req: 0, 
[2025-07-28 01:22:42 DP0 TP0] Decode batch. #running-req: 1, #token: 474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.74, #queue-req: 0, 
[2025-07-28 01:22:42 DP1 TP0] Decode batch. #running-req: 1, #token: 724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.22, #queue-req: 0, 
[2025-07-28 01:22:42 DP0 TP0] Decode batch. #running-req: 1, #token: 514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.75, #queue-req: 0, 
[2025-07-28 01:22:42 DP1 TP0] Decode batch. #running-req: 1, #token: 764, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.49, #queue-req: 0, 
[2025-07-28 01:22:42 DP0 TP0] Decode batch. #running-req: 1, #token: 554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.50, #queue-req: 0, 
[2025-07-28 01:22:42 DP1 TP0] Decode batch. #running-req: 1, #token: 804, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.92, #queue-req: 0, 
[2025-07-28 01:22:43 DP0 TP0] Decode batch. #running-req: 1, #token: 594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.66, #queue-req: 0, 
[2025-07-28 01:22:43 DP1 TP0] Decode batch. #running-req: 1, #token: 844, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.86, #queue-req: 0, 
[2025-07-28 01:22:43 DP0 TP0] Decode batch. #running-req: 1, #token: 634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.55, #queue-req: 0, 
[2025-07-28 01:22:43 DP1 TP0] Decode batch. #running-req: 1, #token: 884, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.97, #queue-req: 0, 
[2025-07-28 01:22:43 DP0 TP0] Decode batch. #running-req: 1, #token: 674, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.70, #queue-req: 0, 
[2025-07-28 01:22:43 DP1 TP0] Decode batch. #running-req: 1, #token: 924, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.00, #queue-req: 0, 
[2025-07-28 01:22:43 DP0 TP0] Decode batch. #running-req: 1, #token: 714, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.80, #queue-req: 0, 
[2025-07-28 01:22:43 DP1 TP0] Decode batch. #running-req: 1, #token: 964, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.92, #queue-req: 0, 
[2025-07-28 01:22:43 DP0 TP0] Decode batch. #running-req: 1, #token: 754, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.75, #queue-req: 0, 
[2025-07-28 01:22:43] INFO:     127.0.0.1:33664 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:22:43 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 78, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:22:43 DP0 TP0] Decode batch. #running-req: 1, #token: 794, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.52, #queue-req: 0, 
[2025-07-28 01:22:44 DP1 TP0] Decode batch. #running-req: 1, #token: 181, token usage: 0.00, cuda graph: True, gen throughput (token/s): 150.69, #queue-req: 0, 
[2025-07-28 01:22:44 DP0 TP0] Decode batch. #running-req: 1, #token: 834, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.62, #queue-req: 0, 
[2025-07-28 01:22:44 DP1 TP0] Decode batch. #running-req: 1, #token: 221, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.62, #queue-req: 0, 
[2025-07-28 01:22:44 DP0 TP0] Decode batch. #running-req: 1, #token: 874, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.11, #queue-req: 0, 
[2025-07-28 01:22:44 DP1 TP0] Decode batch. #running-req: 1, #token: 261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.67, #queue-req: 0, 
[2025-07-28 01:22:44 DP0 TP0] Decode batch. #running-req: 1, #token: 914, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.12, #queue-req: 0, 
[2025-07-28 01:22:44 DP1 TP0] Decode batch. #running-req: 1, #token: 301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.68, #queue-req: 0, 
[2025-07-28 01:22:44 DP0 TP0] Decode batch. #running-req: 1, #token: 954, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.94, #queue-req: 0, 
[2025-07-28 01:22:44 DP1 TP0] Decode batch. #running-req: 1, #token: 341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.43, #queue-req: 0, 
[2025-07-28 01:22:44 DP0 TP0] Decode batch. #running-req: 1, #token: 994, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.31, #queue-req: 0, 
[2025-07-28 01:22:44 DP1 TP0] Decode batch. #running-req: 1, #token: 381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.00, #queue-req: 0, 
[2025-07-28 01:22:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1034, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.23, #queue-req: 0, 
[2025-07-28 01:22:45 DP1 TP0] Decode batch. #running-req: 1, #token: 421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.18, #queue-req: 0, 
[2025-07-28 01:22:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1074, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.68, #queue-req: 0, 
[2025-07-28 01:22:45 DP1 TP0] Decode batch. #running-req: 1, #token: 461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.44, #queue-req: 0, 
[2025-07-28 01:22:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1114, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.68, #queue-req: 0, 
[2025-07-28 01:22:45 DP1 TP0] Decode batch. #running-req: 1, #token: 501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.53, #queue-req: 0, 
[2025-07-28 01:22:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1154, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.73, #queue-req: 0, 
[2025-07-28 01:22:45 DP1 TP0] Decode batch. #running-req: 1, #token: 541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.45, #queue-req: 0, 
[2025-07-28 01:22:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1194, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.55, #queue-req: 0, 
[2025-07-28 01:22:45 DP1 TP0] Decode batch. #running-req: 1, #token: 581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.33, #queue-req: 0, 
[2025-07-28 01:22:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1234, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.64, #queue-req: 0, 
[2025-07-28 01:22:46 DP1 TP0] Decode batch. #running-req: 1, #token: 621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.37, #queue-req: 0, 
[2025-07-28 01:22:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.68, #queue-req: 0, 
[2025-07-28 01:22:46 DP1 TP0] Decode batch. #running-req: 1, #token: 661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.24, #queue-req: 0, 
[2025-07-28 01:22:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.55, #queue-req: 0, 
[2025-07-28 01:22:46 DP1 TP0] Decode batch. #running-req: 1, #token: 701, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.42, #queue-req: 0, 
[2025-07-28 01:22:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.47, #queue-req: 0, 
[2025-07-28 01:22:46 DP1 TP0] Decode batch. #running-req: 1, #token: 741, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.35, #queue-req: 0, 
[2025-07-28 01:22:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.48, #queue-req: 0, 
[2025-07-28 01:22:46 DP1 TP0] Decode batch. #running-req: 1, #token: 781, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.38, #queue-req: 0, 
[2025-07-28 01:22:47 DP0 TP0] Decode batch. #running-req: 1, #token: 1434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.53, #queue-req: 0, 
[2025-07-28 01:22:47 DP1 TP0] Decode batch. #running-req: 1, #token: 821, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.39, #queue-req: 0, 
[2025-07-28 01:22:47 DP0 TP0] Decode batch. #running-req: 1, #token: 1474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.58, #queue-req: 0, 
[2025-07-28 01:22:47 DP1 TP0] Decode batch. #running-req: 1, #token: 861, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.25, #queue-req: 0, 
[2025-07-28 01:22:47 DP0 TP0] Decode batch. #running-req: 1, #token: 1514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.37, #queue-req: 0, 
[2025-07-28 01:22:47 DP1 TP0] Decode batch. #running-req: 1, #token: 901, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.29, #queue-req: 0, 
[2025-07-28 01:22:47 DP0 TP0] Decode batch. #running-req: 1, #token: 1554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.53, #queue-req: 0, 
[2025-07-28 01:22:47 DP1 TP0] Decode batch. #running-req: 1, #token: 941, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.34, #queue-req: 0, 
[2025-07-28 01:22:47 DP0 TP0] Decode batch. #running-req: 1, #token: 1594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.38, #queue-req: 0, 
[2025-07-28 01:22:47 DP1 TP0] Decode batch. #running-req: 1, #token: 981, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.27, #queue-req: 0, 
[2025-07-28 01:22:47 DP1 TP0] Decode batch. #running-req: 1, #token: 1021, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.06, #queue-req: 0, 
[2025-07-28 01:22:47 DP0 TP0] Decode batch. #running-req: 1, #token: 1634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.22, #queue-req: 0, 
[2025-07-28 01:22:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1061, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.30, #queue-req: 0, 
[2025-07-28 01:22:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1674, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.40, #queue-req: 0, 
[2025-07-28 01:22:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1101, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.44, #queue-req: 0, 
[2025-07-28 01:22:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1714, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.44, #queue-req: 0, 
[2025-07-28 01:22:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1141, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.40, #queue-req: 0, 
[2025-07-28 01:22:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1754, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.42, #queue-req: 0, 
[2025-07-28 01:22:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1181, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.42, #queue-req: 0, 
[2025-07-28 01:22:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1794, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.52, #queue-req: 0, 
[2025-07-28 01:22:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1221, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.32, #queue-req: 0, 
[2025-07-28 01:22:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1834, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.14, #queue-req: 0, 
[2025-07-28 01:22:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.09, #queue-req: 0, 
[2025-07-28 01:22:49 DP0 TP0] Decode batch. #running-req: 1, #token: 1874, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.19, #queue-req: 0, 
[2025-07-28 01:22:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.17, #queue-req: 0, 
[2025-07-28 01:22:49 DP0 TP0] Decode batch. #running-req: 1, #token: 1914, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.14, #queue-req: 0, 
[2025-07-28 01:22:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.98, #queue-req: 0, 
[2025-07-28 01:22:49 DP0 TP0] Decode batch. #running-req: 1, #token: 1954, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.33, #queue-req: 0, 
[2025-07-28 01:22:49] INFO:     127.0.0.1:54608 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:22:49 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 146, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:22:49 DP0 TP0] Decode batch. #running-req: 2, #token: 2174, token usage: 0.00, cuda graph: True, gen throughput (token/s): 232.00, #queue-req: 0, 
[2025-07-28 01:22:50 DP0 TP0] Decode batch. #running-req: 2, #token: 2254, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.24, #queue-req: 0, 
[2025-07-28 01:22:50 DP0 TP0] Decode batch. #running-req: 2, #token: 2334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.82, #queue-req: 0, 
[2025-07-28 01:22:50 DP0 TP0] Decode batch. #running-req: 2, #token: 2414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.21, #queue-req: 0, 
[2025-07-28 01:22:50 DP0 TP0] Decode batch. #running-req: 2, #token: 2494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.40, #queue-req: 0, 
[2025-07-28 01:22:51 DP0 TP0] Decode batch. #running-req: 2, #token: 2574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.98, #queue-req: 0, 
[2025-07-28 01:22:51 DP0 TP0] Decode batch. #running-req: 2, #token: 2654, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.62, #queue-req: 0, 
[2025-07-28 01:22:51 DP0 TP0] Decode batch. #running-req: 2, #token: 2734, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.61, #queue-req: 0, 
[2025-07-28 01:22:51 DP0 TP0] Decode batch. #running-req: 2, #token: 2814, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.39, #queue-req: 0, 
[2025-07-28 01:22:52 DP0 TP0] Decode batch. #running-req: 2, #token: 2894, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.82, #queue-req: 0, 
[2025-07-28 01:22:52 DP0 TP0] Decode batch. #running-req: 2, #token: 2974, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.42, #queue-req: 0, 
[2025-07-28 01:22:52 DP0 TP0] Decode batch. #running-req: 2, #token: 3054, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.93, #queue-req: 0, 
[2025-07-28 01:22:52 DP0 TP0] Decode batch. #running-req: 2, #token: 3134, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.38, #queue-req: 0, 
[2025-07-28 01:22:53 DP0 TP0] Decode batch. #running-req: 2, #token: 3214, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.36, #queue-req: 0, 
[2025-07-28 01:22:53 DP0 TP0] Decode batch. #running-req: 2, #token: 3294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.09, #queue-req: 0, 
[2025-07-28 01:22:53 DP0 TP0] Decode batch. #running-req: 2, #token: 3374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.74, #queue-req: 0, 
[2025-07-28 01:22:53 DP0 TP0] Decode batch. #running-req: 2, #token: 3454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.37, #queue-req: 0, 
[2025-07-28 01:22:54 DP0 TP0] Decode batch. #running-req: 2, #token: 3534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.30, #queue-req: 0, 
[2025-07-28 01:22:54 DP0 TP0] Decode batch. #running-req: 2, #token: 3614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.09, #queue-req: 0, 
[2025-07-28 01:22:54 DP0 TP0] Decode batch. #running-req: 2, #token: 3694, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.28, #queue-req: 0, 
[2025-07-28 01:22:54 DP0 TP0] Decode batch. #running-req: 2, #token: 3774, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.34, #queue-req: 0, 
[2025-07-28 01:22:55 DP0 TP0] Decode batch. #running-req: 2, #token: 3854, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.14, #queue-req: 0, 
[2025-07-28 01:22:55 DP0 TP0] Decode batch. #running-req: 2, #token: 3934, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.72, #queue-req: 0, 
[2025-07-28 01:22:55 DP0 TP0] Decode batch. #running-req: 2, #token: 4014, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.75, #queue-req: 0, 
[2025-07-28 01:22:56 DP0 TP0] Decode batch. #running-req: 2, #token: 4094, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.22, #queue-req: 0, 
[2025-07-28 01:22:56 DP0 TP0] Decode batch. #running-req: 2, #token: 4174, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.59, #queue-req: 0, 
[2025-07-28 01:22:56 DP0 TP0] Decode batch. #running-req: 2, #token: 4254, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.45, #queue-req: 0, 
[2025-07-28 01:22:56 DP0 TP0] Decode batch. #running-req: 2, #token: 4334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.74, #queue-req: 0, 
[2025-07-28 01:22:57 DP0 TP0] Decode batch. #running-req: 2, #token: 4414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.34, #queue-req: 0, 
[2025-07-28 01:22:57 DP0 TP0] Decode batch. #running-req: 2, #token: 4494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.11, #queue-req: 0, 
[2025-07-28 01:22:57 DP0 TP0] Decode batch. #running-req: 2, #token: 4574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.91, #queue-req: 0, 
[2025-07-28 01:22:57 DP0 TP0] Decode batch. #running-req: 2, #token: 4654, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.13, #queue-req: 0, 
[2025-07-28 01:22:58 DP0 TP0] Decode batch. #running-req: 2, #token: 4734, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.37, #queue-req: 0, 
[2025-07-28 01:22:58 DP0 TP0] Decode batch. #running-req: 2, #token: 4814, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.78, #queue-req: 0, 
[2025-07-28 01:22:58 DP0 TP0] Decode batch. #running-req: 2, #token: 4894, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.24, #queue-req: 0, 
[2025-07-28 01:22:58 DP0 TP0] Decode batch. #running-req: 2, #token: 4974, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.34, #queue-req: 0, 
[2025-07-28 01:22:59 DP0 TP0] Decode batch. #running-req: 2, #token: 5054, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.99, #queue-req: 0, 
[2025-07-28 01:22:59 DP0 TP0] Decode batch. #running-req: 2, #token: 5134, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.45, #queue-req: 0, 
[2025-07-28 01:22:59] INFO:     127.0.0.1:54620 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:22:59 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:22:59 DP0 TP0] Decode batch. #running-req: 1, #token: 3514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.18, #queue-req: 0, 
[2025-07-28 01:22:59 DP0 TP0] Decode batch. #running-req: 1, #token: 3554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.52, #queue-req: 0, 
[2025-07-28 01:22:59 DP1 TP0] Decode batch. #running-req: 1, #token: 205, token usage: 0.00, cuda graph: True, gen throughput (token/s): 3.88, #queue-req: 0, 
[2025-07-28 01:22:59 DP0 TP0] Decode batch. #running-req: 1, #token: 3594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 206.91, #queue-req: 0, 
[2025-07-28 01:23:00 DP1 TP0] Decode batch. #running-req: 1, #token: 245, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.82, #queue-req: 0, 
[2025-07-28 01:23:00 DP0 TP0] Decode batch. #running-req: 1, #token: 3634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.75, #queue-req: 0, 
[2025-07-28 01:23:00 DP1 TP0] Decode batch. #running-req: 1, #token: 285, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.26, #queue-req: 0, 
[2025-07-28 01:23:00 DP0 TP0] Decode batch. #running-req: 1, #token: 3674, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.43, #queue-req: 0, 
[2025-07-28 01:23:00 DP1 TP0] Decode batch. #running-req: 1, #token: 325, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.50, #queue-req: 0, 
[2025-07-28 01:23:00 DP0 TP0] Decode batch. #running-req: 1, #token: 3714, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.40, #queue-req: 0, 
[2025-07-28 01:23:00 DP1 TP0] Decode batch. #running-req: 1, #token: 365, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.41, #queue-req: 0, 
[2025-07-28 01:23:00 DP0 TP0] Decode batch. #running-req: 1, #token: 3754, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.32, #queue-req: 0, 
[2025-07-28 01:23:00 DP1 TP0] Decode batch. #running-req: 1, #token: 405, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.48, #queue-req: 0, 
[2025-07-28 01:23:00 DP0 TP0] Decode batch. #running-req: 1, #token: 3794, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.36, #queue-req: 0, 
[2025-07-28 01:23:00 DP1 TP0] Decode batch. #running-req: 1, #token: 445, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.35, #queue-req: 0, 
[2025-07-28 01:23:01 DP0 TP0] Decode batch. #running-req: 1, #token: 3834, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.35, #queue-req: 0, 
[2025-07-28 01:23:01 DP1 TP0] Decode batch. #running-req: 1, #token: 485, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.37, #queue-req: 0, 
[2025-07-28 01:23:01 DP0 TP0] Decode batch. #running-req: 1, #token: 3874, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.45, #queue-req: 0, 
[2025-07-28 01:23:01 DP1 TP0] Decode batch. #running-req: 1, #token: 525, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.31, #queue-req: 0, 
[2025-07-28 01:23:01 DP0 TP0] Decode batch. #running-req: 1, #token: 3914, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.23, #queue-req: 0, 
[2025-07-28 01:23:01 DP1 TP0] Decode batch. #running-req: 1, #token: 565, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.21, #queue-req: 0, 
[2025-07-28 01:23:01 DP1 TP0] Decode batch. #running-req: 1, #token: 605, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.08, #queue-req: 0, 
[2025-07-28 01:23:01 DP0 TP0] Decode batch. #running-req: 1, #token: 3954, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.32, #queue-req: 0, 
[2025-07-28 01:23:01 DP1 TP0] Decode batch. #running-req: 1, #token: 645, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.10, #queue-req: 0, 
[2025-07-28 01:23:01 DP0 TP0] Decode batch. #running-req: 1, #token: 3994, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.13, #queue-req: 0, 
[2025-07-28 01:23:02 DP1 TP0] Decode batch. #running-req: 1, #token: 685, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.15, #queue-req: 0, 
[2025-07-28 01:23:02 DP0 TP0] Decode batch. #running-req: 1, #token: 4034, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.32, #queue-req: 0, 
[2025-07-28 01:23:02 DP1 TP0] Decode batch. #running-req: 1, #token: 725, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.21, #queue-req: 0, 
[2025-07-28 01:23:02 DP0 TP0] Decode batch. #running-req: 1, #token: 4074, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.25, #queue-req: 0, 
[2025-07-28 01:23:02 DP1 TP0] Decode batch. #running-req: 1, #token: 765, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.19, #queue-req: 0, 
[2025-07-28 01:23:02 DP0 TP0] Decode batch. #running-req: 1, #token: 4114, token usage: 0.00, cuda graph: True, gen throughput (token/s): 206.87, #queue-req: 0, 
[2025-07-28 01:23:02 DP1 TP0] Decode batch. #running-req: 1, #token: 805, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.24, #queue-req: 0, 
[2025-07-28 01:23:02 DP0 TP0] Decode batch. #running-req: 1, #token: 4154, token usage: 0.00, cuda graph: True, gen throughput (token/s): 206.52, #queue-req: 0, 
[2025-07-28 01:23:02 DP1 TP0] Decode batch. #running-req: 1, #token: 845, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.23, #queue-req: 0, 
[2025-07-28 01:23:02 DP0 TP0] Decode batch. #running-req: 1, #token: 4194, token usage: 0.00, cuda graph: True, gen throughput (token/s): 206.48, #queue-req: 0, 
[2025-07-28 01:23:03 DP1 TP0] Decode batch. #running-req: 1, #token: 885, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.22, #queue-req: 0, 
[2025-07-28 01:23:03 DP0 TP0] Decode batch. #running-req: 1, #token: 4234, token usage: 0.00, cuda graph: True, gen throughput (token/s): 206.58, #queue-req: 0, 
[2025-07-28 01:23:03 DP1 TP0] Decode batch. #running-req: 1, #token: 925, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.16, #queue-req: 0, 
[2025-07-28 01:23:03 DP0 TP0] Decode batch. #running-req: 1, #token: 4274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 206.47, #queue-req: 0, 
[2025-07-28 01:23:03 DP1 TP0] Decode batch. #running-req: 1, #token: 965, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.06, #queue-req: 0, 
[2025-07-28 01:23:03 DP0 TP0] Decode batch. #running-req: 1, #token: 4314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 206.49, #queue-req: 0, 
[2025-07-28 01:23:03 DP1 TP0] Decode batch. #running-req: 1, #token: 1005, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.97, #queue-req: 0, 
[2025-07-28 01:23:03 DP0 TP0] Decode batch. #running-req: 1, #token: 4354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 206.52, #queue-req: 0, 
[2025-07-28 01:23:03 DP1 TP0] Decode batch. #running-req: 1, #token: 1045, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.56, #queue-req: 0, 
[2025-07-28 01:23:03 DP0 TP0] Decode batch. #running-req: 1, #token: 4394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 206.47, #queue-req: 0, 
[2025-07-28 01:23:03] INFO:     127.0.0.1:33670 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:23:03 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 173, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:23:03 DP1 TP0] Decode batch. #running-req: 1, #token: 1085, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.40, #queue-req: 0, 
[2025-07-28 01:23:04] INFO:     127.0.0.1:34496 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:23:04 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 178, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:23:04 DP0 TP0] Decode batch. #running-req: 1, #token: 279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 145.18, #queue-req: 0, 
[2025-07-28 01:23:04 DP1 TP0] Decode batch. #running-req: 1, #token: 298, token usage: 0.00, cuda graph: True, gen throughput (token/s): 149.46, #queue-req: 0, 
[2025-07-28 01:23:04 DP0 TP0] Decode batch. #running-req: 1, #token: 319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.33, #queue-req: 0, 
[2025-07-28 01:23:04 DP1 TP0] Decode batch. #running-req: 1, #token: 338, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.46, #queue-req: 0, 
[2025-07-28 01:23:04 DP0 TP0] Decode batch. #running-req: 1, #token: 359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.20, #queue-req: 0, 
[2025-07-28 01:23:04 DP1 TP0] Decode batch. #running-req: 1, #token: 378, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.41, #queue-req: 0, 
[2025-07-28 01:23:04 DP0 TP0] Decode batch. #running-req: 1, #token: 399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.25, #queue-req: 0, 
[2025-07-28 01:23:04 DP1 TP0] Decode batch. #running-req: 1, #token: 418, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.29, #queue-req: 0, 
[2025-07-28 01:23:04 DP0 TP0] Decode batch. #running-req: 1, #token: 439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.08, #queue-req: 0, 
[2025-07-28 01:23:05 DP1 TP0] Decode batch. #running-req: 1, #token: 458, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.38, #queue-req: 0, 
[2025-07-28 01:23:05 DP0 TP0] Decode batch. #running-req: 1, #token: 479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.15, #queue-req: 0, 
[2025-07-28 01:23:05 DP1 TP0] Decode batch. #running-req: 1, #token: 498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.39, #queue-req: 0, 
[2025-07-28 01:23:05 DP0 TP0] Decode batch. #running-req: 1, #token: 519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.05, #queue-req: 0, 
[2025-07-28 01:23:05 DP1 TP0] Decode batch. #running-req: 1, #token: 538, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.33, #queue-req: 0, 
[2025-07-28 01:23:05 DP0 TP0] Decode batch. #running-req: 1, #token: 559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.98, #queue-req: 0, 
[2025-07-28 01:23:05 DP1 TP0] Decode batch. #running-req: 1, #token: 578, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.23, #queue-req: 0, 
[2025-07-28 01:23:05 DP0 TP0] Decode batch. #running-req: 1, #token: 599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.59, #queue-req: 0, 
[2025-07-28 01:23:05 DP1 TP0] Decode batch. #running-req: 1, #token: 618, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.21, #queue-req: 0, 
[2025-07-28 01:23:05 DP0 TP0] Decode batch. #running-req: 1, #token: 639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.62, #queue-req: 0, 
[2025-07-28 01:23:05 DP1 TP0] Decode batch. #running-req: 1, #token: 658, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.20, #queue-req: 0, 
[2025-07-28 01:23:06 DP0 TP0] Decode batch. #running-req: 1, #token: 679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.77, #queue-req: 0, 
[2025-07-28 01:23:06 DP1 TP0] Decode batch. #running-req: 1, #token: 698, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.20, #queue-req: 0, 
[2025-07-28 01:23:06 DP0 TP0] Decode batch. #running-req: 1, #token: 719, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.74, #queue-req: 0, 
[2025-07-28 01:23:06 DP1 TP0] Decode batch. #running-req: 1, #token: 738, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.24, #queue-req: 0, 
[2025-07-28 01:23:06 DP0 TP0] Decode batch. #running-req: 1, #token: 759, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.87, #queue-req: 0, 
[2025-07-28 01:23:06 DP1 TP0] Decode batch. #running-req: 1, #token: 778, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.20, #queue-req: 0, 
[2025-07-28 01:23:06 DP0 TP0] Decode batch. #running-req: 1, #token: 799, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.70, #queue-req: 0, 
[2025-07-28 01:23:06 DP1 TP0] Decode batch. #running-req: 1, #token: 818, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.15, #queue-req: 0, 
[2025-07-28 01:23:06 DP0 TP0] Decode batch. #running-req: 1, #token: 839, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.77, #queue-req: 0, 
[2025-07-28 01:23:06 DP1 TP0] Decode batch. #running-req: 1, #token: 858, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.05, #queue-req: 0, 
[2025-07-28 01:23:06 DP0 TP0] Decode batch. #running-req: 1, #token: 879, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.72, #queue-req: 0, 
[2025-07-28 01:23:07 DP1 TP0] Decode batch. #running-req: 1, #token: 898, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.98, #queue-req: 0, 
[2025-07-28 01:23:07 DP0 TP0] Decode batch. #running-req: 1, #token: 919, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.90, #queue-req: 0, 
[2025-07-28 01:23:07] INFO:     127.0.0.1:55238 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:23:07 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 88, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:23:07 DP0 TP0] Decode batch. #running-req: 2, #token: 1079, token usage: 0.00, cuda graph: True, gen throughput (token/s): 231.01, #queue-req: 0, 
[2025-07-28 01:23:07] INFO:     127.0.0.1:55222 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:23:07 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 153, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:23:07 DP0 TP0] Decode batch. #running-req: 1, #token: 245, token usage: 0.00, cuda graph: True, gen throughput (token/s): 251.14, #queue-req: 0, 
[2025-07-28 01:23:07 DP1 TP0] Decode batch. #running-req: 1, #token: 264, token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.04, #queue-req: 0, 
[2025-07-28 01:23:07 DP0 TP0] Decode batch. #running-req: 1, #token: 285, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.33, #queue-req: 0, 
[2025-07-28 01:23:07 DP1 TP0] Decode batch. #running-req: 1, #token: 304, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.38, #queue-req: 0, 
[2025-07-28 01:23:08 DP0 TP0] Decode batch. #running-req: 1, #token: 325, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.35, #queue-req: 0, 
[2025-07-28 01:23:08 DP1 TP0] Decode batch. #running-req: 1, #token: 344, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.67, #queue-req: 0, 
[2025-07-28 01:23:08 DP0 TP0] Decode batch. #running-req: 1, #token: 365, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.13, #queue-req: 0, 
[2025-07-28 01:23:08 DP1 TP0] Decode batch. #running-req: 1, #token: 384, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.48, #queue-req: 0, 
[2025-07-28 01:23:08 DP0 TP0] Decode batch. #running-req: 1, #token: 405, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.01, #queue-req: 0, 
[2025-07-28 01:23:08 DP1 TP0] Decode batch. #running-req: 1, #token: 424, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.11, #queue-req: 0, 
[2025-07-28 01:23:08 DP0 TP0] Decode batch. #running-req: 1, #token: 445, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.91, #queue-req: 0, 
[2025-07-28 01:23:08 DP1 TP0] Decode batch. #running-req: 1, #token: 464, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.26, #queue-req: 0, 
[2025-07-28 01:23:08 DP0 TP0] Decode batch. #running-req: 1, #token: 485, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.02, #queue-req: 0, 
[2025-07-28 01:23:08 DP1 TP0] Decode batch. #running-req: 1, #token: 504, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.17, #queue-req: 0, 
[2025-07-28 01:23:09 DP0 TP0] Decode batch. #running-req: 1, #token: 525, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.95, #queue-req: 0, 
[2025-07-28 01:23:09 DP1 TP0] Decode batch. #running-req: 1, #token: 544, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.98, #queue-req: 0, 
[2025-07-28 01:23:09 DP0 TP0] Decode batch. #running-req: 1, #token: 565, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.55, #queue-req: 0, 
[2025-07-28 01:23:09 DP1 TP0] Decode batch. #running-req: 1, #token: 584, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.33, #queue-req: 0, 
[2025-07-28 01:23:09 DP0 TP0] Decode batch. #running-req: 1, #token: 605, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.66, #queue-req: 0, 
[2025-07-28 01:23:09 DP1 TP0] Decode batch. #running-req: 1, #token: 624, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.17, #queue-req: 0, 
[2025-07-28 01:23:09 DP0 TP0] Decode batch. #running-req: 1, #token: 645, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.79, #queue-req: 0, 
[2025-07-28 01:23:09 DP1 TP0] Decode batch. #running-req: 1, #token: 664, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.01, #queue-req: 0, 
[2025-07-28 01:23:09 DP0 TP0] Decode batch. #running-req: 1, #token: 685, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.88, #queue-req: 0, 
[2025-07-28 01:23:09 DP1 TP0] Decode batch. #running-req: 1, #token: 704, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.19, #queue-req: 0, 
[2025-07-28 01:23:09 DP0 TP0] Decode batch. #running-req: 1, #token: 725, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.83, #queue-req: 0, 
[2025-07-28 01:23:10 DP1 TP0] Decode batch. #running-req: 1, #token: 744, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.22, #queue-req: 0, 
[2025-07-28 01:23:10 DP0 TP0] Decode batch. #running-req: 1, #token: 765, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.80, #queue-req: 0, 
[2025-07-28 01:23:10 DP1 TP0] Decode batch. #running-req: 1, #token: 784, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.27, #queue-req: 0, 
[2025-07-28 01:23:10 DP0 TP0] Decode batch. #running-req: 1, #token: 805, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.69, #queue-req: 0, 
[2025-07-28 01:23:10 DP1 TP0] Decode batch. #running-req: 1, #token: 824, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.03, #queue-req: 0, 
[2025-07-28 01:23:10 DP0 TP0] Decode batch. #running-req: 1, #token: 845, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.78, #queue-req: 0, 
[2025-07-28 01:23:10 DP1 TP0] Decode batch. #running-req: 1, #token: 864, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.11, #queue-req: 0, 
[2025-07-28 01:23:10 DP0 TP0] Decode batch. #running-req: 1, #token: 885, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.71, #queue-req: 0, 
[2025-07-28 01:23:10 DP1 TP0] Decode batch. #running-req: 1, #token: 904, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.06, #queue-req: 0, 
[2025-07-28 01:23:10] INFO:     127.0.0.1:55250 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:23:10 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 123, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:23:10 DP0 TP0] Decode batch. #running-req: 1, #token: 224, token usage: 0.00, cuda graph: True, gen throughput (token/s): 148.70, #queue-req: 0, 
[2025-07-28 01:23:10 DP1 TP0] Decode batch. #running-req: 1, #token: 944, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.02, #queue-req: 0, 
[2025-07-28 01:23:11 DP0 TP0] Decode batch. #running-req: 1, #token: 264, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.03, #queue-req: 0, 
[2025-07-28 01:23:11 DP1 TP0] Decode batch. #running-req: 1, #token: 984, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.08, #queue-req: 0, 
[2025-07-28 01:23:11 DP0 TP0] Decode batch. #running-req: 1, #token: 304, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.23, #queue-req: 0, 
[2025-07-28 01:23:11 DP1 TP0] Decode batch. #running-req: 1, #token: 1024, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.11, #queue-req: 0, 
[2025-07-28 01:23:11 DP0 TP0] Decode batch. #running-req: 1, #token: 344, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.09, #queue-req: 0, 
[2025-07-28 01:23:11 DP1 TP0] Decode batch. #running-req: 1, #token: 1064, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.32, #queue-req: 0, 
[2025-07-28 01:23:11 DP0 TP0] Decode batch. #running-req: 1, #token: 384, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.07, #queue-req: 0, 
[2025-07-28 01:23:11 DP1 TP0] Decode batch. #running-req: 1, #token: 1104, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.52, #queue-req: 0, 
[2025-07-28 01:23:11] INFO:     127.0.0.1:55262 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:23:11 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 129, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:23:11 DP0 TP0] Decode batch. #running-req: 1, #token: 424, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.11, #queue-req: 0, 
[2025-07-28 01:23:12 DP1 TP0] Decode batch. #running-req: 1, #token: 235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 148.28, #queue-req: 0, 
[2025-07-28 01:23:12 DP0 TP0] Decode batch. #running-req: 1, #token: 464, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.89, #queue-req: 0, 
[2025-07-28 01:23:12 DP1 TP0] Decode batch. #running-req: 1, #token: 275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.67, #queue-req: 0, 
[2025-07-28 01:23:12 DP0 TP0] Decode batch. #running-req: 1, #token: 504, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.03, #queue-req: 0, 
[2025-07-28 01:23:12 DP1 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.73, #queue-req: 0, 
[2025-07-28 01:23:12 DP0 TP0] Decode batch. #running-req: 1, #token: 544, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.92, #queue-req: 0, 
[2025-07-28 01:23:12 DP1 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.62, #queue-req: 0, 
[2025-07-28 01:23:12 DP0 TP0] Decode batch. #running-req: 1, #token: 584, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.95, #queue-req: 0, 
[2025-07-28 01:23:12 DP1 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.50, #queue-req: 0, 
[2025-07-28 01:23:12 DP0 TP0] Decode batch. #running-req: 1, #token: 624, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.85, #queue-req: 0, 
[2025-07-28 01:23:12 DP1 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.55, #queue-req: 0, 
[2025-07-28 01:23:13 DP0 TP0] Decode batch. #running-req: 1, #token: 664, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.87, #queue-req: 0, 
[2025-07-28 01:23:13 DP1 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.41, #queue-req: 0, 
[2025-07-28 01:23:13 DP0 TP0] Decode batch. #running-req: 1, #token: 704, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.77, #queue-req: 0, 
[2025-07-28 01:23:13 DP1 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.50, #queue-req: 0, 
[2025-07-28 01:23:13 DP0 TP0] Decode batch. #running-req: 1, #token: 744, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.89, #queue-req: 0, 
[2025-07-28 01:23:13 DP1 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.85, #queue-req: 0, 
[2025-07-28 01:23:13 DP0 TP0] Decode batch. #running-req: 1, #token: 784, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.62, #queue-req: 0, 
[2025-07-28 01:23:13 DP1 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.05, #queue-req: 0, 
[2025-07-28 01:23:13 DP0 TP0] Decode batch. #running-req: 1, #token: 824, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.63, #queue-req: 0, 
[2025-07-28 01:23:13 DP1 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.86, #queue-req: 0, 
[2025-07-28 01:23:14 DP0 TP0] Decode batch. #running-req: 1, #token: 864, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.11, #queue-req: 0, 
[2025-07-28 01:23:14 DP1 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.75, #queue-req: 0, 
[2025-07-28 01:23:14 DP0 TP0] Decode batch. #running-req: 1, #token: 904, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.08, #queue-req: 0, 
[2025-07-28 01:23:14 DP1 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.62, #queue-req: 0, 
[2025-07-28 01:23:14 DP0 TP0] Decode batch. #running-req: 1, #token: 944, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.13, #queue-req: 0, 
[2025-07-28 01:23:14 DP1 TP0] Decode batch. #running-req: 1, #token: 755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.39, #queue-req: 0, 
[2025-07-28 01:23:14 DP0 TP0] Decode batch. #running-req: 1, #token: 984, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.42, #queue-req: 0, 
[2025-07-28 01:23:14 DP1 TP0] Decode batch. #running-req: 1, #token: 795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.68, #queue-req: 0, 
[2025-07-28 01:23:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1024, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.59, #queue-req: 0, 
[2025-07-28 01:23:14 DP1 TP0] Decode batch. #running-req: 1, #token: 835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.76, #queue-req: 0, 
[2025-07-28 01:23:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1064, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.82, #queue-req: 0, 
[2025-07-28 01:23:15 DP1 TP0] Decode batch. #running-req: 1, #token: 875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.26, #queue-req: 0, 
[2025-07-28 01:23:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1104, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.49, #queue-req: 0, 
[2025-07-28 01:23:15 DP1 TP0] Decode batch. #running-req: 1, #token: 915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.56, #queue-req: 0, 
[2025-07-28 01:23:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1144, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.85, #queue-req: 0, 
[2025-07-28 01:23:15 DP1 TP0] Decode batch. #running-req: 1, #token: 955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.16, #queue-req: 0, 
[2025-07-28 01:23:15] INFO:     127.0.0.1:55276 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:23:15 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 91, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:23:15 DP0 TP0] Decode batch. #running-req: 1, #token: 229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 147.24, #queue-req: 0, 
[2025-07-28 01:23:15 DP1 TP0] Decode batch. #running-req: 1, #token: 995, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.15, #queue-req: 0, 
[2025-07-28 01:23:15 DP0 TP0] Decode batch. #running-req: 1, #token: 269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.19, #queue-req: 0, 
[2025-07-28 01:23:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.99, #queue-req: 0, 
[2025-07-28 01:23:15 DP0 TP0] Decode batch. #running-req: 1, #token: 309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.10, #queue-req: 0, 
[2025-07-28 01:23:16 DP1 TP0] Decode batch. #running-req: 1, #token: 1075, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.47, #queue-req: 0, 
[2025-07-28 01:23:16 DP0 TP0] Decode batch. #running-req: 1, #token: 349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.95, #queue-req: 0, 
[2025-07-28 01:23:16 DP1 TP0] Decode batch. #running-req: 1, #token: 1115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.47, #queue-req: 0, 
[2025-07-28 01:23:16 DP0 TP0] Decode batch. #running-req: 1, #token: 389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.40, #queue-req: 0, 
[2025-07-28 01:23:16 DP1 TP0] Decode batch. #running-req: 1, #token: 1155, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.46, #queue-req: 0, 
[2025-07-28 01:23:16 DP0 TP0] Decode batch. #running-req: 1, #token: 429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.33, #queue-req: 0, 
[2025-07-28 01:23:16 DP1 TP0] Decode batch. #running-req: 1, #token: 1195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.39, #queue-req: 0, 
[2025-07-28 01:23:16 DP0 TP0] Decode batch. #running-req: 1, #token: 469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.68, #queue-req: 0, 
[2025-07-28 01:23:16 DP1 TP0] Decode batch. #running-req: 1, #token: 1235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.46, #queue-req: 0, 
[2025-07-28 01:23:16 DP0 TP0] Decode batch. #running-req: 1, #token: 509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.40, #queue-req: 0, 
[2025-07-28 01:23:16 DP1 TP0] Decode batch. #running-req: 1, #token: 1275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.39, #queue-req: 0, 
[2025-07-28 01:23:17] INFO:     127.0.0.1:55284 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:23:17 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 92, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:23:17 DP0 TP0] Decode batch. #running-req: 1, #token: 549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.67, #queue-req: 0, 
[2025-07-28 01:23:17 DP1 TP0] Decode batch. #running-req: 1, #token: 248, token usage: 0.00, cuda graph: True, gen throughput (token/s): 143.37, #queue-req: 0, 
[2025-07-28 01:23:17 DP0 TP0] Decode batch. #running-req: 1, #token: 589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.03, #queue-req: 0, 
[2025-07-28 01:23:17 DP1 TP0] Decode batch. #running-req: 1, #token: 288, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.11, #queue-req: 0, 
[2025-07-28 01:23:17 DP0 TP0] Decode batch. #running-req: 1, #token: 629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.72, #queue-req: 0, 
[2025-07-28 01:23:17 DP1 TP0] Decode batch. #running-req: 1, #token: 328, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.88, #queue-req: 0, 
[2025-07-28 01:23:17 DP0 TP0] Decode batch. #running-req: 1, #token: 669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.18, #queue-req: 0, 
[2025-07-28 01:23:17 DP1 TP0] Decode batch. #running-req: 1, #token: 368, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.31, #queue-req: 0, 
[2025-07-28 01:23:17 DP0 TP0] Decode batch. #running-req: 1, #token: 709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.19, #queue-req: 0, 
[2025-07-28 01:23:17 DP1 TP0] Decode batch. #running-req: 1, #token: 408, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.51, #queue-req: 0, 
[2025-07-28 01:23:18 DP0 TP0] Decode batch. #running-req: 1, #token: 749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.61, #queue-req: 0, 
[2025-07-28 01:23:18 DP1 TP0] Decode batch. #running-req: 1, #token: 448, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.59, #queue-req: 0, 
[2025-07-28 01:23:18 DP0 TP0] Decode batch. #running-req: 1, #token: 789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.88, #queue-req: 0, 
[2025-07-28 01:23:18 DP1 TP0] Decode batch. #running-req: 1, #token: 488, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.42, #queue-req: 0, 
[2025-07-28 01:23:18 DP0 TP0] Decode batch. #running-req: 1, #token: 829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.35, #queue-req: 0, 
[2025-07-28 01:23:18 DP1 TP0] Decode batch. #running-req: 1, #token: 528, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.42, #queue-req: 0, 
[2025-07-28 01:23:18 DP0 TP0] Decode batch. #running-req: 1, #token: 869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.42, #queue-req: 0, 
[2025-07-28 01:23:18 DP1 TP0] Decode batch. #running-req: 1, #token: 568, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.43, #queue-req: 0, 
[2025-07-28 01:23:18 DP0 TP0] Decode batch. #running-req: 1, #token: 909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.74, #queue-req: 0, 
[2025-07-28 01:23:18 DP1 TP0] Decode batch. #running-req: 1, #token: 608, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.28, #queue-req: 0, 
[2025-07-28 01:23:19 DP0 TP0] Decode batch. #running-req: 1, #token: 949, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.04, #queue-req: 0, 
[2025-07-28 01:23:19 DP1 TP0] Decode batch. #running-req: 1, #token: 648, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.30, #queue-req: 0, 
[2025-07-28 01:23:19 DP0 TP0] Decode batch. #running-req: 1, #token: 989, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.98, #queue-req: 0, 
[2025-07-28 01:23:19 DP1 TP0] Decode batch. #running-req: 1, #token: 688, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.44, #queue-req: 0, 
[2025-07-28 01:23:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1029, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.48, #queue-req: 0, 
[2025-07-28 01:23:19 DP1 TP0] Decode batch. #running-req: 1, #token: 728, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.34, #queue-req: 0, 
[2025-07-28 01:23:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1069, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.99, #queue-req: 0, 
[2025-07-28 01:23:19 DP1 TP0] Decode batch. #running-req: 1, #token: 768, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.09, #queue-req: 0, 
[2025-07-28 01:23:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1109, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.97, #queue-req: 0, 
[2025-07-28 01:23:19 DP1 TP0] Decode batch. #running-req: 1, #token: 808, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.16, #queue-req: 0, 
[2025-07-28 01:23:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1149, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.17, #queue-req: 0, 
[2025-07-28 01:23:20 DP1 TP0] Decode batch. #running-req: 1, #token: 848, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.08, #queue-req: 0, 
[2025-07-28 01:23:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.91, #queue-req: 0, 
[2025-07-28 01:23:20 DP1 TP0] Decode batch. #running-req: 1, #token: 888, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.19, #queue-req: 0, 
[2025-07-28 01:23:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.88, #queue-req: 0, 
[2025-07-28 01:23:20 DP1 TP0] Decode batch. #running-req: 1, #token: 928, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.03, #queue-req: 0, 
[2025-07-28 01:23:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.83, #queue-req: 0, 
[2025-07-28 01:23:20 DP1 TP0] Decode batch. #running-req: 1, #token: 968, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.02, #queue-req: 0, 
[2025-07-28 01:23:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.78, #queue-req: 0, 
[2025-07-28 01:23:20 DP1 TP0] Decode batch. #running-req: 1, #token: 1008, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.15, #queue-req: 0, 
[2025-07-28 01:23:20] INFO:     127.0.0.1:57374 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:23:20 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 103, #cached-token: 91, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:23:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1048, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.72, #queue-req: 0, 
[2025-07-28 01:23:21 DP0 TP0] Decode batch. #running-req: 1, #token: 210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 148.39, #queue-req: 0, 
[2025-07-28 01:23:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1088, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.33, #queue-req: 0, 
[2025-07-28 01:23:21 DP0 TP0] Decode batch. #running-req: 1, #token: 250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.01, #queue-req: 0, 
[2025-07-28 01:23:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1128, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.20, #queue-req: 0, 
[2025-07-28 01:23:21 DP0 TP0] Decode batch. #running-req: 1, #token: 290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.71, #queue-req: 0, 
[2025-07-28 01:23:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1168, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.37, #queue-req: 0, 
[2025-07-28 01:23:21 DP0 TP0] Decode batch. #running-req: 1, #token: 330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.09, #queue-req: 0, 
[2025-07-28 01:23:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1208, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.14, #queue-req: 0, 
[2025-07-28 01:23:21 DP0 TP0] Decode batch. #running-req: 1, #token: 370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.70, #queue-req: 0, 
[2025-07-28 01:23:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1248, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.25, #queue-req: 0, 
[2025-07-28 01:23:21 DP0 TP0] Decode batch. #running-req: 1, #token: 410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.01, #queue-req: 0, 
[2025-07-28 01:23:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1288, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.10, #queue-req: 0, 
[2025-07-28 01:23:22 DP0 TP0] Decode batch. #running-req: 1, #token: 450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.34, #queue-req: 0, 
[2025-07-28 01:23:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1328, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.00, #queue-req: 0, 
[2025-07-28 01:23:22 DP0 TP0] Decode batch. #running-req: 1, #token: 490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.73, #queue-req: 0, 
[2025-07-28 01:23:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1368, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.97, #queue-req: 0, 
[2025-07-28 01:23:22 DP0 TP0] Decode batch. #running-req: 1, #token: 530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.95, #queue-req: 0, 
[2025-07-28 01:23:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1408, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.04, #queue-req: 0, 
[2025-07-28 01:23:22 DP0 TP0] Decode batch. #running-req: 1, #token: 570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.78, #queue-req: 0, 
[2025-07-28 01:23:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1448, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.07, #queue-req: 0, 
[2025-07-28 01:23:22 DP0 TP0] Decode batch. #running-req: 1, #token: 610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.78, #queue-req: 0, 
[2025-07-28 01:23:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1488, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.00, #queue-req: 0, 
[2025-07-28 01:23:23 DP0 TP0] Decode batch. #running-req: 1, #token: 650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.27, #queue-req: 0, 
[2025-07-28 01:23:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1528, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.77, #queue-req: 0, 
[2025-07-28 01:23:23 DP0 TP0] Decode batch. #running-req: 1, #token: 690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.05, #queue-req: 0, 
[2025-07-28 01:23:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1568, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.07, #queue-req: 0, 
[2025-07-28 01:23:23 DP0 TP0] Decode batch. #running-req: 1, #token: 730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.99, #queue-req: 0, 
[2025-07-28 01:23:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1608, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.79, #queue-req: 0, 
[2025-07-28 01:23:23 DP0 TP0] Decode batch. #running-req: 1, #token: 770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.82, #queue-req: 0, 
[2025-07-28 01:23:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1648, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.75, #queue-req: 0, 
[2025-07-28 01:23:23 DP0 TP0] Decode batch. #running-req: 1, #token: 810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.71, #queue-req: 0, 
[2025-07-28 01:23:24 DP1 TP0] Decode batch. #running-req: 1, #token: 1688, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.88, #queue-req: 0, 
[2025-07-28 01:23:24 DP0 TP0] Decode batch. #running-req: 1, #token: 850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.78, #queue-req: 0, 
[2025-07-28 01:23:24] INFO:     127.0.0.1:57390 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:23:24 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 89, #cached-token: 91, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:23:24 DP0 TP0] Decode batch. #running-req: 1, #token: 890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.70, #queue-req: 0, 
[2025-07-28 01:23:24 DP1 TP0] Decode batch. #running-req: 1, #token: 185, token usage: 0.00, cuda graph: True, gen throughput (token/s): 146.70, #queue-req: 0, 
[2025-07-28 01:23:24 DP0 TP0] Decode batch. #running-req: 1, #token: 930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.82, #queue-req: 0, 
[2025-07-28 01:23:24 DP1 TP0] Decode batch. #running-req: 1, #token: 225, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.67, #queue-req: 0, 
[2025-07-28 01:23:24 DP0 TP0] Decode batch. #running-req: 1, #token: 970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.71, #queue-req: 0, 
[2025-07-28 01:23:24 DP1 TP0] Decode batch. #running-req: 1, #token: 265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.61, #queue-req: 0, 
[2025-07-28 01:23:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.73, #queue-req: 0, 
[2025-07-28 01:23:24 DP1 TP0] Decode batch. #running-req: 1, #token: 305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.57, #queue-req: 0, 
[2025-07-28 01:23:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.30, #queue-req: 0, 
[2025-07-28 01:23:25 DP1 TP0] Decode batch. #running-req: 1, #token: 345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.43, #queue-req: 0, 
[2025-07-28 01:23:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.86, #queue-req: 0, 
[2025-07-28 01:23:25 DP1 TP0] Decode batch. #running-req: 1, #token: 385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.89, #queue-req: 0, 
[2025-07-28 01:23:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.98, #queue-req: 0, 
[2025-07-28 01:23:25 DP1 TP0] Decode batch. #running-req: 1, #token: 425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.94, #queue-req: 0, 
[2025-07-28 01:23:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.89, #queue-req: 0, 
[2025-07-28 01:23:25 DP1 TP0] Decode batch. #running-req: 1, #token: 465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.95, #queue-req: 0, 
[2025-07-28 01:23:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.99, #queue-req: 0, 
[2025-07-28 01:23:25 DP1 TP0] Decode batch. #running-req: 1, #token: 505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.95, #queue-req: 0, 
[2025-07-28 01:23:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.06, #queue-req: 0, 
[2025-07-28 01:23:26 DP1 TP0] Decode batch. #running-req: 1, #token: 545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.99, #queue-req: 0, 
[2025-07-28 01:23:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.93, #queue-req: 0, 
[2025-07-28 01:23:26 DP1 TP0] Decode batch. #running-req: 1, #token: 585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.90, #queue-req: 0, 
[2025-07-28 01:23:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.95, #queue-req: 0, 
[2025-07-28 01:23:26 DP1 TP0] Decode batch. #running-req: 1, #token: 625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.98, #queue-req: 0, 
[2025-07-28 01:23:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.90, #queue-req: 0, 
[2025-07-28 01:23:26 DP1 TP0] Decode batch. #running-req: 1, #token: 665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.74, #queue-req: 0, 
[2025-07-28 01:23:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.69, #queue-req: 0, 
[2025-07-28 01:23:26 DP1 TP0] Decode batch. #running-req: 1, #token: 705, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.87, #queue-req: 0, 
[2025-07-28 01:23:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.72, #queue-req: 0, 
[2025-07-28 01:23:27 DP1 TP0] Decode batch. #running-req: 1, #token: 745, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.72, #queue-req: 0, 
[2025-07-28 01:23:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.75, #queue-req: 0, 
[2025-07-28 01:23:27 DP1 TP0] Decode batch. #running-req: 1, #token: 785, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.54, #queue-req: 0, 
[2025-07-28 01:23:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.72, #queue-req: 0, 
[2025-07-28 01:23:27 DP1 TP0] Decode batch. #running-req: 1, #token: 825, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.16, #queue-req: 0, 
[2025-07-28 01:23:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.48, #queue-req: 0, 
[2025-07-28 01:23:27 DP1 TP0] Decode batch. #running-req: 1, #token: 865, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.11, #queue-req: 0, 
[2025-07-28 01:23:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.44, #queue-req: 0, 
[2025-07-28 01:23:27 DP1 TP0] Decode batch. #running-req: 1, #token: 905, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.66, #queue-req: 0, 
[2025-07-28 01:23:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.63, #queue-req: 0, 
[2025-07-28 01:23:27 DP1 TP0] Decode batch. #running-req: 1, #token: 945, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.75, #queue-req: 0, 
[2025-07-28 01:23:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.60, #queue-req: 0, 
[2025-07-28 01:23:28 DP1 TP0] Decode batch. #running-req: 1, #token: 985, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.45, #queue-req: 0, 
[2025-07-28 01:23:28] INFO:     127.0.0.1:57400 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:23:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1025, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.96, #queue-req: 0, 
[2025-07-28 01:23:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1065, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.93, #queue-req: 0, 
[2025-07-28 01:23:28] INFO:     127.0.0.1:47364 - "POST /v1/chat/completions HTTP/1.1" 200 OK

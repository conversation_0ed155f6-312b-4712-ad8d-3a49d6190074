import json

from google import genai
from google.genai import types
    
client = genai.Client(api_key='AIzaSyDKXQVpntZl8a-DTbW4YD80b8nfj2I7_jE')

uploaded_file = client.files.upload(
        file=f"/data/home/<USER>/repos/anesbench/share/datasets/anesthesia/benchmark/fine/rebuttal/openqa/system2_en_2trans.jsonl",
        config=types.UploadFileConfig(display_name=f'system2_en_2trans', mime_type='jsonl')
    )
print(uploaded_file)

file_batch_job = client.batches.create(
    model="gemini-2.5-flash",
    src=uploaded_file.name,
    config={
        'display_name': f"system2_en_2trans",
    },
    )
print(file_batch_job)
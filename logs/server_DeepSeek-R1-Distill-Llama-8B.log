[2025-07-28 01:23:36] server_args=ServerArgs(model_path='/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Llama-8B', tokenizer_path='/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Llama-8B', tokenizer_mode='auto', skip_tokenizer_init=False, load_format='auto', model_loader_extra_config='{}', trust_remote_code=False, context_length=32768, is_embedding=False, enable_multimodal=None, revision=None, model_impl='auto', host='127.0.0.1', port=8006, skip_server_warmup=False, warmups=None, nccl_port=None, dtype='auto', quantization=None, quantization_param_path=None, kv_cache_dtype='auto', mem_fraction_static=0.85, max_running_requests=None, max_total_tokens=None, chunked_prefill_size=8192, max_prefill_tokens=16384, schedule_policy='fcfs', schedule_conservativeness=1.0, cpu_offload_gb=0, page_size=1, hybrid_kvcache_ratio=None, swa_full_tokens_ratio=0.8, disable_hybrid_swa_memory=False, device='cuda', tp_size=4, pp_size=1, max_micro_batch_size=None, stream_interval=1, stream_output=False, random_seed=1045846734, constrained_json_whitespace_pattern=None, watchdog_timeout=300, dist_timeout=None, download_dir=None, base_gpu_id=0, gpu_id_step=1, sleep_on_idle=False, log_level='info', log_level_http=None, log_requests=False, log_requests_level=0, crash_dump_folder=None, show_time_cost=True, enable_metrics=True, enable_metrics_for_all_schedulers=False, bucket_time_to_first_token=None, bucket_inter_token_latency=None, bucket_e2e_request_latency=None, collect_tokens_histogram=False, decode_log_interval=40, enable_request_time_stats_logging=False, kv_events_config=None, api_key=None, served_model_name='/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Llama-8B', chat_template=None, completion_template=None, file_storage_path='sglang_storage', enable_cache_report=False, reasoning_parser=None, tool_call_parser=None, dp_size=2, load_balance_method='round_robin', dist_init_addr=None, nnodes=1, node_rank=0, json_model_override_args='{}', preferred_sampling_params=None, enable_lora=None, max_lora_rank=None, lora_target_modules=None, lora_paths=None, max_loras_per_batch=8, lora_backend='triton', attention_backend=None, sampling_backend='flashinfer', grammar_backend='xgrammar', mm_attention_backend=None, speculative_algorithm=None, speculative_draft_model_path=None, speculative_num_steps=None, speculative_eagle_topk=None, speculative_num_draft_tokens=None, speculative_accept_threshold_single=1.0, speculative_accept_threshold_acc=1.0, speculative_token_map=None, ep_size=1, enable_ep_moe=False, enable_deepep_moe=False, enable_flashinfer_moe=False, enable_flashinfer_allreduce_fusion=False, deepep_mode='auto', ep_num_redundant_experts=0, ep_dispatch_algorithm='static', init_expert_location='trivial', enable_eplb=False, eplb_algorithm='auto', eplb_rebalance_num_iterations=1000, eplb_rebalance_layers_per_chunk=None, expert_distribution_recorder_mode=None, expert_distribution_recorder_buffer_size=1000, enable_expert_distribution_metrics=False, deepep_config=None, moe_dense_tp_size=None, enable_hierarchical_cache=False, hicache_ratio=2.0, hicache_size=0, hicache_write_policy='write_through_selective', hicache_io_backend='', hicache_storage_backend=None, enable_double_sparsity=False, ds_channel_config_path=None, ds_heavy_channel_num=32, ds_heavy_token_num=256, ds_heavy_channel_type='qk', ds_sparse_decode_threshold=4096, disable_radix_cache=False, cuda_graph_max_bs=None, cuda_graph_bs=None, disable_cuda_graph=False, disable_cuda_graph_padding=False, enable_profile_cuda_graph=False, enable_nccl_nvls=False, enable_tokenizer_batch_encode=False, disable_outlines_disk_cache=False, disable_custom_all_reduce=False, enable_mscclpp=False, disable_overlap_schedule=False, enable_mixed_chunk=False, enable_dp_attention=False, enable_dp_lm_head=False, enable_two_batch_overlap=False, enable_torch_compile=False, torch_compile_max_bs=32, torchao_config='', enable_nan_detection=False, enable_p2p_check=False, triton_attention_reduce_in_fp32=False, triton_attention_num_kv_splits=8, num_continuous_decode_steps=1, delete_ckpt_after_loading=False, enable_memory_saver=False, allow_auto_truncate=False, enable_custom_logit_processor=False, flashinfer_mla_disable_ragged=False, disable_shared_experts_fusion=False, disable_chunked_prefix_cache=False, disable_fast_image_processor=False, enable_return_hidden_states=False, enable_triton_kernel_moe=False, debug_tensor_dump_output_folder=None, debug_tensor_dump_input_file=None, debug_tensor_dump_inject=False, debug_tensor_dump_prefill_only=False, disaggregation_mode='null', disaggregation_transfer_backend='mooncake', disaggregation_bootstrap_port=8998, disaggregation_decode_tp=None, disaggregation_decode_dp=None, disaggregation_prefill_pp=1, disaggregation_ib_device=None, num_reserved_decode_tokens=512, pdlb_url=None, custom_weight_loader=[], weight_loader_disable_mmap=False, enable_pdmux=False, sm_group_num=3)
[2025-07-28 01:23:42] Launch DP0 starting at GPU #0.
[2025-07-28 01:23:42] Launch DP1 starting at GPU #4.
[2025-07-28 01:23:50 DP0 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 01:23:50 DP0 TP0] Init torch distributed begin.
[2025-07-28 01:23:50 DP1 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 01:23:50 DP1 TP0] Init torch distributed begin.
[2025-07-28 01:23:52 DP1 TP0] sglang is using nccl==2.26.2
[2025-07-28 01:23:52 DP0 TP0] sglang is using nccl==2.26.2
[2025-07-28 01:23:53 DP1 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 01:23:53 DP0 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 01:23:53 DP1 TP0] Load weight begin. avail mem=78.21 GB
[2025-07-28 01:23:53 DP0 TP0] Load weight begin. avail mem=78.18 GB

Loading safetensors checkpoint shards:   0% Completed | 0/2 [00:00<?, ?it/s]

Loading safetensors checkpoint shards:   0% Completed | 0/2 [00:00<?, ?it/s]

Loading safetensors checkpoint shards:  50% Completed | 1/2 [00:02<00:02,  2.89s/it]

Loading safetensors checkpoint shards:  50% Completed | 1/2 [00:02<00:02,  2.92s/it]

Loading safetensors checkpoint shards: 100% Completed | 2/2 [00:06<00:00,  3.24s/it]

Loading safetensors checkpoint shards: 100% Completed | 2/2 [00:06<00:00,  3.25s/it]

Loading safetensors checkpoint shards: 100% Completed | 2/2 [00:06<00:00,  3.19s/it]


Loading safetensors checkpoint shards: 100% Completed | 2/2 [00:06<00:00,  3.20s/it]

[2025-07-28 01:24:00 DP0 TP0] Load weight end. type=LlamaForCausalLM, dtype=torch.bfloat16, avail mem=74.39 GB, mem usage=3.80 GB.
[2025-07-28 01:24:00 DP1 TP0] Load weight end. type=LlamaForCausalLM, dtype=torch.bfloat16, avail mem=74.39 GB, mem usage=3.82 GB.
[2025-07-28 01:24:01 DP0 TP3] KV Cache is allocated. #tokens: 2050580, K size: 31.29 GB, V size: 31.29 GB
[2025-07-28 01:24:01 DP0 TP1] KV Cache is allocated. #tokens: 2050580, K size: 31.29 GB, V size: 31.29 GB
[2025-07-28 01:24:01 DP0 TP2] KV Cache is allocated. #tokens: 2050580, K size: 31.29 GB, V size: 31.29 GB
[2025-07-28 01:24:01 DP1 TP2] KV Cache is allocated. #tokens: 2050580, K size: 31.29 GB, V size: 31.29 GB
[2025-07-28 01:24:01 DP1 TP0] KV Cache is allocated. #tokens: 2050580, K size: 31.29 GB, V size: 31.29 GB
[2025-07-28 01:24:01 DP0 TP0] KV Cache is allocated. #tokens: 2050580, K size: 31.29 GB, V size: 31.29 GB
[2025-07-28 01:24:01 DP0 TP0] Memory pool end. avail mem=11.25 GB
[2025-07-28 01:24:01 DP1 TP0] Memory pool end. avail mem=11.25 GB
[2025-07-28 01:24:01 DP1 TP3] KV Cache is allocated. #tokens: 2050580, K size: 31.29 GB, V size: 31.29 GB
[2025-07-28 01:24:01 DP1 TP1] KV Cache is allocated. #tokens: 2050580, K size: 31.29 GB, V size: 31.29 GB
[2025-07-28 01:24:01 DP1 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.75 GB
[2025-07-28 01:24:01 DP0 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.75 GB
[2025-07-28 01:24:01 DP0 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.72 GB):   0%|          | 0/23 [00:00<?, ?it/s][2025-07-28 01:24:01 DP1 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.72 GB):   0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.72 GB):   4%|▍         | 1/23 [00:01<00:24,  1.11s/it]
Capturing batches (bs=152 avail_mem=10.51 GB):   4%|▍         | 1/23 [00:01<00:24,  1.11s/it]
Capturing batches (bs=160 avail_mem=10.72 GB):   4%|▍         | 1/23 [00:01<00:23,  1.08s/it]
Capturing batches (bs=152 avail_mem=10.51 GB):   4%|▍         | 1/23 [00:01<00:23,  1.08s/it]
Capturing batches (bs=152 avail_mem=10.51 GB):   9%|▊         | 2/23 [00:01<00:13,  1.56it/s]
Capturing batches (bs=144 avail_mem=10.41 GB):   9%|▊         | 2/23 [00:01<00:13,  1.56it/s]
Capturing batches (bs=152 avail_mem=10.51 GB):   9%|▊         | 2/23 [00:01<00:14,  1.49it/s]
Capturing batches (bs=144 avail_mem=10.41 GB):   9%|▊         | 2/23 [00:01<00:14,  1.49it/s]
Capturing batches (bs=144 avail_mem=10.41 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.75it/s]
Capturing batches (bs=136 avail_mem=10.33 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.75it/s]
Capturing batches (bs=144 avail_mem=10.41 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.72it/s]
Capturing batches (bs=136 avail_mem=10.33 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.72it/s]
Capturing batches (bs=136 avail_mem=10.33 GB):  17%|█▋        | 4/23 [00:02<00:08,  2.19it/s]
Capturing batches (bs=128 avail_mem=10.24 GB):  17%|█▋        | 4/23 [00:02<00:08,  2.19it/s]
Capturing batches (bs=136 avail_mem=10.33 GB):  17%|█▋        | 4/23 [00:02<00:08,  2.15it/s]
Capturing batches (bs=128 avail_mem=10.24 GB):  17%|█▋        | 4/23 [00:02<00:08,  2.15it/s]
Capturing batches (bs=128 avail_mem=10.24 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.51it/s]
Capturing batches (bs=120 avail_mem=10.17 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.51it/s]
Capturing batches (bs=128 avail_mem=10.24 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.49it/s]
Capturing batches (bs=120 avail_mem=10.17 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.49it/s]
Capturing batches (bs=120 avail_mem=10.17 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.78it/s]
Capturing batches (bs=112 avail_mem=10.09 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.78it/s]
Capturing batches (bs=120 avail_mem=10.17 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.77it/s]
Capturing batches (bs=112 avail_mem=10.09 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.77it/s]
Capturing batches (bs=112 avail_mem=10.09 GB):  30%|███       | 7/23 [00:03<00:05,  2.99it/s]
Capturing batches (bs=104 avail_mem=10.03 GB):  30%|███       | 7/23 [00:03<00:05,  2.99it/s]
Capturing batches (bs=112 avail_mem=10.09 GB):  30%|███       | 7/23 [00:03<00:05,  2.98it/s]
Capturing batches (bs=104 avail_mem=10.03 GB):  30%|███       | 7/23 [00:03<00:05,  2.98it/s]
Capturing batches (bs=104 avail_mem=10.03 GB):  35%|███▍      | 8/23 [00:03<00:04,  3.14it/s]
Capturing batches (bs=96 avail_mem=9.95 GB):  35%|███▍      | 8/23 [00:03<00:04,  3.14it/s]  
Capturing batches (bs=104 avail_mem=10.03 GB):  35%|███▍      | 8/23 [00:03<00:04,  3.12it/s]
Capturing batches (bs=96 avail_mem=9.95 GB):  35%|███▍      | 8/23 [00:03<00:04,  3.12it/s]  
Capturing batches (bs=96 avail_mem=9.95 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.24it/s]
Capturing batches (bs=88 avail_mem=9.90 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.24it/s]
Capturing batches (bs=96 avail_mem=9.95 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.16it/s]
Capturing batches (bs=88 avail_mem=9.90 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.16it/s]
Capturing batches (bs=88 avail_mem=9.90 GB):  43%|████▎     | 10/23 [00:03<00:03,  3.31it/s]
Capturing batches (bs=80 avail_mem=9.82 GB):  43%|████▎     | 10/23 [00:03<00:03,  3.31it/s]
Capturing batches (bs=88 avail_mem=9.90 GB):  43%|████▎     | 10/23 [00:03<00:04,  3.23it/s]
Capturing batches (bs=80 avail_mem=9.82 GB):  43%|████▎     | 10/23 [00:03<00:04,  3.23it/s]
Capturing batches (bs=80 avail_mem=9.82 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.37it/s]
Capturing batches (bs=72 avail_mem=9.81 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.37it/s]
Capturing batches (bs=80 avail_mem=9.82 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.23it/s]
Capturing batches (bs=72 avail_mem=9.81 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.23it/s]
Capturing batches (bs=72 avail_mem=9.81 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.40it/s]
Capturing batches (bs=64 avail_mem=9.75 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.40it/s]
Capturing batches (bs=72 avail_mem=9.81 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.39it/s]
Capturing batches (bs=64 avail_mem=9.75 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.39it/s]
Capturing batches (bs=64 avail_mem=9.75 GB):  57%|█████▋    | 13/23 [00:04<00:02,  3.43it/s]
Capturing batches (bs=56 avail_mem=9.72 GB):  57%|█████▋    | 13/23 [00:04<00:02,  3.43it/s]
Capturing batches (bs=64 avail_mem=9.75 GB):  57%|█████▋    | 13/23 [00:04<00:02,  3.41it/s]
Capturing batches (bs=56 avail_mem=9.72 GB):  57%|█████▋    | 13/23 [00:04<00:02,  3.41it/s]
Capturing batches (bs=56 avail_mem=9.72 GB):  61%|██████    | 14/23 [00:05<00:02,  3.42it/s]
Capturing batches (bs=48 avail_mem=9.67 GB):  61%|██████    | 14/23 [00:05<00:02,  3.42it/s]
Capturing batches (bs=56 avail_mem=9.72 GB):  61%|██████    | 14/23 [00:05<00:02,  3.45it/s]
Capturing batches (bs=48 avail_mem=9.67 GB):  61%|██████    | 14/23 [00:05<00:02,  3.45it/s]
Capturing batches (bs=48 avail_mem=9.67 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.45it/s]
Capturing batches (bs=40 avail_mem=9.66 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.45it/s]
Capturing batches (bs=48 avail_mem=9.67 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.48it/s]
Capturing batches (bs=40 avail_mem=9.66 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.48it/s]
Capturing batches (bs=40 avail_mem=9.66 GB):  70%|██████▉   | 16/23 [00:05<00:02,  3.47it/s]
Capturing batches (bs=32 avail_mem=9.62 GB):  70%|██████▉   | 16/23 [00:05<00:02,  3.47it/s]
Capturing batches (bs=40 avail_mem=9.66 GB):  70%|██████▉   | 16/23 [00:05<00:02,  3.50it/s]
Capturing batches (bs=32 avail_mem=9.62 GB):  70%|██████▉   | 16/23 [00:05<00:02,  3.50it/s]
Capturing batches (bs=32 avail_mem=9.62 GB):  74%|███████▍  | 17/23 [00:05<00:01,  3.44it/s]
Capturing batches (bs=24 avail_mem=9.60 GB):  74%|███████▍  | 17/23 [00:05<00:01,  3.44it/s]
Capturing batches (bs=32 avail_mem=9.62 GB):  74%|███████▍  | 17/23 [00:05<00:01,  3.45it/s]
Capturing batches (bs=24 avail_mem=9.60 GB):  74%|███████▍  | 17/23 [00:05<00:01,  3.45it/s]
Capturing batches (bs=24 avail_mem=9.60 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.46it/s]
Capturing batches (bs=16 avail_mem=9.57 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.46it/s]
Capturing batches (bs=24 avail_mem=9.60 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.48it/s]
Capturing batches (bs=16 avail_mem=9.57 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.48it/s]
Capturing batches (bs=16 avail_mem=9.57 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.48it/s]
Capturing batches (bs=8 avail_mem=9.57 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.48it/s] 
Capturing batches (bs=16 avail_mem=9.57 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.50it/s]
Capturing batches (bs=8 avail_mem=9.57 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.50it/s] 
Capturing batches (bs=8 avail_mem=9.57 GB):  87%|████████▋ | 20/23 [00:06<00:00,  3.50it/s]
Capturing batches (bs=4 avail_mem=9.54 GB):  87%|████████▋ | 20/23 [00:06<00:00,  3.50it/s]
Capturing batches (bs=8 avail_mem=9.57 GB):  87%|████████▋ | 20/23 [00:06<00:00,  3.52it/s]
Capturing batches (bs=4 avail_mem=9.54 GB):  87%|████████▋ | 20/23 [00:06<00:00,  3.52it/s]
Capturing batches (bs=4 avail_mem=9.54 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.52it/s]
Capturing batches (bs=2 avail_mem=9.53 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.52it/s]
Capturing batches (bs=4 avail_mem=9.54 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.52it/s]
Capturing batches (bs=2 avail_mem=9.53 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.52it/s]
Capturing batches (bs=2 avail_mem=9.53 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.52it/s]
Capturing batches (bs=1 avail_mem=9.51 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.52it/s]
Capturing batches (bs=2 avail_mem=9.53 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.54it/s]
Capturing batches (bs=1 avail_mem=9.51 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.54it/s][2025-07-28 01:24:09 DP0 TP3] Registering 1495 cuda graph addresses

Capturing batches (bs=1 avail_mem=9.51 GB): 100%|██████████| 23/23 [00:07<00:00,  3.49it/s]
Capturing batches (bs=1 avail_mem=9.51 GB): 100%|██████████| 23/23 [00:07<00:00,  3.00it/s]
[2025-07-28 01:24:09 DP0 TP0] Registering 1495 cuda graph addresses
[2025-07-28 01:24:09 DP0 TP1] Registering 1495 cuda graph addresses
[2025-07-28 01:24:09 DP0 TP2] Registering 1495 cuda graph addresses
[2025-07-28 01:24:09 DP1 TP3] Registering 1495 cuda graph addresses
[2025-07-28 01:24:09 DP1 TP2] Registering 1495 cuda graph addresses
[2025-07-28 01:24:09 DP1 TP1] Registering 1495 cuda graph addresses
[2025-07-28 01:24:09 DP0 TP0] Capture cuda graph end. Time elapsed: 7.87 s. mem usage=1.25 GB. avail mem=9.50 GB.

Capturing batches (bs=1 avail_mem=9.51 GB): 100%|██████████| 23/23 [00:07<00:00,  3.30it/s]
Capturing batches (bs=1 avail_mem=9.51 GB): 100%|██████████| 23/23 [00:07<00:00,  2.99it/s]
[2025-07-28 01:24:09 DP1 TP0] Registering 1495 cuda graph addresses
[2025-07-28 01:24:09 DP1 TP0] Capture cuda graph end. Time elapsed: 7.93 s. mem usage=1.25 GB. avail mem=9.50 GB.
[2025-07-28 01:24:09 DP0 TP0] max_total_num_tokens=2050580, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=9.50 GB
[2025-07-28 01:24:09 DP1 TP0] max_total_num_tokens=2050580, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=9.50 GB
[2025-07-28 01:24:10] INFO:     Started server process [1845463]
[2025-07-28 01:24:10] INFO:     Waiting for application startup.
[2025-07-28 01:24:10] INFO:     Application startup complete.
[2025-07-28 01:24:10] INFO:     Uvicorn running on http://127.0.0.1:8006 (Press CTRL+C to quit)
[2025-07-28 01:24:11] INFO:     127.0.0.1:39078 - "GET /get_model_info HTTP/1.1" 200 OK
[2025-07-28 01:24:11 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 7, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:24:11 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 7, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:24:11] INFO:     127.0.0.1:39104 - "GET /health HTTP/1.1" 200 OK
[2025-07-28 01:24:12 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 188, #cached-token: 1, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:24:12 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 226, #cached-token: 1, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:24:12 DP0 TP0] Decode batch. #running-req: 1, #token: 267, token usage: 0.00, cuda graph: True, gen throughput (token/s): 14.69, #queue-req: 0, 
[2025-07-28 01:24:12] INFO:     127.0.0.1:39088 - "POST /generate HTTP/1.1" 200 OK
[2025-07-28 01:24:12] The server is fired up and ready to roll!
[2025-07-28 01:24:12 DP1 TP0] Decode batch. #running-req: 1, #token: 229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 14.10, #queue-req: 0, 
[2025-07-28 01:24:12 DP0 TP0] Decode batch. #running-req: 1, #token: 307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.77, #queue-req: 0, 
[2025-07-28 01:24:13 DP1 TP0] Decode batch. #running-req: 1, #token: 269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.58, #queue-req: 0, 
[2025-07-28 01:24:13 DP0 TP0] Decode batch. #running-req: 1, #token: 347, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.89, #queue-req: 0, 
[2025-07-28 01:24:13 DP1 TP0] Decode batch. #running-req: 1, #token: 309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.14, #queue-req: 0, 
[2025-07-28 01:24:13 DP0 TP0] Decode batch. #running-req: 1, #token: 387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.22, #queue-req: 0, 
[2025-07-28 01:24:13 DP1 TP0] Decode batch. #running-req: 1, #token: 349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.56, #queue-req: 0, 
[2025-07-28 01:24:13 DP0 TP0] Decode batch. #running-req: 1, #token: 427, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.49, #queue-req: 0, 
[2025-07-28 01:24:13 DP1 TP0] Decode batch. #running-req: 1, #token: 389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.69, #queue-req: 0, 
[2025-07-28 01:24:13 DP0 TP0] Decode batch. #running-req: 1, #token: 467, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.86, #queue-req: 0, 
[2025-07-28 01:24:13 DP1 TP0] Decode batch. #running-req: 1, #token: 429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.41, #queue-req: 0, 
[2025-07-28 01:24:13 DP0 TP0] Decode batch. #running-req: 1, #token: 507, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.97, #queue-req: 0, 
[2025-07-28 01:24:14 DP1 TP0] Decode batch. #running-req: 1, #token: 469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.48, #queue-req: 0, 
[2025-07-28 01:24:14 DP0 TP0] Decode batch. #running-req: 1, #token: 547, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.77, #queue-req: 0, 
[2025-07-28 01:24:14 DP1 TP0] Decode batch. #running-req: 1, #token: 509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.80, #queue-req: 0, 
[2025-07-28 01:24:14 DP0 TP0] Decode batch. #running-req: 1, #token: 587, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.85, #queue-req: 0, 
[2025-07-28 01:24:14 DP1 TP0] Decode batch. #running-req: 1, #token: 549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.01, #queue-req: 0, 
[2025-07-28 01:24:14 DP0 TP0] Decode batch. #running-req: 1, #token: 627, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.64, #queue-req: 0, 
[2025-07-28 01:24:14 DP1 TP0] Decode batch. #running-req: 1, #token: 589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.49, #queue-req: 0, 
[2025-07-28 01:24:14 DP0 TP0] Decode batch. #running-req: 1, #token: 667, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.89, #queue-req: 0, 
[2025-07-28 01:24:14 DP1 TP0] Decode batch. #running-req: 1, #token: 629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.09, #queue-req: 0, 
[2025-07-28 01:24:14 DP0 TP0] Decode batch. #running-req: 1, #token: 707, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.24, #queue-req: 0, 
[2025-07-28 01:24:15 DP1 TP0] Decode batch. #running-req: 1, #token: 669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.01, #queue-req: 0, 
[2025-07-28 01:24:15 DP0 TP0] Decode batch. #running-req: 1, #token: 747, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.72, #queue-req: 0, 
[2025-07-28 01:24:15 DP1 TP0] Decode batch. #running-req: 1, #token: 709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.98, #queue-req: 0, 
[2025-07-28 01:24:15 DP0 TP0] Decode batch. #running-req: 1, #token: 787, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.43, #queue-req: 0, 
[2025-07-28 01:24:15 DP1 TP0] Decode batch. #running-req: 1, #token: 749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.03, #queue-req: 0, 
[2025-07-28 01:24:15 DP0 TP0] Decode batch. #running-req: 1, #token: 827, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.50, #queue-req: 0, 
[2025-07-28 01:24:15] INFO:     127.0.0.1:39126 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:24:15 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 78, #cached-token: 84, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:24:15 DP0 TP0] Decode batch. #running-req: 2, #token: 961, token usage: 0.00, cuda graph: True, gen throughput (token/s): 231.50, #queue-req: 0, 
[2025-07-28 01:24:15 DP0 TP0] Decode batch. #running-req: 2, #token: 1041, token usage: 0.00, cuda graph: True, gen throughput (token/s): 407.71, #queue-req: 0, 
[2025-07-28 01:24:16 DP0 TP0] Decode batch. #running-req: 2, #token: 1121, token usage: 0.00, cuda graph: True, gen throughput (token/s): 407.03, #queue-req: 0, 
[2025-07-28 01:24:16 DP0 TP0] Decode batch. #running-req: 2, #token: 1201, token usage: 0.00, cuda graph: True, gen throughput (token/s): 405.49, #queue-req: 0, 
[2025-07-28 01:24:16 DP0 TP0] Decode batch. #running-req: 2, #token: 1281, token usage: 0.00, cuda graph: True, gen throughput (token/s): 405.07, #queue-req: 0, 
[2025-07-28 01:24:16 DP0 TP0] Decode batch. #running-req: 2, #token: 1361, token usage: 0.00, cuda graph: True, gen throughput (token/s): 406.41, #queue-req: 0, 
[2025-07-28 01:24:16 DP0 TP0] Decode batch. #running-req: 2, #token: 1441, token usage: 0.00, cuda graph: True, gen throughput (token/s): 406.46, #queue-req: 0, 
[2025-07-28 01:24:16] INFO:     127.0.0.1:39116 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:24:16 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 191, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:24:17 DP0 TP0] Decode batch. #running-req: 1, #token: 458, token usage: 0.00, cuda graph: True, gen throughput (token/s): 351.81, #queue-req: 0, 
[2025-07-28 01:24:17 DP1 TP0] Decode batch. #running-req: 1, #token: 292, token usage: 0.00, cuda graph: True, gen throughput (token/s): 23.18, #queue-req: 0, 
[2025-07-28 01:24:17 DP0 TP0] Decode batch. #running-req: 1, #token: 498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.48, #queue-req: 0, 
[2025-07-28 01:24:17 DP1 TP0] Decode batch. #running-req: 1, #token: 332, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.55, #queue-req: 0, 
[2025-07-28 01:24:17 DP0 TP0] Decode batch. #running-req: 1, #token: 538, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.42, #queue-req: 0, 
[2025-07-28 01:24:17 DP1 TP0] Decode batch. #running-req: 1, #token: 372, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.73, #queue-req: 0, 
[2025-07-28 01:24:17 DP0 TP0] Decode batch. #running-req: 1, #token: 578, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.75, #queue-req: 0, 
[2025-07-28 01:24:17 DP1 TP0] Decode batch. #running-req: 1, #token: 412, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.41, #queue-req: 0, 
[2025-07-28 01:24:17 DP0 TP0] Decode batch. #running-req: 1, #token: 618, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.89, #queue-req: 0, 
[2025-07-28 01:24:17 DP1 TP0] Decode batch. #running-req: 1, #token: 452, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.00, #queue-req: 0, 
[2025-07-28 01:24:17 DP0 TP0] Decode batch. #running-req: 1, #token: 658, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.25, #queue-req: 0, 
[2025-07-28 01:24:18 DP1 TP0] Decode batch. #running-req: 1, #token: 492, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.43, #queue-req: 0, 
[2025-07-28 01:24:18] INFO:     127.0.0.1:60700 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:24:18 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 122, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:24:18 DP0 TP0] Decode batch. #running-req: 1, #token: 225, token usage: 0.00, cuda graph: True, gen throughput (token/s): 169.14, #queue-req: 0, 
[2025-07-28 01:24:18 DP1 TP0] Decode batch. #running-req: 1, #token: 532, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.02, #queue-req: 0, 
[2025-07-28 01:24:18 DP0 TP0] Decode batch. #running-req: 1, #token: 265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.91, #queue-req: 0, 
[2025-07-28 01:24:18 DP1 TP0] Decode batch. #running-req: 1, #token: 572, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.90, #queue-req: 0, 
[2025-07-28 01:24:18 DP0 TP0] Decode batch. #running-req: 1, #token: 305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.50, #queue-req: 0, 
[2025-07-28 01:24:18 DP1 TP0] Decode batch. #running-req: 1, #token: 612, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.53, #queue-req: 0, 
[2025-07-28 01:24:18 DP0 TP0] Decode batch. #running-req: 1, #token: 345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.27, #queue-req: 0, 
[2025-07-28 01:24:18 DP1 TP0] Decode batch. #running-req: 1, #token: 652, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.40, #queue-req: 0, 
[2025-07-28 01:24:18 DP0 TP0] Decode batch. #running-req: 1, #token: 385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.05, #queue-req: 0, 
[2025-07-28 01:24:19 DP1 TP0] Decode batch. #running-req: 1, #token: 692, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.61, #queue-req: 0, 
[2025-07-28 01:24:19 DP0 TP0] Decode batch. #running-req: 1, #token: 425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.24, #queue-req: 0, 
[2025-07-28 01:24:19 DP1 TP0] Decode batch. #running-req: 1, #token: 732, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.52, #queue-req: 0, 
[2025-07-28 01:24:19 DP0 TP0] Decode batch. #running-req: 1, #token: 465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.71, #queue-req: 0, 
[2025-07-28 01:24:19 DP1 TP0] Decode batch. #running-req: 1, #token: 772, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.04, #queue-req: 0, 
[2025-07-28 01:24:19 DP0 TP0] Decode batch. #running-req: 1, #token: 505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.21, #queue-req: 0, 
[2025-07-28 01:24:19 DP1 TP0] Decode batch. #running-req: 1, #token: 812, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.69, #queue-req: 0, 
[2025-07-28 01:24:19 DP0 TP0] Decode batch. #running-req: 1, #token: 545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.93, #queue-req: 0, 
[2025-07-28 01:24:19 DP1 TP0] Decode batch. #running-req: 1, #token: 852, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.14, #queue-req: 0, 
[2025-07-28 01:24:19 DP0 TP0] Decode batch. #running-req: 1, #token: 585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.40, #queue-req: 0, 
[2025-07-28 01:24:19 DP1 TP0] Decode batch. #running-req: 1, #token: 892, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.37, #queue-req: 0, 
[2025-07-28 01:24:20 DP0 TP0] Decode batch. #running-req: 1, #token: 625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.87, #queue-req: 0, 
[2025-07-28 01:24:20 DP1 TP0] Decode batch. #running-req: 1, #token: 932, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.49, #queue-req: 0, 
[2025-07-28 01:24:20 DP0 TP0] Decode batch. #running-req: 1, #token: 665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.97, #queue-req: 0, 
[2025-07-28 01:24:20 DP1 TP0] Decode batch. #running-req: 1, #token: 972, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.68, #queue-req: 0, 
[2025-07-28 01:24:20 DP0 TP0] Decode batch. #running-req: 1, #token: 705, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.36, #queue-req: 0, 
[2025-07-28 01:24:20 DP1 TP0] Decode batch. #running-req: 1, #token: 1012, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.40, #queue-req: 0, 
[2025-07-28 01:24:20 DP0 TP0] Decode batch. #running-req: 1, #token: 745, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.59, #queue-req: 0, 
[2025-07-28 01:24:20 DP1 TP0] Decode batch. #running-req: 1, #token: 1052, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.20, #queue-req: 0, 
[2025-07-28 01:24:20] INFO:     127.0.0.1:60718 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:24:20 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 126, #cached-token: 84, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:24:20 DP1 TP0] Decode batch. #running-req: 2, #token: 1234, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.03, #queue-req: 0, 
[2025-07-28 01:24:21 DP1 TP0] Decode batch. #running-req: 2, #token: 1314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 409.82, #queue-req: 0, 
[2025-07-28 01:24:21 DP1 TP0] Decode batch. #running-req: 2, #token: 1394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 408.82, #queue-req: 0, 
[2025-07-28 01:24:21 DP1 TP0] Decode batch. #running-req: 2, #token: 1474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 410.12, #queue-req: 0, 
[2025-07-28 01:24:21 DP1 TP0] Decode batch. #running-req: 2, #token: 1554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 410.24, #queue-req: 0, 
[2025-07-28 01:24:21 DP1 TP0] Decode batch. #running-req: 2, #token: 1634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 410.23, #queue-req: 0, 
[2025-07-28 01:24:22 DP1 TP0] Decode batch. #running-req: 2, #token: 1714, token usage: 0.00, cuda graph: True, gen throughput (token/s): 409.57, #queue-req: 0, 
[2025-07-28 01:24:22 DP1 TP0] Decode batch. #running-req: 2, #token: 1794, token usage: 0.00, cuda graph: True, gen throughput (token/s): 409.90, #queue-req: 0, 
[2025-07-28 01:24:22 DP1 TP0] Decode batch. #running-req: 2, #token: 1874, token usage: 0.00, cuda graph: True, gen throughput (token/s): 410.08, #queue-req: 0, 
[2025-07-28 01:24:22 DP1 TP0] Decode batch. #running-req: 2, #token: 1954, token usage: 0.00, cuda graph: True, gen throughput (token/s): 408.77, #queue-req: 0, 
[2025-07-28 01:24:22 DP1 TP0] Decode batch. #running-req: 2, #token: 2034, token usage: 0.00, cuda graph: True, gen throughput (token/s): 408.45, #queue-req: 0, 
[2025-07-28 01:24:23 DP1 TP0] Decode batch. #running-req: 2, #token: 2114, token usage: 0.00, cuda graph: True, gen throughput (token/s): 409.80, #queue-req: 0, 
[2025-07-28 01:24:23 DP1 TP0] Decode batch. #running-req: 2, #token: 2194, token usage: 0.00, cuda graph: True, gen throughput (token/s): 410.18, #queue-req: 0, 
[2025-07-28 01:24:23 DP1 TP0] Decode batch. #running-req: 2, #token: 2274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 409.97, #queue-req: 0, 
[2025-07-28 01:24:23 DP1 TP0] Decode batch. #running-req: 2, #token: 2354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 409.83, #queue-req: 0, 
[2025-07-28 01:24:23 DP1 TP0] Decode batch. #running-req: 2, #token: 2434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 410.17, #queue-req: 0, 
[2025-07-28 01:24:24 DP1 TP0] Decode batch. #running-req: 2, #token: 2514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 409.29, #queue-req: 0, 
[2025-07-28 01:24:24 DP1 TP0] Decode batch. #running-req: 2, #token: 2594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 410.05, #queue-req: 0, 
[2025-07-28 01:24:24 DP1 TP0] Decode batch. #running-req: 2, #token: 2674, token usage: 0.00, cuda graph: True, gen throughput (token/s): 409.86, #queue-req: 0, 
[2025-07-28 01:24:24 DP1 TP0] Decode batch. #running-req: 2, #token: 2754, token usage: 0.00, cuda graph: True, gen throughput (token/s): 409.81, #queue-req: 0, 
[2025-07-28 01:24:24] INFO:     127.0.0.1:60714 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:24:24 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 181, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:24:24 DP0 TP0] Decode batch. #running-req: 1, #token: 276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 9.71, #queue-req: 0, 
[2025-07-28 01:24:24 DP1 TP0] Decode batch. #running-req: 1, #token: 1026, token usage: 0.00, cuda graph: True, gen throughput (token/s): 224.32, #queue-req: 0, 
[2025-07-28 01:24:24 DP0 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 192.38, #queue-req: 0, 
[2025-07-28 01:24:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1066, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.53, #queue-req: 0, 
[2025-07-28 01:24:25] INFO:     127.0.0.1:60720 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:24:25 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 164, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:24:25 DP0 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.61, #queue-req: 0, 
[2025-07-28 01:24:25 DP1 TP0] Decode batch. #running-req: 1, #token: 265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 173.89, #queue-req: 0, 
[2025-07-28 01:24:25 DP0 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.13, #queue-req: 0, 
[2025-07-28 01:24:25 DP1 TP0] Decode batch. #running-req: 1, #token: 305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.52, #queue-req: 0, 
[2025-07-28 01:24:25 DP0 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.88, #queue-req: 0, 
[2025-07-28 01:24:25 DP1 TP0] Decode batch. #running-req: 1, #token: 345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.19, #queue-req: 0, 
[2025-07-28 01:24:25 DP0 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.67, #queue-req: 0, 
[2025-07-28 01:24:25 DP1 TP0] Decode batch. #running-req: 1, #token: 385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.33, #queue-req: 0, 
[2025-07-28 01:24:25 DP0 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.53, #queue-req: 0, 
[2025-07-28 01:24:25 DP1 TP0] Decode batch. #running-req: 1, #token: 425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.77, #queue-req: 0, 
[2025-07-28 01:24:26 DP0 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.83, #queue-req: 0, 
[2025-07-28 01:24:26 DP1 TP0] Decode batch. #running-req: 1, #token: 465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.80, #queue-req: 0, 
[2025-07-28 01:24:26 DP0 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.09, #queue-req: 0, 
[2025-07-28 01:24:26 DP1 TP0] Decode batch. #running-req: 1, #token: 505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.79, #queue-req: 0, 
[2025-07-28 01:24:26 DP0 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.24, #queue-req: 0, 
[2025-07-28 01:24:26 DP1 TP0] Decode batch. #running-req: 1, #token: 545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.26, #queue-req: 0, 
[2025-07-28 01:24:26 DP0 TP0] Decode batch. #running-req: 1, #token: 676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.89, #queue-req: 0, 
[2025-07-28 01:24:26 DP1 TP0] Decode batch. #running-req: 1, #token: 585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.08, #queue-req: 0, 
[2025-07-28 01:24:26 DP0 TP0] Decode batch. #running-req: 1, #token: 716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.09, #queue-req: 0, 
[2025-07-28 01:24:26 DP1 TP0] Decode batch. #running-req: 1, #token: 625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.21, #queue-req: 0, 
[2025-07-28 01:24:27 DP0 TP0] Decode batch. #running-req: 1, #token: 756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.64, #queue-req: 0, 
[2025-07-28 01:24:27 DP1 TP0] Decode batch. #running-req: 1, #token: 665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.02, #queue-req: 0, 
[2025-07-28 01:24:27 DP0 TP0] Decode batch. #running-req: 1, #token: 796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.62, #queue-req: 0, 
[2025-07-28 01:24:27 DP1 TP0] Decode batch. #running-req: 1, #token: 705, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.24, #queue-req: 0, 
[2025-07-28 01:24:27 DP0 TP0] Decode batch. #running-req: 1, #token: 836, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.75, #queue-req: 0, 
[2025-07-28 01:24:27 DP1 TP0] Decode batch. #running-req: 1, #token: 745, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.31, #queue-req: 0, 
[2025-07-28 01:24:27 DP0 TP0] Decode batch. #running-req: 1, #token: 876, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.13, #queue-req: 0, 
[2025-07-28 01:24:27 DP1 TP0] Decode batch. #running-req: 1, #token: 785, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.18, #queue-req: 0, 
[2025-07-28 01:24:27 DP0 TP0] Decode batch. #running-req: 1, #token: 916, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.29, #queue-req: 0, 
[2025-07-28 01:24:27 DP1 TP0] Decode batch. #running-req: 1, #token: 825, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.99, #queue-req: 0, 
[2025-07-28 01:24:27] INFO:     127.0.0.1:47358 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:24:27 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 220, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:24:28 DP1 TP0] Decode batch. #running-req: 1, #token: 865, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.04, #queue-req: 0, 
[2025-07-28 01:24:28 DP0 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 168.63, #queue-req: 0, 
[2025-07-28 01:24:28 DP1 TP0] Decode batch. #running-req: 1, #token: 905, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.06, #queue-req: 0, 
[2025-07-28 01:24:28 DP0 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.85, #queue-req: 0, 
[2025-07-28 01:24:28 DP1 TP0] Decode batch. #running-req: 1, #token: 945, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.20, #queue-req: 0, 
[2025-07-28 01:24:28 DP0 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.64, #queue-req: 0, 
[2025-07-28 01:24:28 DP1 TP0] Decode batch. #running-req: 1, #token: 985, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.19, #queue-req: 0, 
[2025-07-28 01:24:28 DP0 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.79, #queue-req: 0, 
[2025-07-28 01:24:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1025, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.12, #queue-req: 0, 
[2025-07-28 01:24:28] INFO:     127.0.0.1:47372 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:24:28 DP0 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.20, #queue-req: 0, 
[2025-07-28 01:24:28 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 164, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:24:28 DP0 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.34, #queue-req: 0, 
[2025-07-28 01:24:29 DP1 TP0] Decode batch. #running-req: 1, #token: 289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 178.18, #queue-req: 0, 
[2025-07-28 01:24:29 DP0 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.60, #queue-req: 0, 
[2025-07-28 01:24:29 DP1 TP0] Decode batch. #running-req: 1, #token: 329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.62, #queue-req: 0, 
[2025-07-28 01:24:29 DP0 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.02, #queue-req: 0, 
[2025-07-28 01:24:29 DP1 TP0] Decode batch. #running-req: 1, #token: 369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.71, #queue-req: 0, 
[2025-07-28 01:24:29 DP0 TP0] Decode batch. #running-req: 1, #token: 656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.02, #queue-req: 0, 
[2025-07-28 01:24:29 DP1 TP0] Decode batch. #running-req: 1, #token: 409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.66, #queue-req: 0, 
[2025-07-28 01:24:29 DP0 TP0] Decode batch. #running-req: 1, #token: 696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.76, #queue-req: 0, 
[2025-07-28 01:24:29 DP1 TP0] Decode batch. #running-req: 1, #token: 449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.78, #queue-req: 0, 
[2025-07-28 01:24:29 DP0 TP0] Decode batch. #running-req: 1, #token: 736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.96, #queue-req: 0, 
[2025-07-28 01:24:29 DP1 TP0] Decode batch. #running-req: 1, #token: 489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.69, #queue-req: 0, 
[2025-07-28 01:24:30 DP0 TP0] Decode batch. #running-req: 1, #token: 776, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.26, #queue-req: 0, 
[2025-07-28 01:24:30 DP1 TP0] Decode batch. #running-req: 1, #token: 529, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.57, #queue-req: 0, 
[2025-07-28 01:24:30 DP0 TP0] Decode batch. #running-req: 1, #token: 816, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.05, #queue-req: 0, 
[2025-07-28 01:24:30 DP1 TP0] Decode batch. #running-req: 1, #token: 569, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.16, #queue-req: 0, 
[2025-07-28 01:24:30 DP0 TP0] Decode batch. #running-req: 1, #token: 856, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.22, #queue-req: 0, 
[2025-07-28 01:24:30 DP1 TP0] Decode batch. #running-req: 1, #token: 609, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.32, #queue-req: 0, 
[2025-07-28 01:24:30 DP0 TP0] Decode batch. #running-req: 1, #token: 896, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.89, #queue-req: 0, 
[2025-07-28 01:24:30 DP1 TP0] Decode batch. #running-req: 1, #token: 649, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.38, #queue-req: 0, 
[2025-07-28 01:24:30 DP0 TP0] Decode batch. #running-req: 1, #token: 936, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.30, #queue-req: 0, 
[2025-07-28 01:24:30 DP1 TP0] Decode batch. #running-req: 1, #token: 689, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.43, #queue-req: 0, 
[2025-07-28 01:24:31 DP0 TP0] Decode batch. #running-req: 1, #token: 976, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.47, #queue-req: 0, 
[2025-07-28 01:24:31 DP1 TP0] Decode batch. #running-req: 1, #token: 729, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.43, #queue-req: 0, 
[2025-07-28 01:24:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1016, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.15, #queue-req: 0, 
[2025-07-28 01:24:31 DP1 TP0] Decode batch. #running-req: 1, #token: 769, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.17, #queue-req: 0, 
[2025-07-28 01:24:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1056, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.68, #queue-req: 0, 
[2025-07-28 01:24:31 DP1 TP0] Decode batch. #running-req: 1, #token: 809, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.07, #queue-req: 0, 
[2025-07-28 01:24:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1096, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.23, #queue-req: 0, 
[2025-07-28 01:24:31 DP1 TP0] Decode batch. #running-req: 1, #token: 849, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.02, #queue-req: 0, 
[2025-07-28 01:24:31] INFO:     127.0.0.1:47378 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:24:31 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:24:31 DP1 TP0] Decode batch. #running-req: 1, #token: 889, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.14, #queue-req: 0, 
[2025-07-28 01:24:31 DP0 TP0] Decode batch. #running-req: 1, #token: 242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 171.90, #queue-req: 0, 
[2025-07-28 01:24:32 DP1 TP0] Decode batch. #running-req: 1, #token: 929, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.24, #queue-req: 0, 
[2025-07-28 01:24:32 DP0 TP0] Decode batch. #running-req: 1, #token: 282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.24, #queue-req: 0, 
[2025-07-28 01:24:32 DP1 TP0] Decode batch. #running-req: 1, #token: 969, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.22, #queue-req: 0, 
[2025-07-28 01:24:32 DP0 TP0] Decode batch. #running-req: 1, #token: 322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.16, #queue-req: 0, 
[2025-07-28 01:24:32] INFO:     127.0.0.1:47390 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:24:32 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 132, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:24:32 DP0 TP0] Decode batch. #running-req: 1, #token: 362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.11, #queue-req: 0, 
[2025-07-28 01:24:32 DP1 TP0] Decode batch. #running-req: 1, #token: 239, token usage: 0.00, cuda graph: True, gen throughput (token/s): 175.58, #queue-req: 0, 
[2025-07-28 01:24:32 DP0 TP0] Decode batch. #running-req: 1, #token: 402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.14, #queue-req: 0, 
[2025-07-28 01:24:32 DP1 TP0] Decode batch. #running-req: 1, #token: 279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 215.10, #queue-req: 0, 
[2025-07-28 01:24:32 DP0 TP0] Decode batch. #running-req: 1, #token: 442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.24, #queue-req: 0, 
[2025-07-28 01:24:32 DP1 TP0] Decode batch. #running-req: 1, #token: 319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.54, #queue-req: 0, 
[2025-07-28 01:24:32 DP0 TP0] Decode batch. #running-req: 1, #token: 482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.31, #queue-req: 0, 
[2025-07-28 01:24:32 DP1 TP0] Decode batch. #running-req: 1, #token: 359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.62, #queue-req: 0, 
[2025-07-28 01:24:33 DP0 TP0] Decode batch. #running-req: 1, #token: 522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.20, #queue-req: 0, 
[2025-07-28 01:24:33 DP1 TP0] Decode batch. #running-req: 1, #token: 399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.67, #queue-req: 0, 
[2025-07-28 01:24:33 DP0 TP0] Decode batch. #running-req: 1, #token: 562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.24, #queue-req: 0, 
[2025-07-28 01:24:33 DP1 TP0] Decode batch. #running-req: 1, #token: 439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.77, #queue-req: 0, 
[2025-07-28 01:24:33 DP0 TP0] Decode batch. #running-req: 1, #token: 602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.22, #queue-req: 0, 
[2025-07-28 01:24:33 DP1 TP0] Decode batch. #running-req: 1, #token: 479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.74, #queue-req: 0, 
[2025-07-28 01:24:33 DP0 TP0] Decode batch. #running-req: 1, #token: 642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.63, #queue-req: 0, 
[2025-07-28 01:24:33 DP1 TP0] Decode batch. #running-req: 1, #token: 519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.60, #queue-req: 0, 
[2025-07-28 01:24:33 DP0 TP0] Decode batch. #running-req: 1, #token: 682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.02, #queue-req: 0, 
[2025-07-28 01:24:33 DP1 TP0] Decode batch. #running-req: 1, #token: 559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.40, #queue-req: 0, 
[2025-07-28 01:24:34 DP0 TP0] Decode batch. #running-req: 1, #token: 722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.02, #queue-req: 0, 
[2025-07-28 01:24:34 DP1 TP0] Decode batch. #running-req: 1, #token: 599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.30, #queue-req: 0, 
[2025-07-28 01:24:34 DP0 TP0] Decode batch. #running-req: 1, #token: 762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.08, #queue-req: 0, 
[2025-07-28 01:24:34 DP1 TP0] Decode batch. #running-req: 1, #token: 639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.68, #queue-req: 0, 
[2025-07-28 01:24:34 DP0 TP0] Decode batch. #running-req: 1, #token: 802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.00, #queue-req: 0, 
[2025-07-28 01:24:34 DP1 TP0] Decode batch. #running-req: 1, #token: 679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.30, #queue-req: 0, 
[2025-07-28 01:24:34 DP1 TP0] Decode batch. #running-req: 1, #token: 719, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.02, #queue-req: 0, 
[2025-07-28 01:24:34 DP0 TP0] Decode batch. #running-req: 1, #token: 842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.09, #queue-req: 0, 
[2025-07-28 01:24:34 DP1 TP0] Decode batch. #running-req: 1, #token: 759, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.68, #queue-req: 0, 
[2025-07-28 01:24:34 DP0 TP0] Decode batch. #running-req: 1, #token: 882, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.35, #queue-req: 0, 
[2025-07-28 01:24:35 DP1 TP0] Decode batch. #running-req: 1, #token: 799, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.55, #queue-req: 0, 
[2025-07-28 01:24:35 DP0 TP0] Decode batch. #running-req: 1, #token: 922, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.61, #queue-req: 0, 
[2025-07-28 01:24:35 DP1 TP0] Decode batch. #running-req: 1, #token: 839, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.90, #queue-req: 0, 
[2025-07-28 01:24:35 DP0 TP0] Decode batch. #running-req: 1, #token: 962, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.55, #queue-req: 0, 
[2025-07-28 01:24:35 DP1 TP0] Decode batch. #running-req: 1, #token: 879, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.84, #queue-req: 0, 
[2025-07-28 01:24:35 DP0 TP0] Decode batch. #running-req: 1, #token: 1002, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.14, #queue-req: 0, 
[2025-07-28 01:24:35 DP1 TP0] Decode batch. #running-req: 1, #token: 919, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.06, #queue-req: 0, 
[2025-07-28 01:24:35 DP0 TP0] Decode batch. #running-req: 1, #token: 1042, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.96, #queue-req: 0, 
[2025-07-28 01:24:35 DP1 TP0] Decode batch. #running-req: 1, #token: 959, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.75, #queue-req: 0, 
[2025-07-28 01:24:35 DP0 TP0] Decode batch. #running-req: 1, #token: 1082, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.79, #queue-req: 0, 
[2025-07-28 01:24:35 DP1 TP0] Decode batch. #running-req: 1, #token: 999, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.03, #queue-req: 0, 
[2025-07-28 01:24:35] INFO:     127.0.0.1:47404 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:24:35 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 188, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:24:36 DP0 TP0] Decode batch. #running-req: 1, #token: 276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 168.93, #queue-req: 0, 
[2025-07-28 01:24:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1039, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.95, #queue-req: 0, 
[2025-07-28 01:24:36 DP0 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.81, #queue-req: 0, 
[2025-07-28 01:24:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1079, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.50, #queue-req: 0, 
[2025-07-28 01:24:36] INFO:     127.0.0.1:47410 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:24:36 DP0 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.05, #queue-req: 0, 
[2025-07-28 01:24:36 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 180, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:24:36 DP1 TP0] Decode batch. #running-req: 1, #token: 297, token usage: 0.00, cuda graph: True, gen throughput (token/s): 168.72, #queue-req: 0, 
[2025-07-28 01:24:36 DP0 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.25, #queue-req: 0, 
[2025-07-28 01:24:36 DP1 TP0] Decode batch. #running-req: 1, #token: 337, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.80, #queue-req: 0, 
[2025-07-28 01:24:36 DP0 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.23, #queue-req: 0, 
[2025-07-28 01:24:36 DP1 TP0] Decode batch. #running-req: 1, #token: 377, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.15, #queue-req: 0, 
[2025-07-28 01:24:36 DP0 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.25, #queue-req: 0, 
[2025-07-28 01:24:37 DP1 TP0] Decode batch. #running-req: 1, #token: 417, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.89, #queue-req: 0, 
[2025-07-28 01:24:37 DP0 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.22, #queue-req: 0, 
[2025-07-28 01:24:37 DP1 TP0] Decode batch. #running-req: 1, #token: 457, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.15, #queue-req: 0, 
[2025-07-28 01:24:37 DP0 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.89, #queue-req: 0, 
[2025-07-28 01:24:37 DP1 TP0] Decode batch. #running-req: 1, #token: 497, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.33, #queue-req: 0, 
[2025-07-28 01:24:37 DP0 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.94, #queue-req: 0, 
[2025-07-28 01:24:37 DP1 TP0] Decode batch. #running-req: 1, #token: 537, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.51, #queue-req: 0, 
[2025-07-28 01:24:37 DP0 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.93, #queue-req: 0, 
[2025-07-28 01:24:37 DP1 TP0] Decode batch. #running-req: 1, #token: 577, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.62, #queue-req: 0, 
[2025-07-28 01:24:37 DP0 TP0] Decode batch. #running-req: 1, #token: 676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.90, #queue-req: 0, 
[2025-07-28 01:24:38 DP1 TP0] Decode batch. #running-req: 1, #token: 617, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.24, #queue-req: 0, 
[2025-07-28 01:24:38 DP0 TP0] Decode batch. #running-req: 1, #token: 716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.92, #queue-req: 0, 
[2025-07-28 01:24:38 DP1 TP0] Decode batch. #running-req: 1, #token: 657, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.21, #queue-req: 0, 
[2025-07-28 01:24:38 DP0 TP0] Decode batch. #running-req: 1, #token: 756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.54, #queue-req: 0, 
[2025-07-28 01:24:38 DP1 TP0] Decode batch. #running-req: 1, #token: 697, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.26, #queue-req: 0, 
[2025-07-28 01:24:38 DP0 TP0] Decode batch. #running-req: 1, #token: 796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.86, #queue-req: 0, 
[2025-07-28 01:24:38 DP1 TP0] Decode batch. #running-req: 1, #token: 737, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.27, #queue-req: 0, 
[2025-07-28 01:24:38 DP0 TP0] Decode batch. #running-req: 1, #token: 836, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.61, #queue-req: 0, 
[2025-07-28 01:24:38 DP1 TP0] Decode batch. #running-req: 1, #token: 777, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.17, #queue-req: 0, 
[2025-07-28 01:24:38 DP0 TP0] Decode batch. #running-req: 1, #token: 876, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.83, #queue-req: 0, 
[2025-07-28 01:24:39 DP1 TP0] Decode batch. #running-req: 1, #token: 817, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.88, #queue-req: 0, 
[2025-07-28 01:24:39 DP0 TP0] Decode batch. #running-req: 1, #token: 916, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.84, #queue-req: 0, 
[2025-07-28 01:24:39 DP1 TP0] Decode batch. #running-req: 1, #token: 857, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.01, #queue-req: 0, 
[2025-07-28 01:24:39 DP0 TP0] Decode batch. #running-req: 1, #token: 956, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.96, #queue-req: 0, 
[2025-07-28 01:24:39 DP1 TP0] Decode batch. #running-req: 1, #token: 897, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.02, #queue-req: 0, 
[2025-07-28 01:24:39 DP0 TP0] Decode batch. #running-req: 1, #token: 996, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.90, #queue-req: 0, 
[2025-07-28 01:24:39 DP1 TP0] Decode batch. #running-req: 1, #token: 937, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.11, #queue-req: 0, 
[2025-07-28 01:24:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1036, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.61, #queue-req: 0, 
[2025-07-28 01:24:39 DP1 TP0] Decode batch. #running-req: 1, #token: 977, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.15, #queue-req: 0, 
[2025-07-28 01:24:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1076, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.23, #queue-req: 0, 
[2025-07-28 01:24:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1017, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.11, #queue-req: 0, 
[2025-07-28 01:24:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1116, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.16, #queue-req: 0, 
[2025-07-28 01:24:40] INFO:     127.0.0.1:48366 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:24:40 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:24:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1057, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.44, #queue-req: 0, 
[2025-07-28 01:24:40 DP0 TP0] Decode batch. #running-req: 1, #token: 242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 173.76, #queue-req: 0, 
[2025-07-28 01:24:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1097, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.52, #queue-req: 0, 
[2025-07-28 01:24:40 DP0 TP0] Decode batch. #running-req: 1, #token: 282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.07, #queue-req: 0, 
[2025-07-28 01:24:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1137, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.40, #queue-req: 0, 
[2025-07-28 01:24:40 DP0 TP0] Decode batch. #running-req: 1, #token: 322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.81, #queue-req: 0, 
[2025-07-28 01:24:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1177, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.59, #queue-req: 0, 
[2025-07-28 01:24:40 DP0 TP0] Decode batch. #running-req: 1, #token: 362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.03, #queue-req: 0, 
[2025-07-28 01:24:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1217, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.55, #queue-req: 0, 
[2025-07-28 01:24:40 DP0 TP0] Decode batch. #running-req: 1, #token: 402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.19, #queue-req: 0, 
[2025-07-28 01:24:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1257, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.97, #queue-req: 0, 
[2025-07-28 01:24:41 DP0 TP0] Decode batch. #running-req: 1, #token: 442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.25, #queue-req: 0, 
[2025-07-28 01:24:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1297, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.58, #queue-req: 0, 
[2025-07-28 01:24:41 DP0 TP0] Decode batch. #running-req: 1, #token: 482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.30, #queue-req: 0, 
[2025-07-28 01:24:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1337, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.34, #queue-req: 0, 
[2025-07-28 01:24:41 DP0 TP0] Decode batch. #running-req: 1, #token: 522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.21, #queue-req: 0, 
[2025-07-28 01:24:41] INFO:     127.0.0.1:48380 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:24:41 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 128, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:24:41 DP1 TP0] Decode batch. #running-req: 1, #token: 233, token usage: 0.00, cuda graph: True, gen throughput (token/s): 171.30, #queue-req: 0, 
[2025-07-28 01:24:41 DP0 TP0] Decode batch. #running-req: 1, #token: 562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.92, #queue-req: 0, 
[2025-07-28 01:24:41 DP1 TP0] Decode batch. #running-req: 1, #token: 273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 215.22, #queue-req: 0, 
[2025-07-28 01:24:41 DP0 TP0] Decode batch. #running-req: 1, #token: 602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.86, #queue-req: 0, 
[2025-07-28 01:24:42 DP1 TP0] Decode batch. #running-req: 1, #token: 313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.57, #queue-req: 0, 
[2025-07-28 01:24:42 DP0 TP0] Decode batch. #running-req: 1, #token: 642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.85, #queue-req: 0, 
[2025-07-28 01:24:42 DP1 TP0] Decode batch. #running-req: 1, #token: 353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.55, #queue-req: 0, 
[2025-07-28 01:24:42 DP0 TP0] Decode batch. #running-req: 1, #token: 682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.01, #queue-req: 0, 
[2025-07-28 01:24:42 DP1 TP0] Decode batch. #running-req: 1, #token: 393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.62, #queue-req: 0, 
[2025-07-28 01:24:42 DP0 TP0] Decode batch. #running-req: 1, #token: 722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.89, #queue-req: 0, 
[2025-07-28 01:24:42 DP1 TP0] Decode batch. #running-req: 1, #token: 433, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.69, #queue-req: 0, 
[2025-07-28 01:24:42 DP0 TP0] Decode batch. #running-req: 1, #token: 762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.18, #queue-req: 0, 
[2025-07-28 01:24:42 DP1 TP0] Decode batch. #running-req: 1, #token: 473, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.68, #queue-req: 0, 
[2025-07-28 01:24:42 DP0 TP0] Decode batch. #running-req: 1, #token: 802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.79, #queue-req: 0, 
[2025-07-28 01:24:42 DP1 TP0] Decode batch. #running-req: 1, #token: 513, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.65, #queue-req: 0, 
[2025-07-28 01:24:42 DP0 TP0] Decode batch. #running-req: 1, #token: 842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.62, #queue-req: 0, 
[2025-07-28 01:24:43 DP1 TP0] Decode batch. #running-req: 1, #token: 553, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.01, #queue-req: 0, 
[2025-07-28 01:24:43 DP0 TP0] Decode batch. #running-req: 1, #token: 882, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.97, #queue-req: 0, 
[2025-07-28 01:24:43 DP1 TP0] Decode batch. #running-req: 1, #token: 593, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.33, #queue-req: 0, 
[2025-07-28 01:24:43 DP0 TP0] Decode batch. #running-req: 1, #token: 922, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.64, #queue-req: 0, 
[2025-07-28 01:24:43 DP1 TP0] Decode batch. #running-req: 1, #token: 633, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.23, #queue-req: 0, 
[2025-07-28 01:24:43 DP0 TP0] Decode batch. #running-req: 1, #token: 962, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.64, #queue-req: 0, 
[2025-07-28 01:24:43 DP1 TP0] Decode batch. #running-req: 1, #token: 673, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.25, #queue-req: 0, 
[2025-07-28 01:24:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1002, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.90, #queue-req: 0, 
[2025-07-28 01:24:43 DP1 TP0] Decode batch. #running-req: 1, #token: 713, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.13, #queue-req: 0, 
[2025-07-28 01:24:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1042, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.93, #queue-req: 0, 
[2025-07-28 01:24:44 DP1 TP0] Decode batch. #running-req: 1, #token: 753, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.26, #queue-req: 0, 
[2025-07-28 01:24:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1082, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.41, #queue-req: 0, 
[2025-07-28 01:24:44] INFO:     127.0.0.1:48412 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:24:44 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 194, #cached-token: 86, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:24:44 DP0 TP0] Decode batch. #running-req: 2, #token: 1342, token usage: 0.00, cuda graph: True, gen throughput (token/s): 275.04, #queue-req: 0, 
[2025-07-28 01:24:44] INFO:     127.0.0.1:48396 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:24:44 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 189, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:24:44 DP0 TP0] Decode batch. #running-req: 1, #token: 344, token usage: 0.00, cuda graph: True, gen throughput (token/s): 323.64, #queue-req: 0, 
[2025-07-28 01:24:44 DP1 TP0] Decode batch. #running-req: 1, #token: 300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 77.25, #queue-req: 0, 
[2025-07-28 01:24:44 DP0 TP0] Decode batch. #running-req: 1, #token: 384, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.55, #queue-req: 0, 
[2025-07-28 01:24:44 DP1 TP0] Decode batch. #running-req: 1, #token: 340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.66, #queue-req: 0, 
[2025-07-28 01:24:44 DP0 TP0] Decode batch. #running-req: 1, #token: 424, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.76, #queue-req: 0, 
[2025-07-28 01:24:44 DP1 TP0] Decode batch. #running-req: 1, #token: 380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.29, #queue-req: 0, 
[2025-07-28 01:24:45 DP0 TP0] Decode batch. #running-req: 1, #token: 464, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.87, #queue-req: 0, 
[2025-07-28 01:24:45 DP1 TP0] Decode batch. #running-req: 1, #token: 420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.86, #queue-req: 0, 
[2025-07-28 01:24:45 DP0 TP0] Decode batch. #running-req: 1, #token: 504, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.90, #queue-req: 0, 
[2025-07-28 01:24:45 DP1 TP0] Decode batch. #running-req: 1, #token: 460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.63, #queue-req: 0, 
[2025-07-28 01:24:45 DP0 TP0] Decode batch. #running-req: 1, #token: 544, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.46, #queue-req: 0, 
[2025-07-28 01:24:45 DP1 TP0] Decode batch. #running-req: 1, #token: 500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.68, #queue-req: 0, 
[2025-07-28 01:24:45 DP0 TP0] Decode batch. #running-req: 1, #token: 584, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.50, #queue-req: 0, 
[2025-07-28 01:24:45 DP1 TP0] Decode batch. #running-req: 1, #token: 540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.29, #queue-req: 0, 
[2025-07-28 01:24:45 DP0 TP0] Decode batch. #running-req: 1, #token: 624, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.50, #queue-req: 0, 
[2025-07-28 01:24:45 DP1 TP0] Decode batch. #running-req: 1, #token: 580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.23, #queue-req: 0, 
[2025-07-28 01:24:46 DP0 TP0] Decode batch. #running-req: 1, #token: 664, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.65, #queue-req: 0, 
[2025-07-28 01:24:46 DP1 TP0] Decode batch. #running-req: 1, #token: 620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.27, #queue-req: 0, 
[2025-07-28 01:24:46 DP0 TP0] Decode batch. #running-req: 1, #token: 704, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.75, #queue-req: 0, 
[2025-07-28 01:24:46 DP1 TP0] Decode batch. #running-req: 1, #token: 660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.37, #queue-req: 0, 
[2025-07-28 01:24:46 DP0 TP0] Decode batch. #running-req: 1, #token: 744, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.80, #queue-req: 0, 
[2025-07-28 01:24:46 DP1 TP0] Decode batch. #running-req: 1, #token: 700, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.41, #queue-req: 0, 
[2025-07-28 01:24:46 DP0 TP0] Decode batch. #running-req: 1, #token: 784, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.66, #queue-req: 0, 
[2025-07-28 01:24:46 DP1 TP0] Decode batch. #running-req: 1, #token: 740, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.38, #queue-req: 0, 
[2025-07-28 01:24:46 DP0 TP0] Decode batch. #running-req: 1, #token: 824, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.46, #queue-req: 0, 
[2025-07-28 01:24:46 DP1 TP0] Decode batch. #running-req: 1, #token: 780, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.27, #queue-req: 0, 
[2025-07-28 01:24:46 DP0 TP0] Decode batch. #running-req: 1, #token: 864, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.49, #queue-req: 0, 
[2025-07-28 01:24:47 DP1 TP0] Decode batch. #running-req: 1, #token: 820, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.01, #queue-req: 0, 
[2025-07-28 01:24:47 DP0 TP0] Decode batch. #running-req: 1, #token: 904, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.52, #queue-req: 0, 
[2025-07-28 01:24:47 DP1 TP0] Decode batch. #running-req: 1, #token: 860, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.87, #queue-req: 0, 
[2025-07-28 01:24:47 DP0 TP0] Decode batch. #running-req: 1, #token: 944, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.55, #queue-req: 0, 
[2025-07-28 01:24:47 DP1 TP0] Decode batch. #running-req: 1, #token: 900, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.96, #queue-req: 0, 
[2025-07-28 01:24:47 DP0 TP0] Decode batch. #running-req: 1, #token: 984, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.66, #queue-req: 0, 
[2025-07-28 01:24:47] INFO:     127.0.0.1:33308 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:24:47 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 157, #cached-token: 86, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:24:47 DP0 TP0] Decode batch. #running-req: 2, #token: 1207, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.00, #queue-req: 0, 
[2025-07-28 01:24:47 DP0 TP0] Decode batch. #running-req: 2, #token: 1287, token usage: 0.00, cuda graph: True, gen throughput (token/s): 410.86, #queue-req: 0, 
[2025-07-28 01:24:48 DP0 TP0] Decode batch. #running-req: 2, #token: 1367, token usage: 0.00, cuda graph: True, gen throughput (token/s): 410.14, #queue-req: 0, 
[2025-07-28 01:24:48 DP0 TP0] Decode batch. #running-req: 2, #token: 1447, token usage: 0.00, cuda graph: True, gen throughput (token/s): 409.54, #queue-req: 0, 
[2025-07-28 01:24:48 DP0 TP0] Decode batch. #running-req: 2, #token: 1527, token usage: 0.00, cuda graph: True, gen throughput (token/s): 409.66, #queue-req: 0, 
[2025-07-28 01:24:48] INFO:     127.0.0.1:33292 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:24:48 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 161, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:24:48 DP1 TP0] Decode batch. #running-req: 1, #token: 255, token usage: 0.00, cuda graph: True, gen throughput (token/s): 32.15, #queue-req: 0, 
[2025-07-28 01:24:48 DP0 TP0] Decode batch. #running-req: 1, #token: 469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 249.08, #queue-req: 0, 
[2025-07-28 01:24:48 DP1 TP0] Decode batch. #running-req: 1, #token: 295, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.98, #queue-req: 0, 
[2025-07-28 01:24:48 DP0 TP0] Decode batch. #running-req: 1, #token: 509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.35, #queue-req: 0, 
[2025-07-28 01:24:49 DP1 TP0] Decode batch. #running-req: 1, #token: 335, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.96, #queue-req: 0, 
[2025-07-28 01:24:49 DP0 TP0] Decode batch. #running-req: 1, #token: 549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.19, #queue-req: 0, 
[2025-07-28 01:24:49 DP1 TP0] Decode batch. #running-req: 1, #token: 375, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.44, #queue-req: 0, 
[2025-07-28 01:24:49 DP0 TP0] Decode batch. #running-req: 1, #token: 589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.39, #queue-req: 0, 
[2025-07-28 01:24:49 DP1 TP0] Decode batch. #running-req: 1, #token: 415, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.44, #queue-req: 0, 
[2025-07-28 01:24:49 DP0 TP0] Decode batch. #running-req: 1, #token: 629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.58, #queue-req: 0, 
[2025-07-28 01:24:49 DP1 TP0] Decode batch. #running-req: 1, #token: 455, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.79, #queue-req: 0, 
[2025-07-28 01:24:49 DP0 TP0] Decode batch. #running-req: 1, #token: 669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.71, #queue-req: 0, 
[2025-07-28 01:24:49 DP1 TP0] Decode batch. #running-req: 1, #token: 495, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.96, #queue-req: 0, 
[2025-07-28 01:24:49 DP0 TP0] Decode batch. #running-req: 1, #token: 709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.56, #queue-req: 0, 
[2025-07-28 01:24:49 DP1 TP0] Decode batch. #running-req: 1, #token: 535, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.01, #queue-req: 0, 
[2025-07-28 01:24:50 DP0 TP0] Decode batch. #running-req: 1, #token: 749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.65, #queue-req: 0, 
[2025-07-28 01:24:50 DP1 TP0] Decode batch. #running-req: 1, #token: 575, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.92, #queue-req: 0, 
[2025-07-28 01:24:50 DP0 TP0] Decode batch. #running-req: 1, #token: 789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.31, #queue-req: 0, 
[2025-07-28 01:24:50 DP1 TP0] Decode batch. #running-req: 1, #token: 615, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.66, #queue-req: 0, 
[2025-07-28 01:24:50 DP0 TP0] Decode batch. #running-req: 1, #token: 829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.50, #queue-req: 0, 
[2025-07-28 01:24:50 DP1 TP0] Decode batch. #running-req: 1, #token: 655, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.18, #queue-req: 0, 
[2025-07-28 01:24:50 DP0 TP0] Decode batch. #running-req: 1, #token: 869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.30, #queue-req: 0, 
[2025-07-28 01:24:50 DP1 TP0] Decode batch. #running-req: 1, #token: 695, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.24, #queue-req: 0, 
[2025-07-28 01:24:50 DP0 TP0] Decode batch. #running-req: 1, #token: 909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.26, #queue-req: 0, 
[2025-07-28 01:24:50 DP1 TP0] Decode batch. #running-req: 1, #token: 735, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.27, #queue-req: 0, 
[2025-07-28 01:24:50 DP0 TP0] Decode batch. #running-req: 1, #token: 949, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.57, #queue-req: 0, 
[2025-07-28 01:24:51 DP1 TP0] Decode batch. #running-req: 1, #token: 775, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.23, #queue-req: 0, 
[2025-07-28 01:24:51 DP0 TP0] Decode batch. #running-req: 1, #token: 989, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.60, #queue-req: 0, 
[2025-07-28 01:24:51 DP1 TP0] Decode batch. #running-req: 1, #token: 815, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.04, #queue-req: 0, 
[2025-07-28 01:24:51 DP0 TP0] Decode batch. #running-req: 1, #token: 1029, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.40, #queue-req: 0, 
[2025-07-28 01:24:51 DP1 TP0] Decode batch. #running-req: 1, #token: 855, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.05, #queue-req: 0, 
[2025-07-28 01:24:51 DP0 TP0] Decode batch. #running-req: 1, #token: 1069, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.71, #queue-req: 0, 
[2025-07-28 01:24:51 DP1 TP0] Decode batch. #running-req: 1, #token: 895, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.96, #queue-req: 0, 
[2025-07-28 01:24:51 DP0 TP0] Decode batch. #running-req: 1, #token: 1109, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.59, #queue-req: 0, 
[2025-07-28 01:24:51 DP1 TP0] Decode batch. #running-req: 1, #token: 935, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.25, #queue-req: 0, 
[2025-07-28 01:24:51 DP0 TP0] Decode batch. #running-req: 1, #token: 1149, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.71, #queue-req: 0, 
[2025-07-28 01:24:52 DP1 TP0] Decode batch. #running-req: 1, #token: 975, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.31, #queue-req: 0, 
[2025-07-28 01:24:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.15, #queue-req: 0, 
[2025-07-28 01:24:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1015, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.23, #queue-req: 0, 
[2025-07-28 01:24:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.12, #queue-req: 0, 
[2025-07-28 01:24:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1055, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.79, #queue-req: 0, 
[2025-07-28 01:24:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.94, #queue-req: 0, 
[2025-07-28 01:24:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1095, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.52, #queue-req: 0, 
[2025-07-28 01:24:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.69, #queue-req: 0, 
[2025-07-28 01:24:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1135, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.52, #queue-req: 0, 
[2025-07-28 01:24:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.58, #queue-req: 0, 
[2025-07-28 01:24:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1175, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.62, #queue-req: 0, 
[2025-07-28 01:24:53] INFO:     127.0.0.1:33318 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:24:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.62, #queue-req: 0, 
[2025-07-28 01:24:53 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 206, #cached-token: 86, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:24:53 DP0 TP0] Decode batch. #running-req: 2, #token: 1674, token usage: 0.00, cuda graph: True, gen throughput (token/s): 342.07, #queue-req: 0, 
[2025-07-28 01:24:53 DP0 TP0] Decode batch. #running-req: 2, #token: 1754, token usage: 0.00, cuda graph: True, gen throughput (token/s): 409.69, #queue-req: 0, 
[2025-07-28 01:24:53 DP0 TP0] Decode batch. #running-req: 2, #token: 1834, token usage: 0.00, cuda graph: True, gen throughput (token/s): 409.20, #queue-req: 0, 
[2025-07-28 01:24:53 DP0 TP0] Decode batch. #running-req: 2, #token: 1914, token usage: 0.00, cuda graph: True, gen throughput (token/s): 408.46, #queue-req: 0, 
[2025-07-28 01:24:54 DP0 TP0] Decode batch. #running-req: 2, #token: 1994, token usage: 0.00, cuda graph: True, gen throughput (token/s): 409.17, #queue-req: 0, 
[2025-07-28 01:24:54 DP0 TP0] Decode batch. #running-req: 2, #token: 2074, token usage: 0.00, cuda graph: True, gen throughput (token/s): 409.33, #queue-req: 0, 
[2025-07-28 01:24:54] INFO:     127.0.0.1:33316 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:24:54 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 142, #cached-token: 89, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:24:54 DP0 TP0] Decode batch. #running-req: 1, #token: 571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 258.54, #queue-req: 0, 
[2025-07-28 01:24:54 DP1 TP0] Decode batch. #running-req: 1, #token: 260, token usage: 0.00, cuda graph: True, gen throughput (token/s): 26.50, #queue-req: 0, 
[2025-07-28 01:24:54 DP0 TP0] Decode batch. #running-req: 1, #token: 611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.81, #queue-req: 0, 
[2025-07-28 01:24:54 DP1 TP0] Decode batch. #running-req: 1, #token: 300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.97, #queue-req: 0, 
[2025-07-28 01:24:54 DP0 TP0] Decode batch. #running-req: 1, #token: 651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.84, #queue-req: 0, 
[2025-07-28 01:24:54 DP1 TP0] Decode batch. #running-req: 1, #token: 340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.96, #queue-req: 0, 
[2025-07-28 01:24:55 DP0 TP0] Decode batch. #running-req: 1, #token: 691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 202.30, #queue-req: 0, 
[2025-07-28 01:24:55 DP1 TP0] Decode batch. #running-req: 1, #token: 380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.25, #queue-req: 0, 
[2025-07-28 01:24:55 DP0 TP0] Decode batch. #running-req: 1, #token: 731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.35, #queue-req: 0, 
[2025-07-28 01:24:55 DP1 TP0] Decode batch. #running-req: 1, #token: 420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.38, #queue-req: 0, 
[2025-07-28 01:24:55 DP0 TP0] Decode batch. #running-req: 1, #token: 771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.93, #queue-req: 0, 
[2025-07-28 01:24:55 DP1 TP0] Decode batch. #running-req: 1, #token: 460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.68, #queue-req: 0, 
[2025-07-28 01:24:55 DP0 TP0] Decode batch. #running-req: 1, #token: 811, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.91, #queue-req: 0, 
[2025-07-28 01:24:55 DP1 TP0] Decode batch. #running-req: 1, #token: 500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.79, #queue-req: 0, 
[2025-07-28 01:24:55 DP0 TP0] Decode batch. #running-req: 1, #token: 851, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.78, #queue-req: 0, 
[2025-07-28 01:24:55 DP1 TP0] Decode batch. #running-req: 1, #token: 540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.25, #queue-req: 0, 
[2025-07-28 01:24:55 DP0 TP0] Decode batch. #running-req: 1, #token: 891, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.53, #queue-req: 0, 
[2025-07-28 01:24:55 DP1 TP0] Decode batch. #running-req: 1, #token: 580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.44, #queue-req: 0, 
[2025-07-28 01:24:56 DP0 TP0] Decode batch. #running-req: 1, #token: 931, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.95, #queue-req: 0, 
[2025-07-28 01:24:56 DP1 TP0] Decode batch. #running-req: 1, #token: 620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.42, #queue-req: 0, 
[2025-07-28 01:24:56 DP0 TP0] Decode batch. #running-req: 1, #token: 971, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.58, #queue-req: 0, 
[2025-07-28 01:24:56 DP1 TP0] Decode batch. #running-req: 1, #token: 660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.48, #queue-req: 0, 
[2025-07-28 01:24:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1011, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.75, #queue-req: 0, 
[2025-07-28 01:24:56 DP1 TP0] Decode batch. #running-req: 1, #token: 700, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.46, #queue-req: 0, 
[2025-07-28 01:24:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1051, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.42, #queue-req: 0, 
[2025-07-28 01:24:56 DP1 TP0] Decode batch. #running-req: 1, #token: 740, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.50, #queue-req: 0, 
[2025-07-28 01:24:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1091, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.92, #queue-req: 0, 
[2025-07-28 01:24:56 DP1 TP0] Decode batch. #running-req: 1, #token: 780, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.50, #queue-req: 0, 
[2025-07-28 01:24:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1131, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.69, #queue-req: 0, 
[2025-07-28 01:24:57 DP1 TP0] Decode batch. #running-req: 1, #token: 820, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.30, #queue-req: 0, 
[2025-07-28 01:24:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1171, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.86, #queue-req: 0, 
[2025-07-28 01:24:57 DP1 TP0] Decode batch. #running-req: 1, #token: 860, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.96, #queue-req: 0, 
[2025-07-28 01:24:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1211, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.87, #queue-req: 0, 
[2025-07-28 01:24:57 DP1 TP0] Decode batch. #running-req: 1, #token: 900, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.26, #queue-req: 0, 
[2025-07-28 01:24:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.39, #queue-req: 0, 
[2025-07-28 01:24:57 DP1 TP0] Decode batch. #running-req: 1, #token: 940, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.38, #queue-req: 0, 
[2025-07-28 01:24:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.38, #queue-req: 0, 
[2025-07-28 01:24:57 DP1 TP0] Decode batch. #running-req: 1, #token: 980, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.38, #queue-req: 0, 
[2025-07-28 01:24:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.27, #queue-req: 0, 
[2025-07-28 01:24:58 DP1 TP0] Decode batch. #running-req: 1, #token: 1020, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.40, #queue-req: 0, 
[2025-07-28 01:24:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.37, #queue-req: 0, 
[2025-07-28 01:24:58 DP1 TP0] Decode batch. #running-req: 1, #token: 1060, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.77, #queue-req: 0, 
[2025-07-28 01:24:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.34, #queue-req: 0, 
[2025-07-28 01:24:58 DP1 TP0] Decode batch. #running-req: 1, #token: 1100, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.61, #queue-req: 0, 
[2025-07-28 01:24:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.52, #queue-req: 0, 
[2025-07-28 01:24:58 DP1 TP0] Decode batch. #running-req: 1, #token: 1140, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.72, #queue-req: 0, 
[2025-07-28 01:24:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.51, #queue-req: 0, 
[2025-07-28 01:24:58 DP1 TP0] Decode batch. #running-req: 1, #token: 1180, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.66, #queue-req: 0, 
[2025-07-28 01:24:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.11, #queue-req: 0, 
[2025-07-28 01:24:58 DP1 TP0] Decode batch. #running-req: 1, #token: 1220, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.95, #queue-req: 0, 
[2025-07-28 01:24:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.23, #queue-req: 0, 
[2025-07-28 01:24:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1260, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.89, #queue-req: 0, 
[2025-07-28 01:24:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.05, #queue-req: 0, 
[2025-07-28 01:24:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.81, #queue-req: 0, 
[2025-07-28 01:24:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.61, #queue-req: 0, 
[2025-07-28 01:24:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.61, #queue-req: 0, 
[2025-07-28 01:24:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.68, #queue-req: 0, 
[2025-07-28 01:24:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.64, #queue-req: 0, 
[2025-07-28 01:24:59] INFO:     127.0.0.1:46420 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:24:59 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 163, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:24:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.64, #queue-req: 0, 
[2025-07-28 01:24:59 DP0 TP0] Decode batch. #running-req: 1, #token: 280, token usage: 0.00, cuda graph: True, gen throughput (token/s): 171.62, #queue-req: 0, 
[2025-07-28 01:25:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.85, #queue-req: 0, 
[2025-07-28 01:25:00 DP0 TP0] Decode batch. #running-req: 1, #token: 320, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.37, #queue-req: 0, 
[2025-07-28 01:25:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.86, #queue-req: 0, 
[2025-07-28 01:25:00 DP0 TP0] Decode batch. #running-req: 1, #token: 360, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.86, #queue-req: 0, 
[2025-07-28 01:25:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.74, #queue-req: 0, 
[2025-07-28 01:25:00 DP0 TP0] Decode batch. #running-req: 1, #token: 400, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.11, #queue-req: 0, 
[2025-07-28 01:25:00] INFO:     127.0.0.1:46428 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:25:00 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 89, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:25:00 DP0 TP0] Decode batch. #running-req: 1, #token: 440, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.44, #queue-req: 0, 
[2025-07-28 01:25:00 DP1 TP0] Decode batch. #running-req: 1, #token: 187, token usage: 0.00, cuda graph: True, gen throughput (token/s): 174.36, #queue-req: 0, 
[2025-07-28 01:25:00 DP0 TP0] Decode batch. #running-req: 1, #token: 480, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.27, #queue-req: 0, 
[2025-07-28 01:25:00 DP1 TP0] Decode batch. #running-req: 1, #token: 227, token usage: 0.00, cuda graph: True, gen throughput (token/s): 218.22, #queue-req: 0, 
[2025-07-28 01:25:01 DP0 TP0] Decode batch. #running-req: 1, #token: 520, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.75, #queue-req: 0, 
[2025-07-28 01:25:01 DP1 TP0] Decode batch. #running-req: 1, #token: 267, token usage: 0.00, cuda graph: True, gen throughput (token/s): 215.87, #queue-req: 0, 
[2025-07-28 01:25:01 DP0 TP0] Decode batch. #running-req: 1, #token: 560, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.47, #queue-req: 0, 
[2025-07-28 01:25:01 DP1 TP0] Decode batch. #running-req: 1, #token: 307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.76, #queue-req: 0, 
[2025-07-28 01:25:01 DP0 TP0] Decode batch. #running-req: 1, #token: 600, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.36, #queue-req: 0, 
[2025-07-28 01:25:01 DP1 TP0] Decode batch. #running-req: 1, #token: 347, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.92, #queue-req: 0, 
[2025-07-28 01:25:01 DP0 TP0] Decode batch. #running-req: 1, #token: 640, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.81, #queue-req: 0, 
[2025-07-28 01:25:01 DP1 TP0] Decode batch. #running-req: 1, #token: 387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.77, #queue-req: 0, 
[2025-07-28 01:25:01 DP0 TP0] Decode batch. #running-req: 1, #token: 680, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.67, #queue-req: 0, 
[2025-07-28 01:25:01 DP1 TP0] Decode batch. #running-req: 1, #token: 427, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.46, #queue-req: 0, 
[2025-07-28 01:25:01 DP0 TP0] Decode batch. #running-req: 1, #token: 720, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.65, #queue-req: 0, 
[2025-07-28 01:25:01 DP1 TP0] Decode batch. #running-req: 1, #token: 467, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.91, #queue-req: 0, 
[2025-07-28 01:25:02 DP0 TP0] Decode batch. #running-req: 1, #token: 760, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.88, #queue-req: 0, 
[2025-07-28 01:25:02 DP1 TP0] Decode batch. #running-req: 1, #token: 507, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.95, #queue-req: 0, 
[2025-07-28 01:25:02 DP0 TP0] Decode batch. #running-req: 1, #token: 800, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.53, #queue-req: 0, 
[2025-07-28 01:25:02 DP1 TP0] Decode batch. #running-req: 1, #token: 547, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.46, #queue-req: 0, 
[2025-07-28 01:25:02 DP0 TP0] Decode batch. #running-req: 1, #token: 840, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.91, #queue-req: 0, 
[2025-07-28 01:25:02 DP1 TP0] Decode batch. #running-req: 1, #token: 587, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.45, #queue-req: 0, 
[2025-07-28 01:25:02 DP0 TP0] Decode batch. #running-req: 1, #token: 880, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.80, #queue-req: 0, 
[2025-07-28 01:25:02 DP1 TP0] Decode batch. #running-req: 1, #token: 627, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.36, #queue-req: 0, 
[2025-07-28 01:25:02 DP0 TP0] Decode batch. #running-req: 1, #token: 920, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.83, #queue-req: 0, 
[2025-07-28 01:25:02 DP1 TP0] Decode batch. #running-req: 1, #token: 667, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.30, #queue-req: 0, 
[2025-07-28 01:25:03 DP0 TP0] Decode batch. #running-req: 1, #token: 960, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.99, #queue-req: 0, 
[2025-07-28 01:25:03 DP1 TP0] Decode batch. #running-req: 1, #token: 707, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.54, #queue-req: 0, 
[2025-07-28 01:25:03 DP0 TP0] Decode batch. #running-req: 1, #token: 1000, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.70, #queue-req: 0, 
[2025-07-28 01:25:03 DP1 TP0] Decode batch. #running-req: 1, #token: 747, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.50, #queue-req: 0, 
[2025-07-28 01:25:03 DP0 TP0] Decode batch. #running-req: 1, #token: 1040, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.69, #queue-req: 0, 
[2025-07-28 01:25:03 DP1 TP0] Decode batch. #running-req: 1, #token: 787, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.39, #queue-req: 0, 
[2025-07-28 01:25:03 DP0 TP0] Decode batch. #running-req: 1, #token: 1080, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.20, #queue-req: 0, 
[2025-07-28 01:25:03 DP1 TP0] Decode batch. #running-req: 1, #token: 827, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.24, #queue-req: 0, 
[2025-07-28 01:25:03 DP1 TP0] Decode batch. #running-req: 1, #token: 867, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.34, #queue-req: 0, 
[2025-07-28 01:25:03 DP0 TP0] Decode batch. #running-req: 1, #token: 1120, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.09, #queue-req: 0, 
[2025-07-28 01:25:03] INFO:     127.0.0.1:46434 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:25:03 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 111, #cached-token: 84, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:25:04 DP0 TP0] Decode batch. #running-req: 2, #token: 1299, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.63, #queue-req: 0, 
[2025-07-28 01:25:04 DP0 TP0] Decode batch. #running-req: 2, #token: 1379, token usage: 0.00, cuda graph: True, gen throughput (token/s): 410.31, #queue-req: 0, 
[2025-07-28 01:25:04] INFO:     127.0.0.1:46430 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:25:04 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 142, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:25:04 DP0 TP0] Decode batch. #running-req: 1, #token: 303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 249.87, #queue-req: 0, 
[2025-07-28 01:25:04 DP1 TP0] Decode batch. #running-req: 1, #token: 259, token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.43, #queue-req: 0, 
[2025-07-28 01:25:04 DP0 TP0] Decode batch. #running-req: 1, #token: 343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.13, #queue-req: 0, 
[2025-07-28 01:25:04 DP1 TP0] Decode batch. #running-req: 1, #token: 299, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.87, #queue-req: 0, 
[2025-07-28 01:25:04 DP0 TP0] Decode batch. #running-req: 1, #token: 383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.07, #queue-req: 0, 
[2025-07-28 01:25:04 DP1 TP0] Decode batch. #running-req: 1, #token: 339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.57, #queue-req: 0, 
[2025-07-28 01:25:05 DP0 TP0] Decode batch. #running-req: 1, #token: 423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.10, #queue-req: 0, 
[2025-07-28 01:25:05 DP1 TP0] Decode batch. #running-req: 1, #token: 379, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.01, #queue-req: 0, 
[2025-07-28 01:25:05 DP0 TP0] Decode batch. #running-req: 1, #token: 463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.87, #queue-req: 0, 
[2025-07-28 01:25:05 DP1 TP0] Decode batch. #running-req: 1, #token: 419, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.32, #queue-req: 0, 
[2025-07-28 01:25:05 DP0 TP0] Decode batch. #running-req: 1, #token: 503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.15, #queue-req: 0, 
[2025-07-28 01:25:05 DP1 TP0] Decode batch. #running-req: 1, #token: 459, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.66, #queue-req: 0, 
[2025-07-28 01:25:05 DP0 TP0] Decode batch. #running-req: 1, #token: 543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.99, #queue-req: 0, 
[2025-07-28 01:25:05 DP1 TP0] Decode batch. #running-req: 1, #token: 499, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.81, #queue-req: 0, 
[2025-07-28 01:25:05 DP0 TP0] Decode batch. #running-req: 1, #token: 583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.94, #queue-req: 0, 
[2025-07-28 01:25:05 DP1 TP0] Decode batch. #running-req: 1, #token: 539, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.47, #queue-req: 0, 
[2025-07-28 01:25:05 DP0 TP0] Decode batch. #running-req: 1, #token: 623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.90, #queue-req: 0, 
[2025-07-28 01:25:06 DP1 TP0] Decode batch. #running-req: 1, #token: 579, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.37, #queue-req: 0, 
[2025-07-28 01:25:06 DP0 TP0] Decode batch. #running-req: 1, #token: 663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.88, #queue-req: 0, 
[2025-07-28 01:25:06 DP1 TP0] Decode batch. #running-req: 1, #token: 619, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.30, #queue-req: 0, 
[2025-07-28 01:25:06 DP0 TP0] Decode batch. #running-req: 1, #token: 703, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.98, #queue-req: 0, 
[2025-07-28 01:25:06 DP1 TP0] Decode batch. #running-req: 1, #token: 659, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.29, #queue-req: 0, 
[2025-07-28 01:25:06 DP0 TP0] Decode batch. #running-req: 1, #token: 743, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.99, #queue-req: 0, 
[2025-07-28 01:25:06 DP1 TP0] Decode batch. #running-req: 1, #token: 699, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.47, #queue-req: 0, 
[2025-07-28 01:25:06 DP0 TP0] Decode batch. #running-req: 1, #token: 783, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.90, #queue-req: 0, 
[2025-07-28 01:25:06 DP1 TP0] Decode batch. #running-req: 1, #token: 739, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.41, #queue-req: 0, 
[2025-07-28 01:25:06 DP0 TP0] Decode batch. #running-req: 1, #token: 823, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.79, #queue-req: 0, 
[2025-07-28 01:25:06 DP1 TP0] Decode batch. #running-req: 1, #token: 779, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.36, #queue-req: 0, 
[2025-07-28 01:25:07 DP0 TP0] Decode batch. #running-req: 1, #token: 863, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.78, #queue-req: 0, 
[2025-07-28 01:25:07] INFO:     127.0.0.1:41420 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:25:07 DP1 TP0] Decode batch. #running-req: 1, #token: 819, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.23, #queue-req: 0, 
[2025-07-28 01:25:07 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 138, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:25:07 DP0 TP0] Decode batch. #running-req: 1, #token: 255, token usage: 0.00, cuda graph: True, gen throughput (token/s): 174.84, #queue-req: 0, 
[2025-07-28 01:25:07 DP1 TP0] Decode batch. #running-req: 1, #token: 859, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.25, #queue-req: 0, 
[2025-07-28 01:25:07 DP0 TP0] Decode batch. #running-req: 1, #token: 295, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.00, #queue-req: 0, 
[2025-07-28 01:25:07 DP1 TP0] Decode batch. #running-req: 1, #token: 899, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.13, #queue-req: 0, 
[2025-07-28 01:25:07 DP0 TP0] Decode batch. #running-req: 1, #token: 335, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.51, #queue-req: 0, 
[2025-07-28 01:25:07 DP1 TP0] Decode batch. #running-req: 1, #token: 939, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.15, #queue-req: 0, 
[2025-07-28 01:25:07 DP0 TP0] Decode batch. #running-req: 1, #token: 375, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.34, #queue-req: 0, 
[2025-07-28 01:25:07 DP1 TP0] Decode batch. #running-req: 1, #token: 979, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.25, #queue-req: 0, 
[2025-07-28 01:25:08 DP0 TP0] Decode batch. #running-req: 1, #token: 415, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.86, #queue-req: 0, 
[2025-07-28 01:25:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1019, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.42, #queue-req: 0, 
[2025-07-28 01:25:08 DP0 TP0] Decode batch. #running-req: 1, #token: 455, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.95, #queue-req: 0, 
[2025-07-28 01:25:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1059, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.68, #queue-req: 0, 
[2025-07-28 01:25:08 DP0 TP0] Decode batch. #running-req: 1, #token: 495, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.02, #queue-req: 0, 
[2025-07-28 01:25:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1099, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.67, #queue-req: 0, 
[2025-07-28 01:25:08 DP0 TP0] Decode batch. #running-req: 1, #token: 535, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.89, #queue-req: 0, 
[2025-07-28 01:25:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1139, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.69, #queue-req: 0, 
[2025-07-28 01:25:08 DP0 TP0] Decode batch. #running-req: 1, #token: 575, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.80, #queue-req: 0, 
[2025-07-28 01:25:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1179, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.88, #queue-req: 0, 
[2025-07-28 01:25:08 DP0 TP0] Decode batch. #running-req: 1, #token: 615, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.85, #queue-req: 0, 
[2025-07-28 01:25:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1219, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.90, #queue-req: 0, 
[2025-07-28 01:25:09 DP0 TP0] Decode batch. #running-req: 1, #token: 655, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.80, #queue-req: 0, 
[2025-07-28 01:25:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1259, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.86, #queue-req: 0, 
[2025-07-28 01:25:09 DP0 TP0] Decode batch. #running-req: 1, #token: 695, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.83, #queue-req: 0, 
[2025-07-28 01:25:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1299, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.80, #queue-req: 0, 
[2025-07-28 01:25:09 DP0 TP0] Decode batch. #running-req: 1, #token: 735, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.87, #queue-req: 0, 
[2025-07-28 01:25:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.49, #queue-req: 0, 
[2025-07-28 01:25:09] INFO:     127.0.0.1:41426 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:25:09 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 147, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:25:09 DP0 TP0] Decode batch. #running-req: 1, #token: 775, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.91, #queue-req: 0, 
[2025-07-28 01:25:09 DP1 TP0] Decode batch. #running-req: 1, #token: 272, token usage: 0.00, cuda graph: True, gen throughput (token/s): 175.20, #queue-req: 0, 
[2025-07-28 01:25:09 DP0 TP0] Decode batch. #running-req: 1, #token: 815, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.65, #queue-req: 0, 
[2025-07-28 01:25:09 DP1 TP0] Decode batch. #running-req: 1, #token: 312, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.58, #queue-req: 0, 
[2025-07-28 01:25:10 DP0 TP0] Decode batch. #running-req: 1, #token: 855, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.70, #queue-req: 0, 
[2025-07-28 01:25:10 DP1 TP0] Decode batch. #running-req: 1, #token: 352, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.66, #queue-req: 0, 
[2025-07-28 01:25:10 DP0 TP0] Decode batch. #running-req: 1, #token: 895, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.65, #queue-req: 0, 
[2025-07-28 01:25:10 DP1 TP0] Decode batch. #running-req: 1, #token: 392, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.69, #queue-req: 0, 
[2025-07-28 01:25:10 DP0 TP0] Decode batch. #running-req: 1, #token: 935, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.83, #queue-req: 0, 
[2025-07-28 01:25:10 DP1 TP0] Decode batch. #running-req: 1, #token: 432, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.94, #queue-req: 0, 
[2025-07-28 01:25:10 DP0 TP0] Decode batch. #running-req: 1, #token: 975, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.77, #queue-req: 0, 
[2025-07-28 01:25:10 DP1 TP0] Decode batch. #running-req: 1, #token: 472, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.71, #queue-req: 0, 
[2025-07-28 01:25:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1015, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.81, #queue-req: 0, 
[2025-07-28 01:25:10 DP1 TP0] Decode batch. #running-req: 1, #token: 512, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.73, #queue-req: 0, 
[2025-07-28 01:25:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1055, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.21, #queue-req: 0, 
[2025-07-28 01:25:11 DP1 TP0] Decode batch. #running-req: 1, #token: 552, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.41, #queue-req: 0, 
[2025-07-28 01:25:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1095, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.24, #queue-req: 0, 
[2025-07-28 01:25:11 DP1 TP0] Decode batch. #running-req: 1, #token: 592, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.43, #queue-req: 0, 
[2025-07-28 01:25:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1135, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.97, #queue-req: 0, 
[2025-07-28 01:25:11 DP1 TP0] Decode batch. #running-req: 1, #token: 632, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.32, #queue-req: 0, 
[2025-07-28 01:25:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1175, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.26, #queue-req: 0, 
[2025-07-28 01:25:11 DP1 TP0] Decode batch. #running-req: 1, #token: 672, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.23, #queue-req: 0, 
[2025-07-28 01:25:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1215, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.08, #queue-req: 0, 
[2025-07-28 01:25:11 DP1 TP0] Decode batch. #running-req: 1, #token: 712, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.19, #queue-req: 0, 
[2025-07-28 01:25:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1255, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.97, #queue-req: 0, 
[2025-07-28 01:25:12 DP1 TP0] Decode batch. #running-req: 1, #token: 752, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.54, #queue-req: 0, 
[2025-07-28 01:25:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1295, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.21, #queue-req: 0, 
[2025-07-28 01:25:12 DP1 TP0] Decode batch. #running-req: 1, #token: 792, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.33, #queue-req: 0, 
[2025-07-28 01:25:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1335, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.10, #queue-req: 0, 
[2025-07-28 01:25:12 DP1 TP0] Decode batch. #running-req: 1, #token: 832, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.17, #queue-req: 0, 
[2025-07-28 01:25:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1375, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.18, #queue-req: 0, 
[2025-07-28 01:25:12 DP1 TP0] Decode batch. #running-req: 1, #token: 872, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.11, #queue-req: 0, 
[2025-07-28 01:25:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1415, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.10, #queue-req: 0, 
[2025-07-28 01:25:12 DP1 TP0] Decode batch. #running-req: 1, #token: 912, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.32, #queue-req: 0, 
[2025-07-28 01:25:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1455, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.20, #queue-req: 0, 
[2025-07-28 01:25:12 DP1 TP0] Decode batch. #running-req: 1, #token: 952, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.42, #queue-req: 0, 
[2025-07-28 01:25:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1495, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.31, #queue-req: 0, 
[2025-07-28 01:25:13 DP1 TP0] Decode batch. #running-req: 1, #token: 992, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.31, #queue-req: 0, 
[2025-07-28 01:25:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1535, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.26, #queue-req: 0, 
[2025-07-28 01:25:13 DP1 TP0] Decode batch. #running-req: 1, #token: 1032, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.28, #queue-req: 0, 
[2025-07-28 01:25:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1575, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.92, #queue-req: 0, 
[2025-07-28 01:25:13 DP1 TP0] Decode batch. #running-req: 1, #token: 1072, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.64, #queue-req: 0, 
[2025-07-28 01:25:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1615, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.02, #queue-req: 0, 
[2025-07-28 01:25:13 DP1 TP0] Decode batch. #running-req: 1, #token: 1112, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.66, #queue-req: 0, 
[2025-07-28 01:25:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1655, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.03, #queue-req: 0, 
[2025-07-28 01:25:13 DP1 TP0] Decode batch. #running-req: 1, #token: 1152, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.67, #queue-req: 0, 
[2025-07-28 01:25:13] INFO:     127.0.0.1:41428 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:25:13 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 111, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:25:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1192, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.87, #queue-req: 0, 
[2025-07-28 01:25:14 DP0 TP0] Decode batch. #running-req: 1, #token: 224, token usage: 0.00, cuda graph: True, gen throughput (token/s): 174.55, #queue-req: 0, 
[2025-07-28 01:25:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1232, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.77, #queue-req: 0, 
[2025-07-28 01:25:14 DP0 TP0] Decode batch. #running-req: 1, #token: 264, token usage: 0.00, cuda graph: True, gen throughput (token/s): 215.32, #queue-req: 0, 
[2025-07-28 01:25:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1272, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.91, #queue-req: 0, 
[2025-07-28 01:25:14 DP0 TP0] Decode batch. #running-req: 1, #token: 304, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.08, #queue-req: 0, 
[2025-07-28 01:25:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1312, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.63, #queue-req: 0, 
[2025-07-28 01:25:14 DP0 TP0] Decode batch. #running-req: 1, #token: 344, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.04, #queue-req: 0, 
[2025-07-28 01:25:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1352, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.63, #queue-req: 0, 
[2025-07-28 01:25:14 DP0 TP0] Decode batch. #running-req: 1, #token: 384, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.00, #queue-req: 0, 
[2025-07-28 01:25:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1392, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.60, #queue-req: 0, 
[2025-07-28 01:25:15 DP0 TP0] Decode batch. #running-req: 1, #token: 424, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.22, #queue-req: 0, 
[2025-07-28 01:25:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1432, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.80, #queue-req: 0, 
[2025-07-28 01:25:15 DP0 TP0] Decode batch. #running-req: 1, #token: 464, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.25, #queue-req: 0, 
[2025-07-28 01:25:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1472, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.69, #queue-req: 0, 
[2025-07-28 01:25:15 DP0 TP0] Decode batch. #running-req: 1, #token: 504, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.13, #queue-req: 0, 
[2025-07-28 01:25:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1512, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.62, #queue-req: 0, 
[2025-07-28 01:25:15 DP0 TP0] Decode batch. #running-req: 1, #token: 544, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.84, #queue-req: 0, 
[2025-07-28 01:25:15] INFO:     127.0.0.1:41444 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:25:15 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 76, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:25:15 DP0 TP0] Decode batch. #running-req: 1, #token: 584, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.80, #queue-req: 0, 
[2025-07-28 01:25:15 DP1 TP0] Decode batch. #running-req: 1, #token: 164, token usage: 0.00, cuda graph: True, gen throughput (token/s): 174.34, #queue-req: 0, 
[2025-07-28 01:25:15 DP0 TP0] Decode batch. #running-req: 1, #token: 624, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.77, #queue-req: 0, 
[2025-07-28 01:25:15 DP1 TP0] Decode batch. #running-req: 1, #token: 204, token usage: 0.00, cuda graph: True, gen throughput (token/s): 219.39, #queue-req: 0, 
[2025-07-28 01:25:16 DP0 TP0] Decode batch. #running-req: 1, #token: 664, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.94, #queue-req: 0, 
[2025-07-28 01:25:16 DP1 TP0] Decode batch. #running-req: 1, #token: 244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 217.43, #queue-req: 0, 
[2025-07-28 01:25:16 DP0 TP0] Decode batch. #running-req: 1, #token: 704, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.89, #queue-req: 0, 
[2025-07-28 01:25:16 DP1 TP0] Decode batch. #running-req: 1, #token: 284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.91, #queue-req: 0, 
[2025-07-28 01:25:16 DP0 TP0] Decode batch. #running-req: 1, #token: 744, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.00, #queue-req: 0, 
[2025-07-28 01:25:16 DP1 TP0] Decode batch. #running-req: 1, #token: 324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.67, #queue-req: 0, 
[2025-07-28 01:25:16 DP0 TP0] Decode batch. #running-req: 1, #token: 784, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.85, #queue-req: 0, 
[2025-07-28 01:25:16 DP1 TP0] Decode batch. #running-req: 1, #token: 364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.79, #queue-req: 0, 
[2025-07-28 01:25:16 DP0 TP0] Decode batch. #running-req: 1, #token: 824, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.60, #queue-req: 0, 
[2025-07-28 01:25:16 DP1 TP0] Decode batch. #running-req: 1, #token: 404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.77, #queue-req: 0, 
[2025-07-28 01:25:17 DP0 TP0] Decode batch. #running-req: 1, #token: 864, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.71, #queue-req: 0, 
[2025-07-28 01:25:17 DP1 TP0] Decode batch. #running-req: 1, #token: 444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.90, #queue-req: 0, 
[2025-07-28 01:25:17 DP0 TP0] Decode batch. #running-req: 1, #token: 904, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.67, #queue-req: 0, 
[2025-07-28 01:25:17 DP1 TP0] Decode batch. #running-req: 1, #token: 484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.82, #queue-req: 0, 
[2025-07-28 01:25:17 DP0 TP0] Decode batch. #running-req: 1, #token: 944, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.87, #queue-req: 0, 
[2025-07-28 01:25:17 DP1 TP0] Decode batch. #running-req: 1, #token: 524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.67, #queue-req: 0, 
[2025-07-28 01:25:17 DP0 TP0] Decode batch. #running-req: 1, #token: 984, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.74, #queue-req: 0, 
[2025-07-28 01:25:17 DP1 TP0] Decode batch. #running-req: 1, #token: 564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.56, #queue-req: 0, 
[2025-07-28 01:25:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1024, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.77, #queue-req: 0, 
[2025-07-28 01:25:17 DP1 TP0] Decode batch. #running-req: 1, #token: 604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.44, #queue-req: 0, 
[2025-07-28 01:25:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1064, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.80, #queue-req: 0, 
[2025-07-28 01:25:18 DP1 TP0] Decode batch. #running-req: 1, #token: 644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.51, #queue-req: 0, 
[2025-07-28 01:25:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1104, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.10, #queue-req: 0, 
[2025-07-28 01:25:18 DP1 TP0] Decode batch. #running-req: 1, #token: 684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.56, #queue-req: 0, 
[2025-07-28 01:25:18] INFO:     127.0.0.1:60006 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:25:18 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:25:18 DP1 TP0] Decode batch. #running-req: 1, #token: 724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.64, #queue-req: 0, 
[2025-07-28 01:25:18 DP0 TP0] Decode batch. #running-req: 1, #token: 217, token usage: 0.00, cuda graph: True, gen throughput (token/s): 165.74, #queue-req: 0, 
[2025-07-28 01:25:18 DP1 TP0] Decode batch. #running-req: 1, #token: 764, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.48, #queue-req: 0, 
[2025-07-28 01:25:18 DP0 TP0] Decode batch. #running-req: 1, #token: 257, token usage: 0.00, cuda graph: True, gen throughput (token/s): 215.13, #queue-req: 0, 
[2025-07-28 01:25:18 DP1 TP0] Decode batch. #running-req: 1, #token: 804, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.46, #queue-req: 0, 
[2025-07-28 01:25:18 DP0 TP0] Decode batch. #running-req: 1, #token: 297, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.67, #queue-req: 0, 
[2025-07-28 01:25:18 DP1 TP0] Decode batch. #running-req: 1, #token: 844, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.26, #queue-req: 0, 
[2025-07-28 01:25:19 DP0 TP0] Decode batch. #running-req: 1, #token: 337, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.47, #queue-req: 0, 
[2025-07-28 01:25:19] INFO:     127.0.0.1:60016 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:25:19 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:25:19 DP0 TP0] Decode batch. #running-req: 1, #token: 377, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.55, #queue-req: 0, 
[2025-07-28 01:25:19 DP1 TP0] Decode batch. #running-req: 1, #token: 244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 172.04, #queue-req: 0, 
[2025-07-28 01:25:19 DP0 TP0] Decode batch. #running-req: 1, #token: 417, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.89, #queue-req: 0, 
[2025-07-28 01:25:19 DP1 TP0] Decode batch. #running-req: 1, #token: 284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.97, #queue-req: 0, 
[2025-07-28 01:25:19 DP0 TP0] Decode batch. #running-req: 1, #token: 457, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.93, #queue-req: 0, 
[2025-07-28 01:25:19 DP1 TP0] Decode batch. #running-req: 1, #token: 324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.66, #queue-req: 0, 
[2025-07-28 01:25:19 DP0 TP0] Decode batch. #running-req: 1, #token: 497, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.13, #queue-req: 0, 
[2025-07-28 01:25:19 DP1 TP0] Decode batch. #running-req: 1, #token: 364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.89, #queue-req: 0, 
[2025-07-28 01:25:19 DP0 TP0] Decode batch. #running-req: 1, #token: 537, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.66, #queue-req: 0, 
[2025-07-28 01:25:19 DP1 TP0] Decode batch. #running-req: 1, #token: 404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.75, #queue-req: 0, 
[2025-07-28 01:25:20 DP0 TP0] Decode batch. #running-req: 1, #token: 577, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.58, #queue-req: 0, 
[2025-07-28 01:25:20 DP1 TP0] Decode batch. #running-req: 1, #token: 444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.96, #queue-req: 0, 
[2025-07-28 01:25:20 DP0 TP0] Decode batch. #running-req: 1, #token: 617, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.61, #queue-req: 0, 
[2025-07-28 01:25:20 DP1 TP0] Decode batch. #running-req: 1, #token: 484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.82, #queue-req: 0, 
[2025-07-28 01:25:20 DP1 TP0] Decode batch. #running-req: 1, #token: 524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.71, #queue-req: 0, 
[2025-07-28 01:25:20 DP0 TP0] Decode batch. #running-req: 1, #token: 657, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.60, #queue-req: 0, 
[2025-07-28 01:25:20 DP1 TP0] Decode batch. #running-req: 1, #token: 564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.37, #queue-req: 0, 
[2025-07-28 01:25:20 DP0 TP0] Decode batch. #running-req: 1, #token: 697, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.57, #queue-req: 0, 
[2025-07-28 01:25:20 DP1 TP0] Decode batch. #running-req: 1, #token: 604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.16, #queue-req: 0, 
[2025-07-28 01:25:20 DP0 TP0] Decode batch. #running-req: 1, #token: 737, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.77, #queue-req: 0, 
[2025-07-28 01:25:21 DP1 TP0] Decode batch. #running-req: 1, #token: 644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.33, #queue-req: 0, 
[2025-07-28 01:25:21 DP0 TP0] Decode batch. #running-req: 1, #token: 777, token usage: 0.00, cuda graph: True, gen throughput (token/s): 206.73, #queue-req: 0, 
[2025-07-28 01:25:21 DP1 TP0] Decode batch. #running-req: 1, #token: 684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.49, #queue-req: 0, 
[2025-07-28 01:25:21 DP0 TP0] Decode batch. #running-req: 1, #token: 817, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.17, #queue-req: 0, 
[2025-07-28 01:25:21 DP1 TP0] Decode batch. #running-req: 1, #token: 724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.42, #queue-req: 0, 
[2025-07-28 01:25:21 DP0 TP0] Decode batch. #running-req: 1, #token: 857, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.62, #queue-req: 0, 
[2025-07-28 01:25:21 DP1 TP0] Decode batch. #running-req: 1, #token: 764, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.08, #queue-req: 0, 
[2025-07-28 01:25:21 DP0 TP0] Decode batch. #running-req: 1, #token: 897, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.48, #queue-req: 0, 
[2025-07-28 01:25:21 DP1 TP0] Decode batch. #running-req: 1, #token: 804, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.32, #queue-req: 0, 
[2025-07-28 01:25:21 DP0 TP0] Decode batch. #running-req: 1, #token: 937, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.79, #queue-req: 0, 
[2025-07-28 01:25:22 DP1 TP0] Decode batch. #running-req: 1, #token: 844, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.00, #queue-req: 0, 
[2025-07-28 01:25:22 DP0 TP0] Decode batch. #running-req: 1, #token: 977, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.67, #queue-req: 0, 
[2025-07-28 01:25:22 DP1 TP0] Decode batch. #running-req: 1, #token: 884, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.11, #queue-req: 0, 
[2025-07-28 01:25:22 DP0 TP0] Decode batch. #running-req: 1, #token: 1017, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.76, #queue-req: 0, 
[2025-07-28 01:25:22] INFO:     127.0.0.1:60020 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:25:22 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 81, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:25:22 DP1 TP0] Decode batch. #running-req: 1, #token: 924, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.12, #queue-req: 0, 
[2025-07-28 01:25:22] INFO:     127.0.0.1:60032 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:25:22 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 151, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:25:22 DP0 TP0] Decode batch. #running-req: 1, #token: 182, token usage: 0.00, cuda graph: True, gen throughput (token/s): 174.17, #queue-req: 0, 
[2025-07-28 01:25:22 DP1 TP0] Decode batch. #running-req: 1, #token: 272, token usage: 0.00, cuda graph: True, gen throughput (token/s): 176.78, #queue-req: 0, 
[2025-07-28 01:25:22 DP0 TP0] Decode batch. #running-req: 1, #token: 222, token usage: 0.00, cuda graph: True, gen throughput (token/s): 217.69, #queue-req: 0, 
[2025-07-28 01:25:22 DP1 TP0] Decode batch. #running-req: 1, #token: 312, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.70, #queue-req: 0, 
[2025-07-28 01:25:22 DP0 TP0] Decode batch. #running-req: 1, #token: 262, token usage: 0.00, cuda graph: True, gen throughput (token/s): 215.24, #queue-req: 0, 
[2025-07-28 01:25:22 DP1 TP0] Decode batch. #running-req: 1, #token: 352, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.77, #queue-req: 0, 
[2025-07-28 01:25:22 DP0 TP0] Decode batch. #running-req: 1, #token: 302, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.95, #queue-req: 0, 
[2025-07-28 01:25:23 DP1 TP0] Decode batch. #running-req: 1, #token: 392, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.79, #queue-req: 0, 
[2025-07-28 01:25:23 DP0 TP0] Decode batch. #running-req: 1, #token: 342, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.05, #queue-req: 0, 
[2025-07-28 01:25:23 DP1 TP0] Decode batch. #running-req: 1, #token: 432, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.95, #queue-req: 0, 
[2025-07-28 01:25:23 DP0 TP0] Decode batch. #running-req: 1, #token: 382, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.81, #queue-req: 0, 
[2025-07-28 01:25:23 DP1 TP0] Decode batch. #running-req: 1, #token: 472, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.93, #queue-req: 0, 
[2025-07-28 01:25:23 DP0 TP0] Decode batch. #running-req: 1, #token: 422, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.22, #queue-req: 0, 
[2025-07-28 01:25:23 DP1 TP0] Decode batch. #running-req: 1, #token: 512, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.93, #queue-req: 0, 
[2025-07-28 01:25:23 DP0 TP0] Decode batch. #running-req: 1, #token: 462, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.20, #queue-req: 0, 
[2025-07-28 01:25:23 DP1 TP0] Decode batch. #running-req: 1, #token: 552, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.35, #queue-req: 0, 
[2025-07-28 01:25:23 DP0 TP0] Decode batch. #running-req: 1, #token: 502, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.16, #queue-req: 0, 
[2025-07-28 01:25:24 DP1 TP0] Decode batch. #running-req: 1, #token: 592, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.41, #queue-req: 0, 
[2025-07-28 01:25:24 DP0 TP0] Decode batch. #running-req: 1, #token: 542, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.05, #queue-req: 0, 
[2025-07-28 01:25:24 DP1 TP0] Decode batch. #running-req: 1, #token: 632, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.46, #queue-req: 0, 
[2025-07-28 01:25:24 DP0 TP0] Decode batch. #running-req: 1, #token: 582, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.85, #queue-req: 0, 
[2025-07-28 01:25:24 DP1 TP0] Decode batch. #running-req: 1, #token: 672, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.52, #queue-req: 0, 
[2025-07-28 01:25:24 DP0 TP0] Decode batch. #running-req: 1, #token: 622, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.84, #queue-req: 0, 
[2025-07-28 01:25:24] INFO:     127.0.0.1:39644 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:25:24 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 142, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:25:24 DP1 TP0] Decode batch. #running-req: 1, #token: 712, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.60, #queue-req: 0, 
[2025-07-28 01:25:24 DP0 TP0] Decode batch. #running-req: 1, #token: 265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 175.21, #queue-req: 0, 
[2025-07-28 01:25:24 DP1 TP0] Decode batch. #running-req: 1, #token: 752, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.58, #queue-req: 0, 
[2025-07-28 01:25:24 DP0 TP0] Decode batch. #running-req: 1, #token: 305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.93, #queue-req: 0, 
[2025-07-28 01:25:25 DP1 TP0] Decode batch. #running-req: 1, #token: 792, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.31, #queue-req: 0, 
[2025-07-28 01:25:25 DP0 TP0] Decode batch. #running-req: 1, #token: 345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.06, #queue-req: 0, 
[2025-07-28 01:25:25 DP1 TP0] Decode batch. #running-req: 1, #token: 832, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.27, #queue-req: 0, 
[2025-07-28 01:25:25 DP0 TP0] Decode batch. #running-req: 1, #token: 385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.92, #queue-req: 0, 
[2025-07-28 01:25:25 DP1 TP0] Decode batch. #running-req: 1, #token: 872, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.17, #queue-req: 0, 
[2025-07-28 01:25:25 DP0 TP0] Decode batch. #running-req: 1, #token: 425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.31, #queue-req: 0, 
[2025-07-28 01:25:25 DP1 TP0] Decode batch. #running-req: 1, #token: 912, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.33, #queue-req: 0, 
[2025-07-28 01:25:25 DP0 TP0] Decode batch. #running-req: 1, #token: 465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.29, #queue-req: 0, 
[2025-07-28 01:25:25 DP1 TP0] Decode batch. #running-req: 1, #token: 952, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.50, #queue-req: 0, 
[2025-07-28 01:25:25 DP0 TP0] Decode batch. #running-req: 1, #token: 505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.38, #queue-req: 0, 
[2025-07-28 01:25:25 DP1 TP0] Decode batch. #running-req: 1, #token: 992, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.44, #queue-req: 0, 
[2025-07-28 01:25:26 DP0 TP0] Decode batch. #running-req: 1, #token: 545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.99, #queue-req: 0, 
[2025-07-28 01:25:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1032, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.30, #queue-req: 0, 
[2025-07-28 01:25:26 DP0 TP0] Decode batch. #running-req: 1, #token: 585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.01, #queue-req: 0, 
[2025-07-28 01:25:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1072, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.69, #queue-req: 0, 
[2025-07-28 01:25:26 DP0 TP0] Decode batch. #running-req: 1, #token: 625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.93, #queue-req: 0, 
[2025-07-28 01:25:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1112, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.67, #queue-req: 0, 
[2025-07-28 01:25:26 DP0 TP0] Decode batch. #running-req: 1, #token: 665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.03, #queue-req: 0, 
[2025-07-28 01:25:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1152, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.75, #queue-req: 0, 
[2025-07-28 01:25:26 DP0 TP0] Decode batch. #running-req: 1, #token: 705, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.12, #queue-req: 0, 
[2025-07-28 01:25:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1192, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.86, #queue-req: 0, 
[2025-07-28 01:25:26 DP0 TP0] Decode batch. #running-req: 1, #token: 745, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.05, #queue-req: 0, 
[2025-07-28 01:25:27] INFO:     127.0.0.1:39652 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:25:27 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 107, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:25:27 DP1 TP0] Decode batch. #running-req: 1, #token: 197, token usage: 0.00, cuda graph: True, gen throughput (token/s): 174.72, #queue-req: 0, 
[2025-07-28 01:25:27 DP0 TP0] Decode batch. #running-req: 1, #token: 785, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.08, #queue-req: 0, 
[2025-07-28 01:25:27 DP1 TP0] Decode batch. #running-req: 1, #token: 237, token usage: 0.00, cuda graph: True, gen throughput (token/s): 217.68, #queue-req: 0, 
[2025-07-28 01:25:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.01, #queue-req: 0, 
[2025-07-28 01:25:27] INFO:     127.0.0.1:39654 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:25:27 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 91, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:25:27 DP1 TP0] Decode batch. #running-req: 1, #token: 277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 215.30, #queue-req: 0, 
[2025-07-28 01:25:27 DP0 TP0] Decode batch. #running-req: 1, #token: 215, token usage: 0.00, cuda graph: True, gen throughput (token/s): 180.36, #queue-req: 0, 
[2025-07-28 01:25:27 DP1 TP0] Decode batch. #running-req: 1, #token: 317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.81, #queue-req: 0, 
[2025-07-28 01:25:27 DP0 TP0] Decode batch. #running-req: 1, #token: 255, token usage: 0.00, cuda graph: True, gen throughput (token/s): 216.07, #queue-req: 0, 
[2025-07-28 01:25:27 DP1 TP0] Decode batch. #running-req: 1, #token: 357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.88, #queue-req: 0, 
[2025-07-28 01:25:27 DP0 TP0] Decode batch. #running-req: 1, #token: 295, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.09, #queue-req: 0, 
[2025-07-28 01:25:28 DP1 TP0] Decode batch. #running-req: 1, #token: 397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.87, #queue-req: 0, 
[2025-07-28 01:25:28 DP0 TP0] Decode batch. #running-req: 1, #token: 335, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.12, #queue-req: 0, 
[2025-07-28 01:25:28 DP1 TP0] Decode batch. #running-req: 1, #token: 437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.81, #queue-req: 0, 
[2025-07-28 01:25:28 DP0 TP0] Decode batch. #running-req: 1, #token: 375, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.84, #queue-req: 0, 
[2025-07-28 01:25:28 DP1 TP0] Decode batch. #running-req: 1, #token: 477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.95, #queue-req: 0, 
[2025-07-28 01:25:28 DP0 TP0] Decode batch. #running-req: 1, #token: 415, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.27, #queue-req: 0, 
[2025-07-28 01:25:28 DP1 TP0] Decode batch. #running-req: 1, #token: 517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.69, #queue-req: 0, 
[2025-07-28 01:25:28 DP0 TP0] Decode batch. #running-req: 1, #token: 455, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.25, #queue-req: 0, 
[2025-07-28 01:25:28 DP1 TP0] Decode batch. #running-req: 1, #token: 557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.31, #queue-req: 0, 
[2025-07-28 01:25:28 DP0 TP0] Decode batch. #running-req: 1, #token: 495, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.24, #queue-req: 0, 
[2025-07-28 01:25:28 DP1 TP0] Decode batch. #running-req: 1, #token: 597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.44, #queue-req: 0, 
[2025-07-28 01:25:29 DP0 TP0] Decode batch. #running-req: 1, #token: 535, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.94, #queue-req: 0, 
[2025-07-28 01:25:29 DP1 TP0] Decode batch. #running-req: 1, #token: 637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.44, #queue-req: 0, 
[2025-07-28 01:25:29 DP0 TP0] Decode batch. #running-req: 1, #token: 575, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.66, #queue-req: 0, 
[2025-07-28 01:25:29 DP1 TP0] Decode batch. #running-req: 1, #token: 677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.58, #queue-req: 0, 
[2025-07-28 01:25:29 DP0 TP0] Decode batch. #running-req: 1, #token: 615, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.97, #queue-req: 0, 
[2025-07-28 01:25:29 DP1 TP0] Decode batch. #running-req: 1, #token: 717, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.46, #queue-req: 0, 
[2025-07-28 01:25:29 DP0 TP0] Decode batch. #running-req: 1, #token: 655, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.83, #queue-req: 0, 
[2025-07-28 01:25:29 DP1 TP0] Decode batch. #running-req: 1, #token: 757, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.57, #queue-req: 0, 
[2025-07-28 01:25:29 DP0 TP0] Decode batch. #running-req: 1, #token: 695, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.96, #queue-req: 0, 
[2025-07-28 01:25:29 DP1 TP0] Decode batch. #running-req: 1, #token: 797, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.18, #queue-req: 0, 
[2025-07-28 01:25:29 DP0 TP0] Decode batch. #running-req: 1, #token: 735, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.01, #queue-req: 0, 
[2025-07-28 01:25:30] INFO:     127.0.0.1:39668 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:25:30 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 85, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:25:30 DP1 TP0] Decode batch. #running-req: 1, #token: 172, token usage: 0.00, cuda graph: True, gen throughput (token/s): 175.05, #queue-req: 0, 
[2025-07-28 01:25:30 DP0 TP0] Decode batch. #running-req: 1, #token: 775, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.66, #queue-req: 0, 
[2025-07-28 01:25:30] INFO:     127.0.0.1:39672 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:25:30 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 109, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:25:30 DP1 TP0] Decode batch. #running-req: 1, #token: 212, token usage: 0.00, cuda graph: True, gen throughput (token/s): 219.12, #queue-req: 0, 
[2025-07-28 01:25:30 DP0 TP0] Decode batch. #running-req: 1, #token: 221, token usage: 0.00, cuda graph: True, gen throughput (token/s): 175.20, #queue-req: 0, 
[2025-07-28 01:25:30 DP1 TP0] Decode batch. #running-req: 1, #token: 252, token usage: 0.00, cuda graph: True, gen throughput (token/s): 216.88, #queue-req: 0, 
[2025-07-28 01:25:30 DP0 TP0] Decode batch. #running-req: 1, #token: 261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.83, #queue-req: 0, 
[2025-07-28 01:25:30 DP1 TP0] Decode batch. #running-req: 1, #token: 292, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.77, #queue-req: 0, 
[2025-07-28 01:25:30 DP0 TP0] Decode batch. #running-req: 1, #token: 301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.74, #queue-req: 0, 
[2025-07-28 01:25:30 DP1 TP0] Decode batch. #running-req: 1, #token: 332, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.61, #queue-req: 0, 
[2025-07-28 01:25:30 DP0 TP0] Decode batch. #running-req: 1, #token: 341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.78, #queue-req: 0, 
[2025-07-28 01:25:31 DP1 TP0] Decode batch. #running-req: 1, #token: 372, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.76, #queue-req: 0, 
[2025-07-28 01:25:31 DP0 TP0] Decode batch. #running-req: 1, #token: 381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.87, #queue-req: 0, 
[2025-07-28 01:25:31 DP1 TP0] Decode batch. #running-req: 1, #token: 412, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.85, #queue-req: 0, 
[2025-07-28 01:25:31 DP0 TP0] Decode batch. #running-req: 1, #token: 421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.02, #queue-req: 0, 
[2025-07-28 01:25:31 DP1 TP0] Decode batch. #running-req: 1, #token: 452, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.79, #queue-req: 0, 
[2025-07-28 01:25:31 DP0 TP0] Decode batch. #running-req: 1, #token: 461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.06, #queue-req: 0, 
[2025-07-28 01:25:31 DP1 TP0] Decode batch. #running-req: 1, #token: 492, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.86, #queue-req: 0, 
[2025-07-28 01:25:31 DP0 TP0] Decode batch. #running-req: 1, #token: 501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.02, #queue-req: 0, 
[2025-07-28 01:25:31 DP1 TP0] Decode batch. #running-req: 1, #token: 532, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.56, #queue-req: 0, 
[2025-07-28 01:25:31 DP0 TP0] Decode batch. #running-req: 1, #token: 541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.20, #queue-req: 0, 
[2025-07-28 01:25:32 DP1 TP0] Decode batch. #running-req: 1, #token: 572, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.48, #queue-req: 0, 
[2025-07-28 01:25:32 DP0 TP0] Decode batch. #running-req: 1, #token: 581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.71, #queue-req: 0, 
[2025-07-28 01:25:32 DP1 TP0] Decode batch. #running-req: 1, #token: 612, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.44, #queue-req: 0, 
[2025-07-28 01:25:32 DP0 TP0] Decode batch. #running-req: 1, #token: 621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.15, #queue-req: 0, 
[2025-07-28 01:25:32 DP1 TP0] Decode batch. #running-req: 1, #token: 652, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.36, #queue-req: 0, 
[2025-07-28 01:25:32 DP0 TP0] Decode batch. #running-req: 1, #token: 661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.21, #queue-req: 0, 
[2025-07-28 01:25:32 DP1 TP0] Decode batch. #running-req: 1, #token: 692, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.54, #queue-req: 0, 
[2025-07-28 01:25:32 DP0 TP0] Decode batch. #running-req: 1, #token: 701, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.24, #queue-req: 0, 
[2025-07-28 01:25:32 DP1 TP0] Decode batch. #running-req: 1, #token: 732, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.13, #queue-req: 0, 
[2025-07-28 01:25:32 DP0 TP0] Decode batch. #running-req: 1, #token: 741, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.47, #queue-req: 0, 
[2025-07-28 01:25:32 DP1 TP0] Decode batch. #running-req: 1, #token: 772, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.51, #queue-req: 0, 
[2025-07-28 01:25:33 DP0 TP0] Decode batch. #running-req: 1, #token: 781, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.98, #queue-req: 0, 
[2025-07-28 01:25:33 DP1 TP0] Decode batch. #running-req: 1, #token: 812, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.20, #queue-req: 0, 
[2025-07-28 01:25:33 DP0 TP0] Decode batch. #running-req: 1, #token: 821, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.75, #queue-req: 0, 
[2025-07-28 01:25:33 DP1 TP0] Decode batch. #running-req: 1, #token: 852, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.27, #queue-req: 0, 
[2025-07-28 01:25:33 DP0 TP0] Decode batch. #running-req: 1, #token: 861, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.69, #queue-req: 0, 
[2025-07-28 01:25:33 DP1 TP0] Decode batch. #running-req: 1, #token: 892, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.32, #queue-req: 0, 
[2025-07-28 01:25:33 DP0 TP0] Decode batch. #running-req: 1, #token: 901, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.04, #queue-req: 0, 
[2025-07-28 01:25:33 DP1 TP0] Decode batch. #running-req: 1, #token: 932, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.22, #queue-req: 0, 
[2025-07-28 01:25:33 DP0 TP0] Decode batch. #running-req: 1, #token: 941, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.90, #queue-req: 0, 
[2025-07-28 01:25:33] INFO:     127.0.0.1:39676 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:25:33 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 154, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:25:33 DP1 TP0] Decode batch. #running-req: 1, #token: 256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 179.36, #queue-req: 0, 
[2025-07-28 01:25:33 DP0 TP0] Decode batch. #running-req: 1, #token: 981, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.87, #queue-req: 0, 
[2025-07-28 01:25:34 DP1 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.88, #queue-req: 0, 
[2025-07-28 01:25:34 DP0 TP0] Decode batch. #running-req: 1, #token: 1021, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.86, #queue-req: 0, 
[2025-07-28 01:25:34 DP1 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.92, #queue-req: 0, 
[2025-07-28 01:25:34 DP0 TP0] Decode batch. #running-req: 1, #token: 1061, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.27, #queue-req: 0, 
[2025-07-28 01:25:34 DP1 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.90, #queue-req: 0, 
[2025-07-28 01:25:34 DP0 TP0] Decode batch. #running-req: 1, #token: 1101, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.08, #queue-req: 0, 
[2025-07-28 01:25:34 DP1 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.90, #queue-req: 0, 
[2025-07-28 01:25:34 DP0 TP0] Decode batch. #running-req: 1, #token: 1141, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.21, #queue-req: 0, 
[2025-07-28 01:25:34 DP1 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.93, #queue-req: 0, 
[2025-07-28 01:25:34 DP0 TP0] Decode batch. #running-req: 1, #token: 1181, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.41, #queue-req: 0, 
[2025-07-28 01:25:35 DP1 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 215.00, #queue-req: 0, 
[2025-07-28 01:25:35 DP0 TP0] Decode batch. #running-req: 1, #token: 1221, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.54, #queue-req: 0, 
[2025-07-28 01:25:35 DP1 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.70, #queue-req: 0, 
[2025-07-28 01:25:35 DP0 TP0] Decode batch. #running-req: 1, #token: 1261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.60, #queue-req: 0, 
[2025-07-28 01:25:35 DP1 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.40, #queue-req: 0, 
[2025-07-28 01:25:35 DP0 TP0] Decode batch. #running-req: 1, #token: 1301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.22, #queue-req: 0, 
[2025-07-28 01:25:35 DP1 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.09, #queue-req: 0, 
[2025-07-28 01:25:35 DP0 TP0] Decode batch. #running-req: 1, #token: 1341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.25, #queue-req: 0, 
[2025-07-28 01:25:35 DP1 TP0] Decode batch. #running-req: 1, #token: 656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.56, #queue-req: 0, 
[2025-07-28 01:25:35 DP0 TP0] Decode batch. #running-req: 1, #token: 1381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.21, #queue-req: 0, 
[2025-07-28 01:25:35 DP1 TP0] Decode batch. #running-req: 1, #token: 696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.70, #queue-req: 0, 
[2025-07-28 01:25:36 DP0 TP0] Decode batch. #running-req: 1, #token: 1421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.15, #queue-req: 0, 
[2025-07-28 01:25:36 DP1 TP0] Decode batch. #running-req: 1, #token: 736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.59, #queue-req: 0, 
[2025-07-28 01:25:36 DP0 TP0] Decode batch. #running-req: 1, #token: 1461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.45, #queue-req: 0, 
[2025-07-28 01:25:36 DP1 TP0] Decode batch. #running-req: 1, #token: 776, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.58, #queue-req: 0, 
[2025-07-28 01:25:36 DP0 TP0] Decode batch. #running-req: 1, #token: 1501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.40, #queue-req: 0, 
[2025-07-28 01:25:36 DP1 TP0] Decode batch. #running-req: 1, #token: 816, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.31, #queue-req: 0, 
[2025-07-28 01:25:36 DP0 TP0] Decode batch. #running-req: 1, #token: 1541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.43, #queue-req: 0, 
[2025-07-28 01:25:36 DP1 TP0] Decode batch. #running-req: 1, #token: 856, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.41, #queue-req: 0, 
[2025-07-28 01:25:36 DP0 TP0] Decode batch. #running-req: 1, #token: 1581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.92, #queue-req: 0, 
[2025-07-28 01:25:36 DP1 TP0] Decode batch. #running-req: 1, #token: 896, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.24, #queue-req: 0, 
[2025-07-28 01:25:36 DP0 TP0] Decode batch. #running-req: 1, #token: 1621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.22, #queue-req: 0, 
[2025-07-28 01:25:37 DP1 TP0] Decode batch. #running-req: 1, #token: 936, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.42, #queue-req: 0, 
[2025-07-28 01:25:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.18, #queue-req: 0, 
[2025-07-28 01:25:37 DP1 TP0] Decode batch. #running-req: 1, #token: 976, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.52, #queue-req: 0, 
[2025-07-28 01:25:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1701, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.28, #queue-req: 0, 
[2025-07-28 01:25:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1016, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.57, #queue-req: 0, 
[2025-07-28 01:25:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1741, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.40, #queue-req: 0, 
[2025-07-28 01:25:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1056, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.90, #queue-req: 0, 
[2025-07-28 01:25:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1781, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.16, #queue-req: 0, 
[2025-07-28 01:25:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1096, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.74, #queue-req: 0, 
[2025-07-28 01:25:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1821, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.08, #queue-req: 0, 
[2025-07-28 01:25:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1136, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.76, #queue-req: 0, 
[2025-07-28 01:25:38 DP0 TP0] Decode batch. #running-req: 1, #token: 1861, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.04, #queue-req: 0, 
[2025-07-28 01:25:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1176, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.00, #queue-req: 0, 
[2025-07-28 01:25:38 DP0 TP0] Decode batch. #running-req: 1, #token: 1901, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.83, #queue-req: 0, 
[2025-07-28 01:25:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1216, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.04, #queue-req: 0, 
[2025-07-28 01:25:38 DP0 TP0] Decode batch. #running-req: 1, #token: 1941, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.75, #queue-req: 0, 
[2025-07-28 01:25:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.03, #queue-req: 0, 
[2025-07-28 01:25:38 DP0 TP0] Decode batch. #running-req: 1, #token: 1981, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.17, #queue-req: 0, 
[2025-07-28 01:25:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.90, #queue-req: 0, 
[2025-07-28 01:25:38 DP0 TP0] Decode batch. #running-req: 1, #token: 2021, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.20, #queue-req: 0, 
[2025-07-28 01:25:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.65, #queue-req: 0, 
[2025-07-28 01:25:39 DP0 TP0] Decode batch. #running-req: 1, #token: 2061, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.86, #queue-req: 0, 
[2025-07-28 01:25:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.74, #queue-req: 0, 
[2025-07-28 01:25:39 DP0 TP0] Decode batch. #running-req: 1, #token: 2101, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.59, #queue-req: 0, 
[2025-07-28 01:25:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.76, #queue-req: 0, 
[2025-07-28 01:25:39 DP0 TP0] Decode batch. #running-req: 1, #token: 2141, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.41, #queue-req: 0, 
[2025-07-28 01:25:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.99, #queue-req: 0, 
[2025-07-28 01:25:39 DP0 TP0] Decode batch. #running-req: 1, #token: 2181, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.51, #queue-req: 0, 
[2025-07-28 01:25:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.90, #queue-req: 0, 
[2025-07-28 01:25:39] INFO:     127.0.0.1:34648 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:25:39 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 136, #cached-token: 84, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:25:39 DP0 TP0] Decode batch. #running-req: 2, #token: 2362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 203.81, #queue-req: 0, 
[2025-07-28 01:25:39 DP0 TP0] Decode batch. #running-req: 2, #token: 2442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 409.63, #queue-req: 0, 
[2025-07-28 01:25:40 DP0 TP0] Decode batch. #running-req: 2, #token: 2522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 409.23, #queue-req: 0, 
[2025-07-28 01:25:40 DP0 TP0] Decode batch. #running-req: 2, #token: 2602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 409.67, #queue-req: 0, 
[2025-07-28 01:25:40 DP0 TP0] Decode batch. #running-req: 2, #token: 2682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 407.76, #queue-req: 0, 
[2025-07-28 01:25:40 DP0 TP0] Decode batch. #running-req: 2, #token: 2762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 408.51, #queue-req: 0, 
[2025-07-28 01:25:40 DP0 TP0] Decode batch. #running-req: 2, #token: 2842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 409.16, #queue-req: 0, 
[2025-07-28 01:25:41 DP0 TP0] Decode batch. #running-req: 2, #token: 2922, token usage: 0.00, cuda graph: True, gen throughput (token/s): 408.74, #queue-req: 0, 
[2025-07-28 01:25:41 DP0 TP0] Decode batch. #running-req: 2, #token: 3002, token usage: 0.00, cuda graph: True, gen throughput (token/s): 409.19, #queue-req: 0, 
[2025-07-28 01:25:41] INFO:     127.0.0.1:39684 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:25:41 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 112, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:25:41 DP0 TP0] Decode batch. #running-req: 1, #token: 585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 390.28, #queue-req: 0, 
[2025-07-28 01:25:41 DP1 TP0] Decode batch. #running-req: 1, #token: 228, token usage: 0.00, cuda graph: True, gen throughput (token/s): 19.52, #queue-req: 0, 
[2025-07-28 01:25:41 DP0 TP0] Decode batch. #running-req: 1, #token: 625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.92, #queue-req: 0, 
[2025-07-28 01:25:41 DP1 TP0] Decode batch. #running-req: 1, #token: 268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.47, #queue-req: 0, 
[2025-07-28 01:25:41 DP0 TP0] Decode batch. #running-req: 1, #token: 665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.92, #queue-req: 0, 
[2025-07-28 01:25:42 DP1 TP0] Decode batch. #running-req: 1, #token: 308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.18, #queue-req: 0, 
[2025-07-28 01:25:42 DP0 TP0] Decode batch. #running-req: 1, #token: 705, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.98, #queue-req: 0, 
[2025-07-28 01:25:42 DP1 TP0] Decode batch. #running-req: 1, #token: 348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.17, #queue-req: 0, 
[2025-07-28 01:25:42 DP0 TP0] Decode batch. #running-req: 1, #token: 745, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.91, #queue-req: 0, 
[2025-07-28 01:25:42 DP1 TP0] Decode batch. #running-req: 1, #token: 388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.51, #queue-req: 0, 
[2025-07-28 01:25:42 DP0 TP0] Decode batch. #running-req: 1, #token: 785, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.73, #queue-req: 0, 
[2025-07-28 01:25:42 DP1 TP0] Decode batch. #running-req: 1, #token: 428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.26, #queue-req: 0, 
[2025-07-28 01:25:42 DP0 TP0] Decode batch. #running-req: 1, #token: 825, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.54, #queue-req: 0, 
[2025-07-28 01:25:42] INFO:     127.0.0.1:34658 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:25:42 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 119, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:25:42 DP1 TP0] Decode batch. #running-req: 1, #token: 468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.93, #queue-req: 0, 
[2025-07-28 01:25:42 DP0 TP0] Decode batch. #running-req: 1, #token: 229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 173.25, #queue-req: 0, 
[2025-07-28 01:25:43 DP1 TP0] Decode batch. #running-req: 1, #token: 508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.93, #queue-req: 0, 
[2025-07-28 01:25:43 DP0 TP0] Decode batch. #running-req: 1, #token: 269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.58, #queue-req: 0, 
[2025-07-28 01:25:43 DP1 TP0] Decode batch. #running-req: 1, #token: 548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.72, #queue-req: 0, 
[2025-07-28 01:25:43 DP0 TP0] Decode batch. #running-req: 1, #token: 309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.73, #queue-req: 0, 
[2025-07-28 01:25:43 DP1 TP0] Decode batch. #running-req: 1, #token: 588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.25, #queue-req: 0, 
[2025-07-28 01:25:43 DP0 TP0] Decode batch. #running-req: 1, #token: 349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.90, #queue-req: 0, 
[2025-07-28 01:25:43 DP1 TP0] Decode batch. #running-req: 1, #token: 628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.24, #queue-req: 0, 
[2025-07-28 01:25:43 DP0 TP0] Decode batch. #running-req: 1, #token: 389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.18, #queue-req: 0, 
[2025-07-28 01:25:43 DP1 TP0] Decode batch. #running-req: 1, #token: 668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.44, #queue-req: 0, 
[2025-07-28 01:25:43 DP0 TP0] Decode batch. #running-req: 1, #token: 429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.22, #queue-req: 0, 
[2025-07-28 01:25:43 DP1 TP0] Decode batch. #running-req: 1, #token: 708, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.56, #queue-req: 0, 
[2025-07-28 01:25:44 DP0 TP0] Decode batch. #running-req: 1, #token: 469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.29, #queue-req: 0, 
[2025-07-28 01:25:44 DP1 TP0] Decode batch. #running-req: 1, #token: 748, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.30, #queue-req: 0, 
[2025-07-28 01:25:44 DP0 TP0] Decode batch. #running-req: 1, #token: 509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.26, #queue-req: 0, 
[2025-07-28 01:25:44 DP1 TP0] Decode batch. #running-req: 1, #token: 788, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.71, #queue-req: 0, 
[2025-07-28 01:25:44 DP0 TP0] Decode batch. #running-req: 1, #token: 549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.05, #queue-req: 0, 
[2025-07-28 01:25:44 DP1 TP0] Decode batch. #running-req: 1, #token: 828, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.22, #queue-req: 0, 
[2025-07-28 01:25:44 DP0 TP0] Decode batch. #running-req: 1, #token: 589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.88, #queue-req: 0, 
[2025-07-28 01:25:44 DP1 TP0] Decode batch. #running-req: 1, #token: 868, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.31, #queue-req: 0, 
[2025-07-28 01:25:44 DP0 TP0] Decode batch. #running-req: 1, #token: 629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.73, #queue-req: 0, 
[2025-07-28 01:25:44 DP1 TP0] Decode batch. #running-req: 1, #token: 908, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.34, #queue-req: 0, 
[2025-07-28 01:25:44 DP0 TP0] Decode batch. #running-req: 1, #token: 669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.92, #queue-req: 0, 
[2025-07-28 01:25:45 DP1 TP0] Decode batch. #running-req: 1, #token: 948, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.23, #queue-req: 0, 
[2025-07-28 01:25:45 DP0 TP0] Decode batch. #running-req: 1, #token: 709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.97, #queue-req: 0, 
[2025-07-28 01:25:45 DP1 TP0] Decode batch. #running-req: 1, #token: 988, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.60, #queue-req: 0, 
[2025-07-28 01:25:45 DP0 TP0] Decode batch. #running-req: 1, #token: 749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.81, #queue-req: 0, 
[2025-07-28 01:25:45 DP1 TP0] Decode batch. #running-req: 1, #token: 1028, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.39, #queue-req: 0, 
[2025-07-28 01:25:45 DP0 TP0] Decode batch. #running-req: 1, #token: 789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.77, #queue-req: 0, 
[2025-07-28 01:25:45 DP1 TP0] Decode batch. #running-req: 1, #token: 1068, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.76, #queue-req: 0, 
[2025-07-28 01:25:45 DP0 TP0] Decode batch. #running-req: 1, #token: 829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.60, #queue-req: 0, 
[2025-07-28 01:25:45] INFO:     127.0.0.1:34670 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:25:45 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 188, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:25:45 DP1 TP0] Decode batch. #running-req: 1, #token: 290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 174.20, #queue-req: 0, 
[2025-07-28 01:25:45 DP0 TP0] Decode batch. #running-req: 1, #token: 869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.41, #queue-req: 0, 
[2025-07-28 01:25:46 DP1 TP0] Decode batch. #running-req: 1, #token: 330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.52, #queue-req: 0, 
[2025-07-28 01:25:46 DP0 TP0] Decode batch. #running-req: 1, #token: 909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.41, #queue-req: 0, 
[2025-07-28 01:25:46] INFO:     127.0.0.1:56312 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:25:46 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 80, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:25:46 DP1 TP0] Decode batch. #running-req: 1, #token: 370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.72, #queue-req: 0, 
[2025-07-28 01:25:46 DP0 TP0] Decode batch. #running-req: 1, #token: 195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 176.30, #queue-req: 0, 
[2025-07-28 01:25:46 DP1 TP0] Decode batch. #running-req: 1, #token: 410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.86, #queue-req: 0, 
[2025-07-28 01:25:46 DP0 TP0] Decode batch. #running-req: 1, #token: 235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 216.52, #queue-req: 0, 
[2025-07-28 01:25:46 DP1 TP0] Decode batch. #running-req: 1, #token: 450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.76, #queue-req: 0, 
[2025-07-28 01:25:46 DP0 TP0] Decode batch. #running-req: 1, #token: 275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.91, #queue-req: 0, 
[2025-07-28 01:25:46 DP1 TP0] Decode batch. #running-req: 1, #token: 490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.77, #queue-req: 0, 
[2025-07-28 01:25:46 DP0 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.71, #queue-req: 0, 
[2025-07-28 01:25:47 DP1 TP0] Decode batch. #running-req: 1, #token: 530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.55, #queue-req: 0, 
[2025-07-28 01:25:47 DP0 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.84, #queue-req: 0, 
[2025-07-28 01:25:47 DP1 TP0] Decode batch. #running-req: 1, #token: 570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.32, #queue-req: 0, 
[2025-07-28 01:25:47 DP0 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.61, #queue-req: 0, 
[2025-07-28 01:25:47 DP1 TP0] Decode batch. #running-req: 1, #token: 610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.27, #queue-req: 0, 
[2025-07-28 01:25:47 DP0 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.43, #queue-req: 0, 
[2025-07-28 01:25:47 DP1 TP0] Decode batch. #running-req: 1, #token: 650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.31, #queue-req: 0, 
[2025-07-28 01:25:47 DP0 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.70, #queue-req: 0, 
[2025-07-28 01:25:47 DP1 TP0] Decode batch. #running-req: 1, #token: 690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.38, #queue-req: 0, 
[2025-07-28 01:25:47 DP0 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.55, #queue-req: 0, 
[2025-07-28 01:25:47 DP1 TP0] Decode batch. #running-req: 1, #token: 730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.49, #queue-req: 0, 
[2025-07-28 01:25:48 DP0 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.29, #queue-req: 0, 
[2025-07-28 01:25:48 DP1 TP0] Decode batch. #running-req: 1, #token: 770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.42, #queue-req: 0, 
[2025-07-28 01:25:48 DP0 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.59, #queue-req: 0, 
[2025-07-28 01:25:48 DP1 TP0] Decode batch. #running-req: 1, #token: 810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.16, #queue-req: 0, 
[2025-07-28 01:25:48 DP0 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.66, #queue-req: 0, 
[2025-07-28 01:25:48 DP1 TP0] Decode batch. #running-req: 1, #token: 850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.19, #queue-req: 0, 
[2025-07-28 01:25:48 DP0 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.24, #queue-req: 0, 
[2025-07-28 01:25:48 DP1 TP0] Decode batch. #running-req: 1, #token: 890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.11, #queue-req: 0, 
[2025-07-28 01:25:48 DP0 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.16, #queue-req: 0, 
[2025-07-28 01:25:48 DP1 TP0] Decode batch. #running-req: 1, #token: 930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.24, #queue-req: 0, 
[2025-07-28 01:25:48 DP0 TP0] Decode batch. #running-req: 1, #token: 755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.31, #queue-req: 0, 
[2025-07-28 01:25:49 DP1 TP0] Decode batch. #running-req: 1, #token: 970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.29, #queue-req: 0, 
[2025-07-28 01:25:49 DP0 TP0] Decode batch. #running-req: 1, #token: 795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.33, #queue-req: 0, 
[2025-07-28 01:25:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.37, #queue-req: 0, 
[2025-07-28 01:25:49 DP0 TP0] Decode batch. #running-req: 1, #token: 835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.25, #queue-req: 0, 
[2025-07-28 01:25:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.84, #queue-req: 0, 
[2025-07-28 01:25:49 DP0 TP0] Decode batch. #running-req: 1, #token: 875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.56, #queue-req: 0, 
[2025-07-28 01:25:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.65, #queue-req: 0, 
[2025-07-28 01:25:49 DP0 TP0] Decode batch. #running-req: 1, #token: 915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.56, #queue-req: 0, 
[2025-07-28 01:25:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.62, #queue-req: 0, 
[2025-07-28 01:25:49 DP0 TP0] Decode batch. #running-req: 1, #token: 955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.62, #queue-req: 0, 
[2025-07-28 01:25:49] INFO:     127.0.0.1:56330 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:25:49 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 86, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:25:50 DP1 TP0] Decode batch. #running-req: 2, #token: 1331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 275.32, #queue-req: 0, 
[2025-07-28 01:25:50 DP1 TP0] Decode batch. #running-req: 2, #token: 1411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 411.56, #queue-req: 0, 
[2025-07-28 01:25:50 DP1 TP0] Decode batch. #running-req: 2, #token: 1491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 411.28, #queue-req: 0, 
[2025-07-28 01:25:50 DP1 TP0] Decode batch. #running-req: 2, #token: 1571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 411.41, #queue-req: 0, 
[2025-07-28 01:25:50 DP1 TP0] Decode batch. #running-req: 2, #token: 1651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 411.02, #queue-req: 0, 
[2025-07-28 01:25:51 DP1 TP0] Decode batch. #running-req: 2, #token: 1731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 411.30, #queue-req: 0, 
[2025-07-28 01:25:51 DP1 TP0] Decode batch. #running-req: 2, #token: 1811, token usage: 0.00, cuda graph: True, gen throughput (token/s): 411.14, #queue-req: 0, 
[2025-07-28 01:25:51 DP1 TP0] Decode batch. #running-req: 2, #token: 1891, token usage: 0.00, cuda graph: True, gen throughput (token/s): 411.08, #queue-req: 0, 
[2025-07-28 01:25:51 DP1 TP0] Decode batch. #running-req: 2, #token: 1971, token usage: 0.00, cuda graph: True, gen throughput (token/s): 410.88, #queue-req: 0, 
[2025-07-28 01:25:51 DP1 TP0] Decode batch. #running-req: 2, #token: 2051, token usage: 0.00, cuda graph: True, gen throughput (token/s): 410.88, #queue-req: 0, 
[2025-07-28 01:25:51 DP1 TP0] Decode batch. #running-req: 2, #token: 2131, token usage: 0.00, cuda graph: True, gen throughput (token/s): 410.68, #queue-req: 0, 
[2025-07-28 01:25:52 DP1 TP0] Decode batch. #running-req: 2, #token: 2211, token usage: 0.00, cuda graph: True, gen throughput (token/s): 410.68, #queue-req: 0, 
[2025-07-28 01:25:52 DP1 TP0] Decode batch. #running-req: 2, #token: 2291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 410.57, #queue-req: 0, 
[2025-07-28 01:25:52 DP1 TP0] Decode batch. #running-req: 2, #token: 2371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 410.94, #queue-req: 0, 
[2025-07-28 01:25:52 DP1 TP0] Decode batch. #running-req: 2, #token: 2451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 410.92, #queue-req: 0, 
[2025-07-28 01:25:52 DP1 TP0] Decode batch. #running-req: 2, #token: 2531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 410.86, #queue-req: 0, 
[2025-07-28 01:25:53 DP1 TP0] Decode batch. #running-req: 2, #token: 2611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 410.76, #queue-req: 0, 
[2025-07-28 01:25:53 DP1 TP0] Decode batch. #running-req: 2, #token: 2691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 410.66, #queue-req: 0, 
[2025-07-28 01:25:53 DP1 TP0] Decode batch. #running-req: 2, #token: 2771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 410.49, #queue-req: 0, 
[2025-07-28 01:25:53 DP1 TP0] Decode batch. #running-req: 2, #token: 2851, token usage: 0.00, cuda graph: True, gen throughput (token/s): 410.72, #queue-req: 0, 
[2025-07-28 01:25:53] INFO:     127.0.0.1:56346 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:25:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 395.95, #queue-req: 0, 
[2025-07-28 01:25:53 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 121, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:25:54 DP1 TP0] Decode batch. #running-req: 1, #token: 2010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.77, #queue-req: 0, 
[2025-07-28 01:25:54 DP0 TP0] Decode batch. #running-req: 1, #token: 246, token usage: 0.00, cuda graph: True, gen throughput (token/s): 9.28, #queue-req: 0, 
[2025-07-28 01:25:54 DP1 TP0] Decode batch. #running-req: 1, #token: 2050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.77, #queue-req: 0, 
[2025-07-28 01:25:54 DP0 TP0] Decode batch. #running-req: 1, #token: 286, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.97, #queue-req: 0, 
[2025-07-28 01:25:54 DP1 TP0] Decode batch. #running-req: 1, #token: 2090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.10, #queue-req: 0, 
[2025-07-28 01:25:54 DP0 TP0] Decode batch. #running-req: 1, #token: 326, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.97, #queue-req: 0, 
[2025-07-28 01:25:54 DP1 TP0] Decode batch. #running-req: 1, #token: 2130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.05, #queue-req: 0, 
[2025-07-28 01:25:54 DP0 TP0] Decode batch. #running-req: 1, #token: 366, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.27, #queue-req: 0, 
[2025-07-28 01:25:54 DP1 TP0] Decode batch. #running-req: 1, #token: 2170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.83, #queue-req: 0, 
[2025-07-28 01:25:54 DP0 TP0] Decode batch. #running-req: 1, #token: 406, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.30, #queue-req: 0, 
[2025-07-28 01:25:55 DP1 TP0] Decode batch. #running-req: 1, #token: 2210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.15, #queue-req: 0, 
[2025-07-28 01:25:55 DP0 TP0] Decode batch. #running-req: 1, #token: 446, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.58, #queue-req: 0, 
[2025-07-28 01:25:55 DP1 TP0] Decode batch. #running-req: 1, #token: 2250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.25, #queue-req: 0, 
[2025-07-28 01:25:55 DP0 TP0] Decode batch. #running-req: 1, #token: 486, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.62, #queue-req: 0, 
[2025-07-28 01:25:55 DP1 TP0] Decode batch. #running-req: 1, #token: 2290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.20, #queue-req: 0, 
[2025-07-28 01:25:55 DP0 TP0] Decode batch. #running-req: 1, #token: 526, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.41, #queue-req: 0, 
[2025-07-28 01:25:55 DP1 TP0] Decode batch. #running-req: 1, #token: 2330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.96, #queue-req: 0, 
[2025-07-28 01:25:55 DP0 TP0] Decode batch. #running-req: 1, #token: 566, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.14, #queue-req: 0, 
[2025-07-28 01:25:55 DP1 TP0] Decode batch. #running-req: 1, #token: 2370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.91, #queue-req: 0, 
[2025-07-28 01:25:55 DP0 TP0] Decode batch. #running-req: 1, #token: 606, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.17, #queue-req: 0, 
[2025-07-28 01:25:55 DP1 TP0] Decode batch. #running-req: 1, #token: 2410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.03, #queue-req: 0, 
[2025-07-28 01:25:56 DP0 TP0] Decode batch. #running-req: 1, #token: 646, token usage: 0.00, cuda graph: True, gen throughput (token/s): 206.38, #queue-req: 0, 
[2025-07-28 01:25:56 DP1 TP0] Decode batch. #running-req: 1, #token: 2450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.81, #queue-req: 0, 
[2025-07-28 01:25:56 DP0 TP0] Decode batch. #running-req: 1, #token: 686, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.98, #queue-req: 0, 
[2025-07-28 01:25:56 DP1 TP0] Decode batch. #running-req: 1, #token: 2490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.37, #queue-req: 0, 
[2025-07-28 01:25:56 DP0 TP0] Decode batch. #running-req: 1, #token: 726, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.48, #queue-req: 0, 
[2025-07-28 01:25:56 DP1 TP0] Decode batch. #running-req: 1, #token: 2530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.07, #queue-req: 0, 
[2025-07-28 01:25:56 DP0 TP0] Decode batch. #running-req: 1, #token: 766, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.39, #queue-req: 0, 
[2025-07-28 01:25:56 DP1 TP0] Decode batch. #running-req: 1, #token: 2570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.72, #queue-req: 0, 
[2025-07-28 01:25:56 DP0 TP0] Decode batch. #running-req: 1, #token: 806, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.51, #queue-req: 0, 
[2025-07-28 01:25:56 DP1 TP0] Decode batch. #running-req: 1, #token: 2610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.22, #queue-req: 0, 
[2025-07-28 01:25:57 DP0 TP0] Decode batch. #running-req: 1, #token: 846, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.60, #queue-req: 0, 
[2025-07-28 01:25:57 DP1 TP0] Decode batch. #running-req: 1, #token: 2650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.69, #queue-req: 0, 
[2025-07-28 01:25:57 DP0 TP0] Decode batch. #running-req: 1, #token: 886, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.81, #queue-req: 0, 
[2025-07-28 01:25:57 DP1 TP0] Decode batch. #running-req: 1, #token: 2690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.77, #queue-req: 0, 
[2025-07-28 01:25:57 DP0 TP0] Decode batch. #running-req: 1, #token: 926, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.20, #queue-req: 0, 
[2025-07-28 01:25:57 DP1 TP0] Decode batch. #running-req: 1, #token: 2730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.00, #queue-req: 0, 
[2025-07-28 01:25:57 DP0 TP0] Decode batch. #running-req: 1, #token: 966, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.42, #queue-req: 0, 
[2025-07-28 01:25:57 DP1 TP0] Decode batch. #running-req: 1, #token: 2770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.15, #queue-req: 0, 
[2025-07-28 01:25:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1006, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.61, #queue-req: 0, 
[2025-07-28 01:25:57 DP1 TP0] Decode batch. #running-req: 1, #token: 2810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.14, #queue-req: 0, 
[2025-07-28 01:25:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1046, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.24, #queue-req: 0, 
[2025-07-28 01:25:58 DP1 TP0] Decode batch. #running-req: 1, #token: 2850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.71, #queue-req: 0, 
[2025-07-28 01:25:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1086, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.96, #queue-req: 0, 
[2025-07-28 01:25:58 DP1 TP0] Decode batch. #running-req: 1, #token: 2890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.97, #queue-req: 0, 
[2025-07-28 01:25:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1126, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.00, #queue-req: 0, 
[2025-07-28 01:25:58 DP1 TP0] Decode batch. #running-req: 1, #token: 2930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.85, #queue-req: 0, 
[2025-07-28 01:25:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1166, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.95, #queue-req: 0, 
[2025-07-28 01:25:58 DP1 TP0] Decode batch. #running-req: 1, #token: 2970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.97, #queue-req: 0, 
[2025-07-28 01:25:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1206, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.96, #queue-req: 0, 
[2025-07-28 01:25:58 DP1 TP0] Decode batch. #running-req: 1, #token: 3010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.08, #queue-req: 0, 
[2025-07-28 01:25:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1246, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.82, #queue-req: 0, 
[2025-07-28 01:25:58 DP1 TP0] Decode batch. #running-req: 1, #token: 3050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.02, #queue-req: 0, 
[2025-07-28 01:25:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1286, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.79, #queue-req: 0, 
[2025-07-28 01:25:59 DP1 TP0] Decode batch. #running-req: 1, #token: 3090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.88, #queue-req: 0, 
[2025-07-28 01:25:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1326, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.40, #queue-req: 0, 
[2025-07-28 01:25:59 DP1 TP0] Decode batch. #running-req: 1, #token: 3130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.71, #queue-req: 0, 
[2025-07-28 01:25:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1366, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.50, #queue-req: 0, 
[2025-07-28 01:25:59 DP1 TP0] Decode batch. #running-req: 1, #token: 3170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.61, #queue-req: 0, 
[2025-07-28 01:25:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1406, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.59, #queue-req: 0, 
[2025-07-28 01:25:59 DP1 TP0] Decode batch. #running-req: 1, #token: 3210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.75, #queue-req: 0, 
[2025-07-28 01:25:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1446, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.76, #queue-req: 0, 
[2025-07-28 01:25:59 DP1 TP0] Decode batch. #running-req: 1, #token: 3250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.89, #queue-req: 0, 
[2025-07-28 01:26:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1486, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.61, #queue-req: 0, 
[2025-07-28 01:26:00 DP1 TP0] Decode batch. #running-req: 1, #token: 3290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.81, #queue-req: 0, 
[2025-07-28 01:26:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1526, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.81, #queue-req: 0, 
[2025-07-28 01:26:00 DP1 TP0] Decode batch. #running-req: 1, #token: 3330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.79, #queue-req: 0, 
[2025-07-28 01:26:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1566, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.58, #queue-req: 0, 
[2025-07-28 01:26:00 DP1 TP0] Decode batch. #running-req: 1, #token: 3370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.59, #queue-req: 0, 
[2025-07-28 01:26:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1606, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.52, #queue-req: 0, 
[2025-07-28 01:26:00 DP1 TP0] Decode batch. #running-req: 1, #token: 3410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.59, #queue-req: 0, 
[2025-07-28 01:26:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1646, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.46, #queue-req: 0, 
[2025-07-28 01:26:00 DP1 TP0] Decode batch. #running-req: 1, #token: 3450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.29, #queue-req: 0, 
[2025-07-28 01:26:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1686, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.62, #queue-req: 0, 
[2025-07-28 01:26:00] INFO:     127.0.0.1:41338 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:26:01 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 139, #cached-token: 86, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:26:01 DP1 TP0] Decode batch. #running-req: 2, #token: 3640, token usage: 0.00, cuda graph: True, gen throughput (token/s): 231.59, #queue-req: 0, 
[2025-07-28 01:26:01 DP1 TP0] Decode batch. #running-req: 2, #token: 3720, token usage: 0.00, cuda graph: True, gen throughput (token/s): 409.22, #queue-req: 0, 
[2025-07-28 01:26:01 DP1 TP0] Decode batch. #running-req: 2, #token: 3800, token usage: 0.00, cuda graph: True, gen throughput (token/s): 409.24, #queue-req: 0, 
[2025-07-28 01:26:01 DP1 TP0] Decode batch. #running-req: 2, #token: 3880, token usage: 0.00, cuda graph: True, gen throughput (token/s): 408.95, #queue-req: 0, 
[2025-07-28 01:26:01 DP1 TP0] Decode batch. #running-req: 2, #token: 3960, token usage: 0.00, cuda graph: True, gen throughput (token/s): 408.82, #queue-req: 0, 
[2025-07-28 01:26:02 DP1 TP0] Decode batch. #running-req: 2, #token: 4040, token usage: 0.00, cuda graph: True, gen throughput (token/s): 408.92, #queue-req: 0, 
[2025-07-28 01:26:02 DP1 TP0] Decode batch. #running-req: 2, #token: 4120, token usage: 0.00, cuda graph: True, gen throughput (token/s): 408.78, #queue-req: 0, 
[2025-07-28 01:26:02 DP1 TP0] Decode batch. #running-req: 2, #token: 4200, token usage: 0.00, cuda graph: True, gen throughput (token/s): 408.88, #queue-req: 0, 
[2025-07-28 01:26:02 DP1 TP0] Decode batch. #running-req: 2, #token: 4280, token usage: 0.00, cuda graph: True, gen throughput (token/s): 408.63, #queue-req: 0, 
[2025-07-28 01:26:02 DP1 TP0] Decode batch. #running-req: 2, #token: 4360, token usage: 0.00, cuda graph: True, gen throughput (token/s): 408.69, #queue-req: 0, 
[2025-07-28 01:26:03 DP1 TP0] Decode batch. #running-req: 2, #token: 4440, token usage: 0.00, cuda graph: True, gen throughput (token/s): 408.49, #queue-req: 0, 
[2025-07-28 01:26:03 DP1 TP0] Decode batch. #running-req: 2, #token: 4520, token usage: 0.00, cuda graph: True, gen throughput (token/s): 408.43, #queue-req: 0, 
[2025-07-28 01:26:03 DP1 TP0] Decode batch. #running-req: 2, #token: 4600, token usage: 0.00, cuda graph: True, gen throughput (token/s): 408.64, #queue-req: 0, 
[2025-07-28 01:26:03 DP1 TP0] Decode batch. #running-req: 2, #token: 4680, token usage: 0.00, cuda graph: True, gen throughput (token/s): 408.33, #queue-req: 0, 
[2025-07-28 01:26:03 DP1 TP0] Decode batch. #running-req: 2, #token: 4760, token usage: 0.00, cuda graph: True, gen throughput (token/s): 408.46, #queue-req: 0, 
[2025-07-28 01:26:04 DP1 TP0] Decode batch. #running-req: 2, #token: 4840, token usage: 0.00, cuda graph: True, gen throughput (token/s): 408.38, #queue-req: 0, 
[2025-07-28 01:26:04 DP1 TP0] Decode batch. #running-req: 2, #token: 4920, token usage: 0.00, cuda graph: True, gen throughput (token/s): 407.92, #queue-req: 0, 
[2025-07-28 01:26:04] INFO:     127.0.0.1:41346 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:26:04 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 131, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:26:04 DP1 TP0] Decode batch. #running-req: 1, #token: 4170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 341.00, #queue-req: 0, 
[2025-07-28 01:26:04 DP0 TP0] Decode batch. #running-req: 1, #token: 251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 11.04, #queue-req: 0, 
[2025-07-28 01:26:04 DP1 TP0] Decode batch. #running-req: 1, #token: 4210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.96, #queue-req: 0, 
[2025-07-28 01:26:04 DP0 TP0] Decode batch. #running-req: 1, #token: 291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 206.58, #queue-req: 0, 
[2025-07-28 01:26:04 DP1 TP0] Decode batch. #running-req: 1, #token: 4250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.08, #queue-req: 0, 
[2025-07-28 01:26:04 DP0 TP0] Decode batch. #running-req: 1, #token: 331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.66, #queue-req: 0, 
[2025-07-28 01:26:04 DP1 TP0] Decode batch. #running-req: 1, #token: 4290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.13, #queue-req: 0, 
[2025-07-28 01:26:05 DP0 TP0] Decode batch. #running-req: 1, #token: 371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.92, #queue-req: 0, 
[2025-07-28 01:26:05 DP1 TP0] Decode batch. #running-req: 1, #token: 4330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.14, #queue-req: 0, 
[2025-07-28 01:26:05 DP0 TP0] Decode batch. #running-req: 1, #token: 411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.73, #queue-req: 0, 
[2025-07-28 01:26:05 DP1 TP0] Decode batch. #running-req: 1, #token: 4370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.97, #queue-req: 0, 
[2025-07-28 01:26:05 DP0 TP0] Decode batch. #running-req: 1, #token: 451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.56, #queue-req: 0, 
[2025-07-28 01:26:05 DP1 TP0] Decode batch. #running-req: 1, #token: 4410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.89, #queue-req: 0, 
[2025-07-28 01:26:05 DP0 TP0] Decode batch. #running-req: 1, #token: 491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.74, #queue-req: 0, 
[2025-07-28 01:26:05 DP1 TP0] Decode batch. #running-req: 1, #token: 4450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.89, #queue-req: 0, 
[2025-07-28 01:26:05 DP0 TP0] Decode batch. #running-req: 1, #token: 531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.89, #queue-req: 0, 
[2025-07-28 01:26:05 DP1 TP0] Decode batch. #running-req: 1, #token: 4490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.94, #queue-req: 0, 
[2025-07-28 01:26:06 DP0 TP0] Decode batch. #running-req: 1, #token: 571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.75, #queue-req: 0, 
[2025-07-28 01:26:06 DP1 TP0] Decode batch. #running-req: 1, #token: 4530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.06, #queue-req: 0, 
[2025-07-28 01:26:06 DP0 TP0] Decode batch. #running-req: 1, #token: 611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.68, #queue-req: 0, 
[2025-07-28 01:26:06 DP1 TP0] Decode batch. #running-req: 1, #token: 4570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.03, #queue-req: 0, 
[2025-07-28 01:26:06 DP0 TP0] Decode batch. #running-req: 1, #token: 651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.18, #queue-req: 0, 
[2025-07-28 01:26:06 DP1 TP0] Decode batch. #running-req: 1, #token: 4610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.02, #queue-req: 0, 
[2025-07-28 01:26:06 DP0 TP0] Decode batch. #running-req: 1, #token: 691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.80, #queue-req: 0, 
[2025-07-28 01:26:06 DP1 TP0] Decode batch. #running-req: 1, #token: 4650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.90, #queue-req: 0, 
[2025-07-28 01:26:06 DP0 TP0] Decode batch. #running-req: 1, #token: 731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.52, #queue-req: 0, 
[2025-07-28 01:26:06 DP1 TP0] Decode batch. #running-req: 1, #token: 4690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.84, #queue-req: 0, 
[2025-07-28 01:26:07 DP0 TP0] Decode batch. #running-req: 1, #token: 771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.37, #queue-req: 0, 
[2025-07-28 01:26:07 DP1 TP0] Decode batch. #running-req: 1, #token: 4730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.65, #queue-req: 0, 
[2025-07-28 01:26:07] INFO:     127.0.0.1:42074 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:26:07 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 91, #cached-token: 86, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:26:07 DP1 TP0] Decode batch. #running-req: 2, #token: 4868, token usage: 0.00, cuda graph: True, gen throughput (token/s): 203.00, #queue-req: 0, 
[2025-07-28 01:26:07 DP1 TP0] Decode batch. #running-req: 2, #token: 4948, token usage: 0.00, cuda graph: True, gen throughput (token/s): 407.53, #queue-req: 0, 
[2025-07-28 01:26:07 DP1 TP0] Decode batch. #running-req: 2, #token: 5028, token usage: 0.00, cuda graph: True, gen throughput (token/s): 407.29, #queue-req: 0, 
[2025-07-28 01:26:07 DP1 TP0] Decode batch. #running-req: 2, #token: 5108, token usage: 0.00, cuda graph: True, gen throughput (token/s): 406.88, #queue-req: 0, 
[2025-07-28 01:26:08 DP1 TP0] Decode batch. #running-req: 2, #token: 5188, token usage: 0.00, cuda graph: True, gen throughput (token/s): 407.00, #queue-req: 0, 
[2025-07-28 01:26:08 DP1 TP0] Decode batch. #running-req: 2, #token: 5268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 407.38, #queue-req: 0, 
[2025-07-28 01:26:08 DP1 TP0] Decode batch. #running-req: 2, #token: 5348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 407.07, #queue-req: 0, 
[2025-07-28 01:26:08 DP1 TP0] Decode batch. #running-req: 2, #token: 5428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 407.26, #queue-req: 0, 
[2025-07-28 01:26:08 DP1 TP0] Decode batch. #running-req: 2, #token: 5508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 407.45, #queue-req: 0, 
[2025-07-28 01:26:09 DP1 TP0] Decode batch. #running-req: 2, #token: 5588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 407.16, #queue-req: 0, 
[2025-07-28 01:26:09 DP1 TP0] Decode batch. #running-req: 2, #token: 5668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 405.94, #queue-req: 0, 
[2025-07-28 01:26:09] INFO:     127.0.0.1:42082 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:26:09 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 134, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:26:09 DP0 TP0] Decode batch. #running-req: 1, #token: 221, token usage: 0.00, cuda graph: True, gen throughput (token/s): 17.01, #queue-req: 0, 
[2025-07-28 01:26:09 DP1 TP0] Decode batch. #running-req: 1, #token: 5210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 251.64, #queue-req: 0, 
[2025-07-28 01:26:09 DP0 TP0] Decode batch. #running-req: 1, #token: 261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 193.33, #queue-req: 0, 
[2025-07-28 01:26:09 DP1 TP0] Decode batch. #running-req: 1, #token: 5250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.80, #queue-req: 0, 
[2025-07-28 01:26:09 DP0 TP0] Decode batch. #running-req: 1, #token: 301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.77, #queue-req: 0, 
[2025-07-28 01:26:09 DP1 TP0] Decode batch. #running-req: 1, #token: 5290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.56, #queue-req: 0, 
[2025-07-28 01:26:09 DP0 TP0] Decode batch. #running-req: 1, #token: 341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.46, #queue-req: 0, 
[2025-07-28 01:26:10 DP1 TP0] Decode batch. #running-req: 1, #token: 5330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.44, #queue-req: 0, 
[2025-07-28 01:26:10 DP0 TP0] Decode batch. #running-req: 1, #token: 381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.41, #queue-req: 0, 
[2025-07-28 01:26:10 DP1 TP0] Decode batch. #running-req: 1, #token: 5370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.50, #queue-req: 0, 
[2025-07-28 01:26:10 DP0 TP0] Decode batch. #running-req: 1, #token: 421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.62, #queue-req: 0, 
[2025-07-28 01:26:10 DP1 TP0] Decode batch. #running-req: 1, #token: 5410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.43, #queue-req: 0, 
[2025-07-28 01:26:10 DP0 TP0] Decode batch. #running-req: 1, #token: 461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.38, #queue-req: 0, 
[2025-07-28 01:26:10 DP1 TP0] Decode batch. #running-req: 1, #token: 5450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.20, #queue-req: 0, 
[2025-07-28 01:26:10 DP0 TP0] Decode batch. #running-req: 1, #token: 501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.30, #queue-req: 0, 
[2025-07-28 01:26:10 DP1 TP0] Decode batch. #running-req: 1, #token: 5490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.24, #queue-req: 0, 
[2025-07-28 01:26:10 DP0 TP0] Decode batch. #running-req: 1, #token: 541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.79, #queue-req: 0, 
[2025-07-28 01:26:10 DP1 TP0] Decode batch. #running-req: 1, #token: 5530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.39, #queue-req: 0, 
[2025-07-28 01:26:11 DP0 TP0] Decode batch. #running-req: 1, #token: 581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.69, #queue-req: 0, 
[2025-07-28 01:26:11 DP1 TP0] Decode batch. #running-req: 1, #token: 5570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.47, #queue-req: 0, 
[2025-07-28 01:26:11 DP0 TP0] Decode batch. #running-req: 1, #token: 621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.73, #queue-req: 0, 
[2025-07-28 01:26:11 DP1 TP0] Decode batch. #running-req: 1, #token: 5610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.54, #queue-req: 0, 
[2025-07-28 01:26:11 DP0 TP0] Decode batch. #running-req: 1, #token: 661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.72, #queue-req: 0, 
[2025-07-28 01:26:11 DP1 TP0] Decode batch. #running-req: 1, #token: 5650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.26, #queue-req: 0, 
[2025-07-28 01:26:11 DP0 TP0] Decode batch. #running-req: 1, #token: 701, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.88, #queue-req: 0, 
[2025-07-28 01:26:11 DP1 TP0] Decode batch. #running-req: 1, #token: 5690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.64, #queue-req: 0, 
[2025-07-28 01:26:11 DP0 TP0] Decode batch. #running-req: 1, #token: 741, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.86, #queue-req: 0, 
[2025-07-28 01:26:11 DP1 TP0] Decode batch. #running-req: 1, #token: 5730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.15, #queue-req: 0, 
[2025-07-28 01:26:12 DP0 TP0] Decode batch. #running-req: 1, #token: 781, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.86, #queue-req: 0, 
[2025-07-28 01:26:12 DP1 TP0] Decode batch. #running-req: 1, #token: 5770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.31, #queue-req: 0, 
[2025-07-28 01:26:12 DP0 TP0] Decode batch. #running-req: 1, #token: 821, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.64, #queue-req: 0, 
[2025-07-28 01:26:12 DP1 TP0] Decode batch. #running-req: 1, #token: 5810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.41, #queue-req: 0, 
[2025-07-28 01:26:12 DP0 TP0] Decode batch. #running-req: 1, #token: 861, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.61, #queue-req: 0, 
[2025-07-28 01:26:12 DP1 TP0] Decode batch. #running-req: 1, #token: 5850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.39, #queue-req: 0, 
[2025-07-28 01:26:12 DP0 TP0] Decode batch. #running-req: 1, #token: 901, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.64, #queue-req: 0, 
[2025-07-28 01:26:12 DP1 TP0] Decode batch. #running-req: 1, #token: 5890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.39, #queue-req: 0, 
[2025-07-28 01:26:12 DP0 TP0] Decode batch. #running-req: 1, #token: 941, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.72, #queue-req: 0, 
[2025-07-28 01:26:12 DP1 TP0] Decode batch. #running-req: 1, #token: 5930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.20, #queue-req: 0, 
[2025-07-28 01:26:12 DP0 TP0] Decode batch. #running-req: 1, #token: 981, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.74, #queue-req: 0, 
[2025-07-28 01:26:13 DP1 TP0] Decode batch. #running-req: 1, #token: 5970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.23, #queue-req: 0, 
[2025-07-28 01:26:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1021, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.66, #queue-req: 0, 
[2025-07-28 01:26:13 DP1 TP0] Decode batch. #running-req: 1, #token: 6010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.95, #queue-req: 0, 
[2025-07-28 01:26:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1061, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.55, #queue-req: 0, 
[2025-07-28 01:26:13 DP1 TP0] Decode batch. #running-req: 1, #token: 6050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.95, #queue-req: 0, 
[2025-07-28 01:26:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1101, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.75, #queue-req: 0, 
[2025-07-28 01:26:13 DP1 TP0] Decode batch. #running-req: 1, #token: 6090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.21, #queue-req: 0, 
[2025-07-28 01:26:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1141, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.09, #queue-req: 0, 
[2025-07-28 01:26:13 DP1 TP0] Decode batch. #running-req: 1, #token: 6130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.15, #queue-req: 0, 
[2025-07-28 01:26:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1181, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.09, #queue-req: 0, 
[2025-07-28 01:26:13 DP1 TP0] Decode batch. #running-req: 1, #token: 6170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.81, #queue-req: 0, 
[2025-07-28 01:26:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1221, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.21, #queue-req: 0, 
[2025-07-28 01:26:14 DP1 TP0] Decode batch. #running-req: 1, #token: 6210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.64, #queue-req: 0, 
[2025-07-28 01:26:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.19, #queue-req: 0, 
[2025-07-28 01:26:14 DP1 TP0] Decode batch. #running-req: 1, #token: 6250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.65, #queue-req: 0, 
[2025-07-28 01:26:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.20, #queue-req: 0, 
[2025-07-28 01:26:14 DP1 TP0] Decode batch. #running-req: 1, #token: 6290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.86, #queue-req: 0, 
[2025-07-28 01:26:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.13, #queue-req: 0, 
[2025-07-28 01:26:14 DP1 TP0] Decode batch. #running-req: 1, #token: 6330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.91, #queue-req: 0, 
[2025-07-28 01:26:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.27, #queue-req: 0, 
[2025-07-28 01:26:14 DP1 TP0] Decode batch. #running-req: 1, #token: 6370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.85, #queue-req: 0, 
[2025-07-28 01:26:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.23, #queue-req: 0, 
[2025-07-28 01:26:15 DP1 TP0] Decode batch. #running-req: 1, #token: 6410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.86, #queue-req: 0, 
[2025-07-28 01:26:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.34, #queue-req: 0, 
[2025-07-28 01:26:15 DP1 TP0] Decode batch. #running-req: 1, #token: 6450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.63, #queue-req: 0, 
[2025-07-28 01:26:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.18, #queue-req: 0, 
[2025-07-28 01:26:15 DP1 TP0] Decode batch. #running-req: 1, #token: 6490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.64, #queue-req: 0, 
[2025-07-28 01:26:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.32, #queue-req: 0, 
[2025-07-28 01:26:15 DP1 TP0] Decode batch. #running-req: 1, #token: 6530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.66, #queue-req: 0, 
[2025-07-28 01:26:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.03, #queue-req: 0, 
[2025-07-28 01:26:15 DP1 TP0] Decode batch. #running-req: 1, #token: 6570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.93, #queue-req: 0, 
[2025-07-28 01:26:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.03, #queue-req: 0, 
[2025-07-28 01:26:16 DP1 TP0] Decode batch. #running-req: 1, #token: 6610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.85, #queue-req: 0, 
[2025-07-28 01:26:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.99, #queue-req: 0, 
[2025-07-28 01:26:16 DP1 TP0] Decode batch. #running-req: 1, #token: 6650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.93, #queue-req: 0, 
[2025-07-28 01:26:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1701, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.28, #queue-req: 0, 
[2025-07-28 01:26:16 DP1 TP0] Decode batch. #running-req: 1, #token: 6690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.76, #queue-req: 0, 
[2025-07-28 01:26:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1741, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.10, #queue-req: 0, 
[2025-07-28 01:26:16 DP1 TP0] Decode batch. #running-req: 1, #token: 6730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.70, #queue-req: 0, 
[2025-07-28 01:26:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1781, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.23, #queue-req: 0, 
[2025-07-28 01:26:16 DP1 TP0] Decode batch. #running-req: 1, #token: 6770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.71, #queue-req: 0, 
[2025-07-28 01:26:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1821, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.12, #queue-req: 0, 
[2025-07-28 01:26:17 DP1 TP0] Decode batch. #running-req: 1, #token: 6810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.78, #queue-req: 0, 
[2025-07-28 01:26:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1861, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.93, #queue-req: 0, 
[2025-07-28 01:26:17 DP1 TP0] Decode batch. #running-req: 1, #token: 6850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.95, #queue-req: 0, 
[2025-07-28 01:26:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1901, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.12, #queue-req: 0, 
[2025-07-28 01:26:17 DP1 TP0] Decode batch. #running-req: 1, #token: 6890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.84, #queue-req: 0, 
[2025-07-28 01:26:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1941, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.11, #queue-req: 0, 
[2025-07-28 01:26:17 DP1 TP0] Decode batch. #running-req: 1, #token: 6930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.73, #queue-req: 0, 
[2025-07-28 01:26:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1981, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.26, #queue-req: 0, 
[2025-07-28 01:26:17 DP1 TP0] Decode batch. #running-req: 1, #token: 6970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.61, #queue-req: 0, 
[2025-07-28 01:26:17 DP0 TP0] Decode batch. #running-req: 1, #token: 2021, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.37, #queue-req: 0, 
[2025-07-28 01:26:17 DP1 TP0] Decode batch. #running-req: 1, #token: 7010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.65, #queue-req: 0, 
[2025-07-28 01:26:18 DP0 TP0] Decode batch. #running-req: 1, #token: 2061, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.12, #queue-req: 0, 
[2025-07-28 01:26:18 DP1 TP0] Decode batch. #running-req: 1, #token: 7050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.58, #queue-req: 0, 
[2025-07-28 01:26:18 DP0 TP0] Decode batch. #running-req: 1, #token: 2101, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.58, #queue-req: 0, 
[2025-07-28 01:26:18 DP1 TP0] Decode batch. #running-req: 1, #token: 7090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.76, #queue-req: 0, 
[2025-07-28 01:26:18 DP0 TP0] Decode batch. #running-req: 1, #token: 2141, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.56, #queue-req: 0, 
[2025-07-28 01:26:18 DP1 TP0] Decode batch. #running-req: 1, #token: 7130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.77, #queue-req: 0, 
[2025-07-28 01:26:18 DP0 TP0] Decode batch. #running-req: 1, #token: 2181, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.66, #queue-req: 0, 
[2025-07-28 01:26:18 DP1 TP0] Decode batch. #running-req: 1, #token: 7170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.81, #queue-req: 0, 
[2025-07-28 01:26:18 DP0 TP0] Decode batch. #running-req: 1, #token: 2221, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.88, #queue-req: 0, 
[2025-07-28 01:26:18 DP1 TP0] Decode batch. #running-req: 1, #token: 7210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.30, #queue-req: 0, 
[2025-07-28 01:26:18 DP0 TP0] Decode batch. #running-req: 1, #token: 2261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.87, #queue-req: 0, 
[2025-07-28 01:26:19 DP1 TP0] Decode batch. #running-req: 1, #token: 7250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.40, #queue-req: 0, 
[2025-07-28 01:26:19 DP0 TP0] Decode batch. #running-req: 1, #token: 2301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.81, #queue-req: 0, 
[2025-07-28 01:26:19 DP1 TP0] Decode batch. #running-req: 1, #token: 7290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.22, #queue-req: 0, 
[2025-07-28 01:26:19 DP0 TP0] Decode batch. #running-req: 1, #token: 2341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.52, #queue-req: 0, 
[2025-07-28 01:26:19 DP1 TP0] Decode batch. #running-req: 1, #token: 7330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.49, #queue-req: 0, 
[2025-07-28 01:26:19 DP0 TP0] Decode batch. #running-req: 1, #token: 2381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.34, #queue-req: 0, 
[2025-07-28 01:26:19 DP1 TP0] Decode batch. #running-req: 1, #token: 7370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.53, #queue-req: 0, 
[2025-07-28 01:26:19 DP0 TP0] Decode batch. #running-req: 1, #token: 2421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.53, #queue-req: 0, 
[2025-07-28 01:26:19 DP1 TP0] Decode batch. #running-req: 1, #token: 7410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.49, #queue-req: 0, 
[2025-07-28 01:26:19 DP0 TP0] Decode batch. #running-req: 1, #token: 2461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.60, #queue-req: 0, 
[2025-07-28 01:26:20 DP1 TP0] Decode batch. #running-req: 1, #token: 7450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.38, #queue-req: 0, 
[2025-07-28 01:26:20 DP0 TP0] Decode batch. #running-req: 1, #token: 2501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.26, #queue-req: 0, 
[2025-07-28 01:26:20 DP1 TP0] Decode batch. #running-req: 1, #token: 7490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.18, #queue-req: 0, 
[2025-07-28 01:26:20 DP0 TP0] Decode batch. #running-req: 1, #token: 2541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.76, #queue-req: 0, 
[2025-07-28 01:26:20 DP1 TP0] Decode batch. #running-req: 1, #token: 7530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.22, #queue-req: 0, 
[2025-07-28 01:26:20 DP0 TP0] Decode batch. #running-req: 1, #token: 2581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.43, #queue-req: 0, 
[2025-07-28 01:26:20 DP1 TP0] Decode batch. #running-req: 1, #token: 7570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.24, #queue-req: 0, 
[2025-07-28 01:26:20 DP0 TP0] Decode batch. #running-req: 1, #token: 2621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.44, #queue-req: 0, 
[2025-07-28 01:26:20 DP1 TP0] Decode batch. #running-req: 1, #token: 7610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.52, #queue-req: 0, 
[2025-07-28 01:26:20 DP0 TP0] Decode batch. #running-req: 1, #token: 2661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.55, #queue-req: 0, 
[2025-07-28 01:26:21 DP1 TP0] Decode batch. #running-req: 1, #token: 7650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.34, #queue-req: 0, 
[2025-07-28 01:26:21 DP0 TP0] Decode batch. #running-req: 1, #token: 2701, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.65, #queue-req: 0, 
[2025-07-28 01:26:21 DP1 TP0] Decode batch. #running-req: 1, #token: 7690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.37, #queue-req: 0, 
[2025-07-28 01:26:21 DP0 TP0] Decode batch. #running-req: 1, #token: 2741, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.59, #queue-req: 0, 
[2025-07-28 01:26:21 DP1 TP0] Decode batch. #running-req: 1, #token: 7730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.10, #queue-req: 0, 
[2025-07-28 01:26:21 DP0 TP0] Decode batch. #running-req: 1, #token: 2781, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.66, #queue-req: 0, 
[2025-07-28 01:26:21 DP1 TP0] Decode batch. #running-req: 1, #token: 7770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.26, #queue-req: 0, 
[2025-07-28 01:26:21 DP0 TP0] Decode batch. #running-req: 1, #token: 2821, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.66, #queue-req: 0, 
[2025-07-28 01:26:21 DP1 TP0] Decode batch. #running-req: 1, #token: 7810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.07, #queue-req: 0, 
[2025-07-28 01:26:21 DP0 TP0] Decode batch. #running-req: 1, #token: 2861, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.55, #queue-req: 0, 
[2025-07-28 01:26:21 DP1 TP0] Decode batch. #running-req: 1, #token: 7850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.38, #queue-req: 0, 
[2025-07-28 01:26:21 DP0 TP0] Decode batch. #running-req: 1, #token: 2901, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.60, #queue-req: 0, 
[2025-07-28 01:26:22 DP1 TP0] Decode batch. #running-req: 1, #token: 7890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.43, #queue-req: 0, 
[2025-07-28 01:26:22 DP0 TP0] Decode batch. #running-req: 1, #token: 2941, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.24, #queue-req: 0, 
[2025-07-28 01:26:22 DP1 TP0] Decode batch. #running-req: 1, #token: 7930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.41, #queue-req: 0, 
[2025-07-28 01:26:22 DP0 TP0] Decode batch. #running-req: 1, #token: 2981, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.04, #queue-req: 0, 
[2025-07-28 01:26:22 DP1 TP0] Decode batch. #running-req: 1, #token: 7970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.19, #queue-req: 0, 
[2025-07-28 01:26:22 DP0 TP0] Decode batch. #running-req: 1, #token: 3021, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.12, #queue-req: 0, 
[2025-07-28 01:26:22 DP1 TP0] Decode batch. #running-req: 1, #token: 8010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.23, #queue-req: 0, 
[2025-07-28 01:26:22 DP0 TP0] Decode batch. #running-req: 1, #token: 3061, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.63, #queue-req: 0, 
[2025-07-28 01:26:22 DP1 TP0] Decode batch. #running-req: 1, #token: 8050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.05, #queue-req: 0, 
[2025-07-28 01:26:22 DP0 TP0] Decode batch. #running-req: 1, #token: 3101, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.53, #queue-req: 0, 
[2025-07-28 01:26:23 DP1 TP0] Decode batch. #running-req: 1, #token: 8090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.26, #queue-req: 0, 
[2025-07-28 01:26:23 DP0 TP0] Decode batch. #running-req: 1, #token: 3141, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.34, #queue-req: 0, 
[2025-07-28 01:26:23] INFO:     127.0.0.1:42084 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:26:23 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 74, #cached-token: 84, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:26:23 DP1 TP0] Decode batch. #running-req: 2, #token: 8219, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.17, #queue-req: 0, 
[2025-07-28 01:26:23 DP1 TP0] Decode batch. #running-req: 2, #token: 8299, token usage: 0.00, cuda graph: True, gen throughput (token/s): 404.27, #queue-req: 0, 
[2025-07-28 01:26:23 DP1 TP0] Decode batch. #running-req: 2, #token: 8379, token usage: 0.00, cuda graph: True, gen throughput (token/s): 403.89, #queue-req: 0, 
[2025-07-28 01:26:23 DP1 TP0] Decode batch. #running-req: 2, #token: 8459, token usage: 0.00, cuda graph: True, gen throughput (token/s): 402.89, #queue-req: 0, 
[2025-07-28 01:26:24 DP1 TP0] Decode batch. #running-req: 2, #token: 8539, token usage: 0.00, cuda graph: True, gen throughput (token/s): 402.95, #queue-req: 0, 
[2025-07-28 01:26:24 DP1 TP0] Decode batch. #running-req: 2, #token: 8619, token usage: 0.00, cuda graph: True, gen throughput (token/s): 402.96, #queue-req: 0, 
[2025-07-28 01:26:24 DP1 TP0] Decode batch. #running-req: 2, #token: 8699, token usage: 0.00, cuda graph: True, gen throughput (token/s): 402.92, #queue-req: 0, 
[2025-07-28 01:26:24 DP1 TP0] Decode batch. #running-req: 2, #token: 8779, token usage: 0.00, cuda graph: True, gen throughput (token/s): 402.91, #queue-req: 0, 
[2025-07-28 01:26:24 DP1 TP0] Decode batch. #running-req: 2, #token: 8859, token usage: 0.00, cuda graph: True, gen throughput (token/s): 403.00, #queue-req: 0, 
[2025-07-28 01:26:24] INFO:     127.0.0.1:56324 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:26:25 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 142, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:26:25 DP1 TP0] Decode batch. #running-req: 1, #token: 533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.63, #queue-req: 0, 
[2025-07-28 01:26:25 DP0 TP0] Decode batch. #running-req: 1, #token: 252, token usage: 0.00, cuda graph: True, gen throughput (token/s): 19.49, #queue-req: 0, 
[2025-07-28 01:26:25 DP1 TP0] Decode batch. #running-req: 1, #token: 573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.38, #queue-req: 0, 
[2025-07-28 01:26:25 DP0 TP0] Decode batch. #running-req: 1, #token: 292, token usage: 0.00, cuda graph: True, gen throughput (token/s): 203.61, #queue-req: 0, 
[2025-07-28 01:26:25 DP1 TP0] Decode batch. #running-req: 1, #token: 613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.31, #queue-req: 0, 
[2025-07-28 01:26:25 DP0 TP0] Decode batch. #running-req: 1, #token: 332, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.80, #queue-req: 0, 
[2025-07-28 01:26:25 DP1 TP0] Decode batch. #running-req: 1, #token: 653, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.28, #queue-req: 0, 
[2025-07-28 01:26:25 DP0 TP0] Decode batch. #running-req: 1, #token: 372, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.87, #queue-req: 0, 
[2025-07-28 01:26:25 DP1 TP0] Decode batch. #running-req: 1, #token: 693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.55, #queue-req: 0, 
[2025-07-28 01:26:25 DP0 TP0] Decode batch. #running-req: 1, #token: 412, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.09, #queue-req: 0, 
[2025-07-28 01:26:26 DP1 TP0] Decode batch. #running-req: 1, #token: 733, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.57, #queue-req: 0, 
[2025-07-28 01:26:26 DP0 TP0] Decode batch. #running-req: 1, #token: 452, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.20, #queue-req: 0, 
[2025-07-28 01:26:26 DP1 TP0] Decode batch. #running-req: 1, #token: 773, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.47, #queue-req: 0, 
[2025-07-28 01:26:26 DP0 TP0] Decode batch. #running-req: 1, #token: 492, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.23, #queue-req: 0, 
[2025-07-28 01:26:26 DP1 TP0] Decode batch. #running-req: 1, #token: 813, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.18, #queue-req: 0, 
[2025-07-28 01:26:26 DP0 TP0] Decode batch. #running-req: 1, #token: 532, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.02, #queue-req: 0, 
[2025-07-28 01:26:26] INFO:     127.0.0.1:53848 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:26:26 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 80, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:26:26 DP1 TP0] Decode batch. #running-req: 1, #token: 174, token usage: 0.00, cuda graph: True, gen throughput (token/s): 176.30, #queue-req: 0, 
[2025-07-28 01:26:26 DP0 TP0] Decode batch. #running-req: 1, #token: 572, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.84, #queue-req: 0, 
[2025-07-28 01:26:26 DP1 TP0] Decode batch. #running-req: 1, #token: 214, token usage: 0.00, cuda graph: True, gen throughput (token/s): 218.78, #queue-req: 0, 
[2025-07-28 01:26:26 DP0 TP0] Decode batch. #running-req: 1, #token: 612, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.87, #queue-req: 0, 
[2025-07-28 01:26:26 DP1 TP0] Decode batch. #running-req: 1, #token: 254, token usage: 0.00, cuda graph: True, gen throughput (token/s): 216.54, #queue-req: 0, 
[2025-07-28 01:26:27 DP0 TP0] Decode batch. #running-req: 1, #token: 652, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.35, #queue-req: 0, 
[2025-07-28 01:26:27 DP1 TP0] Decode batch. #running-req: 1, #token: 294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.68, #queue-req: 0, 
[2025-07-28 01:26:27 DP0 TP0] Decode batch. #running-req: 1, #token: 692, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.71, #queue-req: 0, 
[2025-07-28 01:26:27 DP1 TP0] Decode batch. #running-req: 1, #token: 334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.22, #queue-req: 0, 
[2025-07-28 01:26:27 DP0 TP0] Decode batch. #running-req: 1, #token: 732, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.26, #queue-req: 0, 
[2025-07-28 01:26:27 DP1 TP0] Decode batch. #running-req: 1, #token: 374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.11, #queue-req: 0, 
[2025-07-28 01:26:27 DP0 TP0] Decode batch. #running-req: 1, #token: 772, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.53, #queue-req: 0, 
[2025-07-28 01:26:27 DP1 TP0] Decode batch. #running-req: 1, #token: 414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.39, #queue-req: 0, 
[2025-07-28 01:26:27 DP0 TP0] Decode batch. #running-req: 1, #token: 812, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.76, #queue-req: 0, 
[2025-07-28 01:26:27] INFO:     127.0.0.1:53864 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:26:27 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 169, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:26:27 DP1 TP0] Decode batch. #running-req: 1, #token: 454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.62, #queue-req: 0, 
[2025-07-28 01:26:28 DP0 TP0] Decode batch. #running-req: 1, #token: 293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 171.76, #queue-req: 0, 
[2025-07-28 01:26:28 DP1 TP0] Decode batch. #running-req: 1, #token: 494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.78, #queue-req: 0, 
[2025-07-28 01:26:28 DP0 TP0] Decode batch. #running-req: 1, #token: 333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.97, #queue-req: 0, 
[2025-07-28 01:26:28 DP1 TP0] Decode batch. #running-req: 1, #token: 534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.34, #queue-req: 0, 
[2025-07-28 01:26:28 DP0 TP0] Decode batch. #running-req: 1, #token: 373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.74, #queue-req: 0, 
[2025-07-28 01:26:28 DP1 TP0] Decode batch. #running-req: 1, #token: 574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.37, #queue-req: 0, 
[2025-07-28 01:26:28 DP0 TP0] Decode batch. #running-req: 1, #token: 413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.17, #queue-req: 0, 
[2025-07-28 01:26:28 DP1 TP0] Decode batch. #running-req: 1, #token: 614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.36, #queue-req: 0, 
[2025-07-28 01:26:28 DP0 TP0] Decode batch. #running-req: 1, #token: 453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.28, #queue-req: 0, 
[2025-07-28 01:26:28 DP1 TP0] Decode batch. #running-req: 1, #token: 654, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.57, #queue-req: 0, 
[2025-07-28 01:26:28 DP0 TP0] Decode batch. #running-req: 1, #token: 493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.28, #queue-req: 0, 
[2025-07-28 01:26:29 DP1 TP0] Decode batch. #running-req: 1, #token: 694, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.31, #queue-req: 0, 
[2025-07-28 01:26:29 DP0 TP0] Decode batch. #running-req: 1, #token: 533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.06, #queue-req: 0, 
[2025-07-28 01:26:29 DP1 TP0] Decode batch. #running-req: 1, #token: 734, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.47, #queue-req: 0, 
[2025-07-28 01:26:29 DP0 TP0] Decode batch. #running-req: 1, #token: 573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.95, #queue-req: 0, 
[2025-07-28 01:26:29 DP1 TP0] Decode batch. #running-req: 1, #token: 774, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.51, #queue-req: 0, 
[2025-07-28 01:26:29 DP0 TP0] Decode batch. #running-req: 1, #token: 613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.82, #queue-req: 0, 
[2025-07-28 01:26:29 DP1 TP0] Decode batch. #running-req: 1, #token: 814, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.08, #queue-req: 0, 
[2025-07-28 01:26:29 DP0 TP0] Decode batch. #running-req: 1, #token: 653, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.15, #queue-req: 0, 
[2025-07-28 01:26:29 DP1 TP0] Decode batch. #running-req: 1, #token: 854, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.73, #queue-req: 0, 
[2025-07-28 01:26:29 DP0 TP0] Decode batch. #running-req: 1, #token: 693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.54, #queue-req: 0, 
[2025-07-28 01:26:29 DP1 TP0] Decode batch. #running-req: 1, #token: 894, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.92, #queue-req: 0, 
[2025-07-28 01:26:30 DP0 TP0] Decode batch. #running-req: 1, #token: 733, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.83, #queue-req: 0, 
[2025-07-28 01:26:30 DP1 TP0] Decode batch. #running-req: 1, #token: 934, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.36, #queue-req: 0, 
[2025-07-28 01:26:30 DP0 TP0] Decode batch. #running-req: 1, #token: 773, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.73, #queue-req: 0, 
[2025-07-28 01:26:30 DP1 TP0] Decode batch. #running-req: 1, #token: 974, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.48, #queue-req: 0, 
[2025-07-28 01:26:30 DP0 TP0] Decode batch. #running-req: 1, #token: 813, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.61, #queue-req: 0, 
[2025-07-28 01:26:30 DP1 TP0] Decode batch. #running-req: 1, #token: 1014, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.32, #queue-req: 0, 
[2025-07-28 01:26:30 DP0 TP0] Decode batch. #running-req: 1, #token: 853, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.82, #queue-req: 0, 
[2025-07-28 01:26:30 DP1 TP0] Decode batch. #running-req: 1, #token: 1054, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.84, #queue-req: 0, 
[2025-07-28 01:26:30 DP0 TP0] Decode batch. #running-req: 1, #token: 893, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.93, #queue-req: 0, 
[2025-07-28 01:26:30 DP1 TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.75, #queue-req: 0, 
[2025-07-28 01:26:30] INFO:     127.0.0.1:53866 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:26:30 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 174, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:26:31] INFO:     127.0.0.1:53868 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:26:31 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:26:31 DP0 TP0] Decode batch. #running-req: 1, #token: 172, token usage: 0.00, cuda graph: True, gen throughput (token/s): 174.56, #queue-req: 0, 
[2025-07-28 01:26:31 DP1 TP0] Decode batch. #running-req: 1, #token: 300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 172.04, #queue-req: 0, 
[2025-07-28 01:26:31 DP0 TP0] Decode batch. #running-req: 1, #token: 212, token usage: 0.00, cuda graph: True, gen throughput (token/s): 218.01, #queue-req: 0, 
[2025-07-28 01:26:31 DP1 TP0] Decode batch. #running-req: 1, #token: 340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.01, #queue-req: 0, 
[2025-07-28 01:26:31 DP0 TP0] Decode batch. #running-req: 1, #token: 252, token usage: 0.00, cuda graph: True, gen throughput (token/s): 215.63, #queue-req: 0, 
[2025-07-28 01:26:31 DP1 TP0] Decode batch. #running-req: 1, #token: 380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.15, #queue-req: 0, 
[2025-07-28 01:26:31 DP0 TP0] Decode batch. #running-req: 1, #token: 292, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.68, #queue-req: 0, 
[2025-07-28 01:26:31 DP1 TP0] Decode batch. #running-req: 1, #token: 420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.60, #queue-req: 0, 
[2025-07-28 01:26:31 DP0 TP0] Decode batch. #running-req: 1, #token: 332, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.73, #queue-req: 0, 
[2025-07-28 01:26:31 DP1 TP0] Decode batch. #running-req: 1, #token: 460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.58, #queue-req: 0, 
[2025-07-28 01:26:32 DP0 TP0] Decode batch. #running-req: 1, #token: 372, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.83, #queue-req: 0, 
[2025-07-28 01:26:32 DP1 TP0] Decode batch. #running-req: 1, #token: 500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 215.00, #queue-req: 0, 
[2025-07-28 01:26:32 DP0 TP0] Decode batch. #running-req: 1, #token: 412, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.96, #queue-req: 0, 
[2025-07-28 01:26:32 DP1 TP0] Decode batch. #running-req: 1, #token: 540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.53, #queue-req: 0, 
[2025-07-28 01:26:32 DP0 TP0] Decode batch. #running-req: 1, #token: 452, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.19, #queue-req: 0, 
[2025-07-28 01:26:32 DP1 TP0] Decode batch. #running-req: 1, #token: 580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.49, #queue-req: 0, 
[2025-07-28 01:26:32 DP0 TP0] Decode batch. #running-req: 1, #token: 492, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.18, #queue-req: 0, 
[2025-07-28 01:26:32 DP1 TP0] Decode batch. #running-req: 1, #token: 620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.42, #queue-req: 0, 
[2025-07-28 01:26:32 DP0 TP0] Decode batch. #running-req: 1, #token: 532, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.97, #queue-req: 0, 
[2025-07-28 01:26:32 DP1 TP0] Decode batch. #running-req: 1, #token: 660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.38, #queue-req: 0, 
[2025-07-28 01:26:32 DP0 TP0] Decode batch. #running-req: 1, #token: 572, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.75, #queue-req: 0, 
[2025-07-28 01:26:33 DP1 TP0] Decode batch. #running-req: 1, #token: 700, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.82, #queue-req: 0, 
[2025-07-28 01:26:33] INFO:     127.0.0.1:53880 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:26:33 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 149, #cached-token: 86, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:26:33 DP1 TP0] Decode batch. #running-req: 2, #token: 924, token usage: 0.00, cuda graph: True, gen throughput (token/s): 339.00, #queue-req: 0, 
[2025-07-28 01:26:33 DP1 TP0] Decode batch. #running-req: 2, #token: 1004, token usage: 0.00, cuda graph: True, gen throughput (token/s): 412.30, #queue-req: 0, 
[2025-07-28 01:26:33] INFO:     127.0.0.1:53872 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:26:33 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 118, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:26:33 DP0 TP0] Decode batch. #running-req: 1, #token: 224, token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.35, #queue-req: 0, 
[2025-07-28 01:26:33 DP1 TP0] Decode batch. #running-req: 1, #token: 350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 225.13, #queue-req: 0, 
[2025-07-28 01:26:33 DP0 TP0] Decode batch. #running-req: 1, #token: 264, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.41, #queue-req: 0, 
[2025-07-28 01:26:33 DP1 TP0] Decode batch. #running-req: 1, #token: 390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.80, #queue-req: 0, 
[2025-07-28 01:26:33 DP0 TP0] Decode batch. #running-req: 1, #token: 304, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.20, #queue-req: 0, 
[2025-07-28 01:26:33 DP1 TP0] Decode batch. #running-req: 1, #token: 430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.98, #queue-req: 0, 
[2025-07-28 01:26:34 DP0 TP0] Decode batch. #running-req: 1, #token: 344, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.92, #queue-req: 0, 
[2025-07-28 01:26:34 DP1 TP0] Decode batch. #running-req: 1, #token: 470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.94, #queue-req: 0, 
[2025-07-28 01:26:34 DP0 TP0] Decode batch. #running-req: 1, #token: 384, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.87, #queue-req: 0, 
[2025-07-28 01:26:34 DP1 TP0] Decode batch. #running-req: 1, #token: 510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.91, #queue-req: 0, 
[2025-07-28 01:26:34 DP0 TP0] Decode batch. #running-req: 1, #token: 424, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.56, #queue-req: 0, 
[2025-07-28 01:26:34 DP1 TP0] Decode batch. #running-req: 1, #token: 550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.46, #queue-req: 0, 
[2025-07-28 01:26:34 DP0 TP0] Decode batch. #running-req: 1, #token: 464, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.84, #queue-req: 0, 
[2025-07-28 01:26:34 DP1 TP0] Decode batch. #running-req: 1, #token: 590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.33, #queue-req: 0, 
[2025-07-28 01:26:34 DP0 TP0] Decode batch. #running-req: 1, #token: 504, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.76, #queue-req: 0, 
[2025-07-28 01:26:34 DP1 TP0] Decode batch. #running-req: 1, #token: 630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.41, #queue-req: 0, 
[2025-07-28 01:26:35 DP0 TP0] Decode batch. #running-req: 1, #token: 544, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.75, #queue-req: 0, 
[2025-07-28 01:26:35 DP1 TP0] Decode batch. #running-req: 1, #token: 670, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.43, #queue-req: 0, 
[2025-07-28 01:26:35 DP0 TP0] Decode batch. #running-req: 1, #token: 584, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.69, #queue-req: 0, 
[2025-07-28 01:26:35 DP1 TP0] Decode batch. #running-req: 1, #token: 710, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.59, #queue-req: 0, 
[2025-07-28 01:26:35 DP0 TP0] Decode batch. #running-req: 1, #token: 624, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.62, #queue-req: 0, 
[2025-07-28 01:26:35 DP1 TP0] Decode batch. #running-req: 1, #token: 750, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.30, #queue-req: 0, 
[2025-07-28 01:26:35 DP0 TP0] Decode batch. #running-req: 1, #token: 664, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.81, #queue-req: 0, 
[2025-07-28 01:26:35 DP1 TP0] Decode batch. #running-req: 1, #token: 790, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.36, #queue-req: 0, 
[2025-07-28 01:26:35 DP0 TP0] Decode batch. #running-req: 1, #token: 704, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.82, #queue-req: 0, 
[2025-07-28 01:26:35 DP1 TP0] Decode batch. #running-req: 1, #token: 830, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.22, #queue-req: 0, 
[2025-07-28 01:26:36 DP0 TP0] Decode batch. #running-req: 1, #token: 744, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.04, #queue-req: 0, 
[2025-07-28 01:26:36 DP1 TP0] Decode batch. #running-req: 1, #token: 870, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.26, #queue-req: 0, 
[2025-07-28 01:26:36 DP0 TP0] Decode batch. #running-req: 1, #token: 784, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.74, #queue-req: 0, 
[2025-07-28 01:26:36 DP1 TP0] Decode batch. #running-req: 1, #token: 910, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.35, #queue-req: 0, 
[2025-07-28 01:26:36 DP0 TP0] Decode batch. #running-req: 1, #token: 824, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.78, #queue-req: 0, 
[2025-07-28 01:26:36 DP1 TP0] Decode batch. #running-req: 1, #token: 950, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.45, #queue-req: 0, 
[2025-07-28 01:26:36 DP0 TP0] Decode batch. #running-req: 1, #token: 864, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.19, #queue-req: 0, 
[2025-07-28 01:26:36 DP1 TP0] Decode batch. #running-req: 1, #token: 990, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.51, #queue-req: 0, 
[2025-07-28 01:26:36] INFO:     127.0.0.1:33324 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:26:36 DP0 TP0] Decode batch. #running-req: 1, #token: 904, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.47, #queue-req: 0, 
[2025-07-28 01:26:36 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 124, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:26:36 DP1 TP0] Decode batch. #running-req: 1, #token: 212, token usage: 0.00, cuda graph: True, gen throughput (token/s): 175.45, #queue-req: 0, 
[2025-07-28 01:26:36 DP0 TP0] Decode batch. #running-req: 1, #token: 944, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.04, #queue-req: 0, 
[2025-07-28 01:26:37 DP1 TP0] Decode batch. #running-req: 1, #token: 252, token usage: 0.00, cuda graph: True, gen throughput (token/s): 216.90, #queue-req: 0, 
[2025-07-28 01:26:37 DP0 TP0] Decode batch. #running-req: 1, #token: 984, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.78, #queue-req: 0, 
[2025-07-28 01:26:37 DP1 TP0] Decode batch. #running-req: 1, #token: 292, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.77, #queue-req: 0, 
[2025-07-28 01:26:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1024, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.86, #queue-req: 0, 
[2025-07-28 01:26:37 DP1 TP0] Decode batch. #running-req: 1, #token: 332, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.87, #queue-req: 0, 
[2025-07-28 01:26:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1064, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.02, #queue-req: 0, 
[2025-07-28 01:26:37 DP1 TP0] Decode batch. #running-req: 1, #token: 372, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.87, #queue-req: 0, 
[2025-07-28 01:26:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1104, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.92, #queue-req: 0, 
[2025-07-28 01:26:37 DP1 TP0] Decode batch. #running-req: 1, #token: 412, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.82, #queue-req: 0, 
[2025-07-28 01:26:37] INFO:     127.0.0.1:33332 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:26:37 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 122, #cached-token: 90, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:26:37 DP1 TP0] Decode batch. #running-req: 1, #token: 452, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.93, #queue-req: 0, 
[2025-07-28 01:26:37 DP0 TP0] Decode batch. #running-req: 1, #token: 220, token usage: 0.00, cuda graph: True, gen throughput (token/s): 174.05, #queue-req: 0, 
[2025-07-28 01:26:38 DP1 TP0] Decode batch. #running-req: 1, #token: 492, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.89, #queue-req: 0, 
[2025-07-28 01:26:38 DP0 TP0] Decode batch. #running-req: 1, #token: 260, token usage: 0.00, cuda graph: True, gen throughput (token/s): 215.43, #queue-req: 0, 
[2025-07-28 01:26:38 DP1 TP0] Decode batch. #running-req: 1, #token: 532, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.70, #queue-req: 0, 
[2025-07-28 01:26:38 DP0 TP0] Decode batch. #running-req: 1, #token: 300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.70, #queue-req: 0, 
[2025-07-28 01:26:38 DP1 TP0] Decode batch. #running-req: 1, #token: 572, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.49, #queue-req: 0, 
[2025-07-28 01:26:38 DP0 TP0] Decode batch. #running-req: 1, #token: 340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.90, #queue-req: 0, 
[2025-07-28 01:26:38 DP1 TP0] Decode batch. #running-req: 1, #token: 612, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.42, #queue-req: 0, 
[2025-07-28 01:26:38 DP0 TP0] Decode batch. #running-req: 1, #token: 380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.91, #queue-req: 0, 
[2025-07-28 01:26:38 DP1 TP0] Decode batch. #running-req: 1, #token: 652, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.42, #queue-req: 0, 
[2025-07-28 01:26:38 DP0 TP0] Decode batch. #running-req: 1, #token: 420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.01, #queue-req: 0, 
[2025-07-28 01:26:39 DP1 TP0] Decode batch. #running-req: 1, #token: 692, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.68, #queue-req: 0, 
[2025-07-28 01:26:39 DP0 TP0] Decode batch. #running-req: 1, #token: 460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.02, #queue-req: 0, 
[2025-07-28 01:26:39 DP1 TP0] Decode batch. #running-req: 1, #token: 732, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.65, #queue-req: 0, 
[2025-07-28 01:26:39 DP0 TP0] Decode batch. #running-req: 1, #token: 500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.94, #queue-req: 0, 
[2025-07-28 01:26:39 DP1 TP0] Decode batch. #running-req: 1, #token: 772, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.67, #queue-req: 0, 
[2025-07-28 01:26:39 DP0 TP0] Decode batch. #running-req: 1, #token: 540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.83, #queue-req: 0, 
[2025-07-28 01:26:39 DP1 TP0] Decode batch. #running-req: 1, #token: 812, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.30, #queue-req: 0, 
[2025-07-28 01:26:39 DP0 TP0] Decode batch. #running-req: 1, #token: 580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.72, #queue-req: 0, 
[2025-07-28 01:26:39 DP1 TP0] Decode batch. #running-req: 1, #token: 852, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.42, #queue-req: 0, 
[2025-07-28 01:26:39 DP0 TP0] Decode batch. #running-req: 1, #token: 620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.77, #queue-req: 0, 
[2025-07-28 01:26:39 DP1 TP0] Decode batch. #running-req: 1, #token: 892, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.27, #queue-req: 0, 
[2025-07-28 01:26:40 DP0 TP0] Decode batch. #running-req: 1, #token: 660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.64, #queue-req: 0, 
[2025-07-28 01:26:40 DP1 TP0] Decode batch. #running-req: 1, #token: 932, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.42, #queue-req: 0, 
[2025-07-28 01:26:40 DP0 TP0] Decode batch. #running-req: 1, #token: 700, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.85, #queue-req: 0, 
[2025-07-28 01:26:40 DP1 TP0] Decode batch. #running-req: 1, #token: 972, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.40, #queue-req: 0, 
[2025-07-28 01:26:40 DP0 TP0] Decode batch. #running-req: 1, #token: 740, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.79, #queue-req: 0, 
[2025-07-28 01:26:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1012, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.38, #queue-req: 0, 
[2025-07-28 01:26:40 DP0 TP0] Decode batch. #running-req: 1, #token: 780, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.73, #queue-req: 0, 
[2025-07-28 01:26:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1052, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.86, #queue-req: 0, 
[2025-07-28 01:26:40 DP0 TP0] Decode batch. #running-req: 1, #token: 820, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.47, #queue-req: 0, 
[2025-07-28 01:26:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1092, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.64, #queue-req: 0, 
[2025-07-28 01:26:40 DP0 TP0] Decode batch. #running-req: 1, #token: 860, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.56, #queue-req: 0, 
[2025-07-28 01:26:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1132, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.65, #queue-req: 0, 
[2025-07-28 01:26:41 DP0 TP0] Decode batch. #running-req: 1, #token: 900, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.51, #queue-req: 0, 
[2025-07-28 01:26:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1172, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.77, #queue-req: 0, 
[2025-07-28 01:26:41 DP0 TP0] Decode batch. #running-req: 1, #token: 940, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.79, #queue-req: 0, 
[2025-07-28 01:26:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1212, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.78, #queue-req: 0, 
[2025-07-28 01:26:41 DP0 TP0] Decode batch. #running-req: 1, #token: 980, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.78, #queue-req: 0, 
[2025-07-28 01:26:41] INFO:     127.0.0.1:33342 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:26:41 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 135, #cached-token: 91, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:26:41 DP0 TP0] Decode batch. #running-req: 1, #token: 1020, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.83, #queue-req: 0, 
[2025-07-28 01:26:41 DP1 TP0] Decode batch. #running-req: 1, #token: 259, token usage: 0.00, cuda graph: True, gen throughput (token/s): 175.27, #queue-req: 0, 
[2025-07-28 01:26:41 DP0 TP0] Decode batch. #running-req: 1, #token: 1060, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.05, #queue-req: 0, 
[2025-07-28 01:26:41 DP1 TP0] Decode batch. #running-req: 1, #token: 299, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.29, #queue-req: 0, 
[2025-07-28 01:26:41] INFO:     127.0.0.1:33354 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:26:42 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 98, #cached-token: 90, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:26:42 DP1 TP0] Decode batch. #running-req: 1, #token: 339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.51, #queue-req: 0, 
[2025-07-28 01:26:42 DP0 TP0] Decode batch. #running-req: 1, #token: 204, token usage: 0.00, cuda graph: True, gen throughput (token/s): 171.65, #queue-req: 0, 
[2025-07-28 01:26:42 DP1 TP0] Decode batch. #running-req: 1, #token: 379, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.69, #queue-req: 0, 
[2025-07-28 01:26:42 DP0 TP0] Decode batch. #running-req: 1, #token: 244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 216.41, #queue-req: 0, 
[2025-07-28 01:26:42 DP1 TP0] Decode batch. #running-req: 1, #token: 419, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.71, #queue-req: 0, 
[2025-07-28 01:26:42 DP0 TP0] Decode batch. #running-req: 1, #token: 284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.05, #queue-req: 0, 
[2025-07-28 01:26:42 DP1 TP0] Decode batch. #running-req: 1, #token: 459, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.73, #queue-req: 0, 
[2025-07-28 01:26:42 DP0 TP0] Decode batch. #running-req: 1, #token: 324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.41, #queue-req: 0, 
[2025-07-28 01:26:42 DP1 TP0] Decode batch. #running-req: 1, #token: 499, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.77, #queue-req: 0, 
[2025-07-28 01:26:42 DP0 TP0] Decode batch. #running-req: 1, #token: 364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.64, #queue-req: 0, 
[2025-07-28 01:26:43 DP1 TP0] Decode batch. #running-req: 1, #token: 539, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.49, #queue-req: 0, 
[2025-07-28 01:26:43 DP0 TP0] Decode batch. #running-req: 1, #token: 404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.65, #queue-req: 0, 
[2025-07-28 01:26:43 DP1 TP0] Decode batch. #running-req: 1, #token: 579, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.30, #queue-req: 0, 
[2025-07-28 01:26:43 DP0 TP0] Decode batch. #running-req: 1, #token: 444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.65, #queue-req: 0, 
[2025-07-28 01:26:43 DP1 TP0] Decode batch. #running-req: 1, #token: 619, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.38, #queue-req: 0, 
[2025-07-28 01:26:43 DP0 TP0] Decode batch. #running-req: 1, #token: 484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.77, #queue-req: 0, 
[2025-07-28 01:26:43 DP1 TP0] Decode batch. #running-req: 1, #token: 659, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.37, #queue-req: 0, 
[2025-07-28 01:26:43 DP0 TP0] Decode batch. #running-req: 1, #token: 524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.51, #queue-req: 0, 
[2025-07-28 01:26:43 DP1 TP0] Decode batch. #running-req: 1, #token: 699, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.54, #queue-req: 0, 
[2025-07-28 01:26:43 DP0 TP0] Decode batch. #running-req: 1, #token: 564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.36, #queue-req: 0, 
[2025-07-28 01:26:43 DP1 TP0] Decode batch. #running-req: 1, #token: 739, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.59, #queue-req: 0, 
[2025-07-28 01:26:43 DP0 TP0] Decode batch. #running-req: 1, #token: 604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.66, #queue-req: 0, 
[2025-07-28 01:26:44 DP1 TP0] Decode batch. #running-req: 1, #token: 779, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.37, #queue-req: 0, 
[2025-07-28 01:26:44 DP0 TP0] Decode batch. #running-req: 1, #token: 644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.41, #queue-req: 0, 
[2025-07-28 01:26:44 DP1 TP0] Decode batch. #running-req: 1, #token: 819, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.20, #queue-req: 0, 
[2025-07-28 01:26:44 DP0 TP0] Decode batch. #running-req: 1, #token: 684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.47, #queue-req: 0, 
[2025-07-28 01:26:44 DP1 TP0] Decode batch. #running-req: 1, #token: 859, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.23, #queue-req: 0, 
[2025-07-28 01:26:44 DP0 TP0] Decode batch. #running-req: 1, #token: 724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.62, #queue-req: 0, 
[2025-07-28 01:26:44 DP1 TP0] Decode batch. #running-req: 1, #token: 899, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.20, #queue-req: 0, 
[2025-07-28 01:26:44 DP0 TP0] Decode batch. #running-req: 1, #token: 764, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.71, #queue-req: 0, 
[2025-07-28 01:26:44 DP1 TP0] Decode batch. #running-req: 1, #token: 939, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.26, #queue-req: 0, 
[2025-07-28 01:26:44 DP0 TP0] Decode batch. #running-req: 1, #token: 804, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.50, #queue-req: 0, 
[2025-07-28 01:26:45 DP1 TP0] Decode batch. #running-req: 1, #token: 979, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.41, #queue-req: 0, 
[2025-07-28 01:26:45 DP0 TP0] Decode batch. #running-req: 1, #token: 844, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.50, #queue-req: 0, 
[2025-07-28 01:26:45 DP1 TP0] Decode batch. #running-req: 1, #token: 1019, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.10, #queue-req: 0, 
[2025-07-28 01:26:45 DP0 TP0] Decode batch. #running-req: 1, #token: 884, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.54, #queue-req: 0, 
[2025-07-28 01:26:45 DP1 TP0] Decode batch. #running-req: 1, #token: 1059, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.73, #queue-req: 0, 
[2025-07-28 01:26:45 DP0 TP0] Decode batch. #running-req: 1, #token: 924, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.68, #queue-req: 0, 
[2025-07-28 01:26:45] INFO:     127.0.0.1:33358 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:26:45 DP1 TP0] Decode batch. #running-req: 1, #token: 0, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.74, #queue-req: 0, 
[2025-07-28 01:26:45 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 85, #cached-token: 90, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:26:45 DP0 TP0] Decode batch. #running-req: 1, #token: 964, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.71, #queue-req: 0, 
[2025-07-28 01:26:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1004, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.86, #queue-req: 0, 
[2025-07-28 01:26:45 DP1 TP0] Decode batch. #running-req: 1, #token: 216, token usage: 0.00, cuda graph: True, gen throughput (token/s): 177.61, #queue-req: 0, 
[2025-07-28 01:26:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1044, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.99, #queue-req: 0, 
[2025-07-28 01:26:46 DP1 TP0] Decode batch. #running-req: 1, #token: 256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 215.90, #queue-req: 0, 
[2025-07-28 01:26:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1084, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.51, #queue-req: 0, 
[2025-07-28 01:26:46 DP1 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.96, #queue-req: 0, 
[2025-07-28 01:26:46 DP1 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.39, #queue-req: 0, 
[2025-07-28 01:26:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1124, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.77, #queue-req: 0, 
[2025-07-28 01:26:46 DP1 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.48, #queue-req: 0, 
[2025-07-28 01:26:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1164, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.98, #queue-req: 0, 
[2025-07-28 01:26:46 DP1 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.85, #queue-req: 0, 
[2025-07-28 01:26:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1204, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.19, #queue-req: 0, 
[2025-07-28 01:26:46 DP1 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.72, #queue-req: 0, 
[2025-07-28 01:26:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.28, #queue-req: 0, 
[2025-07-28 01:26:47] INFO:     127.0.0.1:33366 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:26:47 DP1 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.73, #queue-req: 0, 
[2025-07-28 01:26:47 DP1 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.44, #queue-req: 0, 
[2025-07-28 01:26:47 DP1 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.28, #queue-req: 0, 
[2025-07-28 01:26:47 DP1 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.26, #queue-req: 0, 
[2025-07-28 01:26:47 DP1 TP0] Decode batch. #running-req: 1, #token: 656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.58, #queue-req: 0, 
[2025-07-28 01:26:48 DP1 TP0] Decode batch. #running-req: 1, #token: 696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.42, #queue-req: 0, 
[2025-07-28 01:26:48 DP1 TP0] Decode batch. #running-req: 1, #token: 736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.44, #queue-req: 0, 
[2025-07-28 01:26:48] INFO:     127.0.0.1:59442 - "POST /v1/chat/completions HTTP/1.1" 200 OK

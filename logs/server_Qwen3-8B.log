[2025-07-28 01:06:50] server_args=ServerArgs(model_path='/data/home/<USER>/repos/anesbench/share/models/Qwen3-8B', tokenizer_path='/data/home/<USER>/repos/anesbench/share/models/Qwen3-8B', tokenizer_mode='auto', skip_tokenizer_init=False, load_format='auto', model_loader_extra_config='{}', trust_remote_code=False, context_length=32768, is_embedding=False, enable_multimodal=None, revision=None, model_impl='auto', host='127.0.0.1', port=8006, skip_server_warmup=False, warmups=None, nccl_port=None, dtype='auto', quantization=None, quantization_param_path=None, kv_cache_dtype='auto', mem_fraction_static=0.85, max_running_requests=None, max_total_tokens=None, chunked_prefill_size=8192, max_prefill_tokens=16384, schedule_policy='fcfs', schedule_conservativeness=1.0, cpu_offload_gb=0, page_size=1, hybrid_kvcache_ratio=None, swa_full_tokens_ratio=0.8, disable_hybrid_swa_memory=False, device='cuda', tp_size=4, pp_size=1, max_micro_batch_size=None, stream_interval=1, stream_output=False, random_seed=791287618, constrained_json_whitespace_pattern=None, watchdog_timeout=300, dist_timeout=None, download_dir=None, base_gpu_id=0, gpu_id_step=1, sleep_on_idle=False, log_level='info', log_level_http=None, log_requests=False, log_requests_level=0, crash_dump_folder=None, show_time_cost=True, enable_metrics=True, enable_metrics_for_all_schedulers=False, bucket_time_to_first_token=None, bucket_inter_token_latency=None, bucket_e2e_request_latency=None, collect_tokens_histogram=False, decode_log_interval=40, enable_request_time_stats_logging=False, kv_events_config=None, api_key=None, served_model_name='/data/home/<USER>/repos/anesbench/share/models/Qwen3-8B', chat_template=None, completion_template=None, file_storage_path='sglang_storage', enable_cache_report=False, reasoning_parser=None, tool_call_parser=None, dp_size=2, load_balance_method='round_robin', dist_init_addr=None, nnodes=1, node_rank=0, json_model_override_args='{}', preferred_sampling_params=None, enable_lora=None, max_lora_rank=None, lora_target_modules=None, lora_paths=None, max_loras_per_batch=8, lora_backend='triton', attention_backend=None, sampling_backend='flashinfer', grammar_backend='xgrammar', mm_attention_backend=None, speculative_algorithm=None, speculative_draft_model_path=None, speculative_num_steps=None, speculative_eagle_topk=None, speculative_num_draft_tokens=None, speculative_accept_threshold_single=1.0, speculative_accept_threshold_acc=1.0, speculative_token_map=None, ep_size=1, enable_ep_moe=False, enable_deepep_moe=False, enable_flashinfer_moe=False, enable_flashinfer_allreduce_fusion=False, deepep_mode='auto', ep_num_redundant_experts=0, ep_dispatch_algorithm='static', init_expert_location='trivial', enable_eplb=False, eplb_algorithm='auto', eplb_rebalance_num_iterations=1000, eplb_rebalance_layers_per_chunk=None, expert_distribution_recorder_mode=None, expert_distribution_recorder_buffer_size=1000, enable_expert_distribution_metrics=False, deepep_config=None, moe_dense_tp_size=None, enable_hierarchical_cache=False, hicache_ratio=2.0, hicache_size=0, hicache_write_policy='write_through_selective', hicache_io_backend='', hicache_storage_backend=None, enable_double_sparsity=False, ds_channel_config_path=None, ds_heavy_channel_num=32, ds_heavy_token_num=256, ds_heavy_channel_type='qk', ds_sparse_decode_threshold=4096, disable_radix_cache=False, cuda_graph_max_bs=None, cuda_graph_bs=None, disable_cuda_graph=False, disable_cuda_graph_padding=False, enable_profile_cuda_graph=False, enable_nccl_nvls=False, enable_tokenizer_batch_encode=False, disable_outlines_disk_cache=False, disable_custom_all_reduce=False, enable_mscclpp=False, disable_overlap_schedule=False, enable_mixed_chunk=False, enable_dp_attention=False, enable_dp_lm_head=False, enable_two_batch_overlap=False, enable_torch_compile=False, torch_compile_max_bs=32, torchao_config='', enable_nan_detection=False, enable_p2p_check=False, triton_attention_reduce_in_fp32=False, triton_attention_num_kv_splits=8, num_continuous_decode_steps=1, delete_ckpt_after_loading=False, enable_memory_saver=False, allow_auto_truncate=False, enable_custom_logit_processor=False, flashinfer_mla_disable_ragged=False, disable_shared_experts_fusion=False, disable_chunked_prefix_cache=False, disable_fast_image_processor=False, enable_return_hidden_states=False, enable_triton_kernel_moe=False, debug_tensor_dump_output_folder=None, debug_tensor_dump_input_file=None, debug_tensor_dump_inject=False, debug_tensor_dump_prefill_only=False, disaggregation_mode='null', disaggregation_transfer_backend='mooncake', disaggregation_bootstrap_port=8998, disaggregation_decode_tp=None, disaggregation_decode_dp=None, disaggregation_prefill_pp=1, disaggregation_ib_device=None, num_reserved_decode_tokens=512, pdlb_url=None, custom_weight_loader=[], weight_loader_disable_mmap=False, enable_pdmux=False, sm_group_num=3)
[2025-07-28 01:06:56] Launch DP0 starting at GPU #0.
[2025-07-28 01:06:56] Launch DP1 starting at GPU #4.
[2025-07-28 01:07:04 DP1 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 01:07:04 DP1 TP0] Init torch distributed begin.
[2025-07-28 01:07:04 DP0 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 01:07:04 DP0 TP0] Init torch distributed begin.
[2025-07-28 01:07:05 DP0 TP0] sglang is using nccl==2.26.2
[2025-07-28 01:07:05 DP1 TP0] sglang is using nccl==2.26.2
[2025-07-28 01:07:06 DP0 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 01:07:06 DP1 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 01:07:07 DP0 TP0] Load weight begin. avail mem=78.21 GB
[2025-07-28 01:07:07 DP1 TP0] Load weight begin. avail mem=78.21 GB

Loading safetensors checkpoint shards:   0% Completed | 0/5 [00:00<?, ?it/s]

Loading safetensors checkpoint shards:   0% Completed | 0/5 [00:00<?, ?it/s]

Loading safetensors checkpoint shards:  20% Completed | 1/5 [00:01<00:05,  1.47s/it]

Loading safetensors checkpoint shards:  20% Completed | 1/5 [00:01<00:05,  1.44s/it]

Loading safetensors checkpoint shards:  40% Completed | 2/5 [00:02<00:04,  1.43s/it]

Loading safetensors checkpoint shards:  40% Completed | 2/5 [00:02<00:04,  1.41s/it]

Loading safetensors checkpoint shards:  60% Completed | 3/5 [00:03<00:01,  1.17it/s]

Loading safetensors checkpoint shards:  60% Completed | 3/5 [00:03<00:01,  1.15it/s]

Loading safetensors checkpoint shards:  80% Completed | 4/5 [00:04<00:00,  1.04it/s]

Loading safetensors checkpoint shards:  80% Completed | 4/5 [00:04<00:00,  1.05it/s]

Loading safetensors checkpoint shards: 100% Completed | 5/5 [00:05<00:00,  1.12s/it]

Loading safetensors checkpoint shards: 100% Completed | 5/5 [00:05<00:00,  1.12s/it]

Loading safetensors checkpoint shards: 100% Completed | 5/5 [00:05<00:00,  1.11s/it]

Loading safetensors checkpoint shards: 100% Completed | 5/5 [00:05<00:00,  1.11s/it]


[2025-07-28 01:07:12 DP1 TP0] Load weight end. type=Qwen3ForCausalLM, dtype=torch.bfloat16, avail mem=74.37 GB, mem usage=3.84 GB.
[2025-07-28 01:07:12 DP0 TP0] Load weight end. type=Qwen3ForCausalLM, dtype=torch.bfloat16, avail mem=74.37 GB, mem usage=3.84 GB.
[2025-07-28 01:07:13 DP0 TP3] KV Cache is allocated. #tokens: 1822055, K size: 31.28 GB, V size: 31.28 GB
[2025-07-28 01:07:13 DP0 TP1] KV Cache is allocated. #tokens: 1822055, K size: 31.28 GB, V size: 31.28 GB
[2025-07-28 01:07:13 DP1 TP3] KV Cache is allocated. #tokens: 1822055, K size: 31.28 GB, V size: 31.28 GB
[2025-07-28 01:07:13 DP1 TP0] KV Cache is allocated. #tokens: 1822055, K size: 31.28 GB, V size: 31.28 GB
[2025-07-28 01:07:13 DP0 TP2] KV Cache is allocated. #tokens: 1822055, K size: 31.28 GB, V size: 31.28 GB
[2025-07-28 01:07:13 DP1 TP1] KV Cache is allocated. #tokens: 1822055, K size: 31.28 GB, V size: 31.28 GB
[2025-07-28 01:07:13 DP1 TP0] Memory pool end. avail mem=11.27 GB
[2025-07-28 01:07:13 DP0 TP0] KV Cache is allocated. #tokens: 1822055, K size: 31.28 GB, V size: 31.28 GB
[2025-07-28 01:07:13 DP1 TP2] KV Cache is allocated. #tokens: 1822055, K size: 31.28 GB, V size: 31.28 GB
[2025-07-28 01:07:13 DP0 TP0] Memory pool end. avail mem=11.27 GB
[2025-07-28 01:07:13 DP0 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.66 GB
[2025-07-28 01:07:13 DP1 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.66 GB
[2025-07-28 01:07:13 DP1 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.62 GB):   0%|          | 0/23 [00:00<?, ?it/s][2025-07-28 01:07:13 DP0 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.62 GB):   0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.62 GB):   4%|▍         | 1/23 [00:01<00:25,  1.16s/it]
Capturing batches (bs=152 avail_mem=10.37 GB):   4%|▍         | 1/23 [00:01<00:25,  1.16s/it]
Capturing batches (bs=160 avail_mem=10.62 GB):   4%|▍         | 1/23 [00:01<00:26,  1.18s/it]
Capturing batches (bs=152 avail_mem=10.37 GB):   4%|▍         | 1/23 [00:01<00:26,  1.18s/it]
Capturing batches (bs=152 avail_mem=10.37 GB):   9%|▊         | 2/23 [00:01<00:14,  1.44it/s]
Capturing batches (bs=144 avail_mem=10.26 GB):   9%|▊         | 2/23 [00:01<00:14,  1.44it/s]
Capturing batches (bs=152 avail_mem=10.37 GB):   9%|▊         | 2/23 [00:01<00:14,  1.40it/s]
Capturing batches (bs=144 avail_mem=10.26 GB):   9%|▊         | 2/23 [00:01<00:14,  1.40it/s]
Capturing batches (bs=144 avail_mem=10.26 GB):  13%|█▎        | 3/23 [00:02<00:12,  1.63it/s]
Capturing batches (bs=136 avail_mem=10.17 GB):  13%|█▎        | 3/23 [00:02<00:12,  1.63it/s]
Capturing batches (bs=144 avail_mem=10.26 GB):  13%|█▎        | 3/23 [00:02<00:12,  1.61it/s]
Capturing batches (bs=136 avail_mem=10.17 GB):  13%|█▎        | 3/23 [00:02<00:12,  1.61it/s]
Capturing batches (bs=136 avail_mem=10.17 GB):  17%|█▋        | 4/23 [00:02<00:09,  2.01it/s]
Capturing batches (bs=128 avail_mem=10.06 GB):  17%|█▋        | 4/23 [00:02<00:09,  2.01it/s]
Capturing batches (bs=136 avail_mem=10.17 GB):  17%|█▋        | 4/23 [00:02<00:09,  1.97it/s]
Capturing batches (bs=128 avail_mem=10.06 GB):  17%|█▋        | 4/23 [00:02<00:09,  1.97it/s]
Capturing batches (bs=128 avail_mem=10.06 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.27it/s]
Capturing batches (bs=120 avail_mem=9.97 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.27it/s] 
Capturing batches (bs=128 avail_mem=10.06 GB):  22%|██▏       | 5/23 [00:02<00:08,  2.22it/s]
Capturing batches (bs=120 avail_mem=9.97 GB):  22%|██▏       | 5/23 [00:02<00:08,  2.22it/s] 
Capturing batches (bs=120 avail_mem=9.97 GB):  26%|██▌       | 6/23 [00:03<00:06,  2.49it/s]
Capturing batches (bs=112 avail_mem=9.87 GB):  26%|██▌       | 6/23 [00:03<00:06,  2.49it/s]
Capturing batches (bs=120 avail_mem=9.97 GB):  26%|██▌       | 6/23 [00:03<00:06,  2.44it/s]
Capturing batches (bs=112 avail_mem=9.87 GB):  26%|██▌       | 6/23 [00:03<00:06,  2.44it/s]
Capturing batches (bs=112 avail_mem=9.87 GB):  30%|███       | 7/23 [00:03<00:06,  2.64it/s]
Capturing batches (bs=104 avail_mem=9.79 GB):  30%|███       | 7/23 [00:03<00:06,  2.64it/s]
Capturing batches (bs=112 avail_mem=9.87 GB):  30%|███       | 7/23 [00:03<00:06,  2.59it/s]
Capturing batches (bs=104 avail_mem=9.79 GB):  30%|███       | 7/23 [00:03<00:06,  2.59it/s]
Capturing batches (bs=104 avail_mem=9.79 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.76it/s]
Capturing batches (bs=96 avail_mem=9.70 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.76it/s] 
Capturing batches (bs=104 avail_mem=9.79 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.70it/s]
Capturing batches (bs=96 avail_mem=9.70 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.70it/s] 
Capturing batches (bs=96 avail_mem=9.70 GB):  39%|███▉      | 9/23 [00:04<00:04,  2.87it/s]
Capturing batches (bs=88 avail_mem=9.63 GB):  39%|███▉      | 9/23 [00:04<00:04,  2.87it/s]
Capturing batches (bs=96 avail_mem=9.70 GB):  39%|███▉      | 9/23 [00:04<00:04,  2.82it/s]
Capturing batches (bs=88 avail_mem=9.63 GB):  39%|███▉      | 9/23 [00:04<00:04,  2.82it/s]
Capturing batches (bs=88 avail_mem=9.63 GB):  43%|████▎     | 10/23 [00:04<00:04,  2.94it/s]
Capturing batches (bs=80 avail_mem=9.54 GB):  43%|████▎     | 10/23 [00:04<00:04,  2.94it/s]
Capturing batches (bs=88 avail_mem=9.63 GB):  43%|████▎     | 10/23 [00:04<00:04,  2.94it/s]
Capturing batches (bs=80 avail_mem=9.54 GB):  43%|████▎     | 10/23 [00:04<00:04,  2.94it/s]
Capturing batches (bs=80 avail_mem=9.54 GB):  48%|████▊     | 11/23 [00:04<00:04,  2.97it/s]
Capturing batches (bs=72 avail_mem=9.53 GB):  48%|████▊     | 11/23 [00:04<00:04,  2.97it/s]
Capturing batches (bs=80 avail_mem=9.54 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.02it/s]
Capturing batches (bs=72 avail_mem=9.53 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.02it/s]
Capturing batches (bs=72 avail_mem=9.53 GB):  52%|█████▏    | 12/23 [00:04<00:03,  2.97it/s]
Capturing batches (bs=64 avail_mem=9.45 GB):  52%|█████▏    | 12/23 [00:04<00:03,  2.97it/s]
Capturing batches (bs=72 avail_mem=9.53 GB):  52%|█████▏    | 12/23 [00:05<00:03,  3.03it/s]
Capturing batches (bs=64 avail_mem=9.45 GB):  52%|█████▏    | 12/23 [00:05<00:03,  3.03it/s]
Capturing batches (bs=64 avail_mem=9.45 GB):  57%|█████▋    | 13/23 [00:05<00:03,  3.01it/s]
Capturing batches (bs=56 avail_mem=9.41 GB):  57%|█████▋    | 13/23 [00:05<00:03,  3.01it/s]
Capturing batches (bs=64 avail_mem=9.45 GB):  57%|█████▋    | 13/23 [00:05<00:03,  2.87it/s]
Capturing batches (bs=56 avail_mem=9.41 GB):  57%|█████▋    | 13/23 [00:05<00:03,  2.87it/s]
Capturing batches (bs=56 avail_mem=9.41 GB):  61%|██████    | 14/23 [00:05<00:02,  3.02it/s]
Capturing batches (bs=48 avail_mem=9.35 GB):  61%|██████    | 14/23 [00:05<00:02,  3.02it/s]
Capturing batches (bs=56 avail_mem=9.41 GB):  61%|██████    | 14/23 [00:05<00:03,  2.91it/s]
Capturing batches (bs=48 avail_mem=9.35 GB):  61%|██████    | 14/23 [00:05<00:03,  2.91it/s]
Capturing batches (bs=48 avail_mem=9.35 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.03it/s]
Capturing batches (bs=40 avail_mem=9.34 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.03it/s]
Capturing batches (bs=48 avail_mem=9.35 GB):  65%|██████▌   | 15/23 [00:06<00:02,  2.98it/s]
Capturing batches (bs=40 avail_mem=9.34 GB):  65%|██████▌   | 15/23 [00:06<00:02,  2.98it/s]
Capturing batches (bs=40 avail_mem=9.34 GB):  70%|██████▉   | 16/23 [00:06<00:02,  3.03it/s]
Capturing batches (bs=32 avail_mem=9.29 GB):  70%|██████▉   | 16/23 [00:06<00:02,  3.03it/s]
Capturing batches (bs=40 avail_mem=9.34 GB):  70%|██████▉   | 16/23 [00:06<00:02,  2.93it/s]
Capturing batches (bs=32 avail_mem=9.29 GB):  70%|██████▉   | 16/23 [00:06<00:02,  2.93it/s]
Capturing batches (bs=32 avail_mem=9.29 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.01it/s]
Capturing batches (bs=24 avail_mem=9.26 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.01it/s]
Capturing batches (bs=32 avail_mem=9.29 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.06it/s]
Capturing batches (bs=24 avail_mem=9.26 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.06it/s]
Capturing batches (bs=24 avail_mem=9.26 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.02it/s]
Capturing batches (bs=16 avail_mem=9.24 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.02it/s]
Capturing batches (bs=24 avail_mem=9.26 GB):  78%|███████▊  | 18/23 [00:07<00:01,  2.97it/s]
Capturing batches (bs=16 avail_mem=9.24 GB):  78%|███████▊  | 18/23 [00:07<00:01,  2.97it/s]
Capturing batches (bs=16 avail_mem=9.24 GB):  83%|████████▎ | 19/23 [00:07<00:01,  3.05it/s]
Capturing batches (bs=8 avail_mem=9.23 GB):  83%|████████▎ | 19/23 [00:07<00:01,  3.05it/s] 
Capturing batches (bs=16 avail_mem=9.24 GB):  83%|████████▎ | 19/23 [00:07<00:01,  2.93it/s]
Capturing batches (bs=8 avail_mem=9.23 GB):  83%|████████▎ | 19/23 [00:07<00:01,  2.93it/s] 
Capturing batches (bs=8 avail_mem=9.23 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.11it/s]
Capturing batches (bs=4 avail_mem=9.20 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.11it/s]
Capturing batches (bs=8 avail_mem=9.23 GB):  87%|████████▋ | 20/23 [00:07<00:01,  2.85it/s]
Capturing batches (bs=4 avail_mem=9.20 GB):  87%|████████▋ | 20/23 [00:07<00:01,  2.85it/s]
Capturing batches (bs=4 avail_mem=9.20 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.23it/s]
Capturing batches (bs=2 avail_mem=9.19 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.23it/s]
Capturing batches (bs=4 avail_mem=9.20 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.01it/s]
Capturing batches (bs=2 avail_mem=9.19 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.01it/s]
Capturing batches (bs=2 avail_mem=9.19 GB):  96%|█████████▌| 22/23 [00:08<00:00,  3.25it/s]
Capturing batches (bs=1 avail_mem=9.17 GB):  96%|█████████▌| 22/23 [00:08<00:00,  3.25it/s]
Capturing batches (bs=2 avail_mem=9.19 GB):  96%|█████████▌| 22/23 [00:08<00:00,  3.08it/s]
Capturing batches (bs=1 avail_mem=9.17 GB):  96%|█████████▌| 22/23 [00:08<00:00,  3.08it/s][2025-07-28 01:07:22 DP1 TP3] Registering 1679 cuda graph addresses
[2025-07-28 01:07:22 DP1 TP1] Registering 1679 cuda graph addresses
[2025-07-28 01:07:22 DP1 TP2] Registering 1679 cuda graph addresses

Capturing batches (bs=1 avail_mem=9.17 GB): 100%|██████████| 23/23 [00:08<00:00,  2.99it/s]
Capturing batches (bs=1 avail_mem=9.17 GB): 100%|██████████| 23/23 [00:08<00:00,  2.65it/s]
[2025-07-28 01:07:22 DP1 TP0] Registering 1679 cuda graph addresses
[2025-07-28 01:07:22 DP0 TP3] Registering 1679 cuda graph addresses
[2025-07-28 01:07:22 DP0 TP2] Registering 1679 cuda graph addresses

Capturing batches (bs=1 avail_mem=9.17 GB): 100%|██████████| 23/23 [00:08<00:00,  2.90it/s]
Capturing batches (bs=1 avail_mem=9.17 GB): 100%|██████████| 23/23 [00:08<00:00,  2.65it/s]
[2025-07-28 01:07:22 DP0 TP0] Registering 1679 cuda graph addresses
[2025-07-28 01:07:22 DP1 TP0] Capture cuda graph end. Time elapsed: 8.88 s. mem usage=1.49 GB. avail mem=9.16 GB.
[2025-07-28 01:07:22 DP0 TP1] Registering 1679 cuda graph addresses
[2025-07-28 01:07:22 DP0 TP0] Capture cuda graph end. Time elapsed: 9.00 s. mem usage=1.49 GB. avail mem=9.16 GB.
[2025-07-28 01:07:22 DP1 TP0] max_total_num_tokens=1822055, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=9.16 GB
[2025-07-28 01:07:22 DP0 TP0] max_total_num_tokens=1822055, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=9.16 GB
[2025-07-28 01:07:23] INFO:     Started server process [1818034]
[2025-07-28 01:07:23] INFO:     Waiting for application startup.
[2025-07-28 01:07:23] INFO:     Application startup complete.
[2025-07-28 01:07:23] INFO:     Uvicorn running on http://127.0.0.1:8006 (Press CTRL+C to quit)
[2025-07-28 01:07:24] INFO:     127.0.0.1:60084 - "GET /get_model_info HTTP/1.1" 200 OK
[2025-07-28 01:07:24 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:07:24 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:07:25] INFO:     127.0.0.1:60106 - "GET /health HTTP/1.1" 200 OK
[2025-07-28 01:07:25 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 194, #cached-token: 0, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:07:25 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 235, #cached-token: 0, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:07:25] INFO:     127.0.0.1:60096 - "POST /generate HTTP/1.1" 200 OK
[2025-07-28 01:07:25] The server is fired up and ready to roll!
[2025-07-28 01:07:25 DP0 TP0] Decode batch. #running-req: 1, #token: 234, token usage: 0.00, cuda graph: True, gen throughput (token/s): 14.99, #queue-req: 0, 
[2025-07-28 01:07:25 DP1 TP0] Decode batch. #running-req: 1, #token: 275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 14.35, #queue-req: 0, 
[2025-07-28 01:07:25 DP0 TP0] Decode batch. #running-req: 1, #token: 274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.35, #queue-req: 0, 
[2025-07-28 01:07:26 DP1 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.22, #queue-req: 0, 
[2025-07-28 01:07:26 DP0 TP0] Decode batch. #running-req: 1, #token: 314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.10, #queue-req: 0, 
[2025-07-28 01:07:26 DP1 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.57, #queue-req: 0, 
[2025-07-28 01:07:26 DP0 TP0] Decode batch. #running-req: 1, #token: 354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.44, #queue-req: 0, 
[2025-07-28 01:07:26 DP1 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.67, #queue-req: 0, 
[2025-07-28 01:07:26 DP0 TP0] Decode batch. #running-req: 1, #token: 394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.53, #queue-req: 0, 
[2025-07-28 01:07:26 DP1 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.71, #queue-req: 0, 
[2025-07-28 01:07:26 DP0 TP0] Decode batch. #running-req: 1, #token: 434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.39, #queue-req: 0, 
[2025-07-28 01:07:26 DP1 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.99, #queue-req: 0, 
[2025-07-28 01:07:26 DP0 TP0] Decode batch. #running-req: 1, #token: 474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.86, #queue-req: 0, 
[2025-07-28 01:07:27 DP1 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.07, #queue-req: 0, 
[2025-07-28 01:07:27 DP0 TP0] Decode batch. #running-req: 1, #token: 514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.75, #queue-req: 0, 
[2025-07-28 01:07:27 DP1 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.56, #queue-req: 0, 
[2025-07-28 01:07:27 DP0 TP0] Decode batch. #running-req: 1, #token: 554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.28, #queue-req: 0, 
[2025-07-28 01:07:27 DP1 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.16, #queue-req: 0, 
[2025-07-28 01:07:27 DP0 TP0] Decode batch. #running-req: 1, #token: 594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.10, #queue-req: 0, 
[2025-07-28 01:07:27 DP1 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.60, #queue-req: 0, 
[2025-07-28 01:07:27 DP0 TP0] Decode batch. #running-req: 1, #token: 634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.96, #queue-req: 0, 
[2025-07-28 01:07:27 DP1 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.79, #queue-req: 0, 
[2025-07-28 01:07:27 DP0 TP0] Decode batch. #running-req: 1, #token: 674, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.72, #queue-req: 0, 
[2025-07-28 01:07:28 DP1 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.37, #queue-req: 0, 
[2025-07-28 01:07:28 DP0 TP0] Decode batch. #running-req: 1, #token: 714, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.56, #queue-req: 0, 
[2025-07-28 01:07:28 DP1 TP0] Decode batch. #running-req: 1, #token: 755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.17, #queue-req: 0, 
[2025-07-28 01:07:28 DP0 TP0] Decode batch. #running-req: 1, #token: 754, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.61, #queue-req: 0, 
[2025-07-28 01:07:28 DP1 TP0] Decode batch. #running-req: 1, #token: 795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.12, #queue-req: 0, 
[2025-07-28 01:07:28 DP0 TP0] Decode batch. #running-req: 1, #token: 794, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.27, #queue-req: 0, 
[2025-07-28 01:07:28 DP1 TP0] Decode batch. #running-req: 1, #token: 835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.38, #queue-req: 0, 
[2025-07-28 01:07:28 DP0 TP0] Decode batch. #running-req: 1, #token: 834, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.92, #queue-req: 0, 
[2025-07-28 01:07:28 DP1 TP0] Decode batch. #running-req: 1, #token: 875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.51, #queue-req: 0, 
[2025-07-28 01:07:28] INFO:     127.0.0.1:60108 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:07:28 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:07:29 DP1 TP0] Decode batch. #running-req: 1, #token: 915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.49, #queue-req: 0, 
[2025-07-28 01:07:29] INFO:     127.0.0.1:60112 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:07:29 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 207, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:07:29 DP0 TP0] Decode batch. #running-req: 1, #token: 192, token usage: 0.00, cuda graph: True, gen throughput (token/s): 89.67, #queue-req: 0, 
[2025-07-28 01:07:29 DP0 TP0] Decode batch. #running-req: 1, #token: 232, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.99, #queue-req: 0, 
[2025-07-28 01:07:29 DP1 TP0] Decode batch. #running-req: 1, #token: 330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 92.42, #queue-req: 0, 
[2025-07-28 01:07:29 DP0 TP0] Decode batch. #running-req: 1, #token: 272, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.68, #queue-req: 0, 
[2025-07-28 01:07:29 DP1 TP0] Decode batch. #running-req: 1, #token: 370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.26, #queue-req: 0, 
[2025-07-28 01:07:29 DP0 TP0] Decode batch. #running-req: 1, #token: 312, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.44, #queue-req: 0, 
[2025-07-28 01:07:29 DP1 TP0] Decode batch. #running-req: 1, #token: 410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.60, #queue-req: 0, 
[2025-07-28 01:07:30 DP0 TP0] Decode batch. #running-req: 1, #token: 352, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.53, #queue-req: 0, 
[2025-07-28 01:07:30 DP1 TP0] Decode batch. #running-req: 1, #token: 450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.97, #queue-req: 0, 
[2025-07-28 01:07:30 DP0 TP0] Decode batch. #running-req: 1, #token: 392, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.69, #queue-req: 0, 
[2025-07-28 01:07:30 DP1 TP0] Decode batch. #running-req: 1, #token: 490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.31, #queue-req: 0, 
[2025-07-28 01:07:30 DP0 TP0] Decode batch. #running-req: 1, #token: 432, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.21, #queue-req: 0, 
[2025-07-28 01:07:30 DP1 TP0] Decode batch. #running-req: 1, #token: 530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.40, #queue-req: 0, 
[2025-07-28 01:07:30 DP0 TP0] Decode batch. #running-req: 1, #token: 472, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.53, #queue-req: 0, 
[2025-07-28 01:07:30 DP1 TP0] Decode batch. #running-req: 1, #token: 570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.35, #queue-req: 0, 
[2025-07-28 01:07:30 DP0 TP0] Decode batch. #running-req: 1, #token: 512, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.66, #queue-req: 0, 
[2025-07-28 01:07:30 DP1 TP0] Decode batch. #running-req: 1, #token: 610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.42, #queue-req: 0, 
[2025-07-28 01:07:31 DP0 TP0] Decode batch. #running-req: 1, #token: 552, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.95, #queue-req: 0, 
[2025-07-28 01:07:31 DP1 TP0] Decode batch. #running-req: 1, #token: 650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.19, #queue-req: 0, 
[2025-07-28 01:07:31 DP0 TP0] Decode batch. #running-req: 1, #token: 592, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.10, #queue-req: 0, 
[2025-07-28 01:07:31 DP1 TP0] Decode batch. #running-req: 1, #token: 690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.51, #queue-req: 0, 
[2025-07-28 01:07:31 DP0 TP0] Decode batch. #running-req: 1, #token: 632, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.98, #queue-req: 0, 
[2025-07-28 01:07:31 DP1 TP0] Decode batch. #running-req: 1, #token: 730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.51, #queue-req: 0, 
[2025-07-28 01:07:31 DP0 TP0] Decode batch. #running-req: 1, #token: 672, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.85, #queue-req: 0, 
[2025-07-28 01:07:31 DP1 TP0] Decode batch. #running-req: 1, #token: 770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.56, #queue-req: 0, 
[2025-07-28 01:07:31 DP0 TP0] Decode batch. #running-req: 1, #token: 712, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.40, #queue-req: 0, 
[2025-07-28 01:07:31 DP1 TP0] Decode batch. #running-req: 1, #token: 810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.52, #queue-req: 0, 
[2025-07-28 01:07:32 DP0 TP0] Decode batch. #running-req: 1, #token: 752, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.20, #queue-req: 0, 
[2025-07-28 01:07:32 DP1 TP0] Decode batch. #running-req: 1, #token: 850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.42, #queue-req: 0, 
[2025-07-28 01:07:32 DP0 TP0] Decode batch. #running-req: 1, #token: 792, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.93, #queue-req: 0, 
[2025-07-28 01:07:32 DP1 TP0] Decode batch. #running-req: 1, #token: 890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.53, #queue-req: 0, 
[2025-07-28 01:07:32 DP0 TP0] Decode batch. #running-req: 1, #token: 832, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.04, #queue-req: 0, 
[2025-07-28 01:07:32 DP1 TP0] Decode batch. #running-req: 1, #token: 930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.64, #queue-req: 0, 
[2025-07-28 01:07:32 DP0 TP0] Decode batch. #running-req: 1, #token: 872, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.88, #queue-req: 0, 
[2025-07-28 01:07:32 DP1 TP0] Decode batch. #running-req: 1, #token: 970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.63, #queue-req: 0, 
[2025-07-28 01:07:32 DP0 TP0] Decode batch. #running-req: 1, #token: 912, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.33, #queue-req: 0, 
[2025-07-28 01:07:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.63, #queue-req: 0, 
[2025-07-28 01:07:32] INFO:     127.0.0.1:60116 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:07:32 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:07:33 DP1 TP0] Decode batch. #running-req: 1, #token: 1050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.20, #queue-req: 0, 
[2025-07-28 01:07:33 DP0 TP0] Decode batch. #running-req: 1, #token: 230, token usage: 0.00, cuda graph: True, gen throughput (token/s): 157.76, #queue-req: 0, 
[2025-07-28 01:07:33 DP1 TP0] Decode batch. #running-req: 1, #token: 1090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.06, #queue-req: 0, 
[2025-07-28 01:07:33 DP0 TP0] Decode batch. #running-req: 1, #token: 270, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.43, #queue-req: 0, 
[2025-07-28 01:07:33 DP1 TP0] Decode batch. #running-req: 1, #token: 1130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.32, #queue-req: 0, 
[2025-07-28 01:07:33 DP0 TP0] Decode batch. #running-req: 1, #token: 310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.88, #queue-req: 0, 
[2025-07-28 01:07:33 DP1 TP0] Decode batch. #running-req: 1, #token: 1170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.10, #queue-req: 0, 
[2025-07-28 01:07:33 DP0 TP0] Decode batch. #running-req: 1, #token: 350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.82, #queue-req: 0, 
[2025-07-28 01:07:33 DP1 TP0] Decode batch. #running-req: 1, #token: 1210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.78, #queue-req: 0, 
[2025-07-28 01:07:33 DP0 TP0] Decode batch. #running-req: 1, #token: 390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.47, #queue-req: 0, 
[2025-07-28 01:07:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.20, #queue-req: 0, 
[2025-07-28 01:07:34 DP0 TP0] Decode batch. #running-req: 1, #token: 430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.28, #queue-req: 0, 
[2025-07-28 01:07:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.89, #queue-req: 0, 
[2025-07-28 01:07:34 DP0 TP0] Decode batch. #running-req: 1, #token: 470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.85, #queue-req: 0, 
[2025-07-28 01:07:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.90, #queue-req: 0, 
[2025-07-28 01:07:34 DP0 TP0] Decode batch. #running-req: 1, #token: 510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 190.29, #queue-req: 0, 
[2025-07-28 01:07:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.99, #queue-req: 0, 
[2025-07-28 01:07:34 DP0 TP0] Decode batch. #running-req: 1, #token: 550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.58, #queue-req: 0, 
[2025-07-28 01:07:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.98, #queue-req: 0, 
[2025-07-28 01:07:34 DP0 TP0] Decode batch. #running-req: 1, #token: 590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.21, #queue-req: 0, 
[2025-07-28 01:07:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.17, #queue-req: 0, 
[2025-07-28 01:07:35 DP0 TP0] Decode batch. #running-req: 1, #token: 630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.23, #queue-req: 0, 
[2025-07-28 01:07:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.20, #queue-req: 0, 
[2025-07-28 01:07:35 DP0 TP0] Decode batch. #running-req: 1, #token: 670, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.21, #queue-req: 0, 
[2025-07-28 01:07:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.15, #queue-req: 0, 
[2025-07-28 01:07:35 DP0 TP0] Decode batch. #running-req: 1, #token: 710, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.04, #queue-req: 0, 
[2025-07-28 01:07:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.99, #queue-req: 0, 
[2025-07-28 01:07:35 DP0 TP0] Decode batch. #running-req: 1, #token: 750, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.07, #queue-req: 0, 
[2025-07-28 01:07:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.84, #queue-req: 0, 
[2025-07-28 01:07:35 DP0 TP0] Decode batch. #running-req: 1, #token: 790, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.79, #queue-req: 0, 
[2025-07-28 01:07:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.28, #queue-req: 0, 
[2025-07-28 01:07:36 DP0 TP0] Decode batch. #running-req: 1, #token: 830, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.83, #queue-req: 0, 
[2025-07-28 01:07:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.09, #queue-req: 0, 
[2025-07-28 01:07:36 DP0 TP0] Decode batch. #running-req: 1, #token: 870, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.20, #queue-req: 0, 
[2025-07-28 01:07:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.54, #queue-req: 0, 
[2025-07-28 01:07:36 DP0 TP0] Decode batch. #running-req: 1, #token: 910, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.20, #queue-req: 0, 
[2025-07-28 01:07:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.99, #queue-req: 0, 
[2025-07-28 01:07:36 DP0 TP0] Decode batch. #running-req: 1, #token: 950, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.20, #queue-req: 0, 
[2025-07-28 01:07:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.90, #queue-req: 0, 
[2025-07-28 01:07:36 DP0 TP0] Decode batch. #running-req: 1, #token: 990, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.31, #queue-req: 0, 
[2025-07-28 01:07:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.60, #queue-req: 0, 
[2025-07-28 01:07:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1030, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.34, #queue-req: 0, 
[2025-07-28 01:07:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.25, #queue-req: 0, 
[2025-07-28 01:07:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1070, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.85, #queue-req: 0, 
[2025-07-28 01:07:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.68, #queue-req: 0, 
[2025-07-28 01:07:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1110, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.09, #queue-req: 0, 
[2025-07-28 01:07:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.37, #queue-req: 0, 
[2025-07-28 01:07:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1150, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.97, #queue-req: 0, 
[2025-07-28 01:07:37 DP1 TP0] Decode batch. #running-req: 1, #token: 2010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.96, #queue-req: 0, 
[2025-07-28 01:07:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1190, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.69, #queue-req: 0, 
[2025-07-28 01:07:38 DP1 TP0] Decode batch. #running-req: 1, #token: 2050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.91, #queue-req: 0, 
[2025-07-28 01:07:38 DP0 TP0] Decode batch. #running-req: 1, #token: 1230, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.99, #queue-req: 0, 
[2025-07-28 01:07:38 DP1 TP0] Decode batch. #running-req: 1, #token: 2090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.39, #queue-req: 0, 
[2025-07-28 01:07:38 DP0 TP0] Decode batch. #running-req: 1, #token: 1270, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.73, #queue-req: 0, 
[2025-07-28 01:07:38 DP1 TP0] Decode batch. #running-req: 1, #token: 2130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.36, #queue-req: 0, 
[2025-07-28 01:07:38 DP0 TP0] Decode batch. #running-req: 1, #token: 1310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.06, #queue-req: 0, 
[2025-07-28 01:07:38 DP1 TP0] Decode batch. #running-req: 1, #token: 2170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.35, #queue-req: 0, 
[2025-07-28 01:07:38 DP0 TP0] Decode batch. #running-req: 1, #token: 1350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.29, #queue-req: 0, 
[2025-07-28 01:07:38 DP1 TP0] Decode batch. #running-req: 1, #token: 2210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.48, #queue-req: 0, 
[2025-07-28 01:07:38 DP0 TP0] Decode batch. #running-req: 1, #token: 1390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.52, #queue-req: 0, 
[2025-07-28 01:07:39 DP1 TP0] Decode batch. #running-req: 1, #token: 2250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.47, #queue-req: 0, 
[2025-07-28 01:07:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.07, #queue-req: 0, 
[2025-07-28 01:07:39 DP1 TP0] Decode batch. #running-req: 1, #token: 2290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.14, #queue-req: 0, 
[2025-07-28 01:07:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.41, #queue-req: 0, 
[2025-07-28 01:07:39 DP1 TP0] Decode batch. #running-req: 1, #token: 2330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.39, #queue-req: 0, 
[2025-07-28 01:07:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.47, #queue-req: 0, 
[2025-07-28 01:07:39 DP1 TP0] Decode batch. #running-req: 1, #token: 2370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.34, #queue-req: 0, 
[2025-07-28 01:07:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.41, #queue-req: 0, 
[2025-07-28 01:07:39 DP1 TP0] Decode batch. #running-req: 1, #token: 2410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.28, #queue-req: 0, 
[2025-07-28 01:07:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.15, #queue-req: 0, 
[2025-07-28 01:07:40 DP1 TP0] Decode batch. #running-req: 1, #token: 2450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.55, #queue-req: 0, 
[2025-07-28 01:07:40 DP0 TP0] Decode batch. #running-req: 1, #token: 1630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.18, #queue-req: 0, 
[2025-07-28 01:07:40 DP1 TP0] Decode batch. #running-req: 1, #token: 2490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.45, #queue-req: 0, 
[2025-07-28 01:07:40 DP0 TP0] Decode batch. #running-req: 1, #token: 1670, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.31, #queue-req: 0, 
[2025-07-28 01:07:40 DP1 TP0] Decode batch. #running-req: 1, #token: 2530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.39, #queue-req: 0, 
[2025-07-28 01:07:40] INFO:     127.0.0.1:60124 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:07:40 DP0 TP0] Decode batch. #running-req: 1, #token: 1710, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.14, #queue-req: 0, 
[2025-07-28 01:07:40 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 133, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:07:40 DP0 TP0] Decode batch. #running-req: 1, #token: 1750, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.49, #queue-req: 0, 
[2025-07-28 01:07:40 DP1 TP0] Decode batch. #running-req: 1, #token: 254, token usage: 0.00, cuda graph: True, gen throughput (token/s): 161.92, #queue-req: 0, 
[2025-07-28 01:07:40 DP0 TP0] Decode batch. #running-req: 1, #token: 1790, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.42, #queue-req: 0, 
[2025-07-28 01:07:40 DP1 TP0] Decode batch. #running-req: 1, #token: 294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.94, #queue-req: 0, 
[2025-07-28 01:07:41 DP0 TP0] Decode batch. #running-req: 1, #token: 1830, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.08, #queue-req: 0, 
[2025-07-28 01:07:41 DP1 TP0] Decode batch. #running-req: 1, #token: 334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.90, #queue-req: 0, 
[2025-07-28 01:07:41 DP0 TP0] Decode batch. #running-req: 1, #token: 1870, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.14, #queue-req: 0, 
[2025-07-28 01:07:41 DP1 TP0] Decode batch. #running-req: 1, #token: 374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.97, #queue-req: 0, 
[2025-07-28 01:07:41 DP0 TP0] Decode batch. #running-req: 1, #token: 1910, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.21, #queue-req: 0, 
[2025-07-28 01:07:41 DP1 TP0] Decode batch. #running-req: 1, #token: 414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.01, #queue-req: 0, 
[2025-07-28 01:07:41 DP0 TP0] Decode batch. #running-req: 1, #token: 1950, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.03, #queue-req: 0, 
[2025-07-28 01:07:41 DP1 TP0] Decode batch. #running-req: 1, #token: 454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.91, #queue-req: 0, 
[2025-07-28 01:07:41 DP0 TP0] Decode batch. #running-req: 1, #token: 1990, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.23, #queue-req: 0, 
[2025-07-28 01:07:41 DP1 TP0] Decode batch. #running-req: 1, #token: 494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.53, #queue-req: 0, 
[2025-07-28 01:07:42 DP0 TP0] Decode batch. #running-req: 1, #token: 2030, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.36, #queue-req: 0, 
[2025-07-28 01:07:42 DP1 TP0] Decode batch. #running-req: 1, #token: 534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.80, #queue-req: 0, 
[2025-07-28 01:07:42 DP0 TP0] Decode batch. #running-req: 1, #token: 2070, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.12, #queue-req: 0, 
[2025-07-28 01:07:42 DP1 TP0] Decode batch. #running-req: 1, #token: 574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.50, #queue-req: 0, 
[2025-07-28 01:07:42 DP0 TP0] Decode batch. #running-req: 1, #token: 2110, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.80, #queue-req: 0, 
[2025-07-28 01:07:42 DP1 TP0] Decode batch. #running-req: 1, #token: 614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.58, #queue-req: 0, 
[2025-07-28 01:07:42] INFO:     127.0.0.1:46442 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:07:42 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 186, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:07:42 DP1 TP0] Decode batch. #running-req: 1, #token: 654, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.63, #queue-req: 0, 
[2025-07-28 01:07:42 DP0 TP0] Decode batch. #running-req: 1, #token: 300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.17, #queue-req: 0, 
[2025-07-28 01:07:42 DP1 TP0] Decode batch. #running-req: 1, #token: 694, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.75, #queue-req: 0, 
[2025-07-28 01:07:42 DP0 TP0] Decode batch. #running-req: 1, #token: 340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.04, #queue-req: 0, 
[2025-07-28 01:07:43 DP1 TP0] Decode batch. #running-req: 1, #token: 734, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.74, #queue-req: 0, 
[2025-07-28 01:07:43 DP0 TP0] Decode batch. #running-req: 1, #token: 380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.93, #queue-req: 0, 
[2025-07-28 01:07:43 DP1 TP0] Decode batch. #running-req: 1, #token: 774, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.65, #queue-req: 0, 
[2025-07-28 01:07:43 DP0 TP0] Decode batch. #running-req: 1, #token: 420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.86, #queue-req: 0, 
[2025-07-28 01:07:43 DP1 TP0] Decode batch. #running-req: 1, #token: 814, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.45, #queue-req: 0, 
[2025-07-28 01:07:43 DP0 TP0] Decode batch. #running-req: 1, #token: 460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.04, #queue-req: 0, 
[2025-07-28 01:07:43 DP1 TP0] Decode batch. #running-req: 1, #token: 854, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.10, #queue-req: 0, 
[2025-07-28 01:07:43 DP0 TP0] Decode batch. #running-req: 1, #token: 500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.75, #queue-req: 0, 
[2025-07-28 01:07:43 DP1 TP0] Decode batch. #running-req: 1, #token: 894, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.77, #queue-req: 0, 
[2025-07-28 01:07:43 DP0 TP0] Decode batch. #running-req: 1, #token: 540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.22, #queue-req: 0, 
[2025-07-28 01:07:44 DP1 TP0] Decode batch. #running-req: 1, #token: 934, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.25, #queue-req: 0, 
[2025-07-28 01:07:44 DP0 TP0] Decode batch. #running-req: 1, #token: 580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.38, #queue-req: 0, 
[2025-07-28 01:07:44 DP1 TP0] Decode batch. #running-req: 1, #token: 974, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.35, #queue-req: 0, 
[2025-07-28 01:07:44 DP0 TP0] Decode batch. #running-req: 1, #token: 620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.30, #queue-req: 0, 
[2025-07-28 01:07:44 DP1 TP0] Decode batch. #running-req: 1, #token: 1014, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.47, #queue-req: 0, 
[2025-07-28 01:07:44 DP0 TP0] Decode batch. #running-req: 1, #token: 660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.43, #queue-req: 0, 
[2025-07-28 01:07:44 DP1 TP0] Decode batch. #running-req: 1, #token: 1054, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.09, #queue-req: 0, 
[2025-07-28 01:07:44 DP0 TP0] Decode batch. #running-req: 1, #token: 700, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.95, #queue-req: 0, 
[2025-07-28 01:07:44 DP1 TP0] Decode batch. #running-req: 1, #token: 1094, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.23, #queue-req: 0, 
[2025-07-28 01:07:44 DP0 TP0] Decode batch. #running-req: 1, #token: 740, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.98, #queue-req: 0, 
[2025-07-28 01:07:45 DP1 TP0] Decode batch. #running-req: 1, #token: 1134, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.82, #queue-req: 0, 
[2025-07-28 01:07:45 DP0 TP0] Decode batch. #running-req: 1, #token: 780, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.27, #queue-req: 0, 
[2025-07-28 01:07:45 DP1 TP0] Decode batch. #running-req: 1, #token: 1174, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.04, #queue-req: 0, 
[2025-07-28 01:07:45 DP0 TP0] Decode batch. #running-req: 1, #token: 820, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.98, #queue-req: 0, 
[2025-07-28 01:07:45 DP1 TP0] Decode batch. #running-req: 1, #token: 1214, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.89, #queue-req: 0, 
[2025-07-28 01:07:45 DP0 TP0] Decode batch. #running-req: 1, #token: 860, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.94, #queue-req: 0, 
[2025-07-28 01:07:45 DP1 TP0] Decode batch. #running-req: 1, #token: 1254, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.71, #queue-req: 0, 
[2025-07-28 01:07:45 DP0 TP0] Decode batch. #running-req: 1, #token: 900, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.04, #queue-req: 0, 
[2025-07-28 01:07:45 DP1 TP0] Decode batch. #running-req: 1, #token: 1294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.99, #queue-req: 0, 
[2025-07-28 01:07:45 DP0 TP0] Decode batch. #running-req: 1, #token: 940, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.23, #queue-req: 0, 
[2025-07-28 01:07:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.93, #queue-req: 0, 
[2025-07-28 01:07:46 DP0 TP0] Decode batch. #running-req: 1, #token: 980, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.41, #queue-req: 0, 
[2025-07-28 01:07:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.91, #queue-req: 0, 
[2025-07-28 01:07:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1020, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.88, #queue-req: 0, 
[2025-07-28 01:07:46] INFO:     127.0.0.1:46456 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:07:46 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 173, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:07:46] INFO:     127.0.0.1:49310 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:07:46 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 226, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:07:46 DP1 TP0] Decode batch. #running-req: 1, #token: 273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 160.46, #queue-req: 0, 
[2025-07-28 01:07:46 DP0 TP0] Decode batch. #running-req: 1, #token: 322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 158.26, #queue-req: 0, 
[2025-07-28 01:07:46 DP1 TP0] Decode batch. #running-req: 1, #token: 313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.10, #queue-req: 0, 
[2025-07-28 01:07:46 DP0 TP0] Decode batch. #running-req: 1, #token: 362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.83, #queue-req: 0, 
[2025-07-28 01:07:46 DP1 TP0] Decode batch. #running-req: 1, #token: 353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.99, #queue-req: 0, 
[2025-07-28 01:07:47 DP0 TP0] Decode batch. #running-req: 1, #token: 402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.91, #queue-req: 0, 
[2025-07-28 01:07:47 DP1 TP0] Decode batch. #running-req: 1, #token: 393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.97, #queue-req: 0, 
[2025-07-28 01:07:47 DP0 TP0] Decode batch. #running-req: 1, #token: 442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.00, #queue-req: 0, 
[2025-07-28 01:07:47 DP1 TP0] Decode batch. #running-req: 1, #token: 433, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.19, #queue-req: 0, 
[2025-07-28 01:07:47 DP0 TP0] Decode batch. #running-req: 1, #token: 482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.01, #queue-req: 0, 
[2025-07-28 01:07:47 DP1 TP0] Decode batch. #running-req: 1, #token: 473, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.70, #queue-req: 0, 
[2025-07-28 01:07:47 DP0 TP0] Decode batch. #running-req: 1, #token: 522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.77, #queue-req: 0, 
[2025-07-28 01:07:47 DP1 TP0] Decode batch. #running-req: 1, #token: 513, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.63, #queue-req: 0, 
[2025-07-28 01:07:47 DP0 TP0] Decode batch. #running-req: 1, #token: 562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.60, #queue-req: 0, 
[2025-07-28 01:07:47 DP1 TP0] Decode batch. #running-req: 1, #token: 553, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.02, #queue-req: 0, 
[2025-07-28 01:07:48 DP0 TP0] Decode batch. #running-req: 1, #token: 602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.23, #queue-req: 0, 
[2025-07-28 01:07:48 DP1 TP0] Decode batch. #running-req: 1, #token: 593, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.83, #queue-req: 0, 
[2025-07-28 01:07:48 DP0 TP0] Decode batch. #running-req: 1, #token: 642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.30, #queue-req: 0, 
[2025-07-28 01:07:48 DP1 TP0] Decode batch. #running-req: 1, #token: 633, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.75, #queue-req: 0, 
[2025-07-28 01:07:48 DP0 TP0] Decode batch. #running-req: 1, #token: 682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.99, #queue-req: 0, 
[2025-07-28 01:07:48 DP1 TP0] Decode batch. #running-req: 1, #token: 673, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.02, #queue-req: 0, 
[2025-07-28 01:07:48 DP0 TP0] Decode batch. #running-req: 1, #token: 722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.08, #queue-req: 0, 
[2025-07-28 01:07:48 DP1 TP0] Decode batch. #running-req: 1, #token: 713, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.97, #queue-req: 0, 
[2025-07-28 01:07:48 DP0 TP0] Decode batch. #running-req: 1, #token: 762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.96, #queue-req: 0, 
[2025-07-28 01:07:48 DP1 TP0] Decode batch. #running-req: 1, #token: 753, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.91, #queue-req: 0, 
[2025-07-28 01:07:49 DP0 TP0] Decode batch. #running-req: 1, #token: 802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.83, #queue-req: 0, 
[2025-07-28 01:07:49 DP1 TP0] Decode batch. #running-req: 1, #token: 793, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.88, #queue-req: 0, 
[2025-07-28 01:07:49 DP0 TP0] Decode batch. #running-req: 1, #token: 842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.82, #queue-req: 0, 
[2025-07-28 01:07:49 DP1 TP0] Decode batch. #running-req: 1, #token: 833, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.67, #queue-req: 0, 
[2025-07-28 01:07:49 DP0 TP0] Decode batch. #running-req: 1, #token: 882, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.64, #queue-req: 0, 
[2025-07-28 01:07:49 DP1 TP0] Decode batch. #running-req: 1, #token: 873, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.61, #queue-req: 0, 
[2025-07-28 01:07:49 DP0 TP0] Decode batch. #running-req: 1, #token: 922, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.60, #queue-req: 0, 
[2025-07-28 01:07:49 DP1 TP0] Decode batch. #running-req: 1, #token: 913, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.77, #queue-req: 0, 
[2025-07-28 01:07:49 DP0 TP0] Decode batch. #running-req: 1, #token: 962, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.84, #queue-req: 0, 
[2025-07-28 01:07:49 DP1 TP0] Decode batch. #running-req: 1, #token: 953, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.75, #queue-req: 0, 
[2025-07-28 01:07:50 DP0 TP0] Decode batch. #running-req: 1, #token: 1002, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.42, #queue-req: 0, 
[2025-07-28 01:07:50 DP1 TP0] Decode batch. #running-req: 1, #token: 993, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.99, #queue-req: 0, 
[2025-07-28 01:07:50 DP0 TP0] Decode batch. #running-req: 1, #token: 1042, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.94, #queue-req: 0, 
[2025-07-28 01:07:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1033, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.66, #queue-req: 0, 
[2025-07-28 01:07:50 DP0 TP0] Decode batch. #running-req: 1, #token: 1082, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.76, #queue-req: 0, 
[2025-07-28 01:07:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1073, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.44, #queue-req: 0, 
[2025-07-28 01:07:50 DP0 TP0] Decode batch. #running-req: 1, #token: 1122, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.33, #queue-req: 0, 
[2025-07-28 01:07:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1113, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.73, #queue-req: 0, 
[2025-07-28 01:07:50 DP0 TP0] Decode batch. #running-req: 1, #token: 1162, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.82, #queue-req: 0, 
[2025-07-28 01:07:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1153, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.70, #queue-req: 0, 
[2025-07-28 01:07:51 DP0 TP0] Decode batch. #running-req: 1, #token: 1202, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.34, #queue-req: 0, 
[2025-07-28 01:07:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1193, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.12, #queue-req: 0, 
[2025-07-28 01:07:51 DP0 TP0] Decode batch. #running-req: 1, #token: 1242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.84, #queue-req: 0, 
[2025-07-28 01:07:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1233, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.00, #queue-req: 0, 
[2025-07-28 01:07:51 DP0 TP0] Decode batch. #running-req: 1, #token: 1282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.94, #queue-req: 0, 
[2025-07-28 01:07:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.00, #queue-req: 0, 
[2025-07-28 01:07:51 DP0 TP0] Decode batch. #running-req: 1, #token: 1322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.58, #queue-req: 0, 
[2025-07-28 01:07:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.97, #queue-req: 0, 
[2025-07-28 01:07:51 DP0 TP0] Decode batch. #running-req: 1, #token: 1362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.49, #queue-req: 0, 
[2025-07-28 01:07:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.77, #queue-req: 0, 
[2025-07-28 01:07:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.75, #queue-req: 0, 
[2025-07-28 01:07:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.94, #queue-req: 0, 
[2025-07-28 01:07:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.79, #queue-req: 0, 
[2025-07-28 01:07:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1433, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.92, #queue-req: 0, 
[2025-07-28 01:07:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.64, #queue-req: 0, 
[2025-07-28 01:07:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1473, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.05, #queue-req: 0, 
[2025-07-28 01:07:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.75, #queue-req: 0, 
[2025-07-28 01:07:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1513, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.03, #queue-req: 0, 
[2025-07-28 01:07:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.69, #queue-req: 0, 
[2025-07-28 01:07:52] INFO:     127.0.0.1:49318 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:07:52 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 168, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:07:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.60, #queue-req: 0, 
[2025-07-28 01:07:53 DP1 TP0] Decode batch. #running-req: 1, #token: 275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.64, #queue-req: 0, 
[2025-07-28 01:07:53 DP1 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.88, #queue-req: 0, 
[2025-07-28 01:07:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.62, #queue-req: 0, 
[2025-07-28 01:07:53 DP1 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.11, #queue-req: 0, 
[2025-07-28 01:07:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.54, #queue-req: 0, 
[2025-07-28 01:07:53 DP1 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.28, #queue-req: 0, 
[2025-07-28 01:07:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.74, #queue-req: 0, 
[2025-07-28 01:07:53 DP1 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.79, #queue-req: 0, 
[2025-07-28 01:07:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.61, #queue-req: 0, 
[2025-07-28 01:07:54 DP1 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.65, #queue-req: 0, 
[2025-07-28 01:07:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.54, #queue-req: 0, 
[2025-07-28 01:07:54 DP1 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.84, #queue-req: 0, 
[2025-07-28 01:07:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.47, #queue-req: 0, 
[2025-07-28 01:07:54] INFO:     127.0.0.1:49320 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:07:54 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 154, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:07:54 DP1 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.99, #queue-req: 0, 
[2025-07-28 01:07:54 DP0 TP0] Decode batch. #running-req: 1, #token: 251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 152.53, #queue-req: 0, 
[2025-07-28 01:07:54 DP1 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.01, #queue-req: 0, 
[2025-07-28 01:07:54 DP0 TP0] Decode batch. #running-req: 1, #token: 291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.01, #queue-req: 0, 
[2025-07-28 01:07:54 DP1 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.19, #queue-req: 0, 
[2025-07-28 01:07:54 DP0 TP0] Decode batch. #running-req: 1, #token: 331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.34, #queue-req: 0, 
[2025-07-28 01:07:55 DP1 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.10, #queue-req: 0, 
[2025-07-28 01:07:55 DP0 TP0] Decode batch. #running-req: 1, #token: 371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.26, #queue-req: 0, 
[2025-07-28 01:07:55 DP1 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.80, #queue-req: 0, 
[2025-07-28 01:07:55 DP0 TP0] Decode batch. #running-req: 1, #token: 411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.92, #queue-req: 0, 
[2025-07-28 01:07:55 DP1 TP0] Decode batch. #running-req: 1, #token: 755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.87, #queue-req: 0, 
[2025-07-28 01:07:55 DP0 TP0] Decode batch. #running-req: 1, #token: 451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.09, #queue-req: 0, 
[2025-07-28 01:07:55 DP1 TP0] Decode batch. #running-req: 1, #token: 795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.21, #queue-req: 0, 
[2025-07-28 01:07:55 DP0 TP0] Decode batch. #running-req: 1, #token: 491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.80, #queue-req: 0, 
[2025-07-28 01:07:55 DP1 TP0] Decode batch. #running-req: 1, #token: 835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.48, #queue-req: 0, 
[2025-07-28 01:07:55 DP0 TP0] Decode batch. #running-req: 1, #token: 531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.83, #queue-req: 0, 
[2025-07-28 01:07:56 DP1 TP0] Decode batch. #running-req: 1, #token: 875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.36, #queue-req: 0, 
[2025-07-28 01:07:56 DP0 TP0] Decode batch. #running-req: 1, #token: 571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.65, #queue-req: 0, 
[2025-07-28 01:07:56 DP1 TP0] Decode batch. #running-req: 1, #token: 915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.45, #queue-req: 0, 
[2025-07-28 01:07:56 DP0 TP0] Decode batch. #running-req: 1, #token: 611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.51, #queue-req: 0, 
[2025-07-28 01:07:56 DP1 TP0] Decode batch. #running-req: 1, #token: 955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.58, #queue-req: 0, 
[2025-07-28 01:07:56 DP0 TP0] Decode batch. #running-req: 1, #token: 651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.53, #queue-req: 0, 
[2025-07-28 01:07:56 DP1 TP0] Decode batch. #running-req: 1, #token: 995, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.52, #queue-req: 0, 
[2025-07-28 01:07:56 DP0 TP0] Decode batch. #running-req: 1, #token: 691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.41, #queue-req: 0, 
[2025-07-28 01:07:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.24, #queue-req: 0, 
[2025-07-28 01:07:56 DP0 TP0] Decode batch. #running-req: 1, #token: 731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.79, #queue-req: 0, 
[2025-07-28 01:07:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1075, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.90, #queue-req: 0, 
[2025-07-28 01:07:57 DP0 TP0] Decode batch. #running-req: 1, #token: 771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.58, #queue-req: 0, 
[2025-07-28 01:07:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.93, #queue-req: 0, 
[2025-07-28 01:07:57 DP0 TP0] Decode batch. #running-req: 1, #token: 811, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.98, #queue-req: 0, 
[2025-07-28 01:07:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1155, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.92, #queue-req: 0, 
[2025-07-28 01:07:57 DP0 TP0] Decode batch. #running-req: 1, #token: 851, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.32, #queue-req: 0, 
[2025-07-28 01:07:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.03, #queue-req: 0, 
[2025-07-28 01:07:57 DP0 TP0] Decode batch. #running-req: 1, #token: 891, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.13, #queue-req: 0, 
[2025-07-28 01:07:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.03, #queue-req: 0, 
[2025-07-28 01:07:57 DP0 TP0] Decode batch. #running-req: 1, #token: 931, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.62, #queue-req: 0, 
[2025-07-28 01:07:58 DP1 TP0] Decode batch. #running-req: 1, #token: 1275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.97, #queue-req: 0, 
[2025-07-28 01:07:58 DP0 TP0] Decode batch. #running-req: 1, #token: 971, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.39, #queue-req: 0, 
[2025-07-28 01:07:58 DP1 TP0] Decode batch. #running-req: 1, #token: 1315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.95, #queue-req: 0, 
[2025-07-28 01:07:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1011, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.54, #queue-req: 0, 
[2025-07-28 01:07:58 DP1 TP0] Decode batch. #running-req: 1, #token: 1355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.93, #queue-req: 0, 
[2025-07-28 01:07:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1051, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.84, #queue-req: 0, 
[2025-07-28 01:07:58 DP1 TP0] Decode batch. #running-req: 1, #token: 1395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.64, #queue-req: 0, 
[2025-07-28 01:07:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1091, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.67, #queue-req: 0, 
[2025-07-28 01:07:58 DP1 TP0] Decode batch. #running-req: 1, #token: 1435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.96, #queue-req: 0, 
[2025-07-28 01:07:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1131, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.99, #queue-req: 0, 
[2025-07-28 01:07:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.07, #queue-req: 0, 
[2025-07-28 01:07:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1171, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.75, #queue-req: 0, 
[2025-07-28 01:07:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.96, #queue-req: 0, 
[2025-07-28 01:07:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1211, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.90, #queue-req: 0, 
[2025-07-28 01:07:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.80, #queue-req: 0, 
[2025-07-28 01:07:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.95, #queue-req: 0, 
[2025-07-28 01:07:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.45, #queue-req: 0, 
[2025-07-28 01:07:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.91, #queue-req: 0, 
[2025-07-28 01:07:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.45, #queue-req: 0, 
[2025-07-28 01:07:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.45, #queue-req: 0, 
[2025-07-28 01:08:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.37, #queue-req: 0, 
[2025-07-28 01:08:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 192.62, #queue-req: 0, 
[2025-07-28 01:08:00] INFO:     127.0.0.1:56388 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:08:00 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 141, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:08:00 DP1 TP0] Decode batch. #running-req: 2, #token: 1860, token usage: 0.00, cuda graph: True, gen throughput (token/s): 180.26, #queue-req: 0, 
[2025-07-28 01:08:00 DP1 TP0] Decode batch. #running-req: 2, #token: 1940, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.93, #queue-req: 0, 
[2025-07-28 01:08:00 DP1 TP0] Decode batch. #running-req: 2, #token: 2020, token usage: 0.00, cuda graph: True, gen throughput (token/s): 386.25, #queue-req: 0, 
[2025-07-28 01:08:00 DP1 TP0] Decode batch. #running-req: 2, #token: 2100, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.28, #queue-req: 0, 
[2025-07-28 01:08:01 DP1 TP0] Decode batch. #running-req: 2, #token: 2180, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.87, #queue-req: 0, 
[2025-07-28 01:08:01 DP1 TP0] Decode batch. #running-req: 2, #token: 2260, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.72, #queue-req: 0, 
[2025-07-28 01:08:01 DP1 TP0] Decode batch. #running-req: 2, #token: 2340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.75, #queue-req: 0, 
[2025-07-28 01:08:01 DP1 TP0] Decode batch. #running-req: 2, #token: 2420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.24, #queue-req: 0, 
[2025-07-28 01:08:01 DP1 TP0] Decode batch. #running-req: 2, #token: 2500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.77, #queue-req: 0, 
[2025-07-28 01:08:02 DP1 TP0] Decode batch. #running-req: 2, #token: 2580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.38, #queue-req: 0, 
[2025-07-28 01:08:02 DP1 TP0] Decode batch. #running-req: 2, #token: 2660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.83, #queue-req: 0, 
[2025-07-28 01:08:02 DP1 TP0] Decode batch. #running-req: 2, #token: 2740, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.78, #queue-req: 0, 
[2025-07-28 01:08:02 DP1 TP0] Decode batch. #running-req: 2, #token: 2820, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.46, #queue-req: 0, 
[2025-07-28 01:08:02 DP1 TP0] Decode batch. #running-req: 2, #token: 2900, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.78, #queue-req: 0, 
[2025-07-28 01:08:03 DP1 TP0] Decode batch. #running-req: 2, #token: 2980, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.77, #queue-req: 0, 
[2025-07-28 01:08:03 DP1 TP0] Decode batch. #running-req: 2, #token: 3060, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.94, #queue-req: 0, 
[2025-07-28 01:08:03 DP1 TP0] Decode batch. #running-req: 2, #token: 3140, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.87, #queue-req: 0, 
[2025-07-28 01:08:03 DP1 TP0] Decode batch. #running-req: 2, #token: 3220, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.80, #queue-req: 0, 
[2025-07-28 01:08:04 DP1 TP0] Decode batch. #running-req: 2, #token: 3300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.51, #queue-req: 0, 
[2025-07-28 01:08:04 DP1 TP0] Decode batch. #running-req: 2, #token: 3380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.73, #queue-req: 0, 
[2025-07-28 01:08:04 DP1 TP0] Decode batch. #running-req: 2, #token: 3460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.75, #queue-req: 0, 
[2025-07-28 01:08:04 DP1 TP0] Decode batch. #running-req: 2, #token: 3540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.46, #queue-req: 0, 
[2025-07-28 01:08:04 DP1 TP0] Decode batch. #running-req: 2, #token: 3620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.62, #queue-req: 0, 
[2025-07-28 01:08:05 DP1 TP0] Decode batch. #running-req: 2, #token: 3700, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.02, #queue-req: 0, 
[2025-07-28 01:08:05 DP1 TP0] Decode batch. #running-req: 2, #token: 3780, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.40, #queue-req: 0, 
[2025-07-28 01:08:05 DP1 TP0] Decode batch. #running-req: 2, #token: 3860, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.38, #queue-req: 0, 
[2025-07-28 01:08:05 DP1 TP0] Decode batch. #running-req: 2, #token: 3940, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.39, #queue-req: 0, 
[2025-07-28 01:08:05 DP1 TP0] Decode batch. #running-req: 2, #token: 4020, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.84, #queue-req: 0, 
[2025-07-28 01:08:06 DP1 TP0] Decode batch. #running-req: 2, #token: 4100, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.26, #queue-req: 0, 
[2025-07-28 01:08:06] INFO:     127.0.0.1:56398 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:08:06 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 192, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:08:06 DP1 TP0] Decode batch. #running-req: 1, #token: 2875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 274.29, #queue-req: 0, 
[2025-07-28 01:08:06 DP0 TP0] Decode batch. #running-req: 1, #token: 301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 6.35, #queue-req: 0, 
[2025-07-28 01:08:06 DP1 TP0] Decode batch. #running-req: 1, #token: 2915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.07, #queue-req: 0, 
[2025-07-28 01:08:06 DP0 TP0] Decode batch. #running-req: 1, #token: 341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 193.04, #queue-req: 0, 
[2025-07-28 01:08:06 DP1 TP0] Decode batch. #running-req: 1, #token: 2955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.13, #queue-req: 0, 
[2025-07-28 01:08:06 DP0 TP0] Decode batch. #running-req: 1, #token: 381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.78, #queue-req: 0, 
[2025-07-28 01:08:06 DP1 TP0] Decode batch. #running-req: 1, #token: 2995, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.33, #queue-req: 0, 
[2025-07-28 01:08:06 DP0 TP0] Decode batch. #running-req: 1, #token: 421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.46, #queue-req: 0, 
[2025-07-28 01:08:07 DP1 TP0] Decode batch. #running-req: 1, #token: 3035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.19, #queue-req: 0, 
[2025-07-28 01:08:07 DP0 TP0] Decode batch. #running-req: 1, #token: 461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.77, #queue-req: 0, 
[2025-07-28 01:08:07 DP1 TP0] Decode batch. #running-req: 1, #token: 3075, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.23, #queue-req: 0, 
[2025-07-28 01:08:07 DP0 TP0] Decode batch. #running-req: 1, #token: 501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.89, #queue-req: 0, 
[2025-07-28 01:08:07 DP1 TP0] Decode batch. #running-req: 1, #token: 3115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.00, #queue-req: 0, 
[2025-07-28 01:08:07 DP0 TP0] Decode batch. #running-req: 1, #token: 541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.78, #queue-req: 0, 
[2025-07-28 01:08:07 DP1 TP0] Decode batch. #running-req: 1, #token: 3155, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.01, #queue-req: 0, 
[2025-07-28 01:08:07 DP0 TP0] Decode batch. #running-req: 1, #token: 581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.57, #queue-req: 0, 
[2025-07-28 01:08:07 DP1 TP0] Decode batch. #running-req: 1, #token: 3195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.00, #queue-req: 0, 
[2025-07-28 01:08:07 DP0 TP0] Decode batch. #running-req: 1, #token: 621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.65, #queue-req: 0, 
[2025-07-28 01:08:08 DP1 TP0] Decode batch. #running-req: 1, #token: 3235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.08, #queue-req: 0, 
[2025-07-28 01:08:08 DP0 TP0] Decode batch. #running-req: 1, #token: 661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.63, #queue-req: 0, 
[2025-07-28 01:08:08] INFO:     127.0.0.1:56378 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:08:08 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 190, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:08:08 DP1 TP0] Decode batch. #running-req: 1, #token: 298, token usage: 0.00, cuda graph: True, gen throughput (token/s): 157.83, #queue-req: 0, 
[2025-07-28 01:08:08 DP0 TP0] Decode batch. #running-req: 1, #token: 701, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.66, #queue-req: 0, 
[2025-07-28 01:08:08 DP1 TP0] Decode batch. #running-req: 1, #token: 338, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.87, #queue-req: 0, 
[2025-07-28 01:08:08 DP0 TP0] Decode batch. #running-req: 1, #token: 741, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.58, #queue-req: 0, 
[2025-07-28 01:08:08 DP1 TP0] Decode batch. #running-req: 1, #token: 378, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.48, #queue-req: 0, 
[2025-07-28 01:08:08 DP0 TP0] Decode batch. #running-req: 1, #token: 781, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.27, #queue-req: 0, 
[2025-07-28 01:08:08 DP1 TP0] Decode batch. #running-req: 1, #token: 418, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.12, #queue-req: 0, 
[2025-07-28 01:08:08 DP0 TP0] Decode batch. #running-req: 1, #token: 821, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.36, #queue-req: 0, 
[2025-07-28 01:08:09 DP1 TP0] Decode batch. #running-req: 1, #token: 458, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.89, #queue-req: 0, 
[2025-07-28 01:08:09 DP0 TP0] Decode batch. #running-req: 1, #token: 861, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.42, #queue-req: 0, 
[2025-07-28 01:08:09 DP1 TP0] Decode batch. #running-req: 1, #token: 498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.95, #queue-req: 0, 
[2025-07-28 01:08:09 DP0 TP0] Decode batch. #running-req: 1, #token: 901, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.48, #queue-req: 0, 
[2025-07-28 01:08:09 DP1 TP0] Decode batch. #running-req: 1, #token: 538, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.56, #queue-req: 0, 
[2025-07-28 01:08:09 DP0 TP0] Decode batch. #running-req: 1, #token: 941, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.58, #queue-req: 0, 
[2025-07-28 01:08:09 DP1 TP0] Decode batch. #running-req: 1, #token: 578, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.43, #queue-req: 0, 
[2025-07-28 01:08:09 DP0 TP0] Decode batch. #running-req: 1, #token: 981, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.55, #queue-req: 0, 
[2025-07-28 01:08:09 DP1 TP0] Decode batch. #running-req: 1, #token: 618, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.41, #queue-req: 0, 
[2025-07-28 01:08:09 DP0 TP0] Decode batch. #running-req: 1, #token: 1021, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.56, #queue-req: 0, 
[2025-07-28 01:08:10 DP1 TP0] Decode batch. #running-req: 1, #token: 658, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.65, #queue-req: 0, 
[2025-07-28 01:08:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1061, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.85, #queue-req: 0, 
[2025-07-28 01:08:10 DP1 TP0] Decode batch. #running-req: 1, #token: 698, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.35, #queue-req: 0, 
[2025-07-28 01:08:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1101, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.93, #queue-req: 0, 
[2025-07-28 01:08:10 DP1 TP0] Decode batch. #running-req: 1, #token: 738, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.65, #queue-req: 0, 
[2025-07-28 01:08:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1141, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.95, #queue-req: 0, 
[2025-07-28 01:08:10 DP1 TP0] Decode batch. #running-req: 1, #token: 778, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.52, #queue-req: 0, 
[2025-07-28 01:08:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1181, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.08, #queue-req: 0, 
[2025-07-28 01:08:10 DP1 TP0] Decode batch. #running-req: 1, #token: 818, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.44, #queue-req: 0, 
[2025-07-28 01:08:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1221, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.10, #queue-req: 0, 
[2025-07-28 01:08:11 DP1 TP0] Decode batch. #running-req: 1, #token: 858, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.24, #queue-req: 0, 
[2025-07-28 01:08:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.07, #queue-req: 0, 
[2025-07-28 01:08:11 DP1 TP0] Decode batch. #running-req: 1, #token: 898, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.35, #queue-req: 0, 
[2025-07-28 01:08:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.97, #queue-req: 0, 
[2025-07-28 01:08:11 DP1 TP0] Decode batch. #running-req: 1, #token: 938, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.61, #queue-req: 0, 
[2025-07-28 01:08:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.74, #queue-req: 0, 
[2025-07-28 01:08:11 DP1 TP0] Decode batch. #running-req: 1, #token: 978, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.45, #queue-req: 0, 
[2025-07-28 01:08:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.84, #queue-req: 0, 
[2025-07-28 01:08:11 DP1 TP0] Decode batch. #running-req: 1, #token: 1018, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.66, #queue-req: 0, 
[2025-07-28 01:08:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.02, #queue-req: 0, 
[2025-07-28 01:08:12 DP1 TP0] Decode batch. #running-req: 1, #token: 1058, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.94, #queue-req: 0, 
[2025-07-28 01:08:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.10, #queue-req: 0, 
[2025-07-28 01:08:12 DP1 TP0] Decode batch. #running-req: 1, #token: 1098, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.85, #queue-req: 0, 
[2025-07-28 01:08:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.08, #queue-req: 0, 
[2025-07-28 01:08:12 DP1 TP0] Decode batch. #running-req: 1, #token: 1138, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.84, #queue-req: 0, 
[2025-07-28 01:08:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.86, #queue-req: 0, 
[2025-07-28 01:08:12 DP1 TP0] Decode batch. #running-req: 1, #token: 1178, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.83, #queue-req: 0, 
[2025-07-28 01:08:12] INFO:     127.0.0.1:54606 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:08:12 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 137, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:08:12 DP0 TP0] Decode batch. #running-req: 1, #token: 233, token usage: 0.00, cuda graph: True, gen throughput (token/s): 161.88, #queue-req: 0, 
[2025-07-28 01:08:12 DP1 TP0] Decode batch. #running-req: 1, #token: 1218, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.21, #queue-req: 0, 
[2025-07-28 01:08:13 DP0 TP0] Decode batch. #running-req: 1, #token: 273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.90, #queue-req: 0, 
[2025-07-28 01:08:13 DP1 TP0] Decode batch. #running-req: 1, #token: 1258, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.93, #queue-req: 0, 
[2025-07-28 01:08:13 DP0 TP0] Decode batch. #running-req: 1, #token: 313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.17, #queue-req: 0, 
[2025-07-28 01:08:13 DP1 TP0] Decode batch. #running-req: 1, #token: 1298, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.96, #queue-req: 0, 
[2025-07-28 01:08:13 DP0 TP0] Decode batch. #running-req: 1, #token: 353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.82, #queue-req: 0, 
[2025-07-28 01:08:13 DP1 TP0] Decode batch. #running-req: 1, #token: 1338, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.78, #queue-req: 0, 
[2025-07-28 01:08:13 DP0 TP0] Decode batch. #running-req: 1, #token: 393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.06, #queue-req: 0, 
[2025-07-28 01:08:13 DP1 TP0] Decode batch. #running-req: 1, #token: 1378, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.64, #queue-req: 0, 
[2025-07-28 01:08:13 DP0 TP0] Decode batch. #running-req: 1, #token: 433, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.01, #queue-req: 0, 
[2025-07-28 01:08:13 DP1 TP0] Decode batch. #running-req: 1, #token: 1418, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.18, #queue-req: 0, 
[2025-07-28 01:08:14 DP0 TP0] Decode batch. #running-req: 1, #token: 473, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.07, #queue-req: 0, 
[2025-07-28 01:08:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1458, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.66, #queue-req: 0, 
[2025-07-28 01:08:14 DP0 TP0] Decode batch. #running-req: 1, #token: 513, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.03, #queue-req: 0, 
[2025-07-28 01:08:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.96, #queue-req: 0, 
[2025-07-28 01:08:14 DP0 TP0] Decode batch. #running-req: 1, #token: 553, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.67, #queue-req: 0, 
[2025-07-28 01:08:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1538, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.93, #queue-req: 0, 
[2025-07-28 01:08:14 DP0 TP0] Decode batch. #running-req: 1, #token: 593, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.70, #queue-req: 0, 
[2025-07-28 01:08:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1578, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.57, #queue-req: 0, 
[2025-07-28 01:08:14 DP0 TP0] Decode batch. #running-req: 1, #token: 633, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.66, #queue-req: 0, 
[2025-07-28 01:08:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1618, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.06, #queue-req: 0, 
[2025-07-28 01:08:15 DP0 TP0] Decode batch. #running-req: 1, #token: 673, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.63, #queue-req: 0, 
[2025-07-28 01:08:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1658, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.84, #queue-req: 0, 
[2025-07-28 01:08:15 DP0 TP0] Decode batch. #running-req: 1, #token: 713, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.64, #queue-req: 0, 
[2025-07-28 01:08:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1698, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.91, #queue-req: 0, 
[2025-07-28 01:08:15 DP0 TP0] Decode batch. #running-req: 1, #token: 753, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.61, #queue-req: 0, 
[2025-07-28 01:08:15] INFO:     127.0.0.1:54614 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:08:15 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 132, #cached-token: 89, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:08:15 DP1 TP0] Decode batch. #running-req: 1, #token: 237, token usage: 0.00, cuda graph: True, gen throughput (token/s): 161.04, #queue-req: 0, 
[2025-07-28 01:08:15 DP0 TP0] Decode batch. #running-req: 1, #token: 793, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.51, #queue-req: 0, 
[2025-07-28 01:08:15 DP1 TP0] Decode batch. #running-req: 1, #token: 277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.42, #queue-req: 0, 
[2025-07-28 01:08:15 DP0 TP0] Decode batch. #running-req: 1, #token: 833, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.35, #queue-req: 0, 
[2025-07-28 01:08:15 DP1 TP0] Decode batch. #running-req: 1, #token: 317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.89, #queue-req: 0, 
[2025-07-28 01:08:16 DP0 TP0] Decode batch. #running-req: 1, #token: 873, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.35, #queue-req: 0, 
[2025-07-28 01:08:16 DP1 TP0] Decode batch. #running-req: 1, #token: 357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.00, #queue-req: 0, 
[2025-07-28 01:08:16 DP0 TP0] Decode batch. #running-req: 1, #token: 913, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.44, #queue-req: 0, 
[2025-07-28 01:08:16 DP1 TP0] Decode batch. #running-req: 1, #token: 397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.01, #queue-req: 0, 
[2025-07-28 01:08:16 DP0 TP0] Decode batch. #running-req: 1, #token: 953, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.38, #queue-req: 0, 
[2025-07-28 01:08:16 DP1 TP0] Decode batch. #running-req: 1, #token: 437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.08, #queue-req: 0, 
[2025-07-28 01:08:16 DP0 TP0] Decode batch. #running-req: 1, #token: 993, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.52, #queue-req: 0, 
[2025-07-28 01:08:16 DP1 TP0] Decode batch. #running-req: 1, #token: 477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.07, #queue-req: 0, 
[2025-07-28 01:08:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1033, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.15, #queue-req: 0, 
[2025-07-28 01:08:16 DP1 TP0] Decode batch. #running-req: 1, #token: 517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.06, #queue-req: 0, 
[2025-07-28 01:08:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1073, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.76, #queue-req: 0, 
[2025-07-28 01:08:17 DP1 TP0] Decode batch. #running-req: 1, #token: 557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.67, #queue-req: 0, 
[2025-07-28 01:08:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1113, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.94, #queue-req: 0, 
[2025-07-28 01:08:17 DP1 TP0] Decode batch. #running-req: 1, #token: 597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.64, #queue-req: 0, 
[2025-07-28 01:08:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1153, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.82, #queue-req: 0, 
[2025-07-28 01:08:17 DP1 TP0] Decode batch. #running-req: 1, #token: 637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.63, #queue-req: 0, 
[2025-07-28 01:08:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1193, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.95, #queue-req: 0, 
[2025-07-28 01:08:17 DP1 TP0] Decode batch. #running-req: 1, #token: 677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.78, #queue-req: 0, 
[2025-07-28 01:08:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1233, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.97, #queue-req: 0, 
[2025-07-28 01:08:17 DP1 TP0] Decode batch. #running-req: 1, #token: 717, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.68, #queue-req: 0, 
[2025-07-28 01:08:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.97, #queue-req: 0, 
[2025-07-28 01:08:18 DP1 TP0] Decode batch. #running-req: 1, #token: 757, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.75, #queue-req: 0, 
[2025-07-28 01:08:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.81, #queue-req: 0, 
[2025-07-28 01:08:18 DP1 TP0] Decode batch. #running-req: 1, #token: 797, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.56, #queue-req: 0, 
[2025-07-28 01:08:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.86, #queue-req: 0, 
[2025-07-28 01:08:18 DP1 TP0] Decode batch. #running-req: 1, #token: 837, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.46, #queue-req: 0, 
[2025-07-28 01:08:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.75, #queue-req: 0, 
[2025-07-28 01:08:18 DP1 TP0] Decode batch. #running-req: 1, #token: 877, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.43, #queue-req: 0, 
[2025-07-28 01:08:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1433, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.68, #queue-req: 0, 
[2025-07-28 01:08:18 DP1 TP0] Decode batch. #running-req: 1, #token: 917, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.59, #queue-req: 0, 
[2025-07-28 01:08:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1473, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.72, #queue-req: 0, 
[2025-07-28 01:08:19 DP1 TP0] Decode batch. #running-req: 1, #token: 957, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.71, #queue-req: 0, 
[2025-07-28 01:08:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1513, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.75, #queue-req: 0, 
[2025-07-28 01:08:19 DP1 TP0] Decode batch. #running-req: 1, #token: 997, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.40, #queue-req: 0, 
[2025-07-28 01:08:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1553, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.74, #queue-req: 0, 
[2025-07-28 01:08:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1037, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.49, #queue-req: 0, 
[2025-07-28 01:08:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1593, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.70, #queue-req: 0, 
[2025-07-28 01:08:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1077, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.89, #queue-req: 0, 
[2025-07-28 01:08:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1633, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.63, #queue-req: 0, 
[2025-07-28 01:08:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1117, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.91, #queue-req: 0, 
[2025-07-28 01:08:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1673, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.58, #queue-req: 0, 
[2025-07-28 01:08:20 DP1 TP0] Decode batch. #running-req: 1, #token: 1157, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.00, #queue-req: 0, 
[2025-07-28 01:08:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1713, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.78, #queue-req: 0, 
[2025-07-28 01:08:20 DP1 TP0] Decode batch. #running-req: 1, #token: 1197, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.63, #queue-req: 0, 
[2025-07-28 01:08:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1753, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.76, #queue-req: 0, 
[2025-07-28 01:08:20 DP1 TP0] Decode batch. #running-req: 1, #token: 1237, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.06, #queue-req: 0, 
[2025-07-28 01:08:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1793, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.68, #queue-req: 0, 
[2025-07-28 01:08:20 DP1 TP0] Decode batch. #running-req: 1, #token: 1277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.08, #queue-req: 0, 
[2025-07-28 01:08:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1833, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.64, #queue-req: 0, 
[2025-07-28 01:08:20 DP1 TP0] Decode batch. #running-req: 1, #token: 1317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.88, #queue-req: 0, 
[2025-07-28 01:08:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1873, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.57, #queue-req: 0, 
[2025-07-28 01:08:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.92, #queue-req: 0, 
[2025-07-28 01:08:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1913, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.61, #queue-req: 0, 
[2025-07-28 01:08:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.88, #queue-req: 0, 
[2025-07-28 01:08:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1953, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.55, #queue-req: 0, 
[2025-07-28 01:08:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.91, #queue-req: 0, 
[2025-07-28 01:08:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1993, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.73, #queue-req: 0, 
[2025-07-28 01:08:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.97, #queue-req: 0, 
[2025-07-28 01:08:21 DP0 TP0] Decode batch. #running-req: 1, #token: 2033, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.50, #queue-req: 0, 
[2025-07-28 01:08:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.07, #queue-req: 0, 
[2025-07-28 01:08:22 DP0 TP0] Decode batch. #running-req: 1, #token: 2073, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.23, #queue-req: 0, 
[2025-07-28 01:08:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.81, #queue-req: 0, 
[2025-07-28 01:08:22 DP0 TP0] Decode batch. #running-req: 1, #token: 2113, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.13, #queue-req: 0, 
[2025-07-28 01:08:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.75, #queue-req: 0, 
[2025-07-28 01:08:22 DP0 TP0] Decode batch. #running-req: 1, #token: 2153, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.90, #queue-req: 0, 
[2025-07-28 01:08:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.93, #queue-req: 0, 
[2025-07-28 01:08:22 DP0 TP0] Decode batch. #running-req: 1, #token: 2193, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.06, #queue-req: 0, 
[2025-07-28 01:08:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.90, #queue-req: 0, 
[2025-07-28 01:08:22 DP0 TP0] Decode batch. #running-req: 1, #token: 2233, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.33, #queue-req: 0, 
[2025-07-28 01:08:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1717, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.03, #queue-req: 0, 
[2025-07-28 01:08:23 DP0 TP0] Decode batch. #running-req: 1, #token: 2273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.33, #queue-req: 0, 
[2025-07-28 01:08:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1757, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.00, #queue-req: 0, 
[2025-07-28 01:08:23 DP0 TP0] Decode batch. #running-req: 1, #token: 2313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.21, #queue-req: 0, 
[2025-07-28 01:08:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1797, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.01, #queue-req: 0, 
[2025-07-28 01:08:23 DP0 TP0] Decode batch. #running-req: 1, #token: 2353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.11, #queue-req: 0, 
[2025-07-28 01:08:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1837, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.75, #queue-req: 0, 
[2025-07-28 01:08:23 DP0 TP0] Decode batch. #running-req: 1, #token: 2393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.04, #queue-req: 0, 
[2025-07-28 01:08:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1877, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.85, #queue-req: 0, 
[2025-07-28 01:08:23] INFO:     127.0.0.1:50386 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:08:23 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 199, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:08:23] INFO:     127.0.0.1:50400 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:08:23 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 194, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:08:23 DP0 TP0] Decode batch. #running-req: 1, #token: 295, token usage: 0.00, cuda graph: True, gen throughput (token/s): 160.43, #queue-req: 0, 
[2025-07-28 01:08:24 DP1 TP0] Decode batch. #running-req: 1, #token: 305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 157.98, #queue-req: 0, 
[2025-07-28 01:08:24 DP0 TP0] Decode batch. #running-req: 1, #token: 335, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.26, #queue-req: 0, 
[2025-07-28 01:08:24 DP1 TP0] Decode batch. #running-req: 1, #token: 345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.82, #queue-req: 0, 
[2025-07-28 01:08:24 DP0 TP0] Decode batch. #running-req: 1, #token: 375, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.33, #queue-req: 0, 
[2025-07-28 01:08:24 DP1 TP0] Decode batch. #running-req: 1, #token: 385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.95, #queue-req: 0, 
[2025-07-28 01:08:24 DP0 TP0] Decode batch. #running-req: 1, #token: 415, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.03, #queue-req: 0, 
[2025-07-28 01:08:24 DP1 TP0] Decode batch. #running-req: 1, #token: 425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.10, #queue-req: 0, 
[2025-07-28 01:08:24 DP0 TP0] Decode batch. #running-req: 1, #token: 455, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.08, #queue-req: 0, 
[2025-07-28 01:08:24 DP1 TP0] Decode batch. #running-req: 1, #token: 465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.86, #queue-req: 0, 
[2025-07-28 01:08:24 DP0 TP0] Decode batch. #running-req: 1, #token: 495, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.09, #queue-req: 0, 
[2025-07-28 01:08:25 DP1 TP0] Decode batch. #running-req: 1, #token: 505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.06, #queue-req: 0, 
[2025-07-28 01:08:25 DP0 TP0] Decode batch. #running-req: 1, #token: 535, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.87, #queue-req: 0, 
[2025-07-28 01:08:25 DP1 TP0] Decode batch. #running-req: 1, #token: 545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.58, #queue-req: 0, 
[2025-07-28 01:08:25 DP0 TP0] Decode batch. #running-req: 1, #token: 575, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.59, #queue-req: 0, 
[2025-07-28 01:08:25 DP1 TP0] Decode batch. #running-req: 1, #token: 585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.35, #queue-req: 0, 
[2025-07-28 01:08:25 DP0 TP0] Decode batch. #running-req: 1, #token: 615, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.70, #queue-req: 0, 
[2025-07-28 01:08:25 DP1 TP0] Decode batch. #running-req: 1, #token: 625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.43, #queue-req: 0, 
[2025-07-28 01:08:25 DP0 TP0] Decode batch. #running-req: 1, #token: 655, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.67, #queue-req: 0, 
[2025-07-28 01:08:25 DP1 TP0] Decode batch. #running-req: 1, #token: 665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.66, #queue-req: 0, 
[2025-07-28 01:08:25 DP0 TP0] Decode batch. #running-req: 1, #token: 695, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.59, #queue-req: 0, 
[2025-07-28 01:08:26 DP1 TP0] Decode batch. #running-req: 1, #token: 705, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.58, #queue-req: 0, 
[2025-07-28 01:08:26 DP0 TP0] Decode batch. #running-req: 1, #token: 735, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.65, #queue-req: 0, 
[2025-07-28 01:08:26 DP1 TP0] Decode batch. #running-req: 1, #token: 745, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.62, #queue-req: 0, 
[2025-07-28 01:08:26 DP0 TP0] Decode batch. #running-req: 1, #token: 775, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.37, #queue-req: 0, 
[2025-07-28 01:08:26 DP1 TP0] Decode batch. #running-req: 1, #token: 785, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.64, #queue-req: 0, 
[2025-07-28 01:08:26 DP0 TP0] Decode batch. #running-req: 1, #token: 815, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.39, #queue-req: 0, 
[2025-07-28 01:08:26 DP1 TP0] Decode batch. #running-req: 1, #token: 825, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.42, #queue-req: 0, 
[2025-07-28 01:08:26 DP0 TP0] Decode batch. #running-req: 1, #token: 855, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.35, #queue-req: 0, 
[2025-07-28 01:08:26 DP1 TP0] Decode batch. #running-req: 1, #token: 865, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.43, #queue-req: 0, 
[2025-07-28 01:08:26 DP0 TP0] Decode batch. #running-req: 1, #token: 895, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.32, #queue-req: 0, 
[2025-07-28 01:08:27 DP1 TP0] Decode batch. #running-req: 1, #token: 905, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.34, #queue-req: 0, 
[2025-07-28 01:08:27 DP0 TP0] Decode batch. #running-req: 1, #token: 935, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.49, #queue-req: 0, 
[2025-07-28 01:08:27 DP1 TP0] Decode batch. #running-req: 1, #token: 945, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.52, #queue-req: 0, 
[2025-07-28 01:08:27 DP0 TP0] Decode batch. #running-req: 1, #token: 975, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.25, #queue-req: 0, 
[2025-07-28 01:08:27 DP1 TP0] Decode batch. #running-req: 1, #token: 985, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.53, #queue-req: 0, 
[2025-07-28 01:08:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1015, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.53, #queue-req: 0, 
[2025-07-28 01:08:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1025, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.34, #queue-req: 0, 
[2025-07-28 01:08:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1055, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.61, #queue-req: 0, 
[2025-07-28 01:08:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1065, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.84, #queue-req: 0, 
[2025-07-28 01:08:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1095, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.15, #queue-req: 0, 
[2025-07-28 01:08:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1105, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.85, #queue-req: 0, 
[2025-07-28 01:08:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1135, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.48, #queue-req: 0, 
[2025-07-28 01:08:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1145, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.47, #queue-req: 0, 
[2025-07-28 01:08:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1175, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.83, #queue-req: 0, 
[2025-07-28 01:08:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1185, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.03, #queue-req: 0, 
[2025-07-28 01:08:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1215, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.89, #queue-req: 0, 
[2025-07-28 01:08:28] INFO:     127.0.0.1:60890 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:08:28 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 167, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:08:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1225, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.08, #queue-req: 0, 
[2025-07-28 01:08:28 DP0 TP0] Decode batch. #running-req: 1, #token: 282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 165.10, #queue-req: 0, 
[2025-07-28 01:08:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.14, #queue-req: 0, 
[2025-07-28 01:08:28 DP0 TP0] Decode batch. #running-req: 1, #token: 322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.39, #queue-req: 0, 
[2025-07-28 01:08:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.96, #queue-req: 0, 
[2025-07-28 01:08:29 DP0 TP0] Decode batch. #running-req: 1, #token: 362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.01, #queue-req: 0, 
[2025-07-28 01:08:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.86, #queue-req: 0, 
[2025-07-28 01:08:29 DP0 TP0] Decode batch. #running-req: 1, #token: 402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.17, #queue-req: 0, 
[2025-07-28 01:08:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.67, #queue-req: 0, 
[2025-07-28 01:08:29 DP0 TP0] Decode batch. #running-req: 1, #token: 442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.01, #queue-req: 0, 
[2025-07-28 01:08:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.92, #queue-req: 0, 
[2025-07-28 01:08:29 DP0 TP0] Decode batch. #running-req: 1, #token: 482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.80, #queue-req: 0, 
[2025-07-28 01:08:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.94, #queue-req: 0, 
[2025-07-28 01:08:29 DP0 TP0] Decode batch. #running-req: 1, #token: 522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.88, #queue-req: 0, 
[2025-07-28 01:08:30 DP1 TP0] Decode batch. #running-req: 1, #token: 1505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.04, #queue-req: 0, 
[2025-07-28 01:08:30 DP0 TP0] Decode batch. #running-req: 1, #token: 562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.34, #queue-req: 0, 
[2025-07-28 01:08:30 DP1 TP0] Decode batch. #running-req: 1, #token: 1545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.95, #queue-req: 0, 
[2025-07-28 01:08:30 DP0 TP0] Decode batch. #running-req: 1, #token: 602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.79, #queue-req: 0, 
[2025-07-28 01:08:30 DP1 TP0] Decode batch. #running-req: 1, #token: 1585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.66, #queue-req: 0, 
[2025-07-28 01:08:30 DP0 TP0] Decode batch. #running-req: 1, #token: 642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.59, #queue-req: 0, 
[2025-07-28 01:08:30 DP1 TP0] Decode batch. #running-req: 1, #token: 1625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.83, #queue-req: 0, 
[2025-07-28 01:08:30 DP0 TP0] Decode batch. #running-req: 1, #token: 682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.70, #queue-req: 0, 
[2025-07-28 01:08:30 DP1 TP0] Decode batch. #running-req: 1, #token: 1665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.87, #queue-req: 0, 
[2025-07-28 01:08:30 DP0 TP0] Decode batch. #running-req: 1, #token: 722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.67, #queue-req: 0, 
[2025-07-28 01:08:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1705, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.91, #queue-req: 0, 
[2025-07-28 01:08:31 DP0 TP0] Decode batch. #running-req: 1, #token: 762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.71, #queue-req: 0, 
[2025-07-28 01:08:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1745, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.00, #queue-req: 0, 
[2025-07-28 01:08:31 DP0 TP0] Decode batch. #running-req: 1, #token: 802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.43, #queue-req: 0, 
[2025-07-28 01:08:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1785, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.49, #queue-req: 0, 
[2025-07-28 01:08:31 DP0 TP0] Decode batch. #running-req: 1, #token: 842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.30, #queue-req: 0, 
[2025-07-28 01:08:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1825, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.74, #queue-req: 0, 
[2025-07-28 01:08:31 DP0 TP0] Decode batch. #running-req: 1, #token: 882, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.33, #queue-req: 0, 
[2025-07-28 01:08:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1865, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.74, #queue-req: 0, 
[2025-07-28 01:08:31 DP0 TP0] Decode batch. #running-req: 1, #token: 922, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.56, #queue-req: 0, 
[2025-07-28 01:08:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1905, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.69, #queue-req: 0, 
[2025-07-28 01:08:32 DP0 TP0] Decode batch. #running-req: 1, #token: 962, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.42, #queue-req: 0, 
[2025-07-28 01:08:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1945, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.83, #queue-req: 0, 
[2025-07-28 01:08:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1002, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.52, #queue-req: 0, 
[2025-07-28 01:08:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1985, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.45, #queue-req: 0, 
[2025-07-28 01:08:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1042, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.19, #queue-req: 0, 
[2025-07-28 01:08:32 DP1 TP0] Decode batch. #running-req: 1, #token: 2025, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.30, #queue-req: 0, 
[2025-07-28 01:08:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1082, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.29, #queue-req: 0, 
[2025-07-28 01:08:32 DP1 TP0] Decode batch. #running-req: 1, #token: 2065, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.84, #queue-req: 0, 
[2025-07-28 01:08:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1122, token usage: 0.00, cuda graph: True, gen throughput (token/s): 189.89, #queue-req: 0, 
[2025-07-28 01:08:33 DP1 TP0] Decode batch. #running-req: 1, #token: 2105, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.63, #queue-req: 0, 
[2025-07-28 01:08:33 DP0 TP0] Decode batch. #running-req: 1, #token: 1162, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.66, #queue-req: 0, 
[2025-07-28 01:08:33 DP1 TP0] Decode batch. #running-req: 1, #token: 2145, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.04, #queue-req: 0, 
[2025-07-28 01:08:33 DP0 TP0] Decode batch. #running-req: 1, #token: 1202, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.28, #queue-req: 0, 
[2025-07-28 01:08:33 DP1 TP0] Decode batch. #running-req: 1, #token: 2185, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.28, #queue-req: 0, 
[2025-07-28 01:08:33 DP0 TP0] Decode batch. #running-req: 1, #token: 1242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.64, #queue-req: 0, 
[2025-07-28 01:08:33 DP1 TP0] Decode batch. #running-req: 1, #token: 2225, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.49, #queue-req: 0, 
[2025-07-28 01:08:33 DP0 TP0] Decode batch. #running-req: 1, #token: 1282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.90, #queue-req: 0, 
[2025-07-28 01:08:33 DP1 TP0] Decode batch. #running-req: 1, #token: 2265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.52, #queue-req: 0, 
[2025-07-28 01:08:33 DP0 TP0] Decode batch. #running-req: 1, #token: 1322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.82, #queue-req: 0, 
[2025-07-28 01:08:34 DP1 TP0] Decode batch. #running-req: 1, #token: 2305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.42, #queue-req: 0, 
[2025-07-28 01:08:34 DP0 TP0] Decode batch. #running-req: 1, #token: 1362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.87, #queue-req: 0, 
[2025-07-28 01:08:34 DP1 TP0] Decode batch. #running-req: 1, #token: 2345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.31, #queue-req: 0, 
[2025-07-28 01:08:34 DP0 TP0] Decode batch. #running-req: 1, #token: 1402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.87, #queue-req: 0, 
[2025-07-28 01:08:34] INFO:     127.0.0.1:60912 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:08:34 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 172, #cached-token: 89, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:08:34 DP1 TP0] Decode batch. #running-req: 2, #token: 2560, token usage: 0.00, cuda graph: True, gen throughput (token/s): 177.47, #queue-req: 0, 
[2025-07-28 01:08:34 DP1 TP0] Decode batch. #running-req: 2, #token: 2640, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.38, #queue-req: 0, 
[2025-07-28 01:08:34 DP1 TP0] Decode batch. #running-req: 2, #token: 2720, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.14, #queue-req: 0, 
[2025-07-28 01:08:35 DP1 TP0] Decode batch. #running-req: 2, #token: 2800, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.85, #queue-req: 0, 
[2025-07-28 01:08:35 DP1 TP0] Decode batch. #running-req: 2, #token: 2880, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.37, #queue-req: 0, 
[2025-07-28 01:08:35 DP1 TP0] Decode batch. #running-req: 2, #token: 2960, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.37, #queue-req: 0, 
[2025-07-28 01:08:35 DP1 TP0] Decode batch. #running-req: 2, #token: 3040, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.52, #queue-req: 0, 
[2025-07-28 01:08:35 DP1 TP0] Decode batch. #running-req: 2, #token: 3120, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.89, #queue-req: 0, 
[2025-07-28 01:08:36 DP1 TP0] Decode batch. #running-req: 2, #token: 3200, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.14, #queue-req: 0, 
[2025-07-28 01:08:36 DP1 TP0] Decode batch. #running-req: 2, #token: 3280, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.33, #queue-req: 0, 
[2025-07-28 01:08:36 DP1 TP0] Decode batch. #running-req: 2, #token: 3360, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.21, #queue-req: 0, 
[2025-07-28 01:08:36 DP1 TP0] Decode batch. #running-req: 2, #token: 3440, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.57, #queue-req: 0, 
[2025-07-28 01:08:36 DP1 TP0] Decode batch. #running-req: 2, #token: 3520, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.72, #queue-req: 0, 
[2025-07-28 01:08:37 DP1 TP0] Decode batch. #running-req: 2, #token: 3600, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.55, #queue-req: 0, 
[2025-07-28 01:08:37 DP1 TP0] Decode batch. #running-req: 2, #token: 3680, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.26, #queue-req: 0, 
[2025-07-28 01:08:37 DP1 TP0] Decode batch. #running-req: 2, #token: 3760, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.40, #queue-req: 0, 
[2025-07-28 01:08:37 DP1 TP0] Decode batch. #running-req: 2, #token: 3840, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.63, #queue-req: 0, 
[2025-07-28 01:08:38 DP1 TP0] Decode batch. #running-req: 2, #token: 3920, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.27, #queue-req: 0, 
[2025-07-28 01:08:38 DP1 TP0] Decode batch. #running-req: 2, #token: 4000, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.97, #queue-req: 0, 
[2025-07-28 01:08:38 DP1 TP0] Decode batch. #running-req: 2, #token: 4080, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.16, #queue-req: 0, 
[2025-07-28 01:08:38 DP1 TP0] Decode batch. #running-req: 2, #token: 4160, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.98, #queue-req: 0, 
[2025-07-28 01:08:38 DP1 TP0] Decode batch. #running-req: 2, #token: 4240, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.05, #queue-req: 0, 
[2025-07-28 01:08:39 DP1 TP0] Decode batch. #running-req: 2, #token: 4320, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.02, #queue-req: 0, 
[2025-07-28 01:08:39 DP1 TP0] Decode batch. #running-req: 2, #token: 4400, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.61, #queue-req: 0, 
[2025-07-28 01:08:39 DP1 TP0] Decode batch. #running-req: 2, #token: 4480, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.90, #queue-req: 0, 
[2025-07-28 01:08:39 DP1 TP0] Decode batch. #running-req: 2, #token: 4560, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.80, #queue-req: 0, 
[2025-07-28 01:08:39 DP1 TP0] Decode batch. #running-req: 2, #token: 4640, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.86, #queue-req: 0, 
[2025-07-28 01:08:40 DP1 TP0] Decode batch. #running-req: 2, #token: 4720, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.64, #queue-req: 0, 
[2025-07-28 01:08:40 DP1 TP0] Decode batch. #running-req: 2, #token: 4800, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.55, #queue-req: 0, 
[2025-07-28 01:08:40 DP1 TP0] Decode batch. #running-req: 2, #token: 4880, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.55, #queue-req: 0, 
[2025-07-28 01:08:40 DP1 TP0] Decode batch. #running-req: 2, #token: 4960, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.42, #queue-req: 0, 
[2025-07-28 01:08:40 DP1 TP0] Decode batch. #running-req: 2, #token: 5040, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.22, #queue-req: 0, 
[2025-07-28 01:08:41 DP1 TP0] Decode batch. #running-req: 2, #token: 5120, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.35, #queue-req: 0, 
[2025-07-28 01:08:41 DP1 TP0] Decode batch. #running-req: 2, #token: 5200, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.54, #queue-req: 0, 
[2025-07-28 01:08:41 DP1 TP0] Decode batch. #running-req: 2, #token: 5280, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.50, #queue-req: 0, 
[2025-07-28 01:08:41 DP1 TP0] Decode batch. #running-req: 2, #token: 5360, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.31, #queue-req: 0, 
[2025-07-28 01:08:41 DP1 TP0] Decode batch. #running-req: 2, #token: 5440, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.44, #queue-req: 0, 
[2025-07-28 01:08:42] INFO:     127.0.0.1:54378 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:08:42 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 219, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:08:42 DP1 TP0] Decode batch. #running-req: 1, #token: 3865, token usage: 0.00, cuda graph: True, gen throughput (token/s): 320.37, #queue-req: 0, 
[2025-07-28 01:08:42 DP0 TP0] Decode batch. #running-req: 1, #token: 331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 4.99, #queue-req: 0, 
[2025-07-28 01:08:42 DP1 TP0] Decode batch. #running-req: 1, #token: 3905, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.84, #queue-req: 0, 
[2025-07-28 01:08:42 DP0 TP0] Decode batch. #running-req: 1, #token: 371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 191.71, #queue-req: 0, 
[2025-07-28 01:08:42 DP1 TP0] Decode batch. #running-req: 1, #token: 3945, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.87, #queue-req: 0, 
[2025-07-28 01:08:42 DP0 TP0] Decode batch. #running-req: 1, #token: 411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.93, #queue-req: 0, 
[2025-07-28 01:08:42 DP1 TP0] Decode batch. #running-req: 1, #token: 3985, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.91, #queue-req: 0, 
[2025-07-28 01:08:42 DP0 TP0] Decode batch. #running-req: 1, #token: 451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.45, #queue-req: 0, 
[2025-07-28 01:08:42 DP1 TP0] Decode batch. #running-req: 1, #token: 4025, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.96, #queue-req: 0, 
[2025-07-28 01:08:43 DP0 TP0] Decode batch. #running-req: 1, #token: 491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.72, #queue-req: 0, 
[2025-07-28 01:08:43 DP1 TP0] Decode batch. #running-req: 1, #token: 4065, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.88, #queue-req: 0, 
[2025-07-28 01:08:43 DP0 TP0] Decode batch. #running-req: 1, #token: 531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.77, #queue-req: 0, 
[2025-07-28 01:08:43 DP1 TP0] Decode batch. #running-req: 1, #token: 4105, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.80, #queue-req: 0, 
[2025-07-28 01:08:43 DP0 TP0] Decode batch. #running-req: 1, #token: 571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.49, #queue-req: 0, 
[2025-07-28 01:08:43 DP1 TP0] Decode batch. #running-req: 1, #token: 4145, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.33, #queue-req: 0, 
[2025-07-28 01:08:43 DP0 TP0] Decode batch. #running-req: 1, #token: 611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.47, #queue-req: 0, 
[2025-07-28 01:08:43 DP1 TP0] Decode batch. #running-req: 1, #token: 4185, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.29, #queue-req: 0, 
[2025-07-28 01:08:43] INFO:     127.0.0.1:60904 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:08:43 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 155, #cached-token: 90, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:08:43 DP0 TP0] Decode batch. #running-req: 1, #token: 651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.49, #queue-req: 0, 
[2025-07-28 01:08:44 DP1 TP0] Decode batch. #running-req: 1, #token: 270, token usage: 0.00, cuda graph: True, gen throughput (token/s): 159.32, #queue-req: 0, 
[2025-07-28 01:08:44 DP0 TP0] Decode batch. #running-req: 1, #token: 691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.70, #queue-req: 0, 
[2025-07-28 01:08:44 DP1 TP0] Decode batch. #running-req: 1, #token: 310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.95, #queue-req: 0, 
[2025-07-28 01:08:44 DP0 TP0] Decode batch. #running-req: 1, #token: 731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.68, #queue-req: 0, 
[2025-07-28 01:08:44 DP1 TP0] Decode batch. #running-req: 1, #token: 350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.96, #queue-req: 0, 
[2025-07-28 01:08:44 DP0 TP0] Decode batch. #running-req: 1, #token: 771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.71, #queue-req: 0, 
[2025-07-28 01:08:44 DP1 TP0] Decode batch. #running-req: 1, #token: 390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.72, #queue-req: 0, 
[2025-07-28 01:08:44 DP0 TP0] Decode batch. #running-req: 1, #token: 811, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.09, #queue-req: 0, 
[2025-07-28 01:08:44 DP1 TP0] Decode batch. #running-req: 1, #token: 430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.05, #queue-req: 0, 
[2025-07-28 01:08:44 DP0 TP0] Decode batch. #running-req: 1, #token: 851, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.54, #queue-req: 0, 
[2025-07-28 01:08:45 DP1 TP0] Decode batch. #running-req: 1, #token: 470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.07, #queue-req: 0, 
[2025-07-28 01:08:45 DP0 TP0] Decode batch. #running-req: 1, #token: 891, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.51, #queue-req: 0, 
[2025-07-28 01:08:45 DP1 TP0] Decode batch. #running-req: 1, #token: 510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.08, #queue-req: 0, 
[2025-07-28 01:08:45 DP0 TP0] Decode batch. #running-req: 1, #token: 931, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.71, #queue-req: 0, 
[2025-07-28 01:08:45 DP1 TP0] Decode batch. #running-req: 1, #token: 550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.65, #queue-req: 0, 
[2025-07-28 01:08:45 DP0 TP0] Decode batch. #running-req: 1, #token: 971, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.49, #queue-req: 0, 
[2025-07-28 01:08:45 DP1 TP0] Decode batch. #running-req: 1, #token: 590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.57, #queue-req: 0, 
[2025-07-28 01:08:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1011, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.67, #queue-req: 0, 
[2025-07-28 01:08:45 DP1 TP0] Decode batch. #running-req: 1, #token: 630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.71, #queue-req: 0, 
[2025-07-28 01:08:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1051, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.16, #queue-req: 0, 
[2025-07-28 01:08:46 DP1 TP0] Decode batch. #running-req: 1, #token: 670, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.66, #queue-req: 0, 
[2025-07-28 01:08:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1091, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.02, #queue-req: 0, 
[2025-07-28 01:08:46 DP1 TP0] Decode batch. #running-req: 1, #token: 710, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.39, #queue-req: 0, 
[2025-07-28 01:08:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1131, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.03, #queue-req: 0, 
[2025-07-28 01:08:46 DP1 TP0] Decode batch. #running-req: 1, #token: 750, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.82, #queue-req: 0, 
[2025-07-28 01:08:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1171, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.07, #queue-req: 0, 
[2025-07-28 01:08:46 DP1 TP0] Decode batch. #running-req: 1, #token: 790, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.39, #queue-req: 0, 
[2025-07-28 01:08:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1211, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.05, #queue-req: 0, 
[2025-07-28 01:08:46 DP1 TP0] Decode batch. #running-req: 1, #token: 830, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.15, #queue-req: 0, 
[2025-07-28 01:08:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.96, #queue-req: 0, 
[2025-07-28 01:08:47 DP1 TP0] Decode batch. #running-req: 1, #token: 870, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.88, #queue-req: 0, 
[2025-07-28 01:08:47 DP0 TP0] Decode batch. #running-req: 1, #token: 1291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.04, #queue-req: 0, 
[2025-07-28 01:08:47 DP1 TP0] Decode batch. #running-req: 1, #token: 910, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.46, #queue-req: 0, 
[2025-07-28 01:08:47 DP0 TP0] Decode batch. #running-req: 1, #token: 1331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.76, #queue-req: 0, 
[2025-07-28 01:08:47 DP1 TP0] Decode batch. #running-req: 1, #token: 950, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.57, #queue-req: 0, 
[2025-07-28 01:08:47 DP0 TP0] Decode batch. #running-req: 1, #token: 1371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.86, #queue-req: 0, 
[2025-07-28 01:08:47 DP1 TP0] Decode batch. #running-req: 1, #token: 990, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.78, #queue-req: 0, 
[2025-07-28 01:08:47 DP0 TP0] Decode batch. #running-req: 1, #token: 1411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.90, #queue-req: 0, 
[2025-07-28 01:08:47 DP1 TP0] Decode batch. #running-req: 1, #token: 1030, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.05, #queue-req: 0, 
[2025-07-28 01:08:47 DP0 TP0] Decode batch. #running-req: 1, #token: 1451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.98, #queue-req: 0, 
[2025-07-28 01:08:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1070, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.71, #queue-req: 0, 
[2025-07-28 01:08:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.91, #queue-req: 0, 
[2025-07-28 01:08:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1110, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.90, #queue-req: 0, 
[2025-07-28 01:08:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.88, #queue-req: 0, 
[2025-07-28 01:08:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1150, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.95, #queue-req: 0, 
[2025-07-28 01:08:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.73, #queue-req: 0, 
[2025-07-28 01:08:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1190, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.78, #queue-req: 0, 
[2025-07-28 01:08:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.64, #queue-req: 0, 
[2025-07-28 01:08:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1230, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.02, #queue-req: 0, 
[2025-07-28 01:08:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.77, #queue-req: 0, 
[2025-07-28 01:08:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1270, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.08, #queue-req: 0, 
[2025-07-28 01:08:49 DP0 TP0] Decode batch. #running-req: 1, #token: 1691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.74, #queue-req: 0, 
[2025-07-28 01:08:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.98, #queue-req: 0, 
[2025-07-28 01:08:49 DP0 TP0] Decode batch. #running-req: 1, #token: 1731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.81, #queue-req: 0, 
[2025-07-28 01:08:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.77, #queue-req: 0, 
[2025-07-28 01:08:49 DP0 TP0] Decode batch. #running-req: 1, #token: 1771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.85, #queue-req: 0, 
[2025-07-28 01:08:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.59, #queue-req: 0, 
[2025-07-28 01:08:49 DP0 TP0] Decode batch. #running-req: 1, #token: 1811, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.72, #queue-req: 0, 
[2025-07-28 01:08:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.91, #queue-req: 0, 
[2025-07-28 01:08:49 DP0 TP0] Decode batch. #running-req: 1, #token: 1851, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.59, #queue-req: 0, 
[2025-07-28 01:08:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.06, #queue-req: 0, 
[2025-07-28 01:08:50 DP0 TP0] Decode batch. #running-req: 1, #token: 1891, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.60, #queue-req: 0, 
[2025-07-28 01:08:50] INFO:     127.0.0.1:54392 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:08:50 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 175, #cached-token: 89, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:08:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.04, #queue-req: 0, 
[2025-07-28 01:08:50 DP0 TP0] Decode batch. #running-req: 1, #token: 297, token usage: 0.00, cuda graph: True, gen throughput (token/s): 160.11, #queue-req: 0, 
[2025-07-28 01:08:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.02, #queue-req: 0, 
[2025-07-28 01:08:50 DP0 TP0] Decode batch. #running-req: 1, #token: 337, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.30, #queue-req: 0, 
[2025-07-28 01:08:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.58, #queue-req: 0, 
[2025-07-28 01:08:50] INFO:     127.0.0.1:53260 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:08:50 DP0 TP0] Decode batch. #running-req: 1, #token: 377, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.37, #queue-req: 0, 
[2025-07-28 01:08:50 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 94, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:08:50 DP1 TP0] Decode batch. #running-req: 1, #token: 189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 159.49, #queue-req: 0, 
[2025-07-28 01:08:50 DP0 TP0] Decode batch. #running-req: 1, #token: 417, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.11, #queue-req: 0, 
[2025-07-28 01:08:51 DP1 TP0] Decode batch. #running-req: 1, #token: 229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 203.73, #queue-req: 0, 
[2025-07-28 01:08:51 DP0 TP0] Decode batch. #running-req: 1, #token: 457, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.02, #queue-req: 0, 
[2025-07-28 01:08:51 DP1 TP0] Decode batch. #running-req: 1, #token: 269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 202.01, #queue-req: 0, 
[2025-07-28 01:08:51 DP0 TP0] Decode batch. #running-req: 1, #token: 497, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.12, #queue-req: 0, 
[2025-07-28 01:08:51 DP1 TP0] Decode batch. #running-req: 1, #token: 309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.98, #queue-req: 0, 
[2025-07-28 01:08:51 DP0 TP0] Decode batch. #running-req: 1, #token: 537, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.83, #queue-req: 0, 
[2025-07-28 01:08:51 DP1 TP0] Decode batch. #running-req: 1, #token: 349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.02, #queue-req: 0, 
[2025-07-28 01:08:51 DP0 TP0] Decode batch. #running-req: 1, #token: 577, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.76, #queue-req: 0, 
[2025-07-28 01:08:51 DP1 TP0] Decode batch. #running-req: 1, #token: 389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.00, #queue-req: 0, 
[2025-07-28 01:08:51 DP0 TP0] Decode batch. #running-req: 1, #token: 617, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.71, #queue-req: 0, 
[2025-07-28 01:08:52 DP1 TP0] Decode batch. #running-req: 1, #token: 429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.02, #queue-req: 0, 
[2025-07-28 01:08:52 DP0 TP0] Decode batch. #running-req: 1, #token: 657, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.61, #queue-req: 0, 
[2025-07-28 01:08:52 DP1 TP0] Decode batch. #running-req: 1, #token: 469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.31, #queue-req: 0, 
[2025-07-28 01:08:52 DP0 TP0] Decode batch. #running-req: 1, #token: 697, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.60, #queue-req: 0, 
[2025-07-28 01:08:52 DP1 TP0] Decode batch. #running-req: 1, #token: 509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.96, #queue-req: 0, 
[2025-07-28 01:08:52 DP0 TP0] Decode batch. #running-req: 1, #token: 737, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.65, #queue-req: 0, 
[2025-07-28 01:08:52 DP1 TP0] Decode batch. #running-req: 1, #token: 549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.20, #queue-req: 0, 
[2025-07-28 01:08:52 DP0 TP0] Decode batch. #running-req: 1, #token: 777, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.58, #queue-req: 0, 
[2025-07-28 01:08:52 DP1 TP0] Decode batch. #running-req: 1, #token: 589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.56, #queue-req: 0, 
[2025-07-28 01:08:52 DP0 TP0] Decode batch. #running-req: 1, #token: 817, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.38, #queue-req: 0, 
[2025-07-28 01:08:53 DP1 TP0] Decode batch. #running-req: 1, #token: 629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.43, #queue-req: 0, 
[2025-07-28 01:08:53 DP0 TP0] Decode batch. #running-req: 1, #token: 857, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.43, #queue-req: 0, 
[2025-07-28 01:08:53 DP1 TP0] Decode batch. #running-req: 1, #token: 669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.94, #queue-req: 0, 
[2025-07-28 01:08:53 DP0 TP0] Decode batch. #running-req: 1, #token: 897, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.38, #queue-req: 0, 
[2025-07-28 01:08:53 DP1 TP0] Decode batch. #running-req: 1, #token: 709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.61, #queue-req: 0, 
[2025-07-28 01:08:53 DP0 TP0] Decode batch. #running-req: 1, #token: 937, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.51, #queue-req: 0, 
[2025-07-28 01:08:53 DP1 TP0] Decode batch. #running-req: 1, #token: 749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.73, #queue-req: 0, 
[2025-07-28 01:08:53 DP0 TP0] Decode batch. #running-req: 1, #token: 977, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.34, #queue-req: 0, 
[2025-07-28 01:08:53 DP1 TP0] Decode batch. #running-req: 1, #token: 789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.44, #queue-req: 0, 
[2025-07-28 01:08:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1017, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.51, #queue-req: 0, 
[2025-07-28 01:08:54 DP1 TP0] Decode batch. #running-req: 1, #token: 829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.30, #queue-req: 0, 
[2025-07-28 01:08:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1057, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.90, #queue-req: 0, 
[2025-07-28 01:08:54 DP1 TP0] Decode batch. #running-req: 1, #token: 869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.57, #queue-req: 0, 
[2025-07-28 01:08:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1097, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.79, #queue-req: 0, 
[2025-07-28 01:08:54 DP1 TP0] Decode batch. #running-req: 1, #token: 909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.42, #queue-req: 0, 
[2025-07-28 01:08:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1137, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.87, #queue-req: 0, 
[2025-07-28 01:08:54 DP1 TP0] Decode batch. #running-req: 1, #token: 949, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.16, #queue-req: 0, 
[2025-07-28 01:08:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1177, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.90, #queue-req: 0, 
[2025-07-28 01:08:54 DP1 TP0] Decode batch. #running-req: 1, #token: 989, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.48, #queue-req: 0, 
[2025-07-28 01:08:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1217, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.91, #queue-req: 0, 
[2025-07-28 01:08:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1029, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.45, #queue-req: 0, 
[2025-07-28 01:08:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1257, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.99, #queue-req: 0, 
[2025-07-28 01:08:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1069, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.85, #queue-req: 0, 
[2025-07-28 01:08:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1297, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.78, #queue-req: 0, 
[2025-07-28 01:08:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1109, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.88, #queue-req: 0, 
[2025-07-28 01:08:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1337, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.79, #queue-req: 0, 
[2025-07-28 01:08:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1149, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.08, #queue-req: 0, 
[2025-07-28 01:08:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1377, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.94, #queue-req: 0, 
[2025-07-28 01:08:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.14, #queue-req: 0, 
[2025-07-28 01:08:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1417, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.76, #queue-req: 0, 
[2025-07-28 01:08:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.78, #queue-req: 0, 
[2025-07-28 01:08:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1457, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.92, #queue-req: 0, 
[2025-07-28 01:08:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.43, #queue-req: 0, 
[2025-07-28 01:08:56] INFO:     127.0.0.1:53282 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:08:56 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 115, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:08:56 DP0 TP0] Decode batch. #running-req: 2, #token: 1614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 176.62, #queue-req: 0, 
[2025-07-28 01:08:56 DP0 TP0] Decode batch. #running-req: 2, #token: 1694, token usage: 0.00, cuda graph: True, gen throughput (token/s): 386.70, #queue-req: 0, 
[2025-07-28 01:08:56 DP0 TP0] Decode batch. #running-req: 2, #token: 1774, token usage: 0.00, cuda graph: True, gen throughput (token/s): 386.58, #queue-req: 0, 
[2025-07-28 01:08:57 DP0 TP0] Decode batch. #running-req: 2, #token: 1854, token usage: 0.00, cuda graph: True, gen throughput (token/s): 386.49, #queue-req: 0, 
[2025-07-28 01:08:57 DP0 TP0] Decode batch. #running-req: 2, #token: 1934, token usage: 0.00, cuda graph: True, gen throughput (token/s): 386.14, #queue-req: 0, 
[2025-07-28 01:08:57 DP0 TP0] Decode batch. #running-req: 2, #token: 2014, token usage: 0.00, cuda graph: True, gen throughput (token/s): 386.27, #queue-req: 0, 
[2025-07-28 01:08:57 DP0 TP0] Decode batch. #running-req: 2, #token: 2094, token usage: 0.00, cuda graph: True, gen throughput (token/s): 386.15, #queue-req: 0, 
[2025-07-28 01:08:57] INFO:     127.0.0.1:53276 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:08:57 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 150, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:08:57 DP0 TP0] Decode batch. #running-req: 1, #token: 482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 262.94, #queue-req: 0, 
[2025-07-28 01:08:57 DP1 TP0] Decode batch. #running-req: 1, #token: 261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 24.19, #queue-req: 0, 
[2025-07-28 01:08:58 DP0 TP0] Decode batch. #running-req: 1, #token: 522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.04, #queue-req: 0, 
[2025-07-28 01:08:58 DP1 TP0] Decode batch. #running-req: 1, #token: 301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.17, #queue-req: 0, 
[2025-07-28 01:08:58 DP0 TP0] Decode batch. #running-req: 1, #token: 562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.74, #queue-req: 0, 
[2025-07-28 01:08:58 DP1 TP0] Decode batch. #running-req: 1, #token: 341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.69, #queue-req: 0, 
[2025-07-28 01:08:58 DP0 TP0] Decode batch. #running-req: 1, #token: 602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.71, #queue-req: 0, 
[2025-07-28 01:08:58 DP1 TP0] Decode batch. #running-req: 1, #token: 381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.99, #queue-req: 0, 
[2025-07-28 01:08:58 DP0 TP0] Decode batch. #running-req: 1, #token: 642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.70, #queue-req: 0, 
[2025-07-28 01:08:58 DP1 TP0] Decode batch. #running-req: 1, #token: 421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.52, #queue-req: 0, 
[2025-07-28 01:08:58 DP0 TP0] Decode batch. #running-req: 1, #token: 682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.42, #queue-req: 0, 
[2025-07-28 01:08:58 DP1 TP0] Decode batch. #running-req: 1, #token: 461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.97, #queue-req: 0, 
[2025-07-28 01:08:59 DP0 TP0] Decode batch. #running-req: 1, #token: 722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.63, #queue-req: 0, 
[2025-07-28 01:08:59 DP1 TP0] Decode batch. #running-req: 1, #token: 501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.78, #queue-req: 0, 
[2025-07-28 01:08:59 DP0 TP0] Decode batch. #running-req: 1, #token: 762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.63, #queue-req: 0, 
[2025-07-28 01:08:59 DP1 TP0] Decode batch. #running-req: 1, #token: 541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.73, #queue-req: 0, 
[2025-07-28 01:08:59 DP0 TP0] Decode batch. #running-req: 1, #token: 802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.38, #queue-req: 0, 
[2025-07-28 01:08:59 DP1 TP0] Decode batch. #running-req: 1, #token: 581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.26, #queue-req: 0, 
[2025-07-28 01:08:59 DP0 TP0] Decode batch. #running-req: 1, #token: 842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.45, #queue-req: 0, 
[2025-07-28 01:08:59 DP1 TP0] Decode batch. #running-req: 1, #token: 621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.58, #queue-req: 0, 
[2025-07-28 01:08:59 DP0 TP0] Decode batch. #running-req: 1, #token: 882, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.38, #queue-req: 0, 
[2025-07-28 01:08:59 DP1 TP0] Decode batch. #running-req: 1, #token: 661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.57, #queue-req: 0, 
[2025-07-28 01:09:00 DP0 TP0] Decode batch. #running-req: 1, #token: 922, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.49, #queue-req: 0, 
[2025-07-28 01:09:00 DP1 TP0] Decode batch. #running-req: 1, #token: 701, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.71, #queue-req: 0, 
[2025-07-28 01:09:00 DP0 TP0] Decode batch. #running-req: 1, #token: 962, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.48, #queue-req: 0, 
[2025-07-28 01:09:00 DP1 TP0] Decode batch. #running-req: 1, #token: 741, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.58, #queue-req: 0, 
[2025-07-28 01:09:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1002, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.46, #queue-req: 0, 
[2025-07-28 01:09:00 DP1 TP0] Decode batch. #running-req: 1, #token: 781, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.68, #queue-req: 0, 
[2025-07-28 01:09:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1042, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.35, #queue-req: 0, 
[2025-07-28 01:09:00 DP1 TP0] Decode batch. #running-req: 1, #token: 821, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.29, #queue-req: 0, 
[2025-07-28 01:09:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1082, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.77, #queue-req: 0, 
[2025-07-28 01:09:00 DP1 TP0] Decode batch. #running-req: 1, #token: 861, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.13, #queue-req: 0, 
[2025-07-28 01:09:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1122, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.90, #queue-req: 0, 
[2025-07-28 01:09:01 DP1 TP0] Decode batch. #running-req: 1, #token: 901, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.67, #queue-req: 0, 
[2025-07-28 01:09:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1162, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.88, #queue-req: 0, 
[2025-07-28 01:09:01 DP1 TP0] Decode batch. #running-req: 1, #token: 941, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.55, #queue-req: 0, 
[2025-07-28 01:09:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1202, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.93, #queue-req: 0, 
[2025-07-28 01:09:01 DP1 TP0] Decode batch. #running-req: 1, #token: 981, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.47, #queue-req: 0, 
[2025-07-28 01:09:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.83, #queue-req: 0, 
[2025-07-28 01:09:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1021, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.59, #queue-req: 0, 
[2025-07-28 01:09:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 189.45, #queue-req: 0, 
[2025-07-28 01:09:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1061, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.01, #queue-req: 0, 
[2025-07-28 01:09:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.82, #queue-req: 0, 
[2025-07-28 01:09:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1101, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.03, #queue-req: 0, 
[2025-07-28 01:09:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.88, #queue-req: 0, 
[2025-07-28 01:09:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1141, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.69, #queue-req: 0, 
[2025-07-28 01:09:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.80, #queue-req: 0, 
[2025-07-28 01:09:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1181, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.13, #queue-req: 0, 
[2025-07-28 01:09:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.87, #queue-req: 0, 
[2025-07-28 01:09:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1221, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.07, #queue-req: 0, 
[2025-07-28 01:09:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.96, #queue-req: 0, 
[2025-07-28 01:09:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.08, #queue-req: 0, 
[2025-07-28 01:09:03 DP0 TP0] Decode batch. #running-req: 1, #token: 1522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.91, #queue-req: 0, 
[2025-07-28 01:09:03 DP1 TP0] Decode batch. #running-req: 1, #token: 1301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.98, #queue-req: 0, 
[2025-07-28 01:09:03 DP0 TP0] Decode batch. #running-req: 1, #token: 1562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.78, #queue-req: 0, 
[2025-07-28 01:09:03 DP1 TP0] Decode batch. #running-req: 1, #token: 1341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.97, #queue-req: 0, 
[2025-07-28 01:09:03 DP0 TP0] Decode batch. #running-req: 1, #token: 1602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.74, #queue-req: 0, 
[2025-07-28 01:09:03 DP1 TP0] Decode batch. #running-req: 1, #token: 1381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.88, #queue-req: 0, 
[2025-07-28 01:09:03 DP0 TP0] Decode batch. #running-req: 1, #token: 1642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.37, #queue-req: 0, 
[2025-07-28 01:09:03 DP1 TP0] Decode batch. #running-req: 1, #token: 1421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.89, #queue-req: 0, 
[2025-07-28 01:09:03 DP0 TP0] Decode batch. #running-req: 1, #token: 1682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.83, #queue-req: 0, 
[2025-07-28 01:09:03 DP1 TP0] Decode batch. #running-req: 1, #token: 1461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.04, #queue-req: 0, 
[2025-07-28 01:09:04 DP0 TP0] Decode batch. #running-req: 1, #token: 1722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.85, #queue-req: 0, 
[2025-07-28 01:09:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.09, #queue-req: 0, 
[2025-07-28 01:09:04 DP0 TP0] Decode batch. #running-req: 1, #token: 1762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.88, #queue-req: 0, 
[2025-07-28 01:09:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.64, #queue-req: 0, 
[2025-07-28 01:09:04 DP0 TP0] Decode batch. #running-req: 1, #token: 1802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.88, #queue-req: 0, 
[2025-07-28 01:09:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.84, #queue-req: 0, 
[2025-07-28 01:09:04 DP0 TP0] Decode batch. #running-req: 1, #token: 1842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.48, #queue-req: 0, 
[2025-07-28 01:09:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.67, #queue-req: 0, 
[2025-07-28 01:09:04 DP0 TP0] Decode batch. #running-req: 1, #token: 1882, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.60, #queue-req: 0, 
[2025-07-28 01:09:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.90, #queue-req: 0, 
[2025-07-28 01:09:04] INFO:     127.0.0.1:33178 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:09:04 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 148, #cached-token: 88, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:09:05 DP0 TP0] Decode batch. #running-req: 2, #token: 2092, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.84, #queue-req: 0, 
[2025-07-28 01:09:05 DP0 TP0] Decode batch. #running-req: 2, #token: 2172, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.75, #queue-req: 0, 
[2025-07-28 01:09:05 DP0 TP0] Decode batch. #running-req: 2, #token: 2252, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.94, #queue-req: 0, 
[2025-07-28 01:09:05 DP0 TP0] Decode batch. #running-req: 2, #token: 2332, token usage: 0.00, cuda graph: True, gen throughput (token/s): 367.94, #queue-req: 0, 
[2025-07-28 01:09:05 DP0 TP0] Decode batch. #running-req: 2, #token: 2412, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.53, #queue-req: 0, 
[2025-07-28 01:09:06 DP0 TP0] Decode batch. #running-req: 2, #token: 2492, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.17, #queue-req: 0, 
[2025-07-28 01:09:06 DP0 TP0] Decode batch. #running-req: 2, #token: 2572, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.68, #queue-req: 0, 
[2025-07-28 01:09:06 DP0 TP0] Decode batch. #running-req: 2, #token: 2652, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.88, #queue-req: 0, 
[2025-07-28 01:09:06 DP0 TP0] Decode batch. #running-req: 2, #token: 2732, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.78, #queue-req: 0, 
[2025-07-28 01:09:06 DP0 TP0] Decode batch. #running-req: 2, #token: 2812, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.81, #queue-req: 0, 
[2025-07-28 01:09:07 DP0 TP0] Decode batch. #running-req: 2, #token: 2892, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.65, #queue-req: 0, 
[2025-07-28 01:09:07 DP0 TP0] Decode batch. #running-req: 2, #token: 2972, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.43, #queue-req: 0, 
[2025-07-28 01:09:07 DP0 TP0] Decode batch. #running-req: 2, #token: 3052, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.57, #queue-req: 0, 
[2025-07-28 01:09:07 DP0 TP0] Decode batch. #running-req: 2, #token: 3132, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.36, #queue-req: 0, 
[2025-07-28 01:09:08 DP0 TP0] Decode batch. #running-req: 2, #token: 3212, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.63, #queue-req: 0, 
[2025-07-28 01:09:08 DP0 TP0] Decode batch. #running-req: 2, #token: 3292, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.89, #queue-req: 0, 
[2025-07-28 01:09:08 DP0 TP0] Decode batch. #running-req: 2, #token: 3372, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.62, #queue-req: 0, 
[2025-07-28 01:09:08 DP0 TP0] Decode batch. #running-req: 2, #token: 3452, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.45, #queue-req: 0, 
[2025-07-28 01:09:08 DP0 TP0] Decode batch. #running-req: 2, #token: 3532, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.61, #queue-req: 0, 
[2025-07-28 01:09:09 DP0 TP0] Decode batch. #running-req: 2, #token: 3612, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.45, #queue-req: 0, 
[2025-07-28 01:09:09 DP0 TP0] Decode batch. #running-req: 2, #token: 3692, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.29, #queue-req: 0, 
[2025-07-28 01:09:09 DP0 TP0] Decode batch. #running-req: 2, #token: 3772, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.26, #queue-req: 0, 
[2025-07-28 01:09:09 DP0 TP0] Decode batch. #running-req: 2, #token: 3852, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.14, #queue-req: 0, 
[2025-07-28 01:09:09 DP0 TP0] Decode batch. #running-req: 2, #token: 3932, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.17, #queue-req: 0, 
[2025-07-28 01:09:10 DP0 TP0] Decode batch. #running-req: 2, #token: 4012, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.19, #queue-req: 0, 
[2025-07-28 01:09:10 DP0 TP0] Decode batch. #running-req: 2, #token: 4092, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.13, #queue-req: 0, 
[2025-07-28 01:09:10 DP0 TP0] Decode batch. #running-req: 2, #token: 4172, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.11, #queue-req: 0, 
[2025-07-28 01:09:10 DP0 TP0] Decode batch. #running-req: 2, #token: 4252, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.99, #queue-req: 0, 
[2025-07-28 01:09:10 DP0 TP0] Decode batch. #running-req: 2, #token: 4332, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.03, #queue-req: 0, 
[2025-07-28 01:09:11 DP0 TP0] Decode batch. #running-req: 2, #token: 4412, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.86, #queue-req: 0, 
[2025-07-28 01:09:11 DP0 TP0] Decode batch. #running-req: 2, #token: 4492, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.62, #queue-req: 0, 
[2025-07-28 01:09:11 DP0 TP0] Decode batch. #running-req: 2, #token: 4572, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.29, #queue-req: 0, 
[2025-07-28 01:09:11 DP0 TP0] Decode batch. #running-req: 2, #token: 4652, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.43, #queue-req: 0, 
[2025-07-28 01:09:11 DP0 TP0] Decode batch. #running-req: 2, #token: 4732, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.47, #queue-req: 0, 
[2025-07-28 01:09:12 DP0 TP0] Decode batch. #running-req: 2, #token: 4812, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.51, #queue-req: 0, 
[2025-07-28 01:09:12 DP0 TP0] Decode batch. #running-req: 2, #token: 4892, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.46, #queue-req: 0, 
[2025-07-28 01:09:12 DP0 TP0] Decode batch. #running-req: 2, #token: 4972, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.30, #queue-req: 0, 
[2025-07-28 01:09:12 DP0 TP0] Decode batch. #running-req: 2, #token: 5052, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.49, #queue-req: 0, 
[2025-07-28 01:09:13 DP0 TP0] Decode batch. #running-req: 2, #token: 5132, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.36, #queue-req: 0, 
[2025-07-28 01:09:13 DP0 TP0] Decode batch. #running-req: 2, #token: 5212, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.43, #queue-req: 0, 
[2025-07-28 01:09:13 DP0 TP0] Decode batch. #running-req: 2, #token: 5292, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.13, #queue-req: 0, 
[2025-07-28 01:09:13 DP0 TP0] Decode batch. #running-req: 2, #token: 5372, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.34, #queue-req: 0, 
[2025-07-28 01:09:13 DP0 TP0] Decode batch. #running-req: 2, #token: 5452, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.19, #queue-req: 0, 
[2025-07-28 01:09:14 DP0 TP0] Decode batch. #running-req: 2, #token: 5532, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.27, #queue-req: 0, 
[2025-07-28 01:09:14 DP0 TP0] Decode batch. #running-req: 2, #token: 5612, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.28, #queue-req: 0, 
[2025-07-28 01:09:14 DP0 TP0] Decode batch. #running-req: 2, #token: 5692, token usage: 0.00, cuda graph: True, gen throughput (token/s): 382.87, #queue-req: 0, 
[2025-07-28 01:09:14 DP0 TP0] Decode batch. #running-req: 2, #token: 5772, token usage: 0.00, cuda graph: True, gen throughput (token/s): 382.21, #queue-req: 0, 
[2025-07-28 01:09:14 DP0 TP0] Decode batch. #running-req: 2, #token: 5852, token usage: 0.00, cuda graph: True, gen throughput (token/s): 382.36, #queue-req: 0, 
[2025-07-28 01:09:15] INFO:     127.0.0.1:46514 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:09:15 DP0 TP0] Decode batch. #running-req: 1, #token: 3842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 351.70, #queue-req: 0, 
[2025-07-28 01:09:15 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 160, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:09:15 DP0 TP0] Decode batch. #running-req: 1, #token: 3882, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.67, #queue-req: 0, 
[2025-07-28 01:09:15 DP1 TP0] Decode batch. #running-req: 1, #token: 280, token usage: 0.00, cuda graph: True, gen throughput (token/s): 3.83, #queue-req: 0, 
[2025-07-28 01:09:15 DP0 TP0] Decode batch. #running-req: 1, #token: 3922, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.46, #queue-req: 0, 
[2025-07-28 01:09:15 DP1 TP0] Decode batch. #running-req: 1, #token: 320, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.11, #queue-req: 0, 
[2025-07-28 01:09:15 DP0 TP0] Decode batch. #running-req: 1, #token: 3962, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.55, #queue-req: 0, 
[2025-07-28 01:09:15 DP1 TP0] Decode batch. #running-req: 1, #token: 360, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.96, #queue-req: 0, 
[2025-07-28 01:09:15 DP0 TP0] Decode batch. #running-req: 1, #token: 4002, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.38, #queue-req: 0, 
[2025-07-28 01:09:15 DP1 TP0] Decode batch. #running-req: 1, #token: 400, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.11, #queue-req: 0, 
[2025-07-28 01:09:16 DP0 TP0] Decode batch. #running-req: 1, #token: 4042, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.71, #queue-req: 0, 
[2025-07-28 01:09:16 DP1 TP0] Decode batch. #running-req: 1, #token: 440, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.06, #queue-req: 0, 
[2025-07-28 01:09:16 DP0 TP0] Decode batch. #running-req: 1, #token: 4082, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.66, #queue-req: 0, 
[2025-07-28 01:09:16 DP1 TP0] Decode batch. #running-req: 1, #token: 480, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.01, #queue-req: 0, 
[2025-07-28 01:09:16 DP0 TP0] Decode batch. #running-req: 1, #token: 4122, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.41, #queue-req: 0, 
[2025-07-28 01:09:16 DP1 TP0] Decode batch. #running-req: 1, #token: 520, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.57, #queue-req: 0, 
[2025-07-28 01:09:16 DP0 TP0] Decode batch. #running-req: 1, #token: 4162, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.10, #queue-req: 0, 
[2025-07-28 01:09:16 DP1 TP0] Decode batch. #running-req: 1, #token: 560, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.63, #queue-req: 0, 
[2025-07-28 01:09:16 DP0 TP0] Decode batch. #running-req: 1, #token: 4202, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.19, #queue-req: 0, 
[2025-07-28 01:09:16 DP1 TP0] Decode batch. #running-req: 1, #token: 600, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.62, #queue-req: 0, 
[2025-07-28 01:09:17 DP0 TP0] Decode batch. #running-req: 1, #token: 4242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.26, #queue-req: 0, 
[2025-07-28 01:09:17 DP1 TP0] Decode batch. #running-req: 1, #token: 640, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.63, #queue-req: 0, 
[2025-07-28 01:09:17 DP0 TP0] Decode batch. #running-req: 1, #token: 4282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.26, #queue-req: 0, 
[2025-07-28 01:09:17 DP1 TP0] Decode batch. #running-req: 1, #token: 680, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.87, #queue-req: 0, 
[2025-07-28 01:09:17 DP0 TP0] Decode batch. #running-req: 1, #token: 4322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.19, #queue-req: 0, 
[2025-07-28 01:09:17 DP1 TP0] Decode batch. #running-req: 1, #token: 720, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.82, #queue-req: 0, 
[2025-07-28 01:09:17 DP0 TP0] Decode batch. #running-req: 1, #token: 4362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.19, #queue-req: 0, 
[2025-07-28 01:09:17 DP1 TP0] Decode batch. #running-req: 1, #token: 760, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.83, #queue-req: 0, 
[2025-07-28 01:09:17 DP0 TP0] Decode batch. #running-req: 1, #token: 4402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.11, #queue-req: 0, 
[2025-07-28 01:09:17 DP1 TP0] Decode batch. #running-req: 1, #token: 800, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.51, #queue-req: 0, 
[2025-07-28 01:09:18 DP0 TP0] Decode batch. #running-req: 1, #token: 4442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.20, #queue-req: 0, 
[2025-07-28 01:09:18 DP1 TP0] Decode batch. #running-req: 1, #token: 840, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.56, #queue-req: 0, 
[2025-07-28 01:09:18 DP0 TP0] Decode batch. #running-req: 1, #token: 4482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.07, #queue-req: 0, 
[2025-07-28 01:09:18 DP1 TP0] Decode batch. #running-req: 1, #token: 880, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.35, #queue-req: 0, 
[2025-07-28 01:09:18 DP1 TP0] Decode batch. #running-req: 1, #token: 920, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.59, #queue-req: 0, 
[2025-07-28 01:09:18 DP0 TP0] Decode batch. #running-req: 1, #token: 4522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.27, #queue-req: 0, 
[2025-07-28 01:09:18 DP1 TP0] Decode batch. #running-req: 1, #token: 960, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.73, #queue-req: 0, 
[2025-07-28 01:09:18 DP0 TP0] Decode batch. #running-req: 1, #token: 4562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.25, #queue-req: 0, 
[2025-07-28 01:09:18 DP1 TP0] Decode batch. #running-req: 1, #token: 1000, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.50, #queue-req: 0, 
[2025-07-28 01:09:18 DP0 TP0] Decode batch. #running-req: 1, #token: 4602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.17, #queue-req: 0, 
[2025-07-28 01:09:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1040, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.49, #queue-req: 0, 
[2025-07-28 01:09:19 DP0 TP0] Decode batch. #running-req: 1, #token: 4642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.12, #queue-req: 0, 
[2025-07-28 01:09:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1080, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.04, #queue-req: 0, 
[2025-07-28 01:09:19 DP0 TP0] Decode batch. #running-req: 1, #token: 4682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.12, #queue-req: 0, 
[2025-07-28 01:09:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1120, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.09, #queue-req: 0, 
[2025-07-28 01:09:19 DP0 TP0] Decode batch. #running-req: 1, #token: 4722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.11, #queue-req: 0, 
[2025-07-28 01:09:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1160, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.11, #queue-req: 0, 
[2025-07-28 01:09:19 DP0 TP0] Decode batch. #running-req: 1, #token: 4762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.19, #queue-req: 0, 
[2025-07-28 01:09:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1200, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.18, #queue-req: 0, 
[2025-07-28 01:09:19 DP0 TP0] Decode batch. #running-req: 1, #token: 4802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.20, #queue-req: 0, 
[2025-07-28 01:09:20 DP1 TP0] Decode batch. #running-req: 1, #token: 1240, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.06, #queue-req: 0, 
[2025-07-28 01:09:20 DP0 TP0] Decode batch. #running-req: 1, #token: 4842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.23, #queue-req: 0, 
[2025-07-28 01:09:20 DP1 TP0] Decode batch. #running-req: 1, #token: 1280, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.07, #queue-req: 0, 
[2025-07-28 01:09:20 DP0 TP0] Decode batch. #running-req: 1, #token: 4882, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.13, #queue-req: 0, 
[2025-07-28 01:09:20 DP1 TP0] Decode batch. #running-req: 1, #token: 1320, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.24, #queue-req: 0, 
[2025-07-28 01:09:20 DP0 TP0] Decode batch. #running-req: 1, #token: 4922, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.09, #queue-req: 0, 
[2025-07-28 01:09:20 DP1 TP0] Decode batch. #running-req: 1, #token: 1360, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.91, #queue-req: 0, 
[2025-07-28 01:09:20 DP0 TP0] Decode batch. #running-req: 1, #token: 4962, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.97, #queue-req: 0, 
[2025-07-28 01:09:20 DP1 TP0] Decode batch. #running-req: 1, #token: 1400, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.92, #queue-req: 0, 
[2025-07-28 01:09:20 DP0 TP0] Decode batch. #running-req: 1, #token: 5002, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.13, #queue-req: 0, 
[2025-07-28 01:09:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1440, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.20, #queue-req: 0, 
[2025-07-28 01:09:21 DP0 TP0] Decode batch. #running-req: 1, #token: 5042, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.22, #queue-req: 0, 
[2025-07-28 01:09:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1480, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.15, #queue-req: 0, 
[2025-07-28 01:09:21 DP0 TP0] Decode batch. #running-req: 1, #token: 5082, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.16, #queue-req: 0, 
[2025-07-28 01:09:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1520, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.00, #queue-req: 0, 
[2025-07-28 01:09:21 DP0 TP0] Decode batch. #running-req: 1, #token: 5122, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.14, #queue-req: 0, 
[2025-07-28 01:09:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1560, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.02, #queue-req: 0, 
[2025-07-28 01:09:21 DP0 TP0] Decode batch. #running-req: 1, #token: 5162, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.59, #queue-req: 0, 
[2025-07-28 01:09:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1600, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.87, #queue-req: 0, 
[2025-07-28 01:09:21 DP0 TP0] Decode batch. #running-req: 1, #token: 5202, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.58, #queue-req: 0, 
[2025-07-28 01:09:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1640, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.90, #queue-req: 0, 
[2025-07-28 01:09:22 DP0 TP0] Decode batch. #running-req: 1, #token: 5242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.63, #queue-req: 0, 
[2025-07-28 01:09:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1680, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.95, #queue-req: 0, 
[2025-07-28 01:09:22 DP0 TP0] Decode batch. #running-req: 1, #token: 5282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.64, #queue-req: 0, 
[2025-07-28 01:09:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1720, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.92, #queue-req: 0, 
[2025-07-28 01:09:22 DP0 TP0] Decode batch. #running-req: 1, #token: 5322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.73, #queue-req: 0, 
[2025-07-28 01:09:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1760, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.97, #queue-req: 0, 
[2025-07-28 01:09:22 DP0 TP0] Decode batch. #running-req: 1, #token: 5362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.73, #queue-req: 0, 
[2025-07-28 01:09:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1800, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.97, #queue-req: 0, 
[2025-07-28 01:09:22 DP0 TP0] Decode batch. #running-req: 1, #token: 5402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.60, #queue-req: 0, 
[2025-07-28 01:09:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1840, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.75, #queue-req: 0, 
[2025-07-28 01:09:23 DP0 TP0] Decode batch. #running-req: 1, #token: 5442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.58, #queue-req: 0, 
[2025-07-28 01:09:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1880, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.40, #queue-req: 0, 
[2025-07-28 01:09:23 DP0 TP0] Decode batch. #running-req: 1, #token: 5482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.64, #queue-req: 0, 
[2025-07-28 01:09:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1920, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.87, #queue-req: 0, 
[2025-07-28 01:09:23 DP0 TP0] Decode batch. #running-req: 1, #token: 5522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.64, #queue-req: 0, 
[2025-07-28 01:09:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1960, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.59, #queue-req: 0, 
[2025-07-28 01:09:23 DP0 TP0] Decode batch. #running-req: 1, #token: 5562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.69, #queue-req: 0, 
[2025-07-28 01:09:23 DP1 TP0] Decode batch. #running-req: 1, #token: 2000, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.87, #queue-req: 0, 
[2025-07-28 01:09:23 DP0 TP0] Decode batch. #running-req: 1, #token: 5602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.54, #queue-req: 0, 
[2025-07-28 01:09:24 DP1 TP0] Decode batch. #running-req: 1, #token: 2040, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.02, #queue-req: 0, 
[2025-07-28 01:09:24 DP0 TP0] Decode batch. #running-req: 1, #token: 5642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.67, #queue-req: 0, 
[2025-07-28 01:09:24 DP1 TP0] Decode batch. #running-req: 1, #token: 2080, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.51, #queue-req: 0, 
[2025-07-28 01:09:24 DP0 TP0] Decode batch. #running-req: 1, #token: 5682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.56, #queue-req: 0, 
[2025-07-28 01:09:24 DP1 TP0] Decode batch. #running-req: 1, #token: 2120, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.50, #queue-req: 0, 
[2025-07-28 01:09:24 DP0 TP0] Decode batch. #running-req: 1, #token: 5722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.92, #queue-req: 0, 
[2025-07-28 01:09:24 DP1 TP0] Decode batch. #running-req: 1, #token: 2160, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.39, #queue-req: 0, 
[2025-07-28 01:09:24] INFO:     127.0.0.1:50396 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:09:24 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 114, #cached-token: 88, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:09:24 DP0 TP0] Decode batch. #running-req: 2, #token: 5883, token usage: 0.00, cuda graph: True, gen throughput (token/s): 180.98, #queue-req: 0, 
[2025-07-28 01:09:25 DP0 TP0] Decode batch. #running-req: 2, #token: 5963, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.94, #queue-req: 0, 
[2025-07-28 01:09:25 DP0 TP0] Decode batch. #running-req: 2, #token: 6043, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.76, #queue-req: 0, 
[2025-07-28 01:09:25 DP0 TP0] Decode batch. #running-req: 2, #token: 6123, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.73, #queue-req: 0, 
[2025-07-28 01:09:25 DP0 TP0] Decode batch. #running-req: 2, #token: 6203, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.42, #queue-req: 0, 
[2025-07-28 01:09:25 DP0 TP0] Decode batch. #running-req: 2, #token: 6283, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.25, #queue-req: 0, 
[2025-07-28 01:09:26 DP0 TP0] Decode batch. #running-req: 2, #token: 6363, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.27, #queue-req: 0, 
[2025-07-28 01:09:26 DP0 TP0] Decode batch. #running-req: 2, #token: 6443, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.86, #queue-req: 0, 
[2025-07-28 01:09:26 DP0 TP0] Decode batch. #running-req: 2, #token: 6523, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.93, #queue-req: 0, 
[2025-07-28 01:09:26 DP0 TP0] Decode batch. #running-req: 2, #token: 6603, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.76, #queue-req: 0, 
[2025-07-28 01:09:26 DP0 TP0] Decode batch. #running-req: 2, #token: 6683, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.53, #queue-req: 0, 
[2025-07-28 01:09:27 DP0 TP0] Decode batch. #running-req: 2, #token: 6763, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.24, #queue-req: 0, 
[2025-07-28 01:09:27 DP0 TP0] Decode batch. #running-req: 2, #token: 6843, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.27, #queue-req: 0, 
[2025-07-28 01:09:27 DP0 TP0] Decode batch. #running-req: 2, #token: 6923, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.17, #queue-req: 0, 
[2025-07-28 01:09:27 DP0 TP0] Decode batch. #running-req: 2, #token: 7003, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.17, #queue-req: 0, 
[2025-07-28 01:09:27 DP0 TP0] Decode batch. #running-req: 2, #token: 7083, token usage: 0.00, cuda graph: True, gen throughput (token/s): 379.99, #queue-req: 0, 
[2025-07-28 01:09:28 DP0 TP0] Decode batch. #running-req: 2, #token: 7163, token usage: 0.00, cuda graph: True, gen throughput (token/s): 379.83, #queue-req: 0, 
[2025-07-28 01:09:28 DP0 TP0] Decode batch. #running-req: 2, #token: 7243, token usage: 0.00, cuda graph: True, gen throughput (token/s): 379.95, #queue-req: 0, 
[2025-07-28 01:09:28 DP0 TP0] Decode batch. #running-req: 2, #token: 7323, token usage: 0.00, cuda graph: True, gen throughput (token/s): 379.96, #queue-req: 0, 
[2025-07-28 01:09:28 DP0 TP0] Decode batch. #running-req: 2, #token: 7403, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.05, #queue-req: 0, 
[2025-07-28 01:09:29 DP0 TP0] Decode batch. #running-req: 2, #token: 7483, token usage: 0.00, cuda graph: True, gen throughput (token/s): 379.87, #queue-req: 0, 
[2025-07-28 01:09:29 DP0 TP0] Decode batch. #running-req: 2, #token: 7563, token usage: 0.00, cuda graph: True, gen throughput (token/s): 379.85, #queue-req: 0, 
[2025-07-28 01:09:29 DP0 TP0] Decode batch. #running-req: 2, #token: 7643, token usage: 0.00, cuda graph: True, gen throughput (token/s): 379.91, #queue-req: 0, 
[2025-07-28 01:09:29 DP0 TP0] Decode batch. #running-req: 2, #token: 7723, token usage: 0.00, cuda graph: True, gen throughput (token/s): 379.89, #queue-req: 0, 
[2025-07-28 01:09:29 DP0 TP0] Decode batch. #running-req: 2, #token: 7803, token usage: 0.00, cuda graph: True, gen throughput (token/s): 379.89, #queue-req: 0, 
[2025-07-28 01:09:30 DP0 TP0] Decode batch. #running-req: 2, #token: 7883, token usage: 0.00, cuda graph: True, gen throughput (token/s): 379.75, #queue-req: 0, 
[2025-07-28 01:09:30 DP0 TP0] Decode batch. #running-req: 2, #token: 7963, token usage: 0.00, cuda graph: True, gen throughput (token/s): 379.69, #queue-req: 0, 
[2025-07-28 01:09:30 DP0 TP0] Decode batch. #running-req: 2, #token: 8043, token usage: 0.00, cuda graph: True, gen throughput (token/s): 379.65, #queue-req: 0, 
[2025-07-28 01:09:30 DP0 TP0] Decode batch. #running-req: 2, #token: 8123, token usage: 0.00, cuda graph: True, gen throughput (token/s): 379.58, #queue-req: 0, 
[2025-07-28 01:09:30 DP0 TP0] Decode batch. #running-req: 2, #token: 8203, token usage: 0.00, cuda graph: True, gen throughput (token/s): 379.44, #queue-req: 0, 
[2025-07-28 01:09:31] INFO:     127.0.0.1:47462 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:09:31 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 80, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:09:31 DP0 TP0] Decode batch. #running-req: 1, #token: 6962, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.31, #queue-req: 0, 
[2025-07-28 01:09:31 DP1 TP0] Decode batch. #running-req: 1, #token: 199, token usage: 0.00, cuda graph: True, gen throughput (token/s): 6.08, #queue-req: 0, 
[2025-07-28 01:09:31 DP0 TP0] Decode batch. #running-req: 1, #token: 7002, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.96, #queue-req: 0, 
[2025-07-28 01:09:31 DP1 TP0] Decode batch. #running-req: 1, #token: 239, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.78, #queue-req: 0, 
[2025-07-28 01:09:31 DP0 TP0] Decode batch. #running-req: 1, #token: 7042, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.94, #queue-req: 0, 
[2025-07-28 01:09:31 DP1 TP0] Decode batch. #running-req: 1, #token: 279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.90, #queue-req: 0, 
[2025-07-28 01:09:31 DP0 TP0] Decode batch. #running-req: 1, #token: 7082, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.03, #queue-req: 0, 
[2025-07-28 01:09:31 DP1 TP0] Decode batch. #running-req: 1, #token: 319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.69, #queue-req: 0, 
[2025-07-28 01:09:31 DP0 TP0] Decode batch. #running-req: 1, #token: 7122, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.04, #queue-req: 0, 
[2025-07-28 01:09:32 DP1 TP0] Decode batch. #running-req: 1, #token: 359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.90, #queue-req: 0, 
[2025-07-28 01:09:32 DP0 TP0] Decode batch. #running-req: 1, #token: 7162, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.06, #queue-req: 0, 
[2025-07-28 01:09:32 DP1 TP0] Decode batch. #running-req: 1, #token: 399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.07, #queue-req: 0, 
[2025-07-28 01:09:32 DP0 TP0] Decode batch. #running-req: 1, #token: 7202, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.68, #queue-req: 0, 
[2025-07-28 01:09:32 DP1 TP0] Decode batch. #running-req: 1, #token: 439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.13, #queue-req: 0, 
[2025-07-28 01:09:32 DP0 TP0] Decode batch. #running-req: 1, #token: 7242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.22, #queue-req: 0, 
[2025-07-28 01:09:32 DP1 TP0] Decode batch. #running-req: 1, #token: 479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.14, #queue-req: 0, 
[2025-07-28 01:09:32 DP0 TP0] Decode batch. #running-req: 1, #token: 7282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.67, #queue-req: 0, 
[2025-07-28 01:09:32 DP1 TP0] Decode batch. #running-req: 1, #token: 519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.00, #queue-req: 0, 
[2025-07-28 01:09:32 DP0 TP0] Decode batch. #running-req: 1, #token: 7322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.74, #queue-req: 0, 
[2025-07-28 01:09:33 DP1 TP0] Decode batch. #running-req: 1, #token: 559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.71, #queue-req: 0, 
[2025-07-28 01:09:33 DP0 TP0] Decode batch. #running-req: 1, #token: 7362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.75, #queue-req: 0, 
[2025-07-28 01:09:33 DP1 TP0] Decode batch. #running-req: 1, #token: 599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.53, #queue-req: 0, 
[2025-07-28 01:09:33 DP0 TP0] Decode batch. #running-req: 1, #token: 7402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.77, #queue-req: 0, 
[2025-07-28 01:09:33 DP1 TP0] Decode batch. #running-req: 1, #token: 639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.32, #queue-req: 0, 
[2025-07-28 01:09:33 DP0 TP0] Decode batch. #running-req: 1, #token: 7442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.57, #queue-req: 0, 
[2025-07-28 01:09:33 DP1 TP0] Decode batch. #running-req: 1, #token: 679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.92, #queue-req: 0, 
[2025-07-28 01:09:33 DP0 TP0] Decode batch. #running-req: 1, #token: 7482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.59, #queue-req: 0, 
[2025-07-28 01:09:33 DP1 TP0] Decode batch. #running-req: 1, #token: 719, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.84, #queue-req: 0, 
[2025-07-28 01:09:33 DP0 TP0] Decode batch. #running-req: 1, #token: 7522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.60, #queue-req: 0, 
[2025-07-28 01:09:34 DP1 TP0] Decode batch. #running-req: 1, #token: 759, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.80, #queue-req: 0, 
[2025-07-28 01:09:34 DP0 TP0] Decode batch. #running-req: 1, #token: 7562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.66, #queue-req: 0, 
[2025-07-28 01:09:34 DP1 TP0] Decode batch. #running-req: 1, #token: 799, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.69, #queue-req: 0, 
[2025-07-28 01:09:34 DP0 TP0] Decode batch. #running-req: 1, #token: 7602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.71, #queue-req: 0, 
[2025-07-28 01:09:34 DP1 TP0] Decode batch. #running-req: 1, #token: 839, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.47, #queue-req: 0, 
[2025-07-28 01:09:34 DP0 TP0] Decode batch. #running-req: 1, #token: 7642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.64, #queue-req: 0, 
[2025-07-28 01:09:34 DP1 TP0] Decode batch. #running-req: 1, #token: 879, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.47, #queue-req: 0, 
[2025-07-28 01:09:34 DP0 TP0] Decode batch. #running-req: 1, #token: 7682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.69, #queue-req: 0, 
[2025-07-28 01:09:34 DP1 TP0] Decode batch. #running-req: 1, #token: 919, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.61, #queue-req: 0, 
[2025-07-28 01:09:34 DP0 TP0] Decode batch. #running-req: 1, #token: 7722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.56, #queue-req: 0, 
[2025-07-28 01:09:35 DP1 TP0] Decode batch. #running-req: 1, #token: 959, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.58, #queue-req: 0, 
[2025-07-28 01:09:35 DP0 TP0] Decode batch. #running-req: 1, #token: 7762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.53, #queue-req: 0, 
[2025-07-28 01:09:35 DP1 TP0] Decode batch. #running-req: 1, #token: 999, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.61, #queue-req: 0, 
[2025-07-28 01:09:35 DP0 TP0] Decode batch. #running-req: 1, #token: 7802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.54, #queue-req: 0, 
[2025-07-28 01:09:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1039, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.40, #queue-req: 0, 
[2025-07-28 01:09:35 DP0 TP0] Decode batch. #running-req: 1, #token: 7842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.50, #queue-req: 0, 
[2025-07-28 01:09:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1079, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.04, #queue-req: 0, 
[2025-07-28 01:09:35 DP0 TP0] Decode batch. #running-req: 1, #token: 7882, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.52, #queue-req: 0, 
[2025-07-28 01:09:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1119, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.92, #queue-req: 0, 
[2025-07-28 01:09:36 DP0 TP0] Decode batch. #running-req: 1, #token: 7922, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.54, #queue-req: 0, 
[2025-07-28 01:09:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1159, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.91, #queue-req: 0, 
[2025-07-28 01:09:36 DP0 TP0] Decode batch. #running-req: 1, #token: 7962, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.50, #queue-req: 0, 
[2025-07-28 01:09:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1199, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.01, #queue-req: 0, 
[2025-07-28 01:09:36 DP0 TP0] Decode batch. #running-req: 1, #token: 8002, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.50, #queue-req: 0, 
[2025-07-28 01:09:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1239, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.93, #queue-req: 0, 
[2025-07-28 01:09:36 DP0 TP0] Decode batch. #running-req: 1, #token: 8042, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.50, #queue-req: 0, 
[2025-07-28 01:09:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.13, #queue-req: 0, 
[2025-07-28 01:09:36 DP0 TP0] Decode batch. #running-req: 1, #token: 8082, token usage: 0.00, cuda graph: True, gen throughput (token/s): 187.04, #queue-req: 0, 
[2025-07-28 01:09:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.73, #queue-req: 0, 
[2025-07-28 01:09:37 DP0 TP0] Decode batch. #running-req: 1, #token: 8122, token usage: 0.00, cuda graph: True, gen throughput (token/s): 195.79, #queue-req: 0, 
[2025-07-28 01:09:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.88, #queue-req: 0, 
[2025-07-28 01:09:37 DP0 TP0] Decode batch. #running-req: 1, #token: 8162, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.58, #queue-req: 0, 
[2025-07-28 01:09:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.74, #queue-req: 0, 
[2025-07-28 01:09:37 DP0 TP0] Decode batch. #running-req: 1, #token: 8202, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.44, #queue-req: 0, 
[2025-07-28 01:09:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.14, #queue-req: 0, 
[2025-07-28 01:09:37 DP0 TP0] Decode batch. #running-req: 1, #token: 8242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.07, #queue-req: 0, 
[2025-07-28 01:09:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.15, #queue-req: 0, 
[2025-07-28 01:09:37 DP0 TP0] Decode batch. #running-req: 1, #token: 8282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 195.88, #queue-req: 0, 
[2025-07-28 01:09:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.03, #queue-req: 0, 
[2025-07-28 01:09:38 DP0 TP0] Decode batch. #running-req: 1, #token: 8322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 195.20, #queue-req: 0, 
[2025-07-28 01:09:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.62, #queue-req: 0, 
[2025-07-28 01:09:38 DP0 TP0] Decode batch. #running-req: 1, #token: 8362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 194.98, #queue-req: 0, 
[2025-07-28 01:09:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.39, #queue-req: 0, 
[2025-07-28 01:09:38] INFO:     127.0.0.1:33172 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:09:38 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 136, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:09:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.63, #queue-req: 0, 
[2025-07-28 01:09:38 DP0 TP0] Decode batch. #running-req: 1, #token: 231, token usage: 0.00, cuda graph: True, gen throughput (token/s): 143.90, #queue-req: 0, 
[2025-07-28 01:09:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.96, #queue-req: 0, 
[2025-07-28 01:09:38 DP0 TP0] Decode batch. #running-req: 1, #token: 271, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.97, #queue-req: 0, 
[2025-07-28 01:09:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1719, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.11, #queue-req: 0, 
[2025-07-28 01:09:38 DP0 TP0] Decode batch. #running-req: 1, #token: 311, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.92, #queue-req: 0, 
[2025-07-28 01:09:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1759, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.11, #queue-req: 0, 
[2025-07-28 01:09:39 DP0 TP0] Decode batch. #running-req: 1, #token: 351, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.93, #queue-req: 0, 
[2025-07-28 01:09:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1799, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.04, #queue-req: 0, 
[2025-07-28 01:09:39 DP0 TP0] Decode batch. #running-req: 1, #token: 391, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.14, #queue-req: 0, 
[2025-07-28 01:09:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1839, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.73, #queue-req: 0, 
[2025-07-28 01:09:39 DP0 TP0] Decode batch. #running-req: 1, #token: 431, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.03, #queue-req: 0, 
[2025-07-28 01:09:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1879, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.86, #queue-req: 0, 
[2025-07-28 01:09:39 DP0 TP0] Decode batch. #running-req: 1, #token: 471, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.03, #queue-req: 0, 
[2025-07-28 01:09:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1919, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.96, #queue-req: 0, 
[2025-07-28 01:09:39 DP0 TP0] Decode batch. #running-req: 1, #token: 511, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.12, #queue-req: 0, 
[2025-07-28 01:09:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1959, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.01, #queue-req: 0, 
[2025-07-28 01:09:40 DP0 TP0] Decode batch. #running-req: 1, #token: 551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.53, #queue-req: 0, 
[2025-07-28 01:09:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1999, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.05, #queue-req: 0, 
[2025-07-28 01:09:40 DP0 TP0] Decode batch. #running-req: 1, #token: 591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.59, #queue-req: 0, 
[2025-07-28 01:09:40 DP1 TP0] Decode batch. #running-req: 1, #token: 2039, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.02, #queue-req: 0, 
[2025-07-28 01:09:40 DP0 TP0] Decode batch. #running-req: 1, #token: 631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.57, #queue-req: 0, 
[2025-07-28 01:09:40 DP1 TP0] Decode batch. #running-req: 1, #token: 2079, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.53, #queue-req: 0, 
[2025-07-28 01:09:40 DP0 TP0] Decode batch. #running-req: 1, #token: 671, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.41, #queue-req: 0, 
[2025-07-28 01:09:40 DP1 TP0] Decode batch. #running-req: 1, #token: 2119, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.46, #queue-req: 0, 
[2025-07-28 01:09:40 DP0 TP0] Decode batch. #running-req: 1, #token: 711, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.74, #queue-req: 0, 
[2025-07-28 01:09:41 DP1 TP0] Decode batch. #running-req: 1, #token: 2159, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.49, #queue-req: 0, 
[2025-07-28 01:09:41 DP0 TP0] Decode batch. #running-req: 1, #token: 751, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.60, #queue-req: 0, 
[2025-07-28 01:09:41 DP1 TP0] Decode batch. #running-req: 1, #token: 2199, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.61, #queue-req: 0, 
[2025-07-28 01:09:41 DP0 TP0] Decode batch. #running-req: 1, #token: 791, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.35, #queue-req: 0, 
[2025-07-28 01:09:41 DP1 TP0] Decode batch. #running-req: 1, #token: 2239, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.63, #queue-req: 0, 
[2025-07-28 01:09:41 DP0 TP0] Decode batch. #running-req: 1, #token: 831, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.24, #queue-req: 0, 
[2025-07-28 01:09:41 DP1 TP0] Decode batch. #running-req: 1, #token: 2279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.67, #queue-req: 0, 
[2025-07-28 01:09:41 DP0 TP0] Decode batch. #running-req: 1, #token: 871, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.13, #queue-req: 0, 
[2025-07-28 01:09:41 DP1 TP0] Decode batch. #running-req: 1, #token: 2319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.42, #queue-req: 0, 
[2025-07-28 01:09:41 DP0 TP0] Decode batch. #running-req: 1, #token: 911, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.55, #queue-req: 0, 
[2025-07-28 01:09:42 DP1 TP0] Decode batch. #running-req: 1, #token: 2359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.39, #queue-req: 0, 
[2025-07-28 01:09:42 DP0 TP0] Decode batch. #running-req: 1, #token: 951, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.57, #queue-req: 0, 
[2025-07-28 01:09:42 DP1 TP0] Decode batch. #running-req: 1, #token: 2399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.28, #queue-req: 0, 
[2025-07-28 01:09:42 DP0 TP0] Decode batch. #running-req: 1, #token: 991, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.39, #queue-req: 0, 
[2025-07-28 01:09:42 DP1 TP0] Decode batch. #running-req: 1, #token: 2439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.43, #queue-req: 0, 
[2025-07-28 01:09:42 DP0 TP0] Decode batch. #running-req: 1, #token: 1031, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.24, #queue-req: 0, 
[2025-07-28 01:09:42 DP1 TP0] Decode batch. #running-req: 1, #token: 2479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.60, #queue-req: 0, 
[2025-07-28 01:09:42 DP0 TP0] Decode batch. #running-req: 1, #token: 1071, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.58, #queue-req: 0, 
[2025-07-28 01:09:42 DP1 TP0] Decode batch. #running-req: 1, #token: 2519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.62, #queue-req: 0, 
[2025-07-28 01:09:42 DP0 TP0] Decode batch. #running-req: 1, #token: 1111, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.95, #queue-req: 0, 
[2025-07-28 01:09:43 DP1 TP0] Decode batch. #running-req: 1, #token: 2559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.44, #queue-req: 0, 
[2025-07-28 01:09:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1151, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.84, #queue-req: 0, 
[2025-07-28 01:09:43 DP1 TP0] Decode batch. #running-req: 1, #token: 2599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.26, #queue-req: 0, 
[2025-07-28 01:09:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1191, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.74, #queue-req: 0, 
[2025-07-28 01:09:43 DP1 TP0] Decode batch. #running-req: 1, #token: 2639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.31, #queue-req: 0, 
[2025-07-28 01:09:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1231, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.08, #queue-req: 0, 
[2025-07-28 01:09:43 DP1 TP0] Decode batch. #running-req: 1, #token: 2679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.30, #queue-req: 0, 
[2025-07-28 01:09:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1271, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.71, #queue-req: 0, 
[2025-07-28 01:09:43 DP1 TP0] Decode batch. #running-req: 1, #token: 2719, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.45, #queue-req: 0, 
[2025-07-28 01:09:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1311, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.93, #queue-req: 0, 
[2025-07-28 01:09:44] INFO:     127.0.0.1:47470 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:09:44 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 137, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:09:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1351, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.72, #queue-req: 0, 
[2025-07-28 01:09:44 DP1 TP0] Decode batch. #running-req: 1, #token: 233, token usage: 0.00, cuda graph: True, gen throughput (token/s): 159.96, #queue-req: 0, 
[2025-07-28 01:09:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1391, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.69, #queue-req: 0, 
[2025-07-28 01:09:44 DP1 TP0] Decode batch. #running-req: 1, #token: 273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.55, #queue-req: 0, 
[2025-07-28 01:09:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1431, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.57, #queue-req: 0, 
[2025-07-28 01:09:44 DP1 TP0] Decode batch. #running-req: 1, #token: 313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.00, #queue-req: 0, 
[2025-07-28 01:09:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1471, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.40, #queue-req: 0, 
[2025-07-28 01:09:44 DP1 TP0] Decode batch. #running-req: 1, #token: 353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.96, #queue-req: 0, 
[2025-07-28 01:09:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1511, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.96, #queue-req: 0, 
[2025-07-28 01:09:44 DP1 TP0] Decode batch. #running-req: 1, #token: 393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.04, #queue-req: 0, 
[2025-07-28 01:09:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.66, #queue-req: 0, 
[2025-07-28 01:09:45 DP1 TP0] Decode batch. #running-req: 1, #token: 433, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.17, #queue-req: 0, 
[2025-07-28 01:09:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.62, #queue-req: 0, 
[2025-07-28 01:09:45 DP1 TP0] Decode batch. #running-req: 1, #token: 473, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.13, #queue-req: 0, 
[2025-07-28 01:09:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.73, #queue-req: 0, 
[2025-07-28 01:09:45 DP1 TP0] Decode batch. #running-req: 1, #token: 513, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.99, #queue-req: 0, 
[2025-07-28 01:09:45 DP1 TP0] Decode batch. #running-req: 1, #token: 553, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.71, #queue-req: 0, 
[2025-07-28 01:09:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1671, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.47, #queue-req: 0, 
[2025-07-28 01:09:45 DP1 TP0] Decode batch. #running-req: 1, #token: 593, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.64, #queue-req: 0, 
[2025-07-28 01:09:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1711, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.04, #queue-req: 0, 
[2025-07-28 01:09:46 DP1 TP0] Decode batch. #running-req: 1, #token: 633, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.80, #queue-req: 0, 
[2025-07-28 01:09:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1751, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.91, #queue-req: 0, 
[2025-07-28 01:09:46] INFO:     127.0.0.1:55690 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:09:46 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 85, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:09:46 DP1 TP0] Decode batch. #running-req: 1, #token: 673, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.76, #queue-req: 0, 
[2025-07-28 01:09:46 DP0 TP0] Decode batch. #running-req: 1, #token: 191, token usage: 0.00, cuda graph: True, gen throughput (token/s): 163.39, #queue-req: 0, 
[2025-07-28 01:09:46 DP1 TP0] Decode batch. #running-req: 1, #token: 713, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.88, #queue-req: 0, 
[2025-07-28 01:09:46 DP0 TP0] Decode batch. #running-req: 1, #token: 231, token usage: 0.00, cuda graph: True, gen throughput (token/s): 204.67, #queue-req: 0, 
[2025-07-28 01:09:46 DP1 TP0] Decode batch. #running-req: 1, #token: 753, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.83, #queue-req: 0, 
[2025-07-28 01:09:46 DP0 TP0] Decode batch. #running-req: 1, #token: 271, token usage: 0.00, cuda graph: True, gen throughput (token/s): 202.21, #queue-req: 0, 
[2025-07-28 01:09:46 DP1 TP0] Decode batch. #running-req: 1, #token: 793, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.64, #queue-req: 0, 
[2025-07-28 01:09:46 DP0 TP0] Decode batch. #running-req: 1, #token: 311, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.41, #queue-req: 0, 
[2025-07-28 01:09:47 DP1 TP0] Decode batch. #running-req: 1, #token: 833, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.53, #queue-req: 0, 
[2025-07-28 01:09:47 DP0 TP0] Decode batch. #running-req: 1, #token: 351, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.15, #queue-req: 0, 
[2025-07-28 01:09:47 DP1 TP0] Decode batch. #running-req: 1, #token: 873, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.53, #queue-req: 0, 
[2025-07-28 01:09:47 DP0 TP0] Decode batch. #running-req: 1, #token: 391, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.21, #queue-req: 0, 
[2025-07-28 01:09:47 DP1 TP0] Decode batch. #running-req: 1, #token: 913, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.58, #queue-req: 0, 
[2025-07-28 01:09:47 DP0 TP0] Decode batch. #running-req: 1, #token: 431, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.16, #queue-req: 0, 
[2025-07-28 01:09:47 DP1 TP0] Decode batch. #running-req: 1, #token: 953, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.55, #queue-req: 0, 
[2025-07-28 01:09:47 DP0 TP0] Decode batch. #running-req: 1, #token: 471, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.03, #queue-req: 0, 
[2025-07-28 01:09:47 DP1 TP0] Decode batch. #running-req: 1, #token: 993, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.54, #queue-req: 0, 
[2025-07-28 01:09:47 DP0 TP0] Decode batch. #running-req: 1, #token: 511, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.82, #queue-req: 0, 
[2025-07-28 01:09:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1033, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.49, #queue-req: 0, 
[2025-07-28 01:09:48 DP0 TP0] Decode batch. #running-req: 1, #token: 551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.69, #queue-req: 0, 
[2025-07-28 01:09:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1073, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.99, #queue-req: 0, 
[2025-07-28 01:09:48 DP0 TP0] Decode batch. #running-req: 1, #token: 591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.69, #queue-req: 0, 
[2025-07-28 01:09:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1113, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.08, #queue-req: 0, 
[2025-07-28 01:09:48 DP0 TP0] Decode batch. #running-req: 1, #token: 631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.68, #queue-req: 0, 
[2025-07-28 01:09:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1153, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.89, #queue-req: 0, 
[2025-07-28 01:09:48 DP0 TP0] Decode batch. #running-req: 1, #token: 671, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.71, #queue-req: 0, 
[2025-07-28 01:09:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1193, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.19, #queue-req: 0, 
[2025-07-28 01:09:48 DP0 TP0] Decode batch. #running-req: 1, #token: 711, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.38, #queue-req: 0, 
[2025-07-28 01:09:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1233, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.04, #queue-req: 0, 
[2025-07-28 01:09:49 DP0 TP0] Decode batch. #running-req: 1, #token: 751, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.66, #queue-req: 0, 
[2025-07-28 01:09:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.15, #queue-req: 0, 
[2025-07-28 01:09:49 DP0 TP0] Decode batch. #running-req: 1, #token: 791, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.93, #queue-req: 0, 
[2025-07-28 01:09:49] INFO:     127.0.0.1:53750 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:09:49 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 157, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:09:49 DP0 TP0] Decode batch. #running-req: 1, #token: 831, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.53, #queue-req: 0, 
[2025-07-28 01:09:49 DP1 TP0] Decode batch. #running-req: 1, #token: 258, token usage: 0.00, cuda graph: True, gen throughput (token/s): 161.45, #queue-req: 0, 
[2025-07-28 01:09:49 DP0 TP0] Decode batch. #running-req: 1, #token: 871, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.65, #queue-req: 0, 
[2025-07-28 01:09:49 DP1 TP0] Decode batch. #running-req: 1, #token: 298, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.85, #queue-req: 0, 
[2025-07-28 01:09:49 DP0 TP0] Decode batch. #running-req: 1, #token: 911, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.42, #queue-req: 0, 
[2025-07-28 01:09:49 DP1 TP0] Decode batch. #running-req: 1, #token: 338, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.88, #queue-req: 0, 
[2025-07-28 01:09:50 DP0 TP0] Decode batch. #running-req: 1, #token: 951, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.03, #queue-req: 0, 
[2025-07-28 01:09:50 DP1 TP0] Decode batch. #running-req: 1, #token: 378, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.76, #queue-req: 0, 
[2025-07-28 01:09:50 DP0 TP0] Decode batch. #running-req: 1, #token: 991, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.39, #queue-req: 0, 
[2025-07-28 01:09:50 DP1 TP0] Decode batch. #running-req: 1, #token: 418, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.12, #queue-req: 0, 
[2025-07-28 01:09:50 DP0 TP0] Decode batch. #running-req: 1, #token: 1031, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.40, #queue-req: 0, 
[2025-07-28 01:09:50 DP1 TP0] Decode batch. #running-req: 1, #token: 458, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.91, #queue-req: 0, 
[2025-07-28 01:09:50 DP0 TP0] Decode batch. #running-req: 1, #token: 1071, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.95, #queue-req: 0, 
[2025-07-28 01:09:50 DP1 TP0] Decode batch. #running-req: 1, #token: 498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.96, #queue-req: 0, 
[2025-07-28 01:09:50] INFO:     127.0.0.1:53756 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:09:50 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 150, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:09:50 DP1 TP0] Decode batch. #running-req: 1, #token: 538, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.77, #queue-req: 0, 
[2025-07-28 01:09:51 DP0 TP0] Decode batch. #running-req: 1, #token: 250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 162.73, #queue-req: 0, 
[2025-07-28 01:09:51 DP1 TP0] Decode batch. #running-req: 1, #token: 578, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.66, #queue-req: 0, 
[2025-07-28 01:09:51 DP0 TP0] Decode batch. #running-req: 1, #token: 290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.22, #queue-req: 0, 
[2025-07-28 01:09:51 DP1 TP0] Decode batch. #running-req: 1, #token: 618, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.37, #queue-req: 0, 
[2025-07-28 01:09:51 DP0 TP0] Decode batch. #running-req: 1, #token: 330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.18, #queue-req: 0, 
[2025-07-28 01:09:51 DP1 TP0] Decode batch. #running-req: 1, #token: 658, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.63, #queue-req: 0, 
[2025-07-28 01:09:51 DP0 TP0] Decode batch. #running-req: 1, #token: 370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.01, #queue-req: 0, 
[2025-07-28 01:09:51 DP1 TP0] Decode batch. #running-req: 1, #token: 698, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.70, #queue-req: 0, 
[2025-07-28 01:09:51 DP0 TP0] Decode batch. #running-req: 1, #token: 410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.15, #queue-req: 0, 
[2025-07-28 01:09:51 DP1 TP0] Decode batch. #running-req: 1, #token: 738, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.65, #queue-req: 0, 
[2025-07-28 01:09:52 DP0 TP0] Decode batch. #running-req: 1, #token: 450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.05, #queue-req: 0, 
[2025-07-28 01:09:52 DP1 TP0] Decode batch. #running-req: 1, #token: 778, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.75, #queue-req: 0, 
[2025-07-28 01:09:52 DP0 TP0] Decode batch. #running-req: 1, #token: 490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.05, #queue-req: 0, 
[2025-07-28 01:09:52 DP1 TP0] Decode batch. #running-req: 1, #token: 818, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.40, #queue-req: 0, 
[2025-07-28 01:09:52 DP0 TP0] Decode batch. #running-req: 1, #token: 530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.94, #queue-req: 0, 
[2025-07-28 01:09:52 DP1 TP0] Decode batch. #running-req: 1, #token: 858, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.25, #queue-req: 0, 
[2025-07-28 01:09:52 DP0 TP0] Decode batch. #running-req: 1, #token: 570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.70, #queue-req: 0, 
[2025-07-28 01:09:52 DP1 TP0] Decode batch. #running-req: 1, #token: 898, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.42, #queue-req: 0, 
[2025-07-28 01:09:52 DP0 TP0] Decode batch. #running-req: 1, #token: 610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.69, #queue-req: 0, 
[2025-07-28 01:09:52 DP1 TP0] Decode batch. #running-req: 1, #token: 938, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.62, #queue-req: 0, 
[2025-07-28 01:09:53 DP0 TP0] Decode batch. #running-req: 1, #token: 650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.66, #queue-req: 0, 
[2025-07-28 01:09:53 DP1 TP0] Decode batch. #running-req: 1, #token: 978, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.70, #queue-req: 0, 
[2025-07-28 01:09:53 DP0 TP0] Decode batch. #running-req: 1, #token: 690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.66, #queue-req: 0, 
[2025-07-28 01:09:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1018, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.62, #queue-req: 0, 
[2025-07-28 01:09:53 DP0 TP0] Decode batch. #running-req: 1, #token: 730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.73, #queue-req: 0, 
[2025-07-28 01:09:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1058, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.04, #queue-req: 0, 
[2025-07-28 01:09:53 DP0 TP0] Decode batch. #running-req: 1, #token: 770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.65, #queue-req: 0, 
[2025-07-28 01:09:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1098, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.97, #queue-req: 0, 
[2025-07-28 01:09:53 DP0 TP0] Decode batch. #running-req: 1, #token: 810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.37, #queue-req: 0, 
[2025-07-28 01:09:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1138, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.89, #queue-req: 0, 
[2025-07-28 01:09:54 DP0 TP0] Decode batch. #running-req: 1, #token: 850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.42, #queue-req: 0, 
[2025-07-28 01:09:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1178, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.09, #queue-req: 0, 
[2025-07-28 01:09:54 DP0 TP0] Decode batch. #running-req: 1, #token: 890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.45, #queue-req: 0, 
[2025-07-28 01:09:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1218, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.11, #queue-req: 0, 
[2025-07-28 01:09:54 DP0 TP0] Decode batch. #running-req: 1, #token: 930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.46, #queue-req: 0, 
[2025-07-28 01:09:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1258, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.08, #queue-req: 0, 
[2025-07-28 01:09:54 DP0 TP0] Decode batch. #running-req: 1, #token: 970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.47, #queue-req: 0, 
[2025-07-28 01:09:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1298, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.00, #queue-req: 0, 
[2025-07-28 01:09:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.42, #queue-req: 0, 
[2025-07-28 01:09:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1338, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.90, #queue-req: 0, 
[2025-07-28 01:09:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.07, #queue-req: 0, 
[2025-07-28 01:09:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1378, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.92, #queue-req: 0, 
[2025-07-28 01:09:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.83, #queue-req: 0, 
[2025-07-28 01:09:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1418, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.90, #queue-req: 0, 
[2025-07-28 01:09:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.39, #queue-req: 0, 
[2025-07-28 01:09:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1458, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.05, #queue-req: 0, 
[2025-07-28 01:09:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.14, #queue-req: 0, 
[2025-07-28 01:09:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.04, #queue-req: 0, 
[2025-07-28 01:09:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.91, #queue-req: 0, 
[2025-07-28 01:09:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1538, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.94, #queue-req: 0, 
[2025-07-28 01:09:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.83, #queue-req: 0, 
[2025-07-28 01:09:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1578, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.94, #queue-req: 0, 
[2025-07-28 01:09:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.77, #queue-req: 0, 
[2025-07-28 01:09:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1618, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.84, #queue-req: 0, 
[2025-07-28 01:09:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.69, #queue-req: 0, 
[2025-07-28 01:09:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1658, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.85, #queue-req: 0, 
[2025-07-28 01:09:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.70, #queue-req: 0, 
[2025-07-28 01:09:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1698, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.94, #queue-req: 0, 
[2025-07-28 01:09:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.28, #queue-req: 0, 
[2025-07-28 01:09:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1738, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.90, #queue-req: 0, 
[2025-07-28 01:09:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.99, #queue-req: 0, 
[2025-07-28 01:09:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1778, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.82, #queue-req: 0, 
[2025-07-28 01:09:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.88, #queue-req: 0, 
[2025-07-28 01:09:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1818, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.77, #queue-req: 0, 
[2025-07-28 01:09:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.91, #queue-req: 0, 
[2025-07-28 01:09:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1858, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.78, #queue-req: 0, 
[2025-07-28 01:09:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.74, #queue-req: 0, 
[2025-07-28 01:09:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1898, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.79, #queue-req: 0, 
[2025-07-28 01:09:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.72, #queue-req: 0, 
[2025-07-28 01:09:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1938, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.81, #queue-req: 0, 
[2025-07-28 01:09:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.73, #queue-req: 0, 
[2025-07-28 01:09:58 DP1 TP0] Decode batch. #running-req: 1, #token: 1978, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.87, #queue-req: 0, 
[2025-07-28 01:09:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.83, #queue-req: 0, 
[2025-07-28 01:09:58 DP1 TP0] Decode batch. #running-req: 1, #token: 2018, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.96, #queue-req: 0, 
[2025-07-28 01:09:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.71, #queue-req: 0, 
[2025-07-28 01:09:58 DP1 TP0] Decode batch. #running-req: 1, #token: 2058, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.81, #queue-req: 0, 
[2025-07-28 01:09:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.84, #queue-req: 0, 
[2025-07-28 01:09:58 DP1 TP0] Decode batch. #running-req: 1, #token: 2098, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.07, #queue-req: 0, 
[2025-07-28 01:09:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.78, #queue-req: 0, 
[2025-07-28 01:09:58] INFO:     127.0.0.1:53774 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:09:58 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 111, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:09:59 DP1 TP0] Decode batch. #running-req: 2, #token: 2270, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.42, #queue-req: 0, 
[2025-07-28 01:09:59 DP1 TP0] Decode batch. #running-req: 2, #token: 2350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.27, #queue-req: 0, 
[2025-07-28 01:09:59 DP1 TP0] Decode batch. #running-req: 2, #token: 2430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.92, #queue-req: 0, 
[2025-07-28 01:09:59 DP1 TP0] Decode batch. #running-req: 2, #token: 2510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.06, #queue-req: 0, 
[2025-07-28 01:09:59 DP1 TP0] Decode batch. #running-req: 2, #token: 2590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.08, #queue-req: 0, 
[2025-07-28 01:10:00 DP1 TP0] Decode batch. #running-req: 2, #token: 2670, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.14, #queue-req: 0, 
[2025-07-28 01:10:00 DP1 TP0] Decode batch. #running-req: 2, #token: 2750, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.98, #queue-req: 0, 
[2025-07-28 01:10:00 DP1 TP0] Decode batch. #running-req: 2, #token: 2830, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.90, #queue-req: 0, 
[2025-07-28 01:10:00 DP1 TP0] Decode batch. #running-req: 2, #token: 2910, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.06, #queue-req: 0, 
[2025-07-28 01:10:00 DP1 TP0] Decode batch. #running-req: 2, #token: 2990, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.89, #queue-req: 0, 
[2025-07-28 01:10:01 DP1 TP0] Decode batch. #running-req: 2, #token: 3070, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.03, #queue-req: 0, 
[2025-07-28 01:10:01 DP1 TP0] Decode batch. #running-req: 2, #token: 3150, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.71, #queue-req: 0, 
[2025-07-28 01:10:01 DP1 TP0] Decode batch. #running-req: 2, #token: 3230, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.91, #queue-req: 0, 
[2025-07-28 01:10:01 DP1 TP0] Decode batch. #running-req: 2, #token: 3310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.77, #queue-req: 0, 
[2025-07-28 01:10:01 DP1 TP0] Decode batch. #running-req: 2, #token: 3390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.70, #queue-req: 0, 
[2025-07-28 01:10:02 DP1 TP0] Decode batch. #running-req: 2, #token: 3470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.55, #queue-req: 0, 
[2025-07-28 01:10:02 DP1 TP0] Decode batch. #running-req: 2, #token: 3550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.61, #queue-req: 0, 
[2025-07-28 01:10:02 DP1 TP0] Decode batch. #running-req: 2, #token: 3630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.61, #queue-req: 0, 
[2025-07-28 01:10:02 DP1 TP0] Decode batch. #running-req: 2, #token: 3710, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.44, #queue-req: 0, 
[2025-07-28 01:10:02 DP1 TP0] Decode batch. #running-req: 2, #token: 3790, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.44, #queue-req: 0, 
[2025-07-28 01:10:03 DP1 TP0] Decode batch. #running-req: 2, #token: 3870, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.47, #queue-req: 0, 
[2025-07-28 01:10:03 DP1 TP0] Decode batch. #running-req: 2, #token: 3950, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.50, #queue-req: 0, 
[2025-07-28 01:10:03 DP1 TP0] Decode batch. #running-req: 2, #token: 4030, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.31, #queue-req: 0, 
[2025-07-28 01:10:03 DP1 TP0] Decode batch. #running-req: 2, #token: 4110, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.20, #queue-req: 0, 
[2025-07-28 01:10:03 DP1 TP0] Decode batch. #running-req: 2, #token: 4190, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.44, #queue-req: 0, 
[2025-07-28 01:10:04 DP1 TP0] Decode batch. #running-req: 2, #token: 4270, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.01, #queue-req: 0, 
[2025-07-28 01:10:04 DP1 TP0] Decode batch. #running-req: 2, #token: 4350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.83, #queue-req: 0, 
[2025-07-28 01:10:04 DP1 TP0] Decode batch. #running-req: 2, #token: 4430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.79, #queue-req: 0, 
[2025-07-28 01:10:04 DP1 TP0] Decode batch. #running-req: 2, #token: 4510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.61, #queue-req: 0, 
[2025-07-28 01:10:05 DP1 TP0] Decode batch. #running-req: 2, #token: 4590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.69, #queue-req: 0, 
[2025-07-28 01:10:05 DP1 TP0] Decode batch. #running-req: 2, #token: 4670, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.73, #queue-req: 0, 
[2025-07-28 01:10:05 DP1 TP0] Decode batch. #running-req: 2, #token: 4750, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.25, #queue-req: 0, 
[2025-07-28 01:10:05 DP1 TP0] Decode batch. #running-req: 2, #token: 4830, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.64, #queue-req: 0, 
[2025-07-28 01:10:05 DP1 TP0] Decode batch. #running-req: 2, #token: 4910, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.68, #queue-req: 0, 
[2025-07-28 01:10:06 DP1 TP0] Decode batch. #running-req: 2, #token: 4990, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.67, #queue-req: 0, 
[2025-07-28 01:10:06 DP1 TP0] Decode batch. #running-req: 2, #token: 5070, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.52, #queue-req: 0, 
[2025-07-28 01:10:06 DP1 TP0] Decode batch. #running-req: 2, #token: 5150, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.58, #queue-req: 0, 
[2025-07-28 01:10:06 DP1 TP0] Decode batch. #running-req: 2, #token: 5230, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.15, #queue-req: 0, 
[2025-07-28 01:10:06 DP1 TP0] Decode batch. #running-req: 2, #token: 5310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.47, #queue-req: 0, 
[2025-07-28 01:10:07 DP1 TP0] Decode batch. #running-req: 2, #token: 5390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.23, #queue-req: 0, 
[2025-07-28 01:10:07 DP1 TP0] Decode batch. #running-req: 2, #token: 5470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 382.94, #queue-req: 0, 
[2025-07-28 01:10:07 DP1 TP0] Decode batch. #running-req: 2, #token: 5550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.12, #queue-req: 0, 
[2025-07-28 01:10:07 DP1 TP0] Decode batch. #running-req: 2, #token: 5630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.34, #queue-req: 0, 
[2025-07-28 01:10:07 DP1 TP0] Decode batch. #running-req: 2, #token: 5710, token usage: 0.00, cuda graph: True, gen throughput (token/s): 382.94, #queue-req: 0, 
[2025-07-28 01:10:08 DP1 TP0] Decode batch. #running-req: 2, #token: 5790, token usage: 0.00, cuda graph: True, gen throughput (token/s): 382.80, #queue-req: 0, 
[2025-07-28 01:10:08 DP1 TP0] Decode batch. #running-req: 2, #token: 5870, token usage: 0.00, cuda graph: True, gen throughput (token/s): 382.83, #queue-req: 0, 
[2025-07-28 01:10:08 DP1 TP0] Decode batch. #running-req: 2, #token: 5950, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.00, #queue-req: 0, 
[2025-07-28 01:10:08 DP1 TP0] Decode batch. #running-req: 2, #token: 6030, token usage: 0.00, cuda graph: True, gen throughput (token/s): 382.06, #queue-req: 0, 
[2025-07-28 01:10:09 DP1 TP0] Decode batch. #running-req: 2, #token: 6110, token usage: 0.00, cuda graph: True, gen throughput (token/s): 382.65, #queue-req: 0, 
[2025-07-28 01:10:09 DP1 TP0] Decode batch. #running-req: 2, #token: 6190, token usage: 0.00, cuda graph: True, gen throughput (token/s): 382.33, #queue-req: 0, 
[2025-07-28 01:10:09 DP1 TP0] Decode batch. #running-req: 2, #token: 6270, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.98, #queue-req: 0, 
[2025-07-28 01:10:09 DP1 TP0] Decode batch. #running-req: 2, #token: 6350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.50, #queue-req: 0, 
[2025-07-28 01:10:09 DP1 TP0] Decode batch. #running-req: 2, #token: 6430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.97, #queue-req: 0, 
[2025-07-28 01:10:10 DP1 TP0] Decode batch. #running-req: 2, #token: 6510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.80, #queue-req: 0, 
[2025-07-28 01:10:10 DP1 TP0] Decode batch. #running-req: 2, #token: 6590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.67, #queue-req: 0, 
[2025-07-28 01:10:10 DP1 TP0] Decode batch. #running-req: 2, #token: 6670, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.50, #queue-req: 0, 
[2025-07-28 01:10:10 DP1 TP0] Decode batch. #running-req: 2, #token: 6750, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.14, #queue-req: 0, 
[2025-07-28 01:10:10 DP1 TP0] Decode batch. #running-req: 2, #token: 6830, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.65, #queue-req: 0, 
[2025-07-28 01:10:11 DP1 TP0] Decode batch. #running-req: 2, #token: 6910, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.36, #queue-req: 0, 
[2025-07-28 01:10:11 DP1 TP0] Decode batch. #running-req: 2, #token: 6990, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.06, #queue-req: 0, 
[2025-07-28 01:10:11 DP1 TP0] Decode batch. #running-req: 2, #token: 7070, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.96, #queue-req: 0, 
[2025-07-28 01:10:11 DP1 TP0] Decode batch. #running-req: 2, #token: 7150, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.26, #queue-req: 0, 
[2025-07-28 01:10:11 DP1 TP0] Decode batch. #running-req: 2, #token: 7230, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.58, #queue-req: 0, 
[2025-07-28 01:10:12 DP1 TP0] Decode batch. #running-req: 2, #token: 7310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.51, #queue-req: 0, 
[2025-07-28 01:10:12 DP1 TP0] Decode batch. #running-req: 2, #token: 7390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.47, #queue-req: 0, 
[2025-07-28 01:10:12 DP1 TP0] Decode batch. #running-req: 2, #token: 7470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.44, #queue-req: 0, 
[2025-07-28 01:10:12 DP1 TP0] Decode batch. #running-req: 2, #token: 7550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.39, #queue-req: 0, 
[2025-07-28 01:10:12 DP1 TP0] Decode batch. #running-req: 2, #token: 7630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.41, #queue-req: 0, 
[2025-07-28 01:10:13 DP1 TP0] Decode batch. #running-req: 2, #token: 7710, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.25, #queue-req: 0, 
[2025-07-28 01:10:13 DP1 TP0] Decode batch. #running-req: 2, #token: 7790, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.25, #queue-req: 0, 
[2025-07-28 01:10:13 DP1 TP0] Decode batch. #running-req: 2, #token: 7870, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.94, #queue-req: 0, 
[2025-07-28 01:10:13 DP1 TP0] Decode batch. #running-req: 2, #token: 7950, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.24, #queue-req: 0, 
[2025-07-28 01:10:14 DP1 TP0] Decode batch. #running-req: 2, #token: 8030, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.14, #queue-req: 0, 
[2025-07-28 01:10:14 DP1 TP0] Decode batch. #running-req: 2, #token: 8110, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.96, #queue-req: 0, 
[2025-07-28 01:10:14 DP1 TP0] Decode batch. #running-req: 2, #token: 8190, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.20, #queue-req: 0, 
[2025-07-28 01:10:14 DP1 TP0] Decode batch. #running-req: 2, #token: 8270, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.87, #queue-req: 0, 
[2025-07-28 01:10:14 DP1 TP0] Decode batch. #running-req: 2, #token: 8350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.42, #queue-req: 0, 
[2025-07-28 01:10:14] INFO:     127.0.0.1:40728 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:10:15 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 95, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:10:15 DP1 TP0] Decode batch. #running-req: 1, #token: 5218, token usage: 0.00, cuda graph: True, gen throughput (token/s): 281.53, #queue-req: 0, 
[2025-07-28 01:10:15 DP0 TP0] Decode batch. #running-req: 1, #token: 213, token usage: 0.00, cuda graph: True, gen throughput (token/s): 2.43, #queue-req: 0, 
[2025-07-28 01:10:15 DP1 TP0] Decode batch. #running-req: 1, #token: 5258, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.77, #queue-req: 0, 
[2025-07-28 01:10:15 DP0 TP0] Decode batch. #running-req: 1, #token: 253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.15, #queue-req: 0, 
[2025-07-28 01:10:15 DP1 TP0] Decode batch. #running-req: 1, #token: 5298, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.87, #queue-req: 0, 
[2025-07-28 01:10:15 DP0 TP0] Decode batch. #running-req: 1, #token: 293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.07, #queue-req: 0, 
[2025-07-28 01:10:15 DP1 TP0] Decode batch. #running-req: 1, #token: 5338, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.94, #queue-req: 0, 
[2025-07-28 01:10:15 DP0 TP0] Decode batch. #running-req: 1, #token: 333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.54, #queue-req: 0, 
[2025-07-28 01:10:15 DP1 TP0] Decode batch. #running-req: 1, #token: 5378, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.93, #queue-req: 0, 
[2025-07-28 01:10:16 DP0 TP0] Decode batch. #running-req: 1, #token: 373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.47, #queue-req: 0, 
[2025-07-28 01:10:16 DP1 TP0] Decode batch. #running-req: 1, #token: 5418, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.73, #queue-req: 0, 
[2025-07-28 01:10:16 DP0 TP0] Decode batch. #running-req: 1, #token: 413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.93, #queue-req: 0, 
[2025-07-28 01:10:16 DP1 TP0] Decode batch. #running-req: 1, #token: 5458, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.68, #queue-req: 0, 
[2025-07-28 01:10:16 DP0 TP0] Decode batch. #running-req: 1, #token: 453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.88, #queue-req: 0, 
[2025-07-28 01:10:16 DP1 TP0] Decode batch. #running-req: 1, #token: 5498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.75, #queue-req: 0, 
[2025-07-28 01:10:16 DP0 TP0] Decode batch. #running-req: 1, #token: 493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.69, #queue-req: 0, 
[2025-07-28 01:10:16 DP1 TP0] Decode batch. #running-req: 1, #token: 5538, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.82, #queue-req: 0, 
[2025-07-28 01:10:16 DP0 TP0] Decode batch. #running-req: 1, #token: 533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.65, #queue-req: 0, 
[2025-07-28 01:10:16 DP1 TP0] Decode batch. #running-req: 1, #token: 5578, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.71, #queue-req: 0, 
[2025-07-28 01:10:17 DP0 TP0] Decode batch. #running-req: 1, #token: 573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.51, #queue-req: 0, 
[2025-07-28 01:10:17 DP1 TP0] Decode batch. #running-req: 1, #token: 5618, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.44, #queue-req: 0, 
[2025-07-28 01:10:17 DP0 TP0] Decode batch. #running-req: 1, #token: 613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.51, #queue-req: 0, 
[2025-07-28 01:10:17 DP1 TP0] Decode batch. #running-req: 1, #token: 5658, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.73, #queue-req: 0, 
[2025-07-28 01:10:17 DP0 TP0] Decode batch. #running-req: 1, #token: 653, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.60, #queue-req: 0, 
[2025-07-28 01:10:17 DP1 TP0] Decode batch. #running-req: 1, #token: 5698, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.69, #queue-req: 0, 
[2025-07-28 01:10:17 DP0 TP0] Decode batch. #running-req: 1, #token: 693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.52, #queue-req: 0, 
[2025-07-28 01:10:17 DP1 TP0] Decode batch. #running-req: 1, #token: 5738, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.70, #queue-req: 0, 
[2025-07-28 01:10:17 DP0 TP0] Decode batch. #running-req: 1, #token: 733, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.76, #queue-req: 0, 
[2025-07-28 01:10:17 DP1 TP0] Decode batch. #running-req: 1, #token: 5778, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.62, #queue-req: 0, 
[2025-07-28 01:10:18 DP0 TP0] Decode batch. #running-req: 1, #token: 773, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.52, #queue-req: 0, 
[2025-07-28 01:10:18 DP1 TP0] Decode batch. #running-req: 1, #token: 5818, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.71, #queue-req: 0, 
[2025-07-28 01:10:18 DP0 TP0] Decode batch. #running-req: 1, #token: 813, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.37, #queue-req: 0, 
[2025-07-28 01:10:18 DP1 TP0] Decode batch. #running-req: 1, #token: 5858, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.68, #queue-req: 0, 
[2025-07-28 01:10:18 DP0 TP0] Decode batch. #running-req: 1, #token: 853, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.33, #queue-req: 0, 
[2025-07-28 01:10:18 DP1 TP0] Decode batch. #running-req: 1, #token: 5898, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.62, #queue-req: 0, 
[2025-07-28 01:10:18 DP0 TP0] Decode batch. #running-req: 1, #token: 893, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.36, #queue-req: 0, 
[2025-07-28 01:10:18 DP1 TP0] Decode batch. #running-req: 1, #token: 5938, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.62, #queue-req: 0, 
[2025-07-28 01:10:18 DP0 TP0] Decode batch. #running-req: 1, #token: 933, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.41, #queue-req: 0, 
[2025-07-28 01:10:18 DP1 TP0] Decode batch. #running-req: 1, #token: 5978, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.54, #queue-req: 0, 
[2025-07-28 01:10:19 DP0 TP0] Decode batch. #running-req: 1, #token: 973, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.52, #queue-req: 0, 
[2025-07-28 01:10:19 DP1 TP0] Decode batch. #running-req: 1, #token: 6018, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.66, #queue-req: 0, 
[2025-07-28 01:10:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1013, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.51, #queue-req: 0, 
[2025-07-28 01:10:19 DP1 TP0] Decode batch. #running-req: 1, #token: 6058, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.70, #queue-req: 0, 
[2025-07-28 01:10:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1053, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.09, #queue-req: 0, 
[2025-07-28 01:10:19 DP1 TP0] Decode batch. #running-req: 1, #token: 6098, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.70, #queue-req: 0, 
[2025-07-28 01:10:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1093, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.82, #queue-req: 0, 
[2025-07-28 01:10:19 DP1 TP0] Decode batch. #running-req: 1, #token: 6138, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.78, #queue-req: 0, 
[2025-07-28 01:10:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1133, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.58, #queue-req: 0, 
[2025-07-28 01:10:19 DP1 TP0] Decode batch. #running-req: 1, #token: 6178, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.24, #queue-req: 0, 
[2025-07-28 01:10:20] INFO:     127.0.0.1:53768 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:10:20 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 89, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:10:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1173, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.21, #queue-req: 0, 
[2025-07-28 01:10:20 DP1 TP0] Decode batch. #running-req: 1, #token: 194, token usage: 0.00, cuda graph: True, gen throughput (token/s): 159.80, #queue-req: 0, 
[2025-07-28 01:10:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1213, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.99, #queue-req: 0, 
[2025-07-28 01:10:20 DP1 TP0] Decode batch. #running-req: 1, #token: 234, token usage: 0.00, cuda graph: True, gen throughput (token/s): 204.21, #queue-req: 0, 
[2025-07-28 01:10:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.04, #queue-req: 0, 
[2025-07-28 01:10:20 DP1 TP0] Decode batch. #running-req: 1, #token: 274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.47, #queue-req: 0, 
[2025-07-28 01:10:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.88, #queue-req: 0, 
[2025-07-28 01:10:20 DP1 TP0] Decode batch. #running-req: 1, #token: 314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.09, #queue-req: 0, 
[2025-07-28 01:10:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.89, #queue-req: 0, 
[2025-07-28 01:10:20 DP1 TP0] Decode batch. #running-req: 1, #token: 354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.79, #queue-req: 0, 
[2025-07-28 01:10:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.66, #queue-req: 0, 
[2025-07-28 01:10:21 DP1 TP0] Decode batch. #running-req: 1, #token: 394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.94, #queue-req: 0, 
[2025-07-28 01:10:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.74, #queue-req: 0, 
[2025-07-28 01:10:21 DP1 TP0] Decode batch. #running-req: 1, #token: 434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.01, #queue-req: 0, 
[2025-07-28 01:10:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.88, #queue-req: 0, 
[2025-07-28 01:10:21 DP1 TP0] Decode batch. #running-req: 1, #token: 474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.88, #queue-req: 0, 
[2025-07-28 01:10:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.83, #queue-req: 0, 
[2025-07-28 01:10:21 DP1 TP0] Decode batch. #running-req: 1, #token: 514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.20, #queue-req: 0, 
[2025-07-28 01:10:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.87, #queue-req: 0, 
[2025-07-28 01:10:21 DP1 TP0] Decode batch. #running-req: 1, #token: 554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.63, #queue-req: 0, 
[2025-07-28 01:10:22 DP0 TP0] Decode batch. #running-req: 1, #token: 1573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.75, #queue-req: 0, 
[2025-07-28 01:10:22 DP1 TP0] Decode batch. #running-req: 1, #token: 594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.62, #queue-req: 0, 
[2025-07-28 01:10:22 DP0 TP0] Decode batch. #running-req: 1, #token: 1613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.68, #queue-req: 0, 
[2025-07-28 01:10:22 DP1 TP0] Decode batch. #running-req: 1, #token: 634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.63, #queue-req: 0, 
[2025-07-28 01:10:22 DP0 TP0] Decode batch. #running-req: 1, #token: 1653, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.75, #queue-req: 0, 
[2025-07-28 01:10:22 DP1 TP0] Decode batch. #running-req: 1, #token: 674, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.59, #queue-req: 0, 
[2025-07-28 01:10:22 DP0 TP0] Decode batch. #running-req: 1, #token: 1693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.87, #queue-req: 0, 
[2025-07-28 01:10:22 DP1 TP0] Decode batch. #running-req: 1, #token: 714, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.97, #queue-req: 0, 
[2025-07-28 01:10:22 DP0 TP0] Decode batch. #running-req: 1, #token: 1733, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.83, #queue-req: 0, 
[2025-07-28 01:10:22 DP1 TP0] Decode batch. #running-req: 1, #token: 754, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.74, #queue-req: 0, 
[2025-07-28 01:10:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1773, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.73, #queue-req: 0, 
[2025-07-28 01:10:23 DP1 TP0] Decode batch. #running-req: 1, #token: 794, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.54, #queue-req: 0, 
[2025-07-28 01:10:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1813, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.62, #queue-req: 0, 
[2025-07-28 01:10:23 DP1 TP0] Decode batch. #running-req: 1, #token: 834, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.53, #queue-req: 0, 
[2025-07-28 01:10:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1853, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.54, #queue-req: 0, 
[2025-07-28 01:10:23 DP1 TP0] Decode batch. #running-req: 1, #token: 874, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.25, #queue-req: 0, 
[2025-07-28 01:10:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1893, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.53, #queue-req: 0, 
[2025-07-28 01:10:23 DP1 TP0] Decode batch. #running-req: 1, #token: 914, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.54, #queue-req: 0, 
[2025-07-28 01:10:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1933, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.50, #queue-req: 0, 
[2025-07-28 01:10:23 DP1 TP0] Decode batch. #running-req: 1, #token: 954, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.60, #queue-req: 0, 
[2025-07-28 01:10:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1973, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.62, #queue-req: 0, 
[2025-07-28 01:10:24 DP1 TP0] Decode batch. #running-req: 1, #token: 994, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.72, #queue-req: 0, 
[2025-07-28 01:10:24 DP0 TP0] Decode batch. #running-req: 1, #token: 2013, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.68, #queue-req: 0, 
[2025-07-28 01:10:24 DP1 TP0] Decode batch. #running-req: 1, #token: 1034, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.45, #queue-req: 0, 
[2025-07-28 01:10:24 DP0 TP0] Decode batch. #running-req: 1, #token: 2053, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.53, #queue-req: 0, 
[2025-07-28 01:10:24 DP1 TP0] Decode batch. #running-req: 1, #token: 1074, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.66, #queue-req: 0, 
[2025-07-28 01:10:24 DP0 TP0] Decode batch. #running-req: 1, #token: 2093, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.13, #queue-req: 0, 
[2025-07-28 01:10:24 DP1 TP0] Decode batch. #running-req: 1, #token: 1114, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.06, #queue-req: 0, 
[2025-07-28 01:10:24 DP0 TP0] Decode batch. #running-req: 1, #token: 2133, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.16, #queue-req: 0, 
[2025-07-28 01:10:24 DP1 TP0] Decode batch. #running-req: 1, #token: 1154, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.04, #queue-req: 0, 
[2025-07-28 01:10:25 DP0 TP0] Decode batch. #running-req: 1, #token: 2173, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.25, #queue-req: 0, 
[2025-07-28 01:10:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1194, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.03, #queue-req: 0, 
[2025-07-28 01:10:25 DP0 TP0] Decode batch. #running-req: 1, #token: 2213, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.37, #queue-req: 0, 
[2025-07-28 01:10:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1234, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.12, #queue-req: 0, 
[2025-07-28 01:10:25 DP0 TP0] Decode batch. #running-req: 1, #token: 2253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.21, #queue-req: 0, 
[2025-07-28 01:10:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.65, #queue-req: 0, 
[2025-07-28 01:10:25 DP0 TP0] Decode batch. #running-req: 1, #token: 2293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.25, #queue-req: 0, 
[2025-07-28 01:10:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.18, #queue-req: 0, 
[2025-07-28 01:10:25 DP0 TP0] Decode batch. #running-req: 1, #token: 2333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.15, #queue-req: 0, 
[2025-07-28 01:10:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.79, #queue-req: 0, 
[2025-07-28 01:10:26 DP0 TP0] Decode batch. #running-req: 1, #token: 2373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.08, #queue-req: 0, 
[2025-07-28 01:10:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.83, #queue-req: 0, 
[2025-07-28 01:10:26 DP0 TP0] Decode batch. #running-req: 1, #token: 2413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.99, #queue-req: 0, 
[2025-07-28 01:10:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.86, #queue-req: 0, 
[2025-07-28 01:10:26 DP0 TP0] Decode batch. #running-req: 1, #token: 2453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.17, #queue-req: 0, 
[2025-07-28 01:10:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.87, #queue-req: 0, 
[2025-07-28 01:10:26 DP0 TP0] Decode batch. #running-req: 1, #token: 2493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.25, #queue-req: 0, 
[2025-07-28 01:10:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.26, #queue-req: 0, 
[2025-07-28 01:10:26 DP0 TP0] Decode batch. #running-req: 1, #token: 2533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.13, #queue-req: 0, 
[2025-07-28 01:10:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.87, #queue-req: 0, 
[2025-07-28 01:10:27 DP0 TP0] Decode batch. #running-req: 1, #token: 2573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.92, #queue-req: 0, 
[2025-07-28 01:10:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.74, #queue-req: 0, 
[2025-07-28 01:10:27 DP0 TP0] Decode batch. #running-req: 1, #token: 2613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.08, #queue-req: 0, 
[2025-07-28 01:10:27] INFO:     127.0.0.1:54238 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:10:27 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 115, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:10:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.85, #queue-req: 0, 
[2025-07-28 01:10:27 DP0 TP0] Decode batch. #running-req: 1, #token: 228, token usage: 0.00, cuda graph: True, gen throughput (token/s): 162.36, #queue-req: 0, 
[2025-07-28 01:10:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1674, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.78, #queue-req: 0, 
[2025-07-28 01:10:27 DP0 TP0] Decode batch. #running-req: 1, #token: 268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 202.01, #queue-req: 0, 
[2025-07-28 01:10:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1714, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.17, #queue-req: 0, 
[2025-07-28 01:10:27 DP0 TP0] Decode batch. #running-req: 1, #token: 308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.15, #queue-req: 0, 
[2025-07-28 01:10:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1754, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.99, #queue-req: 0, 
[2025-07-28 01:10:28 DP0 TP0] Decode batch. #running-req: 1, #token: 348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.25, #queue-req: 0, 
[2025-07-28 01:10:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1794, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.98, #queue-req: 0, 
[2025-07-28 01:10:28 DP0 TP0] Decode batch. #running-req: 1, #token: 388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.06, #queue-req: 0, 
[2025-07-28 01:10:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1834, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.47, #queue-req: 0, 
[2025-07-28 01:10:28 DP0 TP0] Decode batch. #running-req: 1, #token: 428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.13, #queue-req: 0, 
[2025-07-28 01:10:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1874, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.65, #queue-req: 0, 
[2025-07-28 01:10:28] INFO:     127.0.0.1:54244 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:10:28 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 164, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:10:28 DP0 TP0] Decode batch. #running-req: 1, #token: 468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.91, #queue-req: 0, 
[2025-07-28 01:10:28 DP1 TP0] Decode batch. #running-req: 1, #token: 265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 162.45, #queue-req: 0, 
[2025-07-28 01:10:28 DP0 TP0] Decode batch. #running-req: 1, #token: 508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.71, #queue-req: 0, 
[2025-07-28 01:10:29 DP1 TP0] Decode batch. #running-req: 1, #token: 305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.90, #queue-req: 0, 
[2025-07-28 01:10:29 DP0 TP0] Decode batch. #running-req: 1, #token: 548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.74, #queue-req: 0, 
[2025-07-28 01:10:29 DP1 TP0] Decode batch. #running-req: 1, #token: 345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.57, #queue-req: 0, 
[2025-07-28 01:10:29 DP0 TP0] Decode batch. #running-req: 1, #token: 588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.75, #queue-req: 0, 
[2025-07-28 01:10:29 DP1 TP0] Decode batch. #running-req: 1, #token: 385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.94, #queue-req: 0, 
[2025-07-28 01:10:29 DP0 TP0] Decode batch. #running-req: 1, #token: 628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.67, #queue-req: 0, 
[2025-07-28 01:10:29 DP1 TP0] Decode batch. #running-req: 1, #token: 425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.05, #queue-req: 0, 
[2025-07-28 01:10:29 DP0 TP0] Decode batch. #running-req: 1, #token: 668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.64, #queue-req: 0, 
[2025-07-28 01:10:29 DP1 TP0] Decode batch. #running-req: 1, #token: 465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.09, #queue-req: 0, 
[2025-07-28 01:10:29 DP0 TP0] Decode batch. #running-req: 1, #token: 708, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.32, #queue-req: 0, 
[2025-07-28 01:10:30 DP1 TP0] Decode batch. #running-req: 1, #token: 505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.04, #queue-req: 0, 
[2025-07-28 01:10:30 DP0 TP0] Decode batch. #running-req: 1, #token: 748, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.60, #queue-req: 0, 
[2025-07-28 01:10:30 DP1 TP0] Decode batch. #running-req: 1, #token: 545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.69, #queue-req: 0, 
[2025-07-28 01:10:30 DP0 TP0] Decode batch. #running-req: 1, #token: 788, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.34, #queue-req: 0, 
[2025-07-28 01:10:30 DP1 TP0] Decode batch. #running-req: 1, #token: 585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.51, #queue-req: 0, 
[2025-07-28 01:10:30 DP0 TP0] Decode batch. #running-req: 1, #token: 828, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.10, #queue-req: 0, 
[2025-07-28 01:10:30 DP1 TP0] Decode batch. #running-req: 1, #token: 625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.68, #queue-req: 0, 
[2025-07-28 01:10:30 DP0 TP0] Decode batch. #running-req: 1, #token: 868, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.28, #queue-req: 0, 
[2025-07-28 01:10:30 DP1 TP0] Decode batch. #running-req: 1, #token: 665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.63, #queue-req: 0, 
[2025-07-28 01:10:30 DP0 TP0] Decode batch. #running-req: 1, #token: 908, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.49, #queue-req: 0, 
[2025-07-28 01:10:31 DP1 TP0] Decode batch. #running-req: 1, #token: 705, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.84, #queue-req: 0, 
[2025-07-28 01:10:31 DP0 TP0] Decode batch. #running-req: 1, #token: 948, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.56, #queue-req: 0, 
[2025-07-28 01:10:31 DP1 TP0] Decode batch. #running-req: 1, #token: 745, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.69, #queue-req: 0, 
[2025-07-28 01:10:31 DP0 TP0] Decode batch. #running-req: 1, #token: 988, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.50, #queue-req: 0, 
[2025-07-28 01:10:31 DP1 TP0] Decode batch. #running-req: 1, #token: 785, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.51, #queue-req: 0, 
[2025-07-28 01:10:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1028, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.40, #queue-req: 0, 
[2025-07-28 01:10:31 DP1 TP0] Decode batch. #running-req: 1, #token: 825, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.46, #queue-req: 0, 
[2025-07-28 01:10:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1068, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.89, #queue-req: 0, 
[2025-07-28 01:10:31 DP1 TP0] Decode batch. #running-req: 1, #token: 865, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.49, #queue-req: 0, 
[2025-07-28 01:10:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1108, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.76, #queue-req: 0, 
[2025-07-28 01:10:32 DP1 TP0] Decode batch. #running-req: 1, #token: 905, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.48, #queue-req: 0, 
[2025-07-28 01:10:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1148, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.82, #queue-req: 0, 
[2025-07-28 01:10:32 DP1 TP0] Decode batch. #running-req: 1, #token: 945, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.63, #queue-req: 0, 
[2025-07-28 01:10:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1188, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.81, #queue-req: 0, 
[2025-07-28 01:10:32 DP1 TP0] Decode batch. #running-req: 1, #token: 985, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.55, #queue-req: 0, 
[2025-07-28 01:10:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1228, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.90, #queue-req: 0, 
[2025-07-28 01:10:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1025, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.63, #queue-req: 0, 
[2025-07-28 01:10:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.83, #queue-req: 0, 
[2025-07-28 01:10:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1065, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.02, #queue-req: 0, 
[2025-07-28 01:10:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.70, #queue-req: 0, 
[2025-07-28 01:10:33 DP1 TP0] Decode batch. #running-req: 1, #token: 1105, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.98, #queue-req: 0, 
[2025-07-28 01:10:33 DP0 TP0] Decode batch. #running-req: 1, #token: 1348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.66, #queue-req: 0, 
[2025-07-28 01:10:33 DP1 TP0] Decode batch. #running-req: 1, #token: 1145, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.92, #queue-req: 0, 
[2025-07-28 01:10:33 DP0 TP0] Decode batch. #running-req: 1, #token: 1388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.70, #queue-req: 0, 
[2025-07-28 01:10:33 DP1 TP0] Decode batch. #running-req: 1, #token: 1185, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.05, #queue-req: 0, 
[2025-07-28 01:10:33 DP0 TP0] Decode batch. #running-req: 1, #token: 1428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.69, #queue-req: 0, 
[2025-07-28 01:10:33 DP1 TP0] Decode batch. #running-req: 1, #token: 1225, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.11, #queue-req: 0, 
[2025-07-28 01:10:33 DP0 TP0] Decode batch. #running-req: 1, #token: 1468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.89, #queue-req: 0, 
[2025-07-28 01:10:33 DP1 TP0] Decode batch. #running-req: 1, #token: 1265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.34, #queue-req: 0, 
[2025-07-28 01:10:33 DP0 TP0] Decode batch. #running-req: 1, #token: 1508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.75, #queue-req: 0, 
[2025-07-28 01:10:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.03, #queue-req: 0, 
[2025-07-28 01:10:34 DP0 TP0] Decode batch. #running-req: 1, #token: 1548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.69, #queue-req: 0, 
[2025-07-28 01:10:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.85, #queue-req: 0, 
[2025-07-28 01:10:34 DP0 TP0] Decode batch. #running-req: 1, #token: 1588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.64, #queue-req: 0, 
[2025-07-28 01:10:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.79, #queue-req: 0, 
[2025-07-28 01:10:34 DP0 TP0] Decode batch. #running-req: 1, #token: 1628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.68, #queue-req: 0, 
[2025-07-28 01:10:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.91, #queue-req: 0, 
[2025-07-28 01:10:34 DP0 TP0] Decode batch. #running-req: 1, #token: 1668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.79, #queue-req: 0, 
[2025-07-28 01:10:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.02, #queue-req: 0, 
[2025-07-28 01:10:34 DP0 TP0] Decode batch. #running-req: 1, #token: 1708, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.76, #queue-req: 0, 
[2025-07-28 01:10:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.03, #queue-req: 0, 
[2025-07-28 01:10:35 DP0 TP0] Decode batch. #running-req: 1, #token: 1748, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.79, #queue-req: 0, 
[2025-07-28 01:10:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.97, #queue-req: 0, 
[2025-07-28 01:10:35 DP0 TP0] Decode batch. #running-req: 1, #token: 1788, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.72, #queue-req: 0, 
[2025-07-28 01:10:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.85, #queue-req: 0, 
[2025-07-28 01:10:35 DP0 TP0] Decode batch. #running-req: 1, #token: 1828, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.52, #queue-req: 0, 
[2025-07-28 01:10:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.84, #queue-req: 0, 
[2025-07-28 01:10:35 DP0 TP0] Decode batch. #running-req: 1, #token: 1868, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.59, #queue-req: 0, 
[2025-07-28 01:10:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.67, #queue-req: 0, 
[2025-07-28 01:10:35 DP0 TP0] Decode batch. #running-req: 1, #token: 1908, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.56, #queue-req: 0, 
[2025-07-28 01:10:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1705, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.04, #queue-req: 0, 
[2025-07-28 01:10:36 DP0 TP0] Decode batch. #running-req: 1, #token: 1948, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.57, #queue-req: 0, 
[2025-07-28 01:10:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1745, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.95, #queue-req: 0, 
[2025-07-28 01:10:36 DP0 TP0] Decode batch. #running-req: 1, #token: 1988, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.67, #queue-req: 0, 
[2025-07-28 01:10:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1785, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.96, #queue-req: 0, 
[2025-07-28 01:10:36 DP0 TP0] Decode batch. #running-req: 1, #token: 2028, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.66, #queue-req: 0, 
[2025-07-28 01:10:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1825, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.85, #queue-req: 0, 
[2025-07-28 01:10:36 DP0 TP0] Decode batch. #running-req: 1, #token: 2068, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.39, #queue-req: 0, 
[2025-07-28 01:10:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1865, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.89, #queue-req: 0, 
[2025-07-28 01:10:36 DP0 TP0] Decode batch. #running-req: 1, #token: 2108, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.96, #queue-req: 0, 
[2025-07-28 01:10:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1905, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.77, #queue-req: 0, 
[2025-07-28 01:10:37 DP0 TP0] Decode batch. #running-req: 1, #token: 2148, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.14, #queue-req: 0, 
[2025-07-28 01:10:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1945, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.86, #queue-req: 0, 
[2025-07-28 01:10:37 DP0 TP0] Decode batch. #running-req: 1, #token: 2188, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.04, #queue-req: 0, 
[2025-07-28 01:10:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1985, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.90, #queue-req: 0, 
[2025-07-28 01:10:37 DP0 TP0] Decode batch. #running-req: 1, #token: 2228, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.25, #queue-req: 0, 
[2025-07-28 01:10:37 DP1 TP0] Decode batch. #running-req: 1, #token: 2025, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.99, #queue-req: 0, 
[2025-07-28 01:10:37] INFO:     127.0.0.1:36556 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:10:37 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 143, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:10:37 DP0 TP0] Decode batch. #running-req: 1, #token: 235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 161.72, #queue-req: 0, 
[2025-07-28 01:10:37 DP1 TP0] Decode batch. #running-req: 1, #token: 2065, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.69, #queue-req: 0, 
[2025-07-28 01:10:37 DP0 TP0] Decode batch. #running-req: 1, #token: 275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.63, #queue-req: 0, 
[2025-07-28 01:10:38 DP1 TP0] Decode batch. #running-req: 1, #token: 2105, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.42, #queue-req: 0, 
[2025-07-28 01:10:38 DP0 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 190.82, #queue-req: 0, 
[2025-07-28 01:10:38 DP1 TP0] Decode batch. #running-req: 1, #token: 2145, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.75, #queue-req: 0, 
[2025-07-28 01:10:38 DP0 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.89, #queue-req: 0, 
[2025-07-28 01:10:38 DP1 TP0] Decode batch. #running-req: 1, #token: 2185, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.39, #queue-req: 0, 
[2025-07-28 01:10:38 DP0 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.10, #queue-req: 0, 
[2025-07-28 01:10:38 DP1 TP0] Decode batch. #running-req: 1, #token: 2225, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.58, #queue-req: 0, 
[2025-07-28 01:10:38 DP0 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.95, #queue-req: 0, 
[2025-07-28 01:10:38 DP1 TP0] Decode batch. #running-req: 1, #token: 2265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.53, #queue-req: 0, 
[2025-07-28 01:10:38 DP0 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.09, #queue-req: 0, 
[2025-07-28 01:10:39 DP1 TP0] Decode batch. #running-req: 1, #token: 2305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.65, #queue-req: 0, 
[2025-07-28 01:10:39 DP0 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.06, #queue-req: 0, 
[2025-07-28 01:10:39 DP1 TP0] Decode batch. #running-req: 1, #token: 2345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.39, #queue-req: 0, 
[2025-07-28 01:10:39 DP0 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.64, #queue-req: 0, 
[2025-07-28 01:10:39 DP1 TP0] Decode batch. #running-req: 1, #token: 2385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.39, #queue-req: 0, 
[2025-07-28 01:10:39 DP0 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.56, #queue-req: 0, 
[2025-07-28 01:10:39 DP1 TP0] Decode batch. #running-req: 1, #token: 2425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.12, #queue-req: 0, 
[2025-07-28 01:10:39 DP0 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.61, #queue-req: 0, 
[2025-07-28 01:10:39 DP1 TP0] Decode batch. #running-req: 1, #token: 2465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.46, #queue-req: 0, 
[2025-07-28 01:10:39 DP0 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.55, #queue-req: 0, 
[2025-07-28 01:10:40 DP1 TP0] Decode batch. #running-req: 1, #token: 2505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.53, #queue-req: 0, 
[2025-07-28 01:10:40 DP0 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.58, #queue-req: 0, 
[2025-07-28 01:10:40 DP1 TP0] Decode batch. #running-req: 1, #token: 2545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.42, #queue-req: 0, 
[2025-07-28 01:10:40 DP0 TP0] Decode batch. #running-req: 1, #token: 755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.65, #queue-req: 0, 
[2025-07-28 01:10:40 DP1 TP0] Decode batch. #running-req: 1, #token: 2585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.38, #queue-req: 0, 
[2025-07-28 01:10:40 DP0 TP0] Decode batch. #running-req: 1, #token: 795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.39, #queue-req: 0, 
[2025-07-28 01:10:40 DP1 TP0] Decode batch. #running-req: 1, #token: 2625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.29, #queue-req: 0, 
[2025-07-28 01:10:40 DP0 TP0] Decode batch. #running-req: 1, #token: 835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.25, #queue-req: 0, 
[2025-07-28 01:10:40 DP1 TP0] Decode batch. #running-req: 1, #token: 2665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.29, #queue-req: 0, 
[2025-07-28 01:10:40 DP0 TP0] Decode batch. #running-req: 1, #token: 875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.31, #queue-req: 0, 
[2025-07-28 01:10:41 DP1 TP0] Decode batch. #running-req: 1, #token: 2705, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.36, #queue-req: 0, 
[2025-07-28 01:10:41 DP0 TP0] Decode batch. #running-req: 1, #token: 915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.20, #queue-req: 0, 
[2025-07-28 01:10:41 DP1 TP0] Decode batch. #running-req: 1, #token: 2745, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.40, #queue-req: 0, 
[2025-07-28 01:10:41 DP0 TP0] Decode batch. #running-req: 1, #token: 955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.46, #queue-req: 0, 
[2025-07-28 01:10:41 DP1 TP0] Decode batch. #running-req: 1, #token: 2785, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.33, #queue-req: 0, 
[2025-07-28 01:10:41 DP0 TP0] Decode batch. #running-req: 1, #token: 995, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.39, #queue-req: 0, 
[2025-07-28 01:10:41 DP1 TP0] Decode batch. #running-req: 1, #token: 2825, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.36, #queue-req: 0, 
[2025-07-28 01:10:41 DP0 TP0] Decode batch. #running-req: 1, #token: 1035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.19, #queue-req: 0, 
[2025-07-28 01:10:41 DP1 TP0] Decode batch. #running-req: 1, #token: 2865, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.27, #queue-req: 0, 
[2025-07-28 01:10:41 DP0 TP0] Decode batch. #running-req: 1, #token: 1075, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.85, #queue-req: 0, 
[2025-07-28 01:10:42 DP1 TP0] Decode batch. #running-req: 1, #token: 2905, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.20, #queue-req: 0, 
[2025-07-28 01:10:42 DP0 TP0] Decode batch. #running-req: 1, #token: 1115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.66, #queue-req: 0, 
[2025-07-28 01:10:42 DP1 TP0] Decode batch. #running-req: 1, #token: 2945, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.19, #queue-req: 0, 
[2025-07-28 01:10:42 DP0 TP0] Decode batch. #running-req: 1, #token: 1155, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.77, #queue-req: 0, 
[2025-07-28 01:10:42 DP1 TP0] Decode batch. #running-req: 1, #token: 2985, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.35, #queue-req: 0, 
[2025-07-28 01:10:42 DP0 TP0] Decode batch. #running-req: 1, #token: 1195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.85, #queue-req: 0, 
[2025-07-28 01:10:42 DP1 TP0] Decode batch. #running-req: 1, #token: 3025, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.35, #queue-req: 0, 
[2025-07-28 01:10:42 DP0 TP0] Decode batch. #running-req: 1, #token: 1235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.80, #queue-req: 0, 
[2025-07-28 01:10:42 DP1 TP0] Decode batch. #running-req: 1, #token: 3065, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.31, #queue-req: 0, 
[2025-07-28 01:10:42 DP0 TP0] Decode batch. #running-req: 1, #token: 1275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.82, #queue-req: 0, 
[2025-07-28 01:10:43 DP1 TP0] Decode batch. #running-req: 1, #token: 3105, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.08, #queue-req: 0, 
[2025-07-28 01:10:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.70, #queue-req: 0, 
[2025-07-28 01:10:43 DP1 TP0] Decode batch. #running-req: 1, #token: 3145, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.94, #queue-req: 0, 
[2025-07-28 01:10:43] INFO:     127.0.0.1:36572 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:10:43 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 117, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:10:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.62, #queue-req: 0, 
[2025-07-28 01:10:43 DP1 TP0] Decode batch. #running-req: 1, #token: 218, token usage: 0.00, cuda graph: True, gen throughput (token/s): 161.56, #queue-req: 0, 
[2025-07-28 01:10:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.10, #queue-req: 0, 
[2025-07-28 01:10:43 DP1 TP0] Decode batch. #running-req: 1, #token: 258, token usage: 0.00, cuda graph: True, gen throughput (token/s): 202.31, #queue-req: 0, 
[2025-07-28 01:10:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.37, #queue-req: 0, 
[2025-07-28 01:10:43 DP1 TP0] Decode batch. #running-req: 1, #token: 298, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.70, #queue-req: 0, 
[2025-07-28 01:10:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 185.90, #queue-req: 0, 
[2025-07-28 01:10:44 DP1 TP0] Decode batch. #running-req: 1, #token: 338, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.78, #queue-req: 0, 
[2025-07-28 01:10:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.73, #queue-req: 0, 
[2025-07-28 01:10:44 DP1 TP0] Decode batch. #running-req: 1, #token: 378, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.93, #queue-req: 0, 
[2025-07-28 01:10:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.16, #queue-req: 0, 
[2025-07-28 01:10:44 DP1 TP0] Decode batch. #running-req: 1, #token: 418, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.02, #queue-req: 0, 
[2025-07-28 01:10:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.35, #queue-req: 0, 
[2025-07-28 01:10:44 DP1 TP0] Decode batch. #running-req: 1, #token: 458, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.05, #queue-req: 0, 
[2025-07-28 01:10:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.64, #queue-req: 0, 
[2025-07-28 01:10:44 DP1 TP0] Decode batch. #running-req: 1, #token: 498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.96, #queue-req: 0, 
[2025-07-28 01:10:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.65, #queue-req: 0, 
[2025-07-28 01:10:45 DP1 TP0] Decode batch. #running-req: 1, #token: 538, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.88, #queue-req: 0, 
[2025-07-28 01:10:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.78, #queue-req: 0, 
[2025-07-28 01:10:45 DP1 TP0] Decode batch. #running-req: 1, #token: 578, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.61, #queue-req: 0, 
[2025-07-28 01:10:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.64, #queue-req: 0, 
[2025-07-28 01:10:45 DP1 TP0] Decode batch. #running-req: 1, #token: 618, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.64, #queue-req: 0, 
[2025-07-28 01:10:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.82, #queue-req: 0, 
[2025-07-28 01:10:45 DP1 TP0] Decode batch. #running-req: 1, #token: 658, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.68, #queue-req: 0, 
[2025-07-28 01:10:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.53, #queue-req: 0, 
[2025-07-28 01:10:45 DP1 TP0] Decode batch. #running-req: 1, #token: 698, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.71, #queue-req: 0, 
[2025-07-28 01:10:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.55, #queue-req: 0, 
[2025-07-28 01:10:46 DP1 TP0] Decode batch. #running-req: 1, #token: 738, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.77, #queue-req: 0, 
[2025-07-28 01:10:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.32, #queue-req: 0, 
[2025-07-28 01:10:46 DP1 TP0] Decode batch. #running-req: 1, #token: 778, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.65, #queue-req: 0, 
[2025-07-28 01:10:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.61, #queue-req: 0, 
[2025-07-28 01:10:46 DP1 TP0] Decode batch. #running-req: 1, #token: 818, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.45, #queue-req: 0, 
[2025-07-28 01:10:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1995, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.66, #queue-req: 0, 
[2025-07-28 01:10:46 DP1 TP0] Decode batch. #running-req: 1, #token: 858, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.46, #queue-req: 0, 
[2025-07-28 01:10:46 DP0 TP0] Decode batch. #running-req: 1, #token: 2035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.72, #queue-req: 0, 
[2025-07-28 01:10:46 DP1 TP0] Decode batch. #running-req: 1, #token: 898, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.36, #queue-req: 0, 
[2025-07-28 01:10:47 DP0 TP0] Decode batch. #running-req: 1, #token: 2075, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.30, #queue-req: 0, 
[2025-07-28 01:10:47 DP1 TP0] Decode batch. #running-req: 1, #token: 938, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.63, #queue-req: 0, 
[2025-07-28 01:10:47 DP0 TP0] Decode batch. #running-req: 1, #token: 2115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.92, #queue-req: 0, 
[2025-07-28 01:10:47 DP1 TP0] Decode batch. #running-req: 1, #token: 978, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.65, #queue-req: 0, 
[2025-07-28 01:10:47 DP0 TP0] Decode batch. #running-req: 1, #token: 2155, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.19, #queue-req: 0, 
[2025-07-28 01:10:47 DP1 TP0] Decode batch. #running-req: 1, #token: 1018, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.59, #queue-req: 0, 
[2025-07-28 01:10:47 DP0 TP0] Decode batch. #running-req: 1, #token: 2195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.22, #queue-req: 0, 
[2025-07-28 01:10:47 DP1 TP0] Decode batch. #running-req: 1, #token: 1058, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.89, #queue-req: 0, 
[2025-07-28 01:10:47 DP0 TP0] Decode batch. #running-req: 1, #token: 2235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.29, #queue-req: 0, 
[2025-07-28 01:10:47 DP1 TP0] Decode batch. #running-req: 1, #token: 1098, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.94, #queue-req: 0, 
[2025-07-28 01:10:48 DP0 TP0] Decode batch. #running-req: 1, #token: 2275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.26, #queue-req: 0, 
[2025-07-28 01:10:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1138, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.01, #queue-req: 0, 
[2025-07-28 01:10:48 DP0 TP0] Decode batch. #running-req: 1, #token: 2315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.24, #queue-req: 0, 
[2025-07-28 01:10:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1178, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.07, #queue-req: 0, 
[2025-07-28 01:10:48 DP0 TP0] Decode batch. #running-req: 1, #token: 2355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.04, #queue-req: 0, 
[2025-07-28 01:10:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1218, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.13, #queue-req: 0, 
[2025-07-28 01:10:48 DP0 TP0] Decode batch. #running-req: 1, #token: 2395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.06, #queue-req: 0, 
[2025-07-28 01:10:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1258, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.98, #queue-req: 0, 
[2025-07-28 01:10:48 DP0 TP0] Decode batch. #running-req: 1, #token: 2435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.90, #queue-req: 0, 
[2025-07-28 01:10:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1298, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.03, #queue-req: 0, 
[2025-07-28 01:10:49 DP0 TP0] Decode batch. #running-req: 1, #token: 2475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.37, #queue-req: 0, 
[2025-07-28 01:10:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1338, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.97, #queue-req: 0, 
[2025-07-28 01:10:49] INFO:     127.0.0.1:41706 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:10:49 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 125, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:10:49 DP0 TP0] Decode batch. #running-req: 2, #token: 2664, token usage: 0.00, cuda graph: True, gen throughput (token/s): 258.03, #queue-req: 0, 
[2025-07-28 01:10:49 DP0 TP0] Decode batch. #running-req: 2, #token: 2744, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.26, #queue-req: 0, 
[2025-07-28 01:10:49 DP0 TP0] Decode batch. #running-req: 2, #token: 2824, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.87, #queue-req: 0, 
[2025-07-28 01:10:49 DP0 TP0] Decode batch. #running-req: 2, #token: 2904, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.95, #queue-req: 0, 
[2025-07-28 01:10:50 DP0 TP0] Decode batch. #running-req: 2, #token: 2984, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.74, #queue-req: 0, 
[2025-07-28 01:10:50 DP0 TP0] Decode batch. #running-req: 2, #token: 3064, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.94, #queue-req: 0, 
[2025-07-28 01:10:50 DP0 TP0] Decode batch. #running-req: 2, #token: 3144, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.88, #queue-req: 0, 
[2025-07-28 01:10:50 DP0 TP0] Decode batch. #running-req: 2, #token: 3224, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.74, #queue-req: 0, 
[2025-07-28 01:10:50 DP0 TP0] Decode batch. #running-req: 2, #token: 3304, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.71, #queue-req: 0, 
[2025-07-28 01:10:51 DP0 TP0] Decode batch. #running-req: 2, #token: 3384, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.73, #queue-req: 0, 
[2025-07-28 01:10:51 DP0 TP0] Decode batch. #running-req: 2, #token: 3464, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.82, #queue-req: 0, 
[2025-07-28 01:10:51 DP0 TP0] Decode batch. #running-req: 2, #token: 3544, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.55, #queue-req: 0, 
[2025-07-28 01:10:51 DP0 TP0] Decode batch. #running-req: 2, #token: 3624, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.56, #queue-req: 0, 
[2025-07-28 01:10:51 DP0 TP0] Decode batch. #running-req: 2, #token: 3704, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.64, #queue-req: 0, 
[2025-07-28 01:10:52 DP0 TP0] Decode batch. #running-req: 2, #token: 3784, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.22, #queue-req: 0, 
[2025-07-28 01:10:52 DP0 TP0] Decode batch. #running-req: 2, #token: 3864, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.14, #queue-req: 0, 
[2025-07-28 01:10:52 DP0 TP0] Decode batch. #running-req: 2, #token: 3944, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.11, #queue-req: 0, 
[2025-07-28 01:10:52 DP0 TP0] Decode batch. #running-req: 2, #token: 4024, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.82, #queue-req: 0, 
[2025-07-28 01:10:52 DP0 TP0] Decode batch. #running-req: 2, #token: 4104, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.84, #queue-req: 0, 
[2025-07-28 01:10:53 DP0 TP0] Decode batch. #running-req: 2, #token: 4184, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.88, #queue-req: 0, 
[2025-07-28 01:10:53 DP0 TP0] Decode batch. #running-req: 2, #token: 4264, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.75, #queue-req: 0, 
[2025-07-28 01:10:53 DP0 TP0] Decode batch. #running-req: 2, #token: 4344, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.54, #queue-req: 0, 
[2025-07-28 01:10:53 DP0 TP0] Decode batch. #running-req: 2, #token: 4424, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.01, #queue-req: 0, 
[2025-07-28 01:10:54 DP0 TP0] Decode batch. #running-req: 2, #token: 4504, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.82, #queue-req: 0, 
[2025-07-28 01:10:54 DP0 TP0] Decode batch. #running-req: 2, #token: 4584, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.63, #queue-req: 0, 
[2025-07-28 01:10:54 DP0 TP0] Decode batch. #running-req: 2, #token: 4664, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.31, #queue-req: 0, 
[2025-07-28 01:10:54 DP0 TP0] Decode batch. #running-req: 2, #token: 4744, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.25, #queue-req: 0, 
[2025-07-28 01:10:54 DP0 TP0] Decode batch. #running-req: 2, #token: 4824, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.40, #queue-req: 0, 
[2025-07-28 01:10:54] INFO:     127.0.0.1:41722 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:10:54 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 196, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:10:55 DP0 TP0] Decode batch. #running-req: 1, #token: 3635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.57, #queue-req: 0, 
[2025-07-28 01:10:55 DP1 TP0] Decode batch. #running-req: 1, #token: 318, token usage: 0.00, cuda graph: True, gen throughput (token/s): 6.52, #queue-req: 0, 
[2025-07-28 01:10:55 DP0 TP0] Decode batch. #running-req: 1, #token: 3675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.51, #queue-req: 0, 
[2025-07-28 01:10:55 DP1 TP0] Decode batch. #running-req: 1, #token: 358, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.90, #queue-req: 0, 
[2025-07-28 01:10:55 DP0 TP0] Decode batch. #running-req: 1, #token: 3715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.61, #queue-req: 0, 
[2025-07-28 01:10:55 DP1 TP0] Decode batch. #running-req: 1, #token: 398, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.84, #queue-req: 0, 
[2025-07-28 01:10:55 DP0 TP0] Decode batch. #running-req: 1, #token: 3755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.67, #queue-req: 0, 
[2025-07-28 01:10:55 DP1 TP0] Decode batch. #running-req: 1, #token: 438, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.04, #queue-req: 0, 
[2025-07-28 01:10:55 DP0 TP0] Decode batch. #running-req: 1, #token: 3795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.76, #queue-req: 0, 
[2025-07-28 01:10:55 DP1 TP0] Decode batch. #running-req: 1, #token: 478, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.12, #queue-req: 0, 
[2025-07-28 01:10:56 DP0 TP0] Decode batch. #running-req: 1, #token: 3835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.74, #queue-req: 0, 
[2025-07-28 01:10:56 DP1 TP0] Decode batch. #running-req: 1, #token: 518, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.06, #queue-req: 0, 
[2025-07-28 01:10:56 DP0 TP0] Decode batch. #running-req: 1, #token: 3875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.60, #queue-req: 0, 
[2025-07-28 01:10:56 DP1 TP0] Decode batch. #running-req: 1, #token: 558, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.64, #queue-req: 0, 
[2025-07-28 01:10:56 DP0 TP0] Decode batch. #running-req: 1, #token: 3915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.57, #queue-req: 0, 
[2025-07-28 01:10:56 DP1 TP0] Decode batch. #running-req: 1, #token: 598, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.77, #queue-req: 0, 
[2025-07-28 01:10:56 DP0 TP0] Decode batch. #running-req: 1, #token: 3955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.60, #queue-req: 0, 
[2025-07-28 01:10:56 DP1 TP0] Decode batch. #running-req: 1, #token: 638, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.56, #queue-req: 0, 
[2025-07-28 01:10:56 DP0 TP0] Decode batch. #running-req: 1, #token: 3995, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.70, #queue-req: 0, 
[2025-07-28 01:10:56 DP1 TP0] Decode batch. #running-req: 1, #token: 678, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.72, #queue-req: 0, 
[2025-07-28 01:10:57 DP0 TP0] Decode batch. #running-req: 1, #token: 4035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.70, #queue-req: 0, 
[2025-07-28 01:10:57 DP1 TP0] Decode batch. #running-req: 1, #token: 718, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.84, #queue-req: 0, 
[2025-07-28 01:10:57 DP0 TP0] Decode batch. #running-req: 1, #token: 4075, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.69, #queue-req: 0, 
[2025-07-28 01:10:57 DP1 TP0] Decode batch. #running-req: 1, #token: 758, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.79, #queue-req: 0, 
[2025-07-28 01:10:57 DP0 TP0] Decode batch. #running-req: 1, #token: 4115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.42, #queue-req: 0, 
[2025-07-28 01:10:57 DP1 TP0] Decode batch. #running-req: 1, #token: 798, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.53, #queue-req: 0, 
[2025-07-28 01:10:57 DP0 TP0] Decode batch. #running-req: 1, #token: 4155, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.18, #queue-req: 0, 
[2025-07-28 01:10:57 DP1 TP0] Decode batch. #running-req: 1, #token: 838, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.41, #queue-req: 0, 
[2025-07-28 01:10:57 DP0 TP0] Decode batch. #running-req: 1, #token: 4195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.20, #queue-req: 0, 
[2025-07-28 01:10:57 DP1 TP0] Decode batch. #running-req: 1, #token: 878, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.21, #queue-req: 0, 
[2025-07-28 01:10:58 DP0 TP0] Decode batch. #running-req: 1, #token: 4235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.12, #queue-req: 0, 
[2025-07-28 01:10:58 DP1 TP0] Decode batch. #running-req: 1, #token: 918, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.56, #queue-req: 0, 
[2025-07-28 01:10:58 DP0 TP0] Decode batch. #running-req: 1, #token: 4275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.23, #queue-req: 0, 
[2025-07-28 01:10:58 DP1 TP0] Decode batch. #running-req: 1, #token: 958, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.77, #queue-req: 0, 
[2025-07-28 01:10:58 DP0 TP0] Decode batch. #running-req: 1, #token: 4315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.25, #queue-req: 0, 
[2025-07-28 01:10:58 DP1 TP0] Decode batch. #running-req: 1, #token: 998, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.63, #queue-req: 0, 
[2025-07-28 01:10:58 DP0 TP0] Decode batch. #running-req: 1, #token: 4355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.26, #queue-req: 0, 
[2025-07-28 01:10:58 DP1 TP0] Decode batch. #running-req: 1, #token: 1038, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.39, #queue-req: 0, 
[2025-07-28 01:10:58 DP0 TP0] Decode batch. #running-req: 1, #token: 4395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.06, #queue-req: 0, 
[2025-07-28 01:10:58 DP1 TP0] Decode batch. #running-req: 1, #token: 1078, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.94, #queue-req: 0, 
[2025-07-28 01:10:59 DP0 TP0] Decode batch. #running-req: 1, #token: 4435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.07, #queue-req: 0, 
[2025-07-28 01:10:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1118, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.97, #queue-req: 0, 
[2025-07-28 01:10:59 DP0 TP0] Decode batch. #running-req: 1, #token: 4475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.03, #queue-req: 0, 
[2025-07-28 01:10:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1158, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.00, #queue-req: 0, 
[2025-07-28 01:10:59 DP0 TP0] Decode batch. #running-req: 1, #token: 4515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.10, #queue-req: 0, 
[2025-07-28 01:10:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1198, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.09, #queue-req: 0, 
[2025-07-28 01:10:59 DP0 TP0] Decode batch. #running-req: 1, #token: 4555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.21, #queue-req: 0, 
[2025-07-28 01:10:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1238, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.08, #queue-req: 0, 
[2025-07-28 01:10:59 DP0 TP0] Decode batch. #running-req: 1, #token: 4595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.14, #queue-req: 0, 
[2025-07-28 01:10:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1278, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.10, #queue-req: 0, 
[2025-07-28 01:11:00 DP0 TP0] Decode batch. #running-req: 1, #token: 4635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.97, #queue-req: 0, 
[2025-07-28 01:11:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1318, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.94, #queue-req: 0, 
[2025-07-28 01:11:00 DP0 TP0] Decode batch. #running-req: 1, #token: 4675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.97, #queue-req: 0, 
[2025-07-28 01:11:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1358, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.94, #queue-req: 0, 
[2025-07-28 01:11:00 DP0 TP0] Decode batch. #running-req: 1, #token: 4715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.04, #queue-req: 0, 
[2025-07-28 01:11:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1398, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.87, #queue-req: 0, 
[2025-07-28 01:11:00] INFO:     127.0.0.1:46200 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:11:00 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:11:00 DP0 TP0] Decode batch. #running-req: 2, #token: 4844, token usage: 0.00, cuda graph: True, gen throughput (token/s): 187.89, #queue-req: 0, 
[2025-07-28 01:11:00 DP0 TP0] Decode batch. #running-req: 2, #token: 4924, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.05, #queue-req: 0, 
[2025-07-28 01:11:01 DP0 TP0] Decode batch. #running-req: 2, #token: 5004, token usage: 0.00, cuda graph: True, gen throughput (token/s): 382.83, #queue-req: 0, 
[2025-07-28 01:11:01 DP0 TP0] Decode batch. #running-req: 2, #token: 5084, token usage: 0.00, cuda graph: True, gen throughput (token/s): 382.78, #queue-req: 0, 
[2025-07-28 01:11:01 DP0 TP0] Decode batch. #running-req: 2, #token: 5164, token usage: 0.00, cuda graph: True, gen throughput (token/s): 382.56, #queue-req: 0, 
[2025-07-28 01:11:01 DP0 TP0] Decode batch. #running-req: 2, #token: 5244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 382.24, #queue-req: 0, 
[2025-07-28 01:11:02 DP0 TP0] Decode batch. #running-req: 2, #token: 5324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 382.49, #queue-req: 0, 
[2025-07-28 01:11:02 DP0 TP0] Decode batch. #running-req: 2, #token: 5404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 382.15, #queue-req: 0, 
[2025-07-28 01:11:02 DP0 TP0] Decode batch. #running-req: 2, #token: 5484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 382.08, #queue-req: 0, 
[2025-07-28 01:11:02 DP0 TP0] Decode batch. #running-req: 2, #token: 5564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 382.11, #queue-req: 0, 
[2025-07-28 01:11:02 DP0 TP0] Decode batch. #running-req: 2, #token: 5644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.37, #queue-req: 0, 
[2025-07-28 01:11:03 DP0 TP0] Decode batch. #running-req: 2, #token: 5724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.04, #queue-req: 0, 
[2025-07-28 01:11:03 DP0 TP0] Decode batch. #running-req: 2, #token: 5804, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.94, #queue-req: 0, 
[2025-07-28 01:11:03 DP0 TP0] Decode batch. #running-req: 2, #token: 5884, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.97, #queue-req: 0, 
[2025-07-28 01:11:03 DP0 TP0] Decode batch. #running-req: 2, #token: 5964, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.19, #queue-req: 0, 
[2025-07-28 01:11:03 DP0 TP0] Decode batch. #running-req: 2, #token: 6044, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.19, #queue-req: 0, 
[2025-07-28 01:11:04 DP0 TP0] Decode batch. #running-req: 2, #token: 6124, token usage: 0.00, cuda graph: True, gen throughput (token/s): 361.96, #queue-req: 0, 
[2025-07-28 01:11:04 DP0 TP0] Decode batch. #running-req: 2, #token: 6204, token usage: 0.00, cuda graph: True, gen throughput (token/s): 379.90, #queue-req: 0, 
[2025-07-28 01:11:04 DP0 TP0] Decode batch. #running-req: 2, #token: 6284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.94, #queue-req: 0, 
[2025-07-28 01:11:04 DP0 TP0] Decode batch. #running-req: 2, #token: 6364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.85, #queue-req: 0, 
[2025-07-28 01:11:04 DP0 TP0] Decode batch. #running-req: 2, #token: 6444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.99, #queue-req: 0, 
[2025-07-28 01:11:05 DP0 TP0] Decode batch. #running-req: 2, #token: 6524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.11, #queue-req: 0, 
[2025-07-28 01:11:05 DP0 TP0] Decode batch. #running-req: 2, #token: 6604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.30, #queue-req: 0, 
[2025-07-28 01:11:05 DP0 TP0] Decode batch. #running-req: 2, #token: 6684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 381.04, #queue-req: 0, 
[2025-07-28 01:11:05 DP0 TP0] Decode batch. #running-req: 2, #token: 6764, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.98, #queue-req: 0, 
[2025-07-28 01:11:06 DP0 TP0] Decode batch. #running-req: 2, #token: 6844, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.70, #queue-req: 0, 
[2025-07-28 01:11:06 DP0 TP0] Decode batch. #running-req: 2, #token: 6924, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.89, #queue-req: 0, 
[2025-07-28 01:11:06 DP0 TP0] Decode batch. #running-req: 2, #token: 7004, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.86, #queue-req: 0, 
[2025-07-28 01:11:06 DP0 TP0] Decode batch. #running-req: 2, #token: 7084, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.73, #queue-req: 0, 
[2025-07-28 01:11:06 DP0 TP0] Decode batch. #running-req: 2, #token: 7164, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.72, #queue-req: 0, 
[2025-07-28 01:11:07 DP0 TP0] Decode batch. #running-req: 2, #token: 7244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.77, #queue-req: 0, 
[2025-07-28 01:11:07 DP0 TP0] Decode batch. #running-req: 2, #token: 7324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.60, #queue-req: 0, 
[2025-07-28 01:11:07 DP0 TP0] Decode batch. #running-req: 2, #token: 7404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 380.48, #queue-req: 0, 
[2025-07-28 01:11:07] INFO:     127.0.0.1:46204 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:11:07 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 154, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:11:07 DP0 TP0] Decode batch. #running-req: 1, #token: 6075, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.11, #queue-req: 0, 
[2025-07-28 01:11:07 DP1 TP0] Decode batch. #running-req: 1, #token: 263, token usage: 0.00, cuda graph: True, gen throughput (token/s): 5.55, #queue-req: 0, 
[2025-07-28 01:11:07 DP0 TP0] Decode batch. #running-req: 1, #token: 6115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.50, #queue-req: 0, 
[2025-07-28 01:11:08 DP1 TP0] Decode batch. #running-req: 1, #token: 303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 188.43, #queue-req: 0, 
[2025-07-28 01:11:08 DP0 TP0] Decode batch. #running-req: 1, #token: 6155, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.41, #queue-req: 0, 
[2025-07-28 01:11:08 DP1 TP0] Decode batch. #running-req: 1, #token: 343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.15, #queue-req: 0, 
[2025-07-28 01:11:08 DP0 TP0] Decode batch. #running-req: 1, #token: 6195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.99, #queue-req: 0, 
[2025-07-28 01:11:08 DP1 TP0] Decode batch. #running-req: 1, #token: 383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.62, #queue-req: 0, 
[2025-07-28 01:11:08 DP0 TP0] Decode batch. #running-req: 1, #token: 6235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.02, #queue-req: 0, 
[2025-07-28 01:11:08 DP1 TP0] Decode batch. #running-req: 1, #token: 423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.87, #queue-req: 0, 
[2025-07-28 01:11:08 DP0 TP0] Decode batch. #running-req: 1, #token: 6275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.11, #queue-req: 0, 
[2025-07-28 01:11:08 DP1 TP0] Decode batch. #running-req: 1, #token: 463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.05, #queue-req: 0, 
[2025-07-28 01:11:08 DP0 TP0] Decode batch. #running-req: 1, #token: 6315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.04, #queue-req: 0, 
[2025-07-28 01:11:09 DP1 TP0] Decode batch. #running-req: 1, #token: 503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.07, #queue-req: 0, 
[2025-07-28 01:11:09 DP0 TP0] Decode batch. #running-req: 1, #token: 6355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.04, #queue-req: 0, 
[2025-07-28 01:11:09 DP1 TP0] Decode batch. #running-req: 1, #token: 543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.55, #queue-req: 0, 
[2025-07-28 01:11:09 DP0 TP0] Decode batch. #running-req: 1, #token: 6395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.09, #queue-req: 0, 
[2025-07-28 01:11:09 DP1 TP0] Decode batch. #running-req: 1, #token: 583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.66, #queue-req: 0, 
[2025-07-28 01:11:09 DP0 TP0] Decode batch. #running-req: 1, #token: 6435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.93, #queue-req: 0, 
[2025-07-28 01:11:09 DP1 TP0] Decode batch. #running-req: 1, #token: 623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.61, #queue-req: 0, 
[2025-07-28 01:11:09 DP0 TP0] Decode batch. #running-req: 1, #token: 6475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.00, #queue-req: 0, 
[2025-07-28 01:11:09 DP1 TP0] Decode batch. #running-req: 1, #token: 663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.66, #queue-req: 0, 
[2025-07-28 01:11:09 DP0 TP0] Decode batch. #running-req: 1, #token: 6515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.99, #queue-req: 0, 
[2025-07-28 01:11:10 DP1 TP0] Decode batch. #running-req: 1, #token: 703, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.82, #queue-req: 0, 
[2025-07-28 01:11:10 DP0 TP0] Decode batch. #running-req: 1, #token: 6555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.05, #queue-req: 0, 
[2025-07-28 01:11:10 DP1 TP0] Decode batch. #running-req: 1, #token: 743, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.68, #queue-req: 0, 
[2025-07-28 01:11:10 DP0 TP0] Decode batch. #running-req: 1, #token: 6595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.07, #queue-req: 0, 
[2025-07-28 01:11:10 DP1 TP0] Decode batch. #running-req: 1, #token: 783, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.63, #queue-req: 0, 
[2025-07-28 01:11:10 DP0 TP0] Decode batch. #running-req: 1, #token: 6635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.00, #queue-req: 0, 
[2025-07-28 01:11:10 DP1 TP0] Decode batch. #running-req: 1, #token: 823, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.47, #queue-req: 0, 
[2025-07-28 01:11:10 DP0 TP0] Decode batch. #running-req: 1, #token: 6675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.02, #queue-req: 0, 
[2025-07-28 01:11:10 DP1 TP0] Decode batch. #running-req: 1, #token: 863, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.47, #queue-req: 0, 
[2025-07-28 01:11:10 DP0 TP0] Decode batch. #running-req: 1, #token: 6715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.97, #queue-req: 0, 
[2025-07-28 01:11:11 DP1 TP0] Decode batch. #running-req: 1, #token: 903, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.47, #queue-req: 0, 
[2025-07-28 01:11:11 DP0 TP0] Decode batch. #running-req: 1, #token: 6755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.93, #queue-req: 0, 
[2025-07-28 01:11:11 DP1 TP0] Decode batch. #running-req: 1, #token: 943, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.66, #queue-req: 0, 
[2025-07-28 01:11:11 DP0 TP0] Decode batch. #running-req: 1, #token: 6795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.90, #queue-req: 0, 
[2025-07-28 01:11:11 DP1 TP0] Decode batch. #running-req: 1, #token: 983, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.67, #queue-req: 0, 
[2025-07-28 01:11:11 DP0 TP0] Decode batch. #running-req: 1, #token: 6835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.99, #queue-req: 0, 
[2025-07-28 01:11:11 DP1 TP0] Decode batch. #running-req: 1, #token: 1023, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.72, #queue-req: 0, 
[2025-07-28 01:11:11 DP0 TP0] Decode batch. #running-req: 1, #token: 6875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.97, #queue-req: 0, 
[2025-07-28 01:11:11 DP1 TP0] Decode batch. #running-req: 1, #token: 1063, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.10, #queue-req: 0, 
[2025-07-28 01:11:11 DP0 TP0] Decode batch. #running-req: 1, #token: 6915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.95, #queue-req: 0, 
[2025-07-28 01:11:12 DP1 TP0] Decode batch. #running-req: 1, #token: 1103, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.91, #queue-req: 0, 
[2025-07-28 01:11:12 DP0 TP0] Decode batch. #running-req: 1, #token: 6955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.88, #queue-req: 0, 
[2025-07-28 01:11:12 DP1 TP0] Decode batch. #running-req: 1, #token: 1143, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.97, #queue-req: 0, 
[2025-07-28 01:11:12 DP0 TP0] Decode batch. #running-req: 1, #token: 6995, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.83, #queue-req: 0, 
[2025-07-28 01:11:12 DP1 TP0] Decode batch. #running-req: 1, #token: 1183, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.10, #queue-req: 0, 
[2025-07-28 01:11:12 DP0 TP0] Decode batch. #running-req: 1, #token: 7035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.70, #queue-req: 0, 
[2025-07-28 01:11:12 DP1 TP0] Decode batch. #running-req: 1, #token: 1223, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.13, #queue-req: 0, 
[2025-07-28 01:11:12 DP0 TP0] Decode batch. #running-req: 1, #token: 7075, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.98, #queue-req: 0, 
[2025-07-28 01:11:12 DP1 TP0] Decode batch. #running-req: 1, #token: 1263, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.12, #queue-req: 0, 
[2025-07-28 01:11:12 DP0 TP0] Decode batch. #running-req: 1, #token: 7115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.94, #queue-req: 0, 
[2025-07-28 01:11:13 DP1 TP0] Decode batch. #running-req: 1, #token: 1303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.97, #queue-req: 0, 
[2025-07-28 01:11:13 DP0 TP0] Decode batch. #running-req: 1, #token: 7155, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.94, #queue-req: 0, 
[2025-07-28 01:11:13 DP1 TP0] Decode batch. #running-req: 1, #token: 1343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.97, #queue-req: 0, 
[2025-07-28 01:11:13 DP0 TP0] Decode batch. #running-req: 1, #token: 7195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.70, #queue-req: 0, 
[2025-07-28 01:11:13 DP1 TP0] Decode batch. #running-req: 1, #token: 1383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.94, #queue-req: 0, 
[2025-07-28 01:11:13 DP0 TP0] Decode batch. #running-req: 1, #token: 7235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.59, #queue-req: 0, 
[2025-07-28 01:11:13 DP1 TP0] Decode batch. #running-req: 1, #token: 1423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.04, #queue-req: 0, 
[2025-07-28 01:11:13 DP0 TP0] Decode batch. #running-req: 1, #token: 7275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.54, #queue-req: 0, 
[2025-07-28 01:11:13 DP1 TP0] Decode batch. #running-req: 1, #token: 1463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.97, #queue-req: 0, 
[2025-07-28 01:11:13 DP0 TP0] Decode batch. #running-req: 1, #token: 7315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.74, #queue-req: 0, 
[2025-07-28 01:11:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.13, #queue-req: 0, 
[2025-07-28 01:11:14 DP0 TP0] Decode batch. #running-req: 1, #token: 7355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.69, #queue-req: 0, 
[2025-07-28 01:11:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.99, #queue-req: 0, 
[2025-07-28 01:11:14 DP0 TP0] Decode batch. #running-req: 1, #token: 7395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.71, #queue-req: 0, 
[2025-07-28 01:11:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.93, #queue-req: 0, 
[2025-07-28 01:11:14 DP0 TP0] Decode batch. #running-req: 1, #token: 7435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.70, #queue-req: 0, 
[2025-07-28 01:11:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.88, #queue-req: 0, 
[2025-07-28 01:11:14 DP0 TP0] Decode batch. #running-req: 1, #token: 7475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.60, #queue-req: 0, 
[2025-07-28 01:11:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.91, #queue-req: 0, 
[2025-07-28 01:11:15 DP0 TP0] Decode batch. #running-req: 1, #token: 7515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.49, #queue-req: 0, 
[2025-07-28 01:11:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1703, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.11, #queue-req: 0, 
[2025-07-28 01:11:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1743, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.96, #queue-req: 0, 
[2025-07-28 01:11:15 DP0 TP0] Decode batch. #running-req: 1, #token: 7555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.60, #queue-req: 0, 
[2025-07-28 01:11:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1783, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.12, #queue-req: 0, 
[2025-07-28 01:11:15 DP0 TP0] Decode batch. #running-req: 1, #token: 7595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.63, #queue-req: 0, 
[2025-07-28 01:11:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1823, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.89, #queue-req: 0, 
[2025-07-28 01:11:15 DP0 TP0] Decode batch. #running-req: 1, #token: 7635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.63, #queue-req: 0, 
[2025-07-28 01:11:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1863, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.85, #queue-req: 0, 
[2025-07-28 01:11:15 DP0 TP0] Decode batch. #running-req: 1, #token: 7675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.64, #queue-req: 0, 
[2025-07-28 01:11:16 DP1 TP0] Decode batch. #running-req: 1, #token: 1903, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.83, #queue-req: 0, 
[2025-07-28 01:11:16 DP0 TP0] Decode batch. #running-req: 1, #token: 7715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.50, #queue-req: 0, 
[2025-07-28 01:11:16 DP1 TP0] Decode batch. #running-req: 1, #token: 1943, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.88, #queue-req: 0, 
[2025-07-28 01:11:16 DP0 TP0] Decode batch. #running-req: 1, #token: 7755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.08, #queue-req: 0, 
[2025-07-28 01:11:16 DP1 TP0] Decode batch. #running-req: 1, #token: 1983, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.45, #queue-req: 0, 
[2025-07-28 01:11:16 DP0 TP0] Decode batch. #running-req: 1, #token: 7795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 195.47, #queue-req: 0, 
[2025-07-28 01:11:16 DP1 TP0] Decode batch. #running-req: 1, #token: 2023, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.30, #queue-req: 0, 
[2025-07-28 01:11:16 DP0 TP0] Decode batch. #running-req: 1, #token: 7835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 195.26, #queue-req: 0, 
[2025-07-28 01:11:16 DP1 TP0] Decode batch. #running-req: 1, #token: 2063, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.21, #queue-req: 0, 
[2025-07-28 01:11:16 DP0 TP0] Decode batch. #running-req: 1, #token: 7875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 182.92, #queue-req: 0, 
[2025-07-28 01:11:17 DP1 TP0] Decode batch. #running-req: 1, #token: 2103, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.18, #queue-req: 0, 
[2025-07-28 01:11:17 DP0 TP0] Decode batch. #running-req: 1, #token: 7915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 195.67, #queue-req: 0, 
[2025-07-28 01:11:17 DP1 TP0] Decode batch. #running-req: 1, #token: 2143, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.40, #queue-req: 0, 
[2025-07-28 01:11:17 DP0 TP0] Decode batch. #running-req: 1, #token: 7955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.09, #queue-req: 0, 
[2025-07-28 01:11:17 DP1 TP0] Decode batch. #running-req: 1, #token: 2183, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.46, #queue-req: 0, 
[2025-07-28 01:11:17 DP0 TP0] Decode batch. #running-req: 1, #token: 7995, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.32, #queue-req: 0, 
[2025-07-28 01:11:17 DP1 TP0] Decode batch. #running-req: 1, #token: 2223, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.57, #queue-req: 0, 
[2025-07-28 01:11:17 DP0 TP0] Decode batch. #running-req: 1, #token: 8035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.31, #queue-req: 0, 
[2025-07-28 01:11:17 DP1 TP0] Decode batch. #running-req: 1, #token: 2263, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.49, #queue-req: 0, 
[2025-07-28 01:11:17 DP0 TP0] Decode batch. #running-req: 1, #token: 8075, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.49, #queue-req: 0, 
[2025-07-28 01:11:18 DP1 TP0] Decode batch. #running-req: 1, #token: 2303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.61, #queue-req: 0, 
[2025-07-28 01:11:18 DP0 TP0] Decode batch. #running-req: 1, #token: 8115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.53, #queue-req: 0, 
[2025-07-28 01:11:18 DP1 TP0] Decode batch. #running-req: 1, #token: 2343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.31, #queue-req: 0, 
[2025-07-28 01:11:18 DP0 TP0] Decode batch. #running-req: 1, #token: 8155, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.49, #queue-req: 0, 
[2025-07-28 01:11:18 DP1 TP0] Decode batch. #running-req: 1, #token: 2383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.38, #queue-req: 0, 
[2025-07-28 01:11:18 DP0 TP0] Decode batch. #running-req: 1, #token: 8195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.44, #queue-req: 0, 
[2025-07-28 01:11:18 DP1 TP0] Decode batch. #running-req: 1, #token: 2423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.41, #queue-req: 0, 
[2025-07-28 01:11:18 DP0 TP0] Decode batch. #running-req: 1, #token: 8235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.03, #queue-req: 0, 
[2025-07-28 01:11:18 DP1 TP0] Decode batch. #running-req: 1, #token: 2463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.46, #queue-req: 0, 
[2025-07-28 01:11:18 DP0 TP0] Decode batch. #running-req: 1, #token: 8275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.03, #queue-req: 0, 
[2025-07-28 01:11:19 DP1 TP0] Decode batch. #running-req: 1, #token: 2503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.54, #queue-req: 0, 
[2025-07-28 01:11:19 DP0 TP0] Decode batch. #running-req: 1, #token: 8315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.02, #queue-req: 0, 
[2025-07-28 01:11:19 DP1 TP0] Decode batch. #running-req: 1, #token: 2543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.59, #queue-req: 0, 
[2025-07-28 01:11:19 DP0 TP0] Decode batch. #running-req: 1, #token: 8355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.04, #queue-req: 0, 
[2025-07-28 01:11:19 DP1 TP0] Decode batch. #running-req: 1, #token: 2583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.42, #queue-req: 0, 
[2025-07-28 01:11:19 DP0 TP0] Decode batch. #running-req: 1, #token: 8395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.03, #queue-req: 0, 
[2025-07-28 01:11:19 DP1 TP0] Decode batch. #running-req: 1, #token: 2623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.26, #queue-req: 0, 
[2025-07-28 01:11:19] INFO:     127.0.0.1:36868 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:11:19 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 128, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:11:19 DP0 TP0] Decode batch. #running-req: 1, #token: 230, token usage: 0.00, cuda graph: True, gen throughput (token/s): 157.25, #queue-req: 0, 
[2025-07-28 01:11:19 DP1 TP0] Decode batch. #running-req: 1, #token: 2663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.38, #queue-req: 0, 
[2025-07-28 01:11:19 DP0 TP0] Decode batch. #running-req: 1, #token: 270, token usage: 0.00, cuda graph: True, gen throughput (token/s): 202.12, #queue-req: 0, 
[2025-07-28 01:11:20 DP1 TP0] Decode batch. #running-req: 1, #token: 2703, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.37, #queue-req: 0, 
[2025-07-28 01:11:20 DP0 TP0] Decode batch. #running-req: 1, #token: 310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.30, #queue-req: 0, 
[2025-07-28 01:11:20 DP1 TP0] Decode batch. #running-req: 1, #token: 2743, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.54, #queue-req: 0, 
[2025-07-28 01:11:20 DP0 TP0] Decode batch. #running-req: 1, #token: 350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.19, #queue-req: 0, 
[2025-07-28 01:11:20 DP1 TP0] Decode batch. #running-req: 1, #token: 2783, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.52, #queue-req: 0, 
[2025-07-28 01:11:20 DP0 TP0] Decode batch. #running-req: 1, #token: 390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.10, #queue-req: 0, 
[2025-07-28 01:11:20 DP1 TP0] Decode batch. #running-req: 1, #token: 2823, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.41, #queue-req: 0, 
[2025-07-28 01:11:20 DP0 TP0] Decode batch. #running-req: 1, #token: 430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.08, #queue-req: 0, 
[2025-07-28 01:11:20 DP1 TP0] Decode batch. #running-req: 1, #token: 2863, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.27, #queue-req: 0, 
[2025-07-28 01:11:20 DP0 TP0] Decode batch. #running-req: 1, #token: 470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.05, #queue-req: 0, 
[2025-07-28 01:11:21 DP1 TP0] Decode batch. #running-req: 1, #token: 2903, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.27, #queue-req: 0, 
[2025-07-28 01:11:21 DP0 TP0] Decode batch. #running-req: 1, #token: 510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.17, #queue-req: 0, 
[2025-07-28 01:11:21 DP1 TP0] Decode batch. #running-req: 1, #token: 2943, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.21, #queue-req: 0, 
[2025-07-28 01:11:21 DP0 TP0] Decode batch. #running-req: 1, #token: 550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.71, #queue-req: 0, 
[2025-07-28 01:11:21 DP1 TP0] Decode batch. #running-req: 1, #token: 2983, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.45, #queue-req: 0, 
[2025-07-28 01:11:21 DP0 TP0] Decode batch. #running-req: 1, #token: 590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.73, #queue-req: 0, 
[2025-07-28 01:11:21 DP1 TP0] Decode batch. #running-req: 1, #token: 3023, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.40, #queue-req: 0, 
[2025-07-28 01:11:21 DP0 TP0] Decode batch. #running-req: 1, #token: 630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.67, #queue-req: 0, 
[2025-07-28 01:11:21 DP1 TP0] Decode batch. #running-req: 1, #token: 3063, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.37, #queue-req: 0, 
[2025-07-28 01:11:21 DP0 TP0] Decode batch. #running-req: 1, #token: 670, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.64, #queue-req: 0, 
[2025-07-28 01:11:22 DP1 TP0] Decode batch. #running-req: 1, #token: 3103, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.18, #queue-req: 0, 
[2025-07-28 01:11:22 DP0 TP0] Decode batch. #running-req: 1, #token: 710, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.73, #queue-req: 0, 
[2025-07-28 01:11:22 DP1 TP0] Decode batch. #running-req: 1, #token: 3143, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.04, #queue-req: 0, 
[2025-07-28 01:11:22 DP0 TP0] Decode batch. #running-req: 1, #token: 750, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.60, #queue-req: 0, 
[2025-07-28 01:11:22 DP1 TP0] Decode batch. #running-req: 1, #token: 3183, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.92, #queue-req: 0, 
[2025-07-28 01:11:22 DP0 TP0] Decode batch. #running-req: 1, #token: 790, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.56, #queue-req: 0, 
[2025-07-28 01:11:22 DP1 TP0] Decode batch. #running-req: 1, #token: 3223, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.07, #queue-req: 0, 
[2025-07-28 01:11:22 DP0 TP0] Decode batch. #running-req: 1, #token: 830, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.40, #queue-req: 0, 
[2025-07-28 01:11:22 DP1 TP0] Decode batch. #running-req: 1, #token: 3263, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.20, #queue-req: 0, 
[2025-07-28 01:11:22 DP0 TP0] Decode batch. #running-req: 1, #token: 870, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.42, #queue-req: 0, 
[2025-07-28 01:11:23 DP1 TP0] Decode batch. #running-req: 1, #token: 3303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.17, #queue-req: 0, 
[2025-07-28 01:11:23 DP0 TP0] Decode batch. #running-req: 1, #token: 910, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.44, #queue-req: 0, 
[2025-07-28 01:11:23 DP1 TP0] Decode batch. #running-req: 1, #token: 3343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.09, #queue-req: 0, 
[2025-07-28 01:11:23 DP0 TP0] Decode batch. #running-req: 1, #token: 950, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.51, #queue-req: 0, 
[2025-07-28 01:11:23 DP1 TP0] Decode batch. #running-req: 1, #token: 3383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.97, #queue-req: 0, 
[2025-07-28 01:11:23 DP0 TP0] Decode batch. #running-req: 1, #token: 990, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.47, #queue-req: 0, 
[2025-07-28 01:11:23 DP1 TP0] Decode batch. #running-req: 1, #token: 3423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.94, #queue-req: 0, 
[2025-07-28 01:11:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1030, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.52, #queue-req: 0, 
[2025-07-28 01:11:23 DP1 TP0] Decode batch. #running-req: 1, #token: 3463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.01, #queue-req: 0, 
[2025-07-28 01:11:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1070, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.01, #queue-req: 0, 
[2025-07-28 01:11:24 DP1 TP0] Decode batch. #running-req: 1, #token: 3503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.16, #queue-req: 0, 
[2025-07-28 01:11:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1110, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.88, #queue-req: 0, 
[2025-07-28 01:11:24 DP1 TP0] Decode batch. #running-req: 1, #token: 3543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.08, #queue-req: 0, 
[2025-07-28 01:11:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1150, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.86, #queue-req: 0, 
[2025-07-28 01:11:24 DP1 TP0] Decode batch. #running-req: 1, #token: 3583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.12, #queue-req: 0, 
[2025-07-28 01:11:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1190, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.95, #queue-req: 0, 
[2025-07-28 01:11:24 DP1 TP0] Decode batch. #running-req: 1, #token: 3623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.91, #queue-req: 0, 
[2025-07-28 01:11:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1230, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.93, #queue-req: 0, 
[2025-07-28 01:11:24 DP1 TP0] Decode batch. #running-req: 1, #token: 3663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.93, #queue-req: 0, 
[2025-07-28 01:11:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1270, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.96, #queue-req: 0, 
[2025-07-28 01:11:25 DP1 TP0] Decode batch. #running-req: 1, #token: 3703, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.87, #queue-req: 0, 
[2025-07-28 01:11:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.83, #queue-req: 0, 
[2025-07-28 01:11:25 DP1 TP0] Decode batch. #running-req: 1, #token: 3743, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.66, #queue-req: 0, 
[2025-07-28 01:11:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.78, #queue-req: 0, 
[2025-07-28 01:11:25 DP1 TP0] Decode batch. #running-req: 1, #token: 3783, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.04, #queue-req: 0, 
[2025-07-28 01:11:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.89, #queue-req: 0, 
[2025-07-28 01:11:25 DP1 TP0] Decode batch. #running-req: 1, #token: 3823, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.90, #queue-req: 0, 
[2025-07-28 01:11:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.81, #queue-req: 0, 
[2025-07-28 01:11:25 DP1 TP0] Decode batch. #running-req: 1, #token: 3863, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.97, #queue-req: 0, 
[2025-07-28 01:11:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.95, #queue-req: 0, 
[2025-07-28 01:11:26 DP1 TP0] Decode batch. #running-req: 1, #token: 3903, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.83, #queue-req: 0, 
[2025-07-28 01:11:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.89, #queue-req: 0, 
[2025-07-28 01:11:26 DP1 TP0] Decode batch. #running-req: 1, #token: 3943, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.82, #queue-req: 0, 
[2025-07-28 01:11:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.93, #queue-req: 0, 
[2025-07-28 01:11:26 DP1 TP0] Decode batch. #running-req: 1, #token: 3983, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.91, #queue-req: 0, 
[2025-07-28 01:11:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.72, #queue-req: 0, 
[2025-07-28 01:11:26 DP1 TP0] Decode batch. #running-req: 1, #token: 4023, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.73, #queue-req: 0, 
[2025-07-28 01:11:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.69, #queue-req: 0, 
[2025-07-28 01:11:26 DP1 TP0] Decode batch. #running-req: 1, #token: 4063, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.97, #queue-req: 0, 
[2025-07-28 01:11:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1670, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.63, #queue-req: 0, 
[2025-07-28 01:11:27 DP1 TP0] Decode batch. #running-req: 1, #token: 4103, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.93, #queue-req: 0, 
[2025-07-28 01:11:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1710, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.85, #queue-req: 0, 
[2025-07-28 01:11:27 DP1 TP0] Decode batch. #running-req: 1, #token: 4143, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.38, #queue-req: 0, 
[2025-07-28 01:11:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1750, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.80, #queue-req: 0, 
[2025-07-28 01:11:27 DP1 TP0] Decode batch. #running-req: 1, #token: 4183, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.41, #queue-req: 0, 
[2025-07-28 01:11:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1790, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.81, #queue-req: 0, 
[2025-07-28 01:11:27 DP1 TP0] Decode batch. #running-req: 1, #token: 4223, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.42, #queue-req: 0, 
[2025-07-28 01:11:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1830, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.57, #queue-req: 0, 
[2025-07-28 01:11:27 DP1 TP0] Decode batch. #running-req: 1, #token: 4263, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.51, #queue-req: 0, 
[2025-07-28 01:11:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1870, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.66, #queue-req: 0, 
[2025-07-28 01:11:28 DP1 TP0] Decode batch. #running-req: 1, #token: 4303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.39, #queue-req: 0, 
[2025-07-28 01:11:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1910, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.68, #queue-req: 0, 
[2025-07-28 01:11:28 DP1 TP0] Decode batch. #running-req: 1, #token: 4343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.53, #queue-req: 0, 
[2025-07-28 01:11:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1950, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.76, #queue-req: 0, 
[2025-07-28 01:11:28 DP1 TP0] Decode batch. #running-req: 1, #token: 4383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.44, #queue-req: 0, 
[2025-07-28 01:11:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1990, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.79, #queue-req: 0, 
[2025-07-28 01:11:28 DP1 TP0] Decode batch. #running-req: 1, #token: 4423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.33, #queue-req: 0, 
[2025-07-28 01:11:28 DP0 TP0] Decode batch. #running-req: 1, #token: 2030, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.74, #queue-req: 0, 
[2025-07-28 01:11:28 DP1 TP0] Decode batch. #running-req: 1, #token: 4463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.32, #queue-req: 0, 
[2025-07-28 01:11:28 DP0 TP0] Decode batch. #running-req: 1, #token: 2070, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.42, #queue-req: 0, 
[2025-07-28 01:11:29 DP1 TP0] Decode batch. #running-req: 1, #token: 4503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.49, #queue-req: 0, 
[2025-07-28 01:11:29 DP0 TP0] Decode batch. #running-req: 1, #token: 2110, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.12, #queue-req: 0, 
[2025-07-28 01:11:29 DP1 TP0] Decode batch. #running-req: 1, #token: 4543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.41, #queue-req: 0, 
[2025-07-28 01:11:29 DP0 TP0] Decode batch. #running-req: 1, #token: 2150, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.14, #queue-req: 0, 
[2025-07-28 01:11:29 DP1 TP0] Decode batch. #running-req: 1, #token: 4583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.45, #queue-req: 0, 
[2025-07-28 01:11:29 DP0 TP0] Decode batch. #running-req: 1, #token: 2190, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.22, #queue-req: 0, 
[2025-07-28 01:11:29 DP1 TP0] Decode batch. #running-req: 1, #token: 4623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.42, #queue-req: 0, 
[2025-07-28 01:11:29 DP0 TP0] Decode batch. #running-req: 1, #token: 2230, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.31, #queue-req: 0, 
[2025-07-28 01:11:29 DP1 TP0] Decode batch. #running-req: 1, #token: 4663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.21, #queue-req: 0, 
[2025-07-28 01:11:29 DP0 TP0] Decode batch. #running-req: 1, #token: 2270, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.35, #queue-req: 0, 
[2025-07-28 01:11:30 DP1 TP0] Decode batch. #running-req: 1, #token: 4703, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.22, #queue-req: 0, 
[2025-07-28 01:11:30 DP0 TP0] Decode batch. #running-req: 1, #token: 2310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.30, #queue-req: 0, 
[2025-07-28 01:11:30 DP1 TP0] Decode batch. #running-req: 1, #token: 4743, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.29, #queue-req: 0, 
[2025-07-28 01:11:30 DP0 TP0] Decode batch. #running-req: 1, #token: 2350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.13, #queue-req: 0, 
[2025-07-28 01:11:30 DP1 TP0] Decode batch. #running-req: 1, #token: 4783, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.40, #queue-req: 0, 
[2025-07-28 01:11:30 DP0 TP0] Decode batch. #running-req: 1, #token: 2390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.09, #queue-req: 0, 
[2025-07-28 01:11:30 DP1 TP0] Decode batch. #running-req: 1, #token: 4823, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.38, #queue-req: 0, 
[2025-07-28 01:11:30 DP0 TP0] Decode batch. #running-req: 1, #token: 2430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.17, #queue-req: 0, 
[2025-07-28 01:11:30 DP1 TP0] Decode batch. #running-req: 1, #token: 4863, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.36, #queue-req: 0, 
[2025-07-28 01:11:30 DP0 TP0] Decode batch. #running-req: 1, #token: 2470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.27, #queue-req: 0, 
[2025-07-28 01:11:31 DP1 TP0] Decode batch. #running-req: 1, #token: 4903, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.29, #queue-req: 0, 
[2025-07-28 01:11:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.25, #queue-req: 0, 
[2025-07-28 01:11:31 DP1 TP0] Decode batch. #running-req: 1, #token: 4943, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.24, #queue-req: 0, 
[2025-07-28 01:11:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.19, #queue-req: 0, 
[2025-07-28 01:11:31 DP1 TP0] Decode batch. #running-req: 1, #token: 4983, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.20, #queue-req: 0, 
[2025-07-28 01:11:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.07, #queue-req: 0, 
[2025-07-28 01:11:31 DP1 TP0] Decode batch. #running-req: 1, #token: 5023, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.35, #queue-req: 0, 
[2025-07-28 01:11:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.09, #queue-req: 0, 
[2025-07-28 01:11:31 DP1 TP0] Decode batch. #running-req: 1, #token: 5063, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.33, #queue-req: 0, 
[2025-07-28 01:11:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2670, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.95, #queue-req: 0, 
[2025-07-28 01:11:32 DP1 TP0] Decode batch. #running-req: 1, #token: 5103, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.33, #queue-req: 0, 
[2025-07-28 01:11:32 DP0 TP0] Decode batch. #running-req: 1, #token: 2710, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.13, #queue-req: 0, 
[2025-07-28 01:11:32 DP1 TP0] Decode batch. #running-req: 1, #token: 5143, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.11, #queue-req: 0, 
[2025-07-28 01:11:32 DP0 TP0] Decode batch. #running-req: 1, #token: 2750, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.17, #queue-req: 0, 
[2025-07-28 01:11:32 DP1 TP0] Decode batch. #running-req: 1, #token: 5183, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.72, #queue-req: 0, 
[2025-07-28 01:11:32 DP0 TP0] Decode batch. #running-req: 1, #token: 2790, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.10, #queue-req: 0, 
[2025-07-28 01:11:32 DP1 TP0] Decode batch. #running-req: 1, #token: 5223, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.82, #queue-req: 0, 
[2025-07-28 01:11:32 DP0 TP0] Decode batch. #running-req: 1, #token: 2830, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.08, #queue-req: 0, 
[2025-07-28 01:11:32 DP1 TP0] Decode batch. #running-req: 1, #token: 5263, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.81, #queue-req: 0, 
[2025-07-28 01:11:32 DP0 TP0] Decode batch. #running-req: 1, #token: 2870, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.04, #queue-req: 0, 
[2025-07-28 01:11:33 DP1 TP0] Decode batch. #running-req: 1, #token: 5303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.89, #queue-req: 0, 
[2025-07-28 01:11:33 DP0 TP0] Decode batch. #running-req: 1, #token: 2910, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.99, #queue-req: 0, 
[2025-07-28 01:11:33 DP1 TP0] Decode batch. #running-req: 1, #token: 5343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.91, #queue-req: 0, 
[2025-07-28 01:11:33 DP0 TP0] Decode batch. #running-req: 1, #token: 2950, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.96, #queue-req: 0, 
[2025-07-28 01:11:33 DP1 TP0] Decode batch. #running-req: 1, #token: 5383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.80, #queue-req: 0, 
[2025-07-28 01:11:33 DP0 TP0] Decode batch. #running-req: 1, #token: 2990, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.05, #queue-req: 0, 
[2025-07-28 01:11:33 DP1 TP0] Decode batch. #running-req: 1, #token: 5423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.72, #queue-req: 0, 
[2025-07-28 01:11:33 DP0 TP0] Decode batch. #running-req: 1, #token: 3030, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.18, #queue-req: 0, 
[2025-07-28 01:11:33 DP1 TP0] Decode batch. #running-req: 1, #token: 5463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.80, #queue-req: 0, 
[2025-07-28 01:11:33 DP0 TP0] Decode batch. #running-req: 1, #token: 3070, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.08, #queue-req: 0, 
[2025-07-28 01:11:34] INFO:     127.0.0.1:47490 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:11:34 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:11:34 DP0 TP0] Decode batch. #running-req: 1, #token: 3110, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.85, #queue-req: 0, 
[2025-07-28 01:11:34 DP1 TP0] Decode batch. #running-req: 1, #token: 245, token usage: 0.00, cuda graph: True, gen throughput (token/s): 153.99, #queue-req: 0, 
[2025-07-28 01:11:34 DP1 TP0] Decode batch. #running-req: 1, #token: 285, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.13, #queue-req: 0, 
[2025-07-28 01:11:34 DP0 TP0] Decode batch. #running-req: 1, #token: 3150, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.77, #queue-req: 0, 
[2025-07-28 01:11:34 DP1 TP0] Decode batch. #running-req: 1, #token: 325, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.03, #queue-req: 0, 
[2025-07-28 01:11:34 DP0 TP0] Decode batch. #running-req: 1, #token: 3190, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.76, #queue-req: 0, 
[2025-07-28 01:11:34 DP1 TP0] Decode batch. #running-req: 1, #token: 365, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.60, #queue-req: 0, 
[2025-07-28 01:11:34 DP0 TP0] Decode batch. #running-req: 1, #token: 3230, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.91, #queue-req: 0, 
[2025-07-28 01:11:34 DP1 TP0] Decode batch. #running-req: 1, #token: 405, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.09, #queue-req: 0, 
[2025-07-28 01:11:34 DP0 TP0] Decode batch. #running-req: 1, #token: 3270, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.87, #queue-req: 0, 
[2025-07-28 01:11:35 DP1 TP0] Decode batch. #running-req: 1, #token: 445, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.09, #queue-req: 0, 
[2025-07-28 01:11:35 DP0 TP0] Decode batch. #running-req: 1, #token: 3310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.86, #queue-req: 0, 
[2025-07-28 01:11:35 DP1 TP0] Decode batch. #running-req: 1, #token: 485, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.03, #queue-req: 0, 
[2025-07-28 01:11:35 DP0 TP0] Decode batch. #running-req: 1, #token: 3350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.77, #queue-req: 0, 
[2025-07-28 01:11:35 DP1 TP0] Decode batch. #running-req: 1, #token: 525, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.01, #queue-req: 0, 
[2025-07-28 01:11:35 DP0 TP0] Decode batch. #running-req: 1, #token: 3390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.81, #queue-req: 0, 
[2025-07-28 01:11:35 DP1 TP0] Decode batch. #running-req: 1, #token: 565, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.58, #queue-req: 0, 
[2025-07-28 01:11:35 DP0 TP0] Decode batch. #running-req: 1, #token: 3430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.74, #queue-req: 0, 
[2025-07-28 01:11:35 DP1 TP0] Decode batch. #running-req: 1, #token: 605, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.64, #queue-req: 0, 
[2025-07-28 01:11:35 DP0 TP0] Decode batch. #running-req: 1, #token: 3470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.76, #queue-req: 0, 
[2025-07-28 01:11:36 DP1 TP0] Decode batch. #running-req: 1, #token: 645, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.71, #queue-req: 0, 
[2025-07-28 01:11:36 DP0 TP0] Decode batch. #running-req: 1, #token: 3510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.83, #queue-req: 0, 
[2025-07-28 01:11:36 DP1 TP0] Decode batch. #running-req: 1, #token: 685, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.78, #queue-req: 0, 
[2025-07-28 01:11:36 DP0 TP0] Decode batch. #running-req: 1, #token: 3550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.79, #queue-req: 0, 
[2025-07-28 01:11:36 DP1 TP0] Decode batch. #running-req: 1, #token: 725, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.77, #queue-req: 0, 
[2025-07-28 01:11:36 DP0 TP0] Decode batch. #running-req: 1, #token: 3590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.85, #queue-req: 0, 
[2025-07-28 01:11:36 DP1 TP0] Decode batch. #running-req: 1, #token: 765, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.78, #queue-req: 0, 
[2025-07-28 01:11:36 DP0 TP0] Decode batch. #running-req: 1, #token: 3630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.63, #queue-req: 0, 
[2025-07-28 01:11:36 DP1 TP0] Decode batch. #running-req: 1, #token: 805, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.60, #queue-req: 0, 
[2025-07-28 01:11:36 DP0 TP0] Decode batch. #running-req: 1, #token: 3670, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.66, #queue-req: 0, 
[2025-07-28 01:11:37 DP1 TP0] Decode batch. #running-req: 1, #token: 845, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.52, #queue-req: 0, 
[2025-07-28 01:11:37 DP0 TP0] Decode batch. #running-req: 1, #token: 3710, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.64, #queue-req: 0, 
[2025-07-28 01:11:37 DP1 TP0] Decode batch. #running-req: 1, #token: 885, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.55, #queue-req: 0, 
[2025-07-28 01:11:37 DP0 TP0] Decode batch. #running-req: 1, #token: 3750, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.70, #queue-req: 0, 
[2025-07-28 01:11:37] INFO:     127.0.0.1:37148 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:11:37 DP1 TP0] Decode batch. #running-req: 1, #token: 925, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.72, #queue-req: 0, 
[2025-07-28 01:11:37 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:11:37 DP0 TP0] Decode batch. #running-req: 1, #token: 236, token usage: 0.00, cuda graph: True, gen throughput (token/s): 161.05, #queue-req: 0, 
[2025-07-28 01:11:37 DP1 TP0] Decode batch. #running-req: 1, #token: 965, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.70, #queue-req: 0, 
[2025-07-28 01:11:37 DP0 TP0] Decode batch. #running-req: 1, #token: 276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.77, #queue-req: 0, 
[2025-07-28 01:11:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1005, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.73, #queue-req: 0, 
[2025-07-28 01:11:38 DP0 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.23, #queue-req: 0, 
[2025-07-28 01:11:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1045, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.40, #queue-req: 0, 
[2025-07-28 01:11:38 DP0 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.25, #queue-req: 0, 
[2025-07-28 01:11:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1085, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.00, #queue-req: 0, 
[2025-07-28 01:11:38 DP0 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.22, #queue-req: 0, 
[2025-07-28 01:11:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1125, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.04, #queue-req: 0, 
[2025-07-28 01:11:38 DP0 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.07, #queue-req: 0, 
[2025-07-28 01:11:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1165, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.03, #queue-req: 0, 
[2025-07-28 01:11:38 DP0 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.98, #queue-req: 0, 
[2025-07-28 01:11:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1205, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.17, #queue-req: 0, 
[2025-07-28 01:11:39 DP0 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.90, #queue-req: 0, 
[2025-07-28 01:11:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1245, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.21, #queue-req: 0, 
[2025-07-28 01:11:39 DP0 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 191.25, #queue-req: 0, 
[2025-07-28 01:11:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1285, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.86, #queue-req: 0, 
[2025-07-28 01:11:39 DP0 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.00, #queue-req: 0, 
[2025-07-28 01:11:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1325, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.03, #queue-req: 0, 
[2025-07-28 01:11:39 DP0 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.66, #queue-req: 0, 
[2025-07-28 01:11:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1365, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.87, #queue-req: 0, 
[2025-07-28 01:11:39 DP0 TP0] Decode batch. #running-req: 1, #token: 676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.66, #queue-req: 0, 
[2025-07-28 01:11:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1405, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.06, #queue-req: 0, 
[2025-07-28 01:11:40 DP0 TP0] Decode batch. #running-req: 1, #token: 716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.59, #queue-req: 0, 
[2025-07-28 01:11:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1445, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.26, #queue-req: 0, 
[2025-07-28 01:11:40 DP0 TP0] Decode batch. #running-req: 1, #token: 756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.55, #queue-req: 0, 
[2025-07-28 01:11:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1485, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.09, #queue-req: 0, 
[2025-07-28 01:11:40 DP0 TP0] Decode batch. #running-req: 1, #token: 796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.50, #queue-req: 0, 
[2025-07-28 01:11:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1525, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.10, #queue-req: 0, 
[2025-07-28 01:11:40 DP0 TP0] Decode batch. #running-req: 1, #token: 836, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.28, #queue-req: 0, 
[2025-07-28 01:11:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1565, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.02, #queue-req: 0, 
[2025-07-28 01:11:40 DP0 TP0] Decode batch. #running-req: 1, #token: 876, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.29, #queue-req: 0, 
[2025-07-28 01:11:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1605, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.94, #queue-req: 0, 
[2025-07-28 01:11:41 DP0 TP0] Decode batch. #running-req: 1, #token: 916, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.31, #queue-req: 0, 
[2025-07-28 01:11:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1645, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.88, #queue-req: 0, 
[2025-07-28 01:11:41 DP0 TP0] Decode batch. #running-req: 1, #token: 956, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.39, #queue-req: 0, 
[2025-07-28 01:11:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1685, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.08, #queue-req: 0, 
[2025-07-28 01:11:41 DP0 TP0] Decode batch. #running-req: 1, #token: 996, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.44, #queue-req: 0, 
[2025-07-28 01:11:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1725, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.09, #queue-req: 0, 
[2025-07-28 01:11:41 DP0 TP0] Decode batch. #running-req: 1, #token: 1036, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.24, #queue-req: 0, 
[2025-07-28 01:11:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1765, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.03, #queue-req: 0, 
[2025-07-28 01:11:41 DP0 TP0] Decode batch. #running-req: 1, #token: 1076, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.64, #queue-req: 0, 
[2025-07-28 01:11:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1805, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.98, #queue-req: 0, 
[2025-07-28 01:11:42 DP0 TP0] Decode batch. #running-req: 1, #token: 1116, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.75, #queue-req: 0, 
[2025-07-28 01:11:42 DP1 TP0] Decode batch. #running-req: 1, #token: 1845, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.90, #queue-req: 0, 
[2025-07-28 01:11:42 DP0 TP0] Decode batch. #running-req: 1, #token: 1156, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.62, #queue-req: 0, 
[2025-07-28 01:11:42 DP1 TP0] Decode batch. #running-req: 1, #token: 1885, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.87, #queue-req: 0, 
[2025-07-28 01:11:42 DP0 TP0] Decode batch. #running-req: 1, #token: 1196, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.75, #queue-req: 0, 
[2025-07-28 01:11:42 DP1 TP0] Decode batch. #running-req: 1, #token: 1925, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.91, #queue-req: 0, 
[2025-07-28 01:11:42 DP0 TP0] Decode batch. #running-req: 1, #token: 1236, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.85, #queue-req: 0, 
[2025-07-28 01:11:42 DP1 TP0] Decode batch. #running-req: 1, #token: 1965, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.08, #queue-req: 0, 
[2025-07-28 01:11:42 DP0 TP0] Decode batch. #running-req: 1, #token: 1276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.76, #queue-req: 0, 
[2025-07-28 01:11:42 DP1 TP0] Decode batch. #running-req: 1, #token: 2005, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.07, #queue-req: 0, 
[2025-07-28 01:11:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.63, #queue-req: 0, 
[2025-07-28 01:11:43 DP1 TP0] Decode batch. #running-req: 1, #token: 2045, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.97, #queue-req: 0, 
[2025-07-28 01:11:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.63, #queue-req: 0, 
[2025-07-28 01:11:43 DP1 TP0] Decode batch. #running-req: 1, #token: 2085, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.60, #queue-req: 0, 
[2025-07-28 01:11:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.61, #queue-req: 0, 
[2025-07-28 01:11:43 DP1 TP0] Decode batch. #running-req: 1, #token: 2125, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.52, #queue-req: 0, 
[2025-07-28 01:11:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.69, #queue-req: 0, 
[2025-07-28 01:11:43 DP1 TP0] Decode batch. #running-req: 1, #token: 2165, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.52, #queue-req: 0, 
[2025-07-28 01:11:43] INFO:     127.0.0.1:39966 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:11:43 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 95, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:11:43 DP1 TP0] Decode batch. #running-req: 2, #token: 2325, token usage: 0.00, cuda graph: True, gen throughput (token/s): 262.57, #queue-req: 0, 
[2025-07-28 01:11:44 DP1 TP0] Decode batch. #running-req: 2, #token: 2405, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.64, #queue-req: 0, 
[2025-07-28 01:11:44 DP1 TP0] Decode batch. #running-req: 2, #token: 2485, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.38, #queue-req: 0, 
[2025-07-28 01:11:44 DP1 TP0] Decode batch. #running-req: 2, #token: 2565, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.99, #queue-req: 0, 
[2025-07-28 01:11:44 DP1 TP0] Decode batch. #running-req: 2, #token: 2645, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.11, #queue-req: 0, 
[2025-07-28 01:11:45 DP1 TP0] Decode batch. #running-req: 2, #token: 2725, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.16, #queue-req: 0, 
[2025-07-28 01:11:45 DP1 TP0] Decode batch. #running-req: 2, #token: 2805, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.92, #queue-req: 0, 
[2025-07-28 01:11:45 DP1 TP0] Decode batch. #running-req: 2, #token: 2885, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.94, #queue-req: 0, 
[2025-07-28 01:11:45 DP1 TP0] Decode batch. #running-req: 2, #token: 2965, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.00, #queue-req: 0, 
[2025-07-28 01:11:45 DP1 TP0] Decode batch. #running-req: 2, #token: 3045, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.96, #queue-req: 0, 
[2025-07-28 01:11:46 DP1 TP0] Decode batch. #running-req: 2, #token: 3125, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.80, #queue-req: 0, 
[2025-07-28 01:11:46 DP1 TP0] Decode batch. #running-req: 2, #token: 3205, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.75, #queue-req: 0, 
[2025-07-28 01:11:46 DP1 TP0] Decode batch. #running-req: 2, #token: 3285, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.94, #queue-req: 0, 
[2025-07-28 01:11:46 DP1 TP0] Decode batch. #running-req: 2, #token: 3365, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.81, #queue-req: 0, 
[2025-07-28 01:11:46 DP1 TP0] Decode batch. #running-req: 2, #token: 3445, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.63, #queue-req: 0, 
[2025-07-28 01:11:47 DP1 TP0] Decode batch. #running-req: 2, #token: 3525, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.66, #queue-req: 0, 
[2025-07-28 01:11:47 DP1 TP0] Decode batch. #running-req: 2, #token: 3605, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.74, #queue-req: 0, 
[2025-07-28 01:11:47 DP1 TP0] Decode batch. #running-req: 2, #token: 3685, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.50, #queue-req: 0, 
[2025-07-28 01:11:47] INFO:     127.0.0.1:39958 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:11:47 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:11:47 DP0 TP0] Decode batch. #running-req: 1, #token: 228, token usage: 0.00, cuda graph: True, gen throughput (token/s): 9.82, #queue-req: 0, 
[2025-07-28 01:11:47 DP1 TP0] Decode batch. #running-req: 1, #token: 925, token usage: 0.00, cuda graph: True, gen throughput (token/s): 280.74, #queue-req: 0, 
[2025-07-28 01:11:47 DP1 TP0] Decode batch. #running-req: 1, #token: 965, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.28, #queue-req: 0, 
[2025-07-28 01:11:47 DP0 TP0] Decode batch. #running-req: 1, #token: 268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 181.48, #queue-req: 0, 
[2025-07-28 01:11:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1005, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.80, #queue-req: 0, 
[2025-07-28 01:11:48 DP0 TP0] Decode batch. #running-req: 1, #token: 308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.99, #queue-req: 0, 
[2025-07-28 01:11:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1045, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.35, #queue-req: 0, 
[2025-07-28 01:11:48 DP0 TP0] Decode batch. #running-req: 1, #token: 348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.67, #queue-req: 0, 
[2025-07-28 01:11:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1085, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.06, #queue-req: 0, 
[2025-07-28 01:11:48 DP0 TP0] Decode batch. #running-req: 1, #token: 388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.73, #queue-req: 0, 
[2025-07-28 01:11:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1125, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.06, #queue-req: 0, 
[2025-07-28 01:11:48 DP0 TP0] Decode batch. #running-req: 1, #token: 428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.97, #queue-req: 0, 
[2025-07-28 01:11:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1165, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.12, #queue-req: 0, 
[2025-07-28 01:11:48 DP0 TP0] Decode batch. #running-req: 1, #token: 468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.66, #queue-req: 0, 
[2025-07-28 01:11:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1205, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.66, #queue-req: 0, 
[2025-07-28 01:11:49 DP0 TP0] Decode batch. #running-req: 1, #token: 508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.49, #queue-req: 0, 
[2025-07-28 01:11:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1245, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.61, #queue-req: 0, 
[2025-07-28 01:11:49 DP0 TP0] Decode batch. #running-req: 1, #token: 548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.23, #queue-req: 0, 
[2025-07-28 01:11:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1285, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.44, #queue-req: 0, 
[2025-07-28 01:11:49 DP0 TP0] Decode batch. #running-req: 1, #token: 588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 189.41, #queue-req: 0, 
[2025-07-28 01:11:49] INFO:     127.0.0.1:37690 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:11:49 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 78, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:11:49 DP0 TP0] Decode batch. #running-req: 1, #token: 628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.42, #queue-req: 0, 
[2025-07-28 01:11:49 DP1 TP0] Decode batch. #running-req: 1, #token: 172, token usage: 0.00, cuda graph: True, gen throughput (token/s): 160.31, #queue-req: 0, 
[2025-07-28 01:11:49 DP0 TP0] Decode batch. #running-req: 1, #token: 668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.04, #queue-req: 0, 
[2025-07-28 01:11:49 DP1 TP0] Decode batch. #running-req: 1, #token: 212, token usage: 0.00, cuda graph: True, gen throughput (token/s): 205.18, #queue-req: 0, 
[2025-07-28 01:11:50 DP0 TP0] Decode batch. #running-req: 1, #token: 708, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.49, #queue-req: 0, 
[2025-07-28 01:11:50 DP1 TP0] Decode batch. #running-req: 1, #token: 252, token usage: 0.00, cuda graph: True, gen throughput (token/s): 203.14, #queue-req: 0, 
[2025-07-28 01:11:50 DP0 TP0] Decode batch. #running-req: 1, #token: 748, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.66, #queue-req: 0, 
[2025-07-28 01:11:50 DP1 TP0] Decode batch. #running-req: 1, #token: 292, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.05, #queue-req: 0, 
[2025-07-28 01:11:50 DP0 TP0] Decode batch. #running-req: 1, #token: 788, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.56, #queue-req: 0, 
[2025-07-28 01:11:50 DP1 TP0] Decode batch. #running-req: 1, #token: 332, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.97, #queue-req: 0, 
[2025-07-28 01:11:50 DP0 TP0] Decode batch. #running-req: 1, #token: 828, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.46, #queue-req: 0, 
[2025-07-28 01:11:50 DP1 TP0] Decode batch. #running-req: 1, #token: 372, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.05, #queue-req: 0, 
[2025-07-28 01:11:50 DP0 TP0] Decode batch. #running-req: 1, #token: 868, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.28, #queue-req: 0, 
[2025-07-28 01:11:50 DP1 TP0] Decode batch. #running-req: 1, #token: 412, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.01, #queue-req: 0, 
[2025-07-28 01:11:51 DP0 TP0] Decode batch. #running-req: 1, #token: 908, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.42, #queue-req: 0, 
[2025-07-28 01:11:51 DP1 TP0] Decode batch. #running-req: 1, #token: 452, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.16, #queue-req: 0, 
[2025-07-28 01:11:51 DP0 TP0] Decode batch. #running-req: 1, #token: 948, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.48, #queue-req: 0, 
[2025-07-28 01:11:51 DP1 TP0] Decode batch. #running-req: 1, #token: 492, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.12, #queue-req: 0, 
[2025-07-28 01:11:51 DP0 TP0] Decode batch. #running-req: 1, #token: 988, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.45, #queue-req: 0, 
[2025-07-28 01:11:51 DP1 TP0] Decode batch. #running-req: 1, #token: 532, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.88, #queue-req: 0, 
[2025-07-28 01:11:51 DP0 TP0] Decode batch. #running-req: 1, #token: 1028, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.34, #queue-req: 0, 
[2025-07-28 01:11:51 DP1 TP0] Decode batch. #running-req: 1, #token: 572, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.67, #queue-req: 0, 
[2025-07-28 01:11:51 DP0 TP0] Decode batch. #running-req: 1, #token: 1068, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.67, #queue-req: 0, 
[2025-07-28 01:11:51 DP1 TP0] Decode batch. #running-req: 1, #token: 612, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.67, #queue-req: 0, 
[2025-07-28 01:11:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1108, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.90, #queue-req: 0, 
[2025-07-28 01:11:52 DP1 TP0] Decode batch. #running-req: 1, #token: 652, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.85, #queue-req: 0, 
[2025-07-28 01:11:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1148, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.80, #queue-req: 0, 
[2025-07-28 01:11:52 DP1 TP0] Decode batch. #running-req: 1, #token: 692, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.94, #queue-req: 0, 
[2025-07-28 01:11:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1188, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.83, #queue-req: 0, 
[2025-07-28 01:11:52 DP1 TP0] Decode batch. #running-req: 1, #token: 732, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.75, #queue-req: 0, 
[2025-07-28 01:11:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1228, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.80, #queue-req: 0, 
[2025-07-28 01:11:52 DP1 TP0] Decode batch. #running-req: 1, #token: 772, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.83, #queue-req: 0, 
[2025-07-28 01:11:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.70, #queue-req: 0, 
[2025-07-28 01:11:52 DP1 TP0] Decode batch. #running-req: 1, #token: 812, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.55, #queue-req: 0, 
[2025-07-28 01:11:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.65, #queue-req: 0, 
[2025-07-28 01:11:53 DP1 TP0] Decode batch. #running-req: 1, #token: 852, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.58, #queue-req: 0, 
[2025-07-28 01:11:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.58, #queue-req: 0, 
[2025-07-28 01:11:53 DP1 TP0] Decode batch. #running-req: 1, #token: 892, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.57, #queue-req: 0, 
[2025-07-28 01:11:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.72, #queue-req: 0, 
[2025-07-28 01:11:53 DP1 TP0] Decode batch. #running-req: 1, #token: 932, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.71, #queue-req: 0, 
[2025-07-28 01:11:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.77, #queue-req: 0, 
[2025-07-28 01:11:53 DP1 TP0] Decode batch. #running-req: 1, #token: 972, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.66, #queue-req: 0, 
[2025-07-28 01:11:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.77, #queue-req: 0, 
[2025-07-28 01:11:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1012, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.75, #queue-req: 0, 
[2025-07-28 01:11:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.71, #queue-req: 0, 
[2025-07-28 01:11:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1052, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.33, #queue-req: 0, 
[2025-07-28 01:11:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.78, #queue-req: 0, 
[2025-07-28 01:11:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1092, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.14, #queue-req: 0, 
[2025-07-28 01:11:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.59, #queue-req: 0, 
[2025-07-28 01:11:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1132, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.09, #queue-req: 0, 
[2025-07-28 01:11:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.66, #queue-req: 0, 
[2025-07-28 01:11:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1172, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.84, #queue-req: 0, 
[2025-07-28 01:11:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.53, #queue-req: 0, 
[2025-07-28 01:11:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1212, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.21, #queue-req: 0, 
[2025-07-28 01:11:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1708, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.66, #queue-req: 0, 
[2025-07-28 01:11:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1252, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.10, #queue-req: 0, 
[2025-07-28 01:11:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1748, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.76, #queue-req: 0, 
[2025-07-28 01:11:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1292, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.24, #queue-req: 0, 
[2025-07-28 01:11:55] INFO:     127.0.0.1:37720 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:11:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1788, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.68, #queue-req: 0, 
[2025-07-28 01:11:55 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 146, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:11:55 DP0 TP0] Decode batch. #running-req: 2, #token: 2016, token usage: 0.00, cuda graph: True, gen throughput (token/s): 331.86, #queue-req: 0, 
[2025-07-28 01:11:55 DP0 TP0] Decode batch. #running-req: 2, #token: 2096, token usage: 0.00, cuda graph: True, gen throughput (token/s): 386.01, #queue-req: 0, 
[2025-07-28 01:11:56 DP0 TP0] Decode batch. #running-req: 2, #token: 2176, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.95, #queue-req: 0, 
[2025-07-28 01:11:56 DP0 TP0] Decode batch. #running-req: 2, #token: 2256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.99, #queue-req: 0, 
[2025-07-28 01:11:56 DP0 TP0] Decode batch. #running-req: 2, #token: 2336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.68, #queue-req: 0, 
[2025-07-28 01:11:56 DP0 TP0] Decode batch. #running-req: 2, #token: 2416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 386.17, #queue-req: 0, 
[2025-07-28 01:11:57 DP0 TP0] Decode batch. #running-req: 2, #token: 2496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.39, #queue-req: 0, 
[2025-07-28 01:11:57 DP0 TP0] Decode batch. #running-req: 2, #token: 2576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.04, #queue-req: 0, 
[2025-07-28 01:11:57 DP0 TP0] Decode batch. #running-req: 2, #token: 2656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.19, #queue-req: 0, 
[2025-07-28 01:11:57 DP0 TP0] Decode batch. #running-req: 2, #token: 2736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.68, #queue-req: 0, 
[2025-07-28 01:11:57 DP0 TP0] Decode batch. #running-req: 2, #token: 2816, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.14, #queue-req: 0, 
[2025-07-28 01:11:58 DP0 TP0] Decode batch. #running-req: 2, #token: 2896, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.67, #queue-req: 0, 
[2025-07-28 01:11:58 DP0 TP0] Decode batch. #running-req: 2, #token: 2976, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.95, #queue-req: 0, 
[2025-07-28 01:11:58 DP0 TP0] Decode batch. #running-req: 2, #token: 3056, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.57, #queue-req: 0, 
[2025-07-28 01:11:58 DP0 TP0] Decode batch. #running-req: 2, #token: 3136, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.58, #queue-req: 0, 
[2025-07-28 01:11:58 DP0 TP0] Decode batch. #running-req: 2, #token: 3216, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.00, #queue-req: 0, 
[2025-07-28 01:11:59 DP0 TP0] Decode batch. #running-req: 2, #token: 3296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.73, #queue-req: 0, 
[2025-07-28 01:11:59 DP0 TP0] Decode batch. #running-req: 2, #token: 3376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.74, #queue-req: 0, 
[2025-07-28 01:11:59 DP0 TP0] Decode batch. #running-req: 2, #token: 3456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.67, #queue-req: 0, 
[2025-07-28 01:11:59 DP0 TP0] Decode batch. #running-req: 2, #token: 3536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.91, #queue-req: 0, 
[2025-07-28 01:11:59 DP0 TP0] Decode batch. #running-req: 2, #token: 3616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.57, #queue-req: 0, 
[2025-07-28 01:12:00 DP0 TP0] Decode batch. #running-req: 2, #token: 3696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.70, #queue-req: 0, 
[2025-07-28 01:12:00 DP0 TP0] Decode batch. #running-req: 2, #token: 3776, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.28, #queue-req: 0, 
[2025-07-28 01:12:00 DP0 TP0] Decode batch. #running-req: 2, #token: 3856, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.41, #queue-req: 0, 
[2025-07-28 01:12:00 DP0 TP0] Decode batch. #running-req: 2, #token: 3936, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.92, #queue-req: 0, 
[2025-07-28 01:12:00 DP0 TP0] Decode batch. #running-req: 2, #token: 4016, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.47, #queue-req: 0, 
[2025-07-28 01:12:01 DP0 TP0] Decode batch. #running-req: 2, #token: 4096, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.11, #queue-req: 0, 
[2025-07-28 01:12:01 DP0 TP0] Decode batch. #running-req: 2, #token: 4176, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.20, #queue-req: 0, 
[2025-07-28 01:12:01 DP0 TP0] Decode batch. #running-req: 2, #token: 4256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.02, #queue-req: 0, 
[2025-07-28 01:12:01] INFO:     127.0.0.1:37704 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:12:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1433, token usage: 0.00, cuda graph: True, gen throughput (token/s): 365.77, #queue-req: 0, 
[2025-07-28 01:12:01 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:12:01 DP1 TP0] Decode batch. #running-req: 1, #token: 176, token usage: 0.00, cuda graph: True, gen throughput (token/s): 6.09, #queue-req: 0, 
[2025-07-28 01:12:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1473, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.00, #queue-req: 0, 
[2025-07-28 01:12:02 DP1 TP0] Decode batch. #running-req: 1, #token: 216, token usage: 0.00, cuda graph: True, gen throughput (token/s): 185.42, #queue-req: 0, 
[2025-07-28 01:12:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1513, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.63, #queue-req: 0, 
[2025-07-28 01:12:02 DP1 TP0] Decode batch. #running-req: 1, #token: 256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.79, #queue-req: 0, 
[2025-07-28 01:12:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1553, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.72, #queue-req: 0, 
[2025-07-28 01:12:02 DP1 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.43, #queue-req: 0, 
[2025-07-28 01:12:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1593, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.48, #queue-req: 0, 
[2025-07-28 01:12:02] INFO:     127.0.0.1:53392 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:12:02 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 173, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:12:02 DP1 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.67, #queue-req: 0, 
[2025-07-28 01:12:02 DP0 TP0] Decode batch. #running-req: 1, #token: 295, token usage: 0.00, cuda graph: True, gen throughput (token/s): 152.71, #queue-req: 0, 
[2025-07-28 01:12:02 DP1 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.88, #queue-req: 0, 
[2025-07-28 01:12:03 DP0 TP0] Decode batch. #running-req: 1, #token: 335, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.26, #queue-req: 0, 
[2025-07-28 01:12:03 DP1 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.18, #queue-req: 0, 
[2025-07-28 01:12:03 DP0 TP0] Decode batch. #running-req: 1, #token: 375, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.28, #queue-req: 0, 
[2025-07-28 01:12:03 DP1 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.12, #queue-req: 0, 
[2025-07-28 01:12:03 DP0 TP0] Decode batch. #running-req: 1, #token: 415, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.01, #queue-req: 0, 
[2025-07-28 01:12:03 DP1 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.17, #queue-req: 0, 
[2025-07-28 01:12:03 DP0 TP0] Decode batch. #running-req: 1, #token: 455, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.96, #queue-req: 0, 
[2025-07-28 01:12:03 DP1 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.86, #queue-req: 0, 
[2025-07-28 01:12:03 DP0 TP0] Decode batch. #running-req: 1, #token: 495, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.89, #queue-req: 0, 
[2025-07-28 01:12:03 DP1 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.73, #queue-req: 0, 
[2025-07-28 01:12:04 DP0 TP0] Decode batch. #running-req: 1, #token: 535, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.74, #queue-req: 0, 
[2025-07-28 01:12:04 DP1 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.71, #queue-req: 0, 
[2025-07-28 01:12:04 DP0 TP0] Decode batch. #running-req: 1, #token: 575, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.73, #queue-req: 0, 
[2025-07-28 01:12:04 DP1 TP0] Decode batch. #running-req: 1, #token: 656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.80, #queue-req: 0, 
[2025-07-28 01:12:04 DP0 TP0] Decode batch. #running-req: 1, #token: 615, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.73, #queue-req: 0, 
[2025-07-28 01:12:04 DP1 TP0] Decode batch. #running-req: 1, #token: 696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.64, #queue-req: 0, 
[2025-07-28 01:12:04 DP0 TP0] Decode batch. #running-req: 1, #token: 655, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.56, #queue-req: 0, 
[2025-07-28 01:12:04 DP1 TP0] Decode batch. #running-req: 1, #token: 736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.82, #queue-req: 0, 
[2025-07-28 01:12:04 DP0 TP0] Decode batch. #running-req: 1, #token: 695, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.54, #queue-req: 0, 
[2025-07-28 01:12:04 DP1 TP0] Decode batch. #running-req: 1, #token: 776, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.78, #queue-req: 0, 
[2025-07-28 01:12:05 DP0 TP0] Decode batch. #running-req: 1, #token: 735, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.59, #queue-req: 0, 
[2025-07-28 01:12:05 DP1 TP0] Decode batch. #running-req: 1, #token: 816, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.52, #queue-req: 0, 
[2025-07-28 01:12:05 DP0 TP0] Decode batch. #running-req: 1, #token: 775, token usage: 0.00, cuda graph: True, gen throughput (token/s): 193.38, #queue-req: 0, 
[2025-07-28 01:12:05 DP1 TP0] Decode batch. #running-req: 1, #token: 856, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.47, #queue-req: 0, 
[2025-07-28 01:12:05 DP0 TP0] Decode batch. #running-req: 1, #token: 815, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.73, #queue-req: 0, 
[2025-07-28 01:12:05 DP1 TP0] Decode batch. #running-req: 1, #token: 896, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.11, #queue-req: 0, 
[2025-07-28 01:12:05 DP0 TP0] Decode batch. #running-req: 1, #token: 855, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.24, #queue-req: 0, 
[2025-07-28 01:12:05 DP1 TP0] Decode batch. #running-req: 1, #token: 936, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.69, #queue-req: 0, 
[2025-07-28 01:12:05 DP0 TP0] Decode batch. #running-req: 1, #token: 895, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.40, #queue-req: 0, 
[2025-07-28 01:12:05 DP1 TP0] Decode batch. #running-req: 1, #token: 976, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.75, #queue-req: 0, 
[2025-07-28 01:12:06 DP0 TP0] Decode batch. #running-req: 1, #token: 935, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.44, #queue-req: 0, 
[2025-07-28 01:12:06 DP1 TP0] Decode batch. #running-req: 1, #token: 1016, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.72, #queue-req: 0, 
[2025-07-28 01:12:06 DP0 TP0] Decode batch. #running-req: 1, #token: 975, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.38, #queue-req: 0, 
[2025-07-28 01:12:06 DP1 TP0] Decode batch. #running-req: 1, #token: 1056, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.20, #queue-req: 0, 
[2025-07-28 01:12:06 DP0 TP0] Decode batch. #running-req: 1, #token: 1015, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.43, #queue-req: 0, 
[2025-07-28 01:12:06 DP1 TP0] Decode batch. #running-req: 1, #token: 1096, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.06, #queue-req: 0, 
[2025-07-28 01:12:06 DP0 TP0] Decode batch. #running-req: 1, #token: 1055, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.95, #queue-req: 0, 
[2025-07-28 01:12:06 DP1 TP0] Decode batch. #running-req: 1, #token: 1136, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.09, #queue-req: 0, 
[2025-07-28 01:12:06 DP0 TP0] Decode batch. #running-req: 1, #token: 1095, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.73, #queue-req: 0, 
[2025-07-28 01:12:06 DP1 TP0] Decode batch. #running-req: 1, #token: 1176, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.16, #queue-req: 0, 
[2025-07-28 01:12:07 DP0 TP0] Decode batch. #running-req: 1, #token: 1135, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.69, #queue-req: 0, 
[2025-07-28 01:12:07 DP1 TP0] Decode batch. #running-req: 1, #token: 1216, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.22, #queue-req: 0, 
[2025-07-28 01:12:07 DP0 TP0] Decode batch. #running-req: 1, #token: 1175, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.61, #queue-req: 0, 
[2025-07-28 01:12:07] INFO:     127.0.0.1:53406 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:12:07 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 178, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:12:07 DP1 TP0] Decode batch. #running-req: 1, #token: 267, token usage: 0.00, cuda graph: True, gen throughput (token/s): 158.72, #queue-req: 0, 
[2025-07-28 01:12:07] INFO:     127.0.0.1:59230 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:12:07 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 88, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:12:07 DP0 TP0] Decode batch. #running-req: 1, #token: 184, token usage: 0.00, cuda graph: True, gen throughput (token/s): 164.92, #queue-req: 0, 
[2025-07-28 01:12:07 DP1 TP0] Decode batch. #running-req: 1, #token: 307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.64, #queue-req: 0, 
[2025-07-28 01:12:07 DP0 TP0] Decode batch. #running-req: 1, #token: 224, token usage: 0.00, cuda graph: True, gen throughput (token/s): 205.04, #queue-req: 0, 
[2025-07-28 01:12:07 DP1 TP0] Decode batch. #running-req: 1, #token: 347, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.97, #queue-req: 0, 
[2025-07-28 01:12:07 DP0 TP0] Decode batch. #running-req: 1, #token: 264, token usage: 0.00, cuda graph: True, gen throughput (token/s): 202.40, #queue-req: 0, 
[2025-07-28 01:12:07 DP1 TP0] Decode batch. #running-req: 1, #token: 387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.00, #queue-req: 0, 
[2025-07-28 01:12:08 DP0 TP0] Decode batch. #running-req: 1, #token: 304, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.24, #queue-req: 0, 
[2025-07-28 01:12:08 DP1 TP0] Decode batch. #running-req: 1, #token: 427, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.15, #queue-req: 0, 
[2025-07-28 01:12:08 DP0 TP0] Decode batch. #running-req: 1, #token: 344, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.86, #queue-req: 0, 
[2025-07-28 01:12:08 DP1 TP0] Decode batch. #running-req: 1, #token: 467, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.06, #queue-req: 0, 
[2025-07-28 01:12:08 DP0 TP0] Decode batch. #running-req: 1, #token: 384, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.16, #queue-req: 0, 
[2025-07-28 01:12:08 DP1 TP0] Decode batch. #running-req: 1, #token: 507, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.12, #queue-req: 0, 
[2025-07-28 01:12:08 DP0 TP0] Decode batch. #running-req: 1, #token: 424, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.00, #queue-req: 0, 
[2025-07-28 01:12:08 DP1 TP0] Decode batch. #running-req: 1, #token: 547, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.80, #queue-req: 0, 
[2025-07-28 01:12:08 DP0 TP0] Decode batch. #running-req: 1, #token: 464, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.00, #queue-req: 0, 
[2025-07-28 01:12:08 DP1 TP0] Decode batch. #running-req: 1, #token: 587, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.54, #queue-req: 0, 
[2025-07-28 01:12:09 DP0 TP0] Decode batch. #running-req: 1, #token: 504, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.99, #queue-req: 0, 
[2025-07-28 01:12:09 DP1 TP0] Decode batch. #running-req: 1, #token: 627, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.72, #queue-req: 0, 
[2025-07-28 01:12:09 DP0 TP0] Decode batch. #running-req: 1, #token: 544, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.71, #queue-req: 0, 
[2025-07-28 01:12:09 DP1 TP0] Decode batch. #running-req: 1, #token: 667, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.21, #queue-req: 0, 
[2025-07-28 01:12:09 DP0 TP0] Decode batch. #running-req: 1, #token: 584, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.51, #queue-req: 0, 
[2025-07-28 01:12:09 DP1 TP0] Decode batch. #running-req: 1, #token: 707, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.83, #queue-req: 0, 
[2025-07-28 01:12:09 DP0 TP0] Decode batch. #running-req: 1, #token: 624, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.60, #queue-req: 0, 
[2025-07-28 01:12:09 DP1 TP0] Decode batch. #running-req: 1, #token: 747, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.86, #queue-req: 0, 
[2025-07-28 01:12:09 DP0 TP0] Decode batch. #running-req: 1, #token: 664, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.49, #queue-req: 0, 
[2025-07-28 01:12:09 DP1 TP0] Decode batch. #running-req: 1, #token: 787, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.78, #queue-req: 0, 
[2025-07-28 01:12:10 DP0 TP0] Decode batch. #running-req: 1, #token: 704, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.58, #queue-req: 0, 
[2025-07-28 01:12:10 DP1 TP0] Decode batch. #running-req: 1, #token: 827, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.56, #queue-req: 0, 
[2025-07-28 01:12:10 DP0 TP0] Decode batch. #running-req: 1, #token: 744, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.60, #queue-req: 0, 
[2025-07-28 01:12:10 DP1 TP0] Decode batch. #running-req: 1, #token: 867, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.60, #queue-req: 0, 
[2025-07-28 01:12:10 DP0 TP0] Decode batch. #running-req: 1, #token: 784, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.46, #queue-req: 0, 
[2025-07-28 01:12:10 DP1 TP0] Decode batch. #running-req: 1, #token: 907, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.23, #queue-req: 0, 
[2025-07-28 01:12:10 DP0 TP0] Decode batch. #running-req: 1, #token: 824, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.25, #queue-req: 0, 
[2025-07-28 01:12:10 DP1 TP0] Decode batch. #running-req: 1, #token: 947, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.70, #queue-req: 0, 
[2025-07-28 01:12:10 DP0 TP0] Decode batch. #running-req: 1, #token: 864, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.24, #queue-req: 0, 
[2025-07-28 01:12:10 DP1 TP0] Decode batch. #running-req: 1, #token: 987, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.72, #queue-req: 0, 
[2025-07-28 01:12:10] INFO:     127.0.0.1:59242 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:12:10 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 153, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:12:11 DP0 TP0] Decode batch. #running-req: 1, #token: 904, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.32, #queue-req: 0, 
[2025-07-28 01:12:11 DP1 TP0] Decode batch. #running-req: 1, #token: 279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 161.82, #queue-req: 0, 
[2025-07-28 01:12:11 DP0 TP0] Decode batch. #running-req: 1, #token: 944, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.35, #queue-req: 0, 
[2025-07-28 01:12:11 DP1 TP0] Decode batch. #running-req: 1, #token: 319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.00, #queue-req: 0, 
[2025-07-28 01:12:11 DP0 TP0] Decode batch. #running-req: 1, #token: 984, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.36, #queue-req: 0, 
[2025-07-28 01:12:11] INFO:     127.0.0.1:59256 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:12:11 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 123, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:12:11 DP1 TP0] Decode batch. #running-req: 1, #token: 359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.92, #queue-req: 0, 
[2025-07-28 01:12:11 DP0 TP0] Decode batch. #running-req: 1, #token: 236, token usage: 0.00, cuda graph: True, gen throughput (token/s): 165.74, #queue-req: 0, 
[2025-07-28 01:12:11 DP1 TP0] Decode batch. #running-req: 1, #token: 399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.09, #queue-req: 0, 
[2025-07-28 01:12:11 DP0 TP0] Decode batch. #running-req: 1, #token: 276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.83, #queue-req: 0, 
[2025-07-28 01:12:11 DP1 TP0] Decode batch. #running-req: 1, #token: 439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.13, #queue-req: 0, 
[2025-07-28 01:12:12 DP0 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.36, #queue-req: 0, 
[2025-07-28 01:12:12 DP1 TP0] Decode batch. #running-req: 1, #token: 479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.10, #queue-req: 0, 
[2025-07-28 01:12:12 DP0 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.17, #queue-req: 0, 
[2025-07-28 01:12:12 DP1 TP0] Decode batch. #running-req: 1, #token: 519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.05, #queue-req: 0, 
[2025-07-28 01:12:12 DP0 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.07, #queue-req: 0, 
[2025-07-28 01:12:12 DP1 TP0] Decode batch. #running-req: 1, #token: 559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.76, #queue-req: 0, 
[2025-07-28 01:12:12 DP0 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.99, #queue-req: 0, 
[2025-07-28 01:12:12 DP1 TP0] Decode batch. #running-req: 1, #token: 599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.64, #queue-req: 0, 
[2025-07-28 01:12:12 DP0 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.07, #queue-req: 0, 
[2025-07-28 01:12:12 DP1 TP0] Decode batch. #running-req: 1, #token: 639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.67, #queue-req: 0, 
[2025-07-28 01:12:13 DP0 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.04, #queue-req: 0, 
[2025-07-28 01:12:13 DP1 TP0] Decode batch. #running-req: 1, #token: 679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.81, #queue-req: 0, 
[2025-07-28 01:12:13 DP0 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.60, #queue-req: 0, 
[2025-07-28 01:12:13 DP1 TP0] Decode batch. #running-req: 1, #token: 719, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.87, #queue-req: 0, 
[2025-07-28 01:12:13 DP0 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.50, #queue-req: 0, 
[2025-07-28 01:12:13 DP1 TP0] Decode batch. #running-req: 1, #token: 759, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.84, #queue-req: 0, 
[2025-07-28 01:12:13 DP0 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.59, #queue-req: 0, 
[2025-07-28 01:12:13 DP1 TP0] Decode batch. #running-req: 1, #token: 799, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.66, #queue-req: 0, 
[2025-07-28 01:12:13 DP0 TP0] Decode batch. #running-req: 1, #token: 676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.52, #queue-req: 0, 
[2025-07-28 01:12:13 DP1 TP0] Decode batch. #running-req: 1, #token: 839, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.62, #queue-req: 0, 
[2025-07-28 01:12:14 DP0 TP0] Decode batch. #running-req: 1, #token: 716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.62, #queue-req: 0, 
[2025-07-28 01:12:14 DP1 TP0] Decode batch. #running-req: 1, #token: 879, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.52, #queue-req: 0, 
[2025-07-28 01:12:14 DP0 TP0] Decode batch. #running-req: 1, #token: 756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.60, #queue-req: 0, 
[2025-07-28 01:12:14 DP1 TP0] Decode batch. #running-req: 1, #token: 919, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.60, #queue-req: 0, 
[2025-07-28 01:12:14 DP0 TP0] Decode batch. #running-req: 1, #token: 796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.43, #queue-req: 0, 
[2025-07-28 01:12:14 DP1 TP0] Decode batch. #running-req: 1, #token: 959, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.76, #queue-req: 0, 
[2025-07-28 01:12:14 DP0 TP0] Decode batch. #running-req: 1, #token: 836, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.05, #queue-req: 0, 
[2025-07-28 01:12:14 DP1 TP0] Decode batch. #running-req: 1, #token: 999, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.68, #queue-req: 0, 
[2025-07-28 01:12:14 DP0 TP0] Decode batch. #running-req: 1, #token: 876, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.33, #queue-req: 0, 
[2025-07-28 01:12:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1039, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.46, #queue-req: 0, 
[2025-07-28 01:12:15 DP0 TP0] Decode batch. #running-req: 1, #token: 916, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.37, #queue-req: 0, 
[2025-07-28 01:12:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1079, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.92, #queue-req: 0, 
[2025-07-28 01:12:15 DP0 TP0] Decode batch. #running-req: 1, #token: 956, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.38, #queue-req: 0, 
[2025-07-28 01:12:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1119, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.02, #queue-req: 0, 
[2025-07-28 01:12:15 DP0 TP0] Decode batch. #running-req: 1, #token: 996, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.45, #queue-req: 0, 
[2025-07-28 01:12:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1159, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.03, #queue-req: 0, 
[2025-07-28 01:12:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1036, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.16, #queue-req: 0, 
[2025-07-28 01:12:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1199, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.75, #queue-req: 0, 
[2025-07-28 01:12:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1076, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.68, #queue-req: 0, 
[2025-07-28 01:12:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1239, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.19, #queue-req: 0, 
[2025-07-28 01:12:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1116, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.76, #queue-req: 0, 
[2025-07-28 01:12:16 DP1 TP0] Decode batch. #running-req: 1, #token: 1279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.10, #queue-req: 0, 
[2025-07-28 01:12:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1156, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.74, #queue-req: 0, 
[2025-07-28 01:12:16 DP1 TP0] Decode batch. #running-req: 1, #token: 1319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.03, #queue-req: 0, 
[2025-07-28 01:12:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1196, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.78, #queue-req: 0, 
[2025-07-28 01:12:16 DP1 TP0] Decode batch. #running-req: 1, #token: 1359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.90, #queue-req: 0, 
[2025-07-28 01:12:16] INFO:     127.0.0.1:59270 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:12:16 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 129, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:12:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1236, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.84, #queue-req: 0, 
[2025-07-28 01:12:16 DP1 TP0] Decode batch. #running-req: 1, #token: 254, token usage: 0.00, cuda graph: True, gen throughput (token/s): 162.59, #queue-req: 0, 
[2025-07-28 01:12:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.76, #queue-req: 0, 
[2025-07-28 01:12:17 DP1 TP0] Decode batch. #running-req: 1, #token: 294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.97, #queue-req: 0, 
[2025-07-28 01:12:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.68, #queue-req: 0, 
[2025-07-28 01:12:17 DP1 TP0] Decode batch. #running-req: 1, #token: 334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.00, #queue-req: 0, 
[2025-07-28 01:12:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.76, #queue-req: 0, 
[2025-07-28 01:12:17 DP1 TP0] Decode batch. #running-req: 1, #token: 374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.11, #queue-req: 0, 
[2025-07-28 01:12:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.64, #queue-req: 0, 
[2025-07-28 01:12:17 DP1 TP0] Decode batch. #running-req: 1, #token: 414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.04, #queue-req: 0, 
[2025-07-28 01:12:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.80, #queue-req: 0, 
[2025-07-28 01:12:17 DP1 TP0] Decode batch. #running-req: 1, #token: 454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.08, #queue-req: 0, 
[2025-07-28 01:12:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.75, #queue-req: 0, 
[2025-07-28 01:12:18 DP1 TP0] Decode batch. #running-req: 1, #token: 494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.20, #queue-req: 0, 
[2025-07-28 01:12:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.64, #queue-req: 0, 
[2025-07-28 01:12:18 DP1 TP0] Decode batch. #running-req: 1, #token: 534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.88, #queue-req: 0, 
[2025-07-28 01:12:18] INFO:     127.0.0.1:59282 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:12:18 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 91, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:12:18 DP0 TP0] Decode batch. #running-req: 1, #token: 233, token usage: 0.00, cuda graph: True, gen throughput (token/s): 162.75, #queue-req: 0, 
[2025-07-28 01:12:18 DP1 TP0] Decode batch. #running-req: 1, #token: 574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.68, #queue-req: 0, 
[2025-07-28 01:12:18 DP0 TP0] Decode batch. #running-req: 1, #token: 273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 202.01, #queue-req: 0, 
[2025-07-28 01:12:18 DP1 TP0] Decode batch. #running-req: 1, #token: 614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.60, #queue-req: 0, 
[2025-07-28 01:12:18 DP0 TP0] Decode batch. #running-req: 1, #token: 313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.27, #queue-req: 0, 
[2025-07-28 01:12:18 DP1 TP0] Decode batch. #running-req: 1, #token: 654, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.66, #queue-req: 0, 
[2025-07-28 01:12:18 DP0 TP0] Decode batch. #running-req: 1, #token: 353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.21, #queue-req: 0, 
[2025-07-28 01:12:19 DP1 TP0] Decode batch. #running-req: 1, #token: 694, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.94, #queue-req: 0, 
[2025-07-28 01:12:19 DP0 TP0] Decode batch. #running-req: 1, #token: 393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.12, #queue-req: 0, 
[2025-07-28 01:12:19 DP1 TP0] Decode batch. #running-req: 1, #token: 734, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.83, #queue-req: 0, 
[2025-07-28 01:12:19 DP0 TP0] Decode batch. #running-req: 1, #token: 433, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.99, #queue-req: 0, 
[2025-07-28 01:12:19 DP1 TP0] Decode batch. #running-req: 1, #token: 774, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.71, #queue-req: 0, 
[2025-07-28 01:12:19 DP0 TP0] Decode batch. #running-req: 1, #token: 473, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.75, #queue-req: 0, 
[2025-07-28 01:12:19 DP1 TP0] Decode batch. #running-req: 1, #token: 814, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.60, #queue-req: 0, 
[2025-07-28 01:12:19 DP0 TP0] Decode batch. #running-req: 1, #token: 513, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.66, #queue-req: 0, 
[2025-07-28 01:12:19 DP1 TP0] Decode batch. #running-req: 1, #token: 854, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.67, #queue-req: 0, 
[2025-07-28 01:12:19 DP0 TP0] Decode batch. #running-req: 1, #token: 553, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.48, #queue-req: 0, 
[2025-07-28 01:12:20 DP1 TP0] Decode batch. #running-req: 1, #token: 894, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.56, #queue-req: 0, 
[2025-07-28 01:12:20 DP0 TP0] Decode batch. #running-req: 1, #token: 593, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.39, #queue-req: 0, 
[2025-07-28 01:12:20 DP1 TP0] Decode batch. #running-req: 1, #token: 934, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.72, #queue-req: 0, 
[2025-07-28 01:12:20 DP0 TP0] Decode batch. #running-req: 1, #token: 633, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.50, #queue-req: 0, 
[2025-07-28 01:12:20 DP1 TP0] Decode batch. #running-req: 1, #token: 974, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.79, #queue-req: 0, 
[2025-07-28 01:12:20 DP0 TP0] Decode batch. #running-req: 1, #token: 673, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.44, #queue-req: 0, 
[2025-07-28 01:12:20 DP1 TP0] Decode batch. #running-req: 1, #token: 1014, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.73, #queue-req: 0, 
[2025-07-28 01:12:20 DP0 TP0] Decode batch. #running-req: 1, #token: 713, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.45, #queue-req: 0, 
[2025-07-28 01:12:20 DP1 TP0] Decode batch. #running-req: 1, #token: 1054, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.26, #queue-req: 0, 
[2025-07-28 01:12:20 DP0 TP0] Decode batch. #running-req: 1, #token: 753, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.56, #queue-req: 0, 
[2025-07-28 01:12:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1094, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.02, #queue-req: 0, 
[2025-07-28 01:12:21 DP0 TP0] Decode batch. #running-req: 1, #token: 793, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.37, #queue-req: 0, 
[2025-07-28 01:12:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1134, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.00, #queue-req: 0, 
[2025-07-28 01:12:21 DP0 TP0] Decode batch. #running-req: 1, #token: 833, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.10, #queue-req: 0, 
[2025-07-28 01:12:21] INFO:     127.0.0.1:42172 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:12:21 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 92, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:12:21 DP1 TP0] Decode batch. #running-req: 1, #token: 246, token usage: 0.00, cuda graph: True, gen throughput (token/s): 162.61, #queue-req: 0, 
[2025-07-28 01:12:21 DP0 TP0] Decode batch. #running-req: 1, #token: 873, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.28, #queue-req: 0, 
[2025-07-28 01:12:21 DP1 TP0] Decode batch. #running-req: 1, #token: 286, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.97, #queue-req: 0, 
[2025-07-28 01:12:21 DP0 TP0] Decode batch. #running-req: 1, #token: 913, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.98, #queue-req: 0, 
[2025-07-28 01:12:21 DP1 TP0] Decode batch. #running-req: 1, #token: 326, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.77, #queue-req: 0, 
[2025-07-28 01:12:21 DP0 TP0] Decode batch. #running-req: 1, #token: 953, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.45, #queue-req: 0, 
[2025-07-28 01:12:22 DP1 TP0] Decode batch. #running-req: 1, #token: 366, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.23, #queue-req: 0, 
[2025-07-28 01:12:22 DP0 TP0] Decode batch. #running-req: 1, #token: 993, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.30, #queue-req: 0, 
[2025-07-28 01:12:22 DP1 TP0] Decode batch. #running-req: 1, #token: 406, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.03, #queue-req: 0, 
[2025-07-28 01:12:22 DP0 TP0] Decode batch. #running-req: 1, #token: 1033, token usage: 0.00, cuda graph: True, gen throughput (token/s): 192.38, #queue-req: 0, 
[2025-07-28 01:12:22 DP1 TP0] Decode batch. #running-req: 1, #token: 446, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.22, #queue-req: 0, 
[2025-07-28 01:12:22 DP0 TP0] Decode batch. #running-req: 1, #token: 1073, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.06, #queue-req: 0, 
[2025-07-28 01:12:22 DP1 TP0] Decode batch. #running-req: 1, #token: 486, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.66, #queue-req: 0, 
[2025-07-28 01:12:22 DP0 TP0] Decode batch. #running-req: 1, #token: 1113, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.52, #queue-req: 0, 
[2025-07-28 01:12:22 DP1 TP0] Decode batch. #running-req: 1, #token: 526, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.99, #queue-req: 0, 
[2025-07-28 01:12:22 DP0 TP0] Decode batch. #running-req: 1, #token: 1153, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.58, #queue-req: 0, 
[2025-07-28 01:12:23 DP1 TP0] Decode batch. #running-req: 1, #token: 566, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.73, #queue-req: 0, 
[2025-07-28 01:12:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1193, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.80, #queue-req: 0, 
[2025-07-28 01:12:23 DP1 TP0] Decode batch. #running-req: 1, #token: 606, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.66, #queue-req: 0, 
[2025-07-28 01:12:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1233, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.72, #queue-req: 0, 
[2025-07-28 01:12:23 DP1 TP0] Decode batch. #running-req: 1, #token: 646, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.73, #queue-req: 0, 
[2025-07-28 01:12:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.72, #queue-req: 0, 
[2025-07-28 01:12:23 DP1 TP0] Decode batch. #running-req: 1, #token: 686, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.62, #queue-req: 0, 
[2025-07-28 01:12:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.73, #queue-req: 0, 
[2025-07-28 01:12:23 DP1 TP0] Decode batch. #running-req: 1, #token: 726, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.99, #queue-req: 0, 
[2025-07-28 01:12:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.69, #queue-req: 0, 
[2025-07-28 01:12:24 DP1 TP0] Decode batch. #running-req: 1, #token: 766, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.88, #queue-req: 0, 
[2025-07-28 01:12:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.68, #queue-req: 0, 
[2025-07-28 01:12:24 DP1 TP0] Decode batch. #running-req: 1, #token: 806, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.60, #queue-req: 0, 
[2025-07-28 01:12:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1433, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.73, #queue-req: 0, 
[2025-07-28 01:12:24 DP1 TP0] Decode batch. #running-req: 1, #token: 846, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.51, #queue-req: 0, 
[2025-07-28 01:12:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1473, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.65, #queue-req: 0, 
[2025-07-28 01:12:24 DP1 TP0] Decode batch. #running-req: 1, #token: 886, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.36, #queue-req: 0, 
[2025-07-28 01:12:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1513, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.77, #queue-req: 0, 
[2025-07-28 01:12:24 DP1 TP0] Decode batch. #running-req: 1, #token: 926, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.83, #queue-req: 0, 
[2025-07-28 01:12:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1553, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.69, #queue-req: 0, 
[2025-07-28 01:12:25 DP1 TP0] Decode batch. #running-req: 1, #token: 966, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.74, #queue-req: 0, 
[2025-07-28 01:12:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1593, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.67, #queue-req: 0, 
[2025-07-28 01:12:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1006, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.77, #queue-req: 0, 
[2025-07-28 01:12:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1633, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.48, #queue-req: 0, 
[2025-07-28 01:12:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1046, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.46, #queue-req: 0, 
[2025-07-28 01:12:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1673, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.53, #queue-req: 0, 
[2025-07-28 01:12:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1086, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.91, #queue-req: 0, 
[2025-07-28 01:12:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1713, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.62, #queue-req: 0, 
[2025-07-28 01:12:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1126, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.16, #queue-req: 0, 
[2025-07-28 01:12:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1753, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.65, #queue-req: 0, 
[2025-07-28 01:12:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1166, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.06, #queue-req: 0, 
[2025-07-28 01:12:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1793, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.71, #queue-req: 0, 
[2025-07-28 01:12:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1206, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.24, #queue-req: 0, 
[2025-07-28 01:12:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1833, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.51, #queue-req: 0, 
[2025-07-28 01:12:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1246, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.13, #queue-req: 0, 
[2025-07-28 01:12:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1873, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.46, #queue-req: 0, 
[2025-07-28 01:12:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1286, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.99, #queue-req: 0, 
[2025-07-28 01:12:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1913, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.62, #queue-req: 0, 
[2025-07-28 01:12:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1326, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.16, #queue-req: 0, 
[2025-07-28 01:12:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1953, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.53, #queue-req: 0, 
[2025-07-28 01:12:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1366, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.99, #queue-req: 0, 
[2025-07-28 01:12:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1993, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.59, #queue-req: 0, 
[2025-07-28 01:12:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1406, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.08, #queue-req: 0, 
[2025-07-28 01:12:27 DP0 TP0] Decode batch. #running-req: 1, #token: 2033, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.59, #queue-req: 0, 
[2025-07-28 01:12:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1446, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.09, #queue-req: 0, 
[2025-07-28 01:12:27 DP0 TP0] Decode batch. #running-req: 1, #token: 2073, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.28, #queue-req: 0, 
[2025-07-28 01:12:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1486, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.08, #queue-req: 0, 
[2025-07-28 01:12:27 DP0 TP0] Decode batch. #running-req: 1, #token: 2113, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.04, #queue-req: 0, 
[2025-07-28 01:12:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1526, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.32, #queue-req: 0, 
[2025-07-28 01:12:27 DP0 TP0] Decode batch. #running-req: 1, #token: 2153, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.03, #queue-req: 0, 
[2025-07-28 01:12:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1566, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.03, #queue-req: 0, 
[2025-07-28 01:12:28 DP0 TP0] Decode batch. #running-req: 1, #token: 2193, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.17, #queue-req: 0, 
[2025-07-28 01:12:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1606, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.99, #queue-req: 0, 
[2025-07-28 01:12:28 DP0 TP0] Decode batch. #running-req: 1, #token: 2233, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.17, #queue-req: 0, 
[2025-07-28 01:12:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1646, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.00, #queue-req: 0, 
[2025-07-28 01:12:28 DP0 TP0] Decode batch. #running-req: 1, #token: 2273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.11, #queue-req: 0, 
[2025-07-28 01:12:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1686, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.83, #queue-req: 0, 
[2025-07-28 01:12:28 DP0 TP0] Decode batch. #running-req: 1, #token: 2313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.13, #queue-req: 0, 
[2025-07-28 01:12:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1726, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.23, #queue-req: 0, 
[2025-07-28 01:12:28 DP0 TP0] Decode batch. #running-req: 1, #token: 2353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.00, #queue-req: 0, 
[2025-07-28 01:12:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1766, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.17, #queue-req: 0, 
[2025-07-28 01:12:29 DP0 TP0] Decode batch. #running-req: 1, #token: 2393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.05, #queue-req: 0, 
[2025-07-28 01:12:29] INFO:     127.0.0.1:42186 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:12:29 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 103, #cached-token: 91, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:12:29 DP0 TP0] Decode batch. #running-req: 2, #token: 2570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.52, #queue-req: 0, 
[2025-07-28 01:12:29 DP0 TP0] Decode batch. #running-req: 2, #token: 2650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.31, #queue-req: 0, 
[2025-07-28 01:12:29 DP0 TP0] Decode batch. #running-req: 2, #token: 2730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.24, #queue-req: 0, 
[2025-07-28 01:12:30 DP0 TP0] Decode batch. #running-req: 2, #token: 2810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.97, #queue-req: 0, 
[2025-07-28 01:12:30 DP0 TP0] Decode batch. #running-req: 2, #token: 2890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.99, #queue-req: 0, 
[2025-07-28 01:12:30 DP0 TP0] Decode batch. #running-req: 2, #token: 2970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 385.10, #queue-req: 0, 
[2025-07-28 01:12:30 DP0 TP0] Decode batch. #running-req: 2, #token: 3050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.98, #queue-req: 0, 
[2025-07-28 01:12:30 DP0 TP0] Decode batch. #running-req: 2, #token: 3130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.84, #queue-req: 0, 
[2025-07-28 01:12:31 DP0 TP0] Decode batch. #running-req: 2, #token: 3210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.97, #queue-req: 0, 
[2025-07-28 01:12:31 DP0 TP0] Decode batch. #running-req: 2, #token: 3290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.74, #queue-req: 0, 
[2025-07-28 01:12:31 DP0 TP0] Decode batch. #running-req: 2, #token: 3370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.71, #queue-req: 0, 
[2025-07-28 01:12:31 DP0 TP0] Decode batch. #running-req: 2, #token: 3450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.59, #queue-req: 0, 
[2025-07-28 01:12:31 DP0 TP0] Decode batch. #running-req: 2, #token: 3530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.56, #queue-req: 0, 
[2025-07-28 01:12:32 DP0 TP0] Decode batch. #running-req: 2, #token: 3610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.49, #queue-req: 0, 
[2025-07-28 01:12:32 DP0 TP0] Decode batch. #running-req: 2, #token: 3690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.43, #queue-req: 0, 
[2025-07-28 01:12:32 DP0 TP0] Decode batch. #running-req: 2, #token: 3770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.22, #queue-req: 0, 
[2025-07-28 01:12:32 DP0 TP0] Decode batch. #running-req: 2, #token: 3850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.49, #queue-req: 0, 
[2025-07-28 01:12:32 DP0 TP0] Decode batch. #running-req: 2, #token: 3930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.25, #queue-req: 0, 
[2025-07-28 01:12:33 DP0 TP0] Decode batch. #running-req: 2, #token: 4010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.92, #queue-req: 0, 
[2025-07-28 01:12:33 DP0 TP0] Decode batch. #running-req: 2, #token: 4090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.87, #queue-req: 0, 
[2025-07-28 01:12:33 DP0 TP0] Decode batch. #running-req: 2, #token: 4170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 384.04, #queue-req: 0, 
[2025-07-28 01:12:33 DP0 TP0] Decode batch. #running-req: 2, #token: 4250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.95, #queue-req: 0, 
[2025-07-28 01:12:34 DP0 TP0] Decode batch. #running-req: 2, #token: 4330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.77, #queue-req: 0, 
[2025-07-28 01:12:34 DP0 TP0] Decode batch. #running-req: 2, #token: 4410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.80, #queue-req: 0, 
[2025-07-28 01:12:34 DP0 TP0] Decode batch. #running-req: 2, #token: 4490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.26, #queue-req: 0, 
[2025-07-28 01:12:34 DP0 TP0] Decode batch. #running-req: 2, #token: 4570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.57, #queue-req: 0, 
[2025-07-28 01:12:34 DP0 TP0] Decode batch. #running-req: 2, #token: 4650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.55, #queue-req: 0, 
[2025-07-28 01:12:35 DP0 TP0] Decode batch. #running-req: 2, #token: 4730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.64, #queue-req: 0, 
[2025-07-28 01:12:35 DP0 TP0] Decode batch. #running-req: 2, #token: 4810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.60, #queue-req: 0, 
[2025-07-28 01:12:35 DP0 TP0] Decode batch. #running-req: 2, #token: 4890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.17, #queue-req: 0, 
[2025-07-28 01:12:35 DP0 TP0] Decode batch. #running-req: 2, #token: 4970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.20, #queue-req: 0, 
[2025-07-28 01:12:35 DP0 TP0] Decode batch. #running-req: 2, #token: 5050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 382.90, #queue-req: 0, 
[2025-07-28 01:12:36 DP0 TP0] Decode batch. #running-req: 2, #token: 5130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 382.75, #queue-req: 0, 
[2025-07-28 01:12:36 DP0 TP0] Decode batch. #running-req: 2, #token: 5210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 382.71, #queue-req: 0, 
[2025-07-28 01:12:36 DP0 TP0] Decode batch. #running-req: 2, #token: 5290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 382.64, #queue-req: 0, 
[2025-07-28 01:12:36 DP0 TP0] Decode batch. #running-req: 2, #token: 5370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 382.57, #queue-req: 0, 
[2025-07-28 01:12:36 DP0 TP0] Decode batch. #running-req: 2, #token: 5450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.37, #queue-req: 0, 
[2025-07-28 01:12:37 DP0 TP0] Decode batch. #running-req: 2, #token: 5530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.27, #queue-req: 0, 
[2025-07-28 01:12:37 DP0 TP0] Decode batch. #running-req: 2, #token: 5610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.30, #queue-req: 0, 
[2025-07-28 01:12:37 DP0 TP0] Decode batch. #running-req: 2, #token: 5690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 383.36, #queue-req: 0, 
[2025-07-28 01:12:37] INFO:     127.0.0.1:49520 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:12:37 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 89, #cached-token: 91, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:12:37 DP0 TP0] Decode batch. #running-req: 1, #token: 4033, token usage: 0.00, cuda graph: True, gen throughput (token/s): 278.72, #queue-req: 0, 
[2025-07-28 01:12:37 DP1 TP0] Decode batch. #running-req: 1, #token: 189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 4.57, #queue-req: 0, 
[2025-07-28 01:12:37 DP0 TP0] Decode batch. #running-req: 1, #token: 4073, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.63, #queue-req: 0, 
[2025-07-28 01:12:38 DP1 TP0] Decode batch. #running-req: 1, #token: 229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 187.41, #queue-req: 0, 
[2025-07-28 01:12:38 DP0 TP0] Decode batch. #running-req: 1, #token: 4113, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.38, #queue-req: 0, 
[2025-07-28 01:12:38 DP1 TP0] Decode batch. #running-req: 1, #token: 269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.11, #queue-req: 0, 
[2025-07-28 01:12:38 DP0 TP0] Decode batch. #running-req: 1, #token: 4153, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.14, #queue-req: 0, 
[2025-07-28 01:12:38 DP1 TP0] Decode batch. #running-req: 1, #token: 309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.61, #queue-req: 0, 
[2025-07-28 01:12:38 DP0 TP0] Decode batch. #running-req: 1, #token: 4193, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.07, #queue-req: 0, 
[2025-07-28 01:12:38 DP1 TP0] Decode batch. #running-req: 1, #token: 349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.62, #queue-req: 0, 
[2025-07-28 01:12:38 DP0 TP0] Decode batch. #running-req: 1, #token: 4233, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.09, #queue-req: 0, 
[2025-07-28 01:12:38 DP1 TP0] Decode batch. #running-req: 1, #token: 389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.76, #queue-req: 0, 
[2025-07-28 01:12:38 DP0 TP0] Decode batch. #running-req: 1, #token: 4273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.26, #queue-req: 0, 
[2025-07-28 01:12:39 DP1 TP0] Decode batch. #running-req: 1, #token: 429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.93, #queue-req: 0, 
[2025-07-28 01:12:39 DP0 TP0] Decode batch. #running-req: 1, #token: 4313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.19, #queue-req: 0, 
[2025-07-28 01:12:39 DP1 TP0] Decode batch. #running-req: 1, #token: 469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.09, #queue-req: 0, 
[2025-07-28 01:12:39 DP0 TP0] Decode batch. #running-req: 1, #token: 4353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.17, #queue-req: 0, 
[2025-07-28 01:12:39 DP1 TP0] Decode batch. #running-req: 1, #token: 509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.07, #queue-req: 0, 
[2025-07-28 01:12:39 DP0 TP0] Decode batch. #running-req: 1, #token: 4393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.07, #queue-req: 0, 
[2025-07-28 01:12:39 DP1 TP0] Decode batch. #running-req: 1, #token: 549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.63, #queue-req: 0, 
[2025-07-28 01:12:39 DP0 TP0] Decode batch. #running-req: 1, #token: 4433, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.00, #queue-req: 0, 
[2025-07-28 01:12:39 DP1 TP0] Decode batch. #running-req: 1, #token: 589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.68, #queue-req: 0, 
[2025-07-28 01:12:39 DP0 TP0] Decode batch. #running-req: 1, #token: 4473, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.01, #queue-req: 0, 
[2025-07-28 01:12:39 DP1 TP0] Decode batch. #running-req: 1, #token: 629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.65, #queue-req: 0, 
[2025-07-28 01:12:40 DP0 TP0] Decode batch. #running-req: 1, #token: 4513, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.12, #queue-req: 0, 
[2025-07-28 01:12:40 DP1 TP0] Decode batch. #running-req: 1, #token: 669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.76, #queue-req: 0, 
[2025-07-28 01:12:40 DP0 TP0] Decode batch. #running-req: 1, #token: 4553, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.96, #queue-req: 0, 
[2025-07-28 01:12:40 DP1 TP0] Decode batch. #running-req: 1, #token: 709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.85, #queue-req: 0, 
[2025-07-28 01:12:40 DP0 TP0] Decode batch. #running-req: 1, #token: 4593, token usage: 0.00, cuda graph: True, gen throughput (token/s): 187.57, #queue-req: 0, 
[2025-07-28 01:12:40 DP1 TP0] Decode batch. #running-req: 1, #token: 749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.88, #queue-req: 0, 
[2025-07-28 01:12:40 DP0 TP0] Decode batch. #running-req: 1, #token: 4633, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.10, #queue-req: 0, 
[2025-07-28 01:12:40 DP1 TP0] Decode batch. #running-req: 1, #token: 789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.71, #queue-req: 0, 
[2025-07-28 01:12:40 DP1 TP0] Decode batch. #running-req: 1, #token: 829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.45, #queue-req: 0, 
[2025-07-28 01:12:40 DP0 TP0] Decode batch. #running-req: 1, #token: 4673, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.99, #queue-req: 0, 
[2025-07-28 01:12:41 DP1 TP0] Decode batch. #running-req: 1, #token: 869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.55, #queue-req: 0, 
[2025-07-28 01:12:41 DP0 TP0] Decode batch. #running-req: 1, #token: 4713, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.90, #queue-req: 0, 
[2025-07-28 01:12:41 DP1 TP0] Decode batch. #running-req: 1, #token: 909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.45, #queue-req: 0, 
[2025-07-28 01:12:41 DP0 TP0] Decode batch. #running-req: 1, #token: 4753, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.04, #queue-req: 0, 
[2025-07-28 01:12:41 DP1 TP0] Decode batch. #running-req: 1, #token: 949, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.73, #queue-req: 0, 
[2025-07-28 01:12:41 DP0 TP0] Decode batch. #running-req: 1, #token: 4793, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.07, #queue-req: 0, 
[2025-07-28 01:12:41 DP1 TP0] Decode batch. #running-req: 1, #token: 989, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.72, #queue-req: 0, 
[2025-07-28 01:12:41 DP0 TP0] Decode batch. #running-req: 1, #token: 4833, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.90, #queue-req: 0, 
[2025-07-28 01:12:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1029, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.57, #queue-req: 0, 
[2025-07-28 01:12:42 DP0 TP0] Decode batch. #running-req: 1, #token: 4873, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.05, #queue-req: 0, 
[2025-07-28 01:12:42 DP1 TP0] Decode batch. #running-req: 1, #token: 1069, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.01, #queue-req: 0, 
[2025-07-28 01:12:42 DP0 TP0] Decode batch. #running-req: 1, #token: 4913, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.92, #queue-req: 0, 
[2025-07-28 01:12:42 DP1 TP0] Decode batch. #running-req: 1, #token: 1109, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.87, #queue-req: 0, 
[2025-07-28 01:12:42 DP0 TP0] Decode batch. #running-req: 1, #token: 4953, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.93, #queue-req: 0, 
[2025-07-28 01:12:42 DP1 TP0] Decode batch. #running-req: 1, #token: 1149, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.03, #queue-req: 0, 
[2025-07-28 01:12:42 DP0 TP0] Decode batch. #running-req: 1, #token: 4993, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.84, #queue-req: 0, 
[2025-07-28 01:12:42 DP1 TP0] Decode batch. #running-req: 1, #token: 1189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.14, #queue-req: 0, 
[2025-07-28 01:12:42 DP0 TP0] Decode batch. #running-req: 1, #token: 5033, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.15, #queue-req: 0, 
[2025-07-28 01:12:42 DP1 TP0] Decode batch. #running-req: 1, #token: 1229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.03, #queue-req: 0, 
[2025-07-28 01:12:43 DP0 TP0] Decode batch. #running-req: 1, #token: 5073, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.05, #queue-req: 0, 
[2025-07-28 01:12:43 DP1 TP0] Decode batch. #running-req: 1, #token: 1269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.24, #queue-req: 0, 
[2025-07-28 01:12:43 DP0 TP0] Decode batch. #running-req: 1, #token: 5113, token usage: 0.00, cuda graph: True, gen throughput (token/s): 198.08, #queue-req: 0, 
[2025-07-28 01:12:43] INFO:     127.0.0.1:39804 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:12:43 DP0 TP0] Decode batch. #running-req: 1, #token: 5153, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.70, #queue-req: 0, 
[2025-07-28 01:12:43 DP0 TP0] Decode batch. #running-req: 1, #token: 5193, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.39, #queue-req: 0, 
[2025-07-28 01:12:43 DP0 TP0] Decode batch. #running-req: 1, #token: 5233, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.60, #queue-req: 0, 
[2025-07-28 01:12:44 DP0 TP0] Decode batch. #running-req: 1, #token: 5273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.60, #queue-req: 0, 
[2025-07-28 01:12:44 DP0 TP0] Decode batch. #running-req: 1, #token: 5313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.62, #queue-req: 0, 
[2025-07-28 01:12:44 DP0 TP0] Decode batch. #running-req: 1, #token: 5353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.67, #queue-req: 0, 
[2025-07-28 01:12:44 DP0 TP0] Decode batch. #running-req: 1, #token: 5393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.59, #queue-req: 0, 
[2025-07-28 01:12:44 DP0 TP0] Decode batch. #running-req: 1, #token: 5433, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.52, #queue-req: 0, 
[2025-07-28 01:12:45 DP0 TP0] Decode batch. #running-req: 1, #token: 5473, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.65, #queue-req: 0, 
[2025-07-28 01:12:45 DP0 TP0] Decode batch. #running-req: 1, #token: 5513, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.58, #queue-req: 0, 
[2025-07-28 01:12:45 DP0 TP0] Decode batch. #running-req: 1, #token: 5553, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.68, #queue-req: 0, 
[2025-07-28 01:12:45 DP0 TP0] Decode batch. #running-req: 1, #token: 5593, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.58, #queue-req: 0, 
[2025-07-28 01:12:45 DP0 TP0] Decode batch. #running-req: 1, #token: 5633, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.74, #queue-req: 0, 
[2025-07-28 01:12:46 DP0 TP0] Decode batch. #running-req: 1, #token: 5673, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.42, #queue-req: 0, 
[2025-07-28 01:12:46 DP0 TP0] Decode batch. #running-req: 1, #token: 5713, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.50, #queue-req: 0, 
[2025-07-28 01:12:46 DP0 TP0] Decode batch. #running-req: 1, #token: 5753, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.47, #queue-req: 0, 
[2025-07-28 01:12:46 DP0 TP0] Decode batch. #running-req: 1, #token: 5793, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.61, #queue-req: 0, 
[2025-07-28 01:12:46 DP0 TP0] Decode batch. #running-req: 1, #token: 5833, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.57, #queue-req: 0, 
[2025-07-28 01:12:47 DP0 TP0] Decode batch. #running-req: 1, #token: 5873, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.58, #queue-req: 0, 
[2025-07-28 01:12:47 DP0 TP0] Decode batch. #running-req: 1, #token: 5913, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.55, #queue-req: 0, 
[2025-07-28 01:12:47 DP0 TP0] Decode batch. #running-req: 1, #token: 5953, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.47, #queue-req: 0, 
[2025-07-28 01:12:47 DP0 TP0] Decode batch. #running-req: 1, #token: 5993, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.46, #queue-req: 0, 
[2025-07-28 01:12:47 DP0 TP0] Decode batch. #running-req: 1, #token: 6033, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.51, #queue-req: 0, 
[2025-07-28 01:12:48 DP0 TP0] Decode batch. #running-req: 1, #token: 6073, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.50, #queue-req: 0, 
[2025-07-28 01:12:48 DP0 TP0] Decode batch. #running-req: 1, #token: 6113, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.56, #queue-req: 0, 
[2025-07-28 01:12:48 DP0 TP0] Decode batch. #running-req: 1, #token: 6153, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.40, #queue-req: 0, 
[2025-07-28 01:12:48 DP0 TP0] Decode batch. #running-req: 1, #token: 6193, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.01, #queue-req: 0, 
[2025-07-28 01:12:48 DP0 TP0] Decode batch. #running-req: 1, #token: 6233, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.03, #queue-req: 0, 
[2025-07-28 01:12:49 DP0 TP0] Decode batch. #running-req: 1, #token: 6273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.99, #queue-req: 0, 
[2025-07-28 01:12:49 DP0 TP0] Decode batch. #running-req: 1, #token: 6313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.07, #queue-req: 0, 
[2025-07-28 01:12:49 DP0 TP0] Decode batch. #running-req: 1, #token: 6353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.14, #queue-req: 0, 
[2025-07-28 01:12:49 DP0 TP0] Decode batch. #running-req: 1, #token: 6393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.11, #queue-req: 0, 
[2025-07-28 01:12:49 DP0 TP0] Decode batch. #running-req: 1, #token: 6433, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.92, #queue-req: 0, 
[2025-07-28 01:12:50 DP0 TP0] Decode batch. #running-req: 1, #token: 6473, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.82, #queue-req: 0, 
[2025-07-28 01:12:50 DP0 TP0] Decode batch. #running-req: 1, #token: 6513, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.01, #queue-req: 0, 
[2025-07-28 01:12:50 DP0 TP0] Decode batch. #running-req: 1, #token: 6553, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.99, #queue-req: 0, 
[2025-07-28 01:12:50 DP0 TP0] Decode batch. #running-req: 1, #token: 6593, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.07, #queue-req: 0, 
[2025-07-28 01:12:50 DP0 TP0] Decode batch. #running-req: 1, #token: 6633, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.09, #queue-req: 0, 
[2025-07-28 01:12:51 DP0 TP0] Decode batch. #running-req: 1, #token: 6673, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.96, #queue-req: 0, 
[2025-07-28 01:12:51 DP0 TP0] Decode batch. #running-req: 1, #token: 6713, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.97, #queue-req: 0, 
[2025-07-28 01:12:51 DP0 TP0] Decode batch. #running-req: 1, #token: 6753, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.97, #queue-req: 0, 
[2025-07-28 01:12:51 DP0 TP0] Decode batch. #running-req: 1, #token: 6793, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.88, #queue-req: 0, 
[2025-07-28 01:12:51 DP0 TP0] Decode batch. #running-req: 1, #token: 6833, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.06, #queue-req: 0, 
[2025-07-28 01:12:52 DP0 TP0] Decode batch. #running-req: 1, #token: 6873, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.09, #queue-req: 0, 
[2025-07-28 01:12:52 DP0 TP0] Decode batch. #running-req: 1, #token: 6913, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.93, #queue-req: 0, 
[2025-07-28 01:12:52 DP0 TP0] Decode batch. #running-req: 1, #token: 6953, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.93, #queue-req: 0, 
[2025-07-28 01:12:52 DP0 TP0] Decode batch. #running-req: 1, #token: 6993, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.91, #queue-req: 0, 
[2025-07-28 01:12:52 DP0 TP0] Decode batch. #running-req: 1, #token: 7033, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.94, #queue-req: 0, 
[2025-07-28 01:12:53 DP0 TP0] Decode batch. #running-req: 1, #token: 7073, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.99, #queue-req: 0, 
[2025-07-28 01:12:53 DP0 TP0] Decode batch. #running-req: 1, #token: 7113, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.01, #queue-req: 0, 
[2025-07-28 01:12:53 DP0 TP0] Decode batch. #running-req: 1, #token: 7153, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.97, #queue-req: 0, 
[2025-07-28 01:12:53 DP0 TP0] Decode batch. #running-req: 1, #token: 7193, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.84, #queue-req: 0, 
[2025-07-28 01:12:53 DP0 TP0] Decode batch. #running-req: 1, #token: 7233, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.63, #queue-req: 0, 
[2025-07-28 01:12:54 DP0 TP0] Decode batch. #running-req: 1, #token: 7273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.63, #queue-req: 0, 
[2025-07-28 01:12:54 DP0 TP0] Decode batch. #running-req: 1, #token: 7313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.66, #queue-req: 0, 
[2025-07-28 01:12:54 DP0 TP0] Decode batch. #running-req: 1, #token: 7353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.35, #queue-req: 0, 
[2025-07-28 01:12:54 DP0 TP0] Decode batch. #running-req: 1, #token: 7393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 195.69, #queue-req: 0, 
[2025-07-28 01:12:54 DP0 TP0] Decode batch. #running-req: 1, #token: 7433, token usage: 0.00, cuda graph: True, gen throughput (token/s): 195.36, #queue-req: 0, 
[2025-07-28 01:12:55 DP0 TP0] Decode batch. #running-req: 1, #token: 7473, token usage: 0.00, cuda graph: True, gen throughput (token/s): 183.67, #queue-req: 0, 
[2025-07-28 01:12:55 DP0 TP0] Decode batch. #running-req: 1, #token: 7513, token usage: 0.00, cuda graph: True, gen throughput (token/s): 195.86, #queue-req: 0, 
[2025-07-28 01:12:55 DP0 TP0] Decode batch. #running-req: 1, #token: 7553, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.12, #queue-req: 0, 
[2025-07-28 01:12:55 DP0 TP0] Decode batch. #running-req: 1, #token: 7593, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.67, #queue-req: 0, 
[2025-07-28 01:12:56 DP0 TP0] Decode batch. #running-req: 1, #token: 7633, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.69, #queue-req: 0, 
[2025-07-28 01:12:56 DP0 TP0] Decode batch. #running-req: 1, #token: 7673, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.71, #queue-req: 0, 
[2025-07-28 01:12:56 DP0 TP0] Decode batch. #running-req: 1, #token: 7713, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.54, #queue-req: 0, 
[2025-07-28 01:12:56 DP0 TP0] Decode batch. #running-req: 1, #token: 7753, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.47, #queue-req: 0, 
[2025-07-28 01:12:56 DP0 TP0] Decode batch. #running-req: 1, #token: 7793, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.53, #queue-req: 0, 
[2025-07-28 01:12:57 DP0 TP0] Decode batch. #running-req: 1, #token: 7833, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.51, #queue-req: 0, 
[2025-07-28 01:12:57 DP0 TP0] Decode batch. #running-req: 1, #token: 7873, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.48, #queue-req: 0, 
[2025-07-28 01:12:57 DP0 TP0] Decode batch. #running-req: 1, #token: 7913, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.62, #queue-req: 0, 
[2025-07-28 01:12:57 DP0 TP0] Decode batch. #running-req: 1, #token: 7953, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.60, #queue-req: 0, 
[2025-07-28 01:12:57 DP0 TP0] Decode batch. #running-req: 1, #token: 7993, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.49, #queue-req: 0, 
[2025-07-28 01:12:58 DP0 TP0] Decode batch. #running-req: 1, #token: 8033, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.45, #queue-req: 0, 
[2025-07-28 01:12:58 DP0 TP0] Decode batch. #running-req: 1, #token: 8073, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.50, #queue-req: 0, 
[2025-07-28 01:12:58 DP0 TP0] Decode batch. #running-req: 1, #token: 8113, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.65, #queue-req: 0, 
[2025-07-28 01:12:58 DP0 TP0] Decode batch. #running-req: 1, #token: 8153, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.57, #queue-req: 0, 
[2025-07-28 01:12:58 DP0 TP0] Decode batch. #running-req: 1, #token: 8193, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.52, #queue-req: 0, 
[2025-07-28 01:12:59 DP0 TP0] Decode batch. #running-req: 1, #token: 8233, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.05, #queue-req: 0, 
[2025-07-28 01:12:59 DP0 TP0] Decode batch. #running-req: 1, #token: 8273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.11, #queue-req: 0, 
[2025-07-28 01:12:59 DP0 TP0] Decode batch. #running-req: 1, #token: 8313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.12, #queue-req: 0, 
[2025-07-28 01:12:59 DP0 TP0] Decode batch. #running-req: 1, #token: 8353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.11, #queue-req: 0, 
[2025-07-28 01:12:59 DP0 TP0] Decode batch. #running-req: 1, #token: 8393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.16, #queue-req: 0, 
[2025-07-28 01:12:59] INFO:     127.0.0.1:42184 - "POST /v1/chat/completions HTTP/1.1" 200 OK

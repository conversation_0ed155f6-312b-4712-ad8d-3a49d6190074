[2025-07-28 01:26:55] server_args=ServerArgs(model_path='/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Llama-70B', tokenizer_path='/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Llama-70B', tokenizer_mode='auto', skip_tokenizer_init=False, load_format='auto', model_loader_extra_config='{}', trust_remote_code=False, context_length=32768, is_embedding=False, enable_multimodal=None, revision=None, model_impl='auto', host='127.0.0.1', port=8003, skip_server_warmup=False, warmups=None, nccl_port=None, dtype='auto', quantization=None, quantization_param_path=None, kv_cache_dtype='auto', mem_fraction_static=0.85, max_running_requests=None, max_total_tokens=None, chunked_prefill_size=8192, max_prefill_tokens=16384, schedule_policy='fcfs', schedule_conservativeness=1.0, cpu_offload_gb=0, page_size=1, hybrid_kvcache_ratio=None, swa_full_tokens_ratio=0.8, disable_hybrid_swa_memory=False, device='cuda', tp_size=4, pp_size=1, max_micro_batch_size=None, stream_interval=1, stream_output=False, random_seed=912069224, constrained_json_whitespace_pattern=None, watchdog_timeout=300, dist_timeout=None, download_dir=None, base_gpu_id=0, gpu_id_step=1, sleep_on_idle=False, log_level='info', log_level_http=None, log_requests=False, log_requests_level=0, crash_dump_folder=None, show_time_cost=True, enable_metrics=True, enable_metrics_for_all_schedulers=False, bucket_time_to_first_token=None, bucket_inter_token_latency=None, bucket_e2e_request_latency=None, collect_tokens_histogram=False, decode_log_interval=40, enable_request_time_stats_logging=False, kv_events_config=None, api_key=None, served_model_name='/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Llama-70B', chat_template=None, completion_template=None, file_storage_path='sglang_storage', enable_cache_report=False, reasoning_parser=None, tool_call_parser=None, dp_size=2, load_balance_method='round_robin', dist_init_addr=None, nnodes=1, node_rank=0, json_model_override_args='{}', preferred_sampling_params=None, enable_lora=None, max_lora_rank=None, lora_target_modules=None, lora_paths=None, max_loras_per_batch=8, lora_backend='triton', attention_backend=None, sampling_backend='flashinfer', grammar_backend='xgrammar', mm_attention_backend=None, speculative_algorithm=None, speculative_draft_model_path=None, speculative_num_steps=None, speculative_eagle_topk=None, speculative_num_draft_tokens=None, speculative_accept_threshold_single=1.0, speculative_accept_threshold_acc=1.0, speculative_token_map=None, ep_size=1, enable_ep_moe=False, enable_deepep_moe=False, enable_flashinfer_moe=False, enable_flashinfer_allreduce_fusion=False, deepep_mode='auto', ep_num_redundant_experts=0, ep_dispatch_algorithm='static', init_expert_location='trivial', enable_eplb=False, eplb_algorithm='auto', eplb_rebalance_num_iterations=1000, eplb_rebalance_layers_per_chunk=None, expert_distribution_recorder_mode=None, expert_distribution_recorder_buffer_size=1000, enable_expert_distribution_metrics=False, deepep_config=None, moe_dense_tp_size=None, enable_hierarchical_cache=False, hicache_ratio=2.0, hicache_size=0, hicache_write_policy='write_through_selective', hicache_io_backend='', hicache_storage_backend=None, enable_double_sparsity=False, ds_channel_config_path=None, ds_heavy_channel_num=32, ds_heavy_token_num=256, ds_heavy_channel_type='qk', ds_sparse_decode_threshold=4096, disable_radix_cache=False, cuda_graph_max_bs=None, cuda_graph_bs=None, disable_cuda_graph=False, disable_cuda_graph_padding=False, enable_profile_cuda_graph=False, enable_nccl_nvls=False, enable_tokenizer_batch_encode=False, disable_outlines_disk_cache=False, disable_custom_all_reduce=False, enable_mscclpp=False, disable_overlap_schedule=False, enable_mixed_chunk=False, enable_dp_attention=False, enable_dp_lm_head=False, enable_two_batch_overlap=False, enable_torch_compile=False, torch_compile_max_bs=32, torchao_config='', enable_nan_detection=False, enable_p2p_check=False, triton_attention_reduce_in_fp32=False, triton_attention_num_kv_splits=8, num_continuous_decode_steps=1, delete_ckpt_after_loading=False, enable_memory_saver=False, allow_auto_truncate=False, enable_custom_logit_processor=False, flashinfer_mla_disable_ragged=False, disable_shared_experts_fusion=False, disable_chunked_prefix_cache=False, disable_fast_image_processor=False, enable_return_hidden_states=False, enable_triton_kernel_moe=False, debug_tensor_dump_output_folder=None, debug_tensor_dump_input_file=None, debug_tensor_dump_inject=False, debug_tensor_dump_prefill_only=False, disaggregation_mode='null', disaggregation_transfer_backend='mooncake', disaggregation_bootstrap_port=8998, disaggregation_decode_tp=None, disaggregation_decode_dp=None, disaggregation_prefill_pp=1, disaggregation_ib_device=None, num_reserved_decode_tokens=512, pdlb_url=None, custom_weight_loader=[], weight_loader_disable_mmap=False, enable_pdmux=False, sm_group_num=3)
[2025-07-28 01:27:01] Launch DP0 starting at GPU #0.
[2025-07-28 01:27:01] Launch DP1 starting at GPU #4.
[2025-07-28 01:27:09 DP0 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 01:27:09 DP0 TP0] Init torch distributed begin.
[2025-07-28 01:27:09 DP1 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 01:27:09 DP1 TP0] Init torch distributed begin.
[2025-07-28 01:27:10 DP0 TP0] sglang is using nccl==2.26.2
[2025-07-28 01:27:11 DP1 TP0] sglang is using nccl==2.26.2
[2025-07-28 01:27:12 DP0 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 01:27:12 DP1 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 01:27:12 DP0 TP0] Load weight begin. avail mem=78.21 GB
[2025-07-28 01:27:12 DP1 TP0] Load weight begin. avail mem=78.21 GB
[2025-07-28 01:27:12 DP0 TP1] Scheduler hit an exception: Traceback (most recent call last):
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/scheduler.py", line 2923, in run_scheduler_process
    scheduler = Scheduler(server_args, port_args, gpu_id, tp_rank, pp_rank, dp_rank)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/scheduler.py", line 344, in __init__
    self.tp_worker = TpWorkerClass(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/tp_worker_overlap_thread.py", line 66, in __init__
    self.worker = TpModelWorker(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/tp_worker.py", line 81, in __init__
    self.model_runner = ModelRunner(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_executor/model_runner.py", line 233, in __init__
    self.initialize(min_per_gpu_memory)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_executor/model_runner.py", line 276, in initialize
    self.load_model()
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_executor/model_runner.py", line 622, in load_model
    self.model = get_model(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/__init__.py", line 22, in get_model
    return loader.load_model(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 437, in load_model
    self.load_weights_and_postprocess(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 445, in load_weights_and_postprocess
    model.load_weights(weights)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/models/llama.py", line 585, in load_weights
    for name, loaded_weight in weights:
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 410, in _get_all_weights
    yield from self._get_weights_iterator(primary_weights)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 357, in _get_weights_iterator
    hf_folder, hf_weights_files, use_safetensors = self._prepare_weights(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 346, in _prepare_weights
    raise RuntimeError(
RuntimeError: Cannot find any model weights with `/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Llama-70B`

[2025-07-28 01:27:12 DP0 TP0] Scheduler hit an exception: Traceback (most recent call last):
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/scheduler.py", line 2923, in run_scheduler_process
    scheduler = Scheduler(server_args, port_args, gpu_id, tp_rank, pp_rank, dp_rank)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/scheduler.py", line 344, in __init__
    self.tp_worker = TpWorkerClass(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/tp_worker_overlap_thread.py", line 66, in __init__
    self.worker = TpModelWorker(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/tp_worker.py", line 81, in __init__
    self.model_runner = ModelRunner(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_executor/model_runner.py", line 233, in __init__
    self.initialize(min_per_gpu_memory)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_executor/model_runner.py", line 276, in initialize
    self.load_model()
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_executor/model_runner.py", line 622, in load_model
    self.model = get_model(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/__init__.py", line 22, in get_model
    return loader.load_model(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 437, in load_model
    self.load_weights_and_postprocess(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 445, in load_weights_and_postprocess
    model.load_weights(weights)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/models/llama.py", line 585, in load_weights
    for name, loaded_weight in weights:
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 410, in _get_all_weights
    yield from self._get_weights_iterator(primary_weights)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 357, in _get_weights_iterator
    hf_folder, hf_weights_files, use_safetensors = self._prepare_weights(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 346, in _prepare_weights
    raise RuntimeError(
RuntimeError: Cannot find any model weights with `/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Llama-70B`

[2025-07-28 01:27:12 DP0 TP3] Scheduler hit an exception: Traceback (most recent call last):
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/scheduler.py", line 2923, in run_scheduler_process
    scheduler = Scheduler(server_args, port_args, gpu_id, tp_rank, pp_rank, dp_rank)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/scheduler.py", line 344, in __init__
    self.tp_worker = TpWorkerClass(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/tp_worker_overlap_thread.py", line 66, in __init__
    self.worker = TpModelWorker(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/tp_worker.py", line 81, in __init__
    self.model_runner = ModelRunner(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_executor/model_runner.py", line 233, in __init__
    self.initialize(min_per_gpu_memory)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_executor/model_runner.py", line 276, in initialize
    self.load_model()
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_executor/model_runner.py", line 622, in load_model
    self.model = get_model(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/__init__.py", line 22, in get_model
    return loader.load_model(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 437, in load_model
    self.load_weights_and_postprocess(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 445, in load_weights_and_postprocess
    model.load_weights(weights)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/models/llama.py", line 585, in load_weights
    for name, loaded_weight in weights:
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 410, in _get_all_weights
    yield from self._get_weights_iterator(primary_weights)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 357, in _get_weights_iterator
    hf_folder, hf_weights_files, use_safetensors = self._prepare_weights(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 346, in _prepare_weights
    raise RuntimeError(
RuntimeError: Cannot find any model weights with `/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Llama-70B`

[2025-07-28 01:27:12 DP0 TP2] Scheduler hit an exception: Traceback (most recent call last):
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/scheduler.py", line 2923, in run_scheduler_process
    scheduler = Scheduler(server_args, port_args, gpu_id, tp_rank, pp_rank, dp_rank)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/scheduler.py", line 344, in __init__
    self.tp_worker = TpWorkerClass(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/tp_worker_overlap_thread.py", line 66, in __init__
    self.worker = TpModelWorker(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/tp_worker.py", line 81, in __init__
    self.model_runner = ModelRunner(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_executor/model_runner.py", line 233, in __init__
    self.initialize(min_per_gpu_memory)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_executor/model_runner.py", line 276, in initialize
    self.load_model()
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_executor/model_runner.py", line 622, in load_model
    self.model = get_model(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/__init__.py", line 22, in get_model
    return loader.load_model(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 437, in load_model
    self.load_weights_and_postprocess(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 445, in load_weights_and_postprocess
    model.load_weights(weights)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/models/llama.py", line 585, in load_weights
    for name, loaded_weight in weights:
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 410, in _get_all_weights
    yield from self._get_weights_iterator(primary_weights)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 357, in _get_weights_iterator
    hf_folder, hf_weights_files, use_safetensors = self._prepare_weights(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 346, in _prepare_weights
    raise RuntimeError(
RuntimeError: Cannot find any model weights with `/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Llama-70B`

[2025-07-28 01:27:12 DP1 TP2] Scheduler hit an exception: Traceback (most recent call last):
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/scheduler.py", line 2923, in run_scheduler_process
    scheduler = Scheduler(server_args, port_args, gpu_id, tp_rank, pp_rank, dp_rank)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/scheduler.py", line 344, in __init__
    self.tp_worker = TpWorkerClass(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/tp_worker_overlap_thread.py", line 66, in __init__
    self.worker = TpModelWorker(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/tp_worker.py", line 81, in __init__
    self.model_runner = ModelRunner(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_executor/model_runner.py", line 233, in __init__
    self.initialize(min_per_gpu_memory)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_executor/model_runner.py", line 276, in initialize
    self.load_model()
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_executor/model_runner.py", line 622, in load_model
    self.model = get_model(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/__init__.py", line 22, in get_model
    return loader.load_model(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 437, in load_model
    self.load_weights_and_postprocess(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 445, in load_weights_and_postprocess
    model.load_weights(weights)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/models/llama.py", line 585, in load_weights
    for name, loaded_weight in weights:
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 410, in _get_all_weights
    yield from self._get_weights_iterator(primary_weights)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 357, in _get_weights_iterator
    hf_folder, hf_weights_files, use_safetensors = self._prepare_weights(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 346, in _prepare_weights
    raise RuntimeError(
RuntimeError: Cannot find any model weights with `/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Llama-70B`

[2025-07-28 01:27:12 DP1 TP0] Scheduler hit an exception: Traceback (most recent call last):
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/scheduler.py", line 2923, in run_scheduler_process
    scheduler = Scheduler(server_args, port_args, gpu_id, tp_rank, pp_rank, dp_rank)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/scheduler.py", line 344, in __init__
    self.tp_worker = TpWorkerClass(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/tp_worker_overlap_thread.py", line 66, in __init__
    self.worker = TpModelWorker(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/tp_worker.py", line 81, in __init__
    self.model_runner = ModelRunner(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_executor/model_runner.py", line 233, in __init__
    self.initialize(min_per_gpu_memory)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_executor/model_runner.py", line 276, in initialize
    self.load_model()
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_executor/model_runner.py", line 622, in load_model
    self.model = get_model(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/__init__.py", line 22, in get_model
    return loader.load_model(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 437, in load_model
    self.load_weights_and_postprocess(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 445, in load_weights_and_postprocess
    model.load_weights(weights)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/models/llama.py", line 585, in load_weights
    for name, loaded_weight in weights:
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 410, in _get_all_weights
    yield from self._get_weights_iterator(primary_weights)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 357, in _get_weights_iterator
    hf_folder, hf_weights_files, use_safetensors = self._prepare_weights(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 346, in _prepare_weights
    raise RuntimeError(
RuntimeError: Cannot find any model weights with `/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Llama-70B`

[2025-07-28 01:27:12 DP1 TP1] Scheduler hit an exception: Traceback (most recent call last):
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/scheduler.py", line 2923, in run_scheduler_process
    scheduler = Scheduler(server_args, port_args, gpu_id, tp_rank, pp_rank, dp_rank)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/scheduler.py", line 344, in __init__
    self.tp_worker = TpWorkerClass(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/tp_worker_overlap_thread.py", line 66, in __init__
    self.worker = TpModelWorker(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/tp_worker.py", line 81, in __init__
    self.model_runner = ModelRunner(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_executor/model_runner.py", line 233, in __init__
    self.initialize(min_per_gpu_memory)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_executor/model_runner.py", line 276, in initialize
    self.load_model()
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_executor/model_runner.py", line 622, in load_model
    self.model = get_model(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/__init__.py", line 22, in get_model
    return loader.load_model(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 437, in load_model
    self.load_weights_and_postprocess(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 445, in load_weights_and_postprocess
    model.load_weights(weights)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/models/llama.py", line 585, in load_weights
    for name, loaded_weight in weights:
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 410, in _get_all_weights
    yield from self._get_weights_iterator(primary_weights)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 357, in _get_weights_iterator
    hf_folder, hf_weights_files, use_safetensors = self._prepare_weights(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 346, in _prepare_weights
    raise RuntimeError(
RuntimeError: Cannot find any model weights with `/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Llama-70B`

[2025-07-28 01:27:12 DP1 TP3] Scheduler hit an exception: Traceback (most recent call last):
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/scheduler.py", line 2923, in run_scheduler_process
    scheduler = Scheduler(server_args, port_args, gpu_id, tp_rank, pp_rank, dp_rank)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/scheduler.py", line 344, in __init__
    self.tp_worker = TpWorkerClass(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/tp_worker_overlap_thread.py", line 66, in __init__
    self.worker = TpModelWorker(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/managers/tp_worker.py", line 81, in __init__
    self.model_runner = ModelRunner(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_executor/model_runner.py", line 233, in __init__
    self.initialize(min_per_gpu_memory)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_executor/model_runner.py", line 276, in initialize
    self.load_model()
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_executor/model_runner.py", line 622, in load_model
    self.model = get_model(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/__init__.py", line 22, in get_model
    return loader.load_model(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 437, in load_model
    self.load_weights_and_postprocess(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 445, in load_weights_and_postprocess
    model.load_weights(weights)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/models/llama.py", line 585, in load_weights
    for name, loaded_weight in weights:
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 410, in _get_all_weights
    yield from self._get_weights_iterator(primary_weights)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 357, in _get_weights_iterator
    hf_folder, hf_weights_files, use_safetensors = self._prepare_weights(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/sglang/srt/model_loader/loader.py", line 346, in _prepare_weights
    raise RuntimeError(
RuntimeError: Cannot find any model weights with `/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Llama-70B`

[2025-07-28 01:27:13] Child process unexpectedly failed with exitcode=131. pid=1852562

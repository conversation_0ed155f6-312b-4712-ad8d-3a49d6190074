[2025-07-27 23:58:46] server_args=ServerArgs(model_path='/data/home/<USER>/repos/anesbench/share/models/gemma-3-1b-it', tokenizer_path='/data/home/<USER>/repos/anesbench/share/models/gemma-3-1b-it', tokenizer_mode='auto', skip_tokenizer_init=False, load_format='auto', model_loader_extra_config='{}', trust_remote_code=False, context_length=32768, is_embedding=False, enable_multimodal=None, revision=None, model_impl='auto', host='127.0.0.1', port=8002, skip_server_warmup=False, warmups=None, nccl_port=None, dtype='auto', quantization=None, quantization_param_path=None, kv_cache_dtype='auto', mem_fraction_static=0.85, max_running_requests=None, max_total_tokens=None, chunked_prefill_size=8192, max_prefill_tokens=16384, schedule_policy='fcfs', schedule_conservativeness=1.0, cpu_offload_gb=0, page_size=1, hybrid_kvcache_ratio=None, swa_full_tokens_ratio=0.8, disable_hybrid_swa_memory=False, device='cuda', tp_size=4, pp_size=1, max_micro_batch_size=None, stream_interval=1, stream_output=False, random_seed=720279633, constrained_json_whitespace_pattern=None, watchdog_timeout=300, dist_timeout=None, download_dir=None, base_gpu_id=0, gpu_id_step=1, sleep_on_idle=False, log_level='info', log_level_http=None, log_requests=False, log_requests_level=0, crash_dump_folder=None, show_time_cost=True, enable_metrics=True, enable_metrics_for_all_schedulers=False, bucket_time_to_first_token=None, bucket_inter_token_latency=None, bucket_e2e_request_latency=None, collect_tokens_histogram=False, decode_log_interval=40, enable_request_time_stats_logging=False, kv_events_config=None, api_key=None, served_model_name='/data/home/<USER>/repos/anesbench/share/models/gemma-3-1b-it', chat_template=None, completion_template=None, file_storage_path='sglang_storage', enable_cache_report=False, reasoning_parser=None, tool_call_parser=None, dp_size=2, load_balance_method='round_robin', dist_init_addr=None, nnodes=1, node_rank=0, json_model_override_args='{}', preferred_sampling_params=None, enable_lora=None, max_lora_rank=None, lora_target_modules=None, lora_paths=None, max_loras_per_batch=8, lora_backend='triton', attention_backend=None, sampling_backend='flashinfer', grammar_backend='xgrammar', mm_attention_backend=None, speculative_algorithm=None, speculative_draft_model_path=None, speculative_num_steps=None, speculative_eagle_topk=None, speculative_num_draft_tokens=None, speculative_accept_threshold_single=1.0, speculative_accept_threshold_acc=1.0, speculative_token_map=None, ep_size=1, enable_ep_moe=False, enable_deepep_moe=False, enable_flashinfer_moe=False, enable_flashinfer_allreduce_fusion=False, deepep_mode='auto', ep_num_redundant_experts=0, ep_dispatch_algorithm='static', init_expert_location='trivial', enable_eplb=False, eplb_algorithm='auto', eplb_rebalance_num_iterations=1000, eplb_rebalance_layers_per_chunk=None, expert_distribution_recorder_mode=None, expert_distribution_recorder_buffer_size=1000, enable_expert_distribution_metrics=False, deepep_config=None, moe_dense_tp_size=None, enable_hierarchical_cache=False, hicache_ratio=2.0, hicache_size=0, hicache_write_policy='write_through_selective', hicache_io_backend='', hicache_storage_backend=None, enable_double_sparsity=False, ds_channel_config_path=None, ds_heavy_channel_num=32, ds_heavy_token_num=256, ds_heavy_channel_type='qk', ds_sparse_decode_threshold=4096, disable_radix_cache=False, cuda_graph_max_bs=None, cuda_graph_bs=None, disable_cuda_graph=False, disable_cuda_graph_padding=False, enable_profile_cuda_graph=False, enable_nccl_nvls=False, enable_tokenizer_batch_encode=False, disable_outlines_disk_cache=False, disable_custom_all_reduce=False, enable_mscclpp=False, disable_overlap_schedule=False, enable_mixed_chunk=False, enable_dp_attention=False, enable_dp_lm_head=False, enable_two_batch_overlap=False, enable_torch_compile=False, torch_compile_max_bs=32, torchao_config='', enable_nan_detection=False, enable_p2p_check=False, triton_attention_reduce_in_fp32=False, triton_attention_num_kv_splits=8, num_continuous_decode_steps=1, delete_ckpt_after_loading=False, enable_memory_saver=False, allow_auto_truncate=False, enable_custom_logit_processor=False, flashinfer_mla_disable_ragged=False, disable_shared_experts_fusion=False, disable_chunked_prefix_cache=False, disable_fast_image_processor=False, enable_return_hidden_states=False, enable_triton_kernel_moe=False, debug_tensor_dump_output_folder=None, debug_tensor_dump_input_file=None, debug_tensor_dump_inject=False, debug_tensor_dump_prefill_only=False, disaggregation_mode='null', disaggregation_transfer_backend='mooncake', disaggregation_bootstrap_port=8998, disaggregation_decode_tp=None, disaggregation_decode_dp=None, disaggregation_prefill_pp=1, disaggregation_ib_device=None, num_reserved_decode_tokens=512, pdlb_url=None, custom_weight_loader=[], weight_loader_disable_mmap=False, enable_pdmux=False, sm_group_num=3)
[2025-07-27 23:58:47] Inferred chat template from model path: gemma-it
[2025-07-27 23:58:52] Launch DP0 starting at GPU #0.
[2025-07-27 23:58:52] Launch DP1 starting at GPU #4.
[2025-07-27 23:59:01 DP1 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-27 23:59:01 DP1 TP0] Init torch distributed begin.
[2025-07-27 23:59:01 DP0 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-27 23:59:01 DP0 TP0] Init torch distributed begin.
[2025-07-27 23:59:02 DP1 TP0] sglang is using nccl==2.26.2
[2025-07-27 23:59:02 DP0 TP0] sglang is using nccl==2.26.2
[2025-07-27 23:59:03 DP1 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-27 23:59:04 DP0 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-27 23:59:04 DP1 TP0] Load weight begin. avail mem=78.21 GB

Loading safetensors checkpoint shards:   0% Completed | 0/1 [00:00<?, ?it/s]
[2025-07-27 23:59:04 DP0 TP0] Load weight begin. avail mem=78.21 GB

Loading safetensors checkpoint shards:   0% Completed | 0/1 [00:00<?, ?it/s]

Loading safetensors checkpoint shards: 100% Completed | 1/1 [00:00<00:00,  3.92it/s]

Loading safetensors checkpoint shards: 100% Completed | 1/1 [00:00<00:00,  3.92it/s]

[2025-07-27 23:59:04 DP1 TP0] Load weight end. type=Gemma3ForCausalLM, dtype=torch.bfloat16, avail mem=77.28 GB, mem usage=0.93 GB.
[2025-07-27 23:59:04 DP1 TP0] Use Sliding window memory pool. full_layer_tokens=3178032, swa_layer_tokens=2542425
[2025-07-27 23:59:04 DP1 TP0] KV Cache is allocated. #tokens: 2542425, K size: 26.67 GB, V size: 26.67 GB
[2025-07-27 23:59:04 DP1 TP3] KV Cache is allocated. #tokens: 2542425, K size: 26.67 GB, V size: 26.67 GB
[2025-07-27 23:59:04 DP1 TP1] KV Cache is allocated. #tokens: 2542425, K size: 26.67 GB, V size: 26.67 GB
[2025-07-27 23:59:04 DP1 TP2] KV Cache is allocated. #tokens: 2542425, K size: 26.67 GB, V size: 26.67 GB
[2025-07-27 23:59:04 DP1 TP0] KV Cache is allocated. #tokens: 3178032, K size: 6.06 GB, V size: 6.06 GB
[2025-07-27 23:59:04 DP1 TP3] KV Cache is allocated. #tokens: 3178032, K size: 6.06 GB, V size: 6.06 GB
[2025-07-27 23:59:04 DP1 TP1] KV Cache is allocated. #tokens: 3178032, K size: 6.06 GB, V size: 6.06 GB
[2025-07-27 23:59:04 DP1 TP2] KV Cache is allocated. #tokens: 3178032, K size: 6.06 GB, V size: 6.06 GB
[2025-07-27 23:59:04 DP1 TP0] Memory pool end. avail mem=11.19 GB

Loading safetensors checkpoint shards: 100% Completed | 1/1 [00:00<00:00,  4.39it/s]

Loading safetensors checkpoint shards: 100% Completed | 1/1 [00:00<00:00,  4.39it/s]

[2025-07-27 23:59:04 DP0 TP0] Load weight end. type=Gemma3ForCausalLM, dtype=torch.bfloat16, avail mem=77.28 GB, mem usage=0.93 GB.
[2025-07-27 23:59:04 DP0 TP0] Use Sliding window memory pool. full_layer_tokens=3178032, swa_layer_tokens=2542425
[2025-07-27 23:59:04 DP0 TP0] KV Cache is allocated. #tokens: 2542425, K size: 26.67 GB, V size: 26.67 GB
[2025-07-27 23:59:04 DP0 TP3] KV Cache is allocated. #tokens: 2542425, K size: 26.67 GB, V size: 26.67 GB
[2025-07-27 23:59:04 DP0 TP2] KV Cache is allocated. #tokens: 2542425, K size: 26.67 GB, V size: 26.67 GB
[2025-07-27 23:59:04 DP0 TP1] KV Cache is allocated. #tokens: 2542425, K size: 26.67 GB, V size: 26.67 GB
[2025-07-27 23:59:04 DP0 TP0] KV Cache is allocated. #tokens: 3178032, K size: 6.06 GB, V size: 6.06 GB
[2025-07-27 23:59:04 DP0 TP3] KV Cache is allocated. #tokens: 3178032, K size: 6.06 GB, V size: 6.06 GB
[2025-07-27 23:59:04 DP0 TP2] KV Cache is allocated. #tokens: 3178032, K size: 6.06 GB, V size: 6.06 GB
[2025-07-27 23:59:04 DP0 TP1] KV Cache is allocated. #tokens: 3178032, K size: 6.06 GB, V size: 6.06 GB
[2025-07-27 23:59:04 DP0 TP0] Memory pool end. avail mem=11.19 GB
[2025-07-27 23:59:04 DP1 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.60 GB
[2025-07-27 23:59:05 DP1 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.56 GB):   0%|          | 0/23 [00:00<?, ?it/s][2025-07-27 23:59:05 DP0 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.60 GB
[2025-07-27 23:59:05 DP0 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.56 GB):   0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.56 GB):   4%|▍         | 1/23 [00:01<00:24,  1.09s/it]
Capturing batches (bs=152 avail_mem=9.76 GB):   4%|▍         | 1/23 [00:01<00:24,  1.09s/it] 
Capturing batches (bs=160 avail_mem=10.56 GB):   4%|▍         | 1/23 [00:01<00:25,  1.16s/it]
Capturing batches (bs=152 avail_mem=9.76 GB):   4%|▍         | 1/23 [00:01<00:25,  1.16s/it] 
Capturing batches (bs=152 avail_mem=9.76 GB):   9%|▊         | 2/23 [00:01<00:13,  1.53it/s]
Capturing batches (bs=144 avail_mem=9.42 GB):   9%|▊         | 2/23 [00:01<00:13,  1.53it/s]
Capturing batches (bs=152 avail_mem=9.76 GB):   9%|▊         | 2/23 [00:01<00:14,  1.41it/s]
Capturing batches (bs=144 avail_mem=9.42 GB):   9%|▊         | 2/23 [00:01<00:14,  1.41it/s]
Capturing batches (bs=144 avail_mem=9.42 GB):  13%|█▎        | 3/23 [00:01<00:10,  1.90it/s]
Capturing batches (bs=136 avail_mem=9.38 GB):  13%|█▎        | 3/23 [00:01<00:10,  1.90it/s]
Capturing batches (bs=144 avail_mem=9.42 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.76it/s]
Capturing batches (bs=136 avail_mem=9.38 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.76it/s]
Capturing batches (bs=136 avail_mem=9.38 GB):  17%|█▋        | 4/23 [00:02<00:08,  2.18it/s]
Capturing batches (bs=128 avail_mem=9.07 GB):  17%|█▋        | 4/23 [00:02<00:08,  2.18it/s]
Capturing batches (bs=136 avail_mem=9.38 GB):  17%|█▋        | 4/23 [00:02<00:09,  2.02it/s]
Capturing batches (bs=128 avail_mem=9.07 GB):  17%|█▋        | 4/23 [00:02<00:09,  2.02it/s]
Capturing batches (bs=128 avail_mem=9.07 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.38it/s]
Capturing batches (bs=120 avail_mem=9.03 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.38it/s]
Capturing batches (bs=128 avail_mem=9.07 GB):  22%|██▏       | 5/23 [00:02<00:08,  2.21it/s]
Capturing batches (bs=120 avail_mem=9.03 GB):  22%|██▏       | 5/23 [00:02<00:08,  2.21it/s]
Capturing batches (bs=120 avail_mem=9.03 GB):  26%|██▌       | 6/23 [00:02<00:07,  2.42it/s]
Capturing batches (bs=112 avail_mem=8.76 GB):  26%|██▌       | 6/23 [00:02<00:07,  2.42it/s]
Capturing batches (bs=120 avail_mem=9.03 GB):  26%|██▌       | 6/23 [00:03<00:07,  2.33it/s]
Capturing batches (bs=112 avail_mem=8.76 GB):  26%|██▌       | 6/23 [00:03<00:07,  2.33it/s]
Capturing batches (bs=112 avail_mem=8.76 GB):  30%|███       | 7/23 [00:03<00:06,  2.53it/s]
Capturing batches (bs=104 avail_mem=8.71 GB):  30%|███       | 7/23 [00:03<00:06,  2.53it/s]
Capturing batches (bs=104 avail_mem=8.71 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.59it/s]
Capturing batches (bs=96 avail_mem=8.47 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.59it/s] 
Capturing batches (bs=112 avail_mem=8.76 GB):  30%|███       | 7/23 [00:03<00:06,  2.32it/s]
Capturing batches (bs=104 avail_mem=8.71 GB):  30%|███       | 7/23 [00:03<00:06,  2.32it/s]
Capturing batches (bs=96 avail_mem=8.47 GB):  39%|███▉      | 9/23 [00:04<00:05,  2.64it/s]
Capturing batches (bs=88 avail_mem=8.42 GB):  39%|███▉      | 9/23 [00:04<00:05,  2.64it/s]
Capturing batches (bs=104 avail_mem=8.71 GB):  35%|███▍      | 8/23 [00:03<00:06,  2.42it/s]
Capturing batches (bs=96 avail_mem=8.47 GB):  35%|███▍      | 8/23 [00:03<00:06,  2.42it/s] 
Capturing batches (bs=88 avail_mem=8.42 GB):  43%|████▎     | 10/23 [00:04<00:04,  2.70it/s]
Capturing batches (bs=80 avail_mem=8.38 GB):  43%|████▎     | 10/23 [00:04<00:04,  2.70it/s]
Capturing batches (bs=96 avail_mem=8.47 GB):  39%|███▉      | 9/23 [00:04<00:05,  2.46it/s]
Capturing batches (bs=88 avail_mem=8.42 GB):  39%|███▉      | 9/23 [00:04<00:05,  2.46it/s]
Capturing batches (bs=80 avail_mem=8.38 GB):  48%|████▊     | 11/23 [00:04<00:04,  2.70it/s]
Capturing batches (bs=72 avail_mem=8.18 GB):  48%|████▊     | 11/23 [00:04<00:04,  2.70it/s]
Capturing batches (bs=88 avail_mem=8.42 GB):  43%|████▎     | 10/23 [00:04<00:05,  2.53it/s]
Capturing batches (bs=80 avail_mem=8.38 GB):  43%|████▎     | 10/23 [00:04<00:05,  2.53it/s]
Capturing batches (bs=72 avail_mem=8.18 GB):  52%|█████▏    | 12/23 [00:05<00:03,  2.75it/s]
Capturing batches (bs=64 avail_mem=8.14 GB):  52%|█████▏    | 12/23 [00:05<00:03,  2.75it/s]
Capturing batches (bs=80 avail_mem=8.38 GB):  48%|████▊     | 11/23 [00:05<00:04,  2.59it/s]
Capturing batches (bs=72 avail_mem=8.18 GB):  48%|████▊     | 11/23 [00:05<00:04,  2.59it/s]
Capturing batches (bs=64 avail_mem=8.14 GB):  57%|█████▋    | 13/23 [00:05<00:03,  2.70it/s]
Capturing batches (bs=56 avail_mem=8.10 GB):  57%|█████▋    | 13/23 [00:05<00:03,  2.70it/s]
Capturing batches (bs=72 avail_mem=8.18 GB):  52%|█████▏    | 12/23 [00:05<00:04,  2.50it/s]
Capturing batches (bs=64 avail_mem=8.14 GB):  52%|█████▏    | 12/23 [00:05<00:04,  2.50it/s]
Capturing batches (bs=56 avail_mem=8.10 GB):  61%|██████    | 14/23 [00:05<00:03,  2.53it/s]
Capturing batches (bs=48 avail_mem=8.06 GB):  61%|██████    | 14/23 [00:05<00:03,  2.53it/s]
Capturing batches (bs=64 avail_mem=8.14 GB):  57%|█████▋    | 13/23 [00:05<00:04,  2.49it/s]
Capturing batches (bs=56 avail_mem=7.69 GB):  57%|█████▋    | 13/23 [00:05<00:04,  2.49it/s]
Capturing batches (bs=48 avail_mem=8.06 GB):  65%|██████▌   | 15/23 [00:06<00:03,  2.53it/s]
Capturing batches (bs=40 avail_mem=7.92 GB):  65%|██████▌   | 15/23 [00:06<00:03,  2.53it/s]
Capturing batches (bs=56 avail_mem=7.69 GB):  61%|██████    | 14/23 [00:06<00:03,  2.43it/s]
Capturing batches (bs=48 avail_mem=8.06 GB):  61%|██████    | 14/23 [00:06<00:03,  2.43it/s]
Capturing batches (bs=40 avail_mem=7.92 GB):  70%|██████▉   | 16/23 [00:06<00:02,  2.61it/s]
Capturing batches (bs=32 avail_mem=7.88 GB):  70%|██████▉   | 16/23 [00:06<00:02,  2.61it/s]
Capturing batches (bs=48 avail_mem=8.06 GB):  65%|██████▌   | 15/23 [00:06<00:03,  2.45it/s]
Capturing batches (bs=40 avail_mem=7.92 GB):  65%|██████▌   | 15/23 [00:06<00:03,  2.45it/s]
Capturing batches (bs=32 avail_mem=7.88 GB):  74%|███████▍  | 17/23 [00:07<00:02,  2.65it/s]
Capturing batches (bs=24 avail_mem=7.84 GB):  74%|███████▍  | 17/23 [00:07<00:02,  2.65it/s]
Capturing batches (bs=40 avail_mem=7.92 GB):  70%|██████▉   | 16/23 [00:07<00:02,  2.53it/s]
Capturing batches (bs=32 avail_mem=7.88 GB):  70%|██████▉   | 16/23 [00:07<00:02,  2.53it/s]
Capturing batches (bs=24 avail_mem=7.84 GB):  78%|███████▊  | 18/23 [00:07<00:01,  2.66it/s]
Capturing batches (bs=16 avail_mem=7.80 GB):  78%|███████▊  | 18/23 [00:07<00:01,  2.66it/s]
Capturing batches (bs=32 avail_mem=7.88 GB):  74%|███████▍  | 17/23 [00:07<00:02,  2.63it/s]
Capturing batches (bs=24 avail_mem=7.84 GB):  74%|███████▍  | 17/23 [00:07<00:02,  2.63it/s]
Capturing batches (bs=16 avail_mem=7.80 GB):  83%|████████▎ | 19/23 [00:07<00:01,  2.56it/s]
Capturing batches (bs=8 avail_mem=7.75 GB):  83%|████████▎ | 19/23 [00:07<00:01,  2.56it/s] 
Capturing batches (bs=24 avail_mem=7.84 GB):  78%|███████▊  | 18/23 [00:07<00:01,  2.71it/s]
Capturing batches (bs=16 avail_mem=7.80 GB):  78%|███████▊  | 18/23 [00:07<00:01,  2.71it/s]
Capturing batches (bs=8 avail_mem=7.75 GB):  87%|████████▋ | 20/23 [00:08<00:01,  2.58it/s]
Capturing batches (bs=4 avail_mem=7.71 GB):  87%|████████▋ | 20/23 [00:08<00:01,  2.58it/s]
Capturing batches (bs=16 avail_mem=7.80 GB):  83%|████████▎ | 19/23 [00:08<00:01,  2.76it/s]
Capturing batches (bs=8 avail_mem=7.75 GB):  83%|████████▎ | 19/23 [00:08<00:01,  2.76it/s] 
Capturing batches (bs=4 avail_mem=7.71 GB):  91%|█████████▏| 21/23 [00:08<00:00,  2.66it/s]
Capturing batches (bs=2 avail_mem=7.67 GB):  91%|█████████▏| 21/23 [00:08<00:00,  2.66it/s]
Capturing batches (bs=8 avail_mem=7.75 GB):  87%|████████▋ | 20/23 [00:08<00:01,  2.76it/s]
Capturing batches (bs=4 avail_mem=7.71 GB):  87%|████████▋ | 20/23 [00:08<00:01,  2.76it/s]
Capturing batches (bs=2 avail_mem=7.67 GB):  96%|█████████▌| 22/23 [00:08<00:00,  2.72it/s]
Capturing batches (bs=1 avail_mem=7.63 GB):  96%|█████████▌| 22/23 [00:08<00:00,  2.72it/s]
Capturing batches (bs=4 avail_mem=7.71 GB):  91%|█████████▏| 21/23 [00:08<00:00,  2.68it/s]
Capturing batches (bs=2 avail_mem=7.67 GB):  91%|█████████▏| 21/23 [00:08<00:00,  2.68it/s]
Capturing batches (bs=1 avail_mem=7.63 GB): 100%|██████████| 23/23 [00:09<00:00,  2.77it/s]
Capturing batches (bs=1 avail_mem=7.63 GB): 100%|██████████| 23/23 [00:09<00:00,  2.49it/s]
[2025-07-27 23:59:14 DP1 TP0] Registering 1196 cuda graph addresses
[2025-07-27 23:59:14 DP1 TP2] Registering 1196 cuda graph addresses
[2025-07-27 23:59:14 DP1 TP3] Registering 1196 cuda graph addresses
[2025-07-27 23:59:14 DP1 TP1] Registering 1196 cuda graph addresses
[2025-07-27 23:59:14 DP1 TP0] Capture cuda graph end. Time elapsed: 9.43 s. mem usage=3.02 GB. avail mem=7.58 GB.

Capturing batches (bs=2 avail_mem=7.67 GB):  96%|█████████▌| 22/23 [00:09<00:00,  2.60it/s]
Capturing batches (bs=1 avail_mem=7.63 GB):  96%|█████████▌| 22/23 [00:09<00:00,  2.60it/s]
Capturing batches (bs=1 avail_mem=7.63 GB): 100%|██████████| 23/23 [00:09<00:00,  2.54it/s]
Capturing batches (bs=1 avail_mem=7.63 GB): 100%|██████████| 23/23 [00:09<00:00,  2.37it/s]
[2025-07-27 23:59:14 DP0 TP0] Registering 1196 cuda graph addresses
[2025-07-27 23:59:14 DP0 TP3] Registering 1196 cuda graph addresses
[2025-07-27 23:59:14 DP0 TP1] Registering 1196 cuda graph addresses
[2025-07-27 23:59:14 DP0 TP2] Registering 1196 cuda graph addresses
[2025-07-27 23:59:14 DP0 TP0] Capture cuda graph end. Time elapsed: 9.84 s. mem usage=3.02 GB. avail mem=7.58 GB.
[2025-07-27 23:59:15 DP1 TP0] max_total_num_tokens=3178032, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=7.58 GB
[2025-07-27 23:59:16 DP0 TP0] max_total_num_tokens=3178032, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=7.58 GB
[2025-07-27 23:59:17] INFO:     Started server process [1697538]
[2025-07-27 23:59:17] INFO:     Waiting for application startup.
[2025-07-27 23:59:17] INFO:     Application startup complete.
[2025-07-27 23:59:17] INFO:     Uvicorn running on http://127.0.0.1:8002 (Press CTRL+C to quit)
[2025-07-27 23:59:18] INFO:     127.0.0.1:60524 - "GET /health HTTP/1.1" 200 OK
[2025-07-27 23:59:18 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 251, #cached-token: 0, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-27 23:59:18 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 213, #cached-token: 0, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-27 23:59:18] INFO:     127.0.0.1:60548 - "GET /get_model_info HTTP/1.1" 200 OK
[2025-07-27 23:59:19 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 1, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-27 23:59:19 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 1, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-27 23:59:20] INFO:     127.0.0.1:60550 - "POST /generate HTTP/1.1" 200 OK
[2025-07-27 23:59:20] The server is fired up and ready to roll!
[2025-07-27 23:59:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 292, full token usage: 0.00, #swa token: 292, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 11.27, #queue-req: 0, 
[2025-07-27 23:59:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 254, full token usage: 0.00, #swa token: 254, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 9.57, #queue-req: 0, 
[2025-07-27 23:59:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 332, full token usage: 0.00, #swa token: 332, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 153.81, #queue-req: 0, 
[2025-07-27 23:59:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 294, full token usage: 0.00, #swa token: 294, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 147.74, #queue-req: 0, 
[2025-07-27 23:59:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 372, full token usage: 0.00, #swa token: 372, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 151.79, #queue-req: 0, 
[2025-07-27 23:59:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 334, full token usage: 0.00, #swa token: 334, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 153.46, #queue-req: 0, 
[2025-07-27 23:59:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 412, full token usage: 0.00, #swa token: 412, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 152.11, #queue-req: 0, 
[2025-07-27 23:59:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 374, full token usage: 0.00, #swa token: 374, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.45, #queue-req: 0, 
[2025-07-27 23:59:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 452, full token usage: 0.00, #swa token: 452, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.56, #queue-req: 0, 
[2025-07-27 23:59:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 414, full token usage: 0.00, #swa token: 414, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.00, #queue-req: 0, 
[2025-07-27 23:59:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 492, full token usage: 0.00, #swa token: 492, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 145.61, #queue-req: 0, 
[2025-07-27 23:59:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 454, full token usage: 0.00, #swa token: 454, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.54, #queue-req: 0, 
[2025-07-27 23:59:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 532, full token usage: 0.00, #swa token: 532, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.06, #queue-req: 0, 
[2025-07-27 23:59:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 494, full token usage: 0.00, #swa token: 494, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.96, #queue-req: 0, 
[2025-07-27 23:59:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 572, full token usage: 0.00, #swa token: 572, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.92, #queue-req: 0, 
[2025-07-27 23:59:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 534, full token usage: 0.00, #swa token: 534, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 153.74, #queue-req: 0, 
[2025-07-27 23:59:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 612, full token usage: 0.00, #swa token: 612, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 153.65, #queue-req: 0, 
[2025-07-27 23:59:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 574, full token usage: 0.00, #swa token: 574, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 153.92, #queue-req: 0, 
[2025-07-27 23:59:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 652, full token usage: 0.00, #swa token: 652, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 153.64, #queue-req: 0, 
[2025-07-27 23:59:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 614, full token usage: 0.00, #swa token: 614, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 153.94, #queue-req: 0, 
[2025-07-27 23:59:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 692, full token usage: 0.00, #swa token: 692, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 151.74, #queue-req: 0, 
[2025-07-27 23:59:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 654, full token usage: 0.00, #swa token: 654, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.27, #queue-req: 0, 
[2025-07-27 23:59:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 732, full token usage: 0.00, #swa token: 732, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 150.36, #queue-req: 0, 
[2025-07-27 23:59:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 694, full token usage: 0.00, #swa token: 694, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.01, #queue-req: 0, 
[2025-07-27 23:59:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 772, full token usage: 0.00, #swa token: 772, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 151.97, #queue-req: 0, 
[2025-07-27 23:59:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 734, full token usage: 0.00, #swa token: 734, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 153.15, #queue-req: 0, 
[2025-07-27 23:59:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 812, full token usage: 0.00, #swa token: 812, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.04, #queue-req: 0, 
[2025-07-27 23:59:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 774, full token usage: 0.00, #swa token: 774, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.14, #queue-req: 0, 
[2025-07-27 23:59:24] INFO:     127.0.0.1:60532 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-27 23:59:24 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 93, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-27 23:59:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 200, full token usage: 0.00, #swa token: 200, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 121.12, #queue-req: 0, 
[2025-07-27 23:59:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 814, full token usage: 0.00, #swa token: 814, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 153.82, #queue-req: 0, 
[2025-07-27 23:59:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 240, full token usage: 0.00, #swa token: 240, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 153.26, #queue-req: 0, 
[2025-07-27 23:59:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 854, full token usage: 0.00, #swa token: 854, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 151.60, #queue-req: 0, 
[2025-07-27 23:59:24] INFO:     127.0.0.1:60544 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-27 23:59:24 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 212, #cached-token: 98, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-27 23:59:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 280, full token usage: 0.00, #swa token: 280, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 153.87, #queue-req: 0, 
[2025-07-27 23:59:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 333, full token usage: 0.00, #swa token: 333, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 115.77, #queue-req: 0, 
[2025-07-27 23:59:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 320, full token usage: 0.00, #swa token: 320, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.81, #queue-req: 0, 
[2025-07-27 23:59:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 373, full token usage: 0.00, #swa token: 373, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.67, #queue-req: 0, 
[2025-07-27 23:59:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 360, full token usage: 0.00, #swa token: 360, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.18, #queue-req: 0, 
[2025-07-27 23:59:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 413, full token usage: 0.00, #swa token: 413, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.16, #queue-req: 0, 
[2025-07-27 23:59:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 400, full token usage: 0.00, #swa token: 400, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.78, #queue-req: 0, 
[2025-07-27 23:59:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 453, full token usage: 0.00, #swa token: 453, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.72, #queue-req: 0, 
[2025-07-27 23:59:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 440, full token usage: 0.00, #swa token: 440, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.86, #queue-req: 0, 
[2025-07-27 23:59:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 493, full token usage: 0.00, #swa token: 493, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.04, #queue-req: 0, 
[2025-07-27 23:59:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 480, full token usage: 0.00, #swa token: 480, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.36, #queue-req: 0, 
[2025-07-27 23:59:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 533, full token usage: 0.00, #swa token: 533, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.98, #queue-req: 0, 
[2025-07-27 23:59:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 520, full token usage: 0.00, #swa token: 520, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.81, #queue-req: 0, 
[2025-07-27 23:59:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 573, full token usage: 0.00, #swa token: 573, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.89, #queue-req: 0, 
[2025-07-27 23:59:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 560, full token usage: 0.00, #swa token: 560, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.62, #queue-req: 0, 
[2025-07-27 23:59:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 613, full token usage: 0.00, #swa token: 613, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.89, #queue-req: 0, 
[2025-07-27 23:59:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 600, full token usage: 0.00, #swa token: 600, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.42, #queue-req: 0, 
[2025-07-27 23:59:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 653, full token usage: 0.00, #swa token: 653, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.76, #queue-req: 0, 
[2025-07-27 23:59:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 640, full token usage: 0.00, #swa token: 640, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.32, #queue-req: 0, 
[2025-07-27 23:59:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 693, full token usage: 0.00, #swa token: 693, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.46, #queue-req: 0, 
[2025-07-27 23:59:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 680, full token usage: 0.00, #swa token: 680, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.41, #queue-req: 0, 
[2025-07-27 23:59:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 733, full token usage: 0.00, #swa token: 733, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.84, #queue-req: 0, 
[2025-07-27 23:59:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 720, full token usage: 0.00, #swa token: 720, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.17, #queue-req: 0, 
[2025-07-27 23:59:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 773, full token usage: 0.00, #swa token: 773, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.51, #queue-req: 0, 
[2025-07-27 23:59:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 760, full token usage: 0.00, #swa token: 760, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.87, #queue-req: 0, 
[2025-07-27 23:59:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 813, full token usage: 0.00, #swa token: 813, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.22, #queue-req: 0, 
[2025-07-27 23:59:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 800, full token usage: 0.00, #swa token: 800, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.01, #queue-req: 0, 
[2025-07-27 23:59:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 853, full token usage: 0.00, #swa token: 853, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.23, #queue-req: 0, 
[2025-07-27 23:59:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 840, full token usage: 0.00, #swa token: 840, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 153.24, #queue-req: 0, 
[2025-07-27 23:59:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 893, full token usage: 0.00, #swa token: 893, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.33, #queue-req: 0, 
[2025-07-27 23:59:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 880, full token usage: 0.00, #swa token: 880, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.94, #queue-req: 0, 
[2025-07-27 23:59:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 933, full token usage: 0.00, #swa token: 933, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.99, #queue-req: 0, 
[2025-07-27 23:59:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 920, full token usage: 0.00, #swa token: 920, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.43, #queue-req: 0, 
[2025-07-27 23:59:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 973, full token usage: 0.00, #swa token: 973, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.64, #queue-req: 0, 
[2025-07-27 23:59:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 960, full token usage: 0.00, #swa token: 960, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.47, #queue-req: 0, 
[2025-07-27 23:59:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 1013, full token usage: 0.00, #swa token: 1013, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.56, #queue-req: 0, 
[2025-07-27 23:59:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 1000, full token usage: 0.00, #swa token: 1000, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.77, #queue-req: 0, 
[2025-07-27 23:59:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 1053, full token usage: 0.00, #swa token: 1053, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.74, #queue-req: 0, 
[2025-07-27 23:59:29] INFO:     127.0.0.1:47062 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-27 23:59:29 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 134, #cached-token: 98, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-27 23:59:29] INFO:     127.0.0.1:47072 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-27 23:59:29 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 136, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-27 23:59:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 243, full token usage: 0.00, #swa token: 243, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 120.28, #queue-req: 0, 
[2025-07-27 23:59:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 256, full token usage: 0.00, #swa token: 256, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 122.92, #queue-req: 0, 
[2025-07-27 23:59:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 283, full token usage: 0.00, #swa token: 283, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.57, #queue-req: 0, 
[2025-07-27 23:59:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 296, full token usage: 0.00, #swa token: 296, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.92, #queue-req: 0, 
[2025-07-27 23:59:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 323, full token usage: 0.00, #swa token: 323, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.73, #queue-req: 0, 
[2025-07-27 23:59:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 336, full token usage: 0.00, #swa token: 336, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.99, #queue-req: 0, 
[2025-07-27 23:59:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 363, full token usage: 0.00, #swa token: 363, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.65, #queue-req: 0, 
[2025-07-27 23:59:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 376, full token usage: 0.00, #swa token: 376, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.95, #queue-req: 0, 
[2025-07-27 23:59:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 403, full token usage: 0.00, #swa token: 403, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.59, #queue-req: 0, 
[2025-07-27 23:59:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 416, full token usage: 0.00, #swa token: 416, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.00, #queue-req: 0, 
[2025-07-27 23:59:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 443, full token usage: 0.00, #swa token: 443, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.00, #queue-req: 0, 
[2025-07-27 23:59:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 456, full token usage: 0.00, #swa token: 456, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.62, #queue-req: 0, 
[2025-07-27 23:59:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 483, full token usage: 0.00, #swa token: 483, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.36, #queue-req: 0, 
[2025-07-27 23:59:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 496, full token usage: 0.00, #swa token: 496, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.96, #queue-req: 0, 
[2025-07-27 23:59:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 523, full token usage: 0.00, #swa token: 523, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.40, #queue-req: 0, 
[2025-07-27 23:59:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 536, full token usage: 0.00, #swa token: 536, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.94, #queue-req: 0, 
[2025-07-27 23:59:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 563, full token usage: 0.00, #swa token: 563, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.75, #queue-req: 0, 
[2025-07-27 23:59:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 576, full token usage: 0.00, #swa token: 576, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.59, #queue-req: 0, 
[2025-07-27 23:59:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 603, full token usage: 0.00, #swa token: 603, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.63, #queue-req: 0, 
[2025-07-27 23:59:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 616, full token usage: 0.00, #swa token: 616, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.69, #queue-req: 0, 
[2025-07-27 23:59:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 643, full token usage: 0.00, #swa token: 643, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.77, #queue-req: 0, 
[2025-07-27 23:59:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 656, full token usage: 0.00, #swa token: 656, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.04, #queue-req: 0, 
[2025-07-27 23:59:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 683, full token usage: 0.00, #swa token: 683, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.36, #queue-req: 0, 
[2025-07-27 23:59:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 696, full token usage: 0.00, #swa token: 696, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.56, #queue-req: 0, 
[2025-07-27 23:59:32] INFO:     127.0.0.1:47076 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-27 23:59:32 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 193, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-27 23:59:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 313, full token usage: 0.00, #swa token: 313, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 121.01, #queue-req: 0, 
[2025-07-27 23:59:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 736, full token usage: 0.00, #swa token: 736, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.44, #queue-req: 0, 
[2025-07-27 23:59:33 DP0 TP0] Decode batch. #running-req: 1, #full token: 353, full token usage: 0.00, #swa token: 353, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.40, #queue-req: 0, 
[2025-07-27 23:59:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 776, full token usage: 0.00, #swa token: 776, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.45, #queue-req: 0, 
[2025-07-27 23:59:33 DP0 TP0] Decode batch. #running-req: 1, #full token: 393, full token usage: 0.00, #swa token: 393, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.57, #queue-req: 0, 
[2025-07-27 23:59:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 816, full token usage: 0.00, #swa token: 816, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.19, #queue-req: 0, 
[2025-07-27 23:59:33 DP0 TP0] Decode batch. #running-req: 1, #full token: 433, full token usage: 0.00, #swa token: 433, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.81, #queue-req: 0, 
[2025-07-27 23:59:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 856, full token usage: 0.00, #swa token: 856, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.89, #queue-req: 0, 
[2025-07-27 23:59:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 473, full token usage: 0.00, #swa token: 473, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.33, #queue-req: 0, 
[2025-07-27 23:59:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 896, full token usage: 0.00, #swa token: 896, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.62, #queue-req: 0, 
[2025-07-27 23:59:34] INFO:     127.0.0.1:47078 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-27 23:59:34 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 181, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-27 23:59:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 513, full token usage: 0.00, #swa token: 513, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 153.44, #queue-req: 0, 
[2025-07-27 23:59:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 293, full token usage: 0.00, #swa token: 293, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 118.39, #queue-req: 0, 
[2025-07-27 23:59:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 553, full token usage: 0.00, #swa token: 553, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 153.20, #queue-req: 0, 
[2025-07-27 23:59:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 333, full token usage: 0.00, #swa token: 333, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.02, #queue-req: 0, 
[2025-07-27 23:59:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 593, full token usage: 0.00, #swa token: 593, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.41, #queue-req: 0, 
[2025-07-27 23:59:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 373, full token usage: 0.00, #swa token: 373, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.11, #queue-req: 0, 
[2025-07-27 23:59:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 633, full token usage: 0.00, #swa token: 633, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.04, #queue-req: 0, 
[2025-07-27 23:59:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 413, full token usage: 0.00, #swa token: 413, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.15, #queue-req: 0, 
[2025-07-27 23:59:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 673, full token usage: 0.00, #swa token: 673, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.30, #queue-req: 0, 
[2025-07-27 23:59:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 453, full token usage: 0.00, #swa token: 453, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.86, #queue-req: 0, 
[2025-07-27 23:59:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 713, full token usage: 0.00, #swa token: 713, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 147.04, #queue-req: 0, 
[2025-07-27 23:59:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 493, full token usage: 0.00, #swa token: 493, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.13, #queue-req: 0, 
[2025-07-27 23:59:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 753, full token usage: 0.00, #swa token: 753, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.29, #queue-req: 0, 
[2025-07-27 23:59:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 533, full token usage: 0.00, #swa token: 533, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.76, #queue-req: 0, 
[2025-07-27 23:59:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 793, full token usage: 0.00, #swa token: 793, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 150.24, #queue-req: 0, 
[2025-07-27 23:59:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 573, full token usage: 0.00, #swa token: 573, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.82, #queue-req: 0, 
[2025-07-27 23:59:36] INFO:     127.0.0.1:47570 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-27 23:59:36 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 222, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-27 23:59:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 613, full token usage: 0.00, #swa token: 613, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.77, #queue-req: 0, 
[2025-07-27 23:59:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 344, full token usage: 0.00, #swa token: 344, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.96, #queue-req: 0, 
[2025-07-27 23:59:36] INFO:     127.0.0.1:47582 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-27 23:59:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 0, full token usage: 0.00, #swa token: 0, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.68, #queue-req: 0, 
[2025-07-27 23:59:36 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 173, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-27 23:59:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 384, full token usage: 0.00, #swa token: 384, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.13, #queue-req: 0, 
[2025-07-27 23:59:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 424, full token usage: 0.00, #swa token: 424, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.53, #queue-req: 0, 
[2025-07-27 23:59:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 314, full token usage: 0.00, #swa token: 314, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 125.65, #queue-req: 0, 
[2025-07-27 23:59:37 DP0 TP0] Decode batch. #running-req: 1, #full token: 464, full token usage: 0.00, #swa token: 464, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.92, #queue-req: 0, 
[2025-07-27 23:59:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 354, full token usage: 0.00, #swa token: 354, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.78, #queue-req: 0, 
[2025-07-27 23:59:37 DP0 TP0] Decode batch. #running-req: 1, #full token: 504, full token usage: 0.00, #swa token: 504, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.62, #queue-req: 0, 
[2025-07-27 23:59:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 394, full token usage: 0.00, #swa token: 394, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.87, #queue-req: 0, 
[2025-07-27 23:59:37 DP0 TP0] Decode batch. #running-req: 1, #full token: 544, full token usage: 0.00, #swa token: 544, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.57, #queue-req: 0, 
[2025-07-27 23:59:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 434, full token usage: 0.00, #swa token: 434, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.69, #queue-req: 0, 
[2025-07-27 23:59:37 DP0 TP0] Decode batch. #running-req: 1, #full token: 584, full token usage: 0.00, #swa token: 584, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.61, #queue-req: 0, 
[2025-07-27 23:59:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 474, full token usage: 0.00, #swa token: 474, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.73, #queue-req: 0, 
[2025-07-27 23:59:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 624, full token usage: 0.00, #swa token: 624, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.73, #queue-req: 0, 
[2025-07-27 23:59:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 514, full token usage: 0.00, #swa token: 514, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.77, #queue-req: 0, 
[2025-07-27 23:59:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 664, full token usage: 0.00, #swa token: 664, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.51, #queue-req: 0, 
[2025-07-27 23:59:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 554, full token usage: 0.00, #swa token: 554, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.78, #queue-req: 0, 
[2025-07-27 23:59:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 704, full token usage: 0.00, #swa token: 704, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.46, #queue-req: 0, 
[2025-07-27 23:59:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 594, full token usage: 0.00, #swa token: 594, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.80, #queue-req: 0, 
[2025-07-27 23:59:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 744, full token usage: 0.00, #swa token: 744, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.61, #queue-req: 0, 
[2025-07-27 23:59:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 634, full token usage: 0.00, #swa token: 634, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.82, #queue-req: 0, 
[2025-07-27 23:59:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 784, full token usage: 0.00, #swa token: 784, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.63, #queue-req: 0, 
[2025-07-27 23:59:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 674, full token usage: 0.00, #swa token: 674, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.76, #queue-req: 0, 
[2025-07-27 23:59:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 824, full token usage: 0.00, #swa token: 824, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.49, #queue-req: 0, 
[2025-07-27 23:59:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 714, full token usage: 0.00, #swa token: 714, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.71, #queue-req: 0, 
[2025-07-27 23:59:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 864, full token usage: 0.00, #swa token: 864, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.77, #queue-req: 0, 
[2025-07-27 23:59:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 754, full token usage: 0.00, #swa token: 754, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.74, #queue-req: 0, 
[2025-07-27 23:59:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 904, full token usage: 0.00, #swa token: 904, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.30, #queue-req: 0, 
[2025-07-27 23:59:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 794, full token usage: 0.00, #swa token: 794, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.78, #queue-req: 0, 
[2025-07-27 23:59:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 944, full token usage: 0.00, #swa token: 944, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.56, #queue-req: 0, 
[2025-07-27 23:59:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 834, full token usage: 0.00, #swa token: 834, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.41, #queue-req: 0, 
[2025-07-27 23:59:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 984, full token usage: 0.00, #swa token: 984, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.63, #queue-req: 0, 
[2025-07-27 23:59:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 874, full token usage: 0.00, #swa token: 874, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.56, #queue-req: 0, 
[2025-07-27 23:59:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 1024, full token usage: 0.00, #swa token: 1024, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.75, #queue-req: 0, 
[2025-07-27 23:59:40] INFO:     127.0.0.1:47584 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-27 23:59:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 914, full token usage: 0.00, #swa token: 914, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.81, #queue-req: 0, 
[2025-07-27 23:59:40 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 158, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-27 23:59:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 954, full token usage: 0.00, #swa token: 954, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.43, #queue-req: 0, 
[2025-07-27 23:59:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 290, full token usage: 0.00, #swa token: 290, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 125.55, #queue-req: 0, 
[2025-07-27 23:59:41] INFO:     127.0.0.1:47596 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-27 23:59:41 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 149, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-27 23:59:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 330, full token usage: 0.00, #swa token: 330, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.93, #queue-req: 0, 
[2025-07-27 23:59:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 271, full token usage: 0.00, #swa token: 271, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 122.06, #queue-req: 0, 
[2025-07-27 23:59:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 370, full token usage: 0.00, #swa token: 370, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.76, #queue-req: 0, 
[2025-07-27 23:59:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 311, full token usage: 0.00, #swa token: 311, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.41, #queue-req: 0, 
[2025-07-27 23:59:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 410, full token usage: 0.00, #swa token: 410, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.47, #queue-req: 0, 
[2025-07-27 23:59:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 351, full token usage: 0.00, #swa token: 351, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.72, #queue-req: 0, 
[2025-07-27 23:59:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 450, full token usage: 0.00, #swa token: 450, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.86, #queue-req: 0, 
[2025-07-27 23:59:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 391, full token usage: 0.00, #swa token: 391, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.97, #queue-req: 0, 
[2025-07-27 23:59:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 490, full token usage: 0.00, #swa token: 490, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.50, #queue-req: 0, 
[2025-07-27 23:59:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 431, full token usage: 0.00, #swa token: 431, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.04, #queue-req: 0, 
[2025-07-27 23:59:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 530, full token usage: 0.00, #swa token: 530, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.44, #queue-req: 0, 
[2025-07-27 23:59:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 471, full token usage: 0.00, #swa token: 471, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.79, #queue-req: 0, 
[2025-07-27 23:59:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 570, full token usage: 0.00, #swa token: 570, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.79, #queue-req: 0, 
[2025-07-27 23:59:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 511, full token usage: 0.00, #swa token: 511, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.84, #queue-req: 0, 
[2025-07-27 23:59:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 610, full token usage: 0.00, #swa token: 610, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.83, #queue-req: 0, 
[2025-07-27 23:59:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 551, full token usage: 0.00, #swa token: 551, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.90, #queue-req: 0, 
[2025-07-27 23:59:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 650, full token usage: 0.00, #swa token: 650, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.58, #queue-req: 0, 
[2025-07-27 23:59:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 591, full token usage: 0.00, #swa token: 591, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.17, #queue-req: 0, 
[2025-07-27 23:59:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 690, full token usage: 0.00, #swa token: 690, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.67, #queue-req: 0, 
[2025-07-27 23:59:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 631, full token usage: 0.00, #swa token: 631, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.35, #queue-req: 0, 
[2025-07-27 23:59:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 730, full token usage: 0.00, #swa token: 730, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.84, #queue-req: 0, 
[2025-07-27 23:59:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 671, full token usage: 0.00, #swa token: 671, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.46, #queue-req: 0, 
[2025-07-27 23:59:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 770, full token usage: 0.00, #swa token: 770, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.28, #queue-req: 0, 
[2025-07-27 23:59:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 711, full token usage: 0.00, #swa token: 711, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.81, #queue-req: 0, 
[2025-07-27 23:59:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 810, full token usage: 0.00, #swa token: 810, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.97, #queue-req: 0, 
[2025-07-27 23:59:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 751, full token usage: 0.00, #swa token: 751, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.69, #queue-req: 0, 
[2025-07-27 23:59:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 850, full token usage: 0.00, #swa token: 850, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.51, #queue-req: 0, 
[2025-07-27 23:59:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 791, full token usage: 0.00, #swa token: 791, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.59, #queue-req: 0, 
[2025-07-27 23:59:44] INFO:     127.0.0.1:47598 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-27 23:59:44 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 186, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-27 23:59:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 831, full token usage: 0.00, #swa token: 831, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.65, #queue-req: 0, 
[2025-07-27 23:59:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 307, full token usage: 0.00, #swa token: 307, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 122.71, #queue-req: 0, 
[2025-07-27 23:59:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 871, full token usage: 0.00, #swa token: 871, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.63, #queue-req: 0, 
[2025-07-27 23:59:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 347, full token usage: 0.00, #swa token: 347, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.97, #queue-req: 0, 
[2025-07-27 23:59:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 911, full token usage: 0.00, #swa token: 911, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.65, #queue-req: 0, 
[2025-07-27 23:59:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 387, full token usage: 0.00, #swa token: 387, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.81, #queue-req: 0, 
[2025-07-27 23:59:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 951, full token usage: 0.00, #swa token: 951, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.51, #queue-req: 0, 
[2025-07-27 23:59:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 427, full token usage: 0.00, #swa token: 427, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.99, #queue-req: 0, 
[2025-07-27 23:59:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 991, full token usage: 0.00, #swa token: 991, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.54, #queue-req: 0, 
[2025-07-27 23:59:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 467, full token usage: 0.00, #swa token: 467, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.31, #queue-req: 0, 
[2025-07-27 23:59:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 1031, full token usage: 0.00, #swa token: 1031, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.81, #queue-req: 0, 
[2025-07-27 23:59:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 507, full token usage: 0.00, #swa token: 507, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.94, #queue-req: 0, 
[2025-07-27 23:59:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 1071, full token usage: 0.00, #swa token: 1071, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.77, #queue-req: 0, 
[2025-07-27 23:59:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 547, full token usage: 0.00, #swa token: 547, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.90, #queue-req: 0, 
[2025-07-27 23:59:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 1111, full token usage: 0.00, #swa token: 1111, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.68, #queue-req: 0, 
[2025-07-27 23:59:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 587, full token usage: 0.00, #swa token: 587, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.81, #queue-req: 0, 
[2025-07-27 23:59:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 1151, full token usage: 0.00, #swa token: 1151, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.76, #queue-req: 0, 
[2025-07-27 23:59:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 627, full token usage: 0.00, #swa token: 627, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.24, #queue-req: 0, 
[2025-07-27 23:59:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 1191, full token usage: 0.00, #swa token: 1191, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.62, #queue-req: 0, 
[2025-07-27 23:59:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 667, full token usage: 0.00, #swa token: 667, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.70, #queue-req: 0, 
[2025-07-27 23:59:47] INFO:     127.0.0.1:47608 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-27 23:59:47 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 196, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-27 23:59:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 707, full token usage: 0.00, #swa token: 707, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.53, #queue-req: 0, 
[2025-07-27 23:59:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 315, full token usage: 0.00, #swa token: 315, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 124.00, #queue-req: 0, 
[2025-07-27 23:59:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 747, full token usage: 0.00, #swa token: 747, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.28, #queue-req: 0, 
[2025-07-27 23:59:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 355, full token usage: 0.00, #swa token: 355, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.88, #queue-req: 0, 
[2025-07-27 23:59:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 787, full token usage: 0.00, #swa token: 787, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.66, #queue-req: 0, 
[2025-07-27 23:59:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 395, full token usage: 0.00, #swa token: 395, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.80, #queue-req: 0, 
[2025-07-27 23:59:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 827, full token usage: 0.00, #swa token: 827, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.70, #queue-req: 0, 
[2025-07-27 23:59:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 435, full token usage: 0.00, #swa token: 435, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.86, #queue-req: 0, 
[2025-07-27 23:59:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 867, full token usage: 0.00, #swa token: 867, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.88, #queue-req: 0, 
[2025-07-27 23:59:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 475, full token usage: 0.00, #swa token: 475, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.92, #queue-req: 0, 
[2025-07-27 23:59:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 907, full token usage: 0.00, #swa token: 907, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.89, #queue-req: 0, 
[2025-07-27 23:59:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 515, full token usage: 0.00, #swa token: 515, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.99, #queue-req: 0, 
[2025-07-27 23:59:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 947, full token usage: 0.00, #swa token: 947, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.95, #queue-req: 0, 
[2025-07-27 23:59:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 555, full token usage: 0.00, #swa token: 555, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.72, #queue-req: 0, 
[2025-07-27 23:59:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 987, full token usage: 0.00, #swa token: 987, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.00, #queue-req: 0, 
[2025-07-27 23:59:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 595, full token usage: 0.00, #swa token: 595, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.58, #queue-req: 0, 
[2025-07-27 23:59:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 1027, full token usage: 0.00, #swa token: 1027, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.63, #queue-req: 0, 
[2025-07-27 23:59:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 635, full token usage: 0.00, #swa token: 635, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.96, #queue-req: 0, 
[2025-07-27 23:59:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 1067, full token usage: 0.00, #swa token: 1067, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 153.21, #queue-req: 0, 
[2025-07-27 23:59:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 675, full token usage: 0.00, #swa token: 675, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.87, #queue-req: 0, 
[2025-07-27 23:59:50 DP0 TP0] Decode batch. #running-req: 1, #full token: 1107, full token usage: 0.00, #swa token: 1107, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.44, #queue-req: 0, 
[2025-07-27 23:59:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 715, full token usage: 0.00, #swa token: 715, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.63, #queue-req: 0, 
[2025-07-27 23:59:50 DP0 TP0] Decode batch. #running-req: 1, #full token: 1147, full token usage: 0.00, #swa token: 1147, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.62, #queue-req: 0, 
[2025-07-27 23:59:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 755, full token usage: 0.00, #swa token: 755, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.79, #queue-req: 0, 
[2025-07-27 23:59:50 DP0 TP0] Decode batch. #running-req: 1, #full token: 1187, full token usage: 0.00, #swa token: 1187, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.19, #queue-req: 0, 
[2025-07-27 23:59:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 795, full token usage: 0.00, #swa token: 795, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.67, #queue-req: 0, 
[2025-07-27 23:59:50 DP0 TP0] Decode batch. #running-req: 1, #full token: 1227, full token usage: 0.00, #swa token: 1227, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.55, #queue-req: 0, 
[2025-07-27 23:59:51] INFO:     127.0.0.1:56684 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-27 23:59:51 DP1 TP0] Decode batch. #running-req: 1, #full token: 835, full token usage: 0.00, #swa token: 835, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.78, #queue-req: 0, 
[2025-07-27 23:59:51 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-27 23:59:51 DP1 TP0] Decode batch. #running-req: 1, #full token: 875, full token usage: 0.00, #swa token: 875, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.01, #queue-req: 0, 
[2025-07-27 23:59:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 277, full token usage: 0.00, #swa token: 277, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 122.73, #queue-req: 0, 
[2025-07-27 23:59:51 DP1 TP0] Decode batch. #running-req: 1, #full token: 915, full token usage: 0.00, #swa token: 915, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.64, #queue-req: 0, 
[2025-07-27 23:59:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 317, full token usage: 0.00, #swa token: 317, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.99, #queue-req: 0, 
[2025-07-27 23:59:51 DP1 TP0] Decode batch. #running-req: 1, #full token: 955, full token usage: 0.00, #swa token: 955, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.91, #queue-req: 0, 
[2025-07-27 23:59:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 357, full token usage: 0.00, #swa token: 357, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.83, #queue-req: 0, 
[2025-07-27 23:59:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 995, full token usage: 0.00, #swa token: 995, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.88, #queue-req: 0, 
[2025-07-27 23:59:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 397, full token usage: 0.00, #swa token: 397, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.95, #queue-req: 0, 
[2025-07-27 23:59:52] INFO:     127.0.0.1:56692 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-27 23:59:52 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 134, #cached-token: 101, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-27 23:59:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 437, full token usage: 0.00, #swa token: 437, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.65, #queue-req: 0, 
[2025-07-27 23:59:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 266, full token usage: 0.00, #swa token: 266, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 123.50, #queue-req: 0, 
[2025-07-27 23:59:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 477, full token usage: 0.00, #swa token: 477, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.92, #queue-req: 0, 
[2025-07-27 23:59:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 306, full token usage: 0.00, #swa token: 306, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.11, #queue-req: 0, 
[2025-07-27 23:59:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 517, full token usage: 0.00, #swa token: 517, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.58, #queue-req: 0, 
[2025-07-27 23:59:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 346, full token usage: 0.00, #swa token: 346, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.26, #queue-req: 0, 
[2025-07-27 23:59:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 557, full token usage: 0.00, #swa token: 557, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.71, #queue-req: 0, 
[2025-07-27 23:59:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 386, full token usage: 0.00, #swa token: 386, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.16, #queue-req: 0, 
[2025-07-27 23:59:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 597, full token usage: 0.00, #swa token: 597, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.36, #queue-req: 0, 
[2025-07-27 23:59:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 426, full token usage: 0.00, #swa token: 426, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.32, #queue-req: 0, 
[2025-07-27 23:59:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 637, full token usage: 0.00, #swa token: 637, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.90, #queue-req: 0, 
[2025-07-27 23:59:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 466, full token usage: 0.00, #swa token: 466, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.22, #queue-req: 0, 
[2025-07-27 23:59:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 677, full token usage: 0.00, #swa token: 677, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.03, #queue-req: 0, 
[2025-07-27 23:59:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 506, full token usage: 0.00, #swa token: 506, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.23, #queue-req: 0, 
[2025-07-27 23:59:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 717, full token usage: 0.00, #swa token: 717, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.11, #queue-req: 0, 
[2025-07-27 23:59:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 546, full token usage: 0.00, #swa token: 546, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.41, #queue-req: 0, 
[2025-07-27 23:59:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 757, full token usage: 0.00, #swa token: 757, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.87, #queue-req: 0, 
[2025-07-27 23:59:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 586, full token usage: 0.00, #swa token: 586, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.94, #queue-req: 0, 
[2025-07-27 23:59:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 797, full token usage: 0.00, #swa token: 797, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.92, #queue-req: 0, 
[2025-07-27 23:59:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 626, full token usage: 0.00, #swa token: 626, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.11, #queue-req: 0, 
[2025-07-27 23:59:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 837, full token usage: 0.00, #swa token: 837, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.86, #queue-req: 0, 
[2025-07-27 23:59:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 666, full token usage: 0.00, #swa token: 666, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.18, #queue-req: 0, 
[2025-07-27 23:59:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 877, full token usage: 0.00, #swa token: 877, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.65, #queue-req: 0, 
[2025-07-27 23:59:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 706, full token usage: 0.00, #swa token: 706, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.90, #queue-req: 0, 
[2025-07-27 23:59:55] INFO:     127.0.0.1:56706 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-27 23:59:55 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 200, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-27 23:59:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 746, full token usage: 0.00, #swa token: 746, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.66, #queue-req: 0, 
[2025-07-27 23:59:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 319, full token usage: 0.00, #swa token: 319, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 123.16, #queue-req: 0, 
[2025-07-27 23:59:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 786, full token usage: 0.00, #swa token: 786, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.48, #queue-req: 0, 
[2025-07-27 23:59:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 359, full token usage: 0.00, #swa token: 359, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.99, #queue-req: 0, 
[2025-07-27 23:59:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 826, full token usage: 0.00, #swa token: 826, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.95, #queue-req: 0, 
[2025-07-27 23:59:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 399, full token usage: 0.00, #swa token: 399, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.04, #queue-req: 0, 
[2025-07-27 23:59:56 DP1 TP0] Decode batch. #running-req: 1, #full token: 866, full token usage: 0.00, #swa token: 866, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.05, #queue-req: 0, 
[2025-07-27 23:59:56 DP0 TP0] Decode batch. #running-req: 1, #full token: 439, full token usage: 0.00, #swa token: 439, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.11, #queue-req: 0, 
[2025-07-27 23:59:56 DP1 TP0] Decode batch. #running-req: 1, #full token: 906, full token usage: 0.00, #swa token: 906, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.83, #queue-req: 0, 
[2025-07-27 23:59:56 DP0 TP0] Decode batch. #running-req: 1, #full token: 479, full token usage: 0.00, #swa token: 479, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.19, #queue-req: 0, 
[2025-07-27 23:59:56 DP1 TP0] Decode batch. #running-req: 1, #full token: 946, full token usage: 0.00, #swa token: 946, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.92, #queue-req: 0, 
[2025-07-27 23:59:56 DP0 TP0] Decode batch. #running-req: 1, #full token: 519, full token usage: 0.00, #swa token: 519, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.84, #queue-req: 0, 
[2025-07-27 23:59:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 986, full token usage: 0.00, #swa token: 986, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.59, #queue-req: 0, 
[2025-07-27 23:59:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 559, full token usage: 0.00, #swa token: 559, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.94, #queue-req: 0, 
[2025-07-27 23:59:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 1026, full token usage: 0.00, #swa token: 1026, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.60, #queue-req: 0, 
[2025-07-27 23:59:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 599, full token usage: 0.00, #swa token: 599, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.67, #queue-req: 0, 
[2025-07-27 23:59:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 1066, full token usage: 0.00, #swa token: 1066, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.27, #queue-req: 0, 
[2025-07-27 23:59:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 639, full token usage: 0.00, #swa token: 639, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.88, #queue-req: 0, 
[2025-07-27 23:59:57] INFO:     127.0.0.1:56718 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-27 23:59:57 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 189, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-27 23:59:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 679, full token usage: 0.00, #swa token: 679, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.82, #queue-req: 0, 
[2025-07-27 23:59:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 290, full token usage: 0.00, #swa token: 290, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 123.41, #queue-req: 0, 
[2025-07-27 23:59:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 719, full token usage: 0.00, #swa token: 719, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.71, #queue-req: 0, 
[2025-07-27 23:59:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 330, full token usage: 0.00, #swa token: 330, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.61, #queue-req: 0, 
[2025-07-27 23:59:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 759, full token usage: 0.00, #swa token: 759, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.97, #queue-req: 0, 
[2025-07-27 23:59:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 370, full token usage: 0.00, #swa token: 370, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.83, #queue-req: 0, 
[2025-07-27 23:59:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 799, full token usage: 0.00, #swa token: 799, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.94, #queue-req: 0, 
[2025-07-27 23:59:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 410, full token usage: 0.00, #swa token: 410, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.96, #queue-req: 0, 
[2025-07-27 23:59:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 839, full token usage: 0.00, #swa token: 839, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.02, #queue-req: 0, 
[2025-07-27 23:59:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 450, full token usage: 0.00, #swa token: 450, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.23, #queue-req: 0, 
[2025-07-27 23:59:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 879, full token usage: 0.00, #swa token: 879, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.95, #queue-req: 0, 
[2025-07-27 23:59:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 490, full token usage: 0.00, #swa token: 490, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.99, #queue-req: 0, 
[2025-07-27 23:59:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 919, full token usage: 0.00, #swa token: 919, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.04, #queue-req: 0, 
[2025-07-27 23:59:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 530, full token usage: 0.00, #swa token: 530, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.79, #queue-req: 0, 
[2025-07-27 23:59:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 959, full token usage: 0.00, #swa token: 959, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.95, #queue-req: 0, 
[2025-07-27 23:59:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 570, full token usage: 0.00, #swa token: 570, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.42, #queue-req: 0, 
[2025-07-27 23:59:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 999, full token usage: 0.00, #swa token: 999, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.87, #queue-req: 0, 
[2025-07-27 23:59:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 610, full token usage: 0.00, #swa token: 610, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.68, #queue-req: 0, 
[2025-07-28 00:00:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 1039, full token usage: 0.00, #swa token: 1039, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.90, #queue-req: 0, 
[2025-07-28 00:00:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 650, full token usage: 0.00, #swa token: 650, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.79, #queue-req: 0, 
[2025-07-28 00:00:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 1079, full token usage: 0.00, #swa token: 1079, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.83, #queue-req: 0, 
[2025-07-28 00:00:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 690, full token usage: 0.00, #swa token: 690, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.87, #queue-req: 0, 
[2025-07-28 00:00:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 1119, full token usage: 0.00, #swa token: 1119, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.71, #queue-req: 0, 
[2025-07-28 00:00:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 730, full token usage: 0.00, #swa token: 730, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.65, #queue-req: 0, 
[2025-07-28 00:00:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 1159, full token usage: 0.00, #swa token: 1159, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.96, #queue-req: 0, 
[2025-07-28 00:00:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 770, full token usage: 0.00, #swa token: 770, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.89, #queue-req: 0, 
[2025-07-28 00:00:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 1199, full token usage: 0.00, #swa token: 1199, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.68, #queue-req: 0, 
[2025-07-28 00:00:01 DP1 TP0] Decode batch. #running-req: 1, #full token: 810, full token usage: 0.00, #swa token: 810, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.70, #queue-req: 0, 
[2025-07-28 00:00:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 1239, full token usage: 0.00, #swa token: 1239, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.82, #queue-req: 0, 
[2025-07-28 00:00:01 DP1 TP0] Decode batch. #running-req: 1, #full token: 850, full token usage: 0.00, #swa token: 850, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.92, #queue-req: 0, 
[2025-07-28 00:00:01] INFO:     127.0.0.1:33978 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:01 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 175, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:00:01 DP0 TP0] Decode batch. #running-req: 2, #full token: 1457, full token usage: 0.00, #swa token: 1457, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.62, #queue-req: 0, 
[2025-07-28 00:00:01] INFO:     127.0.0.1:33964 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:01 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 177, #cached-token: 101, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:00:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 317, full token usage: 0.00, #swa token: 317, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.79, #queue-req: 0, 
[2025-07-28 00:00:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 291, full token usage: 0.00, #swa token: 291, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 62.15, #queue-req: 0, 
[2025-07-28 00:00:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 357, full token usage: 0.00, #swa token: 357, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.09, #queue-req: 0, 
[2025-07-28 00:00:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 331, full token usage: 0.00, #swa token: 331, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 152.04, #queue-req: 0, 
[2025-07-28 00:00:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 397, full token usage: 0.00, #swa token: 397, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.80, #queue-req: 0, 
[2025-07-28 00:00:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 371, full token usage: 0.00, #swa token: 371, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.70, #queue-req: 0, 
[2025-07-28 00:00:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 437, full token usage: 0.00, #swa token: 437, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.80, #queue-req: 0, 
[2025-07-28 00:00:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 411, full token usage: 0.00, #swa token: 411, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.09, #queue-req: 0, 
[2025-07-28 00:00:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 477, full token usage: 0.00, #swa token: 477, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.86, #queue-req: 0, 
[2025-07-28 00:00:03 DP1 TP0] Decode batch. #running-req: 1, #full token: 451, full token usage: 0.00, #swa token: 451, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.20, #queue-req: 0, 
[2025-07-28 00:00:03 DP0 TP0] Decode batch. #running-req: 1, #full token: 517, full token usage: 0.00, #swa token: 517, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.80, #queue-req: 0, 
[2025-07-28 00:00:03 DP1 TP0] Decode batch. #running-req: 1, #full token: 491, full token usage: 0.00, #swa token: 491, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.11, #queue-req: 0, 
[2025-07-28 00:00:03 DP0 TP0] Decode batch. #running-req: 1, #full token: 557, full token usage: 0.00, #swa token: 557, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.67, #queue-req: 0, 
[2025-07-28 00:00:03 DP1 TP0] Decode batch. #running-req: 1, #full token: 531, full token usage: 0.00, #swa token: 531, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.16, #queue-req: 0, 
[2025-07-28 00:00:03 DP0 TP0] Decode batch. #running-req: 1, #full token: 597, full token usage: 0.00, #swa token: 597, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.68, #queue-req: 0, 
[2025-07-28 00:00:03 DP1 TP0] Decode batch. #running-req: 1, #full token: 571, full token usage: 0.00, #swa token: 571, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.86, #queue-req: 0, 
[2025-07-28 00:00:04 DP0 TP0] Decode batch. #running-req: 1, #full token: 637, full token usage: 0.00, #swa token: 637, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.83, #queue-req: 0, 
[2025-07-28 00:00:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 611, full token usage: 0.00, #swa token: 611, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.10, #queue-req: 0, 
[2025-07-28 00:00:04 DP0 TP0] Decode batch. #running-req: 1, #full token: 677, full token usage: 0.00, #swa token: 677, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.80, #queue-req: 0, 
[2025-07-28 00:00:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 651, full token usage: 0.00, #swa token: 651, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.93, #queue-req: 0, 
[2025-07-28 00:00:04 DP0 TP0] Decode batch. #running-req: 1, #full token: 717, full token usage: 0.00, #swa token: 717, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.66, #queue-req: 0, 
[2025-07-28 00:00:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 691, full token usage: 0.00, #swa token: 691, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.65, #queue-req: 0, 
[2025-07-28 00:00:04 DP0 TP0] Decode batch. #running-req: 1, #full token: 757, full token usage: 0.00, #swa token: 757, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.82, #queue-req: 0, 
[2025-07-28 00:00:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 731, full token usage: 0.00, #swa token: 731, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.00, #queue-req: 0, 
[2025-07-28 00:00:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 797, full token usage: 0.00, #swa token: 797, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.71, #queue-req: 0, 
[2025-07-28 00:00:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 771, full token usage: 0.00, #swa token: 771, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.98, #queue-req: 0, 
[2025-07-28 00:00:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 837, full token usage: 0.00, #swa token: 837, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.90, #queue-req: 0, 
[2025-07-28 00:00:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 811, full token usage: 0.00, #swa token: 811, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.85, #queue-req: 0, 
[2025-07-28 00:00:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 877, full token usage: 0.00, #swa token: 877, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.89, #queue-req: 0, 
[2025-07-28 00:00:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 851, full token usage: 0.00, #swa token: 851, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.78, #queue-req: 0, 
[2025-07-28 00:00:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 917, full token usage: 0.00, #swa token: 917, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.91, #queue-req: 0, 
[2025-07-28 00:00:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 891, full token usage: 0.00, #swa token: 891, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.08, #queue-req: 0, 
[2025-07-28 00:00:06 DP0 TP0] Decode batch. #running-req: 1, #full token: 957, full token usage: 0.00, #swa token: 957, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.00, #queue-req: 0, 
[2025-07-28 00:00:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 931, full token usage: 0.00, #swa token: 931, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.71, #queue-req: 0, 
[2025-07-28 00:00:06] INFO:     127.0.0.1:33982 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:06 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 210, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:00:06 DP0 TP0] Decode batch. #running-req: 1, #full token: 313, full token usage: 0.00, #swa token: 313, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 123.52, #queue-req: 0, 
[2025-07-28 00:00:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 971, full token usage: 0.00, #swa token: 971, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.03, #queue-req: 0, 
[2025-07-28 00:00:06] INFO:     127.0.0.1:33992 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:06 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 161, #cached-token: 102, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:00:06 DP0 TP0] Decode batch. #running-req: 1, #full token: 353, full token usage: 0.00, #swa token: 353, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.77, #queue-req: 0, 
[2025-07-28 00:00:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 289, full token usage: 0.00, #swa token: 289, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 124.96, #queue-req: 0, 
[2025-07-28 00:00:06 DP0 TP0] Decode batch. #running-req: 1, #full token: 393, full token usage: 0.00, #swa token: 393, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.97, #queue-req: 0, 
[2025-07-28 00:00:07 DP1 TP0] Decode batch. #running-req: 1, #full token: 329, full token usage: 0.00, #swa token: 329, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.14, #queue-req: 0, 
[2025-07-28 00:00:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 433, full token usage: 0.00, #swa token: 433, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.83, #queue-req: 0, 
[2025-07-28 00:00:07 DP1 TP0] Decode batch. #running-req: 1, #full token: 369, full token usage: 0.00, #swa token: 369, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.00, #queue-req: 0, 
[2025-07-28 00:00:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 473, full token usage: 0.00, #swa token: 473, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.20, #queue-req: 0, 
[2025-07-28 00:00:07 DP1 TP0] Decode batch. #running-req: 1, #full token: 409, full token usage: 0.00, #swa token: 409, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.13, #queue-req: 0, 
[2025-07-28 00:00:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 513, full token usage: 0.00, #swa token: 513, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.98, #queue-req: 0, 
[2025-07-28 00:00:07 DP1 TP0] Decode batch. #running-req: 1, #full token: 449, full token usage: 0.00, #swa token: 449, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.05, #queue-req: 0, 
[2025-07-28 00:00:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 553, full token usage: 0.00, #swa token: 553, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.03, #queue-req: 0, 
[2025-07-28 00:00:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 489, full token usage: 0.00, #swa token: 489, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.01, #queue-req: 0, 
[2025-07-28 00:00:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 593, full token usage: 0.00, #swa token: 593, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 146.64, #queue-req: 0, 
[2025-07-28 00:00:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 529, full token usage: 0.00, #swa token: 529, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.89, #queue-req: 0, 
[2025-07-28 00:00:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 633, full token usage: 0.00, #swa token: 633, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 151.17, #queue-req: 0, 
[2025-07-28 00:00:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 569, full token usage: 0.00, #swa token: 569, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.99, #queue-req: 0, 
[2025-07-28 00:00:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 673, full token usage: 0.00, #swa token: 673, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.09, #queue-req: 0, 
[2025-07-28 00:00:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 609, full token usage: 0.00, #swa token: 609, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.00, #queue-req: 0, 
[2025-07-28 00:00:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 713, full token usage: 0.00, #swa token: 713, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.83, #queue-req: 0, 
[2025-07-28 00:00:09 DP1 TP0] Decode batch. #running-req: 1, #full token: 649, full token usage: 0.00, #swa token: 649, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.97, #queue-req: 0, 
[2025-07-28 00:00:09 DP0 TP0] Decode batch. #running-req: 1, #full token: 753, full token usage: 0.00, #swa token: 753, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.55, #queue-req: 0, 
[2025-07-28 00:00:09 DP1 TP0] Decode batch. #running-req: 1, #full token: 689, full token usage: 0.00, #swa token: 689, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.67, #queue-req: 0, 
[2025-07-28 00:00:09 DP0 TP0] Decode batch. #running-req: 1, #full token: 793, full token usage: 0.00, #swa token: 793, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.00, #queue-req: 0, 
[2025-07-28 00:00:09] INFO:     127.0.0.1:51520 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:09 DP1 TP0] Decode batch. #running-req: 1, #full token: 729, full token usage: 0.00, #swa token: 729, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.00, #queue-req: 0, 
[2025-07-28 00:00:09 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 172, #cached-token: 102, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:00:09 DP0 TP0] Decode batch. #running-req: 1, #full token: 303, full token usage: 0.00, #swa token: 303, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 125.45, #queue-req: 0, 
[2025-07-28 00:00:09 DP1 TP0] Decode batch. #running-req: 1, #full token: 769, full token usage: 0.00, #swa token: 769, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.01, #queue-req: 0, 
[2025-07-28 00:00:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 343, full token usage: 0.00, #swa token: 343, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.11, #queue-req: 0, 
[2025-07-28 00:00:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 809, full token usage: 0.00, #swa token: 809, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.91, #queue-req: 0, 
[2025-07-28 00:00:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 383, full token usage: 0.00, #swa token: 383, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.10, #queue-req: 0, 
[2025-07-28 00:00:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 849, full token usage: 0.00, #swa token: 849, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.92, #queue-req: 0, 
[2025-07-28 00:00:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 423, full token usage: 0.00, #swa token: 423, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.10, #queue-req: 0, 
[2025-07-28 00:00:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 889, full token usage: 0.00, #swa token: 889, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.72, #queue-req: 0, 
[2025-07-28 00:00:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 463, full token usage: 0.00, #swa token: 463, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.98, #queue-req: 0, 
[2025-07-28 00:00:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 929, full token usage: 0.00, #swa token: 929, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.00, #queue-req: 0, 
[2025-07-28 00:00:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 503, full token usage: 0.00, #swa token: 503, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.91, #queue-req: 0, 
[2025-07-28 00:00:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 969, full token usage: 0.00, #swa token: 969, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.05, #queue-req: 0, 
[2025-07-28 00:00:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 543, full token usage: 0.00, #swa token: 543, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.04, #queue-req: 0, 
[2025-07-28 00:00:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 1009, full token usage: 0.00, #swa token: 1009, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.48, #queue-req: 0, 
[2025-07-28 00:00:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 583, full token usage: 0.00, #swa token: 583, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.01, #queue-req: 0, 
[2025-07-28 00:00:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 1049, full token usage: 0.00, #swa token: 1049, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.02, #queue-req: 0, 
[2025-07-28 00:00:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 623, full token usage: 0.00, #swa token: 623, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.64, #queue-req: 0, 
[2025-07-28 00:00:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 1089, full token usage: 0.00, #swa token: 1089, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.88, #queue-req: 0, 
[2025-07-28 00:00:12] INFO:     127.0.0.1:51536 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:12 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 102, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:00:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 663, full token usage: 0.00, #swa token: 663, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.84, #queue-req: 0, 
[2025-07-28 00:00:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 218, full token usage: 0.00, #swa token: 218, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 125.48, #queue-req: 0, 
[2025-07-28 00:00:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 703, full token usage: 0.00, #swa token: 703, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 149.98, #queue-req: 0, 
[2025-07-28 00:00:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 258, full token usage: 0.00, #swa token: 258, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.34, #queue-req: 0, 
[2025-07-28 00:00:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 743, full token usage: 0.00, #swa token: 743, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.12, #queue-req: 0, 
[2025-07-28 00:00:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 298, full token usage: 0.00, #swa token: 298, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.39, #queue-req: 0, 
[2025-07-28 00:00:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 783, full token usage: 0.00, #swa token: 783, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.80, #queue-req: 0, 
[2025-07-28 00:00:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 338, full token usage: 0.00, #swa token: 338, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.87, #queue-req: 0, 
[2025-07-28 00:00:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 823, full token usage: 0.00, #swa token: 823, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.12, #queue-req: 0, 
[2025-07-28 00:00:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 378, full token usage: 0.00, #swa token: 378, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.28, #queue-req: 0, 
[2025-07-28 00:00:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 863, full token usage: 0.00, #swa token: 863, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.91, #queue-req: 0, 
[2025-07-28 00:00:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 418, full token usage: 0.00, #swa token: 418, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.02, #queue-req: 0, 
[2025-07-28 00:00:13] INFO:     127.0.0.1:51548 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:13 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 120, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:00:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 243, full token usage: 0.00, #swa token: 243, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 126.66, #queue-req: 0, 
[2025-07-28 00:00:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 458, full token usage: 0.00, #swa token: 458, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.31, #queue-req: 0, 
[2025-07-28 00:00:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 283, full token usage: 0.00, #swa token: 283, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.13, #queue-req: 0, 
[2025-07-28 00:00:14 DP1 TP0] Decode batch. #running-req: 1, #full token: 498, full token usage: 0.00, #swa token: 498, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.20, #queue-req: 0, 
[2025-07-28 00:00:14 DP0 TP0] Decode batch. #running-req: 1, #full token: 323, full token usage: 0.00, #swa token: 323, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.08, #queue-req: 0, 
[2025-07-28 00:00:14 DP1 TP0] Decode batch. #running-req: 1, #full token: 538, full token usage: 0.00, #swa token: 538, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.91, #queue-req: 0, 
[2025-07-28 00:00:14 DP0 TP0] Decode batch. #running-req: 1, #full token: 363, full token usage: 0.00, #swa token: 363, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.20, #queue-req: 0, 
[2025-07-28 00:00:14 DP1 TP0] Decode batch. #running-req: 1, #full token: 578, full token usage: 0.00, #swa token: 578, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.43, #queue-req: 0, 
[2025-07-28 00:00:14 DP0 TP0] Decode batch. #running-req: 1, #full token: 403, full token usage: 0.00, #swa token: 403, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.85, #queue-req: 0, 
[2025-07-28 00:00:14 DP1 TP0] Decode batch. #running-req: 1, #full token: 618, full token usage: 0.00, #swa token: 618, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.07, #queue-req: 0, 
[2025-07-28 00:00:15 DP0 TP0] Decode batch. #running-req: 1, #full token: 443, full token usage: 0.00, #swa token: 443, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.11, #queue-req: 0, 
[2025-07-28 00:00:15 DP1 TP0] Decode batch. #running-req: 1, #full token: 658, full token usage: 0.00, #swa token: 658, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.01, #queue-req: 0, 
[2025-07-28 00:00:15 DP0 TP0] Decode batch. #running-req: 1, #full token: 483, full token usage: 0.00, #swa token: 483, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.93, #queue-req: 0, 
[2025-07-28 00:00:15 DP1 TP0] Decode batch. #running-req: 1, #full token: 698, full token usage: 0.00, #swa token: 698, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.66, #queue-req: 0, 
[2025-07-28 00:00:15 DP0 TP0] Decode batch. #running-req: 1, #full token: 523, full token usage: 0.00, #swa token: 523, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.08, #queue-req: 0, 
[2025-07-28 00:00:15 DP1 TP0] Decode batch. #running-req: 1, #full token: 738, full token usage: 0.00, #swa token: 738, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.03, #queue-req: 0, 
[2025-07-28 00:00:15 DP0 TP0] Decode batch. #running-req: 1, #full token: 563, full token usage: 0.00, #swa token: 563, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.02, #queue-req: 0, 
[2025-07-28 00:00:15 DP1 TP0] Decode batch. #running-req: 1, #full token: 778, full token usage: 0.00, #swa token: 778, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.13, #queue-req: 0, 
[2025-07-28 00:00:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 603, full token usage: 0.00, #swa token: 603, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.82, #queue-req: 0, 
[2025-07-28 00:00:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 818, full token usage: 0.00, #swa token: 818, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.97, #queue-req: 0, 
[2025-07-28 00:00:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 643, full token usage: 0.00, #swa token: 643, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.10, #queue-req: 0, 
[2025-07-28 00:00:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 858, full token usage: 0.00, #swa token: 858, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.06, #queue-req: 0, 
[2025-07-28 00:00:16] INFO:     127.0.0.1:51562 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:16 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 152, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:00:16] INFO:     127.0.0.1:37052 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:16 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 153, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:00:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 266, full token usage: 0.00, #swa token: 266, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 127.38, #queue-req: 0, 
[2025-07-28 00:00:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 282, full token usage: 0.00, #swa token: 282, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 122.84, #queue-req: 0, 
[2025-07-28 00:00:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 306, full token usage: 0.00, #swa token: 306, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.08, #queue-req: 0, 
[2025-07-28 00:00:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 322, full token usage: 0.00, #swa token: 322, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.32, #queue-req: 0, 
[2025-07-28 00:00:17 DP0 TP0] Decode batch. #running-req: 1, #full token: 346, full token usage: 0.00, #swa token: 346, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.12, #queue-req: 0, 
[2025-07-28 00:00:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 362, full token usage: 0.00, #swa token: 362, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.38, #queue-req: 0, 
[2025-07-28 00:00:17 DP0 TP0] Decode batch. #running-req: 1, #full token: 386, full token usage: 0.00, #swa token: 386, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.07, #queue-req: 0, 
[2025-07-28 00:00:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 402, full token usage: 0.00, #swa token: 402, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.01, #queue-req: 0, 
[2025-07-28 00:00:17 DP0 TP0] Decode batch. #running-req: 1, #full token: 426, full token usage: 0.00, #swa token: 426, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.11, #queue-req: 0, 
[2025-07-28 00:00:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 442, full token usage: 0.00, #swa token: 442, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.29, #queue-req: 0, 
[2025-07-28 00:00:17 DP0 TP0] Decode batch. #running-req: 1, #full token: 466, full token usage: 0.00, #swa token: 466, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.15, #queue-req: 0, 
[2025-07-28 00:00:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 482, full token usage: 0.00, #swa token: 482, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.15, #queue-req: 0, 
[2025-07-28 00:00:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 506, full token usage: 0.00, #swa token: 506, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.17, #queue-req: 0, 
[2025-07-28 00:00:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 522, full token usage: 0.00, #swa token: 522, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.02, #queue-req: 0, 
[2025-07-28 00:00:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 546, full token usage: 0.00, #swa token: 546, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.12, #queue-req: 0, 
[2025-07-28 00:00:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 562, full token usage: 0.00, #swa token: 562, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.92, #queue-req: 0, 
[2025-07-28 00:00:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 586, full token usage: 0.00, #swa token: 586, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.05, #queue-req: 0, 
[2025-07-28 00:00:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 602, full token usage: 0.00, #swa token: 602, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.42, #queue-req: 0, 
[2025-07-28 00:00:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 626, full token usage: 0.00, #swa token: 626, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.04, #queue-req: 0, 
[2025-07-28 00:00:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 642, full token usage: 0.00, #swa token: 642, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.09, #queue-req: 0, 
[2025-07-28 00:00:19 DP0 TP0] Decode batch. #running-req: 1, #full token: 666, full token usage: 0.00, #swa token: 666, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.17, #queue-req: 0, 
[2025-07-28 00:00:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 682, full token usage: 0.00, #swa token: 682, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.88, #queue-req: 0, 
[2025-07-28 00:00:19 DP0 TP0] Decode batch. #running-req: 1, #full token: 706, full token usage: 0.00, #swa token: 706, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.14, #queue-req: 0, 
[2025-07-28 00:00:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 722, full token usage: 0.00, #swa token: 722, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.83, #queue-req: 0, 
[2025-07-28 00:00:19 DP0 TP0] Decode batch. #running-req: 1, #full token: 746, full token usage: 0.00, #swa token: 746, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.19, #queue-req: 0, 
[2025-07-28 00:00:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 762, full token usage: 0.00, #swa token: 762, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.97, #queue-req: 0, 
[2025-07-28 00:00:19 DP0 TP0] Decode batch. #running-req: 1, #full token: 786, full token usage: 0.00, #swa token: 786, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.54, #queue-req: 0, 
[2025-07-28 00:00:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 802, full token usage: 0.00, #swa token: 802, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.61, #queue-req: 0, 
[2025-07-28 00:00:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 826, full token usage: 0.00, #swa token: 826, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.56, #queue-req: 0, 
[2025-07-28 00:00:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 842, full token usage: 0.00, #swa token: 842, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.06, #queue-req: 0, 
[2025-07-28 00:00:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 866, full token usage: 0.00, #swa token: 866, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.94, #queue-req: 0, 
[2025-07-28 00:00:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 882, full token usage: 0.00, #swa token: 882, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.75, #queue-req: 0, 
[2025-07-28 00:00:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 906, full token usage: 0.00, #swa token: 906, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.19, #queue-req: 0, 
[2025-07-28 00:00:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 922, full token usage: 0.00, #swa token: 922, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.92, #queue-req: 0, 
[2025-07-28 00:00:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 946, full token usage: 0.00, #swa token: 946, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.08, #queue-req: 0, 
[2025-07-28 00:00:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 962, full token usage: 0.00, #swa token: 962, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.01, #queue-req: 0, 
[2025-07-28 00:00:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 986, full token usage: 0.00, #swa token: 986, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.14, #queue-req: 0, 
[2025-07-28 00:00:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 1002, full token usage: 0.00, #swa token: 1002, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.03, #queue-req: 0, 
[2025-07-28 00:00:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 1026, full token usage: 0.00, #swa token: 1026, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.10, #queue-req: 0, 
[2025-07-28 00:00:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 1042, full token usage: 0.00, #swa token: 1042, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.13, #queue-req: 0, 
[2025-07-28 00:00:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 1066, full token usage: 0.00, #swa token: 1066, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.09, #queue-req: 0, 
[2025-07-28 00:00:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 1082, full token usage: 0.00, #swa token: 1082, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.72, #queue-req: 0, 
[2025-07-28 00:00:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 1106, full token usage: 0.00, #swa token: 1106, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.74, #queue-req: 0, 
[2025-07-28 00:00:22] INFO:     127.0.0.1:37062 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 1122, full token usage: 0.00, #swa token: 1122, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.04, #queue-req: 0, 
[2025-07-28 00:00:22 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 167, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:00:22] INFO:     127.0.0.1:37060 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:22 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 121, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:00:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 305, full token usage: 0.00, #swa token: 305, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 165.79, #queue-req: 0, 
[2025-07-28 00:00:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 258, full token usage: 0.00, #swa token: 258, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 79.86, #queue-req: 0, 
[2025-07-28 00:00:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 345, full token usage: 0.00, #swa token: 345, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.26, #queue-req: 0, 
[2025-07-28 00:00:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 298, full token usage: 0.00, #swa token: 298, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.46, #queue-req: 0, 
[2025-07-28 00:00:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 385, full token usage: 0.00, #swa token: 385, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.93, #queue-req: 0, 
[2025-07-28 00:00:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 338, full token usage: 0.00, #swa token: 338, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.16, #queue-req: 0, 
[2025-07-28 00:00:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 425, full token usage: 0.00, #swa token: 425, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.23, #queue-req: 0, 
[2025-07-28 00:00:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 378, full token usage: 0.00, #swa token: 378, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.11, #queue-req: 0, 
[2025-07-28 00:00:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 465, full token usage: 0.00, #swa token: 465, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.03, #queue-req: 0, 
[2025-07-28 00:00:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 418, full token usage: 0.00, #swa token: 418, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.08, #queue-req: 0, 
[2025-07-28 00:00:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 505, full token usage: 0.00, #swa token: 505, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.74, #queue-req: 0, 
[2025-07-28 00:00:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 458, full token usage: 0.00, #swa token: 458, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.11, #queue-req: 0, 
[2025-07-28 00:00:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 545, full token usage: 0.00, #swa token: 545, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.64, #queue-req: 0, 
[2025-07-28 00:00:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 498, full token usage: 0.00, #swa token: 498, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.96, #queue-req: 0, 
[2025-07-28 00:00:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 585, full token usage: 0.00, #swa token: 585, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.96, #queue-req: 0, 
[2025-07-28 00:00:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 538, full token usage: 0.00, #swa token: 538, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.92, #queue-req: 0, 
[2025-07-28 00:00:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 625, full token usage: 0.00, #swa token: 625, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.05, #queue-req: 0, 
[2025-07-28 00:00:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 578, full token usage: 0.00, #swa token: 578, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.86, #queue-req: 0, 
[2025-07-28 00:00:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 665, full token usage: 0.00, #swa token: 665, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.03, #queue-req: 0, 
[2025-07-28 00:00:24] INFO:     127.0.0.1:37070 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:24 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 89, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:00:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 618, full token usage: 0.00, #swa token: 618, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.85, #queue-req: 0, 
[2025-07-28 00:00:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 210, full token usage: 0.00, #swa token: 210, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 127.40, #queue-req: 0, 
[2025-07-28 00:00:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 658, full token usage: 0.00, #swa token: 658, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.99, #queue-req: 0, 
[2025-07-28 00:00:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 250, full token usage: 0.00, #swa token: 250, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.32, #queue-req: 0, 
[2025-07-28 00:00:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 698, full token usage: 0.00, #swa token: 698, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.91, #queue-req: 0, 
[2025-07-28 00:00:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 290, full token usage: 0.00, #swa token: 290, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.09, #queue-req: 0, 
[2025-07-28 00:00:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 738, full token usage: 0.00, #swa token: 738, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.14, #queue-req: 0, 
[2025-07-28 00:00:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 330, full token usage: 0.00, #swa token: 330, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.92, #queue-req: 0, 
[2025-07-28 00:00:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 778, full token usage: 0.00, #swa token: 778, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.86, #queue-req: 0, 
[2025-07-28 00:00:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 370, full token usage: 0.00, #swa token: 370, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.12, #queue-req: 0, 
[2025-07-28 00:00:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 818, full token usage: 0.00, #swa token: 818, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.02, #queue-req: 0, 
[2025-07-28 00:00:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 410, full token usage: 0.00, #swa token: 410, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.16, #queue-req: 0, 
[2025-07-28 00:00:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 858, full token usage: 0.00, #swa token: 858, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.85, #queue-req: 0, 
[2025-07-28 00:00:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 450, full token usage: 0.00, #swa token: 450, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.94, #queue-req: 0, 
[2025-07-28 00:00:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 898, full token usage: 0.00, #swa token: 898, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.61, #queue-req: 0, 
[2025-07-28 00:00:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 490, full token usage: 0.00, #swa token: 490, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.27, #queue-req: 0, 
[2025-07-28 00:00:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 938, full token usage: 0.00, #swa token: 938, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.33, #queue-req: 0, 
[2025-07-28 00:00:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 530, full token usage: 0.00, #swa token: 530, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.87, #queue-req: 0, 
[2025-07-28 00:00:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 978, full token usage: 0.00, #swa token: 978, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.87, #queue-req: 0, 
[2025-07-28 00:00:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 570, full token usage: 0.00, #swa token: 570, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.99, #queue-req: 0, 
[2025-07-28 00:00:27] INFO:     127.0.0.1:37076 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:27 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 134, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:00:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 238, full token usage: 0.00, #swa token: 238, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 123.95, #queue-req: 0, 
[2025-07-28 00:00:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 610, full token usage: 0.00, #swa token: 610, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.81, #queue-req: 0, 
[2025-07-28 00:00:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 278, full token usage: 0.00, #swa token: 278, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.97, #queue-req: 0, 
[2025-07-28 00:00:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 650, full token usage: 0.00, #swa token: 650, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.03, #queue-req: 0, 
[2025-07-28 00:00:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 318, full token usage: 0.00, #swa token: 318, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.99, #queue-req: 0, 
[2025-07-28 00:00:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 690, full token usage: 0.00, #swa token: 690, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.95, #queue-req: 0, 
[2025-07-28 00:00:28] INFO:     127.0.0.1:47056 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:28 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:00:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 358, full token usage: 0.00, #swa token: 358, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.98, #queue-req: 0, 
[2025-07-28 00:00:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 263, full token usage: 0.00, #swa token: 263, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 124.75, #queue-req: 0, 
[2025-07-28 00:00:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 398, full token usage: 0.00, #swa token: 398, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.88, #queue-req: 0, 
[2025-07-28 00:00:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 303, full token usage: 0.00, #swa token: 303, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.23, #queue-req: 0, 
[2025-07-28 00:00:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 438, full token usage: 0.00, #swa token: 438, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.13, #queue-req: 0, 
[2025-07-28 00:00:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 343, full token usage: 0.00, #swa token: 343, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.68, #queue-req: 0, 
[2025-07-28 00:00:28] INFO:     127.0.0.1:47062 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:28 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 93, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:00:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 207, full token usage: 0.00, #swa token: 207, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 128.82, #queue-req: 0, 
[2025-07-28 00:00:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 383, full token usage: 0.00, #swa token: 383, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.25, #queue-req: 0, 
[2025-07-28 00:00:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 247, full token usage: 0.00, #swa token: 247, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.59, #queue-req: 0, 
[2025-07-28 00:00:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 423, full token usage: 0.00, #swa token: 423, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.84, #queue-req: 0, 
[2025-07-28 00:00:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 287, full token usage: 0.00, #swa token: 287, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.12, #queue-req: 0, 
[2025-07-28 00:00:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 463, full token usage: 0.00, #swa token: 463, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.23, #queue-req: 0, 
[2025-07-28 00:00:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 327, full token usage: 0.00, #swa token: 327, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.16, #queue-req: 0, 
[2025-07-28 00:00:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 503, full token usage: 0.00, #swa token: 503, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.09, #queue-req: 0, 
[2025-07-28 00:00:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 367, full token usage: 0.00, #swa token: 367, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.24, #queue-req: 0, 
[2025-07-28 00:00:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 543, full token usage: 0.00, #swa token: 543, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.16, #queue-req: 0, 
[2025-07-28 00:00:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 407, full token usage: 0.00, #swa token: 407, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.13, #queue-req: 0, 
[2025-07-28 00:00:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 583, full token usage: 0.00, #swa token: 583, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.88, #queue-req: 0, 
[2025-07-28 00:00:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 447, full token usage: 0.00, #swa token: 447, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.15, #queue-req: 0, 
[2025-07-28 00:00:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 623, full token usage: 0.00, #swa token: 623, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.11, #queue-req: 0, 
[2025-07-28 00:00:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 487, full token usage: 0.00, #swa token: 487, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.13, #queue-req: 0, 
[2025-07-28 00:00:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 663, full token usage: 0.00, #swa token: 663, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.18, #queue-req: 0, 
[2025-07-28 00:00:31] INFO:     127.0.0.1:47066 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:31 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 167, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:00:31 DP1 TP0] Decode batch. #running-req: 2, #full token: 884, full token usage: 0.00, #swa token: 884, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 170.13, #queue-req: 0, 
[2025-07-28 00:00:31 DP1 TP0] Decode batch. #running-req: 2, #full token: 964, full token usage: 0.00, #swa token: 964, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.55, #queue-req: 0, 
[2025-07-28 00:00:31 DP1 TP0] Decode batch. #running-req: 2, #full token: 1044, full token usage: 0.00, #swa token: 1044, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.82, #queue-req: 0, 
[2025-07-28 00:00:32 DP1 TP0] Decode batch. #running-req: 2, #full token: 1124, full token usage: 0.00, #swa token: 1124, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.64, #queue-req: 0, 
[2025-07-28 00:00:32 DP1 TP0] Decode batch. #running-req: 2, #full token: 1204, full token usage: 0.00, #swa token: 1204, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.73, #queue-req: 0, 
[2025-07-28 00:00:32 DP1 TP0] Decode batch. #running-req: 2, #full token: 1284, full token usage: 0.00, #swa token: 1284, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.62, #queue-req: 0, 
[2025-07-28 00:00:32 DP1 TP0] Decode batch. #running-req: 2, #full token: 1364, full token usage: 0.00, #swa token: 1364, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.71, #queue-req: 0, 
[2025-07-28 00:00:33 DP1 TP0] Decode batch. #running-req: 2, #full token: 1444, full token usage: 0.00, #swa token: 1444, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.46, #queue-req: 0, 
[2025-07-28 00:00:33] INFO:     127.0.0.1:47064 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:33 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 152, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:00:33 DP0 TP0] Decode batch. #running-req: 1, #full token: 254, full token usage: 0.00, #swa token: 254, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 16.06, #queue-req: 0, 
[2025-07-28 00:00:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 598, full token usage: 0.00, #swa token: 598, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 170.21, #queue-req: 0, 
[2025-07-28 00:00:33 DP0 TP0] Decode batch. #running-req: 1, #full token: 294, full token usage: 0.00, #swa token: 294, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 132.39, #queue-req: 0, 
[2025-07-28 00:00:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 638, full token usage: 0.00, #swa token: 638, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.11, #queue-req: 0, 
[2025-07-28 00:00:33] INFO:     127.0.0.1:47078 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:33 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 117, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:00:33 DP0 TP0] Decode batch. #running-req: 1, #full token: 334, full token usage: 0.00, #swa token: 334, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.86, #queue-req: 0, 
[2025-07-28 00:00:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 246, full token usage: 0.00, #swa token: 246, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 124.82, #queue-req: 0, 
[2025-07-28 00:00:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 374, full token usage: 0.00, #swa token: 374, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.11, #queue-req: 0, 
[2025-07-28 00:00:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 286, full token usage: 0.00, #swa token: 286, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.18, #queue-req: 0, 
[2025-07-28 00:00:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 414, full token usage: 0.00, #swa token: 414, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.14, #queue-req: 0, 
[2025-07-28 00:00:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 326, full token usage: 0.00, #swa token: 326, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.23, #queue-req: 0, 
[2025-07-28 00:00:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 454, full token usage: 0.00, #swa token: 454, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.08, #queue-req: 0, 
[2025-07-28 00:00:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 366, full token usage: 0.00, #swa token: 366, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.21, #queue-req: 0, 
[2025-07-28 00:00:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 494, full token usage: 0.00, #swa token: 494, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.05, #queue-req: 0, 
[2025-07-28 00:00:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 406, full token usage: 0.00, #swa token: 406, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.19, #queue-req: 0, 
[2025-07-28 00:00:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 534, full token usage: 0.00, #swa token: 534, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.09, #queue-req: 0, 
[2025-07-28 00:00:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 446, full token usage: 0.00, #swa token: 446, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.18, #queue-req: 0, 
[2025-07-28 00:00:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 574, full token usage: 0.00, #swa token: 574, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.93, #queue-req: 0, 
[2025-07-28 00:00:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 486, full token usage: 0.00, #swa token: 486, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.04, #queue-req: 0, 
[2025-07-28 00:00:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 614, full token usage: 0.00, #swa token: 614, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.07, #queue-req: 0, 
[2025-07-28 00:00:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 526, full token usage: 0.00, #swa token: 526, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.00, #queue-req: 0, 
[2025-07-28 00:00:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 654, full token usage: 0.00, #swa token: 654, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.89, #queue-req: 0, 
[2025-07-28 00:00:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 566, full token usage: 0.00, #swa token: 566, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.94, #queue-req: 0, 
[2025-07-28 00:00:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 694, full token usage: 0.00, #swa token: 694, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.12, #queue-req: 0, 
[2025-07-28 00:00:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 606, full token usage: 0.00, #swa token: 606, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.96, #queue-req: 0, 
[2025-07-28 00:00:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 734, full token usage: 0.00, #swa token: 734, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.95, #queue-req: 0, 
[2025-07-28 00:00:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 646, full token usage: 0.00, #swa token: 646, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.04, #queue-req: 0, 
[2025-07-28 00:00:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 774, full token usage: 0.00, #swa token: 774, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.81, #queue-req: 0, 
[2025-07-28 00:00:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 686, full token usage: 0.00, #swa token: 686, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.86, #queue-req: 0, 
[2025-07-28 00:00:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 814, full token usage: 0.00, #swa token: 814, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.07, #queue-req: 0, 
[2025-07-28 00:00:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 726, full token usage: 0.00, #swa token: 726, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.07, #queue-req: 0, 
[2025-07-28 00:00:37 DP0 TP0] Decode batch. #running-req: 1, #full token: 854, full token usage: 0.00, #swa token: 854, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.75, #queue-req: 0, 
[2025-07-28 00:00:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 766, full token usage: 0.00, #swa token: 766, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.25, #queue-req: 0, 
[2025-07-28 00:00:37 DP0 TP0] Decode batch. #running-req: 1, #full token: 894, full token usage: 0.00, #swa token: 894, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 150.64, #queue-req: 0, 
[2025-07-28 00:00:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 806, full token usage: 0.00, #swa token: 806, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.01, #queue-req: 0, 
[2025-07-28 00:00:37 DP0 TP0] Decode batch. #running-req: 1, #full token: 934, full token usage: 0.00, #swa token: 934, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.03, #queue-req: 0, 
[2025-07-28 00:00:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 846, full token usage: 0.00, #swa token: 846, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.24, #queue-req: 0, 
[2025-07-28 00:00:37] INFO:     127.0.0.1:54220 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:37 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 103, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:00:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 204, full token usage: 0.00, #swa token: 204, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 125.58, #queue-req: 0, 
[2025-07-28 00:00:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 886, full token usage: 0.00, #swa token: 886, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.89, #queue-req: 0, 
[2025-07-28 00:00:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 244, full token usage: 0.00, #swa token: 244, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.61, #queue-req: 0, 
[2025-07-28 00:00:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 926, full token usage: 0.00, #swa token: 926, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.94, #queue-req: 0, 
[2025-07-28 00:00:38] INFO:     127.0.0.1:54234 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:38 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 92, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:00:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 284, full token usage: 0.00, #swa token: 284, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.17, #queue-req: 0, 
[2025-07-28 00:00:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 225, full token usage: 0.00, #swa token: 225, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 124.24, #queue-req: 0, 
[2025-07-28 00:00:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 324, full token usage: 0.00, #swa token: 324, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.25, #queue-req: 0, 
[2025-07-28 00:00:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 265, full token usage: 0.00, #swa token: 265, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.32, #queue-req: 0, 
[2025-07-28 00:00:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 364, full token usage: 0.00, #swa token: 364, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.01, #queue-req: 0, 
[2025-07-28 00:00:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 305, full token usage: 0.00, #swa token: 305, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.23, #queue-req: 0, 
[2025-07-28 00:00:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 404, full token usage: 0.00, #swa token: 404, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.26, #queue-req: 0, 
[2025-07-28 00:00:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 345, full token usage: 0.00, #swa token: 345, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.07, #queue-req: 0, 
[2025-07-28 00:00:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 444, full token usage: 0.00, #swa token: 444, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.10, #queue-req: 0, 
[2025-07-28 00:00:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 385, full token usage: 0.00, #swa token: 385, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.36, #queue-req: 0, 
[2025-07-28 00:00:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 484, full token usage: 0.00, #swa token: 484, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.16, #queue-req: 0, 
[2025-07-28 00:00:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 425, full token usage: 0.00, #swa token: 425, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.18, #queue-req: 0, 
[2025-07-28 00:00:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 524, full token usage: 0.00, #swa token: 524, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.17, #queue-req: 0, 
[2025-07-28 00:00:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 465, full token usage: 0.00, #swa token: 465, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.24, #queue-req: 0, 
[2025-07-28 00:00:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 564, full token usage: 0.00, #swa token: 564, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.57, #queue-req: 0, 
[2025-07-28 00:00:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 505, full token usage: 0.00, #swa token: 505, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.35, #queue-req: 0, 
[2025-07-28 00:00:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 604, full token usage: 0.00, #swa token: 604, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.41, #queue-req: 0, 
[2025-07-28 00:00:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 545, full token usage: 0.00, #swa token: 545, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.16, #queue-req: 0, 
[2025-07-28 00:00:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 644, full token usage: 0.00, #swa token: 644, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.61, #queue-req: 0, 
[2025-07-28 00:00:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 585, full token usage: 0.00, #swa token: 585, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.09, #queue-req: 0, 
[2025-07-28 00:00:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 684, full token usage: 0.00, #swa token: 684, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 148.18, #queue-req: 0, 
[2025-07-28 00:00:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 625, full token usage: 0.00, #swa token: 625, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.14, #queue-req: 0, 
[2025-07-28 00:00:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 724, full token usage: 0.00, #swa token: 724, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.66, #queue-req: 0, 
[2025-07-28 00:00:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 665, full token usage: 0.00, #swa token: 665, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.09, #queue-req: 0, 
[2025-07-28 00:00:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 764, full token usage: 0.00, #swa token: 764, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.02, #queue-req: 0, 
[2025-07-28 00:00:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 705, full token usage: 0.00, #swa token: 705, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.11, #queue-req: 0, 
[2025-07-28 00:00:41] INFO:     127.0.0.1:54246 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:41 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 122, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:00:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 235, full token usage: 0.00, #swa token: 235, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 125.15, #queue-req: 0, 
[2025-07-28 00:00:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 745, full token usage: 0.00, #swa token: 745, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.23, #queue-req: 0, 
[2025-07-28 00:00:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 275, full token usage: 0.00, #swa token: 275, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.34, #queue-req: 0, 
[2025-07-28 00:00:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 785, full token usage: 0.00, #swa token: 785, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.22, #queue-req: 0, 
[2025-07-28 00:00:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 315, full token usage: 0.00, #swa token: 315, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.37, #queue-req: 0, 
[2025-07-28 00:00:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 825, full token usage: 0.00, #swa token: 825, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.10, #queue-req: 0, 
[2025-07-28 00:00:42] INFO:     127.0.0.1:54260 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 355, full token usage: 0.00, #swa token: 355, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.23, #queue-req: 0, 
[2025-07-28 00:00:42 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 162, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:00:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 272, full token usage: 0.00, #swa token: 272, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 125.13, #queue-req: 0, 
[2025-07-28 00:00:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 395, full token usage: 0.00, #swa token: 395, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.67, #queue-req: 0, 
[2025-07-28 00:00:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 312, full token usage: 0.00, #swa token: 312, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.85, #queue-req: 0, 
[2025-07-28 00:00:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 435, full token usage: 0.00, #swa token: 435, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.23, #queue-req: 0, 
[2025-07-28 00:00:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 352, full token usage: 0.00, #swa token: 352, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.27, #queue-req: 0, 
[2025-07-28 00:00:43] INFO:     127.0.0.1:54262 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:43 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 148, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:00:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 260, full token usage: 0.00, #swa token: 260, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 126.25, #queue-req: 0, 
[2025-07-28 00:00:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 392, full token usage: 0.00, #swa token: 392, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.22, #queue-req: 0, 
[2025-07-28 00:00:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 300, full token usage: 0.00, #swa token: 300, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.18, #queue-req: 0, 
[2025-07-28 00:00:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 432, full token usage: 0.00, #swa token: 432, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.15, #queue-req: 0, 
[2025-07-28 00:00:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 340, full token usage: 0.00, #swa token: 340, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.05, #queue-req: 0, 
[2025-07-28 00:00:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 472, full token usage: 0.00, #swa token: 472, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.29, #queue-req: 0, 
[2025-07-28 00:00:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 380, full token usage: 0.00, #swa token: 380, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.25, #queue-req: 0, 
[2025-07-28 00:00:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 512, full token usage: 0.00, #swa token: 512, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.06, #queue-req: 0, 
[2025-07-28 00:00:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 420, full token usage: 0.00, #swa token: 420, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.19, #queue-req: 0, 
[2025-07-28 00:00:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 552, full token usage: 0.00, #swa token: 552, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.94, #queue-req: 0, 
[2025-07-28 00:00:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 460, full token usage: 0.00, #swa token: 460, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.16, #queue-req: 0, 
[2025-07-28 00:00:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 592, full token usage: 0.00, #swa token: 592, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.05, #queue-req: 0, 
[2025-07-28 00:00:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 500, full token usage: 0.00, #swa token: 500, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.13, #queue-req: 0, 
[2025-07-28 00:00:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 632, full token usage: 0.00, #swa token: 632, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.94, #queue-req: 0, 
[2025-07-28 00:00:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 540, full token usage: 0.00, #swa token: 540, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.05, #queue-req: 0, 
[2025-07-28 00:00:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 672, full token usage: 0.00, #swa token: 672, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.65, #queue-req: 0, 
[2025-07-28 00:00:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 580, full token usage: 0.00, #swa token: 580, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.06, #queue-req: 0, 
[2025-07-28 00:00:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 712, full token usage: 0.00, #swa token: 712, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.87, #queue-req: 0, 
[2025-07-28 00:00:45] INFO:     127.0.0.1:58200 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:45 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 117, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:00:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 620, full token usage: 0.00, #swa token: 620, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.01, #queue-req: 0, 
[2025-07-28 00:00:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 249, full token usage: 0.00, #swa token: 249, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 126.05, #queue-req: 0, 
[2025-07-28 00:00:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 660, full token usage: 0.00, #swa token: 660, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.01, #queue-req: 0, 
[2025-07-28 00:00:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 289, full token usage: 0.00, #swa token: 289, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.32, #queue-req: 0, 
[2025-07-28 00:00:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 700, full token usage: 0.00, #swa token: 700, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.14, #queue-req: 0, 
[2025-07-28 00:00:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 329, full token usage: 0.00, #swa token: 329, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.47, #queue-req: 0, 
[2025-07-28 00:00:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 740, full token usage: 0.00, #swa token: 740, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 152.71, #queue-req: 0, 
[2025-07-28 00:00:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 369, full token usage: 0.00, #swa token: 369, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.12, #queue-req: 0, 
[2025-07-28 00:00:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 780, full token usage: 0.00, #swa token: 780, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.08, #queue-req: 0, 
[2025-07-28 00:00:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 409, full token usage: 0.00, #swa token: 409, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.24, #queue-req: 0, 
[2025-07-28 00:00:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 820, full token usage: 0.00, #swa token: 820, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.06, #queue-req: 0, 
[2025-07-28 00:00:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 449, full token usage: 0.00, #swa token: 449, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.28, #queue-req: 0, 
[2025-07-28 00:00:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 860, full token usage: 0.00, #swa token: 860, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.05, #queue-req: 0, 
[2025-07-28 00:00:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 489, full token usage: 0.00, #swa token: 489, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.26, #queue-req: 0, 
[2025-07-28 00:00:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 900, full token usage: 0.00, #swa token: 900, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.97, #queue-req: 0, 
[2025-07-28 00:00:47] INFO:     127.0.0.1:58210 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:47 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 133, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:00:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 529, full token usage: 0.00, #swa token: 529, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.93, #queue-req: 0, 
[2025-07-28 00:00:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 263, full token usage: 0.00, #swa token: 263, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 124.08, #queue-req: 0, 
[2025-07-28 00:00:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 569, full token usage: 0.00, #swa token: 569, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.24, #queue-req: 0, 
[2025-07-28 00:00:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 303, full token usage: 0.00, #swa token: 303, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.05, #queue-req: 0, 
[2025-07-28 00:00:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 609, full token usage: 0.00, #swa token: 609, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.08, #queue-req: 0, 
[2025-07-28 00:00:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 343, full token usage: 0.00, #swa token: 343, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.12, #queue-req: 0, 
[2025-07-28 00:00:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 649, full token usage: 0.00, #swa token: 649, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.98, #queue-req: 0, 
[2025-07-28 00:00:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 383, full token usage: 0.00, #swa token: 383, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.96, #queue-req: 0, 
[2025-07-28 00:00:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 689, full token usage: 0.00, #swa token: 689, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.17, #queue-req: 0, 
[2025-07-28 00:00:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 423, full token usage: 0.00, #swa token: 423, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.08, #queue-req: 0, 
[2025-07-28 00:00:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 729, full token usage: 0.00, #swa token: 729, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.21, #queue-req: 0, 
[2025-07-28 00:00:49] INFO:     127.0.0.1:58218 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 463, full token usage: 0.00, #swa token: 463, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.11, #queue-req: 0, 
[2025-07-28 00:00:49 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 201, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:00:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 310, full token usage: 0.00, #swa token: 310, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 124.26, #queue-req: 0, 
[2025-07-28 00:00:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 503, full token usage: 0.00, #swa token: 503, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.03, #queue-req: 0, 
[2025-07-28 00:00:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 350, full token usage: 0.00, #swa token: 350, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.09, #queue-req: 0, 
[2025-07-28 00:00:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 543, full token usage: 0.00, #swa token: 543, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.92, #queue-req: 0, 
[2025-07-28 00:00:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 390, full token usage: 0.00, #swa token: 390, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.84, #queue-req: 0, 
[2025-07-28 00:00:50 DP0 TP0] Decode batch. #running-req: 1, #full token: 583, full token usage: 0.00, #swa token: 583, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.01, #queue-req: 0, 
[2025-07-28 00:00:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 430, full token usage: 0.00, #swa token: 430, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.32, #queue-req: 0, 
[2025-07-28 00:00:50 DP0 TP0] Decode batch. #running-req: 1, #full token: 623, full token usage: 0.00, #swa token: 623, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.11, #queue-req: 0, 
[2025-07-28 00:00:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 470, full token usage: 0.00, #swa token: 470, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.31, #queue-req: 0, 
[2025-07-28 00:00:50 DP0 TP0] Decode batch. #running-req: 1, #full token: 663, full token usage: 0.00, #swa token: 663, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.95, #queue-req: 0, 
[2025-07-28 00:00:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 510, full token usage: 0.00, #swa token: 510, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.22, #queue-req: 0, 
[2025-07-28 00:00:50 DP0 TP0] Decode batch. #running-req: 1, #full token: 703, full token usage: 0.00, #swa token: 703, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.14, #queue-req: 0, 
[2025-07-28 00:00:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 550, full token usage: 0.00, #swa token: 550, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.16, #queue-req: 0, 
[2025-07-28 00:00:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 743, full token usage: 0.00, #swa token: 743, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.04, #queue-req: 0, 
[2025-07-28 00:00:51 DP1 TP0] Decode batch. #running-req: 1, #full token: 590, full token usage: 0.00, #swa token: 590, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.93, #queue-req: 0, 
[2025-07-28 00:00:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 783, full token usage: 0.00, #swa token: 783, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.00, #queue-req: 0, 
[2025-07-28 00:00:51 DP1 TP0] Decode batch. #running-req: 1, #full token: 630, full token usage: 0.00, #swa token: 630, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.94, #queue-req: 0, 
[2025-07-28 00:00:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 823, full token usage: 0.00, #swa token: 823, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.87, #queue-req: 0, 
[2025-07-28 00:00:51] INFO:     127.0.0.1:58232 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:51] INFO:     127.0.0.1:58244 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:51 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 90, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:00:51 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 158, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:00:51 DP1 TP0] Decode batch. #running-req: 1, #full token: 273, full token usage: 0.00, #swa token: 273, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 122.98, #queue-req: 0, 
[2025-07-28 00:00:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 225, full token usage: 0.00, #swa token: 225, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 125.47, #queue-req: 0, 
[2025-07-28 00:00:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 313, full token usage: 0.00, #swa token: 313, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.28, #queue-req: 0, 
[2025-07-28 00:00:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 265, full token usage: 0.00, #swa token: 265, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.28, #queue-req: 0, 
[2025-07-28 00:00:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 353, full token usage: 0.00, #swa token: 353, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.15, #queue-req: 0, 
[2025-07-28 00:00:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 305, full token usage: 0.00, #swa token: 305, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.15, #queue-req: 0, 
[2025-07-28 00:00:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 393, full token usage: 0.00, #swa token: 393, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.93, #queue-req: 0, 
[2025-07-28 00:00:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 345, full token usage: 0.00, #swa token: 345, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.14, #queue-req: 0, 
[2025-07-28 00:00:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 433, full token usage: 0.00, #swa token: 433, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.21, #queue-req: 0, 
[2025-07-28 00:00:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 385, full token usage: 0.00, #swa token: 385, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.19, #queue-req: 0, 
[2025-07-28 00:00:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 473, full token usage: 0.00, #swa token: 473, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.23, #queue-req: 0, 
[2025-07-28 00:00:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 425, full token usage: 0.00, #swa token: 425, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.22, #queue-req: 0, 
[2025-07-28 00:00:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 513, full token usage: 0.00, #swa token: 513, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.08, #queue-req: 0, 
[2025-07-28 00:00:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 465, full token usage: 0.00, #swa token: 465, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.11, #queue-req: 0, 
[2025-07-28 00:00:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 553, full token usage: 0.00, #swa token: 553, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.81, #queue-req: 0, 
[2025-07-28 00:00:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 505, full token usage: 0.00, #swa token: 505, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.11, #queue-req: 0, 
[2025-07-28 00:00:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 593, full token usage: 0.00, #swa token: 593, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.10, #queue-req: 0, 
[2025-07-28 00:00:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 545, full token usage: 0.00, #swa token: 545, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.96, #queue-req: 0, 
[2025-07-28 00:00:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 633, full token usage: 0.00, #swa token: 633, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.09, #queue-req: 0, 
[2025-07-28 00:00:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 585, full token usage: 0.00, #swa token: 585, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.98, #queue-req: 0, 
[2025-07-28 00:00:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 673, full token usage: 0.00, #swa token: 673, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.04, #queue-req: 0, 
[2025-07-28 00:00:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 625, full token usage: 0.00, #swa token: 625, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.91, #queue-req: 0, 
[2025-07-28 00:00:54] INFO:     127.0.0.1:58258 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:54 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 134, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:00:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 713, full token usage: 0.00, #swa token: 713, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.45, #queue-req: 0, 
[2025-07-28 00:00:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 271, full token usage: 0.00, #swa token: 271, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 125.58, #queue-req: 0, 
[2025-07-28 00:00:54] INFO:     127.0.0.1:58262 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:54 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:00:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 246, full token usage: 0.00, #swa token: 246, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 124.65, #queue-req: 0, 
[2025-07-28 00:00:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 311, full token usage: 0.00, #swa token: 311, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.10, #queue-req: 0, 
[2025-07-28 00:00:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 286, full token usage: 0.00, #swa token: 286, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.22, #queue-req: 0, 
[2025-07-28 00:00:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 351, full token usage: 0.00, #swa token: 351, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.97, #queue-req: 0, 
[2025-07-28 00:00:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 326, full token usage: 0.00, #swa token: 326, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.19, #queue-req: 0, 
[2025-07-28 00:00:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 391, full token usage: 0.00, #swa token: 391, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.04, #queue-req: 0, 
[2025-07-28 00:00:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 366, full token usage: 0.00, #swa token: 366, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.12, #queue-req: 0, 
[2025-07-28 00:00:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 431, full token usage: 0.00, #swa token: 431, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.09, #queue-req: 0, 
[2025-07-28 00:00:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 406, full token usage: 0.00, #swa token: 406, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.10, #queue-req: 0, 
[2025-07-28 00:00:56 DP0 TP0] Decode batch. #running-req: 1, #full token: 471, full token usage: 0.00, #swa token: 471, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.07, #queue-req: 0, 
[2025-07-28 00:00:56 DP1 TP0] Decode batch. #running-req: 1, #full token: 446, full token usage: 0.00, #swa token: 446, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.27, #queue-req: 0, 
[2025-07-28 00:00:56 DP0 TP0] Decode batch. #running-req: 1, #full token: 511, full token usage: 0.00, #swa token: 511, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.03, #queue-req: 0, 
[2025-07-28 00:00:56 DP1 TP0] Decode batch. #running-req: 1, #full token: 486, full token usage: 0.00, #swa token: 486, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.22, #queue-req: 0, 
[2025-07-28 00:00:56 DP0 TP0] Decode batch. #running-req: 1, #full token: 551, full token usage: 0.00, #swa token: 551, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.09, #queue-req: 0, 
[2025-07-28 00:00:56 DP1 TP0] Decode batch. #running-req: 1, #full token: 526, full token usage: 0.00, #swa token: 526, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.03, #queue-req: 0, 
[2025-07-28 00:00:56 DP0 TP0] Decode batch. #running-req: 1, #full token: 591, full token usage: 0.00, #swa token: 591, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.05, #queue-req: 0, 
[2025-07-28 00:00:56 DP1 TP0] Decode batch. #running-req: 1, #full token: 566, full token usage: 0.00, #swa token: 566, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.01, #queue-req: 0, 
[2025-07-28 00:00:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 631, full token usage: 0.00, #swa token: 631, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.07, #queue-req: 0, 
[2025-07-28 00:00:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 606, full token usage: 0.00, #swa token: 606, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.21, #queue-req: 0, 
[2025-07-28 00:00:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 671, full token usage: 0.00, #swa token: 671, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.01, #queue-req: 0, 
[2025-07-28 00:00:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 646, full token usage: 0.00, #swa token: 646, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.09, #queue-req: 0, 
[2025-07-28 00:00:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 711, full token usage: 0.00, #swa token: 711, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.08, #queue-req: 0, 
[2025-07-28 00:00:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 686, full token usage: 0.00, #swa token: 686, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.21, #queue-req: 0, 
[2025-07-28 00:00:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 751, full token usage: 0.00, #swa token: 751, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.80, #queue-req: 0, 
[2025-07-28 00:00:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 726, full token usage: 0.00, #swa token: 726, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.89, #queue-req: 0, 
[2025-07-28 00:00:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 791, full token usage: 0.00, #swa token: 791, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.09, #queue-req: 0, 
[2025-07-28 00:00:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 766, full token usage: 0.00, #swa token: 766, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.24, #queue-req: 0, 
[2025-07-28 00:00:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 831, full token usage: 0.00, #swa token: 831, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.71, #queue-req: 0, 
[2025-07-28 00:00:58] INFO:     127.0.0.1:51940 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:58 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:00:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 806, full token usage: 0.00, #swa token: 806, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.16, #queue-req: 0, 
[2025-07-28 00:00:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 273, full token usage: 0.00, #swa token: 273, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 124.49, #queue-req: 0, 
[2025-07-28 00:00:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 846, full token usage: 0.00, #swa token: 846, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.82, #queue-req: 0, 
[2025-07-28 00:00:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 313, full token usage: 0.00, #swa token: 313, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.10, #queue-req: 0, 
[2025-07-28 00:00:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 886, full token usage: 0.00, #swa token: 886, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.14, #queue-req: 0, 
[2025-07-28 00:00:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 353, full token usage: 0.00, #swa token: 353, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.09, #queue-req: 0, 
[2025-07-28 00:00:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 926, full token usage: 0.00, #swa token: 926, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.94, #queue-req: 0, 
[2025-07-28 00:00:59] INFO:     127.0.0.1:51948 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:00:59 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 102, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:00:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 393, full token usage: 0.00, #swa token: 393, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.42, #queue-req: 0, 
[2025-07-28 00:00:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 213, full token usage: 0.00, #swa token: 213, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 124.61, #queue-req: 0, 
[2025-07-28 00:00:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 433, full token usage: 0.00, #swa token: 433, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.89, #queue-req: 0, 
[2025-07-28 00:00:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 253, full token usage: 0.00, #swa token: 253, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.46, #queue-req: 0, 
[2025-07-28 00:00:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 473, full token usage: 0.00, #swa token: 473, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.05, #queue-req: 0, 
[2025-07-28 00:01:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 293, full token usage: 0.00, #swa token: 293, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.24, #queue-req: 0, 
[2025-07-28 00:01:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 513, full token usage: 0.00, #swa token: 513, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.51, #queue-req: 0, 
[2025-07-28 00:01:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 333, full token usage: 0.00, #swa token: 333, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.93, #queue-req: 0, 
[2025-07-28 00:01:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 553, full token usage: 0.00, #swa token: 553, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.06, #queue-req: 0, 
[2025-07-28 00:01:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 373, full token usage: 0.00, #swa token: 373, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.15, #queue-req: 0, 
[2025-07-28 00:01:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 593, full token usage: 0.00, #swa token: 593, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.07, #queue-req: 0, 
[2025-07-28 00:01:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 413, full token usage: 0.00, #swa token: 413, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.12, #queue-req: 0, 
[2025-07-28 00:01:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 633, full token usage: 0.00, #swa token: 633, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.97, #queue-req: 0, 
[2025-07-28 00:01:01 DP1 TP0] Decode batch. #running-req: 1, #full token: 453, full token usage: 0.00, #swa token: 453, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.25, #queue-req: 0, 
[2025-07-28 00:01:01] INFO:     127.0.0.1:51958 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:01:01 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 149, #cached-token: 98, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:01:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 259, full token usage: 0.00, #swa token: 259, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 125.41, #queue-req: 0, 
[2025-07-28 00:01:01 DP1 TP0] Decode batch. #running-req: 1, #full token: 493, full token usage: 0.00, #swa token: 493, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.17, #queue-req: 0, 
[2025-07-28 00:01:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 299, full token usage: 0.00, #swa token: 299, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.13, #queue-req: 0, 
[2025-07-28 00:01:01 DP1 TP0] Decode batch. #running-req: 1, #full token: 533, full token usage: 0.00, #swa token: 533, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.04, #queue-req: 0, 
[2025-07-28 00:01:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 339, full token usage: 0.00, #swa token: 339, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.14, #queue-req: 0, 
[2025-07-28 00:01:01 DP1 TP0] Decode batch. #running-req: 1, #full token: 573, full token usage: 0.00, #swa token: 573, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.27, #queue-req: 0, 
[2025-07-28 00:01:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 379, full token usage: 0.00, #swa token: 379, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.11, #queue-req: 0, 
[2025-07-28 00:01:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 613, full token usage: 0.00, #swa token: 613, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.30, #queue-req: 0, 
[2025-07-28 00:01:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 419, full token usage: 0.00, #swa token: 419, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.10, #queue-req: 0, 
[2025-07-28 00:01:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 653, full token usage: 0.00, #swa token: 653, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.06, #queue-req: 0, 
[2025-07-28 00:01:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 459, full token usage: 0.00, #swa token: 459, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.00, #queue-req: 0, 
[2025-07-28 00:01:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 693, full token usage: 0.00, #swa token: 693, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.72, #queue-req: 0, 
[2025-07-28 00:01:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 499, full token usage: 0.00, #swa token: 499, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.05, #queue-req: 0, 
[2025-07-28 00:01:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 733, full token usage: 0.00, #swa token: 733, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.32, #queue-req: 0, 
[2025-07-28 00:01:03] INFO:     127.0.0.1:51982 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:01:03 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 83, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:01:03 DP1 TP0] Decode batch. #running-req: 2, #full token: 873, full token usage: 0.00, #swa token: 873, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 179.00, #queue-req: 0, 
[2025-07-28 00:01:03 DP1 TP0] Decode batch. #running-req: 2, #full token: 953, full token usage: 0.00, #swa token: 953, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.60, #queue-req: 0, 
[2025-07-28 00:01:03 DP1 TP0] Decode batch. #running-req: 2, #full token: 1033, full token usage: 0.00, #swa token: 1033, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.70, #queue-req: 0, 
[2025-07-28 00:01:04 DP1 TP0] Decode batch. #running-req: 2, #full token: 1113, full token usage: 0.00, #swa token: 1113, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.44, #queue-req: 0, 
[2025-07-28 00:01:04 DP1 TP0] Decode batch. #running-req: 2, #full token: 1193, full token usage: 0.00, #swa token: 1193, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.82, #queue-req: 0, 
[2025-07-28 00:01:04 DP1 TP0] Decode batch. #running-req: 2, #full token: 1273, full token usage: 0.00, #swa token: 1273, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.50, #queue-req: 0, 
[2025-07-28 00:01:04] INFO:     127.0.0.1:51970 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:01:04 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 155, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:01:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 437, full token usage: 0.00, #swa token: 437, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 259.34, #queue-req: 0, 
[2025-07-28 00:01:04 DP0 TP0] Decode batch. #running-req: 1, #full token: 265, full token usage: 0.00, #swa token: 265, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 18.84, #queue-req: 0, 
[2025-07-28 00:01:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 477, full token usage: 0.00, #swa token: 477, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.20, #queue-req: 0, 
[2025-07-28 00:01:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 305, full token usage: 0.00, #swa token: 305, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 141.46, #queue-req: 0, 
[2025-07-28 00:01:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 517, full token usage: 0.00, #swa token: 517, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.32, #queue-req: 0, 
[2025-07-28 00:01:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 345, full token usage: 0.00, #swa token: 345, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.14, #queue-req: 0, 
[2025-07-28 00:01:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 557, full token usage: 0.00, #swa token: 557, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.21, #queue-req: 0, 
[2025-07-28 00:01:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 385, full token usage: 0.00, #swa token: 385, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.18, #queue-req: 0, 
[2025-07-28 00:01:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 597, full token usage: 0.00, #swa token: 597, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.27, #queue-req: 0, 
[2025-07-28 00:01:06 DP0 TP0] Decode batch. #running-req: 1, #full token: 425, full token usage: 0.00, #swa token: 425, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.98, #queue-req: 0, 
[2025-07-28 00:01:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 637, full token usage: 0.00, #swa token: 637, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.28, #queue-req: 0, 
[2025-07-28 00:01:06 DP0 TP0] Decode batch. #running-req: 1, #full token: 465, full token usage: 0.00, #swa token: 465, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.07, #queue-req: 0, 
[2025-07-28 00:01:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 677, full token usage: 0.00, #swa token: 677, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.31, #queue-req: 0, 
[2025-07-28 00:01:06 DP0 TP0] Decode batch. #running-req: 1, #full token: 505, full token usage: 0.00, #swa token: 505, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.12, #queue-req: 0, 
[2025-07-28 00:01:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 717, full token usage: 0.00, #swa token: 717, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.39, #queue-req: 0, 
[2025-07-28 00:01:06 DP0 TP0] Decode batch. #running-req: 1, #full token: 545, full token usage: 0.00, #swa token: 545, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.71, #queue-req: 0, 
[2025-07-28 00:01:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 757, full token usage: 0.00, #swa token: 757, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.01, #queue-req: 0, 
[2025-07-28 00:01:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 585, full token usage: 0.00, #swa token: 585, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.85, #queue-req: 0, 
[2025-07-28 00:01:07 DP1 TP0] Decode batch. #running-req: 1, #full token: 797, full token usage: 0.00, #swa token: 797, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.89, #queue-req: 0, 
[2025-07-28 00:01:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 625, full token usage: 0.00, #swa token: 625, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.00, #queue-req: 0, 
[2025-07-28 00:01:07 DP1 TP0] Decode batch. #running-req: 1, #full token: 837, full token usage: 0.00, #swa token: 837, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.00, #queue-req: 0, 
[2025-07-28 00:01:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 665, full token usage: 0.00, #swa token: 665, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.99, #queue-req: 0, 
[2025-07-28 00:01:07 DP1 TP0] Decode batch. #running-req: 1, #full token: 877, full token usage: 0.00, #swa token: 877, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.01, #queue-req: 0, 
[2025-07-28 00:01:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 705, full token usage: 0.00, #swa token: 705, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.77, #queue-req: 0, 
[2025-07-28 00:01:07] INFO:     127.0.0.1:47790 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:01:07 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 91, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:01:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 193, full token usage: 0.00, #swa token: 193, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 124.47, #queue-req: 0, 
[2025-07-28 00:01:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 745, full token usage: 0.00, #swa token: 745, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.15, #queue-req: 0, 
[2025-07-28 00:01:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 233, full token usage: 0.00, #swa token: 233, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.48, #queue-req: 0, 
[2025-07-28 00:01:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 785, full token usage: 0.00, #swa token: 785, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.86, #queue-req: 0, 
[2025-07-28 00:01:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 273, full token usage: 0.00, #swa token: 273, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.38, #queue-req: 0, 
[2025-07-28 00:01:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 825, full token usage: 0.00, #swa token: 825, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.08, #queue-req: 0, 
[2025-07-28 00:01:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 313, full token usage: 0.00, #swa token: 313, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.37, #queue-req: 0, 
[2025-07-28 00:01:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 865, full token usage: 0.00, #swa token: 865, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.94, #queue-req: 0, 
[2025-07-28 00:01:09 DP1 TP0] Decode batch. #running-req: 1, #full token: 353, full token usage: 0.00, #swa token: 353, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.26, #queue-req: 0, 
[2025-07-28 00:01:09 DP0 TP0] Decode batch. #running-req: 1, #full token: 905, full token usage: 0.00, #swa token: 905, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.93, #queue-req: 0, 
[2025-07-28 00:01:09 DP1 TP0] Decode batch. #running-req: 1, #full token: 393, full token usage: 0.00, #swa token: 393, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.37, #queue-req: 0, 
[2025-07-28 00:01:09 DP0 TP0] Decode batch. #running-req: 1, #full token: 945, full token usage: 0.00, #swa token: 945, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.11, #queue-req: 0, 
[2025-07-28 00:01:09 DP1 TP0] Decode batch. #running-req: 1, #full token: 433, full token usage: 0.00, #swa token: 433, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.36, #queue-req: 0, 
[2025-07-28 00:01:09 DP0 TP0] Decode batch. #running-req: 1, #full token: 985, full token usage: 0.00, #swa token: 985, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.99, #queue-req: 0, 
[2025-07-28 00:01:09 DP1 TP0] Decode batch. #running-req: 1, #full token: 473, full token usage: 0.00, #swa token: 473, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.36, #queue-req: 0, 
[2025-07-28 00:01:09 DP0 TP0] Decode batch. #running-req: 1, #full token: 1025, full token usage: 0.00, #swa token: 1025, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.68, #queue-req: 0, 
[2025-07-28 00:01:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 513, full token usage: 0.00, #swa token: 513, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.44, #queue-req: 0, 
[2025-07-28 00:01:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 1065, full token usage: 0.00, #swa token: 1065, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.93, #queue-req: 0, 
[2025-07-28 00:01:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 553, full token usage: 0.00, #swa token: 553, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.35, #queue-req: 0, 
[2025-07-28 00:01:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 1105, full token usage: 0.00, #swa token: 1105, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.96, #queue-req: 0, 
[2025-07-28 00:01:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 593, full token usage: 0.00, #swa token: 593, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.15, #queue-req: 0, 
[2025-07-28 00:01:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 1145, full token usage: 0.00, #swa token: 1145, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.03, #queue-req: 0, 
[2025-07-28 00:01:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 633, full token usage: 0.00, #swa token: 633, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.27, #queue-req: 0, 
[2025-07-28 00:01:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 1185, full token usage: 0.00, #swa token: 1185, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.06, #queue-req: 0, 
[2025-07-28 00:01:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 673, full token usage: 0.00, #swa token: 673, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.17, #queue-req: 0, 
[2025-07-28 00:01:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 1225, full token usage: 0.00, #swa token: 1225, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.95, #queue-req: 0, 
[2025-07-28 00:01:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 713, full token usage: 0.00, #swa token: 713, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.29, #queue-req: 0, 
[2025-07-28 00:01:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 1265, full token usage: 0.00, #swa token: 1265, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.91, #queue-req: 0, 
[2025-07-28 00:01:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 753, full token usage: 0.00, #swa token: 753, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.28, #queue-req: 0, 
[2025-07-28 00:01:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 1305, full token usage: 0.00, #swa token: 1305, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.04, #queue-req: 0, 
[2025-07-28 00:01:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 793, full token usage: 0.00, #swa token: 793, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.31, #queue-req: 0, 
[2025-07-28 00:01:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 1345, full token usage: 0.00, #swa token: 1345, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.11, #queue-req: 0, 
[2025-07-28 00:01:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 833, full token usage: 0.00, #swa token: 833, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.22, #queue-req: 0, 
[2025-07-28 00:01:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 1385, full token usage: 0.00, #swa token: 1385, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.85, #queue-req: 0, 
[2025-07-28 00:01:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 873, full token usage: 0.00, #swa token: 873, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.27, #queue-req: 0, 
[2025-07-28 00:01:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 1425, full token usage: 0.00, #swa token: 1425, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.94, #queue-req: 0, 
[2025-07-28 00:01:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 913, full token usage: 0.00, #swa token: 913, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.34, #queue-req: 0, 
[2025-07-28 00:01:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 1465, full token usage: 0.00, #swa token: 1465, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.05, #queue-req: 0, 
[2025-07-28 00:01:12] INFO:     127.0.0.1:47794 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:01:12 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 179, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:01:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 953, full token usage: 0.00, #swa token: 953, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.52, #queue-req: 0, 
[2025-07-28 00:01:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 310, full token usage: 0.00, #swa token: 310, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 125.83, #queue-req: 0, 
[2025-07-28 00:01:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 993, full token usage: 0.00, #swa token: 993, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.11, #queue-req: 0, 
[2025-07-28 00:01:13] INFO:     127.0.0.1:47806 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:01:13 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 183, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:01:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 350, full token usage: 0.00, #swa token: 350, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.16, #queue-req: 0, 
[2025-07-28 00:01:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 318, full token usage: 0.00, #swa token: 318, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 126.03, #queue-req: 0, 
[2025-07-28 00:01:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 390, full token usage: 0.00, #swa token: 390, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 150.67, #queue-req: 0, 
[2025-07-28 00:01:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 358, full token usage: 0.00, #swa token: 358, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.10, #queue-req: 0, 
[2025-07-28 00:01:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 430, full token usage: 0.00, #swa token: 430, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 144.47, #queue-req: 0, 
[2025-07-28 00:01:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 398, full token usage: 0.00, #swa token: 398, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.16, #queue-req: 0, 
[2025-07-28 00:01:14 DP0 TP0] Decode batch. #running-req: 1, #full token: 470, full token usage: 0.00, #swa token: 470, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 152.79, #queue-req: 0, 
[2025-07-28 00:01:14 DP1 TP0] Decode batch. #running-req: 1, #full token: 438, full token usage: 0.00, #swa token: 438, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.31, #queue-req: 0, 
[2025-07-28 00:01:14 DP0 TP0] Decode batch. #running-req: 1, #full token: 510, full token usage: 0.00, #swa token: 510, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.95, #queue-req: 0, 
[2025-07-28 00:01:14 DP1 TP0] Decode batch. #running-req: 1, #full token: 478, full token usage: 0.00, #swa token: 478, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.62, #queue-req: 0, 
[2025-07-28 00:01:14 DP0 TP0] Decode batch. #running-req: 1, #full token: 550, full token usage: 0.00, #swa token: 550, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.17, #queue-req: 0, 
[2025-07-28 00:01:14 DP1 TP0] Decode batch. #running-req: 1, #full token: 518, full token usage: 0.00, #swa token: 518, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.18, #queue-req: 0, 
[2025-07-28 00:01:14 DP0 TP0] Decode batch. #running-req: 1, #full token: 590, full token usage: 0.00, #swa token: 590, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.55, #queue-req: 0, 
[2025-07-28 00:01:15 DP1 TP0] Decode batch. #running-req: 1, #full token: 558, full token usage: 0.00, #swa token: 558, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.39, #queue-req: 0, 
[2025-07-28 00:01:15 DP0 TP0] Decode batch. #running-req: 1, #full token: 630, full token usage: 0.00, #swa token: 630, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.14, #queue-req: 0, 
[2025-07-28 00:01:15 DP1 TP0] Decode batch. #running-req: 1, #full token: 598, full token usage: 0.00, #swa token: 598, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.12, #queue-req: 0, 
[2025-07-28 00:01:15 DP0 TP0] Decode batch. #running-req: 1, #full token: 670, full token usage: 0.00, #swa token: 670, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.19, #queue-req: 0, 
[2025-07-28 00:01:15 DP1 TP0] Decode batch. #running-req: 1, #full token: 638, full token usage: 0.00, #swa token: 638, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.96, #queue-req: 0, 
[2025-07-28 00:01:15 DP0 TP0] Decode batch. #running-req: 1, #full token: 710, full token usage: 0.00, #swa token: 710, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.10, #queue-req: 0, 
[2025-07-28 00:01:15 DP1 TP0] Decode batch. #running-req: 1, #full token: 678, full token usage: 0.00, #swa token: 678, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.06, #queue-req: 0, 
[2025-07-28 00:01:15 DP0 TP0] Decode batch. #running-req: 1, #full token: 750, full token usage: 0.00, #swa token: 750, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.99, #queue-req: 0, 
[2025-07-28 00:01:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 718, full token usage: 0.00, #swa token: 718, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.75, #queue-req: 0, 
[2025-07-28 00:01:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 790, full token usage: 0.00, #swa token: 790, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.89, #queue-req: 0, 
[2025-07-28 00:01:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 758, full token usage: 0.00, #swa token: 758, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.60, #queue-req: 0, 
[2025-07-28 00:01:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 830, full token usage: 0.00, #swa token: 830, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.07, #queue-req: 0, 
[2025-07-28 00:01:16] INFO:     127.0.0.1:50098 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:01:16 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 96, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:01:16] INFO:     127.0.0.1:50094 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:01:16 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 156, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:01:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 225, full token usage: 0.00, #swa token: 225, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 192.56, #queue-req: 0, 
[2025-07-28 00:01:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 275, full token usage: 0.00, #swa token: 275, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 73.71, #queue-req: 0, 
[2025-07-28 00:01:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 265, full token usage: 0.00, #swa token: 265, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.41, #queue-req: 0, 
[2025-07-28 00:01:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 315, full token usage: 0.00, #swa token: 315, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 152.36, #queue-req: 0, 
[2025-07-28 00:01:17 DP0 TP0] Decode batch. #running-req: 1, #full token: 305, full token usage: 0.00, #swa token: 305, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.65, #queue-req: 0, 
[2025-07-28 00:01:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 355, full token usage: 0.00, #swa token: 355, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.16, #queue-req: 0, 
[2025-07-28 00:01:17 DP0 TP0] Decode batch. #running-req: 1, #full token: 345, full token usage: 0.00, #swa token: 345, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.03, #queue-req: 0, 
[2025-07-28 00:01:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 395, full token usage: 0.00, #swa token: 395, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.15, #queue-req: 0, 
[2025-07-28 00:01:17 DP0 TP0] Decode batch. #running-req: 1, #full token: 385, full token usage: 0.00, #swa token: 385, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.93, #queue-req: 0, 
[2025-07-28 00:01:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 435, full token usage: 0.00, #swa token: 435, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.34, #queue-req: 0, 
[2025-07-28 00:01:17 DP0 TP0] Decode batch. #running-req: 1, #full token: 425, full token usage: 0.00, #swa token: 425, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.11, #queue-req: 0, 
[2025-07-28 00:01:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 475, full token usage: 0.00, #swa token: 475, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.25, #queue-req: 0, 
[2025-07-28 00:01:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 465, full token usage: 0.00, #swa token: 465, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.00, #queue-req: 0, 
[2025-07-28 00:01:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 515, full token usage: 0.00, #swa token: 515, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.20, #queue-req: 0, 
[2025-07-28 00:01:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 505, full token usage: 0.00, #swa token: 505, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.15, #queue-req: 0, 
[2025-07-28 00:01:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 555, full token usage: 0.00, #swa token: 555, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.04, #queue-req: 0, 
[2025-07-28 00:01:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 545, full token usage: 0.00, #swa token: 545, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.98, #queue-req: 0, 
[2025-07-28 00:01:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 595, full token usage: 0.00, #swa token: 595, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.12, #queue-req: 0, 
[2025-07-28 00:01:19 DP0 TP0] Decode batch. #running-req: 1, #full token: 585, full token usage: 0.00, #swa token: 585, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.07, #queue-req: 0, 
[2025-07-28 00:01:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 635, full token usage: 0.00, #swa token: 635, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.92, #queue-req: 0, 
[2025-07-28 00:01:19 DP0 TP0] Decode batch. #running-req: 1, #full token: 625, full token usage: 0.00, #swa token: 625, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.02, #queue-req: 0, 
[2025-07-28 00:01:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 675, full token usage: 0.00, #swa token: 675, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.08, #queue-req: 0, 
[2025-07-28 00:01:19 DP0 TP0] Decode batch. #running-req: 1, #full token: 665, full token usage: 0.00, #swa token: 665, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.10, #queue-req: 0, 
[2025-07-28 00:01:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 715, full token usage: 0.00, #swa token: 715, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.93, #queue-req: 0, 
[2025-07-28 00:01:19 DP0 TP0] Decode batch. #running-req: 1, #full token: 705, full token usage: 0.00, #swa token: 705, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.97, #queue-req: 0, 
[2025-07-28 00:01:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 755, full token usage: 0.00, #swa token: 755, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.98, #queue-req: 0, 
[2025-07-28 00:01:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 745, full token usage: 0.00, #swa token: 745, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.00, #queue-req: 0, 
[2025-07-28 00:01:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 795, full token usage: 0.00, #swa token: 795, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.14, #queue-req: 0, 
[2025-07-28 00:01:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 785, full token usage: 0.00, #swa token: 785, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.94, #queue-req: 0, 
[2025-07-28 00:01:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 835, full token usage: 0.00, #swa token: 835, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.03, #queue-req: 0, 
[2025-07-28 00:01:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 825, full token usage: 0.00, #swa token: 825, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.98, #queue-req: 0, 
[2025-07-28 00:01:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 875, full token usage: 0.00, #swa token: 875, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.14, #queue-req: 0, 
[2025-07-28 00:01:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 865, full token usage: 0.00, #swa token: 865, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.05, #queue-req: 0, 
[2025-07-28 00:01:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 915, full token usage: 0.00, #swa token: 915, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.19, #queue-req: 0, 
[2025-07-28 00:01:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 905, full token usage: 0.00, #swa token: 905, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.10, #queue-req: 0, 
[2025-07-28 00:01:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 955, full token usage: 0.00, #swa token: 955, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.10, #queue-req: 0, 
[2025-07-28 00:01:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 945, full token usage: 0.00, #swa token: 945, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.06, #queue-req: 0, 
[2025-07-28 00:01:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 995, full token usage: 0.00, #swa token: 995, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.20, #queue-req: 0, 
[2025-07-28 00:01:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 985, full token usage: 0.00, #swa token: 985, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.04, #queue-req: 0, 
[2025-07-28 00:01:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 1035, full token usage: 0.00, #swa token: 1035, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.99, #queue-req: 0, 
[2025-07-28 00:01:21] INFO:     127.0.0.1:50122 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:01:21 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:01:21 DP0 TP0] Decode batch. #running-req: 2, #full token: 1168, full token usage: 0.00, #swa token: 1168, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 170.17, #queue-req: 0, 
[2025-07-28 00:01:22] INFO:     127.0.0.1:50106 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:01:22 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 135, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:01:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 280, full token usage: 0.00, #swa token: 280, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.88, #queue-req: 0, 
[2025-07-28 00:01:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 320, full token usage: 0.00, #swa token: 320, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.00, #queue-req: 0, 
[2025-07-28 00:01:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 271, full token usage: 0.00, #swa token: 271, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 54.69, #queue-req: 0, 
[2025-07-28 00:01:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 360, full token usage: 0.00, #swa token: 360, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.15, #queue-req: 0, 
[2025-07-28 00:01:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 311, full token usage: 0.00, #swa token: 311, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.14, #queue-req: 0, 
[2025-07-28 00:01:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 400, full token usage: 0.00, #swa token: 400, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.08, #queue-req: 0, 
[2025-07-28 00:01:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 351, full token usage: 0.00, #swa token: 351, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.29, #queue-req: 0, 
[2025-07-28 00:01:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 440, full token usage: 0.00, #swa token: 440, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.04, #queue-req: 0, 
[2025-07-28 00:01:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 391, full token usage: 0.00, #swa token: 391, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.19, #queue-req: 0, 
[2025-07-28 00:01:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 480, full token usage: 0.00, #swa token: 480, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.07, #queue-req: 0, 
[2025-07-28 00:01:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 431, full token usage: 0.00, #swa token: 431, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.22, #queue-req: 0, 
[2025-07-28 00:01:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 520, full token usage: 0.00, #swa token: 520, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.08, #queue-req: 0, 
[2025-07-28 00:01:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 471, full token usage: 0.00, #swa token: 471, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.18, #queue-req: 0, 
[2025-07-28 00:01:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 560, full token usage: 0.00, #swa token: 560, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.04, #queue-req: 0, 
[2025-07-28 00:01:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 511, full token usage: 0.00, #swa token: 511, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.11, #queue-req: 0, 
[2025-07-28 00:01:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 600, full token usage: 0.00, #swa token: 600, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.07, #queue-req: 0, 
[2025-07-28 00:01:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 551, full token usage: 0.00, #swa token: 551, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.16, #queue-req: 0, 
[2025-07-28 00:01:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 640, full token usage: 0.00, #swa token: 640, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.98, #queue-req: 0, 
[2025-07-28 00:01:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 591, full token usage: 0.00, #swa token: 591, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.87, #queue-req: 0, 
[2025-07-28 00:01:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 680, full token usage: 0.00, #swa token: 680, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.06, #queue-req: 0, 
[2025-07-28 00:01:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 631, full token usage: 0.00, #swa token: 631, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.13, #queue-req: 0, 
[2025-07-28 00:01:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 720, full token usage: 0.00, #swa token: 720, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.01, #queue-req: 0, 
[2025-07-28 00:01:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 671, full token usage: 0.00, #swa token: 671, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.04, #queue-req: 0, 
[2025-07-28 00:01:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 760, full token usage: 0.00, #swa token: 760, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.03, #queue-req: 0, 
[2025-07-28 00:01:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 711, full token usage: 0.00, #swa token: 711, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.20, #queue-req: 0, 
[2025-07-28 00:01:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 800, full token usage: 0.00, #swa token: 800, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.85, #queue-req: 0, 
[2025-07-28 00:01:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 751, full token usage: 0.00, #swa token: 751, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.04, #queue-req: 0, 
[2025-07-28 00:01:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 840, full token usage: 0.00, #swa token: 840, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.01, #queue-req: 0, 
[2025-07-28 00:01:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 791, full token usage: 0.00, #swa token: 791, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.02, #queue-req: 0, 
[2025-07-28 00:01:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 880, full token usage: 0.00, #swa token: 880, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.78, #queue-req: 0, 
[2025-07-28 00:01:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 831, full token usage: 0.00, #swa token: 831, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.86, #queue-req: 0, 
[2025-07-28 00:01:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 920, full token usage: 0.00, #swa token: 920, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.82, #queue-req: 0, 
[2025-07-28 00:01:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 871, full token usage: 0.00, #swa token: 871, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.97, #queue-req: 0, 
[2025-07-28 00:01:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 960, full token usage: 0.00, #swa token: 960, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.95, #queue-req: 0, 
[2025-07-28 00:01:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 911, full token usage: 0.00, #swa token: 911, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.07, #queue-req: 0, 
[2025-07-28 00:01:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 1000, full token usage: 0.00, #swa token: 1000, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.02, #queue-req: 0, 
[2025-07-28 00:01:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 951, full token usage: 0.00, #swa token: 951, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.05, #queue-req: 0, 
[2025-07-28 00:01:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 1040, full token usage: 0.00, #swa token: 1040, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.91, #queue-req: 0, 
[2025-07-28 00:01:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 991, full token usage: 0.00, #swa token: 991, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.03, #queue-req: 0, 
[2025-07-28 00:01:27] INFO:     127.0.0.1:50128 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:01:27 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 131, #cached-token: 103, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:01:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 1031, full token usage: 0.00, #swa token: 1031, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.07, #queue-req: 0, 
[2025-07-28 00:01:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 267, full token usage: 0.00, #swa token: 267, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 126.06, #queue-req: 0, 
[2025-07-28 00:01:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 1071, full token usage: 0.00, #swa token: 1071, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.09, #queue-req: 0, 
[2025-07-28 00:01:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 307, full token usage: 0.00, #swa token: 307, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.04, #queue-req: 0, 
[2025-07-28 00:01:27] INFO:     127.0.0.1:50130 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:01:27 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 152, #cached-token: 104, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:01:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 347, full token usage: 0.00, #swa token: 347, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.02, #queue-req: 0, 
[2025-07-28 00:01:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 263, full token usage: 0.00, #swa token: 263, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 125.48, #queue-req: 0, 
[2025-07-28 00:01:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 387, full token usage: 0.00, #swa token: 387, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.07, #queue-req: 0, 
[2025-07-28 00:01:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 303, full token usage: 0.00, #swa token: 303, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.20, #queue-req: 0, 
[2025-07-28 00:01:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 427, full token usage: 0.00, #swa token: 427, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.99, #queue-req: 0, 
[2025-07-28 00:01:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 343, full token usage: 0.00, #swa token: 343, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.16, #queue-req: 0, 
[2025-07-28 00:01:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 467, full token usage: 0.00, #swa token: 467, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.02, #queue-req: 0, 
[2025-07-28 00:01:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 383, full token usage: 0.00, #swa token: 383, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.13, #queue-req: 0, 
[2025-07-28 00:01:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 507, full token usage: 0.00, #swa token: 507, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.97, #queue-req: 0, 
[2025-07-28 00:01:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 423, full token usage: 0.00, #swa token: 423, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.16, #queue-req: 0, 
[2025-07-28 00:01:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 547, full token usage: 0.00, #swa token: 547, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.05, #queue-req: 0, 
[2025-07-28 00:01:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 463, full token usage: 0.00, #swa token: 463, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.97, #queue-req: 0, 
[2025-07-28 00:01:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 587, full token usage: 0.00, #swa token: 587, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.85, #queue-req: 0, 
[2025-07-28 00:01:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 503, full token usage: 0.00, #swa token: 503, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.25, #queue-req: 0, 
[2025-07-28 00:01:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 627, full token usage: 0.00, #swa token: 627, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.95, #queue-req: 0, 
[2025-07-28 00:01:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 543, full token usage: 0.00, #swa token: 543, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.12, #queue-req: 0, 
[2025-07-28 00:01:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 667, full token usage: 0.00, #swa token: 667, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.96, #queue-req: 0, 
[2025-07-28 00:01:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 583, full token usage: 0.00, #swa token: 583, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.07, #queue-req: 0, 
[2025-07-28 00:01:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 707, full token usage: 0.00, #swa token: 707, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.03, #queue-req: 0, 
[2025-07-28 00:01:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 623, full token usage: 0.00, #swa token: 623, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.08, #queue-req: 0, 
[2025-07-28 00:01:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 747, full token usage: 0.00, #swa token: 747, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.95, #queue-req: 0, 
[2025-07-28 00:01:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 663, full token usage: 0.00, #swa token: 663, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.10, #queue-req: 0, 
[2025-07-28 00:01:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 787, full token usage: 0.00, #swa token: 787, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.87, #queue-req: 0, 
[2025-07-28 00:01:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 703, full token usage: 0.00, #swa token: 703, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.10, #queue-req: 0, 
[2025-07-28 00:01:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 827, full token usage: 0.00, #swa token: 827, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.73, #queue-req: 0, 
[2025-07-28 00:01:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 743, full token usage: 0.00, #swa token: 743, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.10, #queue-req: 0, 
[2025-07-28 00:01:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 867, full token usage: 0.00, #swa token: 867, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.03, #queue-req: 0, 
[2025-07-28 00:01:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 783, full token usage: 0.00, #swa token: 783, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.14, #queue-req: 0, 
[2025-07-28 00:01:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 907, full token usage: 0.00, #swa token: 907, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.90, #queue-req: 0, 
[2025-07-28 00:01:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 823, full token usage: 0.00, #swa token: 823, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.02, #queue-req: 0, 
[2025-07-28 00:01:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 947, full token usage: 0.00, #swa token: 947, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.95, #queue-req: 0, 
[2025-07-28 00:01:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 863, full token usage: 0.00, #swa token: 863, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.17, #queue-req: 0, 
[2025-07-28 00:01:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 987, full token usage: 0.00, #swa token: 987, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.96, #queue-req: 0, 
[2025-07-28 00:01:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 903, full token usage: 0.00, #swa token: 903, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.84, #queue-req: 0, 
[2025-07-28 00:01:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 1027, full token usage: 0.00, #swa token: 1027, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.43, #queue-req: 0, 
[2025-07-28 00:01:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 943, full token usage: 0.00, #swa token: 943, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 154.90, #queue-req: 0, 
[2025-07-28 00:01:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 1067, full token usage: 0.00, #swa token: 1067, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.90, #queue-req: 0, 
[2025-07-28 00:01:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 983, full token usage: 0.00, #swa token: 983, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.69, #queue-req: 0, 
[2025-07-28 00:01:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 1107, full token usage: 0.00, #swa token: 1107, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.08, #queue-req: 0, 
[2025-07-28 00:01:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 1023, full token usage: 0.00, #swa token: 1023, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.98, #queue-req: 0, 
[2025-07-28 00:01:32] INFO:     127.0.0.1:55626 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:01:32 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 110, #cached-token: 103, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:01:33 DP0 TP0] Decode batch. #running-req: 2, #full token: 1262, full token usage: 0.00, #swa token: 1262, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 143.41, #queue-req: 0, 
[2025-07-28 00:01:33 DP0 TP0] Decode batch. #running-req: 2, #full token: 1342, full token usage: 0.00, #swa token: 1342, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.12, #queue-req: 0, 
[2025-07-28 00:01:33] INFO:     127.0.0.1:55610 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:01:33 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 96, #cached-token: 103, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:01:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 209, full token usage: 0.00, #swa token: 209, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 50.86, #queue-req: 0, 
[2025-07-28 00:01:33 DP0 TP0] Decode batch. #running-req: 1, #full token: 298, full token usage: 0.00, #swa token: 298, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.47, #queue-req: 0, 
[2025-07-28 00:01:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 249, full token usage: 0.00, #swa token: 249, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 153.58, #queue-req: 0, 
[2025-07-28 00:01:33 DP0 TP0] Decode batch. #running-req: 1, #full token: 338, full token usage: 0.00, #swa token: 338, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.08, #queue-req: 0, 
[2025-07-28 00:01:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 289, full token usage: 0.00, #swa token: 289, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.18, #queue-req: 0, 
[2025-07-28 00:01:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 378, full token usage: 0.00, #swa token: 378, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.96, #queue-req: 0, 
[2025-07-28 00:01:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 329, full token usage: 0.00, #swa token: 329, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.26, #queue-req: 0, 
[2025-07-28 00:01:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 418, full token usage: 0.00, #swa token: 418, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.40, #queue-req: 0, 
[2025-07-28 00:01:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 369, full token usage: 0.00, #swa token: 369, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.03, #queue-req: 0, 
[2025-07-28 00:01:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 458, full token usage: 0.00, #swa token: 458, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.31, #queue-req: 0, 
[2025-07-28 00:01:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 409, full token usage: 0.00, #swa token: 409, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.26, #queue-req: 0, 
[2025-07-28 00:01:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 498, full token usage: 0.00, #swa token: 498, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.10, #queue-req: 0, 
[2025-07-28 00:01:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 449, full token usage: 0.00, #swa token: 449, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.14, #queue-req: 0, 
[2025-07-28 00:01:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 538, full token usage: 0.00, #swa token: 538, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.94, #queue-req: 0, 
[2025-07-28 00:01:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 489, full token usage: 0.00, #swa token: 489, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.35, #queue-req: 0, 
[2025-07-28 00:01:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 578, full token usage: 0.00, #swa token: 578, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.90, #queue-req: 0, 
[2025-07-28 00:01:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 529, full token usage: 0.00, #swa token: 529, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.10, #queue-req: 0, 
[2025-07-28 00:01:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 618, full token usage: 0.00, #swa token: 618, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.13, #queue-req: 0, 
[2025-07-28 00:01:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 569, full token usage: 0.00, #swa token: 569, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.15, #queue-req: 0, 
[2025-07-28 00:01:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 658, full token usage: 0.00, #swa token: 658, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.19, #queue-req: 0, 
[2025-07-28 00:01:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 609, full token usage: 0.00, #swa token: 609, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.20, #queue-req: 0, 
[2025-07-28 00:01:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 698, full token usage: 0.00, #swa token: 698, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.19, #queue-req: 0, 
[2025-07-28 00:01:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 649, full token usage: 0.00, #swa token: 649, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.14, #queue-req: 0, 
[2025-07-28 00:01:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 738, full token usage: 0.00, #swa token: 738, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.11, #queue-req: 0, 
[2025-07-28 00:01:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 689, full token usage: 0.00, #swa token: 689, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.45, #queue-req: 0, 
[2025-07-28 00:01:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 778, full token usage: 0.00, #swa token: 778, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.12, #queue-req: 0, 
[2025-07-28 00:01:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 729, full token usage: 0.00, #swa token: 729, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.26, #queue-req: 0, 
[2025-07-28 00:01:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 818, full token usage: 0.00, #swa token: 818, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.03, #queue-req: 0, 
[2025-07-28 00:01:36] INFO:     127.0.0.1:42066 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:01:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 769, full token usage: 0.00, #swa token: 769, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.24, #queue-req: 0, 
[2025-07-28 00:01:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 809, full token usage: 0.00, #swa token: 809, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.23, #queue-req: 0, 
[2025-07-28 00:01:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 849, full token usage: 0.00, #swa token: 849, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.06, #queue-req: 0, 
[2025-07-28 00:01:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 889, full token usage: 0.00, #swa token: 889, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.40, #queue-req: 0, 
[2025-07-28 00:01:38] INFO:     127.0.0.1:42076 - "POST /v1/chat/completions HTTP/1.1" 200 OK

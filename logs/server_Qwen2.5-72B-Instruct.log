[2025-07-28 00:45:56] server_args=ServerArgs(model_path='/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-72B-Instruct', tokenizer_path='/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-72B-Instruct', tokenizer_mode='auto', skip_tokenizer_init=False, load_format='auto', model_loader_extra_config='{}', trust_remote_code=False, context_length=32768, is_embedding=False, enable_multimodal=None, revision=None, model_impl='auto', host='127.0.0.1', port=8005, skip_server_warmup=False, warmups=None, nccl_port=None, dtype='auto', quantization=None, quantization_param_path=None, kv_cache_dtype='auto', mem_fraction_static=0.85, max_running_requests=None, max_total_tokens=None, chunked_prefill_size=8192, max_prefill_tokens=16384, schedule_policy='fcfs', schedule_conservativeness=1.0, cpu_offload_gb=0, page_size=1, hybrid_kvcache_ratio=None, swa_full_tokens_ratio=0.8, disable_hybrid_swa_memory=False, device='cuda', tp_size=4, pp_size=1, max_micro_batch_size=None, stream_interval=1, stream_output=False, random_seed=174632376, constrained_json_whitespace_pattern=None, watchdog_timeout=300, dist_timeout=None, download_dir=None, base_gpu_id=0, gpu_id_step=1, sleep_on_idle=False, log_level='info', log_level_http=None, log_requests=False, log_requests_level=0, crash_dump_folder=None, show_time_cost=True, enable_metrics=True, enable_metrics_for_all_schedulers=False, bucket_time_to_first_token=None, bucket_inter_token_latency=None, bucket_e2e_request_latency=None, collect_tokens_histogram=False, decode_log_interval=40, enable_request_time_stats_logging=False, kv_events_config=None, api_key=None, served_model_name='/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-72B-Instruct', chat_template=None, completion_template=None, file_storage_path='sglang_storage', enable_cache_report=False, reasoning_parser=None, tool_call_parser=None, dp_size=2, load_balance_method='round_robin', dist_init_addr=None, nnodes=1, node_rank=0, json_model_override_args='{}', preferred_sampling_params=None, enable_lora=None, max_lora_rank=None, lora_target_modules=None, lora_paths=None, max_loras_per_batch=8, lora_backend='triton', attention_backend=None, sampling_backend='flashinfer', grammar_backend='xgrammar', mm_attention_backend=None, speculative_algorithm=None, speculative_draft_model_path=None, speculative_num_steps=None, speculative_eagle_topk=None, speculative_num_draft_tokens=None, speculative_accept_threshold_single=1.0, speculative_accept_threshold_acc=1.0, speculative_token_map=None, ep_size=1, enable_ep_moe=False, enable_deepep_moe=False, enable_flashinfer_moe=False, enable_flashinfer_allreduce_fusion=False, deepep_mode='auto', ep_num_redundant_experts=0, ep_dispatch_algorithm='static', init_expert_location='trivial', enable_eplb=False, eplb_algorithm='auto', eplb_rebalance_num_iterations=1000, eplb_rebalance_layers_per_chunk=None, expert_distribution_recorder_mode=None, expert_distribution_recorder_buffer_size=1000, enable_expert_distribution_metrics=False, deepep_config=None, moe_dense_tp_size=None, enable_hierarchical_cache=False, hicache_ratio=2.0, hicache_size=0, hicache_write_policy='write_through_selective', hicache_io_backend='', hicache_storage_backend=None, enable_double_sparsity=False, ds_channel_config_path=None, ds_heavy_channel_num=32, ds_heavy_token_num=256, ds_heavy_channel_type='qk', ds_sparse_decode_threshold=4096, disable_radix_cache=False, cuda_graph_max_bs=None, cuda_graph_bs=None, disable_cuda_graph=False, disable_cuda_graph_padding=False, enable_profile_cuda_graph=False, enable_nccl_nvls=False, enable_tokenizer_batch_encode=False, disable_outlines_disk_cache=False, disable_custom_all_reduce=False, enable_mscclpp=False, disable_overlap_schedule=False, enable_mixed_chunk=False, enable_dp_attention=False, enable_dp_lm_head=False, enable_two_batch_overlap=False, enable_torch_compile=False, torch_compile_max_bs=32, torchao_config='', enable_nan_detection=False, enable_p2p_check=False, triton_attention_reduce_in_fp32=False, triton_attention_num_kv_splits=8, num_continuous_decode_steps=1, delete_ckpt_after_loading=False, enable_memory_saver=False, allow_auto_truncate=False, enable_custom_logit_processor=False, flashinfer_mla_disable_ragged=False, disable_shared_experts_fusion=False, disable_chunked_prefix_cache=False, disable_fast_image_processor=False, enable_return_hidden_states=False, enable_triton_kernel_moe=False, debug_tensor_dump_output_folder=None, debug_tensor_dump_input_file=None, debug_tensor_dump_inject=False, debug_tensor_dump_prefill_only=False, disaggregation_mode='null', disaggregation_transfer_backend='mooncake', disaggregation_bootstrap_port=8998, disaggregation_decode_tp=None, disaggregation_decode_dp=None, disaggregation_prefill_pp=1, disaggregation_ib_device=None, num_reserved_decode_tokens=512, pdlb_url=None, custom_weight_loader=[], weight_loader_disable_mmap=False, enable_pdmux=False, sm_group_num=3)
[2025-07-28 00:46:02] Launch DP0 starting at GPU #0.
[2025-07-28 00:46:02] Launch DP1 starting at GPU #4.
[2025-07-28 00:46:10 DP0 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 00:46:10 DP0 TP0] Init torch distributed begin.
[2025-07-28 00:46:10 DP1 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 00:46:10 DP1 TP0] Init torch distributed begin.
[2025-07-28 00:46:11 DP0 TP0] sglang is using nccl==2.26.2
[2025-07-28 00:46:12 DP1 TP0] sglang is using nccl==2.26.2
[2025-07-28 00:46:12 DP0 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 00:46:13 DP1 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 00:46:13 DP0 TP0] Load weight begin. avail mem=78.21 GB

Loading safetensors checkpoint shards:   0% Completed | 0/37 [00:00<?, ?it/s]
[2025-07-28 00:46:13 DP1 TP0] Load weight begin. avail mem=78.21 GB

Loading safetensors checkpoint shards:   0% Completed | 0/37 [00:00<?, ?it/s]

Loading safetensors checkpoint shards:   3% Completed | 1/37 [00:00<00:30,  1.17it/s]

Loading safetensors checkpoint shards:   3% Completed | 1/37 [00:01<00:39,  1.10s/it]

Loading safetensors checkpoint shards:   5% Completed | 2/37 [00:01<00:34,  1.03it/s]

Loading safetensors checkpoint shards:   5% Completed | 2/37 [00:02<00:37,  1.07s/it]

Loading safetensors checkpoint shards:   8% Completed | 3/37 [00:02<00:29,  1.14it/s]

Loading safetensors checkpoint shards:   8% Completed | 3/37 [00:02<00:32,  1.06it/s]

Loading safetensors checkpoint shards:  11% Completed | 4/37 [00:03<00:23,  1.40it/s]

Loading safetensors checkpoint shards:  11% Completed | 4/37 [00:03<00:25,  1.32it/s]

Loading safetensors checkpoint shards:  14% Completed | 5/37 [00:03<00:19,  1.61it/s]

Loading safetensors checkpoint shards:  14% Completed | 5/37 [00:03<00:20,  1.53it/s]

Loading safetensors checkpoint shards:  16% Completed | 6/37 [00:03<00:16,  1.85it/s]

Loading safetensors checkpoint shards:  16% Completed | 6/37 [00:04<00:18,  1.72it/s]

Loading safetensors checkpoint shards:  19% Completed | 7/37 [00:04<00:14,  2.01it/s]

Loading safetensors checkpoint shards:  19% Completed | 7/37 [00:04<00:16,  1.79it/s]

Loading safetensors checkpoint shards:  22% Completed | 8/37 [00:04<00:11,  2.43it/s]

Loading safetensors checkpoint shards:  22% Completed | 8/37 [00:05<00:13,  2.18it/s]

Loading safetensors checkpoint shards:  24% Completed | 9/37 [00:04<00:11,  2.52it/s]

Loading safetensors checkpoint shards:  24% Completed | 9/37 [00:05<00:12,  2.21it/s]

Loading safetensors checkpoint shards:  27% Completed | 10/37 [00:05<00:10,  2.58it/s]

Loading safetensors checkpoint shards:  27% Completed | 10/37 [00:05<00:12,  2.23it/s]

Loading safetensors checkpoint shards:  30% Completed | 11/37 [00:05<00:10,  2.53it/s]

Loading safetensors checkpoint shards:  32% Completed | 12/37 [00:06<00:09,  2.59it/s]

Loading safetensors checkpoint shards:  30% Completed | 11/37 [00:06<00:11,  2.17it/s]

Loading safetensors checkpoint shards:  35% Completed | 13/37 [00:06<00:08,  2.67it/s]

Loading safetensors checkpoint shards:  32% Completed | 12/37 [00:06<00:11,  2.22it/s]

Loading safetensors checkpoint shards:  38% Completed | 14/37 [00:06<00:08,  2.73it/s]

Loading safetensors checkpoint shards:  35% Completed | 13/37 [00:07<00:10,  2.25it/s]

Loading safetensors checkpoint shards:  41% Completed | 15/37 [00:07<00:07,  3.00it/s]

Loading safetensors checkpoint shards:  43% Completed | 16/37 [00:07<00:07,  2.93it/s]

Loading safetensors checkpoint shards:  38% Completed | 14/37 [00:07<00:10,  2.27it/s]

Loading safetensors checkpoint shards:  41% Completed | 15/37 [00:08<00:08,  2.57it/s]

Loading safetensors checkpoint shards:  46% Completed | 17/37 [00:07<00:06,  2.87it/s]

Loading safetensors checkpoint shards:  49% Completed | 18/37 [00:08<00:06,  2.85it/s]

Loading safetensors checkpoint shards:  43% Completed | 16/37 [00:08<00:08,  2.48it/s]

Loading safetensors checkpoint shards:  51% Completed | 19/37 [00:08<00:06,  2.89it/s]

Loading safetensors checkpoint shards:  46% Completed | 17/37 [00:08<00:08,  2.40it/s]

Loading safetensors checkpoint shards:  54% Completed | 20/37 [00:08<00:05,  2.93it/s]

Loading safetensors checkpoint shards:  49% Completed | 18/37 [00:09<00:08,  2.33it/s]

Loading safetensors checkpoint shards:  57% Completed | 21/37 [00:09<00:05,  2.93it/s]

Loading safetensors checkpoint shards:  51% Completed | 19/37 [00:09<00:07,  2.37it/s]

Loading safetensors checkpoint shards:  59% Completed | 22/37 [00:09<00:05,  2.81it/s]

Loading safetensors checkpoint shards:  62% Completed | 23/37 [00:09<00:04,  2.88it/s]

Loading safetensors checkpoint shards:  54% Completed | 20/37 [00:10<00:07,  2.40it/s]

Loading safetensors checkpoint shards:  65% Completed | 24/37 [00:10<00:04,  2.89it/s]

Loading safetensors checkpoint shards:  57% Completed | 21/37 [00:10<00:06,  2.39it/s]

Loading safetensors checkpoint shards:  68% Completed | 25/37 [00:10<00:04,  2.84it/s]

Loading safetensors checkpoint shards:  59% Completed | 22/37 [00:11<00:06,  2.27it/s]

Loading safetensors checkpoint shards:  70% Completed | 26/37 [00:10<00:04,  2.73it/s]

Loading safetensors checkpoint shards:  62% Completed | 23/37 [00:11<00:05,  2.34it/s]

Loading safetensors checkpoint shards:  73% Completed | 27/37 [00:11<00:03,  2.80it/s]

Loading safetensors checkpoint shards:  65% Completed | 24/37 [00:11<00:05,  2.37it/s]

Loading safetensors checkpoint shards:  76% Completed | 28/37 [00:11<00:03,  2.84it/s]

Loading safetensors checkpoint shards:  78% Completed | 29/37 [00:12<00:02,  2.88it/s]

Loading safetensors checkpoint shards:  68% Completed | 25/37 [00:12<00:05,  2.35it/s]

Loading safetensors checkpoint shards:  81% Completed | 30/37 [00:12<00:02,  2.87it/s]

Loading safetensors checkpoint shards:  70% Completed | 26/37 [00:12<00:04,  2.23it/s]

Loading safetensors checkpoint shards:  84% Completed | 31/37 [00:12<00:02,  2.88it/s]

Loading safetensors checkpoint shards:  73% Completed | 27/37 [00:13<00:04,  2.30it/s]

Loading safetensors checkpoint shards:  86% Completed | 32/37 [00:13<00:01,  2.74it/s]

Loading safetensors checkpoint shards:  76% Completed | 28/37 [00:13<00:03,  2.35it/s]

Loading safetensors checkpoint shards:  89% Completed | 33/37 [00:13<00:01,  2.65it/s]

Loading safetensors checkpoint shards:  78% Completed | 29/37 [00:14<00:03,  2.36it/s]

Loading safetensors checkpoint shards:  92% Completed | 34/37 [00:13<00:01,  2.72it/s]

Loading safetensors checkpoint shards:  81% Completed | 30/37 [00:14<00:03,  2.33it/s]

Loading safetensors checkpoint shards:  95% Completed | 35/37 [00:14<00:00,  2.62it/s]

Loading safetensors checkpoint shards:  97% Completed | 36/37 [00:14<00:00,  2.66it/s]

Loading safetensors checkpoint shards:  84% Completed | 31/37 [00:14<00:02,  2.34it/s]

Loading safetensors checkpoint shards: 100% Completed | 37/37 [00:15<00:00,  2.60it/s]

Loading safetensors checkpoint shards: 100% Completed | 37/37 [00:15<00:00,  2.46it/s]

[2025-07-28 00:46:28 DP1 TP0] Load weight end. type=Qwen2ForCausalLM, dtype=torch.bfloat16, avail mem=44.22 GB, mem usage=33.99 GB.

Loading safetensors checkpoint shards:  86% Completed | 32/37 [00:15<00:02,  2.24it/s]

Loading safetensors checkpoint shards:  89% Completed | 33/37 [00:15<00:01,  2.31it/s]

Loading safetensors checkpoint shards:  92% Completed | 34/37 [00:16<00:01,  2.47it/s]

Loading safetensors checkpoint shards:  95% Completed | 35/37 [00:16<00:00,  2.47it/s]

Loading safetensors checkpoint shards:  97% Completed | 36/37 [00:16<00:00,  2.57it/s]

Loading safetensors checkpoint shards: 100% Completed | 37/37 [00:17<00:00,  2.56it/s]

Loading safetensors checkpoint shards: 100% Completed | 37/37 [00:17<00:00,  2.14it/s]

[2025-07-28 00:46:30 DP0 TP0] Load weight end. type=Qwen2ForCausalLM, dtype=torch.bfloat16, avail mem=44.22 GB, mem usage=33.99 GB.
[2025-07-28 00:46:32 DP1 TP1] KV Cache is allocated. #tokens: 424737, K size: 16.20 GB, V size: 16.20 GB
[2025-07-28 00:46:32 DP0 TP2] KV Cache is allocated. #tokens: 424737, K size: 16.20 GB, V size: 16.20 GB
[2025-07-28 00:46:32 DP1 TP2] KV Cache is allocated. #tokens: 424737, K size: 16.20 GB, V size: 16.20 GB
[2025-07-28 00:46:32 DP1 TP0] KV Cache is allocated. #tokens: 424737, K size: 16.20 GB, V size: 16.20 GB
[2025-07-28 00:46:32 DP0 TP1] KV Cache is allocated. #tokens: 424737, K size: 16.20 GB, V size: 16.20 GB
[2025-07-28 00:46:32 DP0 TP3] KV Cache is allocated. #tokens: 424737, K size: 16.20 GB, V size: 16.20 GB
[2025-07-28 00:46:32 DP1 TP0] Memory pool end. avail mem=11.21 GB
[2025-07-28 00:46:32 DP1 TP3] KV Cache is allocated. #tokens: 424737, K size: 16.20 GB, V size: 16.20 GB
[2025-07-28 00:46:32 DP0 TP0] KV Cache is allocated. #tokens: 424737, K size: 16.20 GB, V size: 16.20 GB
[2025-07-28 00:46:32 DP0 TP0] Memory pool end. avail mem=11.21 GB
[2025-07-28 00:46:32 DP1 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.60 GB
[2025-07-28 00:46:32 DP0 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.60 GB
[2025-07-28 00:46:33 DP0 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.56 GB):   0%|          | 0/23 [00:00<?, ?it/s][2025-07-28 00:46:33 DP1 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.56 GB):   0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.56 GB):   4%|▍         | 1/23 [00:01<00:28,  1.32s/it]
Capturing batches (bs=152 avail_mem=10.28 GB):   4%|▍         | 1/23 [00:01<00:28,  1.32s/it]
Capturing batches (bs=160 avail_mem=10.56 GB):   4%|▍         | 1/23 [00:01<00:31,  1.42s/it]
Capturing batches (bs=152 avail_mem=10.28 GB):   4%|▍         | 1/23 [00:01<00:31,  1.42s/it]
Capturing batches (bs=152 avail_mem=10.28 GB):   9%|▊         | 2/23 [00:01<00:16,  1.26it/s]
Capturing batches (bs=144 avail_mem=10.17 GB):   9%|▊         | 2/23 [00:01<00:16,  1.26it/s]
Capturing batches (bs=152 avail_mem=10.28 GB):   9%|▊         | 2/23 [00:01<00:19,  1.08it/s]
Capturing batches (bs=144 avail_mem=10.17 GB):   9%|▊         | 2/23 [00:01<00:19,  1.08it/s]
Capturing batches (bs=144 avail_mem=10.17 GB):  13%|█▎        | 3/23 [00:02<00:13,  1.45it/s]
Capturing batches (bs=136 avail_mem=10.03 GB):  13%|█▎        | 3/23 [00:02<00:13,  1.45it/s]
Capturing batches (bs=144 avail_mem=10.17 GB):  13%|█▎        | 3/23 [00:02<00:15,  1.27it/s]
Capturing batches (bs=136 avail_mem=10.03 GB):  13%|█▎        | 3/23 [00:02<00:15,  1.27it/s]
Capturing batches (bs=136 avail_mem=10.03 GB):  17%|█▋        | 4/23 [00:02<00:10,  1.78it/s]
Capturing batches (bs=128 avail_mem=9.92 GB):  17%|█▋        | 4/23 [00:02<00:10,  1.78it/s] 
Capturing batches (bs=128 avail_mem=9.92 GB):  22%|██▏       | 5/23 [00:03<00:08,  2.07it/s]
Capturing batches (bs=120 avail_mem=9.82 GB):  22%|██▏       | 5/23 [00:03<00:08,  2.07it/s]
Capturing batches (bs=136 avail_mem=10.03 GB):  17%|█▋        | 4/23 [00:03<00:12,  1.57it/s]
Capturing batches (bs=128 avail_mem=9.92 GB):  17%|█▋        | 4/23 [00:03<00:12,  1.57it/s] 
Capturing batches (bs=120 avail_mem=9.82 GB):  26%|██▌       | 6/23 [00:03<00:07,  2.28it/s]
Capturing batches (bs=112 avail_mem=9.71 GB):  26%|██▌       | 6/23 [00:03<00:07,  2.28it/s]
Capturing batches (bs=128 avail_mem=9.92 GB):  22%|██▏       | 5/23 [00:03<00:10,  1.74it/s]
Capturing batches (bs=120 avail_mem=9.82 GB):  22%|██▏       | 5/23 [00:03<00:10,  1.74it/s]
Capturing batches (bs=112 avail_mem=9.71 GB):  30%|███       | 7/23 [00:03<00:06,  2.44it/s]
Capturing batches (bs=104 avail_mem=9.63 GB):  30%|███       | 7/23 [00:03<00:06,  2.44it/s]
Capturing batches (bs=120 avail_mem=9.82 GB):  26%|██▌       | 6/23 [00:03<00:09,  1.84it/s]
Capturing batches (bs=112 avail_mem=9.71 GB):  26%|██▌       | 6/23 [00:03<00:09,  1.84it/s]
Capturing batches (bs=104 avail_mem=9.63 GB):  35%|███▍      | 8/23 [00:04<00:06,  2.47it/s]
Capturing batches (bs=96 avail_mem=9.53 GB):  35%|███▍      | 8/23 [00:04<00:06,  2.47it/s] 
Capturing batches (bs=112 avail_mem=9.71 GB):  30%|███       | 7/23 [00:04<00:07,  2.07it/s]
Capturing batches (bs=104 avail_mem=9.63 GB):  30%|███       | 7/23 [00:04<00:07,  2.07it/s]
Capturing batches (bs=96 avail_mem=9.53 GB):  39%|███▉      | 9/23 [00:04<00:05,  2.58it/s]
Capturing batches (bs=88 avail_mem=9.45 GB):  39%|███▉      | 9/23 [00:04<00:05,  2.58it/s]
Capturing batches (bs=104 avail_mem=9.63 GB):  35%|███▍      | 8/23 [00:04<00:06,  2.21it/s]
Capturing batches (bs=96 avail_mem=9.53 GB):  35%|███▍      | 8/23 [00:04<00:06,  2.21it/s] 
Capturing batches (bs=88 avail_mem=9.45 GB):  43%|████▎     | 10/23 [00:04<00:04,  2.64it/s]
Capturing batches (bs=80 avail_mem=9.36 GB):  43%|████▎     | 10/23 [00:04<00:04,  2.64it/s]
Capturing batches (bs=96 avail_mem=9.53 GB):  39%|███▉      | 9/23 [00:05<00:06,  2.28it/s]
Capturing batches (bs=88 avail_mem=9.45 GB):  39%|███▉      | 9/23 [00:05<00:06,  2.28it/s]
Capturing batches (bs=80 avail_mem=9.36 GB):  48%|████▊     | 11/23 [00:05<00:04,  2.71it/s]
Capturing batches (bs=72 avail_mem=9.34 GB):  48%|████▊     | 11/23 [00:05<00:04,  2.71it/s]
Capturing batches (bs=72 avail_mem=9.34 GB):  52%|█████▏    | 12/23 [00:05<00:04,  2.74it/s]
Capturing batches (bs=64 avail_mem=9.31 GB):  52%|█████▏    | 12/23 [00:05<00:04,  2.74it/s]
Capturing batches (bs=88 avail_mem=9.45 GB):  43%|████▎     | 10/23 [00:05<00:05,  2.31it/s]
Capturing batches (bs=80 avail_mem=9.36 GB):  43%|████▎     | 10/23 [00:05<00:05,  2.31it/s]
Capturing batches (bs=64 avail_mem=9.31 GB):  57%|█████▋    | 13/23 [00:05<00:03,  2.77it/s]
Capturing batches (bs=56 avail_mem=9.25 GB):  57%|█████▋    | 13/23 [00:05<00:03,  2.77it/s]
Capturing batches (bs=80 avail_mem=9.36 GB):  48%|████▊     | 11/23 [00:05<00:05,  2.39it/s]
Capturing batches (bs=72 avail_mem=9.34 GB):  48%|████▊     | 11/23 [00:05<00:05,  2.39it/s]
Capturing batches (bs=56 avail_mem=9.25 GB):  61%|██████    | 14/23 [00:06<00:03,  2.77it/s]
Capturing batches (bs=48 avail_mem=9.19 GB):  61%|██████    | 14/23 [00:06<00:03,  2.77it/s]
Capturing batches (bs=72 avail_mem=9.34 GB):  52%|█████▏    | 12/23 [00:06<00:04,  2.46it/s]
Capturing batches (bs=64 avail_mem=9.31 GB):  52%|█████▏    | 12/23 [00:06<00:04,  2.46it/s]
Capturing batches (bs=48 avail_mem=9.19 GB):  65%|██████▌   | 15/23 [00:06<00:02,  2.75it/s]
Capturing batches (bs=40 avail_mem=9.17 GB):  65%|██████▌   | 15/23 [00:06<00:02,  2.75it/s]
Capturing batches (bs=64 avail_mem=9.31 GB):  57%|█████▋    | 13/23 [00:06<00:03,  2.56it/s]
Capturing batches (bs=56 avail_mem=9.25 GB):  57%|█████▋    | 13/23 [00:06<00:03,  2.56it/s]
Capturing batches (bs=40 avail_mem=9.17 GB):  70%|██████▉   | 16/23 [00:06<00:02,  2.78it/s]
Capturing batches (bs=32 avail_mem=9.12 GB):  70%|██████▉   | 16/23 [00:06<00:02,  2.78it/s]
Capturing batches (bs=56 avail_mem=9.25 GB):  61%|██████    | 14/23 [00:07<00:03,  2.55it/s]
Capturing batches (bs=48 avail_mem=9.19 GB):  61%|██████    | 14/23 [00:07<00:03,  2.55it/s]
Capturing batches (bs=32 avail_mem=9.12 GB):  74%|███████▍  | 17/23 [00:07<00:02,  2.78it/s]
Capturing batches (bs=24 avail_mem=9.09 GB):  74%|███████▍  | 17/23 [00:07<00:02,  2.78it/s]
Capturing batches (bs=48 avail_mem=9.19 GB):  65%|██████▌   | 15/23 [00:07<00:03,  2.47it/s]
Capturing batches (bs=40 avail_mem=9.17 GB):  65%|██████▌   | 15/23 [00:07<00:03,  2.47it/s]
Capturing batches (bs=24 avail_mem=9.09 GB):  78%|███████▊  | 18/23 [00:07<00:01,  2.82it/s]
Capturing batches (bs=16 avail_mem=9.06 GB):  78%|███████▊  | 18/23 [00:07<00:01,  2.82it/s]
Capturing batches (bs=40 avail_mem=9.17 GB):  70%|██████▉   | 16/23 [00:07<00:02,  2.41it/s]
Capturing batches (bs=32 avail_mem=9.12 GB):  70%|██████▉   | 16/23 [00:07<00:02,  2.41it/s]
Capturing batches (bs=16 avail_mem=9.06 GB):  83%|████████▎ | 19/23 [00:08<00:01,  2.84it/s]
Capturing batches (bs=8 avail_mem=9.04 GB):  83%|████████▎ | 19/23 [00:08<00:01,  2.84it/s] 
Capturing batches (bs=8 avail_mem=9.04 GB):  87%|████████▋ | 20/23 [00:08<00:01,  2.88it/s]
Capturing batches (bs=4 avail_mem=9.00 GB):  87%|████████▋ | 20/23 [00:08<00:01,  2.88it/s]
Capturing batches (bs=32 avail_mem=9.12 GB):  74%|███████▍  | 17/23 [00:08<00:02,  2.36it/s]
Capturing batches (bs=24 avail_mem=9.09 GB):  74%|███████▍  | 17/23 [00:08<00:02,  2.36it/s]
Capturing batches (bs=4 avail_mem=9.00 GB):  91%|█████████▏| 21/23 [00:08<00:00,  2.88it/s]
Capturing batches (bs=2 avail_mem=8.98 GB):  91%|█████████▏| 21/23 [00:08<00:00,  2.88it/s]
Capturing batches (bs=24 avail_mem=9.09 GB):  78%|███████▊  | 18/23 [00:08<00:02,  2.34it/s]
Capturing batches (bs=16 avail_mem=9.06 GB):  78%|███████▊  | 18/23 [00:08<00:02,  2.34it/s]
Capturing batches (bs=2 avail_mem=8.98 GB):  96%|█████████▌| 22/23 [00:09<00:00,  2.86it/s]
Capturing batches (bs=1 avail_mem=8.94 GB):  96%|█████████▌| 22/23 [00:09<00:00,  2.86it/s]
Capturing batches (bs=16 avail_mem=9.06 GB):  83%|████████▎ | 19/23 [00:09<00:01,  2.34it/s]
Capturing batches (bs=8 avail_mem=9.04 GB):  83%|████████▎ | 19/23 [00:09<00:01,  2.34it/s] 
Capturing batches (bs=1 avail_mem=8.94 GB): 100%|██████████| 23/23 [00:09<00:00,  2.85it/s]
Capturing batches (bs=1 avail_mem=8.94 GB): 100%|██████████| 23/23 [00:09<00:00,  2.45it/s]
[2025-07-28 00:46:42 DP1 TP3] Registering 3703 cuda graph addresses
[2025-07-28 00:46:42 DP1 TP0] Registering 3703 cuda graph addresses
[2025-07-28 00:46:42 DP1 TP1] Registering 3703 cuda graph addresses
[2025-07-28 00:46:42 DP1 TP2] Registering 3703 cuda graph addresses
[2025-07-28 00:46:42 DP1 TP0] Capture cuda graph end. Time elapsed: 9.69 s. mem usage=1.68 GB. avail mem=8.92 GB.

Capturing batches (bs=8 avail_mem=9.04 GB):  87%|████████▋ | 20/23 [00:09<00:01,  2.32it/s]
Capturing batches (bs=4 avail_mem=9.00 GB):  87%|████████▋ | 20/23 [00:09<00:01,  2.32it/s][2025-07-28 00:46:42 DP1 TP0] max_total_num_tokens=424737, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=8.92 GB

Capturing batches (bs=4 avail_mem=9.00 GB):  91%|█████████▏| 21/23 [00:10<00:00,  2.30it/s]
Capturing batches (bs=2 avail_mem=8.98 GB):  91%|█████████▏| 21/23 [00:10<00:00,  2.30it/s]
Capturing batches (bs=2 avail_mem=8.98 GB):  96%|█████████▌| 22/23 [00:10<00:00,  2.31it/s]
Capturing batches (bs=1 avail_mem=8.94 GB):  96%|█████████▌| 22/23 [00:10<00:00,  2.31it/s][2025-07-28 00:46:44 DP0 TP2] Registering 3703 cuda graph addresses
[2025-07-28 00:46:44 DP0 TP1] Registering 3703 cuda graph addresses
[2025-07-28 00:46:44 DP0 TP3] Registering 3703 cuda graph addresses

Capturing batches (bs=1 avail_mem=8.94 GB): 100%|██████████| 23/23 [00:10<00:00,  2.33it/s]
Capturing batches (bs=1 avail_mem=8.94 GB): 100%|██████████| 23/23 [00:10<00:00,  2.10it/s]
[2025-07-28 00:46:44 DP0 TP0] Registering 3703 cuda graph addresses
[2025-07-28 00:46:44 DP0 TP0] Capture cuda graph end. Time elapsed: 11.24 s. mem usage=1.68 GB. avail mem=8.92 GB.
[2025-07-28 00:46:44 DP0 TP0] max_total_num_tokens=424737, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=8.92 GB
[2025-07-28 00:46:45] INFO:     Started server process [1783123]
[2025-07-28 00:46:45] INFO:     Waiting for application startup.
[2025-07-28 00:46:45] INFO:     Application startup complete.
[2025-07-28 00:46:45] INFO:     Uvicorn running on http://127.0.0.1:8005 (Press CTRL+C to quit)
[2025-07-28 00:46:45] INFO:     127.0.0.1:39626 - "GET /health HTTP/1.1" 200 OK
[2025-07-28 00:46:45 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 256, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:46:45 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 215, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:46:46] INFO:     127.0.0.1:39658 - "GET /get_model_info HTTP/1.1" 200 OK
[2025-07-28 00:46:46 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 0, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:46:46 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 0, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:46:46] INFO:     127.0.0.1:39668 - "POST /generate HTTP/1.1" 200 OK
[2025-07-28 00:46:46] The server is fired up and ready to roll!
[2025-07-28 00:46:47 DP1 TP0] Decode batch. #running-req: 1, #token: 256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 9.88, #queue-req: 0, 
[2025-07-28 00:46:47 DP0 TP0] Decode batch. #running-req: 1, #token: 297, token usage: 0.00, cuda graph: True, gen throughput (token/s): 14.40, #queue-req: 0, 
[2025-07-28 00:46:48 DP1 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.55, #queue-req: 0, 
[2025-07-28 00:46:48 DP0 TP0] Decode batch. #running-req: 1, #token: 337, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.53, #queue-req: 0, 
[2025-07-28 00:46:49 DP1 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.51, #queue-req: 0, 
[2025-07-28 00:46:50 DP0 TP0] Decode batch. #running-req: 1, #token: 377, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.47, #queue-req: 0, 
[2025-07-28 00:46:51 DP1 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:46:51 DP0 TP0] Decode batch. #running-req: 1, #token: 417, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.40, #queue-req: 0, 
[2025-07-28 00:46:52 DP1 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:46:52 DP0 TP0] Decode batch. #running-req: 1, #token: 457, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.39, #queue-req: 0, 
[2025-07-28 00:46:53 DP1 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:46:53 DP0 TP0] Decode batch. #running-req: 1, #token: 497, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:46:54 DP1 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:46:54 DP0 TP0] Decode batch. #running-req: 1, #token: 537, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:46:55 DP1 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.38, #queue-req: 0, 
[2025-07-28 00:46:55 DP0 TP0] Decode batch. #running-req: 1, #token: 577, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.39, #queue-req: 0, 
[2025-07-28 00:46:56 DP1 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:46:56 DP0 TP0] Decode batch. #running-req: 1, #token: 617, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:46:57 DP1 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:46:57 DP0 TP0] Decode batch. #running-req: 1, #token: 657, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:46:59 DP1 TP0] Decode batch. #running-req: 1, #token: 656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.39, #queue-req: 0, 
[2025-07-28 00:46:59 DP0 TP0] Decode batch. #running-req: 1, #token: 697, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.39, #queue-req: 0, 
[2025-07-28 00:46:59] INFO:     127.0.0.1:39654 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:46:59 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 106, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:47:00 DP0 TP0] Decode batch. #running-req: 2, #token: 833, token usage: 0.00, cuda graph: True, gen throughput (token/s): 37.09, #queue-req: 0, 
[2025-07-28 00:47:01 DP0 TP0] Decode batch. #running-req: 2, #token: 913, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.69, #queue-req: 0, 
[2025-07-28 00:47:02 DP0 TP0] Decode batch. #running-req: 2, #token: 993, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.64, #queue-req: 0, 
[2025-07-28 00:47:02] INFO:     127.0.0.1:39642 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:47:02 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 207, #cached-token: 107, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:47:03 DP1 TP0] Decode batch. #running-req: 1, #token: 325, token usage: 0.00, cuda graph: True, gen throughput (token/s): 9.11, #queue-req: 0, 
[2025-07-28 00:47:03 DP0 TP0] Decode batch. #running-req: 1, #token: 322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 37.25, #queue-req: 0, 
[2025-07-28 00:47:04 DP1 TP0] Decode batch. #running-req: 1, #token: 365, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.50, #queue-req: 0, 
[2025-07-28 00:47:05 DP0 TP0] Decode batch. #running-req: 1, #token: 362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.51, #queue-req: 0, 
[2025-07-28 00:47:05 DP1 TP0] Decode batch. #running-req: 1, #token: 405, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:47:06 DP0 TP0] Decode batch. #running-req: 1, #token: 402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:47:06 DP1 TP0] Decode batch. #running-req: 1, #token: 445, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:47:07 DP0 TP0] Decode batch. #running-req: 1, #token: 442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.39, #queue-req: 0, 
[2025-07-28 00:47:07 DP1 TP0] Decode batch. #running-req: 1, #token: 485, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.39, #queue-req: 0, 
[2025-07-28 00:47:08 DP0 TP0] Decode batch. #running-req: 1, #token: 482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:47:09 DP1 TP0] Decode batch. #running-req: 1, #token: 525, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:47:09 DP0 TP0] Decode batch. #running-req: 1, #token: 522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:47:10 DP1 TP0] Decode batch. #running-req: 1, #token: 565, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:47:10 DP0 TP0] Decode batch. #running-req: 1, #token: 562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.04, #queue-req: 0, 
[2025-07-28 00:47:11 DP1 TP0] Decode batch. #running-req: 1, #token: 605, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:47:11 DP0 TP0] Decode batch. #running-req: 1, #token: 602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:47:12 DP1 TP0] Decode batch. #running-req: 1, #token: 645, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:47:12 DP0 TP0] Decode batch. #running-req: 1, #token: 642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.40, #queue-req: 0, 
[2025-07-28 00:47:13 DP1 TP0] Decode batch. #running-req: 1, #token: 685, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:47:14 DP0 TP0] Decode batch. #running-req: 1, #token: 682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.40, #queue-req: 0, 
[2025-07-28 00:47:14 DP1 TP0] Decode batch. #running-req: 1, #token: 725, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:47:15 DP0 TP0] Decode batch. #running-req: 1, #token: 722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:47:15] INFO:     127.0.0.1:46612 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:47:15 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 107, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:47:15 DP1 TP0] Decode batch. #running-req: 1, #token: 765, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:47:16 DP0 TP0] Decode batch. #running-req: 1, #token: 256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.38, #queue-req: 0, 
[2025-07-28 00:47:16 DP1 TP0] Decode batch. #running-req: 1, #token: 805, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:47:17 DP0 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.56, #queue-req: 0, 
[2025-07-28 00:47:18 DP1 TP0] Decode batch. #running-req: 1, #token: 845, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:47:18 DP0 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.56, #queue-req: 0, 
[2025-07-28 00:47:19 DP1 TP0] Decode batch. #running-req: 1, #token: 885, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:47:19 DP0 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.51, #queue-req: 0, 
[2025-07-28 00:47:20] INFO:     127.0.0.1:39208 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:47:20 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 133, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:47:20 DP1 TP0] Decode batch. #running-req: 1, #token: 243, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.42, #queue-req: 0, 
[2025-07-28 00:47:20 DP0 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:47:21 DP1 TP0] Decode batch. #running-req: 1, #token: 283, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.61, #queue-req: 0, 
[2025-07-28 00:47:22 DP0 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:47:22 DP1 TP0] Decode batch. #running-req: 1, #token: 323, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.55, #queue-req: 0, 
[2025-07-28 00:47:23 DP0 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:47:23 DP1 TP0] Decode batch. #running-req: 1, #token: 363, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.53, #queue-req: 0, 
[2025-07-28 00:47:24 DP0 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:47:24 DP1 TP0] Decode batch. #running-req: 1, #token: 403, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.49, #queue-req: 0, 
[2025-07-28 00:47:25 DP0 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:47:26 DP1 TP0] Decode batch. #running-req: 1, #token: 443, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:47:26 DP0 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:47:27 DP1 TP0] Decode batch. #running-req: 1, #token: 483, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:47:27 DP0 TP0] Decode batch. #running-req: 1, #token: 656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.40, #queue-req: 0, 
[2025-07-28 00:47:28 DP1 TP0] Decode batch. #running-req: 1, #token: 523, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:47:28 DP0 TP0] Decode batch. #running-req: 1, #token: 696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:47:29 DP1 TP0] Decode batch. #running-req: 1, #token: 563, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:47:29 DP0 TP0] Decode batch. #running-req: 1, #token: 736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:47:30 DP1 TP0] Decode batch. #running-req: 1, #token: 603, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:47:31 DP0 TP0] Decode batch. #running-req: 1, #token: 776, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.40, #queue-req: 0, 
[2025-07-28 00:47:31 DP1 TP0] Decode batch. #running-req: 1, #token: 643, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:47:32 DP0 TP0] Decode batch. #running-req: 1, #token: 816, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.40, #queue-req: 0, 
[2025-07-28 00:47:32] INFO:     127.0.0.1:53420 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:47:32 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 186, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:47:32 DP1 TP0] Decode batch. #running-req: 1, #token: 683, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:47:33 DP0 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.27, #queue-req: 0, 
[2025-07-28 00:47:33 DP1 TP0] Decode batch. #running-req: 1, #token: 723, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:47:34 DP0 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.53, #queue-req: 0, 
[2025-07-28 00:47:35 DP1 TP0] Decode batch. #running-req: 1, #token: 763, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:47:35 DP0 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.09, #queue-req: 0, 
[2025-07-28 00:47:36 DP1 TP0] Decode batch. #running-req: 1, #token: 803, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:47:36 DP0 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:47:37 DP1 TP0] Decode batch. #running-req: 1, #token: 843, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:47:37 DP0 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:47:38 DP1 TP0] Decode batch. #running-req: 1, #token: 883, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:47:39 DP0 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:47:39 DP1 TP0] Decode batch. #running-req: 1, #token: 923, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:47:40 DP0 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:47:40 DP1 TP0] Decode batch. #running-req: 1, #token: 963, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:47:41 DP0 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:47:41] INFO:     127.0.0.1:53424 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:47:41 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 173, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:47:41 DP1 TP0] Decode batch. #running-req: 1, #token: 284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.22, #queue-req: 0, 
[2025-07-28 00:47:42 DP0 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:47:43 DP1 TP0] Decode batch. #running-req: 1, #token: 324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.57, #queue-req: 0, 
[2025-07-28 00:47:43 DP0 TP0] Decode batch. #running-req: 1, #token: 676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.40, #queue-req: 0, 
[2025-07-28 00:47:44 DP1 TP0] Decode batch. #running-req: 1, #token: 364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.54, #queue-req: 0, 
[2025-07-28 00:47:44 DP0 TP0] Decode batch. #running-req: 1, #token: 716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:47:45 DP1 TP0] Decode batch. #running-req: 1, #token: 404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.50, #queue-req: 0, 
[2025-07-28 00:47:45 DP0 TP0] Decode batch. #running-req: 1, #token: 756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:47:46 DP1 TP0] Decode batch. #running-req: 1, #token: 444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:47:46 DP0 TP0] Decode batch. #running-req: 1, #token: 796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:47:47] INFO:     127.0.0.1:57000 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:47:47 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 226, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:47:47 DP1 TP0] Decode batch. #running-req: 1, #token: 484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:47:48 DP0 TP0] Decode batch. #running-req: 1, #token: 369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.16, #queue-req: 0, 
[2025-07-28 00:47:48 DP1 TP0] Decode batch. #running-req: 1, #token: 524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:47:49 DP0 TP0] Decode batch. #running-req: 1, #token: 409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:47:49 DP1 TP0] Decode batch. #running-req: 1, #token: 564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:47:50 DP0 TP0] Decode batch. #running-req: 1, #token: 449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:47:50 DP1 TP0] Decode batch. #running-req: 1, #token: 604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:47:51 DP0 TP0] Decode batch. #running-req: 1, #token: 489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:47:52 DP1 TP0] Decode batch. #running-req: 1, #token: 644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:47:52 DP0 TP0] Decode batch. #running-req: 1, #token: 529, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:47:53 DP1 TP0] Decode batch. #running-req: 1, #token: 684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:47:53 DP0 TP0] Decode batch. #running-req: 1, #token: 569, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:47:54 DP1 TP0] Decode batch. #running-req: 1, #token: 724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:47:54 DP0 TP0] Decode batch. #running-req: 1, #token: 609, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:47:55 DP1 TP0] Decode batch. #running-req: 1, #token: 764, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:47:56 DP0 TP0] Decode batch. #running-req: 1, #token: 649, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:47:56 DP1 TP0] Decode batch. #running-req: 1, #token: 804, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:47:57 DP0 TP0] Decode batch. #running-req: 1, #token: 689, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:47:57 DP1 TP0] Decode batch. #running-req: 1, #token: 844, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:47:58 DP0 TP0] Decode batch. #running-req: 1, #token: 729, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:47:58] INFO:     127.0.0.1:57016 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:47:58 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 168, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:47:58 DP1 TP0] Decode batch. #running-req: 1, #token: 286, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.31, #queue-req: 0, 
[2025-07-28 00:47:59 DP0 TP0] Decode batch. #running-req: 1, #token: 769, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:48:00 DP1 TP0] Decode batch. #running-req: 1, #token: 326, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.58, #queue-req: 0, 
[2025-07-28 00:48:00 DP0 TP0] Decode batch. #running-req: 1, #token: 809, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.40, #queue-req: 0, 
[2025-07-28 00:48:01 DP1 TP0] Decode batch. #running-req: 1, #token: 366, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.54, #queue-req: 0, 
[2025-07-28 00:48:01 DP0 TP0] Decode batch. #running-req: 1, #token: 849, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:48:02 DP1 TP0] Decode batch. #running-req: 1, #token: 406, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.48, #queue-req: 0, 
[2025-07-28 00:48:02 DP0 TP0] Decode batch. #running-req: 1, #token: 889, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:48:03 DP1 TP0] Decode batch. #running-req: 1, #token: 446, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:48:03] INFO:     127.0.0.1:34516 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:48:03 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 154, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:48:04 DP0 TP0] Decode batch. #running-req: 1, #token: 279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.34, #queue-req: 0, 
[2025-07-28 00:48:04 DP1 TP0] Decode batch. #running-req: 1, #token: 486, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:48:05 DP0 TP0] Decode batch. #running-req: 1, #token: 319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.55, #queue-req: 0, 
[2025-07-28 00:48:05 DP1 TP0] Decode batch. #running-req: 1, #token: 526, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:48:06 DP0 TP0] Decode batch. #running-req: 1, #token: 359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.54, #queue-req: 0, 
[2025-07-28 00:48:06 DP1 TP0] Decode batch. #running-req: 1, #token: 566, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:48:07 DP0 TP0] Decode batch. #running-req: 1, #token: 399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.48, #queue-req: 0, 
[2025-07-28 00:48:07 DP1 TP0] Decode batch. #running-req: 1, #token: 606, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:48:08 DP0 TP0] Decode batch. #running-req: 1, #token: 439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:48:09 DP1 TP0] Decode batch. #running-req: 1, #token: 646, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:48:09 DP0 TP0] Decode batch. #running-req: 1, #token: 479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:48:10 DP1 TP0] Decode batch. #running-req: 1, #token: 686, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:48:10 DP0 TP0] Decode batch. #running-req: 1, #token: 519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:48:11 DP1 TP0] Decode batch. #running-req: 1, #token: 726, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:48:11 DP0 TP0] Decode batch. #running-req: 1, #token: 559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.02, #queue-req: 0, 
[2025-07-28 00:48:12 DP1 TP0] Decode batch. #running-req: 1, #token: 766, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:48:12] INFO:     127.0.0.1:60630 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:48:12 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 141, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:48:13 DP0 TP0] Decode batch. #running-req: 1, #token: 599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:48:13 DP1 TP0] Decode batch. #running-req: 1, #token: 285, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.50, #queue-req: 0, 
[2025-07-28 00:48:14 DP0 TP0] Decode batch. #running-req: 1, #token: 639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:48:14 DP1 TP0] Decode batch. #running-req: 1, #token: 325, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.57, #queue-req: 0, 
[2025-07-28 00:48:15 DP0 TP0] Decode batch. #running-req: 1, #token: 679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:48:15] INFO:     127.0.0.1:51876 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:48:15 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 192, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:48:15 DP1 TP0] Decode batch. #running-req: 1, #token: 365, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.53, #queue-req: 0, 
[2025-07-28 00:48:16 DP0 TP0] Decode batch. #running-req: 1, #token: 324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.32, #queue-req: 0, 
[2025-07-28 00:48:17 DP1 TP0] Decode batch. #running-req: 1, #token: 405, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.47, #queue-req: 0, 
[2025-07-28 00:48:17 DP0 TP0] Decode batch. #running-req: 1, #token: 364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.52, #queue-req: 0, 
[2025-07-28 00:48:18 DP1 TP0] Decode batch. #running-req: 1, #token: 445, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:48:18 DP0 TP0] Decode batch. #running-req: 1, #token: 404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.48, #queue-req: 0, 
[2025-07-28 00:48:19 DP1 TP0] Decode batch. #running-req: 1, #token: 485, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:48:19 DP0 TP0] Decode batch. #running-req: 1, #token: 444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:48:20 DP1 TP0] Decode batch. #running-req: 1, #token: 525, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:48:21 DP0 TP0] Decode batch. #running-req: 1, #token: 484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:48:21 DP1 TP0] Decode batch. #running-req: 1, #token: 565, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:48:22 DP0 TP0] Decode batch. #running-req: 1, #token: 524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:48:22 DP1 TP0] Decode batch. #running-req: 1, #token: 605, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:48:23 DP0 TP0] Decode batch. #running-req: 1, #token: 564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:48:23 DP1 TP0] Decode batch. #running-req: 1, #token: 645, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:48:24 DP0 TP0] Decode batch. #running-req: 1, #token: 604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:48:24 DP1 TP0] Decode batch. #running-req: 1, #token: 685, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:48:25 DP0 TP0] Decode batch. #running-req: 1, #token: 644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:48:26 DP1 TP0] Decode batch. #running-req: 1, #token: 725, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:48:26 DP0 TP0] Decode batch. #running-req: 1, #token: 684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:48:27 DP1 TP0] Decode batch. #running-req: 1, #token: 765, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:48:27 DP0 TP0] Decode batch. #running-req: 1, #token: 724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:48:28] INFO:     127.0.0.1:60884 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:48:28 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 190, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:48:28 DP1 TP0] Decode batch. #running-req: 1, #token: 308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.31, #queue-req: 0, 
[2025-07-28 00:48:28 DP0 TP0] Decode batch. #running-req: 1, #token: 764, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:48:29 DP1 TP0] Decode batch. #running-req: 1, #token: 348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.56, #queue-req: 0, 
[2025-07-28 00:48:30 DP0 TP0] Decode batch. #running-req: 1, #token: 804, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:48:30 DP1 TP0] Decode batch. #running-req: 1, #token: 388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.49, #queue-req: 0, 
[2025-07-28 00:48:31 DP0 TP0] Decode batch. #running-req: 1, #token: 844, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:48:31 DP1 TP0] Decode batch. #running-req: 1, #token: 428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:48:32 DP0 TP0] Decode batch. #running-req: 1, #token: 884, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:48:32 DP1 TP0] Decode batch. #running-req: 1, #token: 468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:48:33 DP0 TP0] Decode batch. #running-req: 1, #token: 924, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:48:34 DP1 TP0] Decode batch. #running-req: 1, #token: 508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:48:34 DP0 TP0] Decode batch. #running-req: 1, #token: 964, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:48:35 DP1 TP0] Decode batch. #running-req: 1, #token: 548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:48:35 DP0 TP0] Decode batch. #running-req: 1, #token: 1004, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:48:36] INFO:     127.0.0.1:60900 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:48:36 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 137, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:48:36 DP1 TP0] Decode batch. #running-req: 1, #token: 588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:48:36 DP0 TP0] Decode batch. #running-req: 1, #token: 272, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.10, #queue-req: 0, 
[2025-07-28 00:48:37 DP1 TP0] Decode batch. #running-req: 1, #token: 628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:48:38 DP0 TP0] Decode batch. #running-req: 1, #token: 312, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.56, #queue-req: 0, 
[2025-07-28 00:48:38 DP1 TP0] Decode batch. #running-req: 1, #token: 668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:48:39 DP0 TP0] Decode batch. #running-req: 1, #token: 352, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.55, #queue-req: 0, 
[2025-07-28 00:48:39 DP1 TP0] Decode batch. #running-req: 1, #token: 708, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:48:40 DP0 TP0] Decode batch. #running-req: 1, #token: 392, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.49, #queue-req: 0, 
[2025-07-28 00:48:40 DP1 TP0] Decode batch. #running-req: 1, #token: 748, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:48:41 DP0 TP0] Decode batch. #running-req: 1, #token: 432, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:48:41 DP1 TP0] Decode batch. #running-req: 1, #token: 788, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:48:42 DP0 TP0] Decode batch. #running-req: 1, #token: 472, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:48:43 DP1 TP0] Decode batch. #running-req: 1, #token: 828, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:48:43 DP0 TP0] Decode batch. #running-req: 1, #token: 512, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:48:44 DP1 TP0] Decode batch. #running-req: 1, #token: 868, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:48:44 DP0 TP0] Decode batch. #running-req: 1, #token: 552, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:48:45 DP1 TP0] Decode batch. #running-req: 1, #token: 908, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:48:45 DP0 TP0] Decode batch. #running-req: 1, #token: 592, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:48:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.39, #queue-req: 0, 
[2025-07-28 00:48:46] INFO:     127.0.0.1:56098 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:48:46 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 132, #cached-token: 110, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:48:47 DP0 TP0] Decode batch. #running-req: 1, #token: 632, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:48:47 DP1 TP0] Decode batch. #running-req: 1, #token: 282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.64, #queue-req: 0, 
[2025-07-28 00:48:48 DP0 TP0] Decode batch. #running-req: 1, #token: 672, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:48:48 DP1 TP0] Decode batch. #running-req: 1, #token: 322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.58, #queue-req: 0, 
[2025-07-28 00:48:49 DP0 TP0] Decode batch. #running-req: 1, #token: 712, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:48:49 DP1 TP0] Decode batch. #running-req: 1, #token: 362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.54, #queue-req: 0, 
[2025-07-28 00:48:50 DP0 TP0] Decode batch. #running-req: 1, #token: 752, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:48:51 DP1 TP0] Decode batch. #running-req: 1, #token: 402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.50, #queue-req: 0, 
[2025-07-28 00:48:51 DP0 TP0] Decode batch. #running-req: 1, #token: 792, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:48:52 DP1 TP0] Decode batch. #running-req: 1, #token: 442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:48:52 DP0 TP0] Decode batch. #running-req: 1, #token: 832, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:48:53 DP1 TP0] Decode batch. #running-req: 1, #token: 482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:48:53 DP0 TP0] Decode batch. #running-req: 1, #token: 872, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.39, #queue-req: 0, 
[2025-07-28 00:48:54 DP1 TP0] Decode batch. #running-req: 1, #token: 522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:48:55 DP0 TP0] Decode batch. #running-req: 1, #token: 912, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.40, #queue-req: 0, 
[2025-07-28 00:48:55 DP1 TP0] Decode batch. #running-req: 1, #token: 562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:48:55] INFO:     127.0.0.1:47390 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:48:55 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 199, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:48:56 DP0 TP0] Decode batch. #running-req: 1, #token: 322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.21, #queue-req: 0, 
[2025-07-28 00:48:56 DP1 TP0] Decode batch. #running-req: 1, #token: 602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:48:57 DP0 TP0] Decode batch. #running-req: 1, #token: 362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.52, #queue-req: 0, 
[2025-07-28 00:48:57 DP1 TP0] Decode batch. #running-req: 1, #token: 642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:48:58 DP0 TP0] Decode batch. #running-req: 1, #token: 402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:48:58 DP1 TP0] Decode batch. #running-req: 1, #token: 682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:48:59 DP0 TP0] Decode batch. #running-req: 1, #token: 442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.40, #queue-req: 0, 
[2025-07-28 00:49:00 DP1 TP0] Decode batch. #running-req: 1, #token: 722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:49:00 DP0 TP0] Decode batch. #running-req: 1, #token: 482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:49:01 DP1 TP0] Decode batch. #running-req: 1, #token: 762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:49:01 DP0 TP0] Decode batch. #running-req: 1, #token: 522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:49:02 DP1 TP0] Decode batch. #running-req: 1, #token: 802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:49:03 DP0 TP0] Decode batch. #running-req: 1, #token: 562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:49:03 DP1 TP0] Decode batch. #running-req: 1, #token: 842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:49:04 DP0 TP0] Decode batch. #running-req: 1, #token: 602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:49:04 DP1 TP0] Decode batch. #running-req: 1, #token: 882, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:49:05 DP0 TP0] Decode batch. #running-req: 1, #token: 642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:49:05 DP1 TP0] Decode batch. #running-req: 1, #token: 922, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:49:06] INFO:     127.0.0.1:58348 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:49:06 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 194, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:49:06 DP0 TP0] Decode batch. #running-req: 1, #token: 682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:49:06 DP1 TP0] Decode batch. #running-req: 1, #token: 330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.32, #queue-req: 0, 
[2025-07-28 00:49:07 DP0 TP0] Decode batch. #running-req: 1, #token: 722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:49:08 DP1 TP0] Decode batch. #running-req: 1, #token: 370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.52, #queue-req: 0, 
[2025-07-28 00:49:08 DP0 TP0] Decode batch. #running-req: 1, #token: 762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.40, #queue-req: 0, 
[2025-07-28 00:49:09 DP1 TP0] Decode batch. #running-req: 1, #token: 410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.47, #queue-req: 0, 
[2025-07-28 00:49:09 DP0 TP0] Decode batch. #running-req: 1, #token: 802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:49:10 DP1 TP0] Decode batch. #running-req: 1, #token: 450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:49:10] INFO:     127.0.0.1:53346 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:49:10 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 167, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:49:10 DP0 TP0] Decode batch. #running-req: 1, #token: 283, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.31, #queue-req: 0, 
[2025-07-28 00:49:11 DP1 TP0] Decode batch. #running-req: 1, #token: 490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:49:12 DP0 TP0] Decode batch. #running-req: 1, #token: 323, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.55, #queue-req: 0, 
[2025-07-28 00:49:12 DP1 TP0] Decode batch. #running-req: 1, #token: 530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:49:13 DP0 TP0] Decode batch. #running-req: 1, #token: 363, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.13, #queue-req: 0, 
[2025-07-28 00:49:13 DP1 TP0] Decode batch. #running-req: 1, #token: 570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:49:14 DP0 TP0] Decode batch. #running-req: 1, #token: 403, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:49:14 DP1 TP0] Decode batch. #running-req: 1, #token: 610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:49:15 DP0 TP0] Decode batch. #running-req: 1, #token: 443, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:49:15 DP1 TP0] Decode batch. #running-req: 1, #token: 650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:49:16 DP0 TP0] Decode batch. #running-req: 1, #token: 483, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:49:17 DP1 TP0] Decode batch. #running-req: 1, #token: 690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:49:17 DP0 TP0] Decode batch. #running-req: 1, #token: 523, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:49:18 DP1 TP0] Decode batch. #running-req: 1, #token: 730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:49:18 DP0 TP0] Decode batch. #running-req: 1, #token: 563, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:49:19 DP1 TP0] Decode batch. #running-req: 1, #token: 770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:49:20 DP0 TP0] Decode batch. #running-req: 1, #token: 603, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:49:20 DP1 TP0] Decode batch. #running-req: 1, #token: 810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:49:20] INFO:     127.0.0.1:56882 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:49:20 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 172, #cached-token: 110, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:49:21 DP0 TP0] Decode batch. #running-req: 1, #token: 643, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:49:21 DP1 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.43, #queue-req: 0, 
[2025-07-28 00:49:22 DP0 TP0] Decode batch. #running-req: 1, #token: 683, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:49:22 DP1 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.55, #queue-req: 0, 
[2025-07-28 00:49:23 DP0 TP0] Decode batch. #running-req: 1, #token: 723, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:49:23 DP1 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.50, #queue-req: 0, 
[2025-07-28 00:49:24 DP0 TP0] Decode batch. #running-req: 1, #token: 763, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:49:25 DP1 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:49:25 DP0 TP0] Decode batch. #running-req: 1, #token: 803, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:49:26 DP1 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:49:26 DP0 TP0] Decode batch. #running-req: 1, #token: 843, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:49:27 DP1 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:49:27 DP0 TP0] Decode batch. #running-req: 1, #token: 883, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:49:28 DP1 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:49:29 DP0 TP0] Decode batch. #running-req: 1, #token: 923, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:49:29 DP1 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:49:30 DP0 TP0] Decode batch. #running-req: 1, #token: 963, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:49:30 DP1 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:49:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1003, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:49:31 DP1 TP0] Decode batch. #running-req: 1, #token: 676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:49:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1043, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.39, #queue-req: 0, 
[2025-07-28 00:49:32 DP1 TP0] Decode batch. #running-req: 1, #token: 716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:49:33] INFO:     127.0.0.1:56890 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:49:33 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 219, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:49:33 DP0 TP0] Decode batch. #running-req: 1, #token: 337, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.14, #queue-req: 0, 
[2025-07-28 00:49:34 DP1 TP0] Decode batch. #running-req: 1, #token: 756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:49:34 DP0 TP0] Decode batch. #running-req: 1, #token: 377, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.50, #queue-req: 0, 
[2025-07-28 00:49:35 DP1 TP0] Decode batch. #running-req: 1, #token: 796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:49:35 DP0 TP0] Decode batch. #running-req: 1, #token: 417, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:49:36 DP1 TP0] Decode batch. #running-req: 1, #token: 836, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:49:37 DP0 TP0] Decode batch. #running-req: 1, #token: 457, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:49:37 DP1 TP0] Decode batch. #running-req: 1, #token: 876, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:49:38 DP0 TP0] Decode batch. #running-req: 1, #token: 497, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.05, #queue-req: 0, 
[2025-07-28 00:49:38 DP1 TP0] Decode batch. #running-req: 1, #token: 916, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:49:39 DP0 TP0] Decode batch. #running-req: 1, #token: 537, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:49:39 DP1 TP0] Decode batch. #running-req: 1, #token: 956, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:49:40 DP0 TP0] Decode batch. #running-req: 1, #token: 577, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:49:40] INFO:     127.0.0.1:57488 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:49:40 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 155, #cached-token: 111, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:49:40 DP1 TP0] Decode batch. #running-req: 1, #token: 276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.36, #queue-req: 0, 
[2025-07-28 00:49:41 DP0 TP0] Decode batch. #running-req: 1, #token: 617, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:49:42 DP1 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.57, #queue-req: 0, 
[2025-07-28 00:49:42 DP0 TP0] Decode batch. #running-req: 1, #token: 657, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:49:43 DP1 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.56, #queue-req: 0, 
[2025-07-28 00:49:43 DP0 TP0] Decode batch. #running-req: 1, #token: 697, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:49:44 DP1 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.51, #queue-req: 0, 
[2025-07-28 00:49:44 DP0 TP0] Decode batch. #running-req: 1, #token: 737, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:49:45 DP1 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:49:46 DP0 TP0] Decode batch. #running-req: 1, #token: 777, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.40, #queue-req: 0, 
[2025-07-28 00:49:46 DP1 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:49:47 DP0 TP0] Decode batch. #running-req: 1, #token: 817, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:49:47 DP1 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:49:48 DP0 TP0] Decode batch. #running-req: 1, #token: 857, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.39, #queue-req: 0, 
[2025-07-28 00:49:48 DP1 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:49:49 DP0 TP0] Decode batch. #running-req: 1, #token: 897, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:49:49 DP1 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:49:50 DP0 TP0] Decode batch. #running-req: 1, #token: 937, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:49:51 DP1 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:49:51 DP0 TP0] Decode batch. #running-req: 1, #token: 977, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:49:52 DP1 TP0] Decode batch. #running-req: 1, #token: 676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:49:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1017, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:49:53 DP1 TP0] Decode batch. #running-req: 1, #token: 716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:49:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1057, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.38, #queue-req: 0, 
[2025-07-28 00:49:54 DP1 TP0] Decode batch. #running-req: 1, #token: 756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:49:54] INFO:     127.0.0.1:58944 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:49:54 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 175, #cached-token: 110, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:49:55 DP0 TP0] Decode batch. #running-req: 1, #token: 292, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.24, #queue-req: 0, 
[2025-07-28 00:49:55 DP1 TP0] Decode batch. #running-req: 1, #token: 796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:49:56 DP0 TP0] Decode batch. #running-req: 1, #token: 332, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.51, #queue-req: 0, 
[2025-07-28 00:49:56 DP1 TP0] Decode batch. #running-req: 1, #token: 836, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:49:57 DP0 TP0] Decode batch. #running-req: 1, #token: 372, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.51, #queue-req: 0, 
[2025-07-28 00:49:57 DP1 TP0] Decode batch. #running-req: 1, #token: 876, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:49:58 DP0 TP0] Decode batch. #running-req: 1, #token: 412, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:49:58 DP1 TP0] Decode batch. #running-req: 1, #token: 916, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:49:59 DP0 TP0] Decode batch. #running-req: 1, #token: 452, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:50:00 DP1 TP0] Decode batch. #running-req: 1, #token: 956, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:50:00 DP0 TP0] Decode batch. #running-req: 1, #token: 492, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:50:01 DP1 TP0] Decode batch. #running-req: 1, #token: 996, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:50:01 DP0 TP0] Decode batch. #running-req: 1, #token: 532, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:50:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1036, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:50:03 DP0 TP0] Decode batch. #running-req: 1, #token: 572, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:50:03 DP1 TP0] Decode batch. #running-req: 1, #token: 1076, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.39, #queue-req: 0, 
[2025-07-28 00:50:04 DP0 TP0] Decode batch. #running-req: 1, #token: 612, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:50:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1116, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.39, #queue-req: 0, 
[2025-07-28 00:50:04] INFO:     127.0.0.1:58946 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:50:04 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 94, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:50:05 DP0 TP0] Decode batch. #running-req: 1, #token: 652, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:50:05 DP1 TP0] Decode batch. #running-req: 1, #token: 235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.69, #queue-req: 0, 
[2025-07-28 00:50:06 DP0 TP0] Decode batch. #running-req: 1, #token: 692, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.39, #queue-req: 0, 
[2025-07-28 00:50:06 DP1 TP0] Decode batch. #running-req: 1, #token: 275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.69, #queue-req: 0, 
[2025-07-28 00:50:07 DP0 TP0] Decode batch. #running-req: 1, #token: 732, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:50:08 DP1 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.58, #queue-req: 0, 
[2025-07-28 00:50:08 DP0 TP0] Decode batch. #running-req: 1, #token: 772, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:50:09 DP1 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.56, #queue-req: 0, 
[2025-07-28 00:50:09 DP0 TP0] Decode batch. #running-req: 1, #token: 812, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:50:10 DP1 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.51, #queue-req: 0, 
[2025-07-28 00:50:10 DP0 TP0] Decode batch. #running-req: 1, #token: 852, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:50:11 DP1 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.47, #queue-req: 0, 
[2025-07-28 00:50:12 DP0 TP0] Decode batch. #running-req: 1, #token: 892, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:50:12 DP1 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:50:13 DP0 TP0] Decode batch. #running-req: 1, #token: 932, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:50:13 DP1 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:50:14 DP0 TP0] Decode batch. #running-req: 1, #token: 972, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.01, #queue-req: 0, 
[2025-07-28 00:50:14 DP1 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:50:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1012, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.38, #queue-req: 0, 
[2025-07-28 00:50:15 DP1 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:50:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1052, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.38, #queue-req: 0, 
[2025-07-28 00:50:17 DP1 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:50:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1092, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.38, #queue-req: 0, 
[2025-07-28 00:50:17] INFO:     127.0.0.1:59146 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:50:17 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 115, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:50:18 DP1 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:50:18 DP0 TP0] Decode batch. #running-req: 1, #token: 255, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.32, #queue-req: 0, 
[2025-07-28 00:50:19 DP1 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:50:19] INFO:     127.0.0.1:36238 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:50:19 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 150, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:50:20 DP0 TP0] Decode batch. #running-req: 1, #token: 295, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.57, #queue-req: 0, 
[2025-07-28 00:50:20 DP1 TP0] Decode batch. #running-req: 1, #token: 289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.43, #queue-req: 0, 
[2025-07-28 00:50:21 DP0 TP0] Decode batch. #running-req: 1, #token: 335, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.56, #queue-req: 0, 
[2025-07-28 00:50:21 DP1 TP0] Decode batch. #running-req: 1, #token: 329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.57, #queue-req: 0, 
[2025-07-28 00:50:22 DP0 TP0] Decode batch. #running-req: 1, #token: 375, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.52, #queue-req: 0, 
[2025-07-28 00:50:22 DP1 TP0] Decode batch. #running-req: 1, #token: 369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.52, #queue-req: 0, 
[2025-07-28 00:50:23 DP0 TP0] Decode batch. #running-req: 1, #token: 415, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:50:23 DP1 TP0] Decode batch. #running-req: 1, #token: 409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.47, #queue-req: 0, 
[2025-07-28 00:50:24 DP0 TP0] Decode batch. #running-req: 1, #token: 455, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:50:25 DP1 TP0] Decode batch. #running-req: 1, #token: 449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:50:25 DP0 TP0] Decode batch. #running-req: 1, #token: 495, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:50:26 DP1 TP0] Decode batch. #running-req: 1, #token: 489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:50:26 DP0 TP0] Decode batch. #running-req: 1, #token: 535, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:50:27 DP1 TP0] Decode batch. #running-req: 1, #token: 529, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:50:28 DP0 TP0] Decode batch. #running-req: 1, #token: 575, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:50:28 DP1 TP0] Decode batch. #running-req: 1, #token: 569, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:50:29 DP0 TP0] Decode batch. #running-req: 1, #token: 615, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.38, #queue-req: 0, 
[2025-07-28 00:50:29 DP1 TP0] Decode batch. #running-req: 1, #token: 609, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:50:30 DP0 TP0] Decode batch. #running-req: 1, #token: 655, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:50:30 DP1 TP0] Decode batch. #running-req: 1, #token: 649, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:50:31 DP0 TP0] Decode batch. #running-req: 1, #token: 695, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:50:31 DP1 TP0] Decode batch. #running-req: 1, #token: 689, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:50:32 DP0 TP0] Decode batch. #running-req: 1, #token: 735, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:50:32 DP1 TP0] Decode batch. #running-req: 1, #token: 729, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:50:33 DP0 TP0] Decode batch. #running-req: 1, #token: 775, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:50:34 DP1 TP0] Decode batch. #running-req: 1, #token: 769, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:50:34 DP0 TP0] Decode batch. #running-req: 1, #token: 815, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:50:35 DP1 TP0] Decode batch. #running-req: 1, #token: 809, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:50:35 DP0 TP0] Decode batch. #running-req: 1, #token: 855, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:50:36 DP1 TP0] Decode batch. #running-req: 1, #token: 849, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:50:37 DP0 TP0] Decode batch. #running-req: 1, #token: 895, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:50:37] INFO:     127.0.0.1:59498 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:50:37 DP1 TP0] Decode batch. #running-req: 1, #token: 889, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:50:37 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 148, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:50:38 DP0 TP0] Decode batch. #running-req: 1, #token: 284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.21, #queue-req: 0, 
[2025-07-28 00:50:38 DP1 TP0] Decode batch. #running-req: 1, #token: 929, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:50:39] INFO:     127.0.0.1:59500 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:50:39 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 160, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:50:39 DP0 TP0] Decode batch. #running-req: 1, #token: 324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.15, #queue-req: 0, 
[2025-07-28 00:50:39 DP1 TP0] Decode batch. #running-req: 1, #token: 280, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.35, #queue-req: 0, 
[2025-07-28 00:50:40 DP0 TP0] Decode batch. #running-req: 1, #token: 364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.50, #queue-req: 0, 
[2025-07-28 00:50:40 DP1 TP0] Decode batch. #running-req: 1, #token: 320, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.58, #queue-req: 0, 
[2025-07-28 00:50:41 DP0 TP0] Decode batch. #running-req: 1, #token: 404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.48, #queue-req: 0, 
[2025-07-28 00:50:42 DP1 TP0] Decode batch. #running-req: 1, #token: 360, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.55, #queue-req: 0, 
[2025-07-28 00:50:42 DP0 TP0] Decode batch. #running-req: 1, #token: 444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:50:43 DP1 TP0] Decode batch. #running-req: 1, #token: 400, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.50, #queue-req: 0, 
[2025-07-28 00:50:43 DP0 TP0] Decode batch. #running-req: 1, #token: 484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:50:44 DP1 TP0] Decode batch. #running-req: 1, #token: 440, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:50:45 DP0 TP0] Decode batch. #running-req: 1, #token: 524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.40, #queue-req: 0, 
[2025-07-28 00:50:45 DP1 TP0] Decode batch. #running-req: 1, #token: 480, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:50:46 DP0 TP0] Decode batch. #running-req: 1, #token: 564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:50:46 DP1 TP0] Decode batch. #running-req: 1, #token: 520, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:50:47 DP0 TP0] Decode batch. #running-req: 1, #token: 604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:50:47 DP1 TP0] Decode batch. #running-req: 1, #token: 560, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:50:48 DP0 TP0] Decode batch. #running-req: 1, #token: 644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:50:48 DP1 TP0] Decode batch. #running-req: 1, #token: 600, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:50:49 DP0 TP0] Decode batch. #running-req: 1, #token: 684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:50:49 DP1 TP0] Decode batch. #running-req: 1, #token: 640, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:50:50 DP0 TP0] Decode batch. #running-req: 1, #token: 724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:50:51 DP1 TP0] Decode batch. #running-req: 1, #token: 680, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:50:51 DP0 TP0] Decode batch. #running-req: 1, #token: 764, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:50:52 DP1 TP0] Decode batch. #running-req: 1, #token: 720, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:50:52 DP0 TP0] Decode batch. #running-req: 1, #token: 804, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:50:53 DP1 TP0] Decode batch. #running-req: 1, #token: 760, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:50:54 DP0 TP0] Decode batch. #running-req: 1, #token: 844, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.34, #queue-req: 0, 
[2025-07-28 00:50:54 DP1 TP0] Decode batch. #running-req: 1, #token: 800, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:50:55 DP0 TP0] Decode batch. #running-req: 1, #token: 884, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.40, #queue-req: 0, 
[2025-07-28 00:50:55 DP1 TP0] Decode batch. #running-req: 1, #token: 840, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:50:56 DP0 TP0] Decode batch. #running-req: 1, #token: 924, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.36, #queue-req: 0, 
[2025-07-28 00:50:56 DP1 TP0] Decode batch. #running-req: 1, #token: 880, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:50:57 DP0 TP0] Decode batch. #running-req: 1, #token: 964, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:50:57 DP1 TP0] Decode batch. #running-req: 1, #token: 920, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:50:58] INFO:     127.0.0.1:50422 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:50:58 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 114, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:50:58 DP0 TP0] Decode batch. #running-req: 1, #token: 225, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.42, #queue-req: 0, 
[2025-07-28 00:50:58 DP1 TP0] Decode batch. #running-req: 1, #token: 960, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:50:59 DP0 TP0] Decode batch. #running-req: 1, #token: 265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.72, #queue-req: 0, 
[2025-07-28 00:50:59] INFO:     127.0.0.1:50432 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:50:59 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 80, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:51:00 DP1 TP0] Decode batch. #running-req: 1, #token: 194, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.54, #queue-req: 0, 
[2025-07-28 00:51:00 DP0 TP0] Decode batch. #running-req: 1, #token: 305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.57, #queue-req: 0, 
[2025-07-28 00:51:01 DP1 TP0] Decode batch. #running-req: 1, #token: 234, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.76, #queue-req: 0, 
[2025-07-28 00:51:02 DP0 TP0] Decode batch. #running-req: 1, #token: 345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.55, #queue-req: 0, 
[2025-07-28 00:51:02 DP1 TP0] Decode batch. #running-req: 1, #token: 274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.68, #queue-req: 0, 
[2025-07-28 00:51:03 DP0 TP0] Decode batch. #running-req: 1, #token: 385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.50, #queue-req: 0, 
[2025-07-28 00:51:03 DP1 TP0] Decode batch. #running-req: 1, #token: 314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.58, #queue-req: 0, 
[2025-07-28 00:51:04 DP0 TP0] Decode batch. #running-req: 1, #token: 425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:51:04 DP1 TP0] Decode batch. #running-req: 1, #token: 354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.56, #queue-req: 0, 
[2025-07-28 00:51:05 DP0 TP0] Decode batch. #running-req: 1, #token: 465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:51:05 DP1 TP0] Decode batch. #running-req: 1, #token: 394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.52, #queue-req: 0, 
[2025-07-28 00:51:06 DP0 TP0] Decode batch. #running-req: 1, #token: 505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:51:06 DP1 TP0] Decode batch. #running-req: 1, #token: 434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.47, #queue-req: 0, 
[2025-07-28 00:51:07 DP0 TP0] Decode batch. #running-req: 1, #token: 545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:51:07 DP1 TP0] Decode batch. #running-req: 1, #token: 474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:51:08 DP0 TP0] Decode batch. #running-req: 1, #token: 585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:51:09 DP1 TP0] Decode batch. #running-req: 1, #token: 514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:51:09 DP0 TP0] Decode batch. #running-req: 1, #token: 625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:51:10 DP1 TP0] Decode batch. #running-req: 1, #token: 554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:51:11 DP0 TP0] Decode batch. #running-req: 1, #token: 665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:51:11 DP1 TP0] Decode batch. #running-req: 1, #token: 594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:51:12 DP0 TP0] Decode batch. #running-req: 1, #token: 705, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:51:12 DP1 TP0] Decode batch. #running-req: 1, #token: 634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:51:13] INFO:     127.0.0.1:59514 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:51:13 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 136, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:51:13 DP0 TP0] Decode batch. #running-req: 1, #token: 245, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.45, #queue-req: 0, 
[2025-07-28 00:51:13 DP1 TP0] Decode batch. #running-req: 1, #token: 674, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:51:14 DP0 TP0] Decode batch. #running-req: 1, #token: 285, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.63, #queue-req: 0, 
[2025-07-28 00:51:14 DP1 TP0] Decode batch. #running-req: 1, #token: 714, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:51:15 DP0 TP0] Decode batch. #running-req: 1, #token: 325, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.19, #queue-req: 0, 
[2025-07-28 00:51:15 DP1 TP0] Decode batch. #running-req: 1, #token: 754, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:51:16 DP0 TP0] Decode batch. #running-req: 1, #token: 365, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.54, #queue-req: 0, 
[2025-07-28 00:51:16] INFO:     127.0.0.1:59526 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:51:16 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 137, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:51:17 DP1 TP0] Decode batch. #running-req: 1, #token: 250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.44, #queue-req: 0, 
[2025-07-28 00:51:17 DP0 TP0] Decode batch. #running-req: 1, #token: 405, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.48, #queue-req: 0, 
[2025-07-28 00:51:18 DP1 TP0] Decode batch. #running-req: 1, #token: 290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.62, #queue-req: 0, 
[2025-07-28 00:51:19 DP0 TP0] Decode batch. #running-req: 1, #token: 445, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:51:19 DP1 TP0] Decode batch. #running-req: 1, #token: 330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.57, #queue-req: 0, 
[2025-07-28 00:51:20 DP0 TP0] Decode batch. #running-req: 1, #token: 485, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:51:20 DP1 TP0] Decode batch. #running-req: 1, #token: 370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.54, #queue-req: 0, 
[2025-07-28 00:51:21 DP0 TP0] Decode batch. #running-req: 1, #token: 525, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:51:21 DP1 TP0] Decode batch. #running-req: 1, #token: 410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.48, #queue-req: 0, 
[2025-07-28 00:51:22 DP0 TP0] Decode batch. #running-req: 1, #token: 565, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:51:22 DP1 TP0] Decode batch. #running-req: 1, #token: 450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:51:23] INFO:     127.0.0.1:50220 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:51:23 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 85, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:51:23 DP0 TP0] Decode batch. #running-req: 1, #token: 204, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.47, #queue-req: 0, 
[2025-07-28 00:51:23 DP1 TP0] Decode batch. #running-req: 1, #token: 490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:51:24 DP0 TP0] Decode batch. #running-req: 1, #token: 244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.74, #queue-req: 0, 
[2025-07-28 00:51:24 DP1 TP0] Decode batch. #running-req: 1, #token: 530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:51:25 DP0 TP0] Decode batch. #running-req: 1, #token: 284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.63, #queue-req: 0, 
[2025-07-28 00:51:26 DP1 TP0] Decode batch. #running-req: 1, #token: 570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:51:26 DP0 TP0] Decode batch. #running-req: 1, #token: 324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.57, #queue-req: 0, 
[2025-07-28 00:51:27 DP1 TP0] Decode batch. #running-req: 1, #token: 610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:51:28 DP0 TP0] Decode batch. #running-req: 1, #token: 364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.53, #queue-req: 0, 
[2025-07-28 00:51:28 DP1 TP0] Decode batch. #running-req: 1, #token: 650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:51:29 DP0 TP0] Decode batch. #running-req: 1, #token: 404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.48, #queue-req: 0, 
[2025-07-28 00:51:29 DP1 TP0] Decode batch. #running-req: 1, #token: 690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:51:30 DP0 TP0] Decode batch. #running-req: 1, #token: 444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:51:30 DP1 TP0] Decode batch. #running-req: 1, #token: 730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:51:31 DP0 TP0] Decode batch. #running-req: 1, #token: 484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:51:31 DP1 TP0] Decode batch. #running-req: 1, #token: 770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:51:32 DP0 TP0] Decode batch. #running-req: 1, #token: 524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:51:32 DP1 TP0] Decode batch. #running-req: 1, #token: 810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:51:33 DP0 TP0] Decode batch. #running-req: 1, #token: 564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:51:34 DP1 TP0] Decode batch. #running-req: 1, #token: 850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:51:34] INFO:     127.0.0.1:50232 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:51:34 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 157, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:51:34 DP0 TP0] Decode batch. #running-req: 1, #token: 604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:51:35 DP1 TP0] Decode batch. #running-req: 1, #token: 288, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.43, #queue-req: 0, 
[2025-07-28 00:51:35 DP0 TP0] Decode batch. #running-req: 1, #token: 644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:51:36 DP1 TP0] Decode batch. #running-req: 1, #token: 328, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.58, #queue-req: 0, 
[2025-07-28 00:51:36] INFO:     127.0.0.1:36658 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:51:36 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 150, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:51:37 DP0 TP0] Decode batch. #running-req: 1, #token: 286, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.41, #queue-req: 0, 
[2025-07-28 00:51:37 DP1 TP0] Decode batch. #running-req: 1, #token: 368, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.54, #queue-req: 0, 
[2025-07-28 00:51:38 DP0 TP0] Decode batch. #running-req: 1, #token: 326, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.57, #queue-req: 0, 
[2025-07-28 00:51:38 DP1 TP0] Decode batch. #running-req: 1, #token: 408, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.49, #queue-req: 0, 
[2025-07-28 00:51:39 DP0 TP0] Decode batch. #running-req: 1, #token: 366, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.53, #queue-req: 0, 
[2025-07-28 00:51:39 DP1 TP0] Decode batch. #running-req: 1, #token: 448, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:51:40 DP0 TP0] Decode batch. #running-req: 1, #token: 406, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.09, #queue-req: 0, 
[2025-07-28 00:51:40 DP1 TP0] Decode batch. #running-req: 1, #token: 488, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.47, #queue-req: 0, 
[2025-07-28 00:51:41 DP0 TP0] Decode batch. #running-req: 1, #token: 446, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:51:41 DP1 TP0] Decode batch. #running-req: 1, #token: 528, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:51:42 DP0 TP0] Decode batch. #running-req: 1, #token: 486, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:51:43 DP1 TP0] Decode batch. #running-req: 1, #token: 568, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:51:43 DP0 TP0] Decode batch. #running-req: 1, #token: 526, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:51:44 DP1 TP0] Decode batch. #running-req: 1, #token: 608, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:51:45 DP0 TP0] Decode batch. #running-req: 1, #token: 566, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:51:45 DP1 TP0] Decode batch. #running-req: 1, #token: 648, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:51:46 DP0 TP0] Decode batch. #running-req: 1, #token: 606, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:51:46 DP1 TP0] Decode batch. #running-req: 1, #token: 688, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:51:47 DP0 TP0] Decode batch. #running-req: 1, #token: 646, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:51:47 DP1 TP0] Decode batch. #running-req: 1, #token: 728, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:51:48 DP0 TP0] Decode batch. #running-req: 1, #token: 686, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:51:48 DP1 TP0] Decode batch. #running-req: 1, #token: 768, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:51:49] INFO:     127.0.0.1:55832 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:51:49 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 111, #cached-token: 106, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:51:49 DP1 TP0] Decode batch. #running-req: 2, #token: 935, token usage: 0.00, cuda graph: True, gen throughput (token/s): 45.83, #queue-req: 0, 
[2025-07-28 00:51:49] INFO:     127.0.0.1:55822 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:51:50 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 95, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:51:50 DP0 TP0] Decode batch. #running-req: 1, #token: 209, token usage: 0.00, cuda graph: True, gen throughput (token/s): 22.20, #queue-req: 0, 
[2025-07-28 00:51:51 DP1 TP0] Decode batch. #running-req: 1, #token: 273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 38.26, #queue-req: 0, 
[2025-07-28 00:51:51 DP0 TP0] Decode batch. #running-req: 1, #token: 249, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.73, #queue-req: 0, 
[2025-07-28 00:51:52 DP1 TP0] Decode batch. #running-req: 1, #token: 313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.58, #queue-req: 0, 
[2025-07-28 00:51:52 DP0 TP0] Decode batch. #running-req: 1, #token: 289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.60, #queue-req: 0, 
[2025-07-28 00:51:53 DP1 TP0] Decode batch. #running-req: 1, #token: 353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.56, #queue-req: 0, 
[2025-07-28 00:51:53 DP0 TP0] Decode batch. #running-req: 1, #token: 329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.56, #queue-req: 0, 
[2025-07-28 00:51:54 DP1 TP0] Decode batch. #running-req: 1, #token: 393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.51, #queue-req: 0, 
[2025-07-28 00:51:54 DP0 TP0] Decode batch. #running-req: 1, #token: 369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.53, #queue-req: 0, 
[2025-07-28 00:51:55 DP1 TP0] Decode batch. #running-req: 1, #token: 433, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.47, #queue-req: 0, 
[2025-07-28 00:51:55 DP0 TP0] Decode batch. #running-req: 1, #token: 409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.48, #queue-req: 0, 
[2025-07-28 00:51:56 DP1 TP0] Decode batch. #running-req: 1, #token: 473, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:51:57 DP0 TP0] Decode batch. #running-req: 1, #token: 449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:51:57 DP1 TP0] Decode batch. #running-req: 1, #token: 513, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:51:58 DP0 TP0] Decode batch. #running-req: 1, #token: 489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:51:58 DP1 TP0] Decode batch. #running-req: 1, #token: 553, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:51:59 DP0 TP0] Decode batch. #running-req: 1, #token: 529, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:51:59] INFO:     127.0.0.1:52556 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:51:59 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 89, #cached-token: 106, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:52:00 DP1 TP0] Decode batch. #running-req: 2, #token: 686, token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.18, #queue-req: 0, 
[2025-07-28 00:52:01 DP1 TP0] Decode batch. #running-req: 2, #token: 766, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.76, #queue-req: 0, 
[2025-07-28 00:52:02 DP1 TP0] Decode batch. #running-req: 2, #token: 846, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.76, #queue-req: 0, 
[2025-07-28 00:52:03 DP1 TP0] Decode batch. #running-req: 2, #token: 926, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.70, #queue-req: 0, 
[2025-07-28 00:52:04 DP1 TP0] Decode batch. #running-req: 2, #token: 1006, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.73, #queue-req: 0, 
[2025-07-28 00:52:05] INFO:     127.0.0.1:52548 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:52:05 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 115, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:52:05 DP1 TP0] Decode batch. #running-req: 1, #token: 399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.86, #queue-req: 0, 
[2025-07-28 00:52:06 DP0 TP0] Decode batch. #running-req: 1, #token: 238, token usage: 0.00, cuda graph: True, gen throughput (token/s): 5.68, #queue-req: 0, 
[2025-07-28 00:52:07 DP1 TP0] Decode batch. #running-req: 1, #token: 439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:52:07 DP0 TP0] Decode batch. #running-req: 1, #token: 278, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.66, #queue-req: 0, 
[2025-07-28 00:52:08 DP1 TP0] Decode batch. #running-req: 1, #token: 479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:52:08 DP0 TP0] Decode batch. #running-req: 1, #token: 318, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.57, #queue-req: 0, 
[2025-07-28 00:52:09 DP1 TP0] Decode batch. #running-req: 1, #token: 519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:52:09 DP0 TP0] Decode batch. #running-req: 1, #token: 358, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.54, #queue-req: 0, 
[2025-07-28 00:52:10 DP1 TP0] Decode batch. #running-req: 1, #token: 559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:52:10 DP0 TP0] Decode batch. #running-req: 1, #token: 398, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.49, #queue-req: 0, 
[2025-07-28 00:52:11 DP1 TP0] Decode batch. #running-req: 1, #token: 599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:52:11 DP0 TP0] Decode batch. #running-req: 1, #token: 438, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:52:12 DP1 TP0] Decode batch. #running-req: 1, #token: 639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:52:13 DP0 TP0] Decode batch. #running-req: 1, #token: 478, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:52:13 DP1 TP0] Decode batch. #running-req: 1, #token: 679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:52:14 DP0 TP0] Decode batch. #running-req: 1, #token: 518, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:52:14 DP1 TP0] Decode batch. #running-req: 1, #token: 719, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:52:15 DP0 TP0] Decode batch. #running-req: 1, #token: 558, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:52:16 DP1 TP0] Decode batch. #running-req: 1, #token: 759, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:52:16 DP0 TP0] Decode batch. #running-req: 1, #token: 598, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.03, #queue-req: 0, 
[2025-07-28 00:52:16] INFO:     127.0.0.1:37910 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:52:16 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 164, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:52:17 DP1 TP0] Decode batch. #running-req: 1, #token: 289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.37, #queue-req: 0, 
[2025-07-28 00:52:17 DP0 TP0] Decode batch. #running-req: 1, #token: 638, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:52:18 DP1 TP0] Decode batch. #running-req: 1, #token: 329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.59, #queue-req: 0, 
[2025-07-28 00:52:18 DP0 TP0] Decode batch. #running-req: 1, #token: 678, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:52:19 DP1 TP0] Decode batch. #running-req: 1, #token: 369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.55, #queue-req: 0, 
[2025-07-28 00:52:19] INFO:     127.0.0.1:60034 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:52:19 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 143, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:52:19 DP0 TP0] Decode batch. #running-req: 1, #token: 253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.36, #queue-req: 0, 
[2025-07-28 00:52:20 DP1 TP0] Decode batch. #running-req: 1, #token: 409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.49, #queue-req: 0, 
[2025-07-28 00:52:21 DP0 TP0] Decode batch. #running-req: 1, #token: 293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.60, #queue-req: 0, 
[2025-07-28 00:52:21 DP1 TP0] Decode batch. #running-req: 1, #token: 449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:52:22 DP0 TP0] Decode batch. #running-req: 1, #token: 333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.56, #queue-req: 0, 
[2025-07-28 00:52:22 DP1 TP0] Decode batch. #running-req: 1, #token: 489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:52:23 DP0 TP0] Decode batch. #running-req: 1, #token: 373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.51, #queue-req: 0, 
[2025-07-28 00:52:24 DP1 TP0] Decode batch. #running-req: 1, #token: 529, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:52:24 DP0 TP0] Decode batch. #running-req: 1, #token: 413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:52:25 DP1 TP0] Decode batch. #running-req: 1, #token: 569, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:52:25 DP0 TP0] Decode batch. #running-req: 1, #token: 453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:52:26 DP1 TP0] Decode batch. #running-req: 1, #token: 609, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:52:26 DP0 TP0] Decode batch. #running-req: 1, #token: 493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:52:27 DP1 TP0] Decode batch. #running-req: 1, #token: 649, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:52:27 DP0 TP0] Decode batch. #running-req: 1, #token: 533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:52:28 DP1 TP0] Decode batch. #running-req: 1, #token: 689, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:52:28 DP0 TP0] Decode batch. #running-req: 1, #token: 573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:52:29 DP1 TP0] Decode batch. #running-req: 1, #token: 729, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:52:30 DP0 TP0] Decode batch. #running-req: 1, #token: 613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:52:30 DP1 TP0] Decode batch. #running-req: 1, #token: 769, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:52:31 DP0 TP0] Decode batch. #running-req: 1, #token: 653, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:52:31 DP1 TP0] Decode batch. #running-req: 1, #token: 809, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:52:32 DP0 TP0] Decode batch. #running-req: 1, #token: 693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:52:33 DP1 TP0] Decode batch. #running-req: 1, #token: 849, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:52:33 DP0 TP0] Decode batch. #running-req: 1, #token: 733, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:52:33] INFO:     127.0.0.1:37446 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:52:33 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 117, #cached-token: 109, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:52:34 DP1 TP0] Decode batch. #running-req: 2, #token: 1018, token usage: 0.00, cuda graph: True, gen throughput (token/s): 41.83, #queue-req: 0, 
[2025-07-28 00:52:34] INFO:     127.0.0.1:37438 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:52:34 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 125, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:52:35 DP0 TP0] Decode batch. #running-req: 1, #token: 258, token usage: 0.00, cuda graph: True, gen throughput (token/s): 23.67, #queue-req: 0, 
[2025-07-28 00:52:35 DP1 TP0] Decode batch. #running-req: 1, #token: 277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 39.08, #queue-req: 0, 
[2025-07-28 00:52:36 DP0 TP0] Decode batch. #running-req: 1, #token: 298, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.57, #queue-req: 0, 
[2025-07-28 00:52:36 DP1 TP0] Decode batch. #running-req: 1, #token: 317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.58, #queue-req: 0, 
[2025-07-28 00:52:37 DP0 TP0] Decode batch. #running-req: 1, #token: 338, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.56, #queue-req: 0, 
[2025-07-28 00:52:37 DP1 TP0] Decode batch. #running-req: 1, #token: 357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.55, #queue-req: 0, 
[2025-07-28 00:52:38 DP0 TP0] Decode batch. #running-req: 1, #token: 378, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.51, #queue-req: 0, 
[2025-07-28 00:52:38 DP1 TP0] Decode batch. #running-req: 1, #token: 397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.50, #queue-req: 0, 
[2025-07-28 00:52:39 DP0 TP0] Decode batch. #running-req: 1, #token: 418, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:52:39 DP1 TP0] Decode batch. #running-req: 1, #token: 437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.47, #queue-req: 0, 
[2025-07-28 00:52:40 DP0 TP0] Decode batch. #running-req: 1, #token: 458, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:52:41 DP1 TP0] Decode batch. #running-req: 1, #token: 477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:52:41 DP0 TP0] Decode batch. #running-req: 1, #token: 498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.06, #queue-req: 0, 
[2025-07-28 00:52:42 DP1 TP0] Decode batch. #running-req: 1, #token: 517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:52:43 DP0 TP0] Decode batch. #running-req: 1, #token: 538, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:52:43 DP1 TP0] Decode batch. #running-req: 1, #token: 557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:52:44 DP0 TP0] Decode batch. #running-req: 1, #token: 578, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:52:44 DP1 TP0] Decode batch. #running-req: 1, #token: 597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:52:45 DP0 TP0] Decode batch. #running-req: 1, #token: 618, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:52:45 DP1 TP0] Decode batch. #running-req: 1, #token: 637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:52:46 DP0 TP0] Decode batch. #running-req: 1, #token: 658, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:52:46 DP1 TP0] Decode batch. #running-req: 1, #token: 677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:52:47 DP0 TP0] Decode batch. #running-req: 1, #token: 698, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:52:47 DP1 TP0] Decode batch. #running-req: 1, #token: 717, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:52:47] INFO:     127.0.0.1:32970 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:52:47 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 196, #cached-token: 109, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:52:49 DP1 TP0] Decode batch. #running-req: 2, #token: 992, token usage: 0.00, cuda graph: True, gen throughput (token/s): 62.64, #queue-req: 0, 
[2025-07-28 00:52:50 DP1 TP0] Decode batch. #running-req: 2, #token: 1072, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.71, #queue-req: 0, 
[2025-07-28 00:52:50] INFO:     127.0.0.1:32964 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:52:50 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:52:51 DP0 TP0] Decode batch. #running-req: 1, #token: 220, token usage: 0.00, cuda graph: True, gen throughput (token/s): 11.11, #queue-req: 0, 
[2025-07-28 00:52:51 DP1 TP0] Decode batch. #running-req: 1, #token: 423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 37.17, #queue-req: 0, 
[2025-07-28 00:52:52 DP0 TP0] Decode batch. #running-req: 1, #token: 260, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.73, #queue-req: 0, 
[2025-07-28 00:52:52 DP1 TP0] Decode batch. #running-req: 1, #token: 463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:52:53 DP0 TP0] Decode batch. #running-req: 1, #token: 300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.57, #queue-req: 0, 
[2025-07-28 00:52:53 DP1 TP0] Decode batch. #running-req: 1, #token: 503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:52:54 DP0 TP0] Decode batch. #running-req: 1, #token: 340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.54, #queue-req: 0, 
[2025-07-28 00:52:54 DP1 TP0] Decode batch. #running-req: 1, #token: 543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:52:55 DP0 TP0] Decode batch. #running-req: 1, #token: 380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.52, #queue-req: 0, 
[2025-07-28 00:52:55 DP1 TP0] Decode batch. #running-req: 1, #token: 583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:52:56 DP0 TP0] Decode batch. #running-req: 1, #token: 420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:52:56 DP1 TP0] Decode batch. #running-req: 1, #token: 623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:52:57 DP0 TP0] Decode batch. #running-req: 1, #token: 460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:52:58 DP1 TP0] Decode batch. #running-req: 1, #token: 663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:52:59 DP0 TP0] Decode batch. #running-req: 1, #token: 500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:52:59 DP1 TP0] Decode batch. #running-req: 1, #token: 703, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:53:00 DP0 TP0] Decode batch. #running-req: 1, #token: 540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:53:00 DP1 TP0] Decode batch. #running-req: 1, #token: 743, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:53:01 DP0 TP0] Decode batch. #running-req: 1, #token: 580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:53:01 DP1 TP0] Decode batch. #running-req: 1, #token: 783, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:53:02 DP0 TP0] Decode batch. #running-req: 1, #token: 620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:53:02 DP1 TP0] Decode batch. #running-req: 1, #token: 823, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:53:03] INFO:     127.0.0.1:47494 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:53:03 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 154, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:53:03] INFO:     127.0.0.1:47502 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:53:03 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 128, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:53:03 DP0 TP0] Decode batch. #running-req: 1, #token: 238, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.58, #queue-req: 0, 
[2025-07-28 00:53:03 DP1 TP0] Decode batch. #running-req: 1, #token: 276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.40, #queue-req: 0, 
[2025-07-28 00:53:04 DP0 TP0] Decode batch. #running-req: 1, #token: 278, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.65, #queue-req: 0, 
[2025-07-28 00:53:04 DP1 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.59, #queue-req: 0, 
[2025-07-28 00:53:05 DP0 TP0] Decode batch. #running-req: 1, #token: 318, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.56, #queue-req: 0, 
[2025-07-28 00:53:06 DP1 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.56, #queue-req: 0, 
[2025-07-28 00:53:07 DP0 TP0] Decode batch. #running-req: 1, #token: 358, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.53, #queue-req: 0, 
[2025-07-28 00:53:07 DP1 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.50, #queue-req: 0, 
[2025-07-28 00:53:08 DP0 TP0] Decode batch. #running-req: 1, #token: 398, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.49, #queue-req: 0, 
[2025-07-28 00:53:08 DP1 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:53:09 DP0 TP0] Decode batch. #running-req: 1, #token: 438, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:53:09 DP1 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:53:10 DP0 TP0] Decode batch. #running-req: 1, #token: 478, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:53:10 DP1 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:53:11 DP0 TP0] Decode batch. #running-req: 1, #token: 518, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:53:11 DP1 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:53:12 DP0 TP0] Decode batch. #running-req: 1, #token: 558, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:53:12 DP1 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:53:13 DP0 TP0] Decode batch. #running-req: 1, #token: 598, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:53:13 DP1 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:53:14 DP0 TP0] Decode batch. #running-req: 1, #token: 638, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:53:15 DP1 TP0] Decode batch. #running-req: 1, #token: 676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:53:16 DP0 TP0] Decode batch. #running-req: 1, #token: 678, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:53:16 DP1 TP0] Decode batch. #running-req: 1, #token: 716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:53:16] INFO:     127.0.0.1:56210 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:53:16 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 108, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:53:17 DP1 TP0] Decode batch. #running-req: 2, #token: 939, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.72, #queue-req: 0, 
[2025-07-28 00:53:18] INFO:     127.0.0.1:56204 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:53:18 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:53:18 DP1 TP0] Decode batch. #running-req: 1, #token: 331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 56.59, #queue-req: 0, 
[2025-07-28 00:53:19 DP0 TP0] Decode batch. #running-req: 1, #token: 282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 12.77, #queue-req: 0, 
[2025-07-28 00:53:19 DP1 TP0] Decode batch. #running-req: 1, #token: 371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.54, #queue-req: 0, 
[2025-07-28 00:53:20 DP0 TP0] Decode batch. #running-req: 1, #token: 322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.57, #queue-req: 0, 
[2025-07-28 00:53:20 DP1 TP0] Decode batch. #running-req: 1, #token: 411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.50, #queue-req: 0, 
[2025-07-28 00:53:21 DP0 TP0] Decode batch. #running-req: 1, #token: 362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.53, #queue-req: 0, 
[2025-07-28 00:53:21 DP1 TP0] Decode batch. #running-req: 1, #token: 451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:53:22 DP0 TP0] Decode batch. #running-req: 1, #token: 402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.48, #queue-req: 0, 
[2025-07-28 00:53:23 DP1 TP0] Decode batch. #running-req: 1, #token: 491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.47, #queue-req: 0, 
[2025-07-28 00:53:23 DP0 TP0] Decode batch. #running-req: 1, #token: 442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:53:24 DP1 TP0] Decode batch. #running-req: 1, #token: 531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:53:24 DP0 TP0] Decode batch. #running-req: 1, #token: 482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:53:25 DP1 TP0] Decode batch. #running-req: 1, #token: 571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:53:25 DP0 TP0] Decode batch. #running-req: 1, #token: 522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:53:26 DP1 TP0] Decode batch. #running-req: 1, #token: 611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:53:27 DP0 TP0] Decode batch. #running-req: 1, #token: 562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:53:27 DP1 TP0] Decode batch. #running-req: 1, #token: 651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:53:28 DP0 TP0] Decode batch. #running-req: 1, #token: 602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:53:28 DP1 TP0] Decode batch. #running-req: 1, #token: 691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:53:29] INFO:     127.0.0.1:48298 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:53:29 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 95, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:53:29 DP0 TP0] Decode batch. #running-req: 1, #token: 642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:53:29 DP1 TP0] Decode batch. #running-req: 1, #token: 225, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.68, #queue-req: 0, 
[2025-07-28 00:53:30 DP0 TP0] Decode batch. #running-req: 1, #token: 682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:53:31 DP1 TP0] Decode batch. #running-req: 1, #token: 265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.73, #queue-req: 0, 
[2025-07-28 00:53:31 DP0 TP0] Decode batch. #running-req: 1, #token: 722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:53:31] INFO:     127.0.0.1:48306 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:53:32 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 107, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:53:32 DP1 TP0] Decode batch. #running-req: 1, #token: 305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.58, #queue-req: 0, 
[2025-07-28 00:53:32 DP0 TP0] Decode batch. #running-req: 1, #token: 273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.49, #queue-req: 0, 
[2025-07-28 00:53:33 DP1 TP0] Decode batch. #running-req: 1, #token: 345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.57, #queue-req: 0, 
[2025-07-28 00:53:33 DP0 TP0] Decode batch. #running-req: 1, #token: 313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.57, #queue-req: 0, 
[2025-07-28 00:53:34 DP1 TP0] Decode batch. #running-req: 1, #token: 385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.53, #queue-req: 0, 
[2025-07-28 00:53:35 DP0 TP0] Decode batch. #running-req: 1, #token: 353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.55, #queue-req: 0, 
[2025-07-28 00:53:35 DP1 TP0] Decode batch. #running-req: 1, #token: 425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.47, #queue-req: 0, 
[2025-07-28 00:53:36 DP0 TP0] Decode batch. #running-req: 1, #token: 393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.50, #queue-req: 0, 
[2025-07-28 00:53:36 DP1 TP0] Decode batch. #running-req: 1, #token: 465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.48, #queue-req: 0, 
[2025-07-28 00:53:37 DP0 TP0] Decode batch. #running-req: 1, #token: 433, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:53:37 DP1 TP0] Decode batch. #running-req: 1, #token: 505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:53:38 DP0 TP0] Decode batch. #running-req: 1, #token: 473, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:53:38 DP1 TP0] Decode batch. #running-req: 1, #token: 545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:53:39 DP0 TP0] Decode batch. #running-req: 1, #token: 513, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:53:40 DP1 TP0] Decode batch. #running-req: 1, #token: 585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:53:40 DP0 TP0] Decode batch. #running-req: 1, #token: 553, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:53:41 DP1 TP0] Decode batch. #running-req: 1, #token: 625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:53:41] INFO:     127.0.0.1:34196 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:53:41 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 78, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:53:41 DP0 TP0] Decode batch. #running-req: 1, #token: 593, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:53:42] INFO:     127.0.0.1:34204 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:53:42 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 146, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:53:42 DP1 TP0] Decode batch. #running-req: 1, #token: 217, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.80, #queue-req: 0, 
[2025-07-28 00:53:43 DP0 TP0] Decode batch. #running-req: 1, #token: 282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.12, #queue-req: 0, 
[2025-07-28 00:53:43 DP1 TP0] Decode batch. #running-req: 1, #token: 257, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.76, #queue-req: 0, 
[2025-07-28 00:53:44 DP0 TP0] Decode batch. #running-req: 1, #token: 322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.55, #queue-req: 0, 
[2025-07-28 00:53:44 DP1 TP0] Decode batch. #running-req: 1, #token: 297, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.59, #queue-req: 0, 
[2025-07-28 00:53:45 DP0 TP0] Decode batch. #running-req: 1, #token: 362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.54, #queue-req: 0, 
[2025-07-28 00:53:45 DP1 TP0] Decode batch. #running-req: 1, #token: 337, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.57, #queue-req: 0, 
[2025-07-28 00:53:46 DP0 TP0] Decode batch. #running-req: 1, #token: 402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.48, #queue-req: 0, 
[2025-07-28 00:53:46 DP1 TP0] Decode batch. #running-req: 1, #token: 377, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.53, #queue-req: 0, 
[2025-07-28 00:53:47 DP0 TP0] Decode batch. #running-req: 1, #token: 442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:53:48 DP1 TP0] Decode batch. #running-req: 1, #token: 417, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.48, #queue-req: 0, 
[2025-07-28 00:53:48 DP0 TP0] Decode batch. #running-req: 1, #token: 482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:53:49 DP1 TP0] Decode batch. #running-req: 1, #token: 457, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:53:49 DP0 TP0] Decode batch. #running-req: 1, #token: 522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:53:50 DP1 TP0] Decode batch. #running-req: 1, #token: 497, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:53:50 DP0 TP0] Decode batch. #running-req: 1, #token: 562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:53:51 DP1 TP0] Decode batch. #running-req: 1, #token: 537, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:53:52 DP0 TP0] Decode batch. #running-req: 1, #token: 602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:53:52 DP1 TP0] Decode batch. #running-req: 1, #token: 577, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:53:53 DP0 TP0] Decode batch. #running-req: 1, #token: 642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:53:53 DP1 TP0] Decode batch. #running-req: 1, #token: 617, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:53:54 DP0 TP0] Decode batch. #running-req: 1, #token: 682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:53:54] INFO:     127.0.0.1:49210 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:53:54 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:53:54 DP1 TP0] Decode batch. #running-req: 1, #token: 201, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.62, #queue-req: 0, 
[2025-07-28 00:53:55 DP0 TP0] Decode batch. #running-req: 1, #token: 722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:53:55 DP1 TP0] Decode batch. #running-req: 1, #token: 241, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.75, #queue-req: 0, 
[2025-07-28 00:53:56] INFO:     127.0.0.1:49212 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:53:56 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 173, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:53:56 DP0 TP0] Decode batch. #running-req: 1, #token: 294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.39, #queue-req: 0, 
[2025-07-28 00:53:57 DP1 TP0] Decode batch. #running-req: 1, #token: 281, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.65, #queue-req: 0, 
[2025-07-28 00:53:57 DP0 TP0] Decode batch. #running-req: 1, #token: 334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.57, #queue-req: 0, 
[2025-07-28 00:53:58 DP1 TP0] Decode batch. #running-req: 1, #token: 321, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.56, #queue-req: 0, 
[2025-07-28 00:53:58 DP0 TP0] Decode batch. #running-req: 1, #token: 374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.52, #queue-req: 0, 
[2025-07-28 00:53:59 DP1 TP0] Decode batch. #running-req: 1, #token: 361, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.54, #queue-req: 0, 
[2025-07-28 00:53:59 DP0 TP0] Decode batch. #running-req: 1, #token: 414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.47, #queue-req: 0, 
[2025-07-28 00:54:00 DP1 TP0] Decode batch. #running-req: 1, #token: 401, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.50, #queue-req: 0, 
[2025-07-28 00:54:01 DP0 TP0] Decode batch. #running-req: 1, #token: 454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:54:01 DP1 TP0] Decode batch. #running-req: 1, #token: 441, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:54:02 DP0 TP0] Decode batch. #running-req: 1, #token: 494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:54:02 DP1 TP0] Decode batch. #running-req: 1, #token: 481, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:54:03 DP0 TP0] Decode batch. #running-req: 1, #token: 534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:54:03 DP1 TP0] Decode batch. #running-req: 1, #token: 521, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:54:04 DP0 TP0] Decode batch. #running-req: 1, #token: 574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:54:04 DP1 TP0] Decode batch. #running-req: 1, #token: 561, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:54:05 DP0 TP0] Decode batch. #running-req: 1, #token: 614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:54:06 DP1 TP0] Decode batch. #running-req: 1, #token: 601, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:54:06 DP0 TP0] Decode batch. #running-req: 1, #token: 654, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:54:07 DP1 TP0] Decode batch. #running-req: 1, #token: 641, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:54:07 DP0 TP0] Decode batch. #running-req: 1, #token: 694, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:54:08 DP1 TP0] Decode batch. #running-req: 1, #token: 681, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:54:09 DP0 TP0] Decode batch. #running-req: 1, #token: 734, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:54:09 DP1 TP0] Decode batch. #running-req: 1, #token: 721, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:54:10 DP0 TP0] Decode batch. #running-req: 1, #token: 774, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:54:10] INFO:     127.0.0.1:34426 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:54:10 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 178, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:54:10 DP1 TP0] Decode batch. #running-req: 1, #token: 290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.37, #queue-req: 0, 
[2025-07-28 00:54:11 DP0 TP0] Decode batch. #running-req: 1, #token: 814, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:54:11 DP1 TP0] Decode batch. #running-req: 1, #token: 330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.58, #queue-req: 0, 
[2025-07-28 00:54:12 DP0 TP0] Decode batch. #running-req: 1, #token: 854, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:54:12 DP1 TP0] Decode batch. #running-req: 1, #token: 370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.53, #queue-req: 0, 
[2025-07-28 00:54:12] INFO:     127.0.0.1:34442 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:54:12 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 88, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:54:13 DP0 TP0] Decode batch. #running-req: 1, #token: 214, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.63, #queue-req: 0, 
[2025-07-28 00:54:14 DP1 TP0] Decode batch. #running-req: 1, #token: 410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.49, #queue-req: 0, 
[2025-07-28 00:54:14 DP0 TP0] Decode batch. #running-req: 1, #token: 254, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.75, #queue-req: 0, 
[2025-07-28 00:54:15 DP1 TP0] Decode batch. #running-req: 1, #token: 450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.47, #queue-req: 0, 
[2025-07-28 00:54:15 DP0 TP0] Decode batch. #running-req: 1, #token: 294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.58, #queue-req: 0, 
[2025-07-28 00:54:16 DP1 TP0] Decode batch. #running-req: 1, #token: 490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:54:16 DP0 TP0] Decode batch. #running-req: 1, #token: 334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.57, #queue-req: 0, 
[2025-07-28 00:54:17 DP1 TP0] Decode batch. #running-req: 1, #token: 530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:54:18 DP0 TP0] Decode batch. #running-req: 1, #token: 374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.52, #queue-req: 0, 
[2025-07-28 00:54:18 DP1 TP0] Decode batch. #running-req: 1, #token: 570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:54:19 DP0 TP0] Decode batch. #running-req: 1, #token: 414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.09, #queue-req: 0, 
[2025-07-28 00:54:19 DP1 TP0] Decode batch. #running-req: 1, #token: 610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:54:20 DP0 TP0] Decode batch. #running-req: 1, #token: 454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:54:20 DP1 TP0] Decode batch. #running-req: 1, #token: 650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:54:21 DP0 TP0] Decode batch. #running-req: 1, #token: 494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:54:21] INFO:     127.0.0.1:41010 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:54:21 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 153, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:54:22 DP1 TP0] Decode batch. #running-req: 1, #token: 268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.40, #queue-req: 0, 
[2025-07-28 00:54:22] INFO:     127.0.0.1:45364 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:54:22 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 123, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:54:22 DP0 TP0] Decode batch. #running-req: 1, #token: 238, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.64, #queue-req: 0, 
[2025-07-28 00:54:23 DP1 TP0] Decode batch. #running-req: 1, #token: 308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.59, #queue-req: 0, 
[2025-07-28 00:54:23 DP0 TP0] Decode batch. #running-req: 1, #token: 278, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.66, #queue-req: 0, 
[2025-07-28 00:54:24 DP1 TP0] Decode batch. #running-req: 1, #token: 348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.57, #queue-req: 0, 
[2025-07-28 00:54:24 DP0 TP0] Decode batch. #running-req: 1, #token: 318, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.56, #queue-req: 0, 
[2025-07-28 00:54:25 DP1 TP0] Decode batch. #running-req: 1, #token: 388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.53, #queue-req: 0, 
[2025-07-28 00:54:26 DP0 TP0] Decode batch. #running-req: 1, #token: 358, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.54, #queue-req: 0, 
[2025-07-28 00:54:26 DP1 TP0] Decode batch. #running-req: 1, #token: 428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.47, #queue-req: 0, 
[2025-07-28 00:54:27 DP0 TP0] Decode batch. #running-req: 1, #token: 398, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.47, #queue-req: 0, 
[2025-07-28 00:54:27 DP1 TP0] Decode batch. #running-req: 1, #token: 468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:54:28 DP0 TP0] Decode batch. #running-req: 1, #token: 438, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:54:28 DP1 TP0] Decode batch. #running-req: 1, #token: 508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:54:29 DP0 TP0] Decode batch. #running-req: 1, #token: 478, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:54:29 DP1 TP0] Decode batch. #running-req: 1, #token: 548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:54:30 DP0 TP0] Decode batch. #running-req: 1, #token: 518, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:54:31 DP1 TP0] Decode batch. #running-req: 1, #token: 588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:54:31 DP0 TP0] Decode batch. #running-req: 1, #token: 558, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:54:32 DP1 TP0] Decode batch. #running-req: 1, #token: 628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:54:32 DP0 TP0] Decode batch. #running-req: 1, #token: 598, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:54:33 DP1 TP0] Decode batch. #running-req: 1, #token: 668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:54:33 DP0 TP0] Decode batch. #running-req: 1, #token: 638, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:54:34 DP1 TP0] Decode batch. #running-req: 1, #token: 708, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:54:35 DP0 TP0] Decode batch. #running-req: 1, #token: 678, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:54:35 DP1 TP0] Decode batch. #running-req: 1, #token: 748, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:54:36 DP0 TP0] Decode batch. #running-req: 1, #token: 718, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:54:36] INFO:     127.0.0.1:45372 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:54:36 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 129, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:54:36] INFO:     127.0.0.1:55496 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:54:36 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 112, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:54:36 DP1 TP0] Decode batch. #running-req: 1, #token: 253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.60, #queue-req: 0, 
[2025-07-28 00:54:37 DP0 TP0] Decode batch. #running-req: 1, #token: 268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.70, #queue-req: 0, 
[2025-07-28 00:54:37 DP1 TP0] Decode batch. #running-req: 1, #token: 293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.60, #queue-req: 0, 
[2025-07-28 00:54:38 DP0 TP0] Decode batch. #running-req: 1, #token: 308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.57, #queue-req: 0, 
[2025-07-28 00:54:39 DP1 TP0] Decode batch. #running-req: 1, #token: 333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.58, #queue-req: 0, 
[2025-07-28 00:54:39 DP0 TP0] Decode batch. #running-req: 1, #token: 348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.56, #queue-req: 0, 
[2025-07-28 00:54:40 DP1 TP0] Decode batch. #running-req: 1, #token: 373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.53, #queue-req: 0, 
[2025-07-28 00:54:40 DP0 TP0] Decode batch. #running-req: 1, #token: 388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.51, #queue-req: 0, 
[2025-07-28 00:54:41 DP1 TP0] Decode batch. #running-req: 1, #token: 413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.48, #queue-req: 0, 
[2025-07-28 00:54:41 DP0 TP0] Decode batch. #running-req: 1, #token: 428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:54:42 DP1 TP0] Decode batch. #running-req: 1, #token: 453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:54:43 DP0 TP0] Decode batch. #running-req: 1, #token: 468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:54:43 DP1 TP0] Decode batch. #running-req: 1, #token: 493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:54:44 DP0 TP0] Decode batch. #running-req: 1, #token: 508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:54:44 DP1 TP0] Decode batch. #running-req: 1, #token: 533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:54:45 DP0 TP0] Decode batch. #running-req: 1, #token: 548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.08, #queue-req: 0, 
[2025-07-28 00:54:45 DP1 TP0] Decode batch. #running-req: 1, #token: 573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:54:46 DP0 TP0] Decode batch. #running-req: 1, #token: 588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:54:46 DP1 TP0] Decode batch. #running-req: 1, #token: 613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:54:47 DP0 TP0] Decode batch. #running-req: 1, #token: 628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:54:48 DP1 TP0] Decode batch. #running-req: 1, #token: 653, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:54:48 DP0 TP0] Decode batch. #running-req: 1, #token: 668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:54:49 DP1 TP0] Decode batch. #running-req: 1, #token: 693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:54:49 DP0 TP0] Decode batch. #running-req: 1, #token: 708, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:54:50 DP1 TP0] Decode batch. #running-req: 1, #token: 733, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:54:50] INFO:     127.0.0.1:47688 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:54:50 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 113, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:54:51 DP1 TP0] Decode batch. #running-req: 2, #token: 948, token usage: 0.00, cuda graph: True, gen throughput (token/s): 53.59, #queue-req: 0, 
[2025-07-28 00:54:51] INFO:     127.0.0.1:47674 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:54:51 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 103, #cached-token: 112, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:54:52 DP0 TP0] Decode batch. #running-req: 1, #token: 225, token usage: 0.00, cuda graph: True, gen throughput (token/s): 16.34, #queue-req: 0, 
[2025-07-28 00:54:52 DP1 TP0] Decode batch. #running-req: 1, #token: 323, token usage: 0.00, cuda graph: True, gen throughput (token/s): 48.28, #queue-req: 0, 
[2025-07-28 00:54:53 DP0 TP0] Decode batch. #running-req: 1, #token: 265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.70, #queue-req: 0, 
[2025-07-28 00:54:53 DP1 TP0] Decode batch. #running-req: 1, #token: 363, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.55, #queue-req: 0, 
[2025-07-28 00:54:54 DP0 TP0] Decode batch. #running-req: 1, #token: 305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.58, #queue-req: 0, 
[2025-07-28 00:54:54 DP1 TP0] Decode batch. #running-req: 1, #token: 403, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.50, #queue-req: 0, 
[2025-07-28 00:54:55 DP0 TP0] Decode batch. #running-req: 1, #token: 345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.56, #queue-req: 0, 
[2025-07-28 00:54:56 DP1 TP0] Decode batch. #running-req: 1, #token: 443, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.47, #queue-req: 0, 
[2025-07-28 00:54:56 DP0 TP0] Decode batch. #running-req: 1, #token: 385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.51, #queue-req: 0, 
[2025-07-28 00:54:57 DP1 TP0] Decode batch. #running-req: 1, #token: 483, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:54:57 DP0 TP0] Decode batch. #running-req: 1, #token: 425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:54:58 DP1 TP0] Decode batch. #running-req: 1, #token: 523, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:54:59 DP0 TP0] Decode batch. #running-req: 1, #token: 465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:54:59 DP1 TP0] Decode batch. #running-req: 1, #token: 563, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:55:00 DP0 TP0] Decode batch. #running-req: 1, #token: 505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:55:00 DP1 TP0] Decode batch. #running-req: 1, #token: 603, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:55:01 DP0 TP0] Decode batch. #running-req: 1, #token: 545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:55:01 DP1 TP0] Decode batch. #running-req: 1, #token: 643, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:55:02 DP0 TP0] Decode batch. #running-req: 1, #token: 585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:55:02 DP1 TP0] Decode batch. #running-req: 1, #token: 683, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:55:03 DP0 TP0] Decode batch. #running-req: 1, #token: 625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:55:03 DP1 TP0] Decode batch. #running-req: 1, #token: 723, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:55:04] INFO:     127.0.0.1:57244 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:55:04 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 89, #cached-token: 112, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:55:04 DP0 TP0] Decode batch. #running-req: 1, #token: 665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:55:05 DP1 TP0] Decode batch. #running-req: 1, #token: 227, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.73, #queue-req: 0, 
[2025-07-28 00:55:05 DP0 TP0] Decode batch. #running-req: 1, #token: 705, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.43, #queue-req: 0, 
[2025-07-28 00:55:06 DP1 TP0] Decode batch. #running-req: 1, #token: 267, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.71, #queue-req: 0, 
[2025-07-28 00:55:06 DP0 TP0] Decode batch. #running-req: 1, #token: 745, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:55:07 DP1 TP0] Decode batch. #running-req: 1, #token: 307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.58, #queue-req: 0, 
[2025-07-28 00:55:08 DP0 TP0] Decode batch. #running-req: 1, #token: 785, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.42, #queue-req: 0, 
[2025-07-28 00:55:08 DP1 TP0] Decode batch. #running-req: 1, #token: 347, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.56, #queue-req: 0, 
[2025-07-28 00:55:09 DP0 TP0] Decode batch. #running-req: 1, #token: 825, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:55:09 DP1 TP0] Decode batch. #running-req: 1, #token: 387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.52, #queue-req: 0, 
[2025-07-28 00:55:10 DP0 TP0] Decode batch. #running-req: 1, #token: 865, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.41, #queue-req: 0, 
[2025-07-28 00:55:10] INFO:     127.0.0.1:57254 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:55:10 DP1 TP0] Decode batch. #running-req: 1, #token: 427, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:55:11 DP1 TP0] Decode batch. #running-req: 1, #token: 467, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:55:12 DP1 TP0] Decode batch. #running-req: 1, #token: 507, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.46, #queue-req: 0, 
[2025-07-28 00:55:14 DP1 TP0] Decode batch. #running-req: 1, #token: 547, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.45, #queue-req: 0, 
[2025-07-28 00:55:15 DP1 TP0] Decode batch. #running-req: 1, #token: 587, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:55:16] INFO:     127.0.0.1:52054 - "POST /v1/chat/completions HTTP/1.1" 200 OK

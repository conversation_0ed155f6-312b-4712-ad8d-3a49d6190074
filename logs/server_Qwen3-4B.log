[2025-07-28 01:01:29] server_args=ServerArgs(model_path='/data/home/<USER>/repos/anesbench/share/models/Qwen3-4B', tokenizer_path='/data/home/<USER>/repos/anesbench/share/models/Qwen3-4B', tokenizer_mode='auto', skip_tokenizer_init=False, load_format='auto', model_loader_extra_config='{}', trust_remote_code=False, context_length=32768, is_embedding=False, enable_multimodal=None, revision=None, model_impl='auto', host='127.0.0.1', port=8003, skip_server_warmup=False, warmups=None, nccl_port=None, dtype='auto', quantization=None, quantization_param_path=None, kv_cache_dtype='auto', mem_fraction_static=0.85, max_running_requests=None, max_total_tokens=None, chunked_prefill_size=8192, max_prefill_tokens=16384, schedule_policy='fcfs', schedule_conservativeness=1.0, cpu_offload_gb=0, page_size=1, hybrid_kvcache_ratio=None, swa_full_tokens_ratio=0.8, disable_hybrid_swa_memory=False, device='cuda', tp_size=4, pp_size=1, max_micro_batch_size=None, stream_interval=1, stream_output=False, random_seed=813755852, constrained_json_whitespace_pattern=None, watchdog_timeout=300, dist_timeout=None, download_dir=None, base_gpu_id=0, gpu_id_step=1, sleep_on_idle=False, log_level='info', log_level_http=None, log_requests=False, log_requests_level=0, crash_dump_folder=None, show_time_cost=True, enable_metrics=True, enable_metrics_for_all_schedulers=False, bucket_time_to_first_token=None, bucket_inter_token_latency=None, bucket_e2e_request_latency=None, collect_tokens_histogram=False, decode_log_interval=40, enable_request_time_stats_logging=False, kv_events_config=None, api_key=None, served_model_name='/data/home/<USER>/repos/anesbench/share/models/Qwen3-4B', chat_template=None, completion_template=None, file_storage_path='sglang_storage', enable_cache_report=False, reasoning_parser=None, tool_call_parser=None, dp_size=2, load_balance_method='round_robin', dist_init_addr=None, nnodes=1, node_rank=0, json_model_override_args='{}', preferred_sampling_params=None, enable_lora=None, max_lora_rank=None, lora_target_modules=None, lora_paths=None, max_loras_per_batch=8, lora_backend='triton', attention_backend=None, sampling_backend='flashinfer', grammar_backend='xgrammar', mm_attention_backend=None, speculative_algorithm=None, speculative_draft_model_path=None, speculative_num_steps=None, speculative_eagle_topk=None, speculative_num_draft_tokens=None, speculative_accept_threshold_single=1.0, speculative_accept_threshold_acc=1.0, speculative_token_map=None, ep_size=1, enable_ep_moe=False, enable_deepep_moe=False, enable_flashinfer_moe=False, enable_flashinfer_allreduce_fusion=False, deepep_mode='auto', ep_num_redundant_experts=0, ep_dispatch_algorithm='static', init_expert_location='trivial', enable_eplb=False, eplb_algorithm='auto', eplb_rebalance_num_iterations=1000, eplb_rebalance_layers_per_chunk=None, expert_distribution_recorder_mode=None, expert_distribution_recorder_buffer_size=1000, enable_expert_distribution_metrics=False, deepep_config=None, moe_dense_tp_size=None, enable_hierarchical_cache=False, hicache_ratio=2.0, hicache_size=0, hicache_write_policy='write_through_selective', hicache_io_backend='', hicache_storage_backend=None, enable_double_sparsity=False, ds_channel_config_path=None, ds_heavy_channel_num=32, ds_heavy_token_num=256, ds_heavy_channel_type='qk', ds_sparse_decode_threshold=4096, disable_radix_cache=False, cuda_graph_max_bs=None, cuda_graph_bs=None, disable_cuda_graph=False, disable_cuda_graph_padding=False, enable_profile_cuda_graph=False, enable_nccl_nvls=False, enable_tokenizer_batch_encode=False, disable_outlines_disk_cache=False, disable_custom_all_reduce=False, enable_mscclpp=False, disable_overlap_schedule=False, enable_mixed_chunk=False, enable_dp_attention=False, enable_dp_lm_head=False, enable_two_batch_overlap=False, enable_torch_compile=False, torch_compile_max_bs=32, torchao_config='', enable_nan_detection=False, enable_p2p_check=False, triton_attention_reduce_in_fp32=False, triton_attention_num_kv_splits=8, num_continuous_decode_steps=1, delete_ckpt_after_loading=False, enable_memory_saver=False, allow_auto_truncate=False, enable_custom_logit_processor=False, flashinfer_mla_disable_ragged=False, disable_shared_experts_fusion=False, disable_chunked_prefix_cache=False, disable_fast_image_processor=False, enable_return_hidden_states=False, enable_triton_kernel_moe=False, debug_tensor_dump_output_folder=None, debug_tensor_dump_input_file=None, debug_tensor_dump_inject=False, debug_tensor_dump_prefill_only=False, disaggregation_mode='null', disaggregation_transfer_backend='mooncake', disaggregation_bootstrap_port=8998, disaggregation_decode_tp=None, disaggregation_decode_dp=None, disaggregation_prefill_pp=1, disaggregation_ib_device=None, num_reserved_decode_tokens=512, pdlb_url=None, custom_weight_loader=[], weight_loader_disable_mmap=False, enable_pdmux=False, sm_group_num=3)
[2025-07-28 01:01:35] Launch DP0 starting at GPU #0.
[2025-07-28 01:01:35] Launch DP1 starting at GPU #4.
[2025-07-28 01:01:42 DP0 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 01:01:42 DP0 TP0] Init torch distributed begin.
[2025-07-28 01:01:42 DP1 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 01:01:42 DP1 TP0] Init torch distributed begin.
[2025-07-28 01:01:44 DP0 TP0] sglang is using nccl==2.26.2
[2025-07-28 01:01:44 DP1 TP0] sglang is using nccl==2.26.2
[2025-07-28 01:01:45 DP0 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 01:01:45 DP1 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 01:01:45 DP0 TP0] Load weight begin. avail mem=78.21 GB

Loading safetensors checkpoint shards:   0% Completed | 0/3 [00:00<?, ?it/s]
[2025-07-28 01:01:45 DP1 TP0] Load weight begin. avail mem=78.21 GB

Loading safetensors checkpoint shards:   0% Completed | 0/3 [00:00<?, ?it/s]

Loading safetensors checkpoint shards:  67% Completed | 2/3 [00:01<00:00,  1.34it/s]

Loading safetensors checkpoint shards:  67% Completed | 2/3 [00:01<00:00,  1.55it/s]

Loading safetensors checkpoint shards: 100% Completed | 3/3 [00:02<00:00,  1.03s/it]

Loading safetensors checkpoint shards: 100% Completed | 3/3 [00:02<00:00,  1.03it/s]

Loading safetensors checkpoint shards: 100% Completed | 3/3 [00:02<00:00,  1.03it/s]


Loading safetensors checkpoint shards: 100% Completed | 3/3 [00:02<00:00,  1.10it/s]

[2025-07-28 01:01:48 DP0 TP0] Load weight end. type=Qwen3ForCausalLM, dtype=torch.bfloat16, avail mem=76.25 GB, mem usage=1.96 GB.
[2025-07-28 01:01:48 DP1 TP0] Load weight end. type=Qwen3ForCausalLM, dtype=torch.bfloat16, avail mem=76.25 GB, mem usage=1.96 GB.
[2025-07-28 01:01:48 DP0 TP2] KV Cache is allocated. #tokens: 1876839, K size: 32.22 GB, V size: 32.22 GB
[2025-07-28 01:01:48 DP0 TP3] KV Cache is allocated. #tokens: 1876839, K size: 32.22 GB, V size: 32.22 GB
[2025-07-28 01:01:48 DP1 TP2] KV Cache is allocated. #tokens: 1876839, K size: 32.22 GB, V size: 32.22 GB
[2025-07-28 01:01:48 DP1 TP0] KV Cache is allocated. #tokens: 1876839, K size: 32.22 GB, V size: 32.22 GB
[2025-07-28 01:01:48 DP1 TP0] Memory pool end. avail mem=11.18 GB
[2025-07-28 01:01:48 DP0 TP1] KV Cache is allocated. #tokens: 1876839, K size: 32.22 GB, V size: 32.22 GB
[2025-07-28 01:01:48 DP0 TP0] KV Cache is allocated. #tokens: 1876839, K size: 32.22 GB, V size: 32.22 GB
[2025-07-28 01:01:48 DP1 TP1] KV Cache is allocated. #tokens: 1876839, K size: 32.22 GB, V size: 32.22 GB
[2025-07-28 01:01:48 DP1 TP3] KV Cache is allocated. #tokens: 1876839, K size: 32.22 GB, V size: 32.22 GB
[2025-07-28 01:01:48 DP0 TP0] Memory pool end. avail mem=11.18 GB
[2025-07-28 01:01:49 DP1 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.55 GB
[2025-07-28 01:01:49 DP0 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.55 GB
[2025-07-28 01:01:49 DP0 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.53 GB):   0%|          | 0/23 [00:00<?, ?it/s][2025-07-28 01:01:49 DP1 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.53 GB):   0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.53 GB):   4%|▍         | 1/23 [00:01<00:22,  1.04s/it]
Capturing batches (bs=152 avail_mem=10.28 GB):   4%|▍         | 1/23 [00:01<00:22,  1.04s/it]
Capturing batches (bs=160 avail_mem=10.53 GB):   4%|▍         | 1/23 [00:01<00:25,  1.16s/it]
Capturing batches (bs=152 avail_mem=10.28 GB):   4%|▍         | 1/23 [00:01<00:25,  1.16s/it]
Capturing batches (bs=152 avail_mem=10.28 GB):   9%|▊         | 2/23 [00:01<00:13,  1.57it/s]
Capturing batches (bs=144 avail_mem=10.17 GB):   9%|▊         | 2/23 [00:01<00:13,  1.57it/s]
Capturing batches (bs=152 avail_mem=10.28 GB):   9%|▊         | 2/23 [00:01<00:14,  1.43it/s]
Capturing batches (bs=144 avail_mem=10.17 GB):   9%|▊         | 2/23 [00:01<00:14,  1.43it/s]
Capturing batches (bs=144 avail_mem=10.17 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.72it/s]
Capturing batches (bs=136 avail_mem=10.08 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.72it/s]
Capturing batches (bs=144 avail_mem=10.17 GB):  13%|█▎        | 3/23 [00:02<00:12,  1.65it/s]
Capturing batches (bs=136 avail_mem=10.08 GB):  13%|█▎        | 3/23 [00:02<00:12,  1.65it/s]
Capturing batches (bs=136 avail_mem=10.08 GB):  17%|█▋        | 4/23 [00:02<00:09,  2.10it/s]
Capturing batches (bs=128 avail_mem=9.97 GB):  17%|█▋        | 4/23 [00:02<00:09,  2.10it/s] 
Capturing batches (bs=136 avail_mem=10.08 GB):  17%|█▋        | 4/23 [00:02<00:09,  2.06it/s]
Capturing batches (bs=128 avail_mem=9.97 GB):  17%|█▋        | 4/23 [00:02<00:09,  2.06it/s] 
Capturing batches (bs=128 avail_mem=9.97 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.39it/s]
Capturing batches (bs=120 avail_mem=9.89 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.39it/s]
Capturing batches (bs=128 avail_mem=9.97 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.39it/s]
Capturing batches (bs=120 avail_mem=9.89 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.39it/s]
Capturing batches (bs=120 avail_mem=9.89 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.61it/s]
Capturing batches (bs=112 avail_mem=9.79 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.61it/s]
Capturing batches (bs=120 avail_mem=9.89 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.64it/s]
Capturing batches (bs=112 avail_mem=9.79 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.64it/s]
Capturing batches (bs=112 avail_mem=9.79 GB):  30%|███       | 7/23 [00:03<00:05,  2.75it/s]
Capturing batches (bs=104 avail_mem=9.72 GB):  30%|███       | 7/23 [00:03<00:05,  2.75it/s]
Capturing batches (bs=112 avail_mem=9.79 GB):  30%|███       | 7/23 [00:03<00:05,  2.83it/s]
Capturing batches (bs=104 avail_mem=9.72 GB):  30%|███       | 7/23 [00:03<00:05,  2.83it/s]
Capturing batches (bs=104 avail_mem=9.72 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.88it/s]
Capturing batches (bs=96 avail_mem=9.63 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.88it/s] 
Capturing batches (bs=104 avail_mem=9.72 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.96it/s]
Capturing batches (bs=96 avail_mem=9.63 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.96it/s] 
Capturing batches (bs=96 avail_mem=9.63 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.03it/s]
Capturing batches (bs=88 avail_mem=9.57 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.03it/s]
Capturing batches (bs=96 avail_mem=9.63 GB):  39%|███▉      | 9/23 [00:03<00:04,  2.94it/s]
Capturing batches (bs=88 avail_mem=9.57 GB):  39%|███▉      | 9/23 [00:03<00:04,  2.94it/s]
Capturing batches (bs=88 avail_mem=9.57 GB):  43%|████▎     | 10/23 [00:04<00:04,  2.86it/s]
Capturing batches (bs=80 avail_mem=9.49 GB):  43%|████▎     | 10/23 [00:04<00:04,  2.86it/s]
Capturing batches (bs=88 avail_mem=9.57 GB):  43%|████▎     | 10/23 [00:04<00:04,  2.91it/s]
Capturing batches (bs=80 avail_mem=9.49 GB):  43%|████▎     | 10/23 [00:04<00:04,  2.91it/s]
Capturing batches (bs=80 avail_mem=9.49 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.07it/s]
Capturing batches (bs=72 avail_mem=9.49 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.07it/s]
Capturing batches (bs=80 avail_mem=9.49 GB):  48%|████▊     | 11/23 [00:04<00:04,  2.99it/s]
Capturing batches (bs=72 avail_mem=9.49 GB):  48%|████▊     | 11/23 [00:04<00:04,  2.99it/s]
Capturing batches (bs=72 avail_mem=9.49 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.13it/s]
Capturing batches (bs=64 avail_mem=9.42 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.13it/s]
Capturing batches (bs=72 avail_mem=9.49 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.06it/s]
Capturing batches (bs=64 avail_mem=9.42 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.06it/s]
Capturing batches (bs=64 avail_mem=9.42 GB):  57%|█████▋    | 13/23 [00:05<00:03,  3.16it/s]
Capturing batches (bs=56 avail_mem=9.37 GB):  57%|█████▋    | 13/23 [00:05<00:03,  3.16it/s]
Capturing batches (bs=64 avail_mem=9.42 GB):  57%|█████▋    | 13/23 [00:05<00:03,  3.08it/s]
Capturing batches (bs=56 avail_mem=9.37 GB):  57%|█████▋    | 13/23 [00:05<00:03,  3.08it/s]
Capturing batches (bs=56 avail_mem=9.37 GB):  61%|██████    | 14/23 [00:05<00:02,  3.17it/s]
Capturing batches (bs=48 avail_mem=9.31 GB):  61%|██████    | 14/23 [00:05<00:02,  3.17it/s]
Capturing batches (bs=56 avail_mem=9.37 GB):  61%|██████    | 14/23 [00:05<00:02,  3.11it/s]
Capturing batches (bs=48 avail_mem=9.31 GB):  61%|██████    | 14/23 [00:05<00:02,  3.11it/s]
Capturing batches (bs=48 avail_mem=9.31 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.21it/s]
Capturing batches (bs=40 avail_mem=9.31 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.21it/s]
Capturing batches (bs=48 avail_mem=9.31 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.14it/s]
Capturing batches (bs=40 avail_mem=9.31 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.14it/s]
Capturing batches (bs=40 avail_mem=9.31 GB):  70%|██████▉   | 16/23 [00:06<00:02,  3.23it/s]
Capturing batches (bs=32 avail_mem=9.25 GB):  70%|██████▉   | 16/23 [00:06<00:02,  3.23it/s]
Capturing batches (bs=40 avail_mem=9.31 GB):  70%|██████▉   | 16/23 [00:06<00:02,  3.16it/s]
Capturing batches (bs=32 avail_mem=9.25 GB):  70%|██████▉   | 16/23 [00:06<00:02,  3.16it/s]
Capturing batches (bs=32 avail_mem=9.25 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.20it/s]
Capturing batches (bs=24 avail_mem=9.23 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.20it/s]
Capturing batches (bs=32 avail_mem=9.25 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.13it/s]
Capturing batches (bs=24 avail_mem=9.23 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.13it/s]
Capturing batches (bs=24 avail_mem=9.23 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.22it/s]
Capturing batches (bs=16 avail_mem=9.20 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.22it/s]
Capturing batches (bs=24 avail_mem=9.23 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.16it/s]
Capturing batches (bs=16 avail_mem=9.20 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.16it/s]
Capturing batches (bs=16 avail_mem=9.20 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.22it/s]
Capturing batches (bs=8 avail_mem=9.19 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.22it/s] 
Capturing batches (bs=16 avail_mem=9.20 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.18it/s]
Capturing batches (bs=8 avail_mem=9.19 GB):  83%|████████▎ | 19/23 [00:07<00:01,  3.18it/s] 
Capturing batches (bs=8 avail_mem=9.19 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.25it/s]
Capturing batches (bs=4 avail_mem=9.17 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.25it/s]
Capturing batches (bs=8 avail_mem=9.19 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.20it/s]
Capturing batches (bs=4 avail_mem=9.17 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.20it/s]
Capturing batches (bs=4 avail_mem=9.17 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.26it/s]
Capturing batches (bs=2 avail_mem=9.16 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.26it/s]
Capturing batches (bs=4 avail_mem=9.17 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.21it/s]
Capturing batches (bs=2 avail_mem=9.16 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.21it/s]
Capturing batches (bs=2 avail_mem=9.16 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.27it/s]
Capturing batches (bs=1 avail_mem=9.13 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.27it/s]
Capturing batches (bs=2 avail_mem=9.16 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.22it/s]
Capturing batches (bs=1 avail_mem=9.13 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.22it/s]
Capturing batches (bs=1 avail_mem=9.13 GB): 100%|██████████| 23/23 [00:08<00:00,  3.25it/s]
Capturing batches (bs=1 avail_mem=9.13 GB): 100%|██████████| 23/23 [00:08<00:00,  2.80it/s]
[2025-07-28 01:01:57 DP0 TP2] Registering 1679 cuda graph addresses
[2025-07-28 01:01:57 DP0 TP0] Registering 1679 cuda graph addresses
[2025-07-28 01:01:57 DP0 TP3] Registering 1679 cuda graph addresses
[2025-07-28 01:01:57 DP0 TP1] Registering 1679 cuda graph addresses
[2025-07-28 01:01:57 DP0 TP0] Capture cuda graph end. Time elapsed: 8.41 s. mem usage=1.42 GB. avail mem=9.13 GB.
[2025-07-28 01:01:57 DP1 TP1] Registering 1679 cuda graph addresses
[2025-07-28 01:01:57 DP1 TP2] Registering 1679 cuda graph addresses

Capturing batches (bs=1 avail_mem=9.13 GB): 100%|██████████| 23/23 [00:08<00:00,  3.20it/s]
Capturing batches (bs=1 avail_mem=9.13 GB): 100%|██████████| 23/23 [00:08<00:00,  2.79it/s]
[2025-07-28 01:01:57 DP1 TP0] Registering 1679 cuda graph addresses
[2025-07-28 01:01:57 DP1 TP3] Registering 1679 cuda graph addresses
[2025-07-28 01:01:57 DP1 TP0] Capture cuda graph end. Time elapsed: 8.49 s. mem usage=1.42 GB. avail mem=9.13 GB.
[2025-07-28 01:01:57 DP0 TP0] max_total_num_tokens=1876839, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=9.13 GB
[2025-07-28 01:01:57 DP1 TP0] max_total_num_tokens=1876839, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=9.13 GB
[2025-07-28 01:01:58] INFO:     Started server process [1809140]
[2025-07-28 01:01:58] INFO:     Waiting for application startup.
[2025-07-28 01:01:58] INFO:     Application startup complete.
[2025-07-28 01:01:58] INFO:     Uvicorn running on http://127.0.0.1:8003 (Press CTRL+C to quit)
[2025-07-28 01:01:59] INFO:     127.0.0.1:57318 - "GET /health HTTP/1.1" 200 OK
[2025-07-28 01:01:59 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 235, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:01:59 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 194, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:01:59] INFO:     127.0.0.1:57332 - "GET /get_model_info HTTP/1.1" 200 OK
[2025-07-28 01:02:00 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 0, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:02:00 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 0, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:02:00] INFO:     127.0.0.1:57346 - "POST /generate HTTP/1.1" 200 OK
[2025-07-28 01:02:00] The server is fired up and ready to roll!
[2025-07-28 01:02:00 DP0 TP0] Decode batch. #running-req: 1, #token: 276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 17.42, #queue-req: 0, 
[2025-07-28 01:02:00 DP1 TP0] Decode batch. #running-req: 1, #token: 235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 17.85, #queue-req: 0, 
[2025-07-28 01:02:00 DP0 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.16, #queue-req: 0, 
[2025-07-28 01:02:00 DP1 TP0] Decode batch. #running-req: 1, #token: 275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 232.48, #queue-req: 0, 
[2025-07-28 01:02:01 DP0 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 235.09, #queue-req: 0, 
[2025-07-28 01:02:01 DP1 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.76, #queue-req: 0, 
[2025-07-28 01:02:01 DP0 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.40, #queue-req: 0, 
[2025-07-28 01:02:01 DP1 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.22, #queue-req: 0, 
[2025-07-28 01:02:01 DP0 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.14, #queue-req: 0, 
[2025-07-28 01:02:01 DP1 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.09, #queue-req: 0, 
[2025-07-28 01:02:01 DP0 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.08, #queue-req: 0, 
[2025-07-28 01:02:01 DP1 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.44, #queue-req: 0, 
[2025-07-28 01:02:01 DP1 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.84, #queue-req: 0, 
[2025-07-28 01:02:01 DP0 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.61, #queue-req: 0, 
[2025-07-28 01:02:01 DP1 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.34, #queue-req: 0, 
[2025-07-28 01:02:01 DP0 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.04, #queue-req: 0, 
[2025-07-28 01:02:02 DP1 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.92, #queue-req: 0, 
[2025-07-28 01:02:02 DP0 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.37, #queue-req: 0, 
[2025-07-28 01:02:02 DP1 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.12, #queue-req: 0, 
[2025-07-28 01:02:02 DP0 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.92, #queue-req: 0, 
[2025-07-28 01:02:02 DP1 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.11, #queue-req: 0, 
[2025-07-28 01:02:02 DP0 TP0] Decode batch. #running-req: 1, #token: 676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.27, #queue-req: 0, 
[2025-07-28 01:02:02 DP1 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.46, #queue-req: 0, 
[2025-07-28 01:02:02 DP0 TP0] Decode batch. #running-req: 1, #token: 716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.89, #queue-req: 0, 
[2025-07-28 01:02:02 DP1 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.49, #queue-req: 0, 
[2025-07-28 01:02:02 DP0 TP0] Decode batch. #running-req: 1, #token: 756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.69, #queue-req: 0, 
[2025-07-28 01:02:02 DP1 TP0] Decode batch. #running-req: 1, #token: 755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.58, #queue-req: 0, 
[2025-07-28 01:02:02 DP0 TP0] Decode batch. #running-req: 1, #token: 796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.44, #queue-req: 0, 
[2025-07-28 01:02:02] INFO:     127.0.0.1:57330 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:02:02 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:02:03 DP0 TP0] Decode batch. #running-req: 2, #token: 934, token usage: 0.00, cuda graph: True, gen throughput (token/s): 127.94, #queue-req: 0, 
[2025-07-28 01:02:03 DP0 TP0] Decode batch. #running-req: 2, #token: 1014, token usage: 0.00, cuda graph: True, gen throughput (token/s): 456.54, #queue-req: 0, 
[2025-07-28 01:02:03 DP0 TP0] Decode batch. #running-req: 2, #token: 1094, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.78, #queue-req: 0, 
[2025-07-28 01:02:03 DP0 TP0] Decode batch. #running-req: 2, #token: 1174, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.71, #queue-req: 0, 
[2025-07-28 01:02:03 DP0 TP0] Decode batch. #running-req: 2, #token: 1254, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.23, #queue-req: 0, 
[2025-07-28 01:02:04 DP0 TP0] Decode batch. #running-req: 2, #token: 1334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 458.67, #queue-req: 0, 
[2025-07-28 01:02:04] INFO:     127.0.0.1:57322 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:02:04 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 207, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:02:04 DP0 TP0] Decode batch. #running-req: 1, #token: 423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 353.68, #queue-req: 0, 
[2025-07-28 01:02:04 DP0 TP0] Decode batch. #running-req: 1, #token: 463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.91, #queue-req: 0, 
[2025-07-28 01:02:04 DP1 TP0] Decode batch. #running-req: 1, #token: 305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 23.77, #queue-req: 0, 
[2025-07-28 01:02:04 DP0 TP0] Decode batch. #running-req: 1, #token: 503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.15, #queue-req: 0, 
[2025-07-28 01:02:04 DP1 TP0] Decode batch. #running-req: 1, #token: 345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.99, #queue-req: 0, 
[2025-07-28 01:02:04 DP0 TP0] Decode batch. #running-req: 1, #token: 543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.73, #queue-req: 0, 
[2025-07-28 01:02:04 DP1 TP0] Decode batch. #running-req: 1, #token: 385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.24, #queue-req: 0, 
[2025-07-28 01:02:04 DP0 TP0] Decode batch. #running-req: 1, #token: 583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.72, #queue-req: 0, 
[2025-07-28 01:02:04 DP1 TP0] Decode batch. #running-req: 1, #token: 425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.40, #queue-req: 0, 
[2025-07-28 01:02:05 DP0 TP0] Decode batch. #running-req: 1, #token: 623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.76, #queue-req: 0, 
[2025-07-28 01:02:05 DP1 TP0] Decode batch. #running-req: 1, #token: 465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.31, #queue-req: 0, 
[2025-07-28 01:02:05 DP0 TP0] Decode batch. #running-req: 1, #token: 663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.14, #queue-req: 0, 
[2025-07-28 01:02:05 DP1 TP0] Decode batch. #running-req: 1, #token: 505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.10, #queue-req: 0, 
[2025-07-28 01:02:05 DP0 TP0] Decode batch. #running-req: 1, #token: 703, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.02, #queue-req: 0, 
[2025-07-28 01:02:05 DP1 TP0] Decode batch. #running-req: 1, #token: 545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.69, #queue-req: 0, 
[2025-07-28 01:02:05 DP0 TP0] Decode batch. #running-req: 1, #token: 743, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.69, #queue-req: 0, 
[2025-07-28 01:02:05 DP1 TP0] Decode batch. #running-req: 1, #token: 585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.43, #queue-req: 0, 
[2025-07-28 01:02:05 DP0 TP0] Decode batch. #running-req: 1, #token: 783, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.61, #queue-req: 0, 
[2025-07-28 01:02:05 DP1 TP0] Decode batch. #running-req: 1, #token: 625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.33, #queue-req: 0, 
[2025-07-28 01:02:05 DP0 TP0] Decode batch. #running-req: 1, #token: 823, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.67, #queue-req: 0, 
[2025-07-28 01:02:05 DP1 TP0] Decode batch. #running-req: 1, #token: 665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.65, #queue-req: 0, 
[2025-07-28 01:02:06 DP0 TP0] Decode batch. #running-req: 1, #token: 863, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.68, #queue-req: 0, 
[2025-07-28 01:02:06 DP1 TP0] Decode batch. #running-req: 1, #token: 705, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.74, #queue-req: 0, 
[2025-07-28 01:02:06 DP0 TP0] Decode batch. #running-req: 1, #token: 903, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.93, #queue-req: 0, 
[2025-07-28 01:02:06 DP1 TP0] Decode batch. #running-req: 1, #token: 745, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.75, #queue-req: 0, 
[2025-07-28 01:02:06 DP0 TP0] Decode batch. #running-req: 1, #token: 943, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.10, #queue-req: 0, 
[2025-07-28 01:02:06 DP1 TP0] Decode batch. #running-req: 1, #token: 785, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.55, #queue-req: 0, 
[2025-07-28 01:02:06 DP0 TP0] Decode batch. #running-req: 1, #token: 983, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.17, #queue-req: 0, 
[2025-07-28 01:02:06 DP1 TP0] Decode batch. #running-req: 1, #token: 825, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.15, #queue-req: 0, 
[2025-07-28 01:02:06 DP0 TP0] Decode batch. #running-req: 1, #token: 1023, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.02, #queue-req: 0, 
[2025-07-28 01:02:06 DP1 TP0] Decode batch. #running-req: 1, #token: 865, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.17, #queue-req: 0, 
[2025-07-28 01:02:06 DP0 TP0] Decode batch. #running-req: 1, #token: 1063, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.57, #queue-req: 0, 
[2025-07-28 01:02:06 DP1 TP0] Decode batch. #running-req: 1, #token: 905, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.21, #queue-req: 0, 
[2025-07-28 01:02:07 DP0 TP0] Decode batch. #running-req: 1, #token: 1103, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.71, #queue-req: 0, 
[2025-07-28 01:02:07 DP1 TP0] Decode batch. #running-req: 1, #token: 945, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.01, #queue-req: 0, 
[2025-07-28 01:02:07 DP0 TP0] Decode batch. #running-req: 1, #token: 1143, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.75, #queue-req: 0, 
[2025-07-28 01:02:07 DP1 TP0] Decode batch. #running-req: 1, #token: 985, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.48, #queue-req: 0, 
[2025-07-28 01:02:07 DP0 TP0] Decode batch. #running-req: 1, #token: 1183, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.34, #queue-req: 0, 
[2025-07-28 01:02:07 DP1 TP0] Decode batch. #running-req: 1, #token: 1025, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.35, #queue-req: 0, 
[2025-07-28 01:02:07 DP0 TP0] Decode batch. #running-req: 1, #token: 1223, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.50, #queue-req: 0, 
[2025-07-28 01:02:07 DP1 TP0] Decode batch. #running-req: 1, #token: 1065, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.08, #queue-req: 0, 
[2025-07-28 01:02:07 DP0 TP0] Decode batch. #running-req: 1, #token: 1263, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.13, #queue-req: 0, 
[2025-07-28 01:02:07 DP1 TP0] Decode batch. #running-req: 1, #token: 1105, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.27, #queue-req: 0, 
[2025-07-28 01:02:07 DP0 TP0] Decode batch. #running-req: 1, #token: 1303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.60, #queue-req: 0, 
[2025-07-28 01:02:07 DP1 TP0] Decode batch. #running-req: 1, #token: 1145, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.22, #queue-req: 0, 
[2025-07-28 01:02:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.54, #queue-req: 0, 
[2025-07-28 01:02:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1185, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.60, #queue-req: 0, 
[2025-07-28 01:02:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.61, #queue-req: 0, 
[2025-07-28 01:02:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1225, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.83, #queue-req: 0, 
[2025-07-28 01:02:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.54, #queue-req: 0, 
[2025-07-28 01:02:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.46, #queue-req: 0, 
[2025-07-28 01:02:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.55, #queue-req: 0, 
[2025-07-28 01:02:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.27, #queue-req: 0, 
[2025-07-28 01:02:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.36, #queue-req: 0, 
[2025-07-28 01:02:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.36, #queue-req: 0, 
[2025-07-28 01:02:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.75, #queue-req: 0, 
[2025-07-28 01:02:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.36, #queue-req: 0, 
[2025-07-28 01:02:09] INFO:     127.0.0.1:38610 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:02:09 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:02:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.05, #queue-req: 0, 
[2025-07-28 01:02:09 DP0 TP0] Decode batch. #running-req: 1, #token: 233, token usage: 0.00, cuda graph: True, gen throughput (token/s): 165.60, #queue-req: 0, 
[2025-07-28 01:02:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.20, #queue-req: 0, 
[2025-07-28 01:02:09 DP0 TP0] Decode batch. #running-req: 1, #token: 273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.06, #queue-req: 0, 
[2025-07-28 01:02:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 233.21, #queue-req: 0, 
[2025-07-28 01:02:09 DP0 TP0] Decode batch. #running-req: 1, #token: 313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.26, #queue-req: 0, 
[2025-07-28 01:02:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.35, #queue-req: 0, 
[2025-07-28 01:02:09 DP0 TP0] Decode batch. #running-req: 1, #token: 353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.85, #queue-req: 0, 
[2025-07-28 01:02:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.02, #queue-req: 0, 
[2025-07-28 01:02:09 DP0 TP0] Decode batch. #running-req: 1, #token: 393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.79, #queue-req: 0, 
[2025-07-28 01:02:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.11, #queue-req: 0, 
[2025-07-28 01:02:09 DP0 TP0] Decode batch. #running-req: 1, #token: 433, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.32, #queue-req: 0, 
[2025-07-28 01:02:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.03, #queue-req: 0, 
[2025-07-28 01:02:10 DP0 TP0] Decode batch. #running-req: 1, #token: 473, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.16, #queue-req: 0, 
[2025-07-28 01:02:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1705, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.17, #queue-req: 0, 
[2025-07-28 01:02:10 DP0 TP0] Decode batch. #running-req: 1, #token: 513, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.27, #queue-req: 0, 
[2025-07-28 01:02:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1745, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.39, #queue-req: 0, 
[2025-07-28 01:02:10 DP0 TP0] Decode batch. #running-req: 1, #token: 553, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.40, #queue-req: 0, 
[2025-07-28 01:02:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1785, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.53, #queue-req: 0, 
[2025-07-28 01:02:10 DP0 TP0] Decode batch. #running-req: 1, #token: 593, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.29, #queue-req: 0, 
[2025-07-28 01:02:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1825, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.04, #queue-req: 0, 
[2025-07-28 01:02:10 DP0 TP0] Decode batch. #running-req: 1, #token: 633, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.46, #queue-req: 0, 
[2025-07-28 01:02:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1865, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.77, #queue-req: 0, 
[2025-07-28 01:02:10 DP0 TP0] Decode batch. #running-req: 1, #token: 673, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.36, #queue-req: 0, 
[2025-07-28 01:02:11] INFO:     127.0.0.1:38612 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:02:11 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 133, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:02:11 DP0 TP0] Decode batch. #running-req: 1, #token: 713, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.56, #queue-req: 0, 
[2025-07-28 01:02:11 DP1 TP0] Decode batch. #running-req: 1, #token: 224, token usage: 0.00, cuda graph: True, gen throughput (token/s): 169.43, #queue-req: 0, 
[2025-07-28 01:02:11 DP0 TP0] Decode batch. #running-req: 1, #token: 753, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.99, #queue-req: 0, 
[2025-07-28 01:02:11 DP1 TP0] Decode batch. #running-req: 1, #token: 264, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.35, #queue-req: 0, 
[2025-07-28 01:02:11 DP0 TP0] Decode batch. #running-req: 1, #token: 793, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.96, #queue-req: 0, 
[2025-07-28 01:02:11 DP1 TP0] Decode batch. #running-req: 1, #token: 304, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.23, #queue-req: 0, 
[2025-07-28 01:02:11 DP0 TP0] Decode batch. #running-req: 1, #token: 833, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.10, #queue-req: 0, 
[2025-07-28 01:02:11 DP1 TP0] Decode batch. #running-req: 1, #token: 344, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.68, #queue-req: 0, 
[2025-07-28 01:02:11 DP0 TP0] Decode batch. #running-req: 1, #token: 873, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.29, #queue-req: 0, 
[2025-07-28 01:02:11 DP1 TP0] Decode batch. #running-req: 1, #token: 384, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.80, #queue-req: 0, 
[2025-07-28 01:02:11 DP0 TP0] Decode batch. #running-req: 1, #token: 913, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.31, #queue-req: 0, 
[2025-07-28 01:02:12 DP1 TP0] Decode batch. #running-req: 1, #token: 424, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.01, #queue-req: 0, 
[2025-07-28 01:02:12 DP0 TP0] Decode batch. #running-req: 1, #token: 953, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.41, #queue-req: 0, 
[2025-07-28 01:02:12 DP1 TP0] Decode batch. #running-req: 1, #token: 464, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.05, #queue-req: 0, 
[2025-07-28 01:02:12 DP0 TP0] Decode batch. #running-req: 1, #token: 993, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.61, #queue-req: 0, 
[2025-07-28 01:02:12 DP1 TP0] Decode batch. #running-req: 1, #token: 504, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.91, #queue-req: 0, 
[2025-07-28 01:02:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1033, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.13, #queue-req: 0, 
[2025-07-28 01:02:12 DP1 TP0] Decode batch. #running-req: 1, #token: 544, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.33, #queue-req: 0, 
[2025-07-28 01:02:12] INFO:     127.0.0.1:38618 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:02:12 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 186, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:02:12 DP1 TP0] Decode batch. #running-req: 1, #token: 584, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.93, #queue-req: 0, 
[2025-07-28 01:02:12 DP0 TP0] Decode batch. #running-req: 1, #token: 276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 171.34, #queue-req: 0, 
[2025-07-28 01:02:12 DP1 TP0] Decode batch. #running-req: 1, #token: 624, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.55, #queue-req: 0, 
[2025-07-28 01:02:12 DP0 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.08, #queue-req: 0, 
[2025-07-28 01:02:12 DP1 TP0] Decode batch. #running-req: 1, #token: 664, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.49, #queue-req: 0, 
[2025-07-28 01:02:13 DP0 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.14, #queue-req: 0, 
[2025-07-28 01:02:13 DP1 TP0] Decode batch. #running-req: 1, #token: 704, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.47, #queue-req: 0, 
[2025-07-28 01:02:13 DP0 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.59, #queue-req: 0, 
[2025-07-28 01:02:13 DP1 TP0] Decode batch. #running-req: 1, #token: 744, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.79, #queue-req: 0, 
[2025-07-28 01:02:13 DP0 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.90, #queue-req: 0, 
[2025-07-28 01:02:13 DP1 TP0] Decode batch. #running-req: 1, #token: 784, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.82, #queue-req: 0, 
[2025-07-28 01:02:13 DP0 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.69, #queue-req: 0, 
[2025-07-28 01:02:13 DP1 TP0] Decode batch. #running-req: 1, #token: 824, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.06, #queue-req: 0, 
[2025-07-28 01:02:13 DP0 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.11, #queue-req: 0, 
[2025-07-28 01:02:13 DP1 TP0] Decode batch. #running-req: 1, #token: 864, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.79, #queue-req: 0, 
[2025-07-28 01:02:13 DP0 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 235.19, #queue-req: 0, 
[2025-07-28 01:02:13 DP1 TP0] Decode batch. #running-req: 1, #token: 904, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.13, #queue-req: 0, 
[2025-07-28 01:02:14 DP0 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.25, #queue-req: 0, 
[2025-07-28 01:02:14 DP1 TP0] Decode batch. #running-req: 1, #token: 944, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.77, #queue-req: 0, 
[2025-07-28 01:02:14 DP0 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.46, #queue-req: 0, 
[2025-07-28 01:02:14 DP1 TP0] Decode batch. #running-req: 1, #token: 984, token usage: 0.00, cuda graph: True, gen throughput (token/s): 234.96, #queue-req: 0, 
[2025-07-28 01:02:14 DP0 TP0] Decode batch. #running-req: 1, #token: 676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.08, #queue-req: 0, 
[2025-07-28 01:02:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1024, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.08, #queue-req: 0, 
[2025-07-28 01:02:14 DP0 TP0] Decode batch. #running-req: 1, #token: 716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 235.62, #queue-req: 0, 
[2025-07-28 01:02:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1064, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.44, #queue-req: 0, 
[2025-07-28 01:02:14 DP0 TP0] Decode batch. #running-req: 1, #token: 756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 233.43, #queue-req: 0, 
[2025-07-28 01:02:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1104, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.33, #queue-req: 0, 
[2025-07-28 01:02:14 DP0 TP0] Decode batch. #running-req: 1, #token: 796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.29, #queue-req: 0, 
[2025-07-28 01:02:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1144, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.33, #queue-req: 0, 
[2025-07-28 01:02:15 DP0 TP0] Decode batch. #running-req: 1, #token: 836, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.47, #queue-req: 0, 
[2025-07-28 01:02:15] INFO:     127.0.0.1:33530 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:02:15 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 173, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:02:15 DP1 TP0] Decode batch. #running-req: 2, #token: 1366, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.31, #queue-req: 0, 
[2025-07-28 01:02:15 DP1 TP0] Decode batch. #running-req: 2, #token: 1446, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.91, #queue-req: 0, 
[2025-07-28 01:02:15 DP1 TP0] Decode batch. #running-req: 2, #token: 1526, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.17, #queue-req: 0, 
[2025-07-28 01:02:15 DP1 TP0] Decode batch. #running-req: 2, #token: 1606, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.43, #queue-req: 0, 
[2025-07-28 01:02:15 DP1 TP0] Decode batch. #running-req: 2, #token: 1686, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.01, #queue-req: 0, 
[2025-07-28 01:02:16 DP1 TP0] Decode batch. #running-req: 2, #token: 1766, token usage: 0.00, cuda graph: True, gen throughput (token/s): 459.01, #queue-req: 0, 
[2025-07-28 01:02:16 DP1 TP0] Decode batch. #running-req: 2, #token: 1846, token usage: 0.00, cuda graph: True, gen throughput (token/s): 453.35, #queue-req: 0, 
[2025-07-28 01:02:16 DP1 TP0] Decode batch. #running-req: 2, #token: 1926, token usage: 0.00, cuda graph: True, gen throughput (token/s): 455.34, #queue-req: 0, 
[2025-07-28 01:02:16 DP1 TP0] Decode batch. #running-req: 2, #token: 2006, token usage: 0.00, cuda graph: True, gen throughput (token/s): 455.24, #queue-req: 0, 
[2025-07-28 01:02:16 DP1 TP0] Decode batch. #running-req: 2, #token: 2086, token usage: 0.00, cuda graph: True, gen throughput (token/s): 447.85, #queue-req: 0, 
[2025-07-28 01:02:16 DP1 TP0] Decode batch. #running-req: 2, #token: 2166, token usage: 0.00, cuda graph: True, gen throughput (token/s): 451.48, #queue-req: 0, 
[2025-07-28 01:02:17 DP1 TP0] Decode batch. #running-req: 2, #token: 2246, token usage: 0.00, cuda graph: True, gen throughput (token/s): 450.90, #queue-req: 0, 
[2025-07-28 01:02:17 DP1 TP0] Decode batch. #running-req: 2, #token: 2326, token usage: 0.00, cuda graph: True, gen throughput (token/s): 449.52, #queue-req: 0, 
[2025-07-28 01:02:17 DP1 TP0] Decode batch. #running-req: 2, #token: 2406, token usage: 0.00, cuda graph: True, gen throughput (token/s): 455.42, #queue-req: 0, 
[2025-07-28 01:02:17 DP1 TP0] Decode batch. #running-req: 2, #token: 2486, token usage: 0.00, cuda graph: True, gen throughput (token/s): 438.28, #queue-req: 0, 
[2025-07-28 01:02:17 DP1 TP0] Decode batch. #running-req: 2, #token: 2566, token usage: 0.00, cuda graph: True, gen throughput (token/s): 458.21, #queue-req: 0, 
[2025-07-28 01:02:18 DP1 TP0] Decode batch. #running-req: 2, #token: 2646, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.24, #queue-req: 0, 
[2025-07-28 01:02:18 DP1 TP0] Decode batch. #running-req: 2, #token: 2726, token usage: 0.00, cuda graph: True, gen throughput (token/s): 449.19, #queue-req: 0, 
[2025-07-28 01:02:18 DP1 TP0] Decode batch. #running-req: 2, #token: 2806, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.87, #queue-req: 0, 
[2025-07-28 01:02:18 DP1 TP0] Decode batch. #running-req: 2, #token: 2886, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.60, #queue-req: 0, 
[2025-07-28 01:02:18 DP1 TP0] Decode batch. #running-req: 2, #token: 2966, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.14, #queue-req: 0, 
[2025-07-28 01:02:18 DP1 TP0] Decode batch. #running-req: 2, #token: 3046, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.90, #queue-req: 0, 
[2025-07-28 01:02:19 DP1 TP0] Decode batch. #running-req: 2, #token: 3126, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.57, #queue-req: 0, 
[2025-07-28 01:02:19 DP1 TP0] Decode batch. #running-req: 2, #token: 3206, token usage: 0.00, cuda graph: True, gen throughput (token/s): 455.68, #queue-req: 0, 
[2025-07-28 01:02:19 DP1 TP0] Decode batch. #running-req: 2, #token: 3286, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.16, #queue-req: 0, 
[2025-07-28 01:02:19 DP1 TP0] Decode batch. #running-req: 2, #token: 3366, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.14, #queue-req: 0, 
[2025-07-28 01:02:19 DP1 TP0] Decode batch. #running-req: 2, #token: 3446, token usage: 0.00, cuda graph: True, gen throughput (token/s): 451.79, #queue-req: 0, 
[2025-07-28 01:02:19 DP1 TP0] Decode batch. #running-req: 2, #token: 3526, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.51, #queue-req: 0, 
[2025-07-28 01:02:20 DP1 TP0] Decode batch. #running-req: 2, #token: 3606, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.86, #queue-req: 0, 
[2025-07-28 01:02:20 DP1 TP0] Decode batch. #running-req: 2, #token: 3686, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.33, #queue-req: 0, 
[2025-07-28 01:02:20 DP1 TP0] Decode batch. #running-req: 2, #token: 3766, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.12, #queue-req: 0, 
[2025-07-28 01:02:20 DP1 TP0] Decode batch. #running-req: 2, #token: 3846, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.16, #queue-req: 0, 
[2025-07-28 01:02:20 DP1 TP0] Decode batch. #running-req: 2, #token: 3926, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.66, #queue-req: 0, 
[2025-07-28 01:02:20 DP1 TP0] Decode batch. #running-req: 2, #token: 4006, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.26, #queue-req: 0, 
[2025-07-28 01:02:21 DP1 TP0] Decode batch. #running-req: 2, #token: 4086, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.16, #queue-req: 0, 
[2025-07-28 01:02:21 DP1 TP0] Decode batch. #running-req: 2, #token: 4166, token usage: 0.00, cuda graph: True, gen throughput (token/s): 456.99, #queue-req: 0, 
[2025-07-28 01:02:21 DP1 TP0] Decode batch. #running-req: 2, #token: 4246, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.58, #queue-req: 0, 
[2025-07-28 01:02:21 DP1 TP0] Decode batch. #running-req: 2, #token: 4326, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.81, #queue-req: 0, 
[2025-07-28 01:02:21 DP1 TP0] Decode batch. #running-req: 2, #token: 4406, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.98, #queue-req: 0, 
[2025-07-28 01:02:22 DP1 TP0] Decode batch. #running-req: 2, #token: 4486, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.81, #queue-req: 0, 
[2025-07-28 01:02:22 DP1 TP0] Decode batch. #running-req: 2, #token: 4566, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.57, #queue-req: 0, 
[2025-07-28 01:02:22 DP1 TP0] Decode batch. #running-req: 2, #token: 4646, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.19, #queue-req: 0, 
[2025-07-28 01:02:22] INFO:     127.0.0.1:33534 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:02:22 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 226, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:02:22 DP1 TP0] Decode batch. #running-req: 1, #token: 2864, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.18, #queue-req: 0, 
[2025-07-28 01:02:22 DP0 TP0] Decode batch. #running-req: 1, #token: 334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 5.26, #queue-req: 0, 
[2025-07-28 01:02:22 DP1 TP0] Decode batch. #running-req: 1, #token: 2904, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.44, #queue-req: 0, 
[2025-07-28 01:02:22 DP0 TP0] Decode batch. #running-req: 1, #token: 374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 220.42, #queue-req: 0, 
[2025-07-28 01:02:22 DP1 TP0] Decode batch. #running-req: 1, #token: 2944, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.89, #queue-req: 0, 
[2025-07-28 01:02:22 DP0 TP0] Decode batch. #running-req: 1, #token: 414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.70, #queue-req: 0, 
[2025-07-28 01:02:23 DP1 TP0] Decode batch. #running-req: 1, #token: 2984, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.24, #queue-req: 0, 
[2025-07-28 01:02:23] INFO:     127.0.0.1:38624 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:02:23 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 168, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:02:23 DP0 TP0] Decode batch. #running-req: 1, #token: 454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.98, #queue-req: 0, 
[2025-07-28 01:02:23 DP1 TP0] Decode batch. #running-req: 1, #token: 285, token usage: 0.00, cuda graph: True, gen throughput (token/s): 172.19, #queue-req: 0, 
[2025-07-28 01:02:23 DP0 TP0] Decode batch. #running-req: 1, #token: 494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.72, #queue-req: 0, 
[2025-07-28 01:02:23 DP1 TP0] Decode batch. #running-req: 1, #token: 325, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.84, #queue-req: 0, 
[2025-07-28 01:02:23 DP0 TP0] Decode batch. #running-req: 1, #token: 534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.73, #queue-req: 0, 
[2025-07-28 01:02:23 DP1 TP0] Decode batch. #running-req: 1, #token: 365, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.85, #queue-req: 0, 
[2025-07-28 01:02:23 DP0 TP0] Decode batch. #running-req: 1, #token: 574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.12, #queue-req: 0, 
[2025-07-28 01:02:23 DP1 TP0] Decode batch. #running-req: 1, #token: 405, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.13, #queue-req: 0, 
[2025-07-28 01:02:23 DP0 TP0] Decode batch. #running-req: 1, #token: 614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.68, #queue-req: 0, 
[2025-07-28 01:02:23 DP1 TP0] Decode batch. #running-req: 1, #token: 445, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.49, #queue-req: 0, 
[2025-07-28 01:02:23 DP0 TP0] Decode batch. #running-req: 1, #token: 654, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.87, #queue-req: 0, 
[2025-07-28 01:02:24 DP1 TP0] Decode batch. #running-req: 1, #token: 485, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.26, #queue-req: 0, 
[2025-07-28 01:02:24 DP0 TP0] Decode batch. #running-req: 1, #token: 694, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.82, #queue-req: 0, 
[2025-07-28 01:02:24 DP1 TP0] Decode batch. #running-req: 1, #token: 525, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.72, #queue-req: 0, 
[2025-07-28 01:02:24 DP0 TP0] Decode batch. #running-req: 1, #token: 734, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.88, #queue-req: 0, 
[2025-07-28 01:02:24 DP1 TP0] Decode batch. #running-req: 1, #token: 565, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.09, #queue-req: 0, 
[2025-07-28 01:02:24 DP0 TP0] Decode batch. #running-req: 1, #token: 774, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.95, #queue-req: 0, 
[2025-07-28 01:02:24 DP1 TP0] Decode batch. #running-req: 1, #token: 605, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.10, #queue-req: 0, 
[2025-07-28 01:02:24 DP0 TP0] Decode batch. #running-req: 1, #token: 814, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.43, #queue-req: 0, 
[2025-07-28 01:02:24 DP1 TP0] Decode batch. #running-req: 1, #token: 645, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.30, #queue-req: 0, 
[2025-07-28 01:02:24 DP0 TP0] Decode batch. #running-req: 1, #token: 854, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.82, #queue-req: 0, 
[2025-07-28 01:02:24 DP1 TP0] Decode batch. #running-req: 1, #token: 685, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.54, #queue-req: 0, 
[2025-07-28 01:02:24 DP0 TP0] Decode batch. #running-req: 1, #token: 894, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.48, #queue-req: 0, 
[2025-07-28 01:02:25 DP1 TP0] Decode batch. #running-req: 1, #token: 725, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.23, #queue-req: 0, 
[2025-07-28 01:02:25 DP0 TP0] Decode batch. #running-req: 1, #token: 934, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.44, #queue-req: 0, 
[2025-07-28 01:02:25 DP1 TP0] Decode batch. #running-req: 1, #token: 765, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.12, #queue-req: 0, 
[2025-07-28 01:02:25 DP0 TP0] Decode batch. #running-req: 1, #token: 974, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.99, #queue-req: 0, 
[2025-07-28 01:02:25 DP1 TP0] Decode batch. #running-req: 1, #token: 805, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.91, #queue-req: 0, 
[2025-07-28 01:02:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1014, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.86, #queue-req: 0, 
[2025-07-28 01:02:25 DP1 TP0] Decode batch. #running-req: 1, #token: 845, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.73, #queue-req: 0, 
[2025-07-28 01:02:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1054, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.37, #queue-req: 0, 
[2025-07-28 01:02:25 DP1 TP0] Decode batch. #running-req: 1, #token: 885, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.71, #queue-req: 0, 
[2025-07-28 01:02:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1094, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.63, #queue-req: 0, 
[2025-07-28 01:02:25 DP1 TP0] Decode batch. #running-req: 1, #token: 925, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.27, #queue-req: 0, 
[2025-07-28 01:02:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1134, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.04, #queue-req: 0, 
[2025-07-28 01:02:26 DP1 TP0] Decode batch. #running-req: 1, #token: 965, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.38, #queue-req: 0, 
[2025-07-28 01:02:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1174, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.51, #queue-req: 0, 
[2025-07-28 01:02:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1005, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.89, #queue-req: 0, 
[2025-07-28 01:02:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1214, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.07, #queue-req: 0, 
[2025-07-28 01:02:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1045, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.12, #queue-req: 0, 
[2025-07-28 01:02:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1254, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.65, #queue-req: 0, 
[2025-07-28 01:02:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1085, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.63, #queue-req: 0, 
[2025-07-28 01:02:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.41, #queue-req: 0, 
[2025-07-28 01:02:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1125, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.48, #queue-req: 0, 
[2025-07-28 01:02:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.06, #queue-req: 0, 
[2025-07-28 01:02:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1165, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.74, #queue-req: 0, 
[2025-07-28 01:02:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.14, #queue-req: 0, 
[2025-07-28 01:02:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1205, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.05, #queue-req: 0, 
[2025-07-28 01:02:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.97, #queue-req: 0, 
[2025-07-28 01:02:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1245, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.94, #queue-req: 0, 
[2025-07-28 01:02:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.27, #queue-req: 0, 
[2025-07-28 01:02:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1285, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.84, #queue-req: 0, 
[2025-07-28 01:02:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.41, #queue-req: 0, 
[2025-07-28 01:02:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1325, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.59, #queue-req: 0, 
[2025-07-28 01:02:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.31, #queue-req: 0, 
[2025-07-28 01:02:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1365, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.31, #queue-req: 0, 
[2025-07-28 01:02:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.05, #queue-req: 0, 
[2025-07-28 01:02:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1405, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.46, #queue-req: 0, 
[2025-07-28 01:02:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.21, #queue-req: 0, 
[2025-07-28 01:02:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1445, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.41, #queue-req: 0, 
[2025-07-28 01:02:28] INFO:     127.0.0.1:58912 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:02:28 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 154, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:02:28 DP0 TP0] Decode batch. #running-req: 2, #token: 1815, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.75, #queue-req: 0, 
[2025-07-28 01:02:28 DP0 TP0] Decode batch. #running-req: 2, #token: 1895, token usage: 0.00, cuda graph: True, gen throughput (token/s): 444.23, #queue-req: 0, 
[2025-07-28 01:02:28 DP0 TP0] Decode batch. #running-req: 2, #token: 1975, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.61, #queue-req: 0, 
[2025-07-28 01:02:28 DP0 TP0] Decode batch. #running-req: 2, #token: 2055, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.60, #queue-req: 0, 
[2025-07-28 01:02:28 DP0 TP0] Decode batch. #running-req: 2, #token: 2135, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.26, #queue-req: 0, 
[2025-07-28 01:02:29 DP0 TP0] Decode batch. #running-req: 2, #token: 2215, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.94, #queue-req: 0, 
[2025-07-28 01:02:29 DP0 TP0] Decode batch. #running-req: 2, #token: 2295, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.74, #queue-req: 0, 
[2025-07-28 01:02:29 DP0 TP0] Decode batch. #running-req: 2, #token: 2375, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.77, #queue-req: 0, 
[2025-07-28 01:02:29 DP0 TP0] Decode batch. #running-req: 2, #token: 2455, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.59, #queue-req: 0, 
[2025-07-28 01:02:29 DP0 TP0] Decode batch. #running-req: 2, #token: 2535, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.99, #queue-req: 0, 
[2025-07-28 01:02:29 DP0 TP0] Decode batch. #running-req: 2, #token: 2615, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.69, #queue-req: 0, 
[2025-07-28 01:02:30 DP0 TP0] Decode batch. #running-req: 2, #token: 2695, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.93, #queue-req: 0, 
[2025-07-28 01:02:30] INFO:     127.0.0.1:58910 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:02:30 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 141, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:02:30 DP0 TP0] Decode batch. #running-req: 1, #token: 728, token usage: 0.00, cuda graph: True, gen throughput (token/s): 416.69, #queue-req: 0, 
[2025-07-28 01:02:30 DP0 TP0] Decode batch. #running-req: 1, #token: 768, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.59, #queue-req: 0, 
[2025-07-28 01:02:30 DP1 TP0] Decode batch. #running-req: 1, #token: 259, token usage: 0.00, cuda graph: True, gen throughput (token/s): 16.55, #queue-req: 0, 
[2025-07-28 01:02:30 DP0 TP0] Decode batch. #running-req: 1, #token: 808, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.16, #queue-req: 0, 
[2025-07-28 01:02:30 DP1 TP0] Decode batch. #running-req: 1, #token: 299, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.19, #queue-req: 0, 
[2025-07-28 01:02:30 DP0 TP0] Decode batch. #running-req: 1, #token: 848, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.05, #queue-req: 0, 
[2025-07-28 01:02:30 DP1 TP0] Decode batch. #running-req: 1, #token: 339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.56, #queue-req: 0, 
[2025-07-28 01:02:30 DP0 TP0] Decode batch. #running-req: 1, #token: 888, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.24, #queue-req: 0, 
[2025-07-28 01:02:30 DP1 TP0] Decode batch. #running-req: 1, #token: 379, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.76, #queue-req: 0, 
[2025-07-28 01:02:31 DP0 TP0] Decode batch. #running-req: 1, #token: 928, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.47, #queue-req: 0, 
[2025-07-28 01:02:31 DP1 TP0] Decode batch. #running-req: 1, #token: 419, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.18, #queue-req: 0, 
[2025-07-28 01:02:31 DP0 TP0] Decode batch. #running-req: 1, #token: 968, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.20, #queue-req: 0, 
[2025-07-28 01:02:31 DP1 TP0] Decode batch. #running-req: 1, #token: 459, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.19, #queue-req: 0, 
[2025-07-28 01:02:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1008, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.23, #queue-req: 0, 
[2025-07-28 01:02:31 DP1 TP0] Decode batch. #running-req: 1, #token: 499, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.10, #queue-req: 0, 
[2025-07-28 01:02:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1048, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.81, #queue-req: 0, 
[2025-07-28 01:02:31 DP1 TP0] Decode batch. #running-req: 1, #token: 539, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.83, #queue-req: 0, 
[2025-07-28 01:02:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1088, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.12, #queue-req: 0, 
[2025-07-28 01:02:31 DP1 TP0] Decode batch. #running-req: 1, #token: 579, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.87, #queue-req: 0, 
[2025-07-28 01:02:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1128, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.94, #queue-req: 0, 
[2025-07-28 01:02:31 DP1 TP0] Decode batch. #running-req: 1, #token: 619, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.50, #queue-req: 0, 
[2025-07-28 01:02:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1168, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.26, #queue-req: 0, 
[2025-07-28 01:02:32 DP1 TP0] Decode batch. #running-req: 1, #token: 659, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.88, #queue-req: 0, 
[2025-07-28 01:02:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1208, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.27, #queue-req: 0, 
[2025-07-28 01:02:32 DP1 TP0] Decode batch. #running-req: 1, #token: 699, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.19, #queue-req: 0, 
[2025-07-28 01:02:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1248, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.46, #queue-req: 0, 
[2025-07-28 01:02:32 DP1 TP0] Decode batch. #running-req: 1, #token: 739, token usage: 0.00, cuda graph: True, gen throughput (token/s): 227.14, #queue-req: 0, 
[2025-07-28 01:02:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1288, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.38, #queue-req: 0, 
[2025-07-28 01:02:32 DP1 TP0] Decode batch. #running-req: 1, #token: 779, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.92, #queue-req: 0, 
[2025-07-28 01:02:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1328, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.29, #queue-req: 0, 
[2025-07-28 01:02:32 DP1 TP0] Decode batch. #running-req: 1, #token: 819, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.70, #queue-req: 0, 
[2025-07-28 01:02:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1368, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.07, #queue-req: 0, 
[2025-07-28 01:02:32 DP1 TP0] Decode batch. #running-req: 1, #token: 859, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.26, #queue-req: 0, 
[2025-07-28 01:02:32] INFO:     127.0.0.1:58926 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:02:32 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 192, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:02:33 DP1 TP0] Decode batch. #running-req: 1, #token: 899, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.12, #queue-req: 0, 
[2025-07-28 01:02:33 DP0 TP0] Decode batch. #running-req: 1, #token: 298, token usage: 0.00, cuda graph: True, gen throughput (token/s): 170.01, #queue-req: 0, 
[2025-07-28 01:02:33 DP1 TP0] Decode batch. #running-req: 1, #token: 939, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.54, #queue-req: 0, 
[2025-07-28 01:02:33 DP0 TP0] Decode batch. #running-req: 1, #token: 338, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.65, #queue-req: 0, 
[2025-07-28 01:02:33 DP0 TP0] Decode batch. #running-req: 1, #token: 378, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.62, #queue-req: 0, 
[2025-07-28 01:02:33 DP1 TP0] Decode batch. #running-req: 1, #token: 979, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.87, #queue-req: 0, 
[2025-07-28 01:02:33 DP0 TP0] Decode batch. #running-req: 1, #token: 418, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.59, #queue-req: 0, 
[2025-07-28 01:02:33 DP1 TP0] Decode batch. #running-req: 1, #token: 1019, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.66, #queue-req: 0, 
[2025-07-28 01:02:33 DP0 TP0] Decode batch. #running-req: 1, #token: 458, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.62, #queue-req: 0, 
[2025-07-28 01:02:33 DP1 TP0] Decode batch. #running-req: 1, #token: 1059, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.87, #queue-req: 0, 
[2025-07-28 01:02:33 DP0 TP0] Decode batch. #running-req: 1, #token: 498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.36, #queue-req: 0, 
[2025-07-28 01:02:33 DP1 TP0] Decode batch. #running-req: 1, #token: 1099, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.60, #queue-req: 0, 
[2025-07-28 01:02:34 DP0 TP0] Decode batch. #running-req: 1, #token: 538, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.78, #queue-req: 0, 
[2025-07-28 01:02:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1139, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.56, #queue-req: 0, 
[2025-07-28 01:02:34 DP0 TP0] Decode batch. #running-req: 1, #token: 578, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.88, #queue-req: 0, 
[2025-07-28 01:02:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1179, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.44, #queue-req: 0, 
[2025-07-28 01:02:34 DP0 TP0] Decode batch. #running-req: 1, #token: 618, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.77, #queue-req: 0, 
[2025-07-28 01:02:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1219, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.27, #queue-req: 0, 
[2025-07-28 01:02:34 DP0 TP0] Decode batch. #running-req: 1, #token: 658, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.65, #queue-req: 0, 
[2025-07-28 01:02:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1259, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.69, #queue-req: 0, 
[2025-07-28 01:02:34 DP0 TP0] Decode batch. #running-req: 1, #token: 698, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.68, #queue-req: 0, 
[2025-07-28 01:02:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1299, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.14, #queue-req: 0, 
[2025-07-28 01:02:34 DP0 TP0] Decode batch. #running-req: 1, #token: 738, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.73, #queue-req: 0, 
[2025-07-28 01:02:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.58, #queue-req: 0, 
[2025-07-28 01:02:35 DP0 TP0] Decode batch. #running-req: 1, #token: 778, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.72, #queue-req: 0, 
[2025-07-28 01:02:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1379, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.28, #queue-req: 0, 
[2025-07-28 01:02:35 DP0 TP0] Decode batch. #running-req: 1, #token: 818, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.77, #queue-req: 0, 
[2025-07-28 01:02:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1419, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.14, #queue-req: 0, 
[2025-07-28 01:02:35] INFO:     127.0.0.1:58932 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:02:35 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 190, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:02:35 DP0 TP0] Decode batch. #running-req: 1, #token: 858, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.83, #queue-req: 0, 
[2025-07-28 01:02:35 DP1 TP0] Decode batch. #running-req: 1, #token: 300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 171.38, #queue-req: 0, 
[2025-07-28 01:02:35 DP0 TP0] Decode batch. #running-req: 1, #token: 898, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.82, #queue-req: 0, 
[2025-07-28 01:02:35 DP1 TP0] Decode batch. #running-req: 1, #token: 340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.64, #queue-req: 0, 
[2025-07-28 01:02:35 DP0 TP0] Decode batch. #running-req: 1, #token: 938, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.51, #queue-req: 0, 
[2025-07-28 01:02:35 DP1 TP0] Decode batch. #running-req: 1, #token: 380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.99, #queue-req: 0, 
[2025-07-28 01:02:35 DP0 TP0] Decode batch. #running-req: 1, #token: 978, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.31, #queue-req: 0, 
[2025-07-28 01:02:35 DP1 TP0] Decode batch. #running-req: 1, #token: 420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.28, #queue-req: 0, 
[2025-07-28 01:02:36 DP0 TP0] Decode batch. #running-req: 1, #token: 1018, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.59, #queue-req: 0, 
[2025-07-28 01:02:36 DP1 TP0] Decode batch. #running-req: 1, #token: 460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.12, #queue-req: 0, 
[2025-07-28 01:02:36 DP0 TP0] Decode batch. #running-req: 1, #token: 1058, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.35, #queue-req: 0, 
[2025-07-28 01:02:36 DP1 TP0] Decode batch. #running-req: 1, #token: 500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.71, #queue-req: 0, 
[2025-07-28 01:02:36 DP0 TP0] Decode batch. #running-req: 1, #token: 1098, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.16, #queue-req: 0, 
[2025-07-28 01:02:36 DP1 TP0] Decode batch. #running-req: 1, #token: 540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.11, #queue-req: 0, 
[2025-07-28 01:02:36 DP0 TP0] Decode batch. #running-req: 1, #token: 1138, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.25, #queue-req: 0, 
[2025-07-28 01:02:36 DP1 TP0] Decode batch. #running-req: 1, #token: 580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.97, #queue-req: 0, 
[2025-07-28 01:02:36 DP0 TP0] Decode batch. #running-req: 1, #token: 1178, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.69, #queue-req: 0, 
[2025-07-28 01:02:36 DP1 TP0] Decode batch. #running-req: 1, #token: 620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.02, #queue-req: 0, 
[2025-07-28 01:02:36 DP0 TP0] Decode batch. #running-req: 1, #token: 1218, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.84, #queue-req: 0, 
[2025-07-28 01:02:36 DP1 TP0] Decode batch. #running-req: 1, #token: 660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.59, #queue-req: 0, 
[2025-07-28 01:02:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1258, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.64, #queue-req: 0, 
[2025-07-28 01:02:37 DP1 TP0] Decode batch. #running-req: 1, #token: 700, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.57, #queue-req: 0, 
[2025-07-28 01:02:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1298, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.28, #queue-req: 0, 
[2025-07-28 01:02:37 DP1 TP0] Decode batch. #running-req: 1, #token: 740, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.10, #queue-req: 0, 
[2025-07-28 01:02:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1338, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.13, #queue-req: 0, 
[2025-07-28 01:02:37 DP1 TP0] Decode batch. #running-req: 1, #token: 780, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.43, #queue-req: 0, 
[2025-07-28 01:02:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1378, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.23, #queue-req: 0, 
[2025-07-28 01:02:37 DP1 TP0] Decode batch. #running-req: 1, #token: 820, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.83, #queue-req: 0, 
[2025-07-28 01:02:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1418, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.27, #queue-req: 0, 
[2025-07-28 01:02:37 DP1 TP0] Decode batch. #running-req: 1, #token: 860, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.93, #queue-req: 0, 
[2025-07-28 01:02:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1458, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.77, #queue-req: 0, 
[2025-07-28 01:02:37 DP1 TP0] Decode batch. #running-req: 1, #token: 900, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.87, #queue-req: 0, 
[2025-07-28 01:02:38 DP0 TP0] Decode batch. #running-req: 1, #token: 1498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.92, #queue-req: 0, 
[2025-07-28 01:02:38 DP1 TP0] Decode batch. #running-req: 1, #token: 940, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.41, #queue-req: 0, 
[2025-07-28 01:02:38 DP0 TP0] Decode batch. #running-req: 1, #token: 1538, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.27, #queue-req: 0, 
[2025-07-28 01:02:38 DP1 TP0] Decode batch. #running-req: 1, #token: 980, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.47, #queue-req: 0, 
[2025-07-28 01:02:38 DP0 TP0] Decode batch. #running-req: 1, #token: 1578, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.64, #queue-req: 0, 
[2025-07-28 01:02:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1020, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.36, #queue-req: 0, 
[2025-07-28 01:02:38 DP0 TP0] Decode batch. #running-req: 1, #token: 1618, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.02, #queue-req: 0, 
[2025-07-28 01:02:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1060, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.42, #queue-req: 0, 
[2025-07-28 01:02:38 DP0 TP0] Decode batch. #running-req: 1, #token: 1658, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.96, #queue-req: 0, 
[2025-07-28 01:02:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1100, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.21, #queue-req: 0, 
[2025-07-28 01:02:38 DP0 TP0] Decode batch. #running-req: 1, #token: 1698, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.21, #queue-req: 0, 
[2025-07-28 01:02:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1140, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.19, #queue-req: 0, 
[2025-07-28 01:02:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1738, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.47, #queue-req: 0, 
[2025-07-28 01:02:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1180, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.29, #queue-req: 0, 
[2025-07-28 01:02:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1778, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.02, #queue-req: 0, 
[2025-07-28 01:02:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1220, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.75, #queue-req: 0, 
[2025-07-28 01:02:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1818, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.71, #queue-req: 0, 
[2025-07-28 01:02:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1260, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.81, #queue-req: 0, 
[2025-07-28 01:02:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1858, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.92, #queue-req: 0, 
[2025-07-28 01:02:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.31, #queue-req: 0, 
[2025-07-28 01:02:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1898, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.60, #queue-req: 0, 
[2025-07-28 01:02:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.27, #queue-req: 0, 
[2025-07-28 01:02:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1938, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.38, #queue-req: 0, 
[2025-07-28 01:02:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.35, #queue-req: 0, 
[2025-07-28 01:02:40 DP0 TP0] Decode batch. #running-req: 1, #token: 1978, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.92, #queue-req: 0, 
[2025-07-28 01:02:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.54, #queue-req: 0, 
[2025-07-28 01:02:40 DP0 TP0] Decode batch. #running-req: 1, #token: 2018, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.86, #queue-req: 0, 
[2025-07-28 01:02:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.88, #queue-req: 0, 
[2025-07-28 01:02:40 DP0 TP0] Decode batch. #running-req: 1, #token: 2058, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.76, #queue-req: 0, 
[2025-07-28 01:02:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.81, #queue-req: 0, 
[2025-07-28 01:02:40 DP0 TP0] Decode batch. #running-req: 1, #token: 2098, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.10, #queue-req: 0, 
[2025-07-28 01:02:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.47, #queue-req: 0, 
[2025-07-28 01:02:40 DP0 TP0] Decode batch. #running-req: 1, #token: 2138, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.21, #queue-req: 0, 
[2025-07-28 01:02:40] INFO:     127.0.0.1:49228 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:02:40 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 137, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:02:40 DP0 TP0] Decode batch. #running-req: 2, #token: 2341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.40, #queue-req: 0, 
[2025-07-28 01:02:41 DP0 TP0] Decode batch. #running-req: 2, #token: 2421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.41, #queue-req: 0, 
[2025-07-28 01:02:41 DP0 TP0] Decode batch. #running-req: 2, #token: 2501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.56, #queue-req: 0, 
[2025-07-28 01:02:41 DP0 TP0] Decode batch. #running-req: 2, #token: 2581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.31, #queue-req: 0, 
[2025-07-28 01:02:41 DP0 TP0] Decode batch. #running-req: 2, #token: 2661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.54, #queue-req: 0, 
[2025-07-28 01:02:41 DP0 TP0] Decode batch. #running-req: 2, #token: 2741, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.89, #queue-req: 0, 
[2025-07-28 01:02:41 DP0 TP0] Decode batch. #running-req: 2, #token: 2821, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.10, #queue-req: 0, 
[2025-07-28 01:02:42 DP0 TP0] Decode batch. #running-req: 2, #token: 2901, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.80, #queue-req: 0, 
[2025-07-28 01:02:42 DP0 TP0] Decode batch. #running-req: 2, #token: 2981, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.04, #queue-req: 0, 
[2025-07-28 01:02:42 DP0 TP0] Decode batch. #running-req: 2, #token: 3061, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.68, #queue-req: 0, 
[2025-07-28 01:02:42] INFO:     127.0.0.1:49216 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:02:42 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 132, #cached-token: 89, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:02:42 DP0 TP0] Decode batch. #running-req: 1, #token: 648, token usage: 0.00, cuda graph: True, gen throughput (token/s): 401.24, #queue-req: 0, 
[2025-07-28 01:02:42 DP1 TP0] Decode batch. #running-req: 1, #token: 224, token usage: 0.00, cuda graph: True, gen throughput (token/s): 19.03, #queue-req: 0, 
[2025-07-28 01:02:42 DP0 TP0] Decode batch. #running-req: 1, #token: 688, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.37, #queue-req: 0, 
[2025-07-28 01:02:42 DP1 TP0] Decode batch. #running-req: 1, #token: 264, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.89, #queue-req: 0, 
[2025-07-28 01:02:42 DP0 TP0] Decode batch. #running-req: 1, #token: 728, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.40, #queue-req: 0, 
[2025-07-28 01:02:43 DP1 TP0] Decode batch. #running-req: 1, #token: 304, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.46, #queue-req: 0, 
[2025-07-28 01:02:43 DP0 TP0] Decode batch. #running-req: 1, #token: 768, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.37, #queue-req: 0, 
[2025-07-28 01:02:43 DP1 TP0] Decode batch. #running-req: 1, #token: 344, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.94, #queue-req: 0, 
[2025-07-28 01:02:43 DP0 TP0] Decode batch. #running-req: 1, #token: 808, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.87, #queue-req: 0, 
[2025-07-28 01:02:43 DP1 TP0] Decode batch. #running-req: 1, #token: 384, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.11, #queue-req: 0, 
[2025-07-28 01:02:43 DP0 TP0] Decode batch. #running-req: 1, #token: 848, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.38, #queue-req: 0, 
[2025-07-28 01:02:43 DP1 TP0] Decode batch. #running-req: 1, #token: 424, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.91, #queue-req: 0, 
[2025-07-28 01:02:43 DP0 TP0] Decode batch. #running-req: 1, #token: 888, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.66, #queue-req: 0, 
[2025-07-28 01:02:43 DP1 TP0] Decode batch. #running-req: 1, #token: 464, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.33, #queue-req: 0, 
[2025-07-28 01:02:43 DP0 TP0] Decode batch. #running-req: 1, #token: 928, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.89, #queue-req: 0, 
[2025-07-28 01:02:43 DP1 TP0] Decode batch. #running-req: 1, #token: 504, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.68, #queue-req: 0, 
[2025-07-28 01:02:43 DP0 TP0] Decode batch. #running-req: 1, #token: 968, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.93, #queue-req: 0, 
[2025-07-28 01:02:44 DP1 TP0] Decode batch. #running-req: 1, #token: 544, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.67, #queue-req: 0, 
[2025-07-28 01:02:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1008, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.76, #queue-req: 0, 
[2025-07-28 01:02:44 DP1 TP0] Decode batch. #running-req: 1, #token: 584, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.00, #queue-req: 0, 
[2025-07-28 01:02:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1048, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.09, #queue-req: 0, 
[2025-07-28 01:02:44 DP1 TP0] Decode batch. #running-req: 1, #token: 624, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.14, #queue-req: 0, 
[2025-07-28 01:02:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1088, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.45, #queue-req: 0, 
[2025-07-28 01:02:44 DP1 TP0] Decode batch. #running-req: 1, #token: 664, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.43, #queue-req: 0, 
[2025-07-28 01:02:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1128, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.57, #queue-req: 0, 
[2025-07-28 01:02:44 DP1 TP0] Decode batch. #running-req: 1, #token: 704, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.46, #queue-req: 0, 
[2025-07-28 01:02:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1168, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.54, #queue-req: 0, 
[2025-07-28 01:02:44 DP1 TP0] Decode batch. #running-req: 1, #token: 744, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.15, #queue-req: 0, 
[2025-07-28 01:02:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1208, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.90, #queue-req: 0, 
[2025-07-28 01:02:45 DP1 TP0] Decode batch. #running-req: 1, #token: 784, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.18, #queue-req: 0, 
[2025-07-28 01:02:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1248, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.02, #queue-req: 0, 
[2025-07-28 01:02:45 DP1 TP0] Decode batch. #running-req: 1, #token: 824, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.91, #queue-req: 0, 
[2025-07-28 01:02:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1288, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.90, #queue-req: 0, 
[2025-07-28 01:02:45 DP1 TP0] Decode batch. #running-req: 1, #token: 864, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.94, #queue-req: 0, 
[2025-07-28 01:02:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1328, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.31, #queue-req: 0, 
[2025-07-28 01:02:45 DP1 TP0] Decode batch. #running-req: 1, #token: 904, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.07, #queue-req: 0, 
[2025-07-28 01:02:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1368, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.12, #queue-req: 0, 
[2025-07-28 01:02:45 DP1 TP0] Decode batch. #running-req: 1, #token: 944, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.09, #queue-req: 0, 
[2025-07-28 01:02:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1408, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.39, #queue-req: 0, 
[2025-07-28 01:02:45 DP1 TP0] Decode batch. #running-req: 1, #token: 984, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.22, #queue-req: 0, 
[2025-07-28 01:02:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1448, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.86, #queue-req: 0, 
[2025-07-28 01:02:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1024, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.14, #queue-req: 0, 
[2025-07-28 01:02:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1488, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.99, #queue-req: 0, 
[2025-07-28 01:02:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1064, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.11, #queue-req: 0, 
[2025-07-28 01:02:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1528, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.06, #queue-req: 0, 
[2025-07-28 01:02:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1104, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.84, #queue-req: 0, 
[2025-07-28 01:02:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1568, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.44, #queue-req: 0, 
[2025-07-28 01:02:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1144, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.95, #queue-req: 0, 
[2025-07-28 01:02:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1608, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.17, #queue-req: 0, 
[2025-07-28 01:02:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1184, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.53, #queue-req: 0, 
[2025-07-28 01:02:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1648, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.98, #queue-req: 0, 
[2025-07-28 01:02:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1224, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.47, #queue-req: 0, 
[2025-07-28 01:02:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1688, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.59, #queue-req: 0, 
[2025-07-28 01:02:47 DP1 TP0] Decode batch. #running-req: 1, #token: 1264, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.98, #queue-req: 0, 
[2025-07-28 01:02:47 DP0 TP0] Decode batch. #running-req: 1, #token: 1728, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.76, #queue-req: 0, 
[2025-07-28 01:02:47 DP1 TP0] Decode batch. #running-req: 1, #token: 1304, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.39, #queue-req: 0, 
[2025-07-28 01:02:47 DP0 TP0] Decode batch. #running-req: 1, #token: 1768, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.64, #queue-req: 0, 
[2025-07-28 01:02:47] INFO:     127.0.0.1:49230 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:02:47 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 199, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:02:47 DP1 TP0] Decode batch. #running-req: 1, #token: 1344, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.98, #queue-req: 0, 
[2025-07-28 01:02:47 DP0 TP0] Decode batch. #running-req: 1, #token: 311, token usage: 0.00, cuda graph: True, gen throughput (token/s): 176.26, #queue-req: 0, 
[2025-07-28 01:02:47 DP1 TP0] Decode batch. #running-req: 1, #token: 1384, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.16, #queue-req: 0, 
[2025-07-28 01:02:47 DP0 TP0] Decode batch. #running-req: 1, #token: 351, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.86, #queue-req: 0, 
[2025-07-28 01:02:47 DP1 TP0] Decode batch. #running-req: 1, #token: 1424, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.47, #queue-req: 0, 
[2025-07-28 01:02:47 DP0 TP0] Decode batch. #running-req: 1, #token: 391, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.93, #queue-req: 0, 
[2025-07-28 01:02:47 DP1 TP0] Decode batch. #running-req: 1, #token: 1464, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.88, #queue-req: 0, 
[2025-07-28 01:02:47 DP0 TP0] Decode batch. #running-req: 1, #token: 431, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.16, #queue-req: 0, 
[2025-07-28 01:02:47 DP1 TP0] Decode batch. #running-req: 1, #token: 1504, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.77, #queue-req: 0, 
[2025-07-28 01:02:48 DP0 TP0] Decode batch. #running-req: 1, #token: 471, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.03, #queue-req: 0, 
[2025-07-28 01:02:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1544, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.58, #queue-req: 0, 
[2025-07-28 01:02:48 DP0 TP0] Decode batch. #running-req: 1, #token: 511, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.95, #queue-req: 0, 
[2025-07-28 01:02:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1584, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.18, #queue-req: 0, 
[2025-07-28 01:02:48 DP0 TP0] Decode batch. #running-req: 1, #token: 551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.47, #queue-req: 0, 
[2025-07-28 01:02:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1624, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.42, #queue-req: 0, 
[2025-07-28 01:02:48 DP0 TP0] Decode batch. #running-req: 1, #token: 591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.88, #queue-req: 0, 
[2025-07-28 01:02:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1664, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.52, #queue-req: 0, 
[2025-07-28 01:02:48 DP0 TP0] Decode batch. #running-req: 1, #token: 631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.43, #queue-req: 0, 
[2025-07-28 01:02:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1704, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.92, #queue-req: 0, 
[2025-07-28 01:02:48 DP0 TP0] Decode batch. #running-req: 1, #token: 671, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.79, #queue-req: 0, 
[2025-07-28 01:02:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1744, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.48, #queue-req: 0, 
[2025-07-28 01:02:49 DP0 TP0] Decode batch. #running-req: 1, #token: 711, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.00, #queue-req: 0, 
[2025-07-28 01:02:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1784, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.71, #queue-req: 0, 
[2025-07-28 01:02:49 DP0 TP0] Decode batch. #running-req: 1, #token: 751, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.03, #queue-req: 0, 
[2025-07-28 01:02:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1824, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.94, #queue-req: 0, 
[2025-07-28 01:02:49 DP0 TP0] Decode batch. #running-req: 1, #token: 791, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.70, #queue-req: 0, 
[2025-07-28 01:02:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1864, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.36, #queue-req: 0, 
[2025-07-28 01:02:49 DP0 TP0] Decode batch. #running-req: 1, #token: 831, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.05, #queue-req: 0, 
[2025-07-28 01:02:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1904, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.15, #queue-req: 0, 
[2025-07-28 01:02:49 DP0 TP0] Decode batch. #running-req: 1, #token: 871, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.32, #queue-req: 0, 
[2025-07-28 01:02:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1944, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.52, #queue-req: 0, 
[2025-07-28 01:02:49 DP0 TP0] Decode batch. #running-req: 1, #token: 911, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.44, #queue-req: 0, 
[2025-07-28 01:02:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1984, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.83, #queue-req: 0, 
[2025-07-28 01:02:50 DP0 TP0] Decode batch. #running-req: 1, #token: 951, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.52, #queue-req: 0, 
[2025-07-28 01:02:50 DP1 TP0] Decode batch. #running-req: 1, #token: 2024, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.96, #queue-req: 0, 
[2025-07-28 01:02:50 DP0 TP0] Decode batch. #running-req: 1, #token: 991, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.08, #queue-req: 0, 
[2025-07-28 01:02:50 DP1 TP0] Decode batch. #running-req: 1, #token: 2064, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.64, #queue-req: 0, 
[2025-07-28 01:02:50 DP0 TP0] Decode batch. #running-req: 1, #token: 1031, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.85, #queue-req: 0, 
[2025-07-28 01:02:50 DP1 TP0] Decode batch. #running-req: 1, #token: 2104, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.40, #queue-req: 0, 
[2025-07-28 01:02:50] INFO:     127.0.0.1:42000 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:02:50 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 194, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:02:50 DP1 TP0] Decode batch. #running-req: 2, #token: 2367, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.21, #queue-req: 0, 
[2025-07-28 01:02:50 DP1 TP0] Decode batch. #running-req: 2, #token: 2447, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.56, #queue-req: 0, 
[2025-07-28 01:02:51 DP1 TP0] Decode batch. #running-req: 2, #token: 2527, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.00, #queue-req: 0, 
[2025-07-28 01:02:51 DP1 TP0] Decode batch. #running-req: 2, #token: 2607, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.26, #queue-req: 0, 
[2025-07-28 01:02:51 DP1 TP0] Decode batch. #running-req: 2, #token: 2687, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.52, #queue-req: 0, 
[2025-07-28 01:02:51 DP1 TP0] Decode batch. #running-req: 2, #token: 2767, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.22, #queue-req: 0, 
[2025-07-28 01:02:51 DP1 TP0] Decode batch. #running-req: 2, #token: 2847, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.63, #queue-req: 0, 
[2025-07-28 01:02:51 DP1 TP0] Decode batch. #running-req: 2, #token: 2927, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.35, #queue-req: 0, 
[2025-07-28 01:02:52 DP1 TP0] Decode batch. #running-req: 2, #token: 3007, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.03, #queue-req: 0, 
[2025-07-28 01:02:52 DP1 TP0] Decode batch. #running-req: 2, #token: 3087, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.91, #queue-req: 0, 
[2025-07-28 01:02:52 DP1 TP0] Decode batch. #running-req: 2, #token: 3167, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.11, #queue-req: 0, 
[2025-07-28 01:02:52 DP1 TP0] Decode batch. #running-req: 2, #token: 3247, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.02, #queue-req: 0, 
[2025-07-28 01:02:52 DP1 TP0] Decode batch. #running-req: 2, #token: 3327, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.57, #queue-req: 0, 
[2025-07-28 01:02:52 DP1 TP0] Decode batch. #running-req: 2, #token: 3407, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.33, #queue-req: 0, 
[2025-07-28 01:02:53 DP1 TP0] Decode batch. #running-req: 2, #token: 3487, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.39, #queue-req: 0, 
[2025-07-28 01:02:53 DP1 TP0] Decode batch. #running-req: 2, #token: 3567, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.20, #queue-req: 0, 
[2025-07-28 01:02:53 DP1 TP0] Decode batch. #running-req: 2, #token: 3647, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.01, #queue-req: 0, 
[2025-07-28 01:02:53 DP1 TP0] Decode batch. #running-req: 2, #token: 3727, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.62, #queue-req: 0, 
[2025-07-28 01:02:53 DP1 TP0] Decode batch. #running-req: 2, #token: 3807, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.73, #queue-req: 0, 
[2025-07-28 01:02:53 DP1 TP0] Decode batch. #running-req: 2, #token: 3887, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.48, #queue-req: 0, 
[2025-07-28 01:02:54 DP1 TP0] Decode batch. #running-req: 2, #token: 3967, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.15, #queue-req: 0, 
[2025-07-28 01:02:54 DP1 TP0] Decode batch. #running-req: 2, #token: 4047, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.27, #queue-req: 0, 
[2025-07-28 01:02:54 DP1 TP0] Decode batch. #running-req: 2, #token: 4127, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.79, #queue-req: 0, 
[2025-07-28 01:02:54 DP1 TP0] Decode batch. #running-req: 2, #token: 4207, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.78, #queue-req: 0, 
[2025-07-28 01:02:54 DP1 TP0] Decode batch. #running-req: 2, #token: 4287, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.19, #queue-req: 0, 
[2025-07-28 01:02:54 DP1 TP0] Decode batch. #running-req: 2, #token: 4367, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.55, #queue-req: 0, 
[2025-07-28 01:02:55 DP1 TP0] Decode batch. #running-req: 2, #token: 4447, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.49, #queue-req: 0, 
[2025-07-28 01:02:55 DP1 TP0] Decode batch. #running-req: 2, #token: 4527, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.40, #queue-req: 0, 
[2025-07-28 01:02:55 DP1 TP0] Decode batch. #running-req: 2, #token: 4607, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.17, #queue-req: 0, 
[2025-07-28 01:02:55 DP1 TP0] Decode batch. #running-req: 2, #token: 4687, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.84, #queue-req: 0, 
[2025-07-28 01:02:55 DP1 TP0] Decode batch. #running-req: 2, #token: 4767, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.06, #queue-req: 0, 
[2025-07-28 01:02:56 DP1 TP0] Decode batch. #running-req: 2, #token: 4847, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.77, #queue-req: 0, 
[2025-07-28 01:02:56 DP1 TP0] Decode batch. #running-req: 2, #token: 4927, token usage: 0.00, cuda graph: True, gen throughput (token/s): 459.44, #queue-req: 0, 
[2025-07-28 01:02:56 DP1 TP0] Decode batch. #running-req: 2, #token: 5007, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.79, #queue-req: 0, 
[2025-07-28 01:02:56 DP1 TP0] Decode batch. #running-req: 2, #token: 5087, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.80, #queue-req: 0, 
[2025-07-28 01:02:56 DP1 TP0] Decode batch. #running-req: 2, #token: 5167, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.37, #queue-req: 0, 
[2025-07-28 01:02:56 DP1 TP0] Decode batch. #running-req: 2, #token: 5247, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.41, #queue-req: 0, 
[2025-07-28 01:02:57 DP1 TP0] Decode batch. #running-req: 2, #token: 5327, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.53, #queue-req: 0, 
[2025-07-28 01:02:57 DP1 TP0] Decode batch. #running-req: 2, #token: 5407, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.26, #queue-req: 0, 
[2025-07-28 01:02:57 DP1 TP0] Decode batch. #running-req: 2, #token: 5487, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.84, #queue-req: 0, 
[2025-07-28 01:02:57 DP1 TP0] Decode batch. #running-req: 2, #token: 5567, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.31, #queue-req: 0, 
[2025-07-28 01:02:57 DP1 TP0] Decode batch. #running-req: 2, #token: 5647, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.96, #queue-req: 0, 
[2025-07-28 01:02:57 DP1 TP0] Decode batch. #running-req: 2, #token: 5727, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.54, #queue-req: 0, 
[2025-07-28 01:02:58 DP1 TP0] Decode batch. #running-req: 2, #token: 5807, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.51, #queue-req: 0, 
[2025-07-28 01:02:58 DP1 TP0] Decode batch. #running-req: 2, #token: 5887, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.94, #queue-req: 0, 
[2025-07-28 01:02:58 DP1 TP0] Decode batch. #running-req: 2, #token: 5967, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.41, #queue-req: 0, 
[2025-07-28 01:02:58 DP1 TP0] Decode batch. #running-req: 2, #token: 6047, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.68, #queue-req: 0, 
[2025-07-28 01:02:58 DP1 TP0] Decode batch. #running-req: 2, #token: 6127, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.71, #queue-req: 0, 
[2025-07-28 01:02:58 DP1 TP0] Decode batch. #running-req: 2, #token: 6207, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.84, #queue-req: 0, 
[2025-07-28 01:02:59 DP1 TP0] Decode batch. #running-req: 2, #token: 6287, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.76, #queue-req: 0, 
[2025-07-28 01:02:59 DP1 TP0] Decode batch. #running-req: 2, #token: 6367, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.13, #queue-req: 0, 
[2025-07-28 01:02:59 DP1 TP0] Decode batch. #running-req: 2, #token: 6447, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.96, #queue-req: 0, 
[2025-07-28 01:02:59 DP1 TP0] Decode batch. #running-req: 2, #token: 6527, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.02, #queue-req: 0, 
[2025-07-28 01:02:59 DP1 TP0] Decode batch. #running-req: 2, #token: 6607, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.04, #queue-req: 0, 
[2025-07-28 01:03:00 DP1 TP0] Decode batch. #running-req: 2, #token: 6687, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.17, #queue-req: 0, 
[2025-07-28 01:03:00 DP1 TP0] Decode batch. #running-req: 2, #token: 6767, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.62, #queue-req: 0, 
[2025-07-28 01:03:00 DP1 TP0] Decode batch. #running-req: 2, #token: 6847, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.15, #queue-req: 0, 
[2025-07-28 01:03:00 DP1 TP0] Decode batch. #running-req: 2, #token: 6927, token usage: 0.00, cuda graph: True, gen throughput (token/s): 459.09, #queue-req: 0, 
[2025-07-28 01:03:00 DP1 TP0] Decode batch. #running-req: 2, #token: 7007, token usage: 0.00, cuda graph: True, gen throughput (token/s): 459.70, #queue-req: 0, 
[2025-07-28 01:03:00 DP1 TP0] Decode batch. #running-req: 2, #token: 7087, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.59, #queue-req: 0, 
[2025-07-28 01:03:01 DP1 TP0] Decode batch. #running-req: 2, #token: 7167, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.04, #queue-req: 0, 
[2025-07-28 01:03:01 DP1 TP0] Decode batch. #running-req: 2, #token: 7247, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.43, #queue-req: 0, 
[2025-07-28 01:03:01 DP1 TP0] Decode batch. #running-req: 2, #token: 7327, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.92, #queue-req: 0, 
[2025-07-28 01:03:01 DP1 TP0] Decode batch. #running-req: 2, #token: 7407, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.71, #queue-req: 0, 
[2025-07-28 01:03:01 DP1 TP0] Decode batch. #running-req: 2, #token: 7487, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.38, #queue-req: 0, 
[2025-07-28 01:03:01 DP1 TP0] Decode batch. #running-req: 2, #token: 7567, token usage: 0.00, cuda graph: True, gen throughput (token/s): 459.12, #queue-req: 0, 
[2025-07-28 01:03:02 DP1 TP0] Decode batch. #running-req: 2, #token: 7647, token usage: 0.00, cuda graph: True, gen throughput (token/s): 455.64, #queue-req: 0, 
[2025-07-28 01:03:02 DP1 TP0] Decode batch. #running-req: 2, #token: 7727, token usage: 0.00, cuda graph: True, gen throughput (token/s): 457.53, #queue-req: 0, 
[2025-07-28 01:03:02 DP1 TP0] Decode batch. #running-req: 2, #token: 7807, token usage: 0.00, cuda graph: True, gen throughput (token/s): 457.26, #queue-req: 0, 
[2025-07-28 01:03:02 DP1 TP0] Decode batch. #running-req: 2, #token: 7887, token usage: 0.00, cuda graph: True, gen throughput (token/s): 459.63, #queue-req: 0, 
[2025-07-28 01:03:02 DP1 TP0] Decode batch. #running-req: 2, #token: 7967, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.19, #queue-req: 0, 
[2025-07-28 01:03:02 DP1 TP0] Decode batch. #running-req: 2, #token: 8047, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.16, #queue-req: 0, 
[2025-07-28 01:03:03 DP1 TP0] Decode batch. #running-req: 2, #token: 8127, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.71, #queue-req: 0, 
[2025-07-28 01:03:03 DP1 TP0] Decode batch. #running-req: 2, #token: 8207, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.46, #queue-req: 0, 
[2025-07-28 01:03:03 DP1 TP0] Decode batch. #running-req: 2, #token: 8287, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.07, #queue-req: 0, 
[2025-07-28 01:03:03 DP1 TP0] Decode batch. #running-req: 2, #token: 8367, token usage: 0.00, cuda graph: True, gen throughput (token/s): 459.93, #queue-req: 0, 
[2025-07-28 01:03:03 DP1 TP0] Decode batch. #running-req: 2, #token: 8447, token usage: 0.00, cuda graph: True, gen throughput (token/s): 457.99, #queue-req: 0, 
[2025-07-28 01:03:04 DP1 TP0] Decode batch. #running-req: 2, #token: 8527, token usage: 0.00, cuda graph: True, gen throughput (token/s): 459.32, #queue-req: 0, 
[2025-07-28 01:03:04 DP1 TP0] Decode batch. #running-req: 2, #token: 8607, token usage: 0.00, cuda graph: True, gen throughput (token/s): 459.66, #queue-req: 0, 
[2025-07-28 01:03:04 DP1 TP0] Decode batch. #running-req: 2, #token: 8687, token usage: 0.00, cuda graph: True, gen throughput (token/s): 459.44, #queue-req: 0, 
[2025-07-28 01:03:04 DP1 TP0] Decode batch. #running-req: 2, #token: 8767, token usage: 0.00, cuda graph: True, gen throughput (token/s): 459.52, #queue-req: 0, 
[2025-07-28 01:03:04 DP1 TP0] Decode batch. #running-req: 2, #token: 8847, token usage: 0.00, cuda graph: True, gen throughput (token/s): 457.54, #queue-req: 0, 
[2025-07-28 01:03:04 DP1 TP0] Decode batch. #running-req: 2, #token: 8927, token usage: 0.00, cuda graph: True, gen throughput (token/s): 457.05, #queue-req: 0, 
[2025-07-28 01:03:05 DP1 TP0] Decode batch. #running-req: 2, #token: 9007, token usage: 0.00, cuda graph: True, gen throughput (token/s): 457.80, #queue-req: 0, 
[2025-07-28 01:03:05 DP1 TP0] Decode batch. #running-req: 2, #token: 9087, token usage: 0.00, cuda graph: True, gen throughput (token/s): 457.85, #queue-req: 0, 
[2025-07-28 01:03:05 DP1 TP0] Decode batch. #running-req: 2, #token: 9167, token usage: 0.00, cuda graph: True, gen throughput (token/s): 458.30, #queue-req: 0, 
[2025-07-28 01:03:05 DP1 TP0] Decode batch. #running-req: 2, #token: 9247, token usage: 0.00, cuda graph: True, gen throughput (token/s): 458.31, #queue-req: 0, 
[2025-07-28 01:03:05 DP1 TP0] Decode batch. #running-req: 2, #token: 9327, token usage: 0.00, cuda graph: True, gen throughput (token/s): 458.17, #queue-req: 0, 
[2025-07-28 01:03:05 DP1 TP0] Decode batch. #running-req: 2, #token: 9407, token usage: 0.01, cuda graph: True, gen throughput (token/s): 458.44, #queue-req: 0, 
[2025-07-28 01:03:06 DP1 TP0] Decode batch. #running-req: 2, #token: 9487, token usage: 0.01, cuda graph: True, gen throughput (token/s): 459.16, #queue-req: 0, 
[2025-07-28 01:03:06 DP1 TP0] Decode batch. #running-req: 2, #token: 9567, token usage: 0.01, cuda graph: True, gen throughput (token/s): 459.22, #queue-req: 0, 
[2025-07-28 01:03:06 DP1 TP0] Decode batch. #running-req: 2, #token: 9647, token usage: 0.01, cuda graph: True, gen throughput (token/s): 458.84, #queue-req: 0, 
[2025-07-28 01:03:06 DP1 TP0] Decode batch. #running-req: 2, #token: 9727, token usage: 0.01, cuda graph: True, gen throughput (token/s): 458.01, #queue-req: 0, 
[2025-07-28 01:03:06 DP1 TP0] Decode batch. #running-req: 2, #token: 9807, token usage: 0.01, cuda graph: True, gen throughput (token/s): 456.27, #queue-req: 0, 
[2025-07-28 01:03:06 DP1 TP0] Decode batch. #running-req: 2, #token: 9887, token usage: 0.01, cuda graph: True, gen throughput (token/s): 451.87, #queue-req: 0, 
[2025-07-28 01:03:07 DP1 TP0] Decode batch. #running-req: 2, #token: 9967, token usage: 0.01, cuda graph: True, gen throughput (token/s): 455.54, #queue-req: 0, 
[2025-07-28 01:03:07 DP1 TP0] Decode batch. #running-req: 2, #token: 10047, token usage: 0.01, cuda graph: True, gen throughput (token/s): 456.61, #queue-req: 0, 
[2025-07-28 01:03:07 DP1 TP0] Decode batch. #running-req: 2, #token: 10127, token usage: 0.01, cuda graph: True, gen throughput (token/s): 456.92, #queue-req: 0, 
[2025-07-28 01:03:07 DP1 TP0] Decode batch. #running-req: 2, #token: 10207, token usage: 0.01, cuda graph: True, gen throughput (token/s): 457.55, #queue-req: 0, 
[2025-07-28 01:03:07 DP1 TP0] Decode batch. #running-req: 2, #token: 10287, token usage: 0.01, cuda graph: True, gen throughput (token/s): 457.27, #queue-req: 0, 
[2025-07-28 01:03:08 DP1 TP0] Decode batch. #running-req: 2, #token: 10367, token usage: 0.01, cuda graph: True, gen throughput (token/s): 456.67, #queue-req: 0, 
[2025-07-28 01:03:08 DP1 TP0] Decode batch. #running-req: 2, #token: 10447, token usage: 0.01, cuda graph: True, gen throughput (token/s): 456.64, #queue-req: 0, 
[2025-07-28 01:03:08 DP1 TP0] Decode batch. #running-req: 2, #token: 10527, token usage: 0.01, cuda graph: True, gen throughput (token/s): 456.50, #queue-req: 0, 
[2025-07-28 01:03:08 DP1 TP0] Decode batch. #running-req: 2, #token: 10607, token usage: 0.01, cuda graph: True, gen throughput (token/s): 455.05, #queue-req: 0, 
[2025-07-28 01:03:08 DP1 TP0] Decode batch. #running-req: 2, #token: 10687, token usage: 0.01, cuda graph: True, gen throughput (token/s): 456.09, #queue-req: 0, 
[2025-07-28 01:03:08 DP1 TP0] Decode batch. #running-req: 2, #token: 10767, token usage: 0.01, cuda graph: True, gen throughput (token/s): 455.99, #queue-req: 0, 
[2025-07-28 01:03:09 DP1 TP0] Decode batch. #running-req: 2, #token: 10847, token usage: 0.01, cuda graph: True, gen throughput (token/s): 455.96, #queue-req: 0, 
[2025-07-28 01:03:09 DP1 TP0] Decode batch. #running-req: 2, #token: 10927, token usage: 0.01, cuda graph: True, gen throughput (token/s): 456.33, #queue-req: 0, 
[2025-07-28 01:03:09 DP1 TP0] Decode batch. #running-req: 2, #token: 11007, token usage: 0.01, cuda graph: True, gen throughput (token/s): 455.78, #queue-req: 0, 
[2025-07-28 01:03:09 DP1 TP0] Decode batch. #running-req: 2, #token: 11087, token usage: 0.01, cuda graph: True, gen throughput (token/s): 454.75, #queue-req: 0, 
[2025-07-28 01:03:09 DP1 TP0] Decode batch. #running-req: 2, #token: 11167, token usage: 0.01, cuda graph: True, gen throughput (token/s): 455.73, #queue-req: 0, 
[2025-07-28 01:03:09 DP1 TP0] Decode batch. #running-req: 2, #token: 11247, token usage: 0.01, cuda graph: True, gen throughput (token/s): 451.49, #queue-req: 0, 
[2025-07-28 01:03:10 DP1 TP0] Decode batch. #running-req: 2, #token: 11327, token usage: 0.01, cuda graph: True, gen throughput (token/s): 456.02, #queue-req: 0, 
[2025-07-28 01:03:10 DP1 TP0] Decode batch. #running-req: 2, #token: 11407, token usage: 0.01, cuda graph: True, gen throughput (token/s): 455.77, #queue-req: 0, 
[2025-07-28 01:03:10 DP1 TP0] Decode batch. #running-req: 2, #token: 11487, token usage: 0.01, cuda graph: True, gen throughput (token/s): 456.35, #queue-req: 0, 
[2025-07-28 01:03:10 DP1 TP0] Decode batch. #running-req: 2, #token: 11567, token usage: 0.01, cuda graph: True, gen throughput (token/s): 456.38, #queue-req: 0, 
[2025-07-28 01:03:10 DP1 TP0] Decode batch. #running-req: 2, #token: 11647, token usage: 0.01, cuda graph: True, gen throughput (token/s): 456.97, #queue-req: 0, 
[2025-07-28 01:03:11 DP1 TP0] Decode batch. #running-req: 2, #token: 11727, token usage: 0.01, cuda graph: True, gen throughput (token/s): 457.25, #queue-req: 0, 
[2025-07-28 01:03:11 DP1 TP0] Decode batch. #running-req: 2, #token: 11807, token usage: 0.01, cuda graph: True, gen throughput (token/s): 456.07, #queue-req: 0, 
[2025-07-28 01:03:11 DP1 TP0] Decode batch. #running-req: 2, #token: 11887, token usage: 0.01, cuda graph: True, gen throughput (token/s): 455.67, #queue-req: 0, 
[2025-07-28 01:03:11 DP1 TP0] Decode batch. #running-req: 2, #token: 11967, token usage: 0.01, cuda graph: True, gen throughput (token/s): 456.69, #queue-req: 0, 
[2025-07-28 01:03:11 DP1 TP0] Decode batch. #running-req: 2, #token: 12047, token usage: 0.01, cuda graph: True, gen throughput (token/s): 456.44, #queue-req: 0, 
[2025-07-28 01:03:11 DP1 TP0] Decode batch. #running-req: 2, #token: 12127, token usage: 0.01, cuda graph: True, gen throughput (token/s): 456.20, #queue-req: 0, 
[2025-07-28 01:03:12 DP1 TP0] Decode batch. #running-req: 2, #token: 12207, token usage: 0.01, cuda graph: True, gen throughput (token/s): 455.81, #queue-req: 0, 
[2025-07-28 01:03:12 DP1 TP0] Decode batch. #running-req: 2, #token: 12287, token usage: 0.01, cuda graph: True, gen throughput (token/s): 457.13, #queue-req: 0, 
[2025-07-28 01:03:12 DP1 TP0] Decode batch. #running-req: 2, #token: 12367, token usage: 0.01, cuda graph: True, gen throughput (token/s): 455.90, #queue-req: 0, 
[2025-07-28 01:03:12 DP1 TP0] Decode batch. #running-req: 2, #token: 12447, token usage: 0.01, cuda graph: True, gen throughput (token/s): 456.31, #queue-req: 0, 
[2025-07-28 01:03:12 DP1 TP0] Decode batch. #running-req: 2, #token: 12527, token usage: 0.01, cuda graph: True, gen throughput (token/s): 455.70, #queue-req: 0, 
[2025-07-28 01:03:12 DP1 TP0] Decode batch. #running-req: 2, #token: 12607, token usage: 0.01, cuda graph: True, gen throughput (token/s): 455.78, #queue-req: 0, 
[2025-07-28 01:03:13 DP1 TP0] Decode batch. #running-req: 2, #token: 12687, token usage: 0.01, cuda graph: True, gen throughput (token/s): 456.08, #queue-req: 0, 
[2025-07-28 01:03:13 DP1 TP0] Decode batch. #running-req: 2, #token: 12767, token usage: 0.01, cuda graph: True, gen throughput (token/s): 455.50, #queue-req: 0, 
[2025-07-28 01:03:13 DP1 TP0] Decode batch. #running-req: 2, #token: 12847, token usage: 0.01, cuda graph: True, gen throughput (token/s): 455.89, #queue-req: 0, 
[2025-07-28 01:03:13 DP1 TP0] Decode batch. #running-req: 2, #token: 12927, token usage: 0.01, cuda graph: True, gen throughput (token/s): 455.98, #queue-req: 0, 
[2025-07-28 01:03:13 DP1 TP0] Decode batch. #running-req: 2, #token: 13007, token usage: 0.01, cuda graph: True, gen throughput (token/s): 455.61, #queue-req: 0, 
[2025-07-28 01:03:13 DP1 TP0] Decode batch. #running-req: 2, #token: 13087, token usage: 0.01, cuda graph: True, gen throughput (token/s): 455.80, #queue-req: 0, 
[2025-07-28 01:03:14 DP1 TP0] Decode batch. #running-req: 2, #token: 13167, token usage: 0.01, cuda graph: True, gen throughput (token/s): 455.56, #queue-req: 0, 
[2025-07-28 01:03:14 DP1 TP0] Decode batch. #running-req: 2, #token: 13247, token usage: 0.01, cuda graph: True, gen throughput (token/s): 455.51, #queue-req: 0, 
[2025-07-28 01:03:14 DP1 TP0] Decode batch. #running-req: 2, #token: 13327, token usage: 0.01, cuda graph: True, gen throughput (token/s): 455.31, #queue-req: 0, 
[2025-07-28 01:03:14 DP1 TP0] Decode batch. #running-req: 2, #token: 13407, token usage: 0.01, cuda graph: True, gen throughput (token/s): 455.87, #queue-req: 0, 
[2025-07-28 01:03:14 DP1 TP0] Decode batch. #running-req: 2, #token: 13487, token usage: 0.01, cuda graph: True, gen throughput (token/s): 447.31, #queue-req: 0, 
[2025-07-28 01:03:15 DP1 TP0] Decode batch. #running-req: 2, #token: 13567, token usage: 0.01, cuda graph: True, gen throughput (token/s): 454.53, #queue-req: 0, 
[2025-07-28 01:03:15 DP1 TP0] Decode batch. #running-req: 2, #token: 13647, token usage: 0.01, cuda graph: True, gen throughput (token/s): 454.58, #queue-req: 0, 
[2025-07-28 01:03:15 DP1 TP0] Decode batch. #running-req: 2, #token: 13727, token usage: 0.01, cuda graph: True, gen throughput (token/s): 454.49, #queue-req: 0, 
[2025-07-28 01:03:15 DP1 TP0] Decode batch. #running-req: 2, #token: 13807, token usage: 0.01, cuda graph: True, gen throughput (token/s): 454.54, #queue-req: 0, 
[2025-07-28 01:03:15 DP1 TP0] Decode batch. #running-req: 2, #token: 13887, token usage: 0.01, cuda graph: True, gen throughput (token/s): 454.21, #queue-req: 0, 
[2025-07-28 01:03:15 DP1 TP0] Decode batch. #running-req: 2, #token: 13967, token usage: 0.01, cuda graph: True, gen throughput (token/s): 454.15, #queue-req: 0, 
[2025-07-28 01:03:16 DP1 TP0] Decode batch. #running-req: 2, #token: 14047, token usage: 0.01, cuda graph: True, gen throughput (token/s): 454.43, #queue-req: 0, 
[2025-07-28 01:03:16 DP1 TP0] Decode batch. #running-req: 2, #token: 14127, token usage: 0.01, cuda graph: True, gen throughput (token/s): 453.61, #queue-req: 0, 
[2025-07-28 01:03:16 DP1 TP0] Decode batch. #running-req: 2, #token: 14207, token usage: 0.01, cuda graph: True, gen throughput (token/s): 454.13, #queue-req: 0, 
[2025-07-28 01:03:16 DP1 TP0] Decode batch. #running-req: 2, #token: 14287, token usage: 0.01, cuda graph: True, gen throughput (token/s): 454.60, #queue-req: 0, 
[2025-07-28 01:03:16 DP1 TP0] Decode batch. #running-req: 2, #token: 14367, token usage: 0.01, cuda graph: True, gen throughput (token/s): 454.61, #queue-req: 0, 
[2025-07-28 01:03:16 DP1 TP0] Decode batch. #running-req: 2, #token: 14447, token usage: 0.01, cuda graph: True, gen throughput (token/s): 454.65, #queue-req: 0, 
[2025-07-28 01:03:17 DP1 TP0] Decode batch. #running-req: 2, #token: 14527, token usage: 0.01, cuda graph: True, gen throughput (token/s): 453.56, #queue-req: 0, 
[2025-07-28 01:03:17 DP1 TP0] Decode batch. #running-req: 2, #token: 14607, token usage: 0.01, cuda graph: True, gen throughput (token/s): 451.61, #queue-req: 0, 
[2025-07-28 01:03:17 DP1 TP0] Decode batch. #running-req: 2, #token: 14687, token usage: 0.01, cuda graph: True, gen throughput (token/s): 453.39, #queue-req: 0, 
[2025-07-28 01:03:17 DP1 TP0] Decode batch. #running-req: 2, #token: 14767, token usage: 0.01, cuda graph: True, gen throughput (token/s): 453.37, #queue-req: 0, 
[2025-07-28 01:03:17 DP1 TP0] Decode batch. #running-req: 2, #token: 14847, token usage: 0.01, cuda graph: True, gen throughput (token/s): 453.13, #queue-req: 0, 
[2025-07-28 01:03:18] INFO:     127.0.0.1:41992 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:03:18 DP1 TP0] Decode batch. #running-req: 1, #token: 6590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 399.75, #queue-req: 0, 
[2025-07-28 01:03:18 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 167, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:03:18 DP1 TP0] Decode batch. #running-req: 1, #token: 6630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.62, #queue-req: 0, 
[2025-07-28 01:03:18 DP0 TP0] Decode batch. #running-req: 1, #token: 277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 1.44, #queue-req: 0, 
[2025-07-28 01:03:18 DP1 TP0] Decode batch. #running-req: 1, #token: 6670, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.56, #queue-req: 0, 
[2025-07-28 01:03:18 DP0 TP0] Decode batch. #running-req: 1, #token: 317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 222.09, #queue-req: 0, 
[2025-07-28 01:03:18 DP1 TP0] Decode batch. #running-req: 1, #token: 6710, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.14, #queue-req: 0, 
[2025-07-28 01:03:18 DP0 TP0] Decode batch. #running-req: 1, #token: 357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.44, #queue-req: 0, 
[2025-07-28 01:03:18 DP1 TP0] Decode batch. #running-req: 1, #token: 6750, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.89, #queue-req: 0, 
[2025-07-28 01:03:18 DP0 TP0] Decode batch. #running-req: 1, #token: 397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.74, #queue-req: 0, 
[2025-07-28 01:03:18 DP1 TP0] Decode batch. #running-req: 1, #token: 6790, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.84, #queue-req: 0, 
[2025-07-28 01:03:18 DP0 TP0] Decode batch. #running-req: 1, #token: 437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.15, #queue-req: 0, 
[2025-07-28 01:03:19 DP1 TP0] Decode batch. #running-req: 1, #token: 6830, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.73, #queue-req: 0, 
[2025-07-28 01:03:19 DP0 TP0] Decode batch. #running-req: 1, #token: 477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.09, #queue-req: 0, 
[2025-07-28 01:03:19 DP1 TP0] Decode batch. #running-req: 1, #token: 6870, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.48, #queue-req: 0, 
[2025-07-28 01:03:19 DP0 TP0] Decode batch. #running-req: 1, #token: 517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.23, #queue-req: 0, 
[2025-07-28 01:03:19 DP1 TP0] Decode batch. #running-req: 1, #token: 6910, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.46, #queue-req: 0, 
[2025-07-28 01:03:19 DP0 TP0] Decode batch. #running-req: 1, #token: 557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.74, #queue-req: 0, 
[2025-07-28 01:03:19 DP1 TP0] Decode batch. #running-req: 1, #token: 6950, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.83, #queue-req: 0, 
[2025-07-28 01:03:19 DP0 TP0] Decode batch. #running-req: 1, #token: 597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.89, #queue-req: 0, 
[2025-07-28 01:03:19 DP1 TP0] Decode batch. #running-req: 1, #token: 6990, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.08, #queue-req: 0, 
[2025-07-28 01:03:19 DP0 TP0] Decode batch. #running-req: 1, #token: 637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.90, #queue-req: 0, 
[2025-07-28 01:03:19 DP1 TP0] Decode batch. #running-req: 1, #token: 7030, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.85, #queue-req: 0, 
[2025-07-28 01:03:19 DP0 TP0] Decode batch. #running-req: 1, #token: 677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.03, #queue-req: 0, 
[2025-07-28 01:03:20 DP1 TP0] Decode batch. #running-req: 1, #token: 7070, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.00, #queue-req: 0, 
[2025-07-28 01:03:20 DP0 TP0] Decode batch. #running-req: 1, #token: 717, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.86, #queue-req: 0, 
[2025-07-28 01:03:20 DP1 TP0] Decode batch. #running-req: 1, #token: 7110, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.87, #queue-req: 0, 
[2025-07-28 01:03:20 DP0 TP0] Decode batch. #running-req: 1, #token: 757, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.96, #queue-req: 0, 
[2025-07-28 01:03:20 DP1 TP0] Decode batch. #running-req: 1, #token: 7150, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.16, #queue-req: 0, 
[2025-07-28 01:03:20 DP0 TP0] Decode batch. #running-req: 1, #token: 797, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.61, #queue-req: 0, 
[2025-07-28 01:03:20 DP1 TP0] Decode batch. #running-req: 1, #token: 7190, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.86, #queue-req: 0, 
[2025-07-28 01:03:20 DP0 TP0] Decode batch. #running-req: 1, #token: 837, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.33, #queue-req: 0, 
[2025-07-28 01:03:20 DP1 TP0] Decode batch. #running-req: 1, #token: 7230, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.39, #queue-req: 0, 
[2025-07-28 01:03:20 DP0 TP0] Decode batch. #running-req: 1, #token: 877, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.19, #queue-req: 0, 
[2025-07-28 01:03:20 DP1 TP0] Decode batch. #running-req: 1, #token: 7270, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.35, #queue-req: 0, 
[2025-07-28 01:03:20 DP0 TP0] Decode batch. #running-req: 1, #token: 917, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.65, #queue-req: 0, 
[2025-07-28 01:03:21 DP1 TP0] Decode batch. #running-req: 1, #token: 7310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.54, #queue-req: 0, 
[2025-07-28 01:03:21 DP0 TP0] Decode batch. #running-req: 1, #token: 957, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.73, #queue-req: 0, 
[2025-07-28 01:03:21 DP0 TP0] Decode batch. #running-req: 1, #token: 997, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.85, #queue-req: 0, 
[2025-07-28 01:03:21 DP1 TP0] Decode batch. #running-req: 1, #token: 7350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.74, #queue-req: 0, 
[2025-07-28 01:03:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1037, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.18, #queue-req: 0, 
[2025-07-28 01:03:21 DP1 TP0] Decode batch. #running-req: 1, #token: 7390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.18, #queue-req: 0, 
[2025-07-28 01:03:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1077, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.39, #queue-req: 0, 
[2025-07-28 01:03:21 DP1 TP0] Decode batch. #running-req: 1, #token: 7430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.61, #queue-req: 0, 
[2025-07-28 01:03:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1117, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.53, #queue-req: 0, 
[2025-07-28 01:03:21 DP1 TP0] Decode batch. #running-req: 1, #token: 7470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.30, #queue-req: 0, 
[2025-07-28 01:03:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1157, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.64, #queue-req: 0, 
[2025-07-28 01:03:21 DP1 TP0] Decode batch. #running-req: 1, #token: 7510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.38, #queue-req: 0, 
[2025-07-28 01:03:22 DP0 TP0] Decode batch. #running-req: 1, #token: 1197, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.92, #queue-req: 0, 
[2025-07-28 01:03:22 DP1 TP0] Decode batch. #running-req: 1, #token: 7550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.37, #queue-req: 0, 
[2025-07-28 01:03:22 DP0 TP0] Decode batch. #running-req: 1, #token: 1237, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.05, #queue-req: 0, 
[2025-07-28 01:03:22 DP1 TP0] Decode batch. #running-req: 1, #token: 7590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.13, #queue-req: 0, 
[2025-07-28 01:03:22] INFO:     127.0.0.1:38984 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:03:22 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 172, #cached-token: 89, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:03:22 DP1 TP0] Decode batch. #running-req: 2, #token: 7819, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.36, #queue-req: 0, 
[2025-07-28 01:03:22 DP1 TP0] Decode batch. #running-req: 2, #token: 7899, token usage: 0.00, cuda graph: True, gen throughput (token/s): 457.68, #queue-req: 0, 
[2025-07-28 01:03:22 DP1 TP0] Decode batch. #running-req: 2, #token: 7979, token usage: 0.00, cuda graph: True, gen throughput (token/s): 456.44, #queue-req: 0, 
[2025-07-28 01:03:22 DP1 TP0] Decode batch. #running-req: 2, #token: 8059, token usage: 0.00, cuda graph: True, gen throughput (token/s): 456.20, #queue-req: 0, 
[2025-07-28 01:03:23 DP1 TP0] Decode batch. #running-req: 2, #token: 8139, token usage: 0.00, cuda graph: True, gen throughput (token/s): 457.17, #queue-req: 0, 
[2025-07-28 01:03:23 DP1 TP0] Decode batch. #running-req: 2, #token: 8219, token usage: 0.00, cuda graph: True, gen throughput (token/s): 456.65, #queue-req: 0, 
[2025-07-28 01:03:23 DP1 TP0] Decode batch. #running-req: 2, #token: 8299, token usage: 0.00, cuda graph: True, gen throughput (token/s): 456.46, #queue-req: 0, 
[2025-07-28 01:03:23 DP1 TP0] Decode batch. #running-req: 2, #token: 8379, token usage: 0.00, cuda graph: True, gen throughput (token/s): 455.03, #queue-req: 0, 
[2025-07-28 01:03:23 DP1 TP0] Decode batch. #running-req: 2, #token: 8459, token usage: 0.00, cuda graph: True, gen throughput (token/s): 456.26, #queue-req: 0, 
[2025-07-28 01:03:24 DP1 TP0] Decode batch. #running-req: 2, #token: 8539, token usage: 0.00, cuda graph: True, gen throughput (token/s): 456.60, #queue-req: 0, 
[2025-07-28 01:03:24 DP1 TP0] Decode batch. #running-req: 2, #token: 8619, token usage: 0.00, cuda graph: True, gen throughput (token/s): 455.53, #queue-req: 0, 
[2025-07-28 01:03:24 DP1 TP0] Decode batch. #running-req: 2, #token: 8699, token usage: 0.00, cuda graph: True, gen throughput (token/s): 457.16, #queue-req: 0, 
[2025-07-28 01:03:24 DP1 TP0] Decode batch. #running-req: 2, #token: 8779, token usage: 0.00, cuda graph: True, gen throughput (token/s): 455.39, #queue-req: 0, 
[2025-07-28 01:03:24 DP1 TP0] Decode batch. #running-req: 2, #token: 8859, token usage: 0.00, cuda graph: True, gen throughput (token/s): 456.10, #queue-req: 0, 
[2025-07-28 01:03:24 DP1 TP0] Decode batch. #running-req: 2, #token: 8939, token usage: 0.00, cuda graph: True, gen throughput (token/s): 455.85, #queue-req: 0, 
[2025-07-28 01:03:25 DP1 TP0] Decode batch. #running-req: 2, #token: 9019, token usage: 0.00, cuda graph: True, gen throughput (token/s): 454.52, #queue-req: 0, 
[2025-07-28 01:03:25 DP1 TP0] Decode batch. #running-req: 2, #token: 9099, token usage: 0.00, cuda graph: True, gen throughput (token/s): 455.21, #queue-req: 0, 
[2025-07-28 01:03:25 DP1 TP0] Decode batch. #running-req: 2, #token: 9179, token usage: 0.00, cuda graph: True, gen throughput (token/s): 455.48, #queue-req: 0, 
[2025-07-28 01:03:25 DP1 TP0] Decode batch. #running-req: 2, #token: 9259, token usage: 0.00, cuda graph: True, gen throughput (token/s): 455.43, #queue-req: 0, 
[2025-07-28 01:03:25 DP1 TP0] Decode batch. #running-req: 2, #token: 9339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 455.24, #queue-req: 0, 
[2025-07-28 01:03:25 DP1 TP0] Decode batch. #running-req: 2, #token: 9419, token usage: 0.01, cuda graph: True, gen throughput (token/s): 455.32, #queue-req: 0, 
[2025-07-28 01:03:26 DP1 TP0] Decode batch. #running-req: 2, #token: 9499, token usage: 0.01, cuda graph: True, gen throughput (token/s): 453.11, #queue-req: 0, 
[2025-07-28 01:03:26] INFO:     127.0.0.1:42016 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:03:26 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 219, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:03:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1158, token usage: 0.00, cuda graph: True, gen throughput (token/s): 264.15, #queue-req: 0, 
[2025-07-28 01:03:26 DP0 TP0] Decode batch. #running-req: 1, #token: 321, token usage: 0.00, cuda graph: True, gen throughput (token/s): 9.70, #queue-req: 0, 
[2025-07-28 01:03:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1198, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.13, #queue-req: 0, 
[2025-07-28 01:03:26 DP0 TP0] Decode batch. #running-req: 1, #token: 361, token usage: 0.00, cuda graph: True, gen throughput (token/s): 221.79, #queue-req: 0, 
[2025-07-28 01:03:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1238, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.44, #queue-req: 0, 
[2025-07-28 01:03:26 DP0 TP0] Decode batch. #running-req: 1, #token: 401, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.43, #queue-req: 0, 
[2025-07-28 01:03:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1278, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.36, #queue-req: 0, 
[2025-07-28 01:03:26 DP0 TP0] Decode batch. #running-req: 1, #token: 441, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.46, #queue-req: 0, 
[2025-07-28 01:03:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1318, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.04, #queue-req: 0, 
[2025-07-28 01:03:27 DP0 TP0] Decode batch. #running-req: 1, #token: 481, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.82, #queue-req: 0, 
[2025-07-28 01:03:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1358, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.19, #queue-req: 0, 
[2025-07-28 01:03:27 DP0 TP0] Decode batch. #running-req: 1, #token: 521, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.31, #queue-req: 0, 
[2025-07-28 01:03:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1398, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.08, #queue-req: 0, 
[2025-07-28 01:03:27 DP0 TP0] Decode batch. #running-req: 1, #token: 561, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.30, #queue-req: 0, 
[2025-07-28 01:03:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1438, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.30, #queue-req: 0, 
[2025-07-28 01:03:27 DP0 TP0] Decode batch. #running-req: 1, #token: 601, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.63, #queue-req: 0, 
[2025-07-28 01:03:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1478, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.56, #queue-req: 0, 
[2025-07-28 01:03:27] INFO:     127.0.0.1:44648 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:03:27 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 155, #cached-token: 90, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:03:27 DP0 TP0] Decode batch. #running-req: 1, #token: 641, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.47, #queue-req: 0, 
[2025-07-28 01:03:27 DP0 TP0] Decode batch. #running-req: 1, #token: 681, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.58, #queue-req: 0, 
[2025-07-28 01:03:27 DP1 TP0] Decode batch. #running-req: 1, #token: 282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 175.60, #queue-req: 0, 
[2025-07-28 01:03:28 DP0 TP0] Decode batch. #running-req: 1, #token: 721, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.76, #queue-req: 0, 
[2025-07-28 01:03:28 DP1 TP0] Decode batch. #running-req: 1, #token: 322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.96, #queue-req: 0, 
[2025-07-28 01:03:28 DP0 TP0] Decode batch. #running-req: 1, #token: 761, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.95, #queue-req: 0, 
[2025-07-28 01:03:28 DP1 TP0] Decode batch. #running-req: 1, #token: 362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.89, #queue-req: 0, 
[2025-07-28 01:03:28 DP0 TP0] Decode batch. #running-req: 1, #token: 801, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.33, #queue-req: 0, 
[2025-07-28 01:03:28 DP1 TP0] Decode batch. #running-req: 1, #token: 402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.89, #queue-req: 0, 
[2025-07-28 01:03:28 DP0 TP0] Decode batch. #running-req: 1, #token: 841, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.09, #queue-req: 0, 
[2025-07-28 01:03:28 DP1 TP0] Decode batch. #running-req: 1, #token: 442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.99, #queue-req: 0, 
[2025-07-28 01:03:28 DP0 TP0] Decode batch. #running-req: 1, #token: 881, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.23, #queue-req: 0, 
[2025-07-28 01:03:28 DP1 TP0] Decode batch. #running-req: 1, #token: 482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.07, #queue-req: 0, 
[2025-07-28 01:03:28 DP0 TP0] Decode batch. #running-req: 1, #token: 921, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.87, #queue-req: 0, 
[2025-07-28 01:03:28 DP1 TP0] Decode batch. #running-req: 1, #token: 522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.06, #queue-req: 0, 
[2025-07-28 01:03:28 DP0 TP0] Decode batch. #running-req: 1, #token: 961, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.82, #queue-req: 0, 
[2025-07-28 01:03:29 DP1 TP0] Decode batch. #running-req: 1, #token: 562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.43, #queue-req: 0, 
[2025-07-28 01:03:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1001, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.88, #queue-req: 0, 
[2025-07-28 01:03:29 DP1 TP0] Decode batch. #running-req: 1, #token: 602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.46, #queue-req: 0, 
[2025-07-28 01:03:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1041, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.54, #queue-req: 0, 
[2025-07-28 01:03:29 DP1 TP0] Decode batch. #running-req: 1, #token: 642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.43, #queue-req: 0, 
[2025-07-28 01:03:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1081, token usage: 0.00, cuda graph: True, gen throughput (token/s): 229.47, #queue-req: 0, 
[2025-07-28 01:03:29 DP1 TP0] Decode batch. #running-req: 1, #token: 682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.75, #queue-req: 0, 
[2025-07-28 01:03:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1121, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.94, #queue-req: 0, 
[2025-07-28 01:03:29 DP1 TP0] Decode batch. #running-req: 1, #token: 722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.42, #queue-req: 0, 
[2025-07-28 01:03:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1161, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.97, #queue-req: 0, 
[2025-07-28 01:03:29 DP1 TP0] Decode batch. #running-req: 1, #token: 762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.44, #queue-req: 0, 
[2025-07-28 01:03:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1201, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.20, #queue-req: 0, 
[2025-07-28 01:03:30 DP1 TP0] Decode batch. #running-req: 1, #token: 802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.09, #queue-req: 0, 
[2025-07-28 01:03:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1241, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.84, #queue-req: 0, 
[2025-07-28 01:03:30 DP1 TP0] Decode batch. #running-req: 1, #token: 842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.17, #queue-req: 0, 
[2025-07-28 01:03:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1281, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.41, #queue-req: 0, 
[2025-07-28 01:03:30 DP1 TP0] Decode batch. #running-req: 1, #token: 882, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.22, #queue-req: 0, 
[2025-07-28 01:03:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1321, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.02, #queue-req: 0, 
[2025-07-28 01:03:30 DP1 TP0] Decode batch. #running-req: 1, #token: 922, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.09, #queue-req: 0, 
[2025-07-28 01:03:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1361, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.05, #queue-req: 0, 
[2025-07-28 01:03:30 DP1 TP0] Decode batch. #running-req: 1, #token: 962, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.53, #queue-req: 0, 
[2025-07-28 01:03:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1401, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.13, #queue-req: 0, 
[2025-07-28 01:03:30 DP1 TP0] Decode batch. #running-req: 1, #token: 1002, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.50, #queue-req: 0, 
[2025-07-28 01:03:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1441, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.39, #queue-req: 0, 
[2025-07-28 01:03:30 DP1 TP0] Decode batch. #running-req: 1, #token: 1042, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.87, #queue-req: 0, 
[2025-07-28 01:03:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1481, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.45, #queue-req: 0, 
[2025-07-28 01:03:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1082, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.11, #queue-req: 0, 
[2025-07-28 01:03:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1521, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.44, #queue-req: 0, 
[2025-07-28 01:03:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1122, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.32, #queue-req: 0, 
[2025-07-28 01:03:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1561, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.90, #queue-req: 0, 
[2025-07-28 01:03:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1162, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.35, #queue-req: 0, 
[2025-07-28 01:03:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1601, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.36, #queue-req: 0, 
[2025-07-28 01:03:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1202, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.77, #queue-req: 0, 
[2025-07-28 01:03:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1641, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.58, #queue-req: 0, 
[2025-07-28 01:03:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.62, #queue-req: 0, 
[2025-07-28 01:03:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1681, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.96, #queue-req: 0, 
[2025-07-28 01:03:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.93, #queue-req: 0, 
[2025-07-28 01:03:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1721, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.02, #queue-req: 0, 
[2025-07-28 01:03:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.14, #queue-req: 0, 
[2025-07-28 01:03:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1761, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.97, #queue-req: 0, 
[2025-07-28 01:03:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.29, #queue-req: 0, 
[2025-07-28 01:03:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1801, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.93, #queue-req: 0, 
[2025-07-28 01:03:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.22, #queue-req: 0, 
[2025-07-28 01:03:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1841, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.31, #queue-req: 0, 
[2025-07-28 01:03:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.75, #queue-req: 0, 
[2025-07-28 01:03:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1881, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.43, #queue-req: 0, 
[2025-07-28 01:03:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.73, #queue-req: 0, 
[2025-07-28 01:03:32] INFO:     127.0.0.1:44678 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:03:32 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 175, #cached-token: 89, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:03:32 DP0 TP0] Decode batch. #running-req: 2, #token: 2111, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.60, #queue-req: 0, 
[2025-07-28 01:03:33 DP0 TP0] Decode batch. #running-req: 2, #token: 2191, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.36, #queue-req: 0, 
[2025-07-28 01:03:33 DP0 TP0] Decode batch. #running-req: 2, #token: 2271, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.43, #queue-req: 0, 
[2025-07-28 01:03:33 DP0 TP0] Decode batch. #running-req: 2, #token: 2351, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.22, #queue-req: 0, 
[2025-07-28 01:03:33 DP0 TP0] Decode batch. #running-req: 2, #token: 2431, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.96, #queue-req: 0, 
[2025-07-28 01:03:33 DP0 TP0] Decode batch. #running-req: 2, #token: 2511, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.57, #queue-req: 0, 
[2025-07-28 01:03:34 DP0 TP0] Decode batch. #running-req: 2, #token: 2591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.77, #queue-req: 0, 
[2025-07-28 01:03:34 DP0 TP0] Decode batch. #running-req: 2, #token: 2671, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.93, #queue-req: 0, 
[2025-07-28 01:03:34 DP0 TP0] Decode batch. #running-req: 2, #token: 2751, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.38, #queue-req: 0, 
[2025-07-28 01:03:34 DP0 TP0] Decode batch. #running-req: 2, #token: 2831, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.33, #queue-req: 0, 
[2025-07-28 01:03:34 DP0 TP0] Decode batch. #running-req: 2, #token: 2911, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.12, #queue-req: 0, 
[2025-07-28 01:03:34 DP0 TP0] Decode batch. #running-req: 2, #token: 2991, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.18, #queue-req: 0, 
[2025-07-28 01:03:35 DP0 TP0] Decode batch. #running-req: 2, #token: 3071, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.30, #queue-req: 0, 
[2025-07-28 01:03:35 DP0 TP0] Decode batch. #running-req: 2, #token: 3151, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.72, #queue-req: 0, 
[2025-07-28 01:03:35 DP0 TP0] Decode batch. #running-req: 2, #token: 3231, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.54, #queue-req: 0, 
[2025-07-28 01:03:35 DP0 TP0] Decode batch. #running-req: 2, #token: 3311, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.26, #queue-req: 0, 
[2025-07-28 01:03:35 DP0 TP0] Decode batch. #running-req: 2, #token: 3391, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.26, #queue-req: 0, 
[2025-07-28 01:03:35 DP0 TP0] Decode batch. #running-req: 2, #token: 3471, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.16, #queue-req: 0, 
[2025-07-28 01:03:36 DP0 TP0] Decode batch. #running-req: 2, #token: 3551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.17, #queue-req: 0, 
[2025-07-28 01:03:36 DP0 TP0] Decode batch. #running-req: 2, #token: 3631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.33, #queue-req: 0, 
[2025-07-28 01:03:36 DP0 TP0] Decode batch. #running-req: 2, #token: 3711, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.21, #queue-req: 0, 
[2025-07-28 01:03:36 DP0 TP0] Decode batch. #running-req: 2, #token: 3791, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.17, #queue-req: 0, 
[2025-07-28 01:03:36 DP0 TP0] Decode batch. #running-req: 2, #token: 3871, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.32, #queue-req: 0, 
[2025-07-28 01:03:36 DP0 TP0] Decode batch. #running-req: 2, #token: 3951, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.64, #queue-req: 0, 
[2025-07-28 01:03:37 DP0 TP0] Decode batch. #running-req: 2, #token: 4031, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.81, #queue-req: 0, 
[2025-07-28 01:03:37 DP0 TP0] Decode batch. #running-req: 2, #token: 4111, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.69, #queue-req: 0, 
[2025-07-28 01:03:37 DP0 TP0] Decode batch. #running-req: 2, #token: 4191, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.45, #queue-req: 0, 
[2025-07-28 01:03:37 DP0 TP0] Decode batch. #running-req: 2, #token: 4271, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.58, #queue-req: 0, 
[2025-07-28 01:03:37 DP0 TP0] Decode batch. #running-req: 2, #token: 4351, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.97, #queue-req: 0, 
[2025-07-28 01:03:37 DP0 TP0] Decode batch. #running-req: 2, #token: 4431, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.62, #queue-req: 0, 
[2025-07-28 01:03:38 DP0 TP0] Decode batch. #running-req: 2, #token: 4511, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.82, #queue-req: 0, 
[2025-07-28 01:03:38 DP0 TP0] Decode batch. #running-req: 2, #token: 4591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.09, #queue-req: 0, 
[2025-07-28 01:03:38] INFO:     127.0.0.1:44664 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:03:38 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 94, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:03:38 DP0 TP0] Decode batch. #running-req: 1, #token: 1557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.86, #queue-req: 0, 
[2025-07-28 01:03:38 DP1 TP0] Decode batch. #running-req: 1, #token: 201, token usage: 0.00, cuda graph: True, gen throughput (token/s): 6.93, #queue-req: 0, 
[2025-07-28 01:03:38 DP0 TP0] Decode batch. #running-req: 1, #token: 1597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.26, #queue-req: 0, 
[2025-07-28 01:03:38 DP1 TP0] Decode batch. #running-req: 1, #token: 241, token usage: 0.00, cuda graph: True, gen throughput (token/s): 222.44, #queue-req: 0, 
[2025-07-28 01:03:38 DP0 TP0] Decode batch. #running-req: 1, #token: 1637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.31, #queue-req: 0, 
[2025-07-28 01:03:38 DP1 TP0] Decode batch. #running-req: 1, #token: 281, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.75, #queue-req: 0, 
[2025-07-28 01:03:38 DP0 TP0] Decode batch. #running-req: 1, #token: 1677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.52, #queue-req: 0, 
[2025-07-28 01:03:39 DP1 TP0] Decode batch. #running-req: 1, #token: 321, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.53, #queue-req: 0, 
[2025-07-28 01:03:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1717, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.94, #queue-req: 0, 
[2025-07-28 01:03:39 DP1 TP0] Decode batch. #running-req: 1, #token: 361, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.79, #queue-req: 0, 
[2025-07-28 01:03:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1757, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.66, #queue-req: 0, 
[2025-07-28 01:03:39 DP1 TP0] Decode batch. #running-req: 1, #token: 401, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.28, #queue-req: 0, 
[2025-07-28 01:03:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1797, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.36, #queue-req: 0, 
[2025-07-28 01:03:39] INFO:     127.0.0.1:55722 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:03:39 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 115, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:03:39 DP1 TP0] Decode batch. #running-req: 1, #token: 441, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.96, #queue-req: 0, 
[2025-07-28 01:03:39 DP0 TP0] Decode batch. #running-req: 1, #token: 233, token usage: 0.00, cuda graph: True, gen throughput (token/s): 170.91, #queue-req: 0, 
[2025-07-28 01:03:39 DP1 TP0] Decode batch. #running-req: 1, #token: 481, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.79, #queue-req: 0, 
[2025-07-28 01:03:39 DP0 TP0] Decode batch. #running-req: 1, #token: 273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.66, #queue-req: 0, 
[2025-07-28 01:03:39 DP1 TP0] Decode batch. #running-req: 1, #token: 521, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.31, #queue-req: 0, 
[2025-07-28 01:03:40 DP0 TP0] Decode batch. #running-req: 1, #token: 313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.09, #queue-req: 0, 
[2025-07-28 01:03:40 DP1 TP0] Decode batch. #running-req: 1, #token: 561, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.63, #queue-req: 0, 
[2025-07-28 01:03:40 DP0 TP0] Decode batch. #running-req: 1, #token: 353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.12, #queue-req: 0, 
[2025-07-28 01:03:40 DP1 TP0] Decode batch. #running-req: 1, #token: 601, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.86, #queue-req: 0, 
[2025-07-28 01:03:40 DP0 TP0] Decode batch. #running-req: 1, #token: 393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.22, #queue-req: 0, 
[2025-07-28 01:03:40 DP1 TP0] Decode batch. #running-req: 1, #token: 641, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.61, #queue-req: 0, 
[2025-07-28 01:03:40 DP0 TP0] Decode batch. #running-req: 1, #token: 433, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.55, #queue-req: 0, 
[2025-07-28 01:03:40 DP1 TP0] Decode batch. #running-req: 1, #token: 681, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.06, #queue-req: 0, 
[2025-07-28 01:03:40 DP0 TP0] Decode batch. #running-req: 1, #token: 473, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.21, #queue-req: 0, 
[2025-07-28 01:03:40 DP1 TP0] Decode batch. #running-req: 1, #token: 721, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.50, #queue-req: 0, 
[2025-07-28 01:03:40 DP0 TP0] Decode batch. #running-req: 1, #token: 513, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.54, #queue-req: 0, 
[2025-07-28 01:03:40 DP1 TP0] Decode batch. #running-req: 1, #token: 761, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.99, #queue-req: 0, 
[2025-07-28 01:03:41 DP0 TP0] Decode batch. #running-req: 1, #token: 553, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.96, #queue-req: 0, 
[2025-07-28 01:03:41 DP1 TP0] Decode batch. #running-req: 1, #token: 801, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.55, #queue-req: 0, 
[2025-07-28 01:03:41 DP0 TP0] Decode batch. #running-req: 1, #token: 593, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.94, #queue-req: 0, 
[2025-07-28 01:03:41 DP1 TP0] Decode batch. #running-req: 1, #token: 841, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.02, #queue-req: 0, 
[2025-07-28 01:03:41 DP0 TP0] Decode batch. #running-req: 1, #token: 633, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.35, #queue-req: 0, 
[2025-07-28 01:03:41 DP1 TP0] Decode batch. #running-req: 1, #token: 881, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.46, #queue-req: 0, 
[2025-07-28 01:03:41] INFO:     127.0.0.1:55736 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:03:41 DP0 TP0] Decode batch. #running-req: 1, #token: 673, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.94, #queue-req: 0, 
[2025-07-28 01:03:41 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 150, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:03:41 DP1 TP0] Decode batch. #running-req: 1, #token: 250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 174.71, #queue-req: 0, 
[2025-07-28 01:03:41 DP0 TP0] Decode batch. #running-req: 1, #token: 713, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.97, #queue-req: 0, 
[2025-07-28 01:03:41 DP1 TP0] Decode batch. #running-req: 1, #token: 290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.57, #queue-req: 0, 
[2025-07-28 01:03:41 DP0 TP0] Decode batch. #running-req: 1, #token: 753, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.98, #queue-req: 0, 
[2025-07-28 01:03:41 DP1 TP0] Decode batch. #running-req: 1, #token: 330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.77, #queue-req: 0, 
[2025-07-28 01:03:42 DP0 TP0] Decode batch. #running-req: 1, #token: 793, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.56, #queue-req: 0, 
[2025-07-28 01:03:42 DP1 TP0] Decode batch. #running-req: 1, #token: 370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.70, #queue-req: 0, 
[2025-07-28 01:03:42 DP0 TP0] Decode batch. #running-req: 1, #token: 833, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.23, #queue-req: 0, 
[2025-07-28 01:03:42 DP1 TP0] Decode batch. #running-req: 1, #token: 410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.59, #queue-req: 0, 
[2025-07-28 01:03:42 DP0 TP0] Decode batch. #running-req: 1, #token: 873, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.95, #queue-req: 0, 
[2025-07-28 01:03:42 DP1 TP0] Decode batch. #running-req: 1, #token: 450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.09, #queue-req: 0, 
[2025-07-28 01:03:42 DP0 TP0] Decode batch. #running-req: 1, #token: 913, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.24, #queue-req: 0, 
[2025-07-28 01:03:42 DP1 TP0] Decode batch. #running-req: 1, #token: 490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.98, #queue-req: 0, 
[2025-07-28 01:03:42 DP0 TP0] Decode batch. #running-req: 1, #token: 953, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.68, #queue-req: 0, 
[2025-07-28 01:03:42 DP1 TP0] Decode batch. #running-req: 1, #token: 530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.70, #queue-req: 0, 
[2025-07-28 01:03:42 DP0 TP0] Decode batch. #running-req: 1, #token: 993, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.68, #queue-req: 0, 
[2025-07-28 01:03:42] INFO:     127.0.0.1:55742 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:03:42 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 148, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:03:42 DP1 TP0] Decode batch. #running-req: 1, #token: 570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.24, #queue-req: 0, 
[2025-07-28 01:03:43 DP0 TP0] Decode batch. #running-req: 1, #token: 268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 180.36, #queue-req: 0, 
[2025-07-28 01:03:43 DP1 TP0] Decode batch. #running-req: 1, #token: 610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.37, #queue-req: 0, 
[2025-07-28 01:03:43 DP0 TP0] Decode batch. #running-req: 1, #token: 308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.33, #queue-req: 0, 
[2025-07-28 01:03:43 DP1 TP0] Decode batch. #running-req: 1, #token: 650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.07, #queue-req: 0, 
[2025-07-28 01:03:43 DP0 TP0] Decode batch. #running-req: 1, #token: 348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.30, #queue-req: 0, 
[2025-07-28 01:03:43 DP1 TP0] Decode batch. #running-req: 1, #token: 690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.43, #queue-req: 0, 
[2025-07-28 01:03:43 DP0 TP0] Decode batch. #running-req: 1, #token: 388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.17, #queue-req: 0, 
[2025-07-28 01:03:43 DP1 TP0] Decode batch. #running-req: 1, #token: 730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.66, #queue-req: 0, 
[2025-07-28 01:03:43 DP0 TP0] Decode batch. #running-req: 1, #token: 428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.49, #queue-req: 0, 
[2025-07-28 01:03:43 DP1 TP0] Decode batch. #running-req: 1, #token: 770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.47, #queue-req: 0, 
[2025-07-28 01:03:43 DP0 TP0] Decode batch. #running-req: 1, #token: 468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.37, #queue-req: 0, 
[2025-07-28 01:03:43 DP1 TP0] Decode batch. #running-req: 1, #token: 810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.09, #queue-req: 0, 
[2025-07-28 01:03:44 DP0 TP0] Decode batch. #running-req: 1, #token: 508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.10, #queue-req: 0, 
[2025-07-28 01:03:44 DP1 TP0] Decode batch. #running-req: 1, #token: 850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.11, #queue-req: 0, 
[2025-07-28 01:03:44 DP0 TP0] Decode batch. #running-req: 1, #token: 548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.96, #queue-req: 0, 
[2025-07-28 01:03:44 DP1 TP0] Decode batch. #running-req: 1, #token: 890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.84, #queue-req: 0, 
[2025-07-28 01:03:44 DP0 TP0] Decode batch. #running-req: 1, #token: 588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.10, #queue-req: 0, 
[2025-07-28 01:03:44 DP1 TP0] Decode batch. #running-req: 1, #token: 930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.24, #queue-req: 0, 
[2025-07-28 01:03:44 DP0 TP0] Decode batch. #running-req: 1, #token: 628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.21, #queue-req: 0, 
[2025-07-28 01:03:44 DP1 TP0] Decode batch. #running-req: 1, #token: 970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.29, #queue-req: 0, 
[2025-07-28 01:03:44 DP0 TP0] Decode batch. #running-req: 1, #token: 668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.34, #queue-req: 0, 
[2025-07-28 01:03:44 DP1 TP0] Decode batch. #running-req: 1, #token: 1010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.27, #queue-req: 0, 
[2025-07-28 01:03:44 DP0 TP0] Decode batch. #running-req: 1, #token: 708, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.06, #queue-req: 0, 
[2025-07-28 01:03:44 DP1 TP0] Decode batch. #running-req: 1, #token: 1050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.56, #queue-req: 0, 
[2025-07-28 01:03:45 DP0 TP0] Decode batch. #running-req: 1, #token: 748, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.92, #queue-req: 0, 
[2025-07-28 01:03:45 DP1 TP0] Decode batch. #running-req: 1, #token: 1090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.10, #queue-req: 0, 
[2025-07-28 01:03:45 DP0 TP0] Decode batch. #running-req: 1, #token: 788, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.82, #queue-req: 0, 
[2025-07-28 01:03:45 DP1 TP0] Decode batch. #running-req: 1, #token: 1130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.09, #queue-req: 0, 
[2025-07-28 01:03:45 DP0 TP0] Decode batch. #running-req: 1, #token: 828, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.58, #queue-req: 0, 
[2025-07-28 01:03:45 DP1 TP0] Decode batch. #running-req: 1, #token: 1170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.42, #queue-req: 0, 
[2025-07-28 01:03:45 DP0 TP0] Decode batch. #running-req: 1, #token: 868, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.36, #queue-req: 0, 
[2025-07-28 01:03:45 DP1 TP0] Decode batch. #running-req: 1, #token: 1210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.51, #queue-req: 0, 
[2025-07-28 01:03:45 DP0 TP0] Decode batch. #running-req: 1, #token: 908, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.70, #queue-req: 0, 
[2025-07-28 01:03:45 DP1 TP0] Decode batch. #running-req: 1, #token: 1250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.52, #queue-req: 0, 
[2025-07-28 01:03:45 DP0 TP0] Decode batch. #running-req: 1, #token: 948, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.48, #queue-req: 0, 
[2025-07-28 01:03:45 DP1 TP0] Decode batch. #running-req: 1, #token: 1290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.52, #queue-req: 0, 
[2025-07-28 01:03:46 DP0 TP0] Decode batch. #running-req: 1, #token: 988, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.35, #queue-req: 0, 
[2025-07-28 01:03:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.14, #queue-req: 0, 
[2025-07-28 01:03:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1028, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.19, #queue-req: 0, 
[2025-07-28 01:03:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.30, #queue-req: 0, 
[2025-07-28 01:03:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1068, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.21, #queue-req: 0, 
[2025-07-28 01:03:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.12, #queue-req: 0, 
[2025-07-28 01:03:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1108, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.22, #queue-req: 0, 
[2025-07-28 01:03:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.59, #queue-req: 0, 
[2025-07-28 01:03:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1148, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.31, #queue-req: 0, 
[2025-07-28 01:03:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.78, #queue-req: 0, 
[2025-07-28 01:03:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1188, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.38, #queue-req: 0, 
[2025-07-28 01:03:46] INFO:     127.0.0.1:55750 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:03:46 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 160, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:03:46 DP1 TP0] Decode batch. #running-req: 1, #token: 256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 175.06, #queue-req: 0, 
[2025-07-28 01:03:47 DP0 TP0] Decode batch. #running-req: 1, #token: 1228, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.70, #queue-req: 0, 
[2025-07-28 01:03:47 DP1 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.70, #queue-req: 0, 
[2025-07-28 01:03:47 DP0 TP0] Decode batch. #running-req: 1, #token: 1268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.87, #queue-req: 0, 
[2025-07-28 01:03:47 DP1 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.02, #queue-req: 0, 
[2025-07-28 01:03:47 DP0 TP0] Decode batch. #running-req: 1, #token: 1308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.27, #queue-req: 0, 
[2025-07-28 01:03:47 DP1 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.80, #queue-req: 0, 
[2025-07-28 01:03:47 DP0 TP0] Decode batch. #running-req: 1, #token: 1348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.06, #queue-req: 0, 
[2025-07-28 01:03:47 DP1 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.06, #queue-req: 0, 
[2025-07-28 01:03:47 DP0 TP0] Decode batch. #running-req: 1, #token: 1388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.20, #queue-req: 0, 
[2025-07-28 01:03:47 DP1 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.54, #queue-req: 0, 
[2025-07-28 01:03:47 DP0 TP0] Decode batch. #running-req: 1, #token: 1428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.37, #queue-req: 0, 
[2025-07-28 01:03:47 DP1 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.46, #queue-req: 0, 
[2025-07-28 01:03:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.68, #queue-req: 0, 
[2025-07-28 01:03:48 DP1 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.07, #queue-req: 0, 
[2025-07-28 01:03:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.66, #queue-req: 0, 
[2025-07-28 01:03:48 DP1 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.89, #queue-req: 0, 
[2025-07-28 01:03:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.64, #queue-req: 0, 
[2025-07-28 01:03:48 DP1 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.76, #queue-req: 0, 
[2025-07-28 01:03:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.01, #queue-req: 0, 
[2025-07-28 01:03:48 DP1 TP0] Decode batch. #running-req: 1, #token: 656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.20, #queue-req: 0, 
[2025-07-28 01:03:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.18, #queue-req: 0, 
[2025-07-28 01:03:48 DP1 TP0] Decode batch. #running-req: 1, #token: 696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.44, #queue-req: 0, 
[2025-07-28 01:03:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.07, #queue-req: 0, 
[2025-07-28 01:03:48 DP1 TP0] Decode batch. #running-req: 1, #token: 736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.29, #queue-req: 0, 
[2025-07-28 01:03:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1708, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.49, #queue-req: 0, 
[2025-07-28 01:03:49 DP1 TP0] Decode batch. #running-req: 1, #token: 776, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.23, #queue-req: 0, 
[2025-07-28 01:03:49 DP0 TP0] Decode batch. #running-req: 1, #token: 1748, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.23, #queue-req: 0, 
[2025-07-28 01:03:49 DP1 TP0] Decode batch. #running-req: 1, #token: 816, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.88, #queue-req: 0, 
[2025-07-28 01:03:49 DP0 TP0] Decode batch. #running-req: 1, #token: 1788, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.69, #queue-req: 0, 
[2025-07-28 01:03:49 DP1 TP0] Decode batch. #running-req: 1, #token: 856, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.00, #queue-req: 0, 
[2025-07-28 01:03:49 DP0 TP0] Decode batch. #running-req: 1, #token: 1828, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.93, #queue-req: 0, 
[2025-07-28 01:03:49 DP1 TP0] Decode batch. #running-req: 1, #token: 896, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.81, #queue-req: 0, 
[2025-07-28 01:03:49 DP0 TP0] Decode batch. #running-req: 1, #token: 1868, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.87, #queue-req: 0, 
[2025-07-28 01:03:49 DP1 TP0] Decode batch. #running-req: 1, #token: 936, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.24, #queue-req: 0, 
[2025-07-28 01:03:49 DP0 TP0] Decode batch. #running-req: 1, #token: 1908, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.90, #queue-req: 0, 
[2025-07-28 01:03:49 DP1 TP0] Decode batch. #running-req: 1, #token: 976, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.32, #queue-req: 0, 
[2025-07-28 01:03:49 DP0 TP0] Decode batch. #running-req: 1, #token: 1948, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.26, #queue-req: 0, 
[2025-07-28 01:03:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1016, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.29, #queue-req: 0, 
[2025-07-28 01:03:50 DP0 TP0] Decode batch. #running-req: 1, #token: 1988, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.50, #queue-req: 0, 
[2025-07-28 01:03:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1056, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.59, #queue-req: 0, 
[2025-07-28 01:03:50 DP0 TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.02, #queue-req: 0, 
[2025-07-28 01:03:50] INFO:     127.0.0.1:39732 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:03:50 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 114, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:03:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1096, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.77, #queue-req: 0, 
[2025-07-28 01:03:50 DP0 TP0] Decode batch. #running-req: 1, #token: 242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 180.18, #queue-req: 0, 
[2025-07-28 01:03:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1136, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.88, #queue-req: 0, 
[2025-07-28 01:03:50 DP0 TP0] Decode batch. #running-req: 1, #token: 282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.16, #queue-req: 0, 
[2025-07-28 01:03:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1176, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.20, #queue-req: 0, 
[2025-07-28 01:03:50 DP0 TP0] Decode batch. #running-req: 1, #token: 322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.41, #queue-req: 0, 
[2025-07-28 01:03:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1216, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.36, #queue-req: 0, 
[2025-07-28 01:03:51 DP0 TP0] Decode batch. #running-req: 1, #token: 362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.78, #queue-req: 0, 
[2025-07-28 01:03:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.36, #queue-req: 0, 
[2025-07-28 01:03:51 DP0 TP0] Decode batch. #running-req: 1, #token: 402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.45, #queue-req: 0, 
[2025-07-28 01:03:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.30, #queue-req: 0, 
[2025-07-28 01:03:51 DP0 TP0] Decode batch. #running-req: 1, #token: 442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.86, #queue-req: 0, 
[2025-07-28 01:03:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.73, #queue-req: 0, 
[2025-07-28 01:03:51 DP0 TP0] Decode batch. #running-req: 1, #token: 482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.63, #queue-req: 0, 
[2025-07-28 01:03:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.85, #queue-req: 0, 
[2025-07-28 01:03:51 DP0 TP0] Decode batch. #running-req: 1, #token: 522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.57, #queue-req: 0, 
[2025-07-28 01:03:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.82, #queue-req: 0, 
[2025-07-28 01:03:51 DP0 TP0] Decode batch. #running-req: 1, #token: 562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.62, #queue-req: 0, 
[2025-07-28 01:03:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.47, #queue-req: 0, 
[2025-07-28 01:03:52 DP0 TP0] Decode batch. #running-req: 1, #token: 602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.26, #queue-req: 0, 
[2025-07-28 01:03:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.59, #queue-req: 0, 
[2025-07-28 01:03:52 DP0 TP0] Decode batch. #running-req: 1, #token: 642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.95, #queue-req: 0, 
[2025-07-28 01:03:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.55, #queue-req: 0, 
[2025-07-28 01:03:52 DP0 TP0] Decode batch. #running-req: 1, #token: 682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.69, #queue-req: 0, 
[2025-07-28 01:03:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.85, #queue-req: 0, 
[2025-07-28 01:03:52 DP0 TP0] Decode batch. #running-req: 1, #token: 722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.04, #queue-req: 0, 
[2025-07-28 01:03:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.61, #queue-req: 0, 
[2025-07-28 01:03:52 DP0 TP0] Decode batch. #running-req: 1, #token: 762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.00, #queue-req: 0, 
[2025-07-28 01:03:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.14, #queue-req: 0, 
[2025-07-28 01:03:52 DP0 TP0] Decode batch. #running-req: 1, #token: 802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.18, #queue-req: 0, 
[2025-07-28 01:03:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.28, #queue-req: 0, 
[2025-07-28 01:03:52 DP0 TP0] Decode batch. #running-req: 1, #token: 842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.72, #queue-req: 0, 
[2025-07-28 01:03:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.32, #queue-req: 0, 
[2025-07-28 01:03:53 DP0 TP0] Decode batch. #running-req: 1, #token: 882, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.46, #queue-req: 0, 
[2025-07-28 01:03:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1776, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.40, #queue-req: 0, 
[2025-07-28 01:03:53 DP0 TP0] Decode batch. #running-req: 1, #token: 922, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.66, #queue-req: 0, 
[2025-07-28 01:03:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1816, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.97, #queue-req: 0, 
[2025-07-28 01:03:53 DP0 TP0] Decode batch. #running-req: 1, #token: 962, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.50, #queue-req: 0, 
[2025-07-28 01:03:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1856, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.74, #queue-req: 0, 
[2025-07-28 01:03:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1002, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.31, #queue-req: 0, 
[2025-07-28 01:03:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1896, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.78, #queue-req: 0, 
[2025-07-28 01:03:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1042, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.74, #queue-req: 0, 
[2025-07-28 01:03:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1936, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.01, #queue-req: 0, 
[2025-07-28 01:03:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1082, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.48, #queue-req: 0, 
[2025-07-28 01:03:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1976, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.18, #queue-req: 0, 
[2025-07-28 01:03:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1122, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.54, #queue-req: 0, 
[2025-07-28 01:03:54 DP1 TP0] Decode batch. #running-req: 1, #token: 2016, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.69, #queue-req: 0, 
[2025-07-28 01:03:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1162, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.58, #queue-req: 0, 
[2025-07-28 01:03:54 DP1 TP0] Decode batch. #running-req: 1, #token: 2056, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.91, #queue-req: 0, 
[2025-07-28 01:03:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1202, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.62, #queue-req: 0, 
[2025-07-28 01:03:54 DP1 TP0] Decode batch. #running-req: 1, #token: 2096, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.14, #queue-req: 0, 
[2025-07-28 01:03:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.18, #queue-req: 0, 
[2025-07-28 01:03:54 DP1 TP0] Decode batch. #running-req: 1, #token: 2136, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.17, #queue-req: 0, 
[2025-07-28 01:03:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.11, #queue-req: 0, 
[2025-07-28 01:03:54 DP1 TP0] Decode batch. #running-req: 1, #token: 2176, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.26, #queue-req: 0, 
[2025-07-28 01:03:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.43, #queue-req: 0, 
[2025-07-28 01:03:55 DP1 TP0] Decode batch. #running-req: 1, #token: 2216, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.47, #queue-req: 0, 
[2025-07-28 01:03:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.35, #queue-req: 0, 
[2025-07-28 01:03:55 DP1 TP0] Decode batch. #running-req: 1, #token: 2256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.35, #queue-req: 0, 
[2025-07-28 01:03:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.59, #queue-req: 0, 
[2025-07-28 01:03:55 DP1 TP0] Decode batch. #running-req: 1, #token: 2296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.76, #queue-req: 0, 
[2025-07-28 01:03:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.41, #queue-req: 0, 
[2025-07-28 01:03:55 DP1 TP0] Decode batch. #running-req: 1, #token: 2336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.81, #queue-req: 0, 
[2025-07-28 01:03:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.47, #queue-req: 0, 
[2025-07-28 01:03:55 DP1 TP0] Decode batch. #running-req: 1, #token: 2376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.76, #queue-req: 0, 
[2025-07-28 01:03:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 229.29, #queue-req: 0, 
[2025-07-28 01:03:55 DP1 TP0] Decode batch. #running-req: 1, #token: 2416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.70, #queue-req: 0, 
[2025-07-28 01:03:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.15, #queue-req: 0, 
[2025-07-28 01:03:56 DP1 TP0] Decode batch. #running-req: 1, #token: 2456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.94, #queue-req: 0, 
[2025-07-28 01:03:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.34, #queue-req: 0, 
[2025-07-28 01:03:56 DP1 TP0] Decode batch. #running-req: 1, #token: 2496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.93, #queue-req: 0, 
[2025-07-28 01:03:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.53, #queue-req: 0, 
[2025-07-28 01:03:56 DP1 TP0] Decode batch. #running-req: 1, #token: 2536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.89, #queue-req: 0, 
[2025-07-28 01:03:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.28, #queue-req: 0, 
[2025-07-28 01:03:56 DP1 TP0] Decode batch. #running-req: 1, #token: 2576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.38, #queue-req: 0, 
[2025-07-28 01:03:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.73, #queue-req: 0, 
[2025-07-28 01:03:56 DP1 TP0] Decode batch. #running-req: 1, #token: 2616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.80, #queue-req: 0, 
[2025-07-28 01:03:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.62, #queue-req: 0, 
[2025-07-28 01:03:56 DP1 TP0] Decode batch. #running-req: 1, #token: 2656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.55, #queue-req: 0, 
[2025-07-28 01:03:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.30, #queue-req: 0, 
[2025-07-28 01:03:57 DP1 TP0] Decode batch. #running-req: 1, #token: 2696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.48, #queue-req: 0, 
[2025-07-28 01:03:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.80, #queue-req: 0, 
[2025-07-28 01:03:57 DP1 TP0] Decode batch. #running-req: 1, #token: 2736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.66, #queue-req: 0, 
[2025-07-28 01:03:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1882, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.63, #queue-req: 0, 
[2025-07-28 01:03:57 DP1 TP0] Decode batch. #running-req: 1, #token: 2776, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.76, #queue-req: 0, 
[2025-07-28 01:03:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1922, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.90, #queue-req: 0, 
[2025-07-28 01:03:57 DP1 TP0] Decode batch. #running-req: 1, #token: 2816, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.53, #queue-req: 0, 
[2025-07-28 01:03:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1962, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.35, #queue-req: 0, 
[2025-07-28 01:03:57 DP1 TP0] Decode batch. #running-req: 1, #token: 2856, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.62, #queue-req: 0, 
[2025-07-28 01:03:57 DP0 TP0] Decode batch. #running-req: 1, #token: 2002, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.56, #queue-req: 0, 
[2025-07-28 01:03:57 DP1 TP0] Decode batch. #running-req: 1, #token: 2896, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.49, #queue-req: 0, 
[2025-07-28 01:03:57 DP0 TP0] Decode batch. #running-req: 1, #token: 2042, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.90, #queue-req: 0, 
[2025-07-28 01:03:58 DP1 TP0] Decode batch. #running-req: 1, #token: 2936, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.96, #queue-req: 0, 
[2025-07-28 01:03:58 DP0 TP0] Decode batch. #running-req: 1, #token: 2082, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.94, #queue-req: 0, 
[2025-07-28 01:03:58 DP1 TP0] Decode batch. #running-req: 1, #token: 2976, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.39, #queue-req: 0, 
[2025-07-28 01:03:58 DP0 TP0] Decode batch. #running-req: 1, #token: 2122, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.89, #queue-req: 0, 
[2025-07-28 01:03:58 DP1 TP0] Decode batch. #running-req: 1, #token: 3016, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.11, #queue-req: 0, 
[2025-07-28 01:03:58 DP0 TP0] Decode batch. #running-req: 1, #token: 2162, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.87, #queue-req: 0, 
[2025-07-28 01:03:58 DP1 TP0] Decode batch. #running-req: 1, #token: 3056, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.20, #queue-req: 0, 
[2025-07-28 01:03:58 DP0 TP0] Decode batch. #running-req: 1, #token: 2202, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.85, #queue-req: 0, 
[2025-07-28 01:03:58 DP1 TP0] Decode batch. #running-req: 1, #token: 3096, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.14, #queue-req: 0, 
[2025-07-28 01:03:58 DP0 TP0] Decode batch. #running-req: 1, #token: 2242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.27, #queue-req: 0, 
[2025-07-28 01:03:58 DP1 TP0] Decode batch. #running-req: 1, #token: 3136, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.75, #queue-req: 0, 
[2025-07-28 01:03:58 DP0 TP0] Decode batch. #running-req: 1, #token: 2282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.10, #queue-req: 0, 
[2025-07-28 01:03:59 DP1 TP0] Decode batch. #running-req: 1, #token: 3176, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.74, #queue-req: 0, 
[2025-07-28 01:03:59 DP0 TP0] Decode batch. #running-req: 1, #token: 2322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.92, #queue-req: 0, 
[2025-07-28 01:03:59 DP1 TP0] Decode batch. #running-req: 1, #token: 3216, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.02, #queue-req: 0, 
[2025-07-28 01:03:59 DP0 TP0] Decode batch. #running-req: 1, #token: 2362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.80, #queue-req: 0, 
[2025-07-28 01:03:59 DP1 TP0] Decode batch. #running-req: 1, #token: 3256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.19, #queue-req: 0, 
[2025-07-28 01:03:59 DP0 TP0] Decode batch. #running-req: 1, #token: 2402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.99, #queue-req: 0, 
[2025-07-28 01:03:59 DP1 TP0] Decode batch. #running-req: 1, #token: 3296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.06, #queue-req: 0, 
[2025-07-28 01:03:59 DP0 TP0] Decode batch. #running-req: 1, #token: 2442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.45, #queue-req: 0, 
[2025-07-28 01:03:59 DP1 TP0] Decode batch. #running-req: 1, #token: 3336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.25, #queue-req: 0, 
[2025-07-28 01:03:59 DP0 TP0] Decode batch. #running-req: 1, #token: 2482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.19, #queue-req: 0, 
[2025-07-28 01:03:59 DP1 TP0] Decode batch. #running-req: 1, #token: 3376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.96, #queue-req: 0, 
[2025-07-28 01:03:59 DP0 TP0] Decode batch. #running-req: 1, #token: 2522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.93, #queue-req: 0, 
[2025-07-28 01:04:00 DP1 TP0] Decode batch. #running-req: 1, #token: 3416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.96, #queue-req: 0, 
[2025-07-28 01:04:00 DP0 TP0] Decode batch. #running-req: 1, #token: 2562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.86, #queue-req: 0, 
[2025-07-28 01:04:00 DP1 TP0] Decode batch. #running-req: 1, #token: 3456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.96, #queue-req: 0, 
[2025-07-28 01:04:00 DP0 TP0] Decode batch. #running-req: 1, #token: 2602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.86, #queue-req: 0, 
[2025-07-28 01:04:00 DP1 TP0] Decode batch. #running-req: 1, #token: 3496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.30, #queue-req: 0, 
[2025-07-28 01:04:00 DP0 TP0] Decode batch. #running-req: 1, #token: 2642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.58, #queue-req: 0, 
[2025-07-28 01:04:00 DP1 TP0] Decode batch. #running-req: 1, #token: 3536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.75, #queue-req: 0, 
[2025-07-28 01:04:00 DP0 TP0] Decode batch. #running-req: 1, #token: 2682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.51, #queue-req: 0, 
[2025-07-28 01:04:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.29, #queue-req: 0, 
[2025-07-28 01:04:00] INFO:     127.0.0.1:39738 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:04:00 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 80, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:04:00 DP0 TP0] Decode batch. #running-req: 1, #token: 2722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.34, #queue-req: 0, 
[2025-07-28 01:04:00 DP1 TP0] Decode batch. #running-req: 1, #token: 205, token usage: 0.00, cuda graph: True, gen throughput (token/s): 181.82, #queue-req: 0, 
[2025-07-28 01:04:00 DP0 TP0] Decode batch. #running-req: 1, #token: 2762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.71, #queue-req: 0, 
[2025-07-28 01:04:01 DP1 TP0] Decode batch. #running-req: 1, #token: 245, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.27, #queue-req: 0, 
[2025-07-28 01:04:01 DP0 TP0] Decode batch. #running-req: 1, #token: 2802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.38, #queue-req: 0, 
[2025-07-28 01:04:01 DP1 TP0] Decode batch. #running-req: 1, #token: 285, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.82, #queue-req: 0, 
[2025-07-28 01:04:01 DP0 TP0] Decode batch. #running-req: 1, #token: 2842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.79, #queue-req: 0, 
[2025-07-28 01:04:01 DP1 TP0] Decode batch. #running-req: 1, #token: 325, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.61, #queue-req: 0, 
[2025-07-28 01:04:01 DP0 TP0] Decode batch. #running-req: 1, #token: 2882, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.31, #queue-req: 0, 
[2025-07-28 01:04:01 DP1 TP0] Decode batch. #running-req: 1, #token: 365, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.08, #queue-req: 0, 
[2025-07-28 01:04:01 DP0 TP0] Decode batch. #running-req: 1, #token: 2922, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.33, #queue-req: 0, 
[2025-07-28 01:04:01 DP1 TP0] Decode batch. #running-req: 1, #token: 405, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.67, #queue-req: 0, 
[2025-07-28 01:04:01 DP0 TP0] Decode batch. #running-req: 1, #token: 2962, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.26, #queue-req: 0, 
[2025-07-28 01:04:01 DP1 TP0] Decode batch. #running-req: 1, #token: 445, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.74, #queue-req: 0, 
[2025-07-28 01:04:01 DP0 TP0] Decode batch. #running-req: 1, #token: 3002, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.37, #queue-req: 0, 
[2025-07-28 01:04:02 DP1 TP0] Decode batch. #running-req: 1, #token: 485, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.72, #queue-req: 0, 
[2025-07-28 01:04:02 DP0 TP0] Decode batch. #running-req: 1, #token: 3042, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.39, #queue-req: 0, 
[2025-07-28 01:04:02 DP1 TP0] Decode batch. #running-req: 1, #token: 525, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.14, #queue-req: 0, 
[2025-07-28 01:04:02 DP0 TP0] Decode batch. #running-req: 1, #token: 3082, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.97, #queue-req: 0, 
[2025-07-28 01:04:02 DP1 TP0] Decode batch. #running-req: 1, #token: 565, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.61, #queue-req: 0, 
[2025-07-28 01:04:02 DP0 TP0] Decode batch. #running-req: 1, #token: 3122, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.80, #queue-req: 0, 
[2025-07-28 01:04:02 DP1 TP0] Decode batch. #running-req: 1, #token: 605, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.14, #queue-req: 0, 
[2025-07-28 01:04:02 DP0 TP0] Decode batch. #running-req: 1, #token: 3162, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.70, #queue-req: 0, 
[2025-07-28 01:04:02 DP1 TP0] Decode batch. #running-req: 1, #token: 645, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.48, #queue-req: 0, 
[2025-07-28 01:04:02 DP0 TP0] Decode batch. #running-req: 1, #token: 3202, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.57, #queue-req: 0, 
[2025-07-28 01:04:02 DP1 TP0] Decode batch. #running-req: 1, #token: 685, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.88, #queue-req: 0, 
[2025-07-28 01:04:02 DP0 TP0] Decode batch. #running-req: 1, #token: 3242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.46, #queue-req: 0, 
[2025-07-28 01:04:03 DP1 TP0] Decode batch. #running-req: 1, #token: 725, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.89, #queue-req: 0, 
[2025-07-28 01:04:03 DP0 TP0] Decode batch. #running-req: 1, #token: 3282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.03, #queue-req: 0, 
[2025-07-28 01:04:03 DP1 TP0] Decode batch. #running-req: 1, #token: 765, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.72, #queue-req: 0, 
[2025-07-28 01:04:03 DP0 TP0] Decode batch. #running-req: 1, #token: 3322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.03, #queue-req: 0, 
[2025-07-28 01:04:03 DP1 TP0] Decode batch. #running-req: 1, #token: 805, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.36, #queue-req: 0, 
[2025-07-28 01:04:03 DP0 TP0] Decode batch. #running-req: 1, #token: 3362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.83, #queue-req: 0, 
[2025-07-28 01:04:03 DP1 TP0] Decode batch. #running-req: 1, #token: 845, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.14, #queue-req: 0, 
[2025-07-28 01:04:03 DP0 TP0] Decode batch. #running-req: 1, #token: 3402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.43, #queue-req: 0, 
[2025-07-28 01:04:03 DP1 TP0] Decode batch. #running-req: 1, #token: 885, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.34, #queue-req: 0, 
[2025-07-28 01:04:03 DP0 TP0] Decode batch. #running-req: 1, #token: 3442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.97, #queue-req: 0, 
[2025-07-28 01:04:03 DP1 TP0] Decode batch. #running-req: 1, #token: 925, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.65, #queue-req: 0, 
[2025-07-28 01:04:03 DP0 TP0] Decode batch. #running-req: 1, #token: 3482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.87, #queue-req: 0, 
[2025-07-28 01:04:04 DP1 TP0] Decode batch. #running-req: 1, #token: 965, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.47, #queue-req: 0, 
[2025-07-28 01:04:04 DP0 TP0] Decode batch. #running-req: 1, #token: 3522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.90, #queue-req: 0, 
[2025-07-28 01:04:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1005, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.93, #queue-req: 0, 
[2025-07-28 01:04:04 DP0 TP0] Decode batch. #running-req: 1, #token: 3562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.15, #queue-req: 0, 
[2025-07-28 01:04:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1045, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.07, #queue-req: 0, 
[2025-07-28 01:04:04 DP0 TP0] Decode batch. #running-req: 1, #token: 3602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.57, #queue-req: 0, 
[2025-07-28 01:04:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1085, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.61, #queue-req: 0, 
[2025-07-28 01:04:04 DP0 TP0] Decode batch. #running-req: 1, #token: 3642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.72, #queue-req: 0, 
[2025-07-28 01:04:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1125, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.51, #queue-req: 0, 
[2025-07-28 01:04:04 DP0 TP0] Decode batch. #running-req: 1, #token: 3682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.68, #queue-req: 0, 
[2025-07-28 01:04:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1165, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.17, #queue-req: 0, 
[2025-07-28 01:04:04 DP0 TP0] Decode batch. #running-req: 1, #token: 3722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.80, #queue-req: 0, 
[2025-07-28 01:04:05 DP1 TP0] Decode batch. #running-req: 1, #token: 1205, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.07, #queue-req: 0, 
[2025-07-28 01:04:05 DP0 TP0] Decode batch. #running-req: 1, #token: 3762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.92, #queue-req: 0, 
[2025-07-28 01:04:05 DP1 TP0] Decode batch. #running-req: 1, #token: 1245, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.51, #queue-req: 0, 
[2025-07-28 01:04:05] INFO:     127.0.0.1:49116 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:04:05 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 136, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:04:05 DP0 TP0] Decode batch. #running-req: 2, #token: 3947, token usage: 0.00, cuda graph: True, gen throughput (token/s): 223.00, #queue-req: 0, 
[2025-07-28 01:04:05 DP0 TP0] Decode batch. #running-req: 2, #token: 4027, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.34, #queue-req: 0, 
[2025-07-28 01:04:05 DP0 TP0] Decode batch. #running-req: 2, #token: 4107, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.27, #queue-req: 0, 
[2025-07-28 01:04:05 DP0 TP0] Decode batch. #running-req: 2, #token: 4187, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.06, #queue-req: 0, 
[2025-07-28 01:04:05 DP0 TP0] Decode batch. #running-req: 2, #token: 4267, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.78, #queue-req: 0, 
[2025-07-28 01:04:06] INFO:     127.0.0.1:39742 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:04:06 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 137, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:04:06 DP0 TP0] Decode batch. #running-req: 1, #token: 430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.80, #queue-req: 0, 
[2025-07-28 01:04:06 DP1 TP0] Decode batch. #running-req: 1, #token: 259, token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.25, #queue-req: 0, 
[2025-07-28 01:04:06 DP0 TP0] Decode batch. #running-req: 1, #token: 470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.67, #queue-req: 0, 
[2025-07-28 01:04:06 DP1 TP0] Decode batch. #running-req: 1, #token: 299, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.01, #queue-req: 0, 
[2025-07-28 01:04:06 DP0 TP0] Decode batch. #running-req: 1, #token: 510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.55, #queue-req: 0, 
[2025-07-28 01:04:06 DP1 TP0] Decode batch. #running-req: 1, #token: 339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.85, #queue-req: 0, 
[2025-07-28 01:04:06 DP0 TP0] Decode batch. #running-req: 1, #token: 550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.62, #queue-req: 0, 
[2025-07-28 01:04:06 DP1 TP0] Decode batch. #running-req: 1, #token: 379, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.34, #queue-req: 0, 
[2025-07-28 01:04:06 DP0 TP0] Decode batch. #running-req: 1, #token: 590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.35, #queue-req: 0, 
[2025-07-28 01:04:06 DP1 TP0] Decode batch. #running-req: 1, #token: 419, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.97, #queue-req: 0, 
[2025-07-28 01:04:06 DP0 TP0] Decode batch. #running-req: 1, #token: 630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.02, #queue-req: 0, 
[2025-07-28 01:04:07 DP1 TP0] Decode batch. #running-req: 1, #token: 459, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.42, #queue-req: 0, 
[2025-07-28 01:04:07 DP0 TP0] Decode batch. #running-req: 1, #token: 670, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.32, #queue-req: 0, 
[2025-07-28 01:04:07 DP1 TP0] Decode batch. #running-req: 1, #token: 499, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.52, #queue-req: 0, 
[2025-07-28 01:04:07 DP0 TP0] Decode batch. #running-req: 1, #token: 710, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.24, #queue-req: 0, 
[2025-07-28 01:04:07 DP1 TP0] Decode batch. #running-req: 1, #token: 539, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.98, #queue-req: 0, 
[2025-07-28 01:04:07 DP0 TP0] Decode batch. #running-req: 1, #token: 750, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.34, #queue-req: 0, 
[2025-07-28 01:04:07 DP1 TP0] Decode batch. #running-req: 1, #token: 579, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.02, #queue-req: 0, 
[2025-07-28 01:04:07 DP0 TP0] Decode batch. #running-req: 1, #token: 790, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.84, #queue-req: 0, 
[2025-07-28 01:04:07 DP1 TP0] Decode batch. #running-req: 1, #token: 619, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.95, #queue-req: 0, 
[2025-07-28 01:04:07 DP0 TP0] Decode batch. #running-req: 1, #token: 830, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.74, #queue-req: 0, 
[2025-07-28 01:04:07] INFO:     127.0.0.1:46916 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:04:07 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 85, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:04:07 DP1 TP0] Decode batch. #running-req: 1, #token: 659, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.23, #queue-req: 0, 
[2025-07-28 01:04:08 DP0 TP0] Decode batch. #running-req: 1, #token: 195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 181.39, #queue-req: 0, 
[2025-07-28 01:04:08 DP1 TP0] Decode batch. #running-req: 1, #token: 699, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.34, #queue-req: 0, 
[2025-07-28 01:04:08 DP0 TP0] Decode batch. #running-req: 1, #token: 235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.28, #queue-req: 0, 
[2025-07-28 01:04:08 DP1 TP0] Decode batch. #running-req: 1, #token: 739, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.25, #queue-req: 0, 
[2025-07-28 01:04:08 DP0 TP0] Decode batch. #running-req: 1, #token: 275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.85, #queue-req: 0, 
[2025-07-28 01:04:08 DP1 TP0] Decode batch. #running-req: 1, #token: 779, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.33, #queue-req: 0, 
[2025-07-28 01:04:08 DP0 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.91, #queue-req: 0, 
[2025-07-28 01:04:08 DP1 TP0] Decode batch. #running-req: 1, #token: 819, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.73, #queue-req: 0, 
[2025-07-28 01:04:08 DP0 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.84, #queue-req: 0, 
[2025-07-28 01:04:08 DP1 TP0] Decode batch. #running-req: 1, #token: 859, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.74, #queue-req: 0, 
[2025-07-28 01:04:08 DP0 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.77, #queue-req: 0, 
[2025-07-28 01:04:08 DP1 TP0] Decode batch. #running-req: 1, #token: 899, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.84, #queue-req: 0, 
[2025-07-28 01:04:09 DP0 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.00, #queue-req: 0, 
[2025-07-28 01:04:09 DP1 TP0] Decode batch. #running-req: 1, #token: 939, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.94, #queue-req: 0, 
[2025-07-28 01:04:09 DP0 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.79, #queue-req: 0, 
[2025-07-28 01:04:09 DP1 TP0] Decode batch. #running-req: 1, #token: 979, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.29, #queue-req: 0, 
[2025-07-28 01:04:09 DP0 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.90, #queue-req: 0, 
[2025-07-28 01:04:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1019, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.08, #queue-req: 0, 
[2025-07-28 01:04:09 DP0 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.58, #queue-req: 0, 
[2025-07-28 01:04:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1059, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.26, #queue-req: 0, 
[2025-07-28 01:04:09 DP0 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.52, #queue-req: 0, 
[2025-07-28 01:04:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1099, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.20, #queue-req: 0, 
[2025-07-28 01:04:09 DP0 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.37, #queue-req: 0, 
[2025-07-28 01:04:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1139, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.31, #queue-req: 0, 
[2025-07-28 01:04:09 DP0 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.59, #queue-req: 0, 
[2025-07-28 01:04:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1179, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.55, #queue-req: 0, 
[2025-07-28 01:04:10 DP0 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.45, #queue-req: 0, 
[2025-07-28 01:04:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1219, token usage: 0.00, cuda graph: True, gen throughput (token/s): 223.85, #queue-req: 0, 
[2025-07-28 01:04:10 DP0 TP0] Decode batch. #running-req: 1, #token: 755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.85, #queue-req: 0, 
[2025-07-28 01:04:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1259, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.01, #queue-req: 0, 
[2025-07-28 01:04:10 DP0 TP0] Decode batch. #running-req: 1, #token: 795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.29, #queue-req: 0, 
[2025-07-28 01:04:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1299, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.30, #queue-req: 0, 
[2025-07-28 01:04:10 DP0 TP0] Decode batch. #running-req: 1, #token: 835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.10, #queue-req: 0, 
[2025-07-28 01:04:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.20, #queue-req: 0, 
[2025-07-28 01:04:10 DP0 TP0] Decode batch. #running-req: 1, #token: 875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.02, #queue-req: 0, 
[2025-07-28 01:04:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1379, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.40, #queue-req: 0, 
[2025-07-28 01:04:10 DP0 TP0] Decode batch. #running-req: 1, #token: 915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.33, #queue-req: 0, 
[2025-07-28 01:04:11 DP1 TP0] Decode batch. #running-req: 1, #token: 1419, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.54, #queue-req: 0, 
[2025-07-28 01:04:11 DP0 TP0] Decode batch. #running-req: 1, #token: 955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.88, #queue-req: 0, 
[2025-07-28 01:04:11 DP1 TP0] Decode batch. #running-req: 1, #token: 1459, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.05, #queue-req: 0, 
[2025-07-28 01:04:11 DP0 TP0] Decode batch. #running-req: 1, #token: 995, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.82, #queue-req: 0, 
[2025-07-28 01:04:11 DP1 TP0] Decode batch. #running-req: 1, #token: 1499, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.68, #queue-req: 0, 
[2025-07-28 01:04:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.52, #queue-req: 0, 
[2025-07-28 01:04:11 DP1 TP0] Decode batch. #running-req: 1, #token: 1539, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.59, #queue-req: 0, 
[2025-07-28 01:04:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1075, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.20, #queue-req: 0, 
[2025-07-28 01:04:11 DP1 TP0] Decode batch. #running-req: 1, #token: 1579, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.24, #queue-req: 0, 
[2025-07-28 01:04:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.17, #queue-req: 0, 
[2025-07-28 01:04:11 DP1 TP0] Decode batch. #running-req: 1, #token: 1619, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.19, #queue-req: 0, 
[2025-07-28 01:04:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1155, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.14, #queue-req: 0, 
[2025-07-28 01:04:12 DP1 TP0] Decode batch. #running-req: 1, #token: 1659, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.28, #queue-req: 0, 
[2025-07-28 01:04:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.71, #queue-req: 0, 
[2025-07-28 01:04:12 DP1 TP0] Decode batch. #running-req: 1, #token: 1699, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.37, #queue-req: 0, 
[2025-07-28 01:04:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.42, #queue-req: 0, 
[2025-07-28 01:04:12] INFO:     127.0.0.1:46926 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:04:12 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 157, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:04:12 DP1 TP0] Decode batch. #running-req: 1, #token: 250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 169.57, #queue-req: 0, 
[2025-07-28 01:04:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.16, #queue-req: 0, 
[2025-07-28 01:04:12 DP1 TP0] Decode batch. #running-req: 1, #token: 290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.75, #queue-req: 0, 
[2025-07-28 01:04:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.19, #queue-req: 0, 
[2025-07-28 01:04:12 DP1 TP0] Decode batch. #running-req: 1, #token: 330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.24, #queue-req: 0, 
[2025-07-28 01:04:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.22, #queue-req: 0, 
[2025-07-28 01:04:12 DP1 TP0] Decode batch. #running-req: 1, #token: 370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.87, #queue-req: 0, 
[2025-07-28 01:04:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.40, #queue-req: 0, 
[2025-07-28 01:04:13 DP1 TP0] Decode batch. #running-req: 1, #token: 410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.22, #queue-req: 0, 
[2025-07-28 01:04:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.32, #queue-req: 0, 
[2025-07-28 01:04:13 DP1 TP0] Decode batch. #running-req: 1, #token: 450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.36, #queue-req: 0, 
[2025-07-28 01:04:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.73, #queue-req: 0, 
[2025-07-28 01:04:13 DP1 TP0] Decode batch. #running-req: 1, #token: 490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.34, #queue-req: 0, 
[2025-07-28 01:04:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.91, #queue-req: 0, 
[2025-07-28 01:04:13 DP1 TP0] Decode batch. #running-req: 1, #token: 530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.97, #queue-req: 0, 
[2025-07-28 01:04:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.10, #queue-req: 0, 
[2025-07-28 01:04:13 DP1 TP0] Decode batch. #running-req: 1, #token: 570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.80, #queue-req: 0, 
[2025-07-28 01:04:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.35, #queue-req: 0, 
[2025-07-28 01:04:13 DP1 TP0] Decode batch. #running-req: 1, #token: 610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.71, #queue-req: 0, 
[2025-07-28 01:04:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.25, #queue-req: 0, 
[2025-07-28 01:04:14 DP1 TP0] Decode batch. #running-req: 1, #token: 650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.78, #queue-req: 0, 
[2025-07-28 01:04:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.34, #queue-req: 0, 
[2025-07-28 01:04:14 DP1 TP0] Decode batch. #running-req: 1, #token: 690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.85, #queue-req: 0, 
[2025-07-28 01:04:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.76, #queue-req: 0, 
[2025-07-28 01:04:14 DP1 TP0] Decode batch. #running-req: 1, #token: 730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.33, #queue-req: 0, 
[2025-07-28 01:04:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.80, #queue-req: 0, 
[2025-07-28 01:04:14 DP1 TP0] Decode batch. #running-req: 1, #token: 770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.96, #queue-req: 0, 
[2025-07-28 01:04:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.75, #queue-req: 0, 
[2025-07-28 01:04:14 DP1 TP0] Decode batch. #running-req: 1, #token: 810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.43, #queue-req: 0, 
[2025-07-28 01:04:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.33, #queue-req: 0, 
[2025-07-28 01:04:14 DP1 TP0] Decode batch. #running-req: 1, #token: 850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.40, #queue-req: 0, 
[2025-07-28 01:04:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.33, #queue-req: 0, 
[2025-07-28 01:04:15 DP1 TP0] Decode batch. #running-req: 1, #token: 890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.26, #queue-req: 0, 
[2025-07-28 01:04:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.28, #queue-req: 0, 
[2025-07-28 01:04:15 DP1 TP0] Decode batch. #running-req: 1, #token: 930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.77, #queue-req: 0, 
[2025-07-28 01:04:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.11, #queue-req: 0, 
[2025-07-28 01:04:15 DP1 TP0] Decode batch. #running-req: 1, #token: 970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.60, #queue-req: 0, 
[2025-07-28 01:04:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1995, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.11, #queue-req: 0, 
[2025-07-28 01:04:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.58, #queue-req: 0, 
[2025-07-28 01:04:15 DP0 TP0] Decode batch. #running-req: 1, #token: 2035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.09, #queue-req: 0, 
[2025-07-28 01:04:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.73, #queue-req: 0, 
[2025-07-28 01:04:15 DP0 TP0] Decode batch. #running-req: 1, #token: 2075, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.48, #queue-req: 0, 
[2025-07-28 01:04:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.46, #queue-req: 0, 
[2025-07-28 01:04:15 DP0 TP0] Decode batch. #running-req: 1, #token: 2115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.24, #queue-req: 0, 
[2025-07-28 01:04:16 DP1 TP0] Decode batch. #running-req: 1, #token: 1130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.48, #queue-req: 0, 
[2025-07-28 01:04:16 DP0 TP0] Decode batch. #running-req: 1, #token: 2155, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.25, #queue-req: 0, 
[2025-07-28 01:04:16 DP1 TP0] Decode batch. #running-req: 1, #token: 1170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.61, #queue-req: 0, 
[2025-07-28 01:04:16 DP0 TP0] Decode batch. #running-req: 1, #token: 2195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.24, #queue-req: 0, 
[2025-07-28 01:04:16 DP1 TP0] Decode batch. #running-req: 1, #token: 1210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.81, #queue-req: 0, 
[2025-07-28 01:04:16 DP0 TP0] Decode batch. #running-req: 1, #token: 2235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.42, #queue-req: 0, 
[2025-07-28 01:04:16 DP1 TP0] Decode batch. #running-req: 1, #token: 1250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.79, #queue-req: 0, 
[2025-07-28 01:04:16 DP0 TP0] Decode batch. #running-req: 1, #token: 2275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.54, #queue-req: 0, 
[2025-07-28 01:04:16 DP1 TP0] Decode batch. #running-req: 1, #token: 1290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.81, #queue-req: 0, 
[2025-07-28 01:04:16 DP0 TP0] Decode batch. #running-req: 1, #token: 2315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.51, #queue-req: 0, 
[2025-07-28 01:04:16 DP1 TP0] Decode batch. #running-req: 1, #token: 1330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.40, #queue-req: 0, 
[2025-07-28 01:04:16 DP0 TP0] Decode batch. #running-req: 1, #token: 2355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.30, #queue-req: 0, 
[2025-07-28 01:04:17 DP1 TP0] Decode batch. #running-req: 1, #token: 1370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.31, #queue-req: 0, 
[2025-07-28 01:04:17 DP0 TP0] Decode batch. #running-req: 1, #token: 2395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.03, #queue-req: 0, 
[2025-07-28 01:04:17 DP1 TP0] Decode batch. #running-req: 1, #token: 1410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.26, #queue-req: 0, 
[2025-07-28 01:04:17 DP0 TP0] Decode batch. #running-req: 1, #token: 2435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.22, #queue-req: 0, 
[2025-07-28 01:04:17 DP1 TP0] Decode batch. #running-req: 1, #token: 1450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.76, #queue-req: 0, 
[2025-07-28 01:04:17 DP0 TP0] Decode batch. #running-req: 1, #token: 2475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.41, #queue-req: 0, 
[2025-07-28 01:04:17 DP1 TP0] Decode batch. #running-req: 1, #token: 1490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.78, #queue-req: 0, 
[2025-07-28 01:04:17] INFO:     127.0.0.1:46930 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:04:17 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 150, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:04:17 DP0 TP0] Decode batch. #running-req: 1, #token: 242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 176.43, #queue-req: 0, 
[2025-07-28 01:04:17 DP1 TP0] Decode batch. #running-req: 1, #token: 1530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.66, #queue-req: 0, 
[2025-07-28 01:04:17 DP0 TP0] Decode batch. #running-req: 1, #token: 282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.37, #queue-req: 0, 
[2025-07-28 01:04:17 DP1 TP0] Decode batch. #running-req: 1, #token: 1570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.16, #queue-req: 0, 
[2025-07-28 01:04:17 DP0 TP0] Decode batch. #running-req: 1, #token: 322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.61, #queue-req: 0, 
[2025-07-28 01:04:18 DP1 TP0] Decode batch. #running-req: 1, #token: 1610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.15, #queue-req: 0, 
[2025-07-28 01:04:18 DP0 TP0] Decode batch. #running-req: 1, #token: 362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.27, #queue-req: 0, 
[2025-07-28 01:04:18 DP1 TP0] Decode batch. #running-req: 1, #token: 1650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.14, #queue-req: 0, 
[2025-07-28 01:04:18 DP0 TP0] Decode batch. #running-req: 1, #token: 402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.12, #queue-req: 0, 
[2025-07-28 01:04:18 DP1 TP0] Decode batch. #running-req: 1, #token: 1690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.13, #queue-req: 0, 
[2025-07-28 01:04:18 DP0 TP0] Decode batch. #running-req: 1, #token: 442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.31, #queue-req: 0, 
[2025-07-28 01:04:18 DP1 TP0] Decode batch. #running-req: 1, #token: 1730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.75, #queue-req: 0, 
[2025-07-28 01:04:18 DP0 TP0] Decode batch. #running-req: 1, #token: 482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.91, #queue-req: 0, 
[2025-07-28 01:04:18 DP1 TP0] Decode batch. #running-req: 1, #token: 1770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.50, #queue-req: 0, 
[2025-07-28 01:04:18 DP0 TP0] Decode batch. #running-req: 1, #token: 522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.59, #queue-req: 0, 
[2025-07-28 01:04:18 DP1 TP0] Decode batch. #running-req: 1, #token: 1810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.16, #queue-req: 0, 
[2025-07-28 01:04:18 DP0 TP0] Decode batch. #running-req: 1, #token: 562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.75, #queue-req: 0, 
[2025-07-28 01:04:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.66, #queue-req: 0, 
[2025-07-28 01:04:19 DP0 TP0] Decode batch. #running-req: 1, #token: 602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.69, #queue-req: 0, 
[2025-07-28 01:04:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.71, #queue-req: 0, 
[2025-07-28 01:04:19 DP0 TP0] Decode batch. #running-req: 1, #token: 642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.20, #queue-req: 0, 
[2025-07-28 01:04:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.92, #queue-req: 0, 
[2025-07-28 01:04:19 DP0 TP0] Decode batch. #running-req: 1, #token: 682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.19, #queue-req: 0, 
[2025-07-28 01:04:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.57, #queue-req: 0, 
[2025-07-28 01:04:19 DP0 TP0] Decode batch. #running-req: 1, #token: 722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.91, #queue-req: 0, 
[2025-07-28 01:04:19 DP1 TP0] Decode batch. #running-req: 1, #token: 2010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.28, #queue-req: 0, 
[2025-07-28 01:04:19 DP0 TP0] Decode batch. #running-req: 1, #token: 762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.96, #queue-req: 0, 
[2025-07-28 01:04:19 DP1 TP0] Decode batch. #running-req: 1, #token: 2050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.28, #queue-req: 0, 
[2025-07-28 01:04:19 DP0 TP0] Decode batch. #running-req: 1, #token: 802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.49, #queue-req: 0, 
[2025-07-28 01:04:20 DP1 TP0] Decode batch. #running-req: 1, #token: 2090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.53, #queue-req: 0, 
[2025-07-28 01:04:20 DP0 TP0] Decode batch. #running-req: 1, #token: 842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.99, #queue-req: 0, 
[2025-07-28 01:04:20 DP1 TP0] Decode batch. #running-req: 1, #token: 2130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.50, #queue-req: 0, 
[2025-07-28 01:04:20 DP0 TP0] Decode batch. #running-req: 1, #token: 882, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.45, #queue-req: 0, 
[2025-07-28 01:04:20 DP1 TP0] Decode batch. #running-req: 1, #token: 2170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.43, #queue-req: 0, 
[2025-07-28 01:04:20 DP0 TP0] Decode batch. #running-req: 1, #token: 922, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.68, #queue-req: 0, 
[2025-07-28 01:04:20 DP1 TP0] Decode batch. #running-req: 1, #token: 2210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 235.11, #queue-req: 0, 
[2025-07-28 01:04:20 DP0 TP0] Decode batch. #running-req: 1, #token: 962, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.58, #queue-req: 0, 
[2025-07-28 01:04:20 DP1 TP0] Decode batch. #running-req: 1, #token: 2250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.24, #queue-req: 0, 
[2025-07-28 01:04:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1002, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.61, #queue-req: 0, 
[2025-07-28 01:04:20 DP1 TP0] Decode batch. #running-req: 1, #token: 2290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.34, #queue-req: 0, 
[2025-07-28 01:04:20] INFO:     127.0.0.1:40370 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:04:20 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 111, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:04:21 DP1 TP0] Decode batch. #running-req: 2, #token: 2476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 336.00, #queue-req: 0, 
[2025-07-28 01:04:21 DP1 TP0] Decode batch. #running-req: 2, #token: 2556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.07, #queue-req: 0, 
[2025-07-28 01:04:21 DP1 TP0] Decode batch. #running-req: 2, #token: 2636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.85, #queue-req: 0, 
[2025-07-28 01:04:21 DP1 TP0] Decode batch. #running-req: 2, #token: 2716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.95, #queue-req: 0, 
[2025-07-28 01:04:21 DP1 TP0] Decode batch. #running-req: 2, #token: 2796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.46, #queue-req: 0, 
[2025-07-28 01:04:21 DP1 TP0] Decode batch. #running-req: 2, #token: 2876, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.33, #queue-req: 0, 
[2025-07-28 01:04:22 DP1 TP0] Decode batch. #running-req: 2, #token: 2956, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.26, #queue-req: 0, 
[2025-07-28 01:04:22 DP1 TP0] Decode batch. #running-req: 2, #token: 3036, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.88, #queue-req: 0, 
[2025-07-28 01:04:22 DP1 TP0] Decode batch. #running-req: 2, #token: 3116, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.10, #queue-req: 0, 
[2025-07-28 01:04:22 DP1 TP0] Decode batch. #running-req: 2, #token: 3196, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.45, #queue-req: 0, 
[2025-07-28 01:04:22 DP1 TP0] Decode batch. #running-req: 2, #token: 3276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.44, #queue-req: 0, 
[2025-07-28 01:04:22 DP1 TP0] Decode batch. #running-req: 2, #token: 3356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.44, #queue-req: 0, 
[2025-07-28 01:04:23 DP1 TP0] Decode batch. #running-req: 2, #token: 3436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.47, #queue-req: 0, 
[2025-07-28 01:04:23 DP1 TP0] Decode batch. #running-req: 2, #token: 3516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.21, #queue-req: 0, 
[2025-07-28 01:04:23 DP1 TP0] Decode batch. #running-req: 2, #token: 3596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.40, #queue-req: 0, 
[2025-07-28 01:04:23 DP1 TP0] Decode batch. #running-req: 2, #token: 3676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.02, #queue-req: 0, 
[2025-07-28 01:04:23 DP1 TP0] Decode batch. #running-req: 2, #token: 3756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.64, #queue-req: 0, 
[2025-07-28 01:04:24 DP1 TP0] Decode batch. #running-req: 2, #token: 3836, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.23, #queue-req: 0, 
[2025-07-28 01:04:24 DP1 TP0] Decode batch. #running-req: 2, #token: 3916, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.11, #queue-req: 0, 
[2025-07-28 01:04:24 DP1 TP0] Decode batch. #running-req: 2, #token: 3996, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.20, #queue-req: 0, 
[2025-07-28 01:04:24 DP1 TP0] Decode batch. #running-req: 2, #token: 4076, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.07, #queue-req: 0, 
[2025-07-28 01:04:24 DP1 TP0] Decode batch. #running-req: 2, #token: 4156, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.06, #queue-req: 0, 
[2025-07-28 01:04:24 DP1 TP0] Decode batch. #running-req: 2, #token: 4236, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.69, #queue-req: 0, 
[2025-07-28 01:04:25 DP1 TP0] Decode batch. #running-req: 2, #token: 4316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.66, #queue-req: 0, 
[2025-07-28 01:04:25 DP1 TP0] Decode batch. #running-req: 2, #token: 4396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.47, #queue-req: 0, 
[2025-07-28 01:04:25 DP1 TP0] Decode batch. #running-req: 2, #token: 4476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.01, #queue-req: 0, 
[2025-07-28 01:04:25 DP1 TP0] Decode batch. #running-req: 2, #token: 4556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.81, #queue-req: 0, 
[2025-07-28 01:04:25 DP1 TP0] Decode batch. #running-req: 2, #token: 4636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.64, #queue-req: 0, 
[2025-07-28 01:04:25 DP1 TP0] Decode batch. #running-req: 2, #token: 4716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.31, #queue-req: 0, 
[2025-07-28 01:04:26 DP1 TP0] Decode batch. #running-req: 2, #token: 4796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.15, #queue-req: 0, 
[2025-07-28 01:04:26] INFO:     127.0.0.1:40366 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:04:26 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 95, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:04:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1431, token usage: 0.00, cuda graph: True, gen throughput (token/s): 350.09, #queue-req: 0, 
[2025-07-28 01:04:26 DP0 TP0] Decode batch. #running-req: 1, #token: 197, token usage: 0.00, cuda graph: True, gen throughput (token/s): 7.18, #queue-req: 0, 
[2025-07-28 01:04:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1471, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.84, #queue-req: 0, 
[2025-07-28 01:04:26 DP0 TP0] Decode batch. #running-req: 1, #token: 237, token usage: 0.00, cuda graph: True, gen throughput (token/s): 220.55, #queue-req: 0, 
[2025-07-28 01:04:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1511, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.88, #queue-req: 0, 
[2025-07-28 01:04:26 DP0 TP0] Decode batch. #running-req: 1, #token: 277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.73, #queue-req: 0, 
[2025-07-28 01:04:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.59, #queue-req: 0, 
[2025-07-28 01:04:26 DP0 TP0] Decode batch. #running-req: 1, #token: 317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.15, #queue-req: 0, 
[2025-07-28 01:04:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.60, #queue-req: 0, 
[2025-07-28 01:04:27 DP0 TP0] Decode batch. #running-req: 1, #token: 357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.31, #queue-req: 0, 
[2025-07-28 01:04:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.39, #queue-req: 0, 
[2025-07-28 01:04:27 DP0 TP0] Decode batch. #running-req: 1, #token: 397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.40, #queue-req: 0, 
[2025-07-28 01:04:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1671, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.46, #queue-req: 0, 
[2025-07-28 01:04:27 DP0 TP0] Decode batch. #running-req: 1, #token: 437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.98, #queue-req: 0, 
[2025-07-28 01:04:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1711, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.99, #queue-req: 0, 
[2025-07-28 01:04:27 DP0 TP0] Decode batch. #running-req: 1, #token: 477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.04, #queue-req: 0, 
[2025-07-28 01:04:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1751, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.07, #queue-req: 0, 
[2025-07-28 01:04:27 DP0 TP0] Decode batch. #running-req: 1, #token: 517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.00, #queue-req: 0, 
[2025-07-28 01:04:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1791, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.99, #queue-req: 0, 
[2025-07-28 01:04:27 DP0 TP0] Decode batch. #running-req: 1, #token: 557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.77, #queue-req: 0, 
[2025-07-28 01:04:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1831, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.39, #queue-req: 0, 
[2025-07-28 01:04:28 DP0 TP0] Decode batch. #running-req: 1, #token: 597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.03, #queue-req: 0, 
[2025-07-28 01:04:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1871, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.34, #queue-req: 0, 
[2025-07-28 01:04:28] INFO:     127.0.0.1:40378 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:04:28 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 89, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:04:28 DP0 TP0] Decode batch. #running-req: 1, #token: 637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.10, #queue-req: 0, 
[2025-07-28 01:04:28 DP1 TP0] Decode batch. #running-req: 1, #token: 191, token usage: 0.00, cuda graph: True, gen throughput (token/s): 181.76, #queue-req: 0, 
[2025-07-28 01:04:28 DP0 TP0] Decode batch. #running-req: 1, #token: 677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.15, #queue-req: 0, 
[2025-07-28 01:04:28 DP1 TP0] Decode batch. #running-req: 1, #token: 231, token usage: 0.00, cuda graph: True, gen throughput (token/s): 249.57, #queue-req: 0, 
[2025-07-28 01:04:28 DP0 TP0] Decode batch. #running-req: 1, #token: 717, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.35, #queue-req: 0, 
[2025-07-28 01:04:28 DP1 TP0] Decode batch. #running-req: 1, #token: 271, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.33, #queue-req: 0, 
[2025-07-28 01:04:28 DP0 TP0] Decode batch. #running-req: 1, #token: 757, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.60, #queue-req: 0, 
[2025-07-28 01:04:28 DP1 TP0] Decode batch. #running-req: 1, #token: 311, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.39, #queue-req: 0, 
[2025-07-28 01:04:28 DP0 TP0] Decode batch. #running-req: 1, #token: 797, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.50, #queue-req: 0, 
[2025-07-28 01:04:28 DP1 TP0] Decode batch. #running-req: 1, #token: 351, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.26, #queue-req: 0, 
[2025-07-28 01:04:29 DP0 TP0] Decode batch. #running-req: 1, #token: 837, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.30, #queue-req: 0, 
[2025-07-28 01:04:29 DP1 TP0] Decode batch. #running-req: 1, #token: 391, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.26, #queue-req: 0, 
[2025-07-28 01:04:29 DP0 TP0] Decode batch. #running-req: 1, #token: 877, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.13, #queue-req: 0, 
[2025-07-28 01:04:29 DP1 TP0] Decode batch. #running-req: 1, #token: 431, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.59, #queue-req: 0, 
[2025-07-28 01:04:29 DP0 TP0] Decode batch. #running-req: 1, #token: 917, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.43, #queue-req: 0, 
[2025-07-28 01:04:29 DP1 TP0] Decode batch. #running-req: 1, #token: 471, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.64, #queue-req: 0, 
[2025-07-28 01:04:29 DP0 TP0] Decode batch. #running-req: 1, #token: 957, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.50, #queue-req: 0, 
[2025-07-28 01:04:29 DP1 TP0] Decode batch. #running-req: 1, #token: 511, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.56, #queue-req: 0, 
[2025-07-28 01:04:29 DP0 TP0] Decode batch. #running-req: 1, #token: 997, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.65, #queue-req: 0, 
[2025-07-28 01:04:29 DP1 TP0] Decode batch. #running-req: 1, #token: 551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.86, #queue-req: 0, 
[2025-07-28 01:04:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1037, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.26, #queue-req: 0, 
[2025-07-28 01:04:29 DP1 TP0] Decode batch. #running-req: 1, #token: 591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.94, #queue-req: 0, 
[2025-07-28 01:04:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1077, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.31, #queue-req: 0, 
[2025-07-28 01:04:30 DP1 TP0] Decode batch. #running-req: 1, #token: 631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.77, #queue-req: 0, 
[2025-07-28 01:04:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1117, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.29, #queue-req: 0, 
[2025-07-28 01:04:30 DP1 TP0] Decode batch. #running-req: 1, #token: 671, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.95, #queue-req: 0, 
[2025-07-28 01:04:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1157, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.83, #queue-req: 0, 
[2025-07-28 01:04:30 DP1 TP0] Decode batch. #running-req: 1, #token: 711, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.25, #queue-req: 0, 
[2025-07-28 01:04:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1197, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.71, #queue-req: 0, 
[2025-07-28 01:04:30 DP1 TP0] Decode batch. #running-req: 1, #token: 751, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.17, #queue-req: 0, 
[2025-07-28 01:04:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1237, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.69, #queue-req: 0, 
[2025-07-28 01:04:30 DP1 TP0] Decode batch. #running-req: 1, #token: 791, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.97, #queue-req: 0, 
[2025-07-28 01:04:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 228.98, #queue-req: 0, 
[2025-07-28 01:04:30 DP1 TP0] Decode batch. #running-req: 1, #token: 831, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.52, #queue-req: 0, 
[2025-07-28 01:04:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.02, #queue-req: 0, 
[2025-07-28 01:04:31 DP1 TP0] Decode batch. #running-req: 1, #token: 871, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.56, #queue-req: 0, 
[2025-07-28 01:04:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.78, #queue-req: 0, 
[2025-07-28 01:04:31 DP1 TP0] Decode batch. #running-req: 1, #token: 911, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.68, #queue-req: 0, 
[2025-07-28 01:04:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.61, #queue-req: 0, 
[2025-07-28 01:04:31 DP1 TP0] Decode batch. #running-req: 1, #token: 951, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.92, #queue-req: 0, 
[2025-07-28 01:04:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.15, #queue-req: 0, 
[2025-07-28 01:04:31 DP1 TP0] Decode batch. #running-req: 1, #token: 991, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.96, #queue-req: 0, 
[2025-07-28 01:04:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.29, #queue-req: 0, 
[2025-07-28 01:04:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1031, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.39, #queue-req: 0, 
[2025-07-28 01:04:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.23, #queue-req: 0, 
[2025-07-28 01:04:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1071, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.67, #queue-req: 0, 
[2025-07-28 01:04:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.52, #queue-req: 0, 
[2025-07-28 01:04:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1111, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.73, #queue-req: 0, 
[2025-07-28 01:04:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.36, #queue-req: 0, 
[2025-07-28 01:04:32] INFO:     127.0.0.1:42152 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:04:32 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 115, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:04:32 DP0 TP0] Decode batch. #running-req: 2, #token: 1771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 276.31, #queue-req: 0, 
[2025-07-28 01:04:32 DP0 TP0] Decode batch. #running-req: 2, #token: 1851, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.54, #queue-req: 0, 
[2025-07-28 01:04:32 DP0 TP0] Decode batch. #running-req: 2, #token: 1931, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.74, #queue-req: 0, 
[2025-07-28 01:04:32 DP0 TP0] Decode batch. #running-req: 2, #token: 2011, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.81, #queue-req: 0, 
[2025-07-28 01:04:33 DP0 TP0] Decode batch. #running-req: 2, #token: 2091, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.57, #queue-req: 0, 
[2025-07-28 01:04:33 DP0 TP0] Decode batch. #running-req: 2, #token: 2171, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.49, #queue-req: 0, 
[2025-07-28 01:04:33 DP0 TP0] Decode batch. #running-req: 2, #token: 2251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.17, #queue-req: 0, 
[2025-07-28 01:04:33 DP0 TP0] Decode batch. #running-req: 2, #token: 2331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.37, #queue-req: 0, 
[2025-07-28 01:04:33 DP0 TP0] Decode batch. #running-req: 2, #token: 2411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.09, #queue-req: 0, 
[2025-07-28 01:04:33 DP0 TP0] Decode batch. #running-req: 2, #token: 2491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.16, #queue-req: 0, 
[2025-07-28 01:04:34 DP0 TP0] Decode batch. #running-req: 2, #token: 2571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.86, #queue-req: 0, 
[2025-07-28 01:04:34 DP0 TP0] Decode batch. #running-req: 2, #token: 2651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.85, #queue-req: 0, 
[2025-07-28 01:04:34 DP0 TP0] Decode batch. #running-req: 2, #token: 2731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.85, #queue-req: 0, 
[2025-07-28 01:04:34 DP0 TP0] Decode batch. #running-req: 2, #token: 2811, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.46, #queue-req: 0, 
[2025-07-28 01:04:34 DP0 TP0] Decode batch. #running-req: 2, #token: 2891, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.83, #queue-req: 0, 
[2025-07-28 01:04:34 DP0 TP0] Decode batch. #running-req: 2, #token: 2971, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.26, #queue-req: 0, 
[2025-07-28 01:04:35 DP0 TP0] Decode batch. #running-req: 2, #token: 3051, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.38, #queue-req: 0, 
[2025-07-28 01:04:35 DP0 TP0] Decode batch. #running-req: 2, #token: 3131, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.78, #queue-req: 0, 
[2025-07-28 01:04:35 DP0 TP0] Decode batch. #running-req: 2, #token: 3211, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.03, #queue-req: 0, 
[2025-07-28 01:04:35 DP0 TP0] Decode batch. #running-req: 2, #token: 3291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.05, #queue-req: 0, 
[2025-07-28 01:04:35 DP0 TP0] Decode batch. #running-req: 2, #token: 3371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.08, #queue-req: 0, 
[2025-07-28 01:04:35 DP0 TP0] Decode batch. #running-req: 2, #token: 3451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.23, #queue-req: 0, 
[2025-07-28 01:04:36 DP0 TP0] Decode batch. #running-req: 2, #token: 3531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.95, #queue-req: 0, 
[2025-07-28 01:04:36 DP0 TP0] Decode batch. #running-req: 2, #token: 3611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.10, #queue-req: 0, 
[2025-07-28 01:04:36 DP0 TP0] Decode batch. #running-req: 2, #token: 3691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.95, #queue-req: 0, 
[2025-07-28 01:04:36 DP0 TP0] Decode batch. #running-req: 2, #token: 3771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.71, #queue-req: 0, 
[2025-07-28 01:04:36 DP0 TP0] Decode batch. #running-req: 2, #token: 3851, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.91, #queue-req: 0, 
[2025-07-28 01:04:36 DP0 TP0] Decode batch. #running-req: 2, #token: 3931, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.74, #queue-req: 0, 
[2025-07-28 01:04:37 DP0 TP0] Decode batch. #running-req: 2, #token: 4011, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.65, #queue-req: 0, 
[2025-07-28 01:04:37 DP0 TP0] Decode batch. #running-req: 2, #token: 4091, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.86, #queue-req: 0, 
[2025-07-28 01:04:37 DP0 TP0] Decode batch. #running-req: 2, #token: 4171, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.11, #queue-req: 0, 
[2025-07-28 01:04:37 DP0 TP0] Decode batch. #running-req: 2, #token: 4251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.01, #queue-req: 0, 
[2025-07-28 01:04:37 DP0 TP0] Decode batch. #running-req: 2, #token: 4331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.61, #queue-req: 0, 
[2025-07-28 01:04:38 DP0 TP0] Decode batch. #running-req: 2, #token: 4411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.85, #queue-req: 0, 
[2025-07-28 01:04:38 DP0 TP0] Decode batch. #running-req: 2, #token: 4491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.26, #queue-req: 0, 
[2025-07-28 01:04:38 DP0 TP0] Decode batch. #running-req: 2, #token: 4571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.08, #queue-req: 0, 
[2025-07-28 01:04:38 DP0 TP0] Decode batch. #running-req: 2, #token: 4651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.13, #queue-req: 0, 
[2025-07-28 01:04:38 DP0 TP0] Decode batch. #running-req: 2, #token: 4731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.67, #queue-req: 0, 
[2025-07-28 01:04:38 DP0 TP0] Decode batch. #running-req: 2, #token: 4811, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.41, #queue-req: 0, 
[2025-07-28 01:04:39 DP0 TP0] Decode batch. #running-req: 2, #token: 4891, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.53, #queue-req: 0, 
[2025-07-28 01:04:39 DP0 TP0] Decode batch. #running-req: 2, #token: 4971, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.77, #queue-req: 0, 
[2025-07-28 01:04:39 DP0 TP0] Decode batch. #running-req: 2, #token: 5051, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.37, #queue-req: 0, 
[2025-07-28 01:04:39 DP0 TP0] Decode batch. #running-req: 2, #token: 5131, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.44, #queue-req: 0, 
[2025-07-28 01:04:39 DP0 TP0] Decode batch. #running-req: 2, #token: 5211, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.54, #queue-req: 0, 
[2025-07-28 01:04:39 DP0 TP0] Decode batch. #running-req: 2, #token: 5291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.24, #queue-req: 0, 
[2025-07-28 01:04:40 DP0 TP0] Decode batch. #running-req: 2, #token: 5371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.12, #queue-req: 0, 
[2025-07-28 01:04:40 DP0 TP0] Decode batch. #running-req: 2, #token: 5451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.21, #queue-req: 0, 
[2025-07-28 01:04:40 DP0 TP0] Decode batch. #running-req: 2, #token: 5531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.10, #queue-req: 0, 
[2025-07-28 01:04:40 DP0 TP0] Decode batch. #running-req: 2, #token: 5611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.24, #queue-req: 0, 
[2025-07-28 01:04:40 DP0 TP0] Decode batch. #running-req: 2, #token: 5691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.77, #queue-req: 0, 
[2025-07-28 01:04:40 DP0 TP0] Decode batch. #running-req: 2, #token: 5771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.92, #queue-req: 0, 
[2025-07-28 01:04:41 DP0 TP0] Decode batch. #running-req: 2, #token: 5851, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.10, #queue-req: 0, 
[2025-07-28 01:04:41 DP0 TP0] Decode batch. #running-req: 2, #token: 5931, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.59, #queue-req: 0, 
[2025-07-28 01:04:41 DP0 TP0] Decode batch. #running-req: 2, #token: 6011, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.89, #queue-req: 0, 
[2025-07-28 01:04:41 DP0 TP0] Decode batch. #running-req: 2, #token: 6091, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.56, #queue-req: 0, 
[2025-07-28 01:04:41 DP0 TP0] Decode batch. #running-req: 2, #token: 6171, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.83, #queue-req: 0, 
[2025-07-28 01:04:41 DP0 TP0] Decode batch. #running-req: 2, #token: 6251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.80, #queue-req: 0, 
[2025-07-28 01:04:42 DP0 TP0] Decode batch. #running-req: 2, #token: 6331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.55, #queue-req: 0, 
[2025-07-28 01:04:42 DP0 TP0] Decode batch. #running-req: 2, #token: 6411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.72, #queue-req: 0, 
[2025-07-28 01:04:42 DP0 TP0] Decode batch. #running-req: 2, #token: 6491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.27, #queue-req: 0, 
[2025-07-28 01:04:42 DP0 TP0] Decode batch. #running-req: 2, #token: 6571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.73, #queue-req: 0, 
[2025-07-28 01:04:42 DP0 TP0] Decode batch. #running-req: 2, #token: 6651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.03, #queue-req: 0, 
[2025-07-28 01:04:43 DP0 TP0] Decode batch. #running-req: 2, #token: 6731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.74, #queue-req: 0, 
[2025-07-28 01:04:43 DP0 TP0] Decode batch. #running-req: 2, #token: 6811, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.09, #queue-req: 0, 
[2025-07-28 01:04:43 DP0 TP0] Decode batch. #running-req: 2, #token: 6891, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.15, #queue-req: 0, 
[2025-07-28 01:04:43 DP0 TP0] Decode batch. #running-req: 2, #token: 6971, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.20, #queue-req: 0, 
[2025-07-28 01:04:43 DP0 TP0] Decode batch. #running-req: 2, #token: 7051, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.07, #queue-req: 0, 
[2025-07-28 01:04:43 DP0 TP0] Decode batch. #running-req: 2, #token: 7131, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.82, #queue-req: 0, 
[2025-07-28 01:04:44 DP0 TP0] Decode batch. #running-req: 2, #token: 7211, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.90, #queue-req: 0, 
[2025-07-28 01:04:44 DP0 TP0] Decode batch. #running-req: 2, #token: 7291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.60, #queue-req: 0, 
[2025-07-28 01:04:44 DP0 TP0] Decode batch. #running-req: 2, #token: 7371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.62, #queue-req: 0, 
[2025-07-28 01:04:44 DP0 TP0] Decode batch. #running-req: 2, #token: 7451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.67, #queue-req: 0, 
[2025-07-28 01:04:44 DP0 TP0] Decode batch. #running-req: 2, #token: 7531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.37, #queue-req: 0, 
[2025-07-28 01:04:44 DP0 TP0] Decode batch. #running-req: 2, #token: 7611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 459.92, #queue-req: 0, 
[2025-07-28 01:04:45 DP0 TP0] Decode batch. #running-req: 2, #token: 7691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 457.04, #queue-req: 0, 
[2025-07-28 01:04:45 DP0 TP0] Decode batch. #running-req: 2, #token: 7771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 455.87, #queue-req: 0, 
[2025-07-28 01:04:45 DP0 TP0] Decode batch. #running-req: 2, #token: 7851, token usage: 0.00, cuda graph: True, gen throughput (token/s): 456.52, #queue-req: 0, 
[2025-07-28 01:04:45 DP0 TP0] Decode batch. #running-req: 2, #token: 7931, token usage: 0.00, cuda graph: True, gen throughput (token/s): 458.51, #queue-req: 0, 
[2025-07-28 01:04:45 DP0 TP0] Decode batch. #running-req: 2, #token: 8011, token usage: 0.00, cuda graph: True, gen throughput (token/s): 459.68, #queue-req: 0, 
[2025-07-28 01:04:45 DP0 TP0] Decode batch. #running-req: 2, #token: 8091, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.84, #queue-req: 0, 
[2025-07-28 01:04:46 DP0 TP0] Decode batch. #running-req: 2, #token: 8171, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.19, #queue-req: 0, 
[2025-07-28 01:04:46 DP0 TP0] Decode batch. #running-req: 2, #token: 8251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.35, #queue-req: 0, 
[2025-07-28 01:04:46 DP0 TP0] Decode batch. #running-req: 2, #token: 8331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.33, #queue-req: 0, 
[2025-07-28 01:04:46 DP0 TP0] Decode batch. #running-req: 2, #token: 8411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.31, #queue-req: 0, 
[2025-07-28 01:04:46 DP0 TP0] Decode batch. #running-req: 2, #token: 8491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.71, #queue-req: 0, 
[2025-07-28 01:04:47 DP0 TP0] Decode batch. #running-req: 2, #token: 8571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.80, #queue-req: 0, 
[2025-07-28 01:04:47 DP0 TP0] Decode batch. #running-req: 2, #token: 8651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.92, #queue-req: 0, 
[2025-07-28 01:04:47 DP0 TP0] Decode batch. #running-req: 2, #token: 8731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.27, #queue-req: 0, 
[2025-07-28 01:04:47 DP0 TP0] Decode batch. #running-req: 2, #token: 8811, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.06, #queue-req: 0, 
[2025-07-28 01:04:47 DP0 TP0] Decode batch. #running-req: 2, #token: 8891, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.53, #queue-req: 0, 
[2025-07-28 01:04:47 DP0 TP0] Decode batch. #running-req: 2, #token: 8971, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.08, #queue-req: 0, 
[2025-07-28 01:04:48 DP0 TP0] Decode batch. #running-req: 2, #token: 9051, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.55, #queue-req: 0, 
[2025-07-28 01:04:48 DP0 TP0] Decode batch. #running-req: 2, #token: 9131, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.68, #queue-req: 0, 
[2025-07-28 01:04:48 DP0 TP0] Decode batch. #running-req: 2, #token: 9211, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.83, #queue-req: 0, 
[2025-07-28 01:04:48 DP0 TP0] Decode batch. #running-req: 2, #token: 9291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.65, #queue-req: 0, 
[2025-07-28 01:04:48 DP0 TP0] Decode batch. #running-req: 2, #token: 9371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.39, #queue-req: 0, 
[2025-07-28 01:04:48 DP0 TP0] Decode batch. #running-req: 2, #token: 9451, token usage: 0.01, cuda graph: True, gen throughput (token/s): 460.67, #queue-req: 0, 
[2025-07-28 01:04:49 DP0 TP0] Decode batch. #running-req: 2, #token: 9531, token usage: 0.01, cuda graph: True, gen throughput (token/s): 460.65, #queue-req: 0, 
[2025-07-28 01:04:49 DP0 TP0] Decode batch. #running-req: 2, #token: 9611, token usage: 0.01, cuda graph: True, gen throughput (token/s): 460.61, #queue-req: 0, 
[2025-07-28 01:04:49 DP0 TP0] Decode batch. #running-req: 2, #token: 9691, token usage: 0.01, cuda graph: True, gen throughput (token/s): 460.48, #queue-req: 0, 
[2025-07-28 01:04:49 DP0 TP0] Decode batch. #running-req: 2, #token: 9771, token usage: 0.01, cuda graph: True, gen throughput (token/s): 460.66, #queue-req: 0, 
[2025-07-28 01:04:49 DP0 TP0] Decode batch. #running-req: 2, #token: 9851, token usage: 0.01, cuda graph: True, gen throughput (token/s): 460.23, #queue-req: 0, 
[2025-07-28 01:04:49 DP0 TP0] Decode batch. #running-req: 2, #token: 9931, token usage: 0.01, cuda graph: True, gen throughput (token/s): 460.24, #queue-req: 0, 
[2025-07-28 01:04:50 DP0 TP0] Decode batch. #running-req: 2, #token: 10011, token usage: 0.01, cuda graph: True, gen throughput (token/s): 460.00, #queue-req: 0, 
[2025-07-28 01:04:50 DP0 TP0] Decode batch. #running-req: 2, #token: 10091, token usage: 0.01, cuda graph: True, gen throughput (token/s): 460.53, #queue-req: 0, 
[2025-07-28 01:04:50 DP0 TP0] Decode batch. #running-req: 2, #token: 10171, token usage: 0.01, cuda graph: True, gen throughput (token/s): 460.43, #queue-req: 0, 
[2025-07-28 01:04:50 DP0 TP0] Decode batch. #running-req: 2, #token: 10251, token usage: 0.01, cuda graph: True, gen throughput (token/s): 460.53, #queue-req: 0, 
[2025-07-28 01:04:50 DP0 TP0] Decode batch. #running-req: 2, #token: 10331, token usage: 0.01, cuda graph: True, gen throughput (token/s): 460.17, #queue-req: 0, 
[2025-07-28 01:04:51 DP0 TP0] Decode batch. #running-req: 2, #token: 10411, token usage: 0.01, cuda graph: True, gen throughput (token/s): 460.30, #queue-req: 0, 
[2025-07-28 01:04:51] INFO:     127.0.0.1:42156 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:04:51 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 164, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:04:51 DP0 TP0] Decode batch. #running-req: 1, #token: 5997, token usage: 0.00, cuda graph: True, gen throughput (token/s): 251.52, #queue-req: 0, 
[2025-07-28 01:04:51 DP1 TP0] Decode batch. #running-req: 1, #token: 254, token usage: 0.00, cuda graph: True, gen throughput (token/s): 2.09, #queue-req: 0, 
[2025-07-28 01:04:51 DP0 TP0] Decode batch. #running-req: 1, #token: 6037, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.02, #queue-req: 0, 
[2025-07-28 01:04:51 DP1 TP0] Decode batch. #running-req: 1, #token: 294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 215.99, #queue-req: 0, 
[2025-07-28 01:04:51 DP0 TP0] Decode batch. #running-req: 1, #token: 6077, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.46, #queue-req: 0, 
[2025-07-28 01:04:51 DP1 TP0] Decode batch. #running-req: 1, #token: 334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.61, #queue-req: 0, 
[2025-07-28 01:04:51 DP0 TP0] Decode batch. #running-req: 1, #token: 6117, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.39, #queue-req: 0, 
[2025-07-28 01:04:51 DP1 TP0] Decode batch. #running-req: 1, #token: 374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.14, #queue-req: 0, 
[2025-07-28 01:04:51 DP0 TP0] Decode batch. #running-req: 1, #token: 6157, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.67, #queue-req: 0, 
[2025-07-28 01:04:51 DP1 TP0] Decode batch. #running-req: 1, #token: 414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.64, #queue-req: 0, 
[2025-07-28 01:04:52 DP0 TP0] Decode batch. #running-req: 1, #token: 6197, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.43, #queue-req: 0, 
[2025-07-28 01:04:52 DP1 TP0] Decode batch. #running-req: 1, #token: 454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.11, #queue-req: 0, 
[2025-07-28 01:04:52 DP0 TP0] Decode batch. #running-req: 1, #token: 6237, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.44, #queue-req: 0, 
[2025-07-28 01:04:52 DP1 TP0] Decode batch. #running-req: 1, #token: 494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.07, #queue-req: 0, 
[2025-07-28 01:04:52 DP0 TP0] Decode batch. #running-req: 1, #token: 6277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.47, #queue-req: 0, 
[2025-07-28 01:04:52 DP1 TP0] Decode batch. #running-req: 1, #token: 534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.62, #queue-req: 0, 
[2025-07-28 01:04:52 DP0 TP0] Decode batch. #running-req: 1, #token: 6317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.74, #queue-req: 0, 
[2025-07-28 01:04:52 DP1 TP0] Decode batch. #running-req: 1, #token: 574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.52, #queue-req: 0, 
[2025-07-28 01:04:52 DP0 TP0] Decode batch. #running-req: 1, #token: 6357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.82, #queue-req: 0, 
[2025-07-28 01:04:52 DP1 TP0] Decode batch. #running-req: 1, #token: 614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.47, #queue-req: 0, 
[2025-07-28 01:04:52 DP0 TP0] Decode batch. #running-req: 1, #token: 6397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.39, #queue-req: 0, 
[2025-07-28 01:04:52 DP1 TP0] Decode batch. #running-req: 1, #token: 654, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.72, #queue-req: 0, 
[2025-07-28 01:04:53 DP1 TP0] Decode batch. #running-req: 1, #token: 694, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.70, #queue-req: 0, 
[2025-07-28 01:04:53 DP0 TP0] Decode batch. #running-req: 1, #token: 6437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.60, #queue-req: 0, 
[2025-07-28 01:04:53 DP1 TP0] Decode batch. #running-req: 1, #token: 734, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.20, #queue-req: 0, 
[2025-07-28 01:04:53 DP0 TP0] Decode batch. #running-req: 1, #token: 6477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.46, #queue-req: 0, 
[2025-07-28 01:04:53 DP1 TP0] Decode batch. #running-req: 1, #token: 774, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.35, #queue-req: 0, 
[2025-07-28 01:04:53 DP0 TP0] Decode batch. #running-req: 1, #token: 6517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.52, #queue-req: 0, 
[2025-07-28 01:04:53 DP1 TP0] Decode batch. #running-req: 1, #token: 814, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.12, #queue-req: 0, 
[2025-07-28 01:04:53 DP0 TP0] Decode batch. #running-req: 1, #token: 6557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.79, #queue-req: 0, 
[2025-07-28 01:04:53 DP1 TP0] Decode batch. #running-req: 1, #token: 854, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.94, #queue-req: 0, 
[2025-07-28 01:04:53 DP0 TP0] Decode batch. #running-req: 1, #token: 6597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.89, #queue-req: 0, 
[2025-07-28 01:04:53 DP1 TP0] Decode batch. #running-req: 1, #token: 894, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.82, #queue-req: 0, 
[2025-07-28 01:04:53 DP0 TP0] Decode batch. #running-req: 1, #token: 6637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.66, #queue-req: 0, 
[2025-07-28 01:04:54 DP1 TP0] Decode batch. #running-req: 1, #token: 934, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.50, #queue-req: 0, 
[2025-07-28 01:04:54 DP0 TP0] Decode batch. #running-req: 1, #token: 6677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.74, #queue-req: 0, 
[2025-07-28 01:04:54 DP1 TP0] Decode batch. #running-req: 1, #token: 974, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.61, #queue-req: 0, 
[2025-07-28 01:04:54 DP0 TP0] Decode batch. #running-req: 1, #token: 6717, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.33, #queue-req: 0, 
[2025-07-28 01:04:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1014, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.63, #queue-req: 0, 
[2025-07-28 01:04:54 DP0 TP0] Decode batch. #running-req: 1, #token: 6757, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.34, #queue-req: 0, 
[2025-07-28 01:04:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1054, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.75, #queue-req: 0, 
[2025-07-28 01:04:54 DP0 TP0] Decode batch. #running-req: 1, #token: 6797, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.42, #queue-req: 0, 
[2025-07-28 01:04:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1094, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.48, #queue-req: 0, 
[2025-07-28 01:04:54 DP0 TP0] Decode batch. #running-req: 1, #token: 6837, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.74, #queue-req: 0, 
[2025-07-28 01:04:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1134, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.56, #queue-req: 0, 
[2025-07-28 01:04:54 DP0 TP0] Decode batch. #running-req: 1, #token: 6877, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.63, #queue-req: 0, 
[2025-07-28 01:04:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1174, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.56, #queue-req: 0, 
[2025-07-28 01:04:55 DP0 TP0] Decode batch. #running-req: 1, #token: 6917, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.73, #queue-req: 0, 
[2025-07-28 01:04:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1214, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.98, #queue-req: 0, 
[2025-07-28 01:04:55 DP0 TP0] Decode batch. #running-req: 1, #token: 6957, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.39, #queue-req: 0, 
[2025-07-28 01:04:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1254, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.95, #queue-req: 0, 
[2025-07-28 01:04:55 DP0 TP0] Decode batch. #running-req: 1, #token: 6997, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.43, #queue-req: 0, 
[2025-07-28 01:04:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.60, #queue-req: 0, 
[2025-07-28 01:04:55 DP0 TP0] Decode batch. #running-req: 1, #token: 7037, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.37, #queue-req: 0, 
[2025-07-28 01:04:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.32, #queue-req: 0, 
[2025-07-28 01:04:55 DP0 TP0] Decode batch. #running-req: 1, #token: 7077, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.61, #queue-req: 0, 
[2025-07-28 01:04:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.37, #queue-req: 0, 
[2025-07-28 01:04:55 DP0 TP0] Decode batch. #running-req: 1, #token: 7117, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.61, #queue-req: 0, 
[2025-07-28 01:04:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.55, #queue-req: 0, 
[2025-07-28 01:04:56 DP0 TP0] Decode batch. #running-req: 1, #token: 7157, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.75, #queue-req: 0, 
[2025-07-28 01:04:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.81, #queue-req: 0, 
[2025-07-28 01:04:56 DP0 TP0] Decode batch. #running-req: 1, #token: 7197, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.99, #queue-req: 0, 
[2025-07-28 01:04:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.88, #queue-req: 0, 
[2025-07-28 01:04:56 DP0 TP0] Decode batch. #running-req: 1, #token: 7237, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.82, #queue-req: 0, 
[2025-07-28 01:04:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.86, #queue-req: 0, 
[2025-07-28 01:04:56 DP0 TP0] Decode batch. #running-req: 1, #token: 7277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.99, #queue-req: 0, 
[2025-07-28 01:04:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.66, #queue-req: 0, 
[2025-07-28 01:04:56 DP0 TP0] Decode batch. #running-req: 1, #token: 7317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.22, #queue-req: 0, 
[2025-07-28 01:04:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.00, #queue-req: 0, 
[2025-07-28 01:04:56 DP0 TP0] Decode batch. #running-req: 1, #token: 7357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.10, #queue-req: 0, 
[2025-07-28 01:04:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1654, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.67, #queue-req: 0, 
[2025-07-28 01:04:57 DP0 TP0] Decode batch. #running-req: 1, #token: 7397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 225.48, #queue-req: 0, 
[2025-07-28 01:04:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1694, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.24, #queue-req: 0, 
[2025-07-28 01:04:57 DP0 TP0] Decode batch. #running-req: 1, #token: 7437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.63, #queue-req: 0, 
[2025-07-28 01:04:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1734, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.54, #queue-req: 0, 
[2025-07-28 01:04:57 DP0 TP0] Decode batch. #running-req: 1, #token: 7477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.79, #queue-req: 0, 
[2025-07-28 01:04:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1774, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.48, #queue-req: 0, 
[2025-07-28 01:04:57 DP0 TP0] Decode batch. #running-req: 1, #token: 7517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.85, #queue-req: 0, 
[2025-07-28 01:04:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1814, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.25, #queue-req: 0, 
[2025-07-28 01:04:57 DP0 TP0] Decode batch. #running-req: 1, #token: 7557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.69, #queue-req: 0, 
[2025-07-28 01:04:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1854, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.69, #queue-req: 0, 
[2025-07-28 01:04:57 DP0 TP0] Decode batch. #running-req: 1, #token: 7597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.92, #queue-req: 0, 
[2025-07-28 01:04:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1894, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.77, #queue-req: 0, 
[2025-07-28 01:04:58 DP0 TP0] Decode batch. #running-req: 1, #token: 7637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.93, #queue-req: 0, 
[2025-07-28 01:04:58 DP1 TP0] Decode batch. #running-req: 1, #token: 1934, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.10, #queue-req: 0, 
[2025-07-28 01:04:58 DP0 TP0] Decode batch. #running-req: 1, #token: 7677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.91, #queue-req: 0, 
[2025-07-28 01:04:58] INFO:     127.0.0.1:46356 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:04:58 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 143, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:04:58 DP0 TP0] Decode batch. #running-req: 2, #token: 7886, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.14, #queue-req: 0, 
[2025-07-28 01:04:58 DP0 TP0] Decode batch. #running-req: 2, #token: 7966, token usage: 0.00, cuda graph: True, gen throughput (token/s): 458.83, #queue-req: 0, 
[2025-07-28 01:04:58 DP0 TP0] Decode batch. #running-req: 2, #token: 8046, token usage: 0.00, cuda graph: True, gen throughput (token/s): 458.96, #queue-req: 0, 
[2025-07-28 01:04:58 DP0 TP0] Decode batch. #running-req: 2, #token: 8126, token usage: 0.00, cuda graph: True, gen throughput (token/s): 458.60, #queue-req: 0, 
[2025-07-28 01:04:59 DP0 TP0] Decode batch. #running-req: 2, #token: 8206, token usage: 0.00, cuda graph: True, gen throughput (token/s): 457.34, #queue-req: 0, 
[2025-07-28 01:04:59 DP0 TP0] Decode batch. #running-req: 2, #token: 8286, token usage: 0.00, cuda graph: True, gen throughput (token/s): 458.27, #queue-req: 0, 
[2025-07-28 01:04:59 DP0 TP0] Decode batch. #running-req: 2, #token: 8366, token usage: 0.00, cuda graph: True, gen throughput (token/s): 458.36, #queue-req: 0, 
[2025-07-28 01:04:59 DP0 TP0] Decode batch. #running-req: 2, #token: 8446, token usage: 0.00, cuda graph: True, gen throughput (token/s): 458.21, #queue-req: 0, 
[2025-07-28 01:04:59 DP0 TP0] Decode batch. #running-req: 2, #token: 8526, token usage: 0.00, cuda graph: True, gen throughput (token/s): 458.03, #queue-req: 0, 
[2025-07-28 01:05:00 DP0 TP0] Decode batch. #running-req: 2, #token: 8606, token usage: 0.00, cuda graph: True, gen throughput (token/s): 458.26, #queue-req: 0, 
[2025-07-28 01:05:00 DP0 TP0] Decode batch. #running-req: 2, #token: 8686, token usage: 0.00, cuda graph: True, gen throughput (token/s): 457.90, #queue-req: 0, 
[2025-07-28 01:05:00 DP0 TP0] Decode batch. #running-req: 2, #token: 8766, token usage: 0.00, cuda graph: True, gen throughput (token/s): 458.18, #queue-req: 0, 
[2025-07-28 01:05:00 DP0 TP0] Decode batch. #running-req: 2, #token: 8846, token usage: 0.00, cuda graph: True, gen throughput (token/s): 457.78, #queue-req: 0, 
[2025-07-28 01:05:00 DP0 TP0] Decode batch. #running-req: 2, #token: 8926, token usage: 0.00, cuda graph: True, gen throughput (token/s): 456.75, #queue-req: 0, 
[2025-07-28 01:05:00 DP0 TP0] Decode batch. #running-req: 2, #token: 9006, token usage: 0.00, cuda graph: True, gen throughput (token/s): 456.83, #queue-req: 0, 
[2025-07-28 01:05:01 DP0 TP0] Decode batch. #running-req: 2, #token: 9086, token usage: 0.00, cuda graph: True, gen throughput (token/s): 456.69, #queue-req: 0, 
[2025-07-28 01:05:01 DP0 TP0] Decode batch. #running-req: 2, #token: 9166, token usage: 0.00, cuda graph: True, gen throughput (token/s): 456.92, #queue-req: 0, 
[2025-07-28 01:05:01] INFO:     127.0.0.1:42148 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:05:01 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 117, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:05:01 DP0 TP0] Decode batch. #running-req: 1, #token: 934, token usage: 0.00, cuda graph: True, gen throughput (token/s): 332.63, #queue-req: 0, 
[2025-07-28 01:05:01 DP1 TP0] Decode batch. #running-req: 1, #token: 214, token usage: 0.00, cuda graph: True, gen throughput (token/s): 12.10, #queue-req: 0, 
[2025-07-28 01:05:01 DP0 TP0] Decode batch. #running-req: 1, #token: 974, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.26, #queue-req: 0, 
[2025-07-28 01:05:01 DP1 TP0] Decode batch. #running-req: 1, #token: 254, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.91, #queue-req: 0, 
[2025-07-28 01:05:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1014, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.76, #queue-req: 0, 
[2025-07-28 01:05:01 DP1 TP0] Decode batch. #running-req: 1, #token: 294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.66, #queue-req: 0, 
[2025-07-28 01:05:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1054, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.17, #queue-req: 0, 
[2025-07-28 01:05:01 DP1 TP0] Decode batch. #running-req: 1, #token: 334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.69, #queue-req: 0, 
[2025-07-28 01:05:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1094, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.79, #queue-req: 0, 
[2025-07-28 01:05:02 DP1 TP0] Decode batch. #running-req: 1, #token: 374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.54, #queue-req: 0, 
[2025-07-28 01:05:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1134, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.81, #queue-req: 0, 
[2025-07-28 01:05:02 DP1 TP0] Decode batch. #running-req: 1, #token: 414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.79, #queue-req: 0, 
[2025-07-28 01:05:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1174, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.02, #queue-req: 0, 
[2025-07-28 01:05:02 DP1 TP0] Decode batch. #running-req: 1, #token: 454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.89, #queue-req: 0, 
[2025-07-28 01:05:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1214, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.20, #queue-req: 0, 
[2025-07-28 01:05:02 DP1 TP0] Decode batch. #running-req: 1, #token: 494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.15, #queue-req: 0, 
[2025-07-28 01:05:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1254, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.28, #queue-req: 0, 
[2025-07-28 01:05:02 DP1 TP0] Decode batch. #running-req: 1, #token: 534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.09, #queue-req: 0, 
[2025-07-28 01:05:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.15, #queue-req: 0, 
[2025-07-28 01:05:02 DP1 TP0] Decode batch. #running-req: 1, #token: 574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.12, #queue-req: 0, 
[2025-07-28 01:05:03 DP0 TP0] Decode batch. #running-req: 1, #token: 1334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.86, #queue-req: 0, 
[2025-07-28 01:05:03 DP1 TP0] Decode batch. #running-req: 1, #token: 614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.23, #queue-req: 0, 
[2025-07-28 01:05:03 DP0 TP0] Decode batch. #running-req: 1, #token: 1374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.86, #queue-req: 0, 
[2025-07-28 01:05:03 DP1 TP0] Decode batch. #running-req: 1, #token: 654, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.17, #queue-req: 0, 
[2025-07-28 01:05:03 DP0 TP0] Decode batch. #running-req: 1, #token: 1414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.89, #queue-req: 0, 
[2025-07-28 01:05:03 DP1 TP0] Decode batch. #running-req: 1, #token: 694, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.36, #queue-req: 0, 
[2025-07-28 01:05:03 DP0 TP0] Decode batch. #running-req: 1, #token: 1454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.30, #queue-req: 0, 
[2025-07-28 01:05:03 DP1 TP0] Decode batch. #running-req: 1, #token: 734, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.58, #queue-req: 0, 
[2025-07-28 01:05:03 DP0 TP0] Decode batch. #running-req: 1, #token: 1494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.23, #queue-req: 0, 
[2025-07-28 01:05:03 DP1 TP0] Decode batch. #running-req: 1, #token: 774, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.33, #queue-req: 0, 
[2025-07-28 01:05:03 DP0 TP0] Decode batch. #running-req: 1, #token: 1534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.24, #queue-req: 0, 
[2025-07-28 01:05:03 DP1 TP0] Decode batch. #running-req: 1, #token: 814, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.81, #queue-req: 0, 
[2025-07-28 01:05:04 DP0 TP0] Decode batch. #running-req: 1, #token: 1574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.88, #queue-req: 0, 
[2025-07-28 01:05:04 DP1 TP0] Decode batch. #running-req: 1, #token: 854, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.80, #queue-req: 0, 
[2025-07-28 01:05:04 DP0 TP0] Decode batch. #running-req: 1, #token: 1614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.77, #queue-req: 0, 
[2025-07-28 01:05:04 DP1 TP0] Decode batch. #running-req: 1, #token: 894, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.81, #queue-req: 0, 
[2025-07-28 01:05:04 DP0 TP0] Decode batch. #running-req: 1, #token: 1654, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.55, #queue-req: 0, 
[2025-07-28 01:05:04 DP1 TP0] Decode batch. #running-req: 1, #token: 934, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.15, #queue-req: 0, 
[2025-07-28 01:05:04 DP0 TP0] Decode batch. #running-req: 1, #token: 1694, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.89, #queue-req: 0, 
[2025-07-28 01:05:04 DP1 TP0] Decode batch. #running-req: 1, #token: 974, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.16, #queue-req: 0, 
[2025-07-28 01:05:04 DP0 TP0] Decode batch. #running-req: 1, #token: 1734, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.11, #queue-req: 0, 
[2025-07-28 01:05:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1014, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.05, #queue-req: 0, 
[2025-07-28 01:05:04] INFO:     127.0.0.1:38898 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:05:04 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 125, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:05:04 DP0 TP0] Decode batch. #running-req: 1, #token: 234, token usage: 0.00, cuda graph: True, gen throughput (token/s): 181.63, #queue-req: 0, 
[2025-07-28 01:05:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1054, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.41, #queue-req: 0, 
[2025-07-28 01:05:04] INFO:     127.0.0.1:38914 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:05:04 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 196, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:05:05 DP0 TP0] Decode batch. #running-req: 1, #token: 274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.59, #queue-req: 0, 
[2025-07-28 01:05:05 DP1 TP0] Decode batch. #running-req: 1, #token: 323, token usage: 0.00, cuda graph: True, gen throughput (token/s): 174.82, #queue-req: 0, 
[2025-07-28 01:05:05 DP0 TP0] Decode batch. #running-req: 1, #token: 314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.38, #queue-req: 0, 
[2025-07-28 01:05:05 DP1 TP0] Decode batch. #running-req: 1, #token: 363, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.02, #queue-req: 0, 
[2025-07-28 01:05:05 DP0 TP0] Decode batch. #running-req: 1, #token: 354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.59, #queue-req: 0, 
[2025-07-28 01:05:05 DP1 TP0] Decode batch. #running-req: 1, #token: 403, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.12, #queue-req: 0, 
[2025-07-28 01:05:05 DP0 TP0] Decode batch. #running-req: 1, #token: 394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.87, #queue-req: 0, 
[2025-07-28 01:05:05 DP1 TP0] Decode batch. #running-req: 1, #token: 443, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.32, #queue-req: 0, 
[2025-07-28 01:05:05 DP0 TP0] Decode batch. #running-req: 1, #token: 434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.74, #queue-req: 0, 
[2025-07-28 01:05:05 DP1 TP0] Decode batch. #running-req: 1, #token: 483, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.19, #queue-req: 0, 
[2025-07-28 01:05:05 DP0 TP0] Decode batch. #running-req: 1, #token: 474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.61, #queue-req: 0, 
[2025-07-28 01:05:05 DP1 TP0] Decode batch. #running-req: 1, #token: 523, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.78, #queue-req: 0, 
[2025-07-28 01:05:06 DP0 TP0] Decode batch. #running-req: 1, #token: 514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.41, #queue-req: 0, 
[2025-07-28 01:05:06 DP1 TP0] Decode batch. #running-req: 1, #token: 563, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.30, #queue-req: 0, 
[2025-07-28 01:05:06 DP0 TP0] Decode batch. #running-req: 1, #token: 554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.94, #queue-req: 0, 
[2025-07-28 01:05:06 DP1 TP0] Decode batch. #running-req: 1, #token: 603, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.46, #queue-req: 0, 
[2025-07-28 01:05:06 DP0 TP0] Decode batch. #running-req: 1, #token: 594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.73, #queue-req: 0, 
[2025-07-28 01:05:06 DP1 TP0] Decode batch. #running-req: 1, #token: 643, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.45, #queue-req: 0, 
[2025-07-28 01:05:06 DP0 TP0] Decode batch. #running-req: 1, #token: 634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.77, #queue-req: 0, 
[2025-07-28 01:05:06 DP1 TP0] Decode batch. #running-req: 1, #token: 683, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.77, #queue-req: 0, 
[2025-07-28 01:05:06 DP0 TP0] Decode batch. #running-req: 1, #token: 674, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.08, #queue-req: 0, 
[2025-07-28 01:05:06 DP1 TP0] Decode batch. #running-req: 1, #token: 723, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.65, #queue-req: 0, 
[2025-07-28 01:05:06 DP0 TP0] Decode batch. #running-req: 1, #token: 714, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.21, #queue-req: 0, 
[2025-07-28 01:05:06 DP1 TP0] Decode batch. #running-req: 1, #token: 763, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.63, #queue-req: 0, 
[2025-07-28 01:05:07 DP0 TP0] Decode batch. #running-req: 1, #token: 754, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.24, #queue-req: 0, 
[2025-07-28 01:05:07 DP1 TP0] Decode batch. #running-req: 1, #token: 803, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.19, #queue-req: 0, 
[2025-07-28 01:05:07 DP0 TP0] Decode batch. #running-req: 1, #token: 794, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.72, #queue-req: 0, 
[2025-07-28 01:05:07 DP1 TP0] Decode batch. #running-req: 1, #token: 843, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.23, #queue-req: 0, 
[2025-07-28 01:05:07 DP0 TP0] Decode batch. #running-req: 1, #token: 834, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.55, #queue-req: 0, 
[2025-07-28 01:05:07 DP1 TP0] Decode batch. #running-req: 1, #token: 883, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.39, #queue-req: 0, 
[2025-07-28 01:05:07 DP0 TP0] Decode batch. #running-req: 1, #token: 874, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.58, #queue-req: 0, 
[2025-07-28 01:05:07 DP1 TP0] Decode batch. #running-req: 1, #token: 923, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.64, #queue-req: 0, 
[2025-07-28 01:05:07 DP0 TP0] Decode batch. #running-req: 1, #token: 914, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.71, #queue-req: 0, 
[2025-07-28 01:05:07 DP1 TP0] Decode batch. #running-req: 1, #token: 963, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.78, #queue-req: 0, 
[2025-07-28 01:05:07 DP0 TP0] Decode batch. #running-req: 1, #token: 954, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.98, #queue-req: 0, 
[2025-07-28 01:05:07 DP1 TP0] Decode batch. #running-req: 1, #token: 1003, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.86, #queue-req: 0, 
[2025-07-28 01:05:08 DP0 TP0] Decode batch. #running-req: 1, #token: 994, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.86, #queue-req: 0, 
[2025-07-28 01:05:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1043, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.23, #queue-req: 0, 
[2025-07-28 01:05:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1034, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.44, #queue-req: 0, 
[2025-07-28 01:05:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1083, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.59, #queue-req: 0, 
[2025-07-28 01:05:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1074, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.68, #queue-req: 0, 
[2025-07-28 01:05:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1123, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.80, #queue-req: 0, 
[2025-07-28 01:05:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1114, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.70, #queue-req: 0, 
[2025-07-28 01:05:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1163, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.79, #queue-req: 0, 
[2025-07-28 01:05:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1154, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.65, #queue-req: 0, 
[2025-07-28 01:05:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1203, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.06, #queue-req: 0, 
[2025-07-28 01:05:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1194, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.18, #queue-req: 0, 
[2025-07-28 01:05:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1243, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.03, #queue-req: 0, 
[2025-07-28 01:05:09 DP0 TP0] Decode batch. #running-req: 1, #token: 1234, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.92, #queue-req: 0, 
[2025-07-28 01:05:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1283, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.96, #queue-req: 0, 
[2025-07-28 01:05:09 DP0 TP0] Decode batch. #running-req: 1, #token: 1274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.12, #queue-req: 0, 
[2025-07-28 01:05:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1323, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.54, #queue-req: 0, 
[2025-07-28 01:05:09 DP0 TP0] Decode batch. #running-req: 1, #token: 1314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.43, #queue-req: 0, 
[2025-07-28 01:05:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1363, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.70, #queue-req: 0, 
[2025-07-28 01:05:09 DP0 TP0] Decode batch. #running-req: 1, #token: 1354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.78, #queue-req: 0, 
[2025-07-28 01:05:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1403, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.57, #queue-req: 0, 
[2025-07-28 01:05:09 DP0 TP0] Decode batch. #running-req: 1, #token: 1394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.51, #queue-req: 0, 
[2025-07-28 01:05:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1443, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.94, #queue-req: 0, 
[2025-07-28 01:05:09 DP0 TP0] Decode batch. #running-req: 1, #token: 1434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.76, #queue-req: 0, 
[2025-07-28 01:05:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1483, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.97, #queue-req: 0, 
[2025-07-28 01:05:09 DP0 TP0] Decode batch. #running-req: 1, #token: 1474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.47, #queue-req: 0, 
[2025-07-28 01:05:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1523, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.89, #queue-req: 0, 
[2025-07-28 01:05:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.38, #queue-req: 0, 
[2025-07-28 01:05:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1563, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.49, #queue-req: 0, 
[2025-07-28 01:05:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.69, #queue-req: 0, 
[2025-07-28 01:05:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1603, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.32, #queue-req: 0, 
[2025-07-28 01:05:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.46, #queue-req: 0, 
[2025-07-28 01:05:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1643, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.70, #queue-req: 0, 
[2025-07-28 01:05:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.52, #queue-req: 0, 
[2025-07-28 01:05:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1683, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.80, #queue-req: 0, 
[2025-07-28 01:05:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1674, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.44, #queue-req: 0, 
[2025-07-28 01:05:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1723, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.05, #queue-req: 0, 
[2025-07-28 01:05:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1714, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.74, #queue-req: 0, 
[2025-07-28 01:05:11 DP1 TP0] Decode batch. #running-req: 1, #token: 1763, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.20, #queue-req: 0, 
[2025-07-28 01:05:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1754, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.90, #queue-req: 0, 
[2025-07-28 01:05:11 DP1 TP0] Decode batch. #running-req: 1, #token: 1803, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.82, #queue-req: 0, 
[2025-07-28 01:05:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1794, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.90, #queue-req: 0, 
[2025-07-28 01:05:11 DP1 TP0] Decode batch. #running-req: 1, #token: 1843, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.28, #queue-req: 0, 
[2025-07-28 01:05:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1834, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.09, #queue-req: 0, 
[2025-07-28 01:05:11 DP1 TP0] Decode batch. #running-req: 1, #token: 1883, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.26, #queue-req: 0, 
[2025-07-28 01:05:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1874, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.18, #queue-req: 0, 
[2025-07-28 01:05:11 DP1 TP0] Decode batch. #running-req: 1, #token: 1923, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.32, #queue-req: 0, 
[2025-07-28 01:05:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1914, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.05, #queue-req: 0, 
[2025-07-28 01:05:11 DP1 TP0] Decode batch. #running-req: 1, #token: 1963, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.84, #queue-req: 0, 
[2025-07-28 01:05:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1954, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.63, #queue-req: 0, 
[2025-07-28 01:05:12 DP1 TP0] Decode batch. #running-req: 1, #token: 2003, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.83, #queue-req: 0, 
[2025-07-28 01:05:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1994, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.51, #queue-req: 0, 
[2025-07-28 01:05:12 DP1 TP0] Decode batch. #running-req: 1, #token: 2043, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.64, #queue-req: 0, 
[2025-07-28 01:05:12 DP0 TP0] Decode batch. #running-req: 1, #token: 2034, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.69, #queue-req: 0, 
[2025-07-28 01:05:12 DP1 TP0] Decode batch. #running-req: 1, #token: 2083, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.79, #queue-req: 0, 
[2025-07-28 01:05:12 DP0 TP0] Decode batch. #running-req: 1, #token: 2074, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.30, #queue-req: 0, 
[2025-07-28 01:05:12 DP1 TP0] Decode batch. #running-req: 1, #token: 2123, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.83, #queue-req: 0, 
[2025-07-28 01:05:12] INFO:     127.0.0.1:51434 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:05:12 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:05:12 DP0 TP0] Decode batch. #running-req: 1, #token: 182, token usage: 0.00, cuda graph: True, gen throughput (token/s): 181.15, #queue-req: 0, 
[2025-07-28 01:05:12 DP1 TP0] Decode batch. #running-req: 1, #token: 2163, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.67, #queue-req: 0, 
[2025-07-28 01:05:12 DP0 TP0] Decode batch. #running-req: 1, #token: 222, token usage: 0.00, cuda graph: True, gen throughput (token/s): 250.91, #queue-req: 0, 
[2025-07-28 01:05:12 DP1 TP0] Decode batch. #running-req: 1, #token: 2203, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.43, #queue-req: 0, 
[2025-07-28 01:05:13 DP0 TP0] Decode batch. #running-req: 1, #token: 262, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.85, #queue-req: 0, 
[2025-07-28 01:05:13 DP1 TP0] Decode batch. #running-req: 1, #token: 2243, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.08, #queue-req: 0, 
[2025-07-28 01:05:13 DP0 TP0] Decode batch. #running-req: 1, #token: 302, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.13, #queue-req: 0, 
[2025-07-28 01:05:13 DP1 TP0] Decode batch. #running-req: 1, #token: 2283, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.80, #queue-req: 0, 
[2025-07-28 01:05:13 DP0 TP0] Decode batch. #running-req: 1, #token: 342, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.23, #queue-req: 0, 
[2025-07-28 01:05:13 DP1 TP0] Decode batch. #running-req: 1, #token: 2323, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.65, #queue-req: 0, 
[2025-07-28 01:05:13 DP0 TP0] Decode batch. #running-req: 1, #token: 382, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.08, #queue-req: 0, 
[2025-07-28 01:05:13 DP1 TP0] Decode batch. #running-req: 1, #token: 2363, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.55, #queue-req: 0, 
[2025-07-28 01:05:13 DP0 TP0] Decode batch. #running-req: 1, #token: 422, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.26, #queue-req: 0, 
[2025-07-28 01:05:13 DP1 TP0] Decode batch. #running-req: 1, #token: 2403, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.15, #queue-req: 0, 
[2025-07-28 01:05:13 DP0 TP0] Decode batch. #running-req: 1, #token: 462, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.38, #queue-req: 0, 
[2025-07-28 01:05:13 DP1 TP0] Decode batch. #running-req: 1, #token: 2443, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.69, #queue-req: 0, 
[2025-07-28 01:05:13 DP0 TP0] Decode batch. #running-req: 1, #token: 502, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.39, #queue-req: 0, 
[2025-07-28 01:05:14 DP1 TP0] Decode batch. #running-req: 1, #token: 2483, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.74, #queue-req: 0, 
[2025-07-28 01:05:14 DP0 TP0] Decode batch. #running-req: 1, #token: 542, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.69, #queue-req: 0, 
[2025-07-28 01:05:14 DP1 TP0] Decode batch. #running-req: 1, #token: 2523, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.00, #queue-req: 0, 
[2025-07-28 01:05:14 DP0 TP0] Decode batch. #running-req: 1, #token: 582, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.74, #queue-req: 0, 
[2025-07-28 01:05:14 DP1 TP0] Decode batch. #running-req: 1, #token: 2563, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.71, #queue-req: 0, 
[2025-07-28 01:05:14 DP0 TP0] Decode batch. #running-req: 1, #token: 622, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.67, #queue-req: 0, 
[2025-07-28 01:05:14 DP1 TP0] Decode batch. #running-req: 1, #token: 2603, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.64, #queue-req: 0, 
[2025-07-28 01:05:14 DP0 TP0] Decode batch. #running-req: 1, #token: 662, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.76, #queue-req: 0, 
[2025-07-28 01:05:14 DP1 TP0] Decode batch. #running-req: 1, #token: 2643, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.48, #queue-req: 0, 
[2025-07-28 01:05:14 DP0 TP0] Decode batch. #running-req: 1, #token: 702, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.02, #queue-req: 0, 
[2025-07-28 01:05:14 DP1 TP0] Decode batch. #running-req: 1, #token: 2683, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.54, #queue-req: 0, 
[2025-07-28 01:05:14 DP0 TP0] Decode batch. #running-req: 1, #token: 742, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.07, #queue-req: 0, 
[2025-07-28 01:05:15 DP1 TP0] Decode batch. #running-req: 1, #token: 2723, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.46, #queue-req: 0, 
[2025-07-28 01:05:15 DP0 TP0] Decode batch. #running-req: 1, #token: 782, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.95, #queue-req: 0, 
[2025-07-28 01:05:15 DP1 TP0] Decode batch. #running-req: 1, #token: 2763, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.40, #queue-req: 0, 
[2025-07-28 01:05:15 DP0 TP0] Decode batch. #running-req: 1, #token: 822, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.41, #queue-req: 0, 
[2025-07-28 01:05:15 DP1 TP0] Decode batch. #running-req: 1, #token: 2803, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.61, #queue-req: 0, 
[2025-07-28 01:05:15 DP0 TP0] Decode batch. #running-req: 1, #token: 862, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.37, #queue-req: 0, 
[2025-07-28 01:05:15 DP1 TP0] Decode batch. #running-req: 1, #token: 2843, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.14, #queue-req: 0, 
[2025-07-28 01:05:15 DP0 TP0] Decode batch. #running-req: 1, #token: 902, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.41, #queue-req: 0, 
[2025-07-28 01:05:15 DP1 TP0] Decode batch. #running-req: 1, #token: 2883, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.03, #queue-req: 0, 
[2025-07-28 01:05:15 DP0 TP0] Decode batch. #running-req: 1, #token: 942, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.71, #queue-req: 0, 
[2025-07-28 01:05:15 DP1 TP0] Decode batch. #running-req: 1, #token: 2923, token usage: 0.00, cuda graph: True, gen throughput (token/s): 222.97, #queue-req: 0, 
[2025-07-28 01:05:15 DP0 TP0] Decode batch. #running-req: 1, #token: 982, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.91, #queue-req: 0, 
[2025-07-28 01:05:16 DP1 TP0] Decode batch. #running-req: 1, #token: 2963, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.02, #queue-req: 0, 
[2025-07-28 01:05:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1022, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.90, #queue-req: 0, 
[2025-07-28 01:05:16 DP1 TP0] Decode batch. #running-req: 1, #token: 3003, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.85, #queue-req: 0, 
[2025-07-28 01:05:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1062, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.66, #queue-req: 0, 
[2025-07-28 01:05:16 DP1 TP0] Decode batch. #running-req: 1, #token: 3043, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.07, #queue-req: 0, 
[2025-07-28 01:05:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1102, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.49, #queue-req: 0, 
[2025-07-28 01:05:16 DP1 TP0] Decode batch. #running-req: 1, #token: 3083, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.78, #queue-req: 0, 
[2025-07-28 01:05:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1142, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.71, #queue-req: 0, 
[2025-07-28 01:05:16 DP1 TP0] Decode batch. #running-req: 1, #token: 3123, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.46, #queue-req: 0, 
[2025-07-28 01:05:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1182, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.96, #queue-req: 0, 
[2025-07-28 01:05:16 DP1 TP0] Decode batch. #running-req: 1, #token: 3163, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.31, #queue-req: 0, 
[2025-07-28 01:05:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1222, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.04, #queue-req: 0, 
[2025-07-28 01:05:17 DP1 TP0] Decode batch. #running-req: 1, #token: 3203, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.31, #queue-req: 0, 
[2025-07-28 01:05:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1262, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.10, #queue-req: 0, 
[2025-07-28 01:05:17 DP1 TP0] Decode batch. #running-req: 1, #token: 3243, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.68, #queue-req: 0, 
[2025-07-28 01:05:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1302, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.84, #queue-req: 0, 
[2025-07-28 01:05:17 DP1 TP0] Decode batch. #running-req: 1, #token: 3283, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.01, #queue-req: 0, 
[2025-07-28 01:05:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1342, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.44, #queue-req: 0, 
[2025-07-28 01:05:17 DP1 TP0] Decode batch. #running-req: 1, #token: 3323, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.86, #queue-req: 0, 
[2025-07-28 01:05:17] INFO:     127.0.0.1:51444 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:05:17 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 154, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:05:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1382, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.64, #queue-req: 0, 
[2025-07-28 01:05:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1422, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.84, #queue-req: 0, 
[2025-07-28 01:05:17 DP1 TP0] Decode batch. #running-req: 1, #token: 278, token usage: 0.00, cuda graph: True, gen throughput (token/s): 173.12, #queue-req: 0, 
[2025-07-28 01:05:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1462, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.02, #queue-req: 0, 
[2025-07-28 01:05:17 DP1 TP0] Decode batch. #running-req: 1, #token: 318, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.17, #queue-req: 0, 
[2025-07-28 01:05:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1502, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.05, #queue-req: 0, 
[2025-07-28 01:05:18 DP1 TP0] Decode batch. #running-req: 1, #token: 358, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.80, #queue-req: 0, 
[2025-07-28 01:05:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1542, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.69, #queue-req: 0, 
[2025-07-28 01:05:18 DP1 TP0] Decode batch. #running-req: 1, #token: 398, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.64, #queue-req: 0, 
[2025-07-28 01:05:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1582, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.14, #queue-req: 0, 
[2025-07-28 01:05:18 DP1 TP0] Decode batch. #running-req: 1, #token: 438, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.46, #queue-req: 0, 
[2025-07-28 01:05:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1622, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.66, #queue-req: 0, 
[2025-07-28 01:05:18 DP1 TP0] Decode batch. #running-req: 1, #token: 478, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.46, #queue-req: 0, 
[2025-07-28 01:05:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1662, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.60, #queue-req: 0, 
[2025-07-28 01:05:18 DP1 TP0] Decode batch. #running-req: 1, #token: 518, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.48, #queue-req: 0, 
[2025-07-28 01:05:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1702, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.88, #queue-req: 0, 
[2025-07-28 01:05:18 DP1 TP0] Decode batch. #running-req: 1, #token: 558, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.15, #queue-req: 0, 
[2025-07-28 01:05:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1742, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.79, #queue-req: 0, 
[2025-07-28 01:05:19 DP1 TP0] Decode batch. #running-req: 1, #token: 598, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.24, #queue-req: 0, 
[2025-07-28 01:05:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1782, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.85, #queue-req: 0, 
[2025-07-28 01:05:19 DP1 TP0] Decode batch. #running-req: 1, #token: 638, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.63, #queue-req: 0, 
[2025-07-28 01:05:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1822, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.42, #queue-req: 0, 
[2025-07-28 01:05:19 DP1 TP0] Decode batch. #running-req: 1, #token: 678, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.36, #queue-req: 0, 
[2025-07-28 01:05:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1862, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.22, #queue-req: 0, 
[2025-07-28 01:05:19 DP1 TP0] Decode batch. #running-req: 1, #token: 718, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.28, #queue-req: 0, 
[2025-07-28 01:05:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1902, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.25, #queue-req: 0, 
[2025-07-28 01:05:19 DP1 TP0] Decode batch. #running-req: 1, #token: 758, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.65, #queue-req: 0, 
[2025-07-28 01:05:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1942, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.45, #queue-req: 0, 
[2025-07-28 01:05:19 DP1 TP0] Decode batch. #running-req: 1, #token: 798, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.19, #queue-req: 0, 
[2025-07-28 01:05:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1982, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.64, #queue-req: 0, 
[2025-07-28 01:05:20 DP1 TP0] Decode batch. #running-req: 1, #token: 838, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.94, #queue-req: 0, 
[2025-07-28 01:05:20 DP0 TP0] Decode batch. #running-req: 1, #token: 2022, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.67, #queue-req: 0, 
[2025-07-28 01:05:20 DP1 TP0] Decode batch. #running-req: 1, #token: 878, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.07, #queue-req: 0, 
[2025-07-28 01:05:20 DP0 TP0] Decode batch. #running-req: 1, #token: 2062, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.31, #queue-req: 0, 
[2025-07-28 01:05:20 DP1 TP0] Decode batch. #running-req: 1, #token: 918, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.24, #queue-req: 0, 
[2025-07-28 01:05:20 DP0 TP0] Decode batch. #running-req: 1, #token: 2102, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.02, #queue-req: 0, 
[2025-07-28 01:05:20 DP1 TP0] Decode batch. #running-req: 1, #token: 958, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.24, #queue-req: 0, 
[2025-07-28 01:05:20 DP0 TP0] Decode batch. #running-req: 1, #token: 2142, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.75, #queue-req: 0, 
[2025-07-28 01:05:20 DP1 TP0] Decode batch. #running-req: 1, #token: 998, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.29, #queue-req: 0, 
[2025-07-28 01:05:20 DP0 TP0] Decode batch. #running-req: 1, #token: 2182, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.61, #queue-req: 0, 
[2025-07-28 01:05:20 DP1 TP0] Decode batch. #running-req: 1, #token: 1038, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.99, #queue-req: 0, 
[2025-07-28 01:05:21 DP0 TP0] Decode batch. #running-req: 1, #token: 2222, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.05, #queue-req: 0, 
[2025-07-28 01:05:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1078, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.73, #queue-req: 0, 
[2025-07-28 01:05:21 DP0 TP0] Decode batch. #running-req: 1, #token: 2262, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.06, #queue-req: 0, 
[2025-07-28 01:05:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1118, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.99, #queue-req: 0, 
[2025-07-28 01:05:21 DP0 TP0] Decode batch. #running-req: 1, #token: 2302, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.98, #queue-req: 0, 
[2025-07-28 01:05:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1158, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.94, #queue-req: 0, 
[2025-07-28 01:05:21 DP0 TP0] Decode batch. #running-req: 1, #token: 2342, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.67, #queue-req: 0, 
[2025-07-28 01:05:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1198, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.39, #queue-req: 0, 
[2025-07-28 01:05:21 DP0 TP0] Decode batch. #running-req: 1, #token: 2382, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.61, #queue-req: 0, 
[2025-07-28 01:05:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1238, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.42, #queue-req: 0, 
[2025-07-28 01:05:21 DP0 TP0] Decode batch. #running-req: 1, #token: 2422, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.58, #queue-req: 0, 
[2025-07-28 01:05:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1278, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.51, #queue-req: 0, 
[2025-07-28 01:05:21] INFO:     127.0.0.1:56404 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:05:21 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 128, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:05:22 DP0 TP0] Decode batch. #running-req: 2, #token: 2606, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.26, #queue-req: 0, 
[2025-07-28 01:05:22 DP0 TP0] Decode batch. #running-req: 2, #token: 2686, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.18, #queue-req: 0, 
[2025-07-28 01:05:22 DP0 TP0] Decode batch. #running-req: 2, #token: 2766, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.68, #queue-req: 0, 
[2025-07-28 01:05:22 DP0 TP0] Decode batch. #running-req: 2, #token: 2846, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.79, #queue-req: 0, 
[2025-07-28 01:05:22 DP0 TP0] Decode batch. #running-req: 2, #token: 2926, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.77, #queue-req: 0, 
[2025-07-28 01:05:22 DP0 TP0] Decode batch. #running-req: 2, #token: 3006, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.42, #queue-req: 0, 
[2025-07-28 01:05:23 DP0 TP0] Decode batch. #running-req: 2, #token: 3086, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.53, #queue-req: 0, 
[2025-07-28 01:05:23 DP0 TP0] Decode batch. #running-req: 2, #token: 3166, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.06, #queue-req: 0, 
[2025-07-28 01:05:23 DP0 TP0] Decode batch. #running-req: 2, #token: 3246, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.38, #queue-req: 0, 
[2025-07-28 01:05:23 DP0 TP0] Decode batch. #running-req: 2, #token: 3326, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.60, #queue-req: 0, 
[2025-07-28 01:05:23 DP0 TP0] Decode batch. #running-req: 2, #token: 3406, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.07, #queue-req: 0, 
[2025-07-28 01:05:23 DP0 TP0] Decode batch. #running-req: 2, #token: 3486, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.67, #queue-req: 0, 
[2025-07-28 01:05:24 DP0 TP0] Decode batch. #running-req: 2, #token: 3566, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.84, #queue-req: 0, 
[2025-07-28 01:05:24 DP0 TP0] Decode batch. #running-req: 2, #token: 3646, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.76, #queue-req: 0, 
[2025-07-28 01:05:24 DP0 TP0] Decode batch. #running-req: 2, #token: 3726, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.80, #queue-req: 0, 
[2025-07-28 01:05:24] INFO:     127.0.0.1:56402 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:05:24 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:05:24 DP0 TP0] Decode batch. #running-req: 1, #token: 829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 351.21, #queue-req: 0, 
[2025-07-28 01:05:24 DP1 TP0] Decode batch. #running-req: 1, #token: 251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 13.87, #queue-req: 0, 
[2025-07-28 01:05:24 DP0 TP0] Decode batch. #running-req: 1, #token: 869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.21, #queue-req: 0, 
[2025-07-28 01:05:24 DP1 TP0] Decode batch. #running-req: 1, #token: 291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 222.77, #queue-req: 0, 
[2025-07-28 01:05:24 DP0 TP0] Decode batch. #running-req: 1, #token: 909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.61, #queue-req: 0, 
[2025-07-28 01:05:25 DP1 TP0] Decode batch. #running-req: 1, #token: 331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.89, #queue-req: 0, 
[2025-07-28 01:05:25 DP0 TP0] Decode batch. #running-req: 1, #token: 949, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.84, #queue-req: 0, 
[2025-07-28 01:05:25 DP1 TP0] Decode batch. #running-req: 1, #token: 371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.75, #queue-req: 0, 
[2025-07-28 01:05:25 DP0 TP0] Decode batch. #running-req: 1, #token: 989, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.94, #queue-req: 0, 
[2025-07-28 01:05:25 DP1 TP0] Decode batch. #running-req: 1, #token: 411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.29, #queue-req: 0, 
[2025-07-28 01:05:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1029, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.50, #queue-req: 0, 
[2025-07-28 01:05:25 DP1 TP0] Decode batch. #running-req: 1, #token: 451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.82, #queue-req: 0, 
[2025-07-28 01:05:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1069, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.65, #queue-req: 0, 
[2025-07-28 01:05:25 DP1 TP0] Decode batch. #running-req: 1, #token: 491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.80, #queue-req: 0, 
[2025-07-28 01:05:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1109, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.64, #queue-req: 0, 
[2025-07-28 01:05:25 DP1 TP0] Decode batch. #running-req: 1, #token: 531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.32, #queue-req: 0, 
[2025-07-28 01:05:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1149, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.63, #queue-req: 0, 
[2025-07-28 01:05:26 DP1 TP0] Decode batch. #running-req: 1, #token: 571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.79, #queue-req: 0, 
[2025-07-28 01:05:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.45, #queue-req: 0, 
[2025-07-28 01:05:26 DP1 TP0] Decode batch. #running-req: 1, #token: 611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.82, #queue-req: 0, 
[2025-07-28 01:05:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.04, #queue-req: 0, 
[2025-07-28 01:05:26 DP1 TP0] Decode batch. #running-req: 1, #token: 651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.20, #queue-req: 0, 
[2025-07-28 01:05:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.13, #queue-req: 0, 
[2025-07-28 01:05:26 DP1 TP0] Decode batch. #running-req: 1, #token: 691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.34, #queue-req: 0, 
[2025-07-28 01:05:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.82, #queue-req: 0, 
[2025-07-28 01:05:26 DP1 TP0] Decode batch. #running-req: 1, #token: 731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.09, #queue-req: 0, 
[2025-07-28 01:05:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.56, #queue-req: 0, 
[2025-07-28 01:05:26 DP1 TP0] Decode batch. #running-req: 1, #token: 771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.59, #queue-req: 0, 
[2025-07-28 01:05:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.48, #queue-req: 0, 
[2025-07-28 01:05:27 DP1 TP0] Decode batch. #running-req: 1, #token: 811, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.04, #queue-req: 0, 
[2025-07-28 01:05:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.54, #queue-req: 0, 
[2025-07-28 01:05:27 DP1 TP0] Decode batch. #running-req: 1, #token: 851, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.15, #queue-req: 0, 
[2025-07-28 01:05:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.25, #queue-req: 0, 
[2025-07-28 01:05:27 DP1 TP0] Decode batch. #running-req: 1, #token: 891, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.17, #queue-req: 0, 
[2025-07-28 01:05:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.92, #queue-req: 0, 
[2025-07-28 01:05:27 DP1 TP0] Decode batch. #running-req: 1, #token: 931, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.32, #queue-req: 0, 
[2025-07-28 01:05:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.68, #queue-req: 0, 
[2025-07-28 01:05:27 DP1 TP0] Decode batch. #running-req: 1, #token: 971, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.99, #queue-req: 0, 
[2025-07-28 01:05:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.33, #queue-req: 0, 
[2025-07-28 01:05:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1011, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.52, #queue-req: 0, 
[2025-07-28 01:05:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.55, #queue-req: 0, 
[2025-07-28 01:05:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1051, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.48, #queue-req: 0, 
[2025-07-28 01:05:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.53, #queue-req: 0, 
[2025-07-28 01:05:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1091, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.41, #queue-req: 0, 
[2025-07-28 01:05:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.56, #queue-req: 0, 
[2025-07-28 01:05:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1131, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.15, #queue-req: 0, 
[2025-07-28 01:05:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.57, #queue-req: 0, 
[2025-07-28 01:05:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1171, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.76, #queue-req: 0, 
[2025-07-28 01:05:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.45, #queue-req: 0, 
[2025-07-28 01:05:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1211, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.11, #queue-req: 0, 
[2025-07-28 01:05:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.07, #queue-req: 0, 
[2025-07-28 01:05:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.46, #queue-req: 0, 
[2025-07-28 01:05:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.86, #queue-req: 0, 
[2025-07-28 01:05:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.30, #queue-req: 0, 
[2025-07-28 01:05:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.62, #queue-req: 0, 
[2025-07-28 01:05:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.94, #queue-req: 0, 
[2025-07-28 01:05:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1949, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.34, #queue-req: 0, 
[2025-07-28 01:05:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.38, #queue-req: 0, 
[2025-07-28 01:05:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1989, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.51, #queue-req: 0, 
[2025-07-28 01:05:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.25, #queue-req: 0, 
[2025-07-28 01:05:29 DP0 TP0] Decode batch. #running-req: 1, #token: 2029, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.60, #queue-req: 0, 
[2025-07-28 01:05:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.88, #queue-req: 0, 
[2025-07-28 01:05:29 DP0 TP0] Decode batch. #running-req: 1, #token: 2069, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.21, #queue-req: 0, 
[2025-07-28 01:05:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.87, #queue-req: 0, 
[2025-07-28 01:05:29 DP0 TP0] Decode batch. #running-req: 1, #token: 2109, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.56, #queue-req: 0, 
[2025-07-28 01:05:30 DP1 TP0] Decode batch. #running-req: 1, #token: 1531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.68, #queue-req: 0, 
[2025-07-28 01:05:30 DP0 TP0] Decode batch. #running-req: 1, #token: 2149, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.46, #queue-req: 0, 
[2025-07-28 01:05:30 DP1 TP0] Decode batch. #running-req: 1, #token: 1571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.58, #queue-req: 0, 
[2025-07-28 01:05:30 DP0 TP0] Decode batch. #running-req: 1, #token: 2189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.64, #queue-req: 0, 
[2025-07-28 01:05:30 DP1 TP0] Decode batch. #running-req: 1, #token: 1611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.21, #queue-req: 0, 
[2025-07-28 01:05:30 DP0 TP0] Decode batch. #running-req: 1, #token: 2229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.74, #queue-req: 0, 
[2025-07-28 01:05:30 DP1 TP0] Decode batch. #running-req: 1, #token: 1651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.35, #queue-req: 0, 
[2025-07-28 01:05:30 DP0 TP0] Decode batch. #running-req: 1, #token: 2269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.66, #queue-req: 0, 
[2025-07-28 01:05:30 DP1 TP0] Decode batch. #running-req: 1, #token: 1691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.70, #queue-req: 0, 
[2025-07-28 01:05:30 DP0 TP0] Decode batch. #running-req: 1, #token: 2309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.91, #queue-req: 0, 
[2025-07-28 01:05:30 DP1 TP0] Decode batch. #running-req: 1, #token: 1731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.34, #queue-req: 0, 
[2025-07-28 01:05:30 DP0 TP0] Decode batch. #running-req: 1, #token: 2349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.49, #queue-req: 0, 
[2025-07-28 01:05:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.80, #queue-req: 0, 
[2025-07-28 01:05:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.45, #queue-req: 0, 
[2025-07-28 01:05:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1811, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.41, #queue-req: 0, 
[2025-07-28 01:05:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.21, #queue-req: 0, 
[2025-07-28 01:05:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1851, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.06, #queue-req: 0, 
[2025-07-28 01:05:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.75, #queue-req: 0, 
[2025-07-28 01:05:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1891, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.36, #queue-req: 0, 
[2025-07-28 01:05:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.85, #queue-req: 0, 
[2025-07-28 01:05:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1931, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.43, #queue-req: 0, 
[2025-07-28 01:05:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.44, #queue-req: 0, 
[2025-07-28 01:05:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1971, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.41, #queue-req: 0, 
[2025-07-28 01:05:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 227.33, #queue-req: 0, 
[2025-07-28 01:05:32 DP1 TP0] Decode batch. #running-req: 1, #token: 2011, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.19, #queue-req: 0, 
[2025-07-28 01:05:32 DP0 TP0] Decode batch. #running-req: 1, #token: 2629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.92, #queue-req: 0, 
[2025-07-28 01:05:32 DP1 TP0] Decode batch. #running-req: 1, #token: 2051, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.27, #queue-req: 0, 
[2025-07-28 01:05:32 DP0 TP0] Decode batch. #running-req: 1, #token: 2669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.61, #queue-req: 0, 
[2025-07-28 01:05:32 DP1 TP0] Decode batch. #running-req: 1, #token: 2091, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.31, #queue-req: 0, 
[2025-07-28 01:05:32 DP0 TP0] Decode batch. #running-req: 1, #token: 2709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.67, #queue-req: 0, 
[2025-07-28 01:05:32 DP1 TP0] Decode batch. #running-req: 1, #token: 2131, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.03, #queue-req: 0, 
[2025-07-28 01:05:32 DP0 TP0] Decode batch. #running-req: 1, #token: 2749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.62, #queue-req: 0, 
[2025-07-28 01:05:32 DP1 TP0] Decode batch. #running-req: 1, #token: 2171, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.58, #queue-req: 0, 
[2025-07-28 01:05:32 DP0 TP0] Decode batch. #running-req: 1, #token: 2789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.74, #queue-req: 0, 
[2025-07-28 01:05:32 DP1 TP0] Decode batch. #running-req: 1, #token: 2211, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.52, #queue-req: 0, 
[2025-07-28 01:05:32 DP0 TP0] Decode batch. #running-req: 1, #token: 2829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.62, #queue-req: 0, 
[2025-07-28 01:05:33 DP1 TP0] Decode batch. #running-req: 1, #token: 2251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.46, #queue-req: 0, 
[2025-07-28 01:05:33 DP0 TP0] Decode batch. #running-req: 1, #token: 2869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.45, #queue-req: 0, 
[2025-07-28 01:05:33 DP1 TP0] Decode batch. #running-req: 1, #token: 2291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.57, #queue-req: 0, 
[2025-07-28 01:05:33 DP0 TP0] Decode batch. #running-req: 1, #token: 2909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.54, #queue-req: 0, 
[2025-07-28 01:05:33 DP1 TP0] Decode batch. #running-req: 1, #token: 2331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.93, #queue-req: 0, 
[2025-07-28 01:05:33 DP0 TP0] Decode batch. #running-req: 1, #token: 2949, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.64, #queue-req: 0, 
[2025-07-28 01:05:33 DP1 TP0] Decode batch. #running-req: 1, #token: 2371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.59, #queue-req: 0, 
[2025-07-28 01:05:33 DP0 TP0] Decode batch. #running-req: 1, #token: 2989, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.89, #queue-req: 0, 
[2025-07-28 01:05:33 DP1 TP0] Decode batch. #running-req: 1, #token: 2411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.38, #queue-req: 0, 
[2025-07-28 01:05:33 DP0 TP0] Decode batch. #running-req: 1, #token: 3029, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.82, #queue-req: 0, 
[2025-07-28 01:05:33 DP1 TP0] Decode batch. #running-req: 1, #token: 2451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.54, #queue-req: 0, 
[2025-07-28 01:05:33 DP0 TP0] Decode batch. #running-req: 1, #token: 3069, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.73, #queue-req: 0, 
[2025-07-28 01:05:34 DP1 TP0] Decode batch. #running-req: 1, #token: 2491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.76, #queue-req: 0, 
[2025-07-28 01:05:34 DP0 TP0] Decode batch. #running-req: 1, #token: 3109, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.29, #queue-req: 0, 
[2025-07-28 01:05:34 DP1 TP0] Decode batch. #running-req: 1, #token: 2531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.31, #queue-req: 0, 
[2025-07-28 01:05:34 DP0 TP0] Decode batch. #running-req: 1, #token: 3149, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.21, #queue-req: 0, 
[2025-07-28 01:05:34 DP1 TP0] Decode batch. #running-req: 1, #token: 2571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.82, #queue-req: 0, 
[2025-07-28 01:05:34 DP0 TP0] Decode batch. #running-req: 1, #token: 3189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.26, #queue-req: 0, 
[2025-07-28 01:05:34 DP1 TP0] Decode batch. #running-req: 1, #token: 2611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.22, #queue-req: 0, 
[2025-07-28 01:05:34 DP0 TP0] Decode batch. #running-req: 1, #token: 3229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.56, #queue-req: 0, 
[2025-07-28 01:05:34 DP1 TP0] Decode batch. #running-req: 1, #token: 2651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.24, #queue-req: 0, 
[2025-07-28 01:05:34 DP0 TP0] Decode batch. #running-req: 1, #token: 3269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.68, #queue-req: 0, 
[2025-07-28 01:05:34 DP1 TP0] Decode batch. #running-req: 1, #token: 2691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.12, #queue-req: 0, 
[2025-07-28 01:05:34 DP0 TP0] Decode batch. #running-req: 1, #token: 3309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.41, #queue-req: 0, 
[2025-07-28 01:05:35 DP1 TP0] Decode batch. #running-req: 1, #token: 2731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.15, #queue-req: 0, 
[2025-07-28 01:05:35 DP0 TP0] Decode batch. #running-req: 1, #token: 3349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.35, #queue-req: 0, 
[2025-07-28 01:05:35 DP1 TP0] Decode batch. #running-req: 1, #token: 2771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.77, #queue-req: 0, 
[2025-07-28 01:05:35 DP0 TP0] Decode batch. #running-req: 1, #token: 3389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.19, #queue-req: 0, 
[2025-07-28 01:05:35 DP1 TP0] Decode batch. #running-req: 1, #token: 2811, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.32, #queue-req: 0, 
[2025-07-28 01:05:35 DP0 TP0] Decode batch. #running-req: 1, #token: 3429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.25, #queue-req: 0, 
[2025-07-28 01:05:35 DP1 TP0] Decode batch. #running-req: 1, #token: 2851, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.99, #queue-req: 0, 
[2025-07-28 01:05:35 DP0 TP0] Decode batch. #running-req: 1, #token: 3469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.20, #queue-req: 0, 
[2025-07-28 01:05:35 DP1 TP0] Decode batch. #running-req: 1, #token: 2891, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.16, #queue-req: 0, 
[2025-07-28 01:05:35 DP0 TP0] Decode batch. #running-req: 1, #token: 3509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.09, #queue-req: 0, 
[2025-07-28 01:05:35 DP1 TP0] Decode batch. #running-req: 1, #token: 2931, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.59, #queue-req: 0, 
[2025-07-28 01:05:35 DP0 TP0] Decode batch. #running-req: 1, #token: 3549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.81, #queue-req: 0, 
[2025-07-28 01:05:36 DP1 TP0] Decode batch. #running-req: 1, #token: 2971, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.61, #queue-req: 0, 
[2025-07-28 01:05:36 DP0 TP0] Decode batch. #running-req: 1, #token: 3589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.88, #queue-req: 0, 
[2025-07-28 01:05:36 DP1 TP0] Decode batch. #running-req: 1, #token: 3011, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.32, #queue-req: 0, 
[2025-07-28 01:05:36 DP0 TP0] Decode batch. #running-req: 1, #token: 3629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.41, #queue-req: 0, 
[2025-07-28 01:05:36 DP1 TP0] Decode batch. #running-req: 1, #token: 3051, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.19, #queue-req: 0, 
[2025-07-28 01:05:36 DP0 TP0] Decode batch. #running-req: 1, #token: 3669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.46, #queue-req: 0, 
[2025-07-28 01:05:36 DP1 TP0] Decode batch. #running-req: 1, #token: 3091, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.14, #queue-req: 0, 
[2025-07-28 01:05:36 DP0 TP0] Decode batch. #running-req: 1, #token: 3709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.23, #queue-req: 0, 
[2025-07-28 01:05:36 DP1 TP0] Decode batch. #running-req: 1, #token: 3131, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.35, #queue-req: 0, 
[2025-07-28 01:05:36 DP0 TP0] Decode batch. #running-req: 1, #token: 3749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.99, #queue-req: 0, 
[2025-07-28 01:05:36 DP1 TP0] Decode batch. #running-req: 1, #token: 3171, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.91, #queue-req: 0, 
[2025-07-28 01:05:36 DP0 TP0] Decode batch. #running-req: 1, #token: 3789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.09, #queue-req: 0, 
[2025-07-28 01:05:36 DP1 TP0] Decode batch. #running-req: 1, #token: 3211, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.76, #queue-req: 0, 
[2025-07-28 01:05:37 DP0 TP0] Decode batch. #running-req: 1, #token: 3829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.46, #queue-req: 0, 
[2025-07-28 01:05:37 DP1 TP0] Decode batch. #running-req: 1, #token: 3251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.20, #queue-req: 0, 
[2025-07-28 01:05:37 DP0 TP0] Decode batch. #running-req: 1, #token: 3869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.21, #queue-req: 0, 
[2025-07-28 01:05:37 DP1 TP0] Decode batch. #running-req: 1, #token: 3291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.96, #queue-req: 0, 
[2025-07-28 01:05:37 DP0 TP0] Decode batch. #running-req: 1, #token: 3909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.95, #queue-req: 0, 
[2025-07-28 01:05:37 DP1 TP0] Decode batch. #running-req: 1, #token: 3331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.49, #queue-req: 0, 
[2025-07-28 01:05:37 DP0 TP0] Decode batch. #running-req: 1, #token: 3949, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.09, #queue-req: 0, 
[2025-07-28 01:05:37 DP1 TP0] Decode batch. #running-req: 1, #token: 3371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.84, #queue-req: 0, 
[2025-07-28 01:05:37 DP0 TP0] Decode batch. #running-req: 1, #token: 3989, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.25, #queue-req: 0, 
[2025-07-28 01:05:37 DP1 TP0] Decode batch. #running-req: 1, #token: 3411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.38, #queue-req: 0, 
[2025-07-28 01:05:37 DP0 TP0] Decode batch. #running-req: 1, #token: 4029, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.56, #queue-req: 0, 
[2025-07-28 01:05:37 DP1 TP0] Decode batch. #running-req: 1, #token: 3451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.49, #queue-req: 0, 
[2025-07-28 01:05:38 DP0 TP0] Decode batch. #running-req: 1, #token: 4069, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.58, #queue-req: 0, 
[2025-07-28 01:05:38 DP1 TP0] Decode batch. #running-req: 1, #token: 3491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.68, #queue-req: 0, 
[2025-07-28 01:05:38 DP0 TP0] Decode batch. #running-req: 1, #token: 4109, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.34, #queue-req: 0, 
[2025-07-28 01:05:38 DP1 TP0] Decode batch. #running-req: 1, #token: 3531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.32, #queue-req: 0, 
[2025-07-28 01:05:38 DP0 TP0] Decode batch. #running-req: 1, #token: 4149, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.78, #queue-req: 0, 
[2025-07-28 01:05:38 DP1 TP0] Decode batch. #running-req: 1, #token: 3571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.82, #queue-req: 0, 
[2025-07-28 01:05:38 DP0 TP0] Decode batch. #running-req: 1, #token: 4189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.14, #queue-req: 0, 
[2025-07-28 01:05:38 DP1 TP0] Decode batch. #running-req: 1, #token: 3611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.32, #queue-req: 0, 
[2025-07-28 01:05:38 DP0 TP0] Decode batch. #running-req: 1, #token: 4229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.74, #queue-req: 0, 
[2025-07-28 01:05:38 DP1 TP0] Decode batch. #running-req: 1, #token: 3651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.12, #queue-req: 0, 
[2025-07-28 01:05:38 DP0 TP0] Decode batch. #running-req: 1, #token: 4269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.17, #queue-req: 0, 
[2025-07-28 01:05:38 DP1 TP0] Decode batch. #running-req: 1, #token: 3691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.18, #queue-req: 0, 
[2025-07-28 01:05:39 DP0 TP0] Decode batch. #running-req: 1, #token: 4309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.21, #queue-req: 0, 
[2025-07-28 01:05:39 DP1 TP0] Decode batch. #running-req: 1, #token: 3731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.96, #queue-req: 0, 
[2025-07-28 01:05:39 DP0 TP0] Decode batch. #running-req: 1, #token: 4349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.07, #queue-req: 0, 
[2025-07-28 01:05:39 DP1 TP0] Decode batch. #running-req: 1, #token: 3771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.63, #queue-req: 0, 
[2025-07-28 01:05:39 DP0 TP0] Decode batch. #running-req: 1, #token: 4389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.81, #queue-req: 0, 
[2025-07-28 01:05:39 DP1 TP0] Decode batch. #running-req: 1, #token: 3811, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.41, #queue-req: 0, 
[2025-07-28 01:05:39 DP0 TP0] Decode batch. #running-req: 1, #token: 4429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.60, #queue-req: 0, 
[2025-07-28 01:05:39 DP1 TP0] Decode batch. #running-req: 1, #token: 3851, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.67, #queue-req: 0, 
[2025-07-28 01:05:39 DP0 TP0] Decode batch. #running-req: 1, #token: 4469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.57, #queue-req: 0, 
[2025-07-28 01:05:39 DP1 TP0] Decode batch. #running-req: 1, #token: 3891, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.10, #queue-req: 0, 
[2025-07-28 01:05:39 DP0 TP0] Decode batch. #running-req: 1, #token: 4509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.72, #queue-req: 0, 
[2025-07-28 01:05:39 DP1 TP0] Decode batch. #running-req: 1, #token: 3931, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.76, #queue-req: 0, 
[2025-07-28 01:05:40 DP0 TP0] Decode batch. #running-req: 1, #token: 4549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.03, #queue-req: 0, 
[2025-07-28 01:05:40 DP1 TP0] Decode batch. #running-req: 1, #token: 3971, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.36, #queue-req: 0, 
[2025-07-28 01:05:40 DP0 TP0] Decode batch. #running-req: 1, #token: 4589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.90, #queue-req: 0, 
[2025-07-28 01:05:40 DP1 TP0] Decode batch. #running-req: 1, #token: 4011, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.21, #queue-req: 0, 
[2025-07-28 01:05:40 DP0 TP0] Decode batch. #running-req: 1, #token: 4629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.79, #queue-req: 0, 
[2025-07-28 01:05:40 DP1 TP0] Decode batch. #running-req: 1, #token: 4051, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.38, #queue-req: 0, 
[2025-07-28 01:05:40 DP0 TP0] Decode batch. #running-req: 1, #token: 4669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.64, #queue-req: 0, 
[2025-07-28 01:05:40 DP1 TP0] Decode batch. #running-req: 1, #token: 4091, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.30, #queue-req: 0, 
[2025-07-28 01:05:40 DP0 TP0] Decode batch. #running-req: 1, #token: 4709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.59, #queue-req: 0, 
[2025-07-28 01:05:40 DP1 TP0] Decode batch. #running-req: 1, #token: 4131, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.23, #queue-req: 0, 
[2025-07-28 01:05:40 DP0 TP0] Decode batch. #running-req: 1, #token: 4749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.92, #queue-req: 0, 
[2025-07-28 01:05:40 DP1 TP0] Decode batch. #running-req: 1, #token: 4171, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.59, #queue-req: 0, 
[2025-07-28 01:05:41 DP0 TP0] Decode batch. #running-req: 1, #token: 4789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.98, #queue-req: 0, 
[2025-07-28 01:05:41 DP1 TP0] Decode batch. #running-req: 1, #token: 4211, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.29, #queue-req: 0, 
[2025-07-28 01:05:41 DP0 TP0] Decode batch. #running-req: 1, #token: 4829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.97, #queue-req: 0, 
[2025-07-28 01:05:41 DP1 TP0] Decode batch. #running-req: 1, #token: 4251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.37, #queue-req: 0, 
[2025-07-28 01:05:41 DP0 TP0] Decode batch. #running-req: 1, #token: 4869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.07, #queue-req: 0, 
[2025-07-28 01:05:41 DP1 TP0] Decode batch. #running-req: 1, #token: 4291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.69, #queue-req: 0, 
[2025-07-28 01:05:41 DP0 TP0] Decode batch. #running-req: 1, #token: 4909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.65, #queue-req: 0, 
[2025-07-28 01:05:41 DP1 TP0] Decode batch. #running-req: 1, #token: 4331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.16, #queue-req: 0, 
[2025-07-28 01:05:41 DP0 TP0] Decode batch. #running-req: 1, #token: 4949, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.91, #queue-req: 0, 
[2025-07-28 01:05:41 DP1 TP0] Decode batch. #running-req: 1, #token: 4371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.85, #queue-req: 0, 
[2025-07-28 01:05:41 DP0 TP0] Decode batch. #running-req: 1, #token: 4989, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.79, #queue-req: 0, 
[2025-07-28 01:05:41 DP1 TP0] Decode batch. #running-req: 1, #token: 4411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.24, #queue-req: 0, 
[2025-07-28 01:05:42 DP0 TP0] Decode batch. #running-req: 1, #token: 5029, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.19, #queue-req: 0, 
[2025-07-28 01:05:42 DP1 TP0] Decode batch. #running-req: 1, #token: 4451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.20, #queue-req: 0, 
[2025-07-28 01:05:42 DP0 TP0] Decode batch. #running-req: 1, #token: 5069, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.36, #queue-req: 0, 
[2025-07-28 01:05:42 DP1 TP0] Decode batch. #running-req: 1, #token: 4491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.30, #queue-req: 0, 
[2025-07-28 01:05:42 DP0 TP0] Decode batch. #running-req: 1, #token: 5109, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.27, #queue-req: 0, 
[2025-07-28 01:05:42 DP1 TP0] Decode batch. #running-req: 1, #token: 4531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.24, #queue-req: 0, 
[2025-07-28 01:05:42 DP0 TP0] Decode batch. #running-req: 1, #token: 5149, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.66, #queue-req: 0, 
[2025-07-28 01:05:42 DP1 TP0] Decode batch. #running-req: 1, #token: 4571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.85, #queue-req: 0, 
[2025-07-28 01:05:42 DP0 TP0] Decode batch. #running-req: 1, #token: 5189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.46, #queue-req: 0, 
[2025-07-28 01:05:42 DP1 TP0] Decode batch. #running-req: 1, #token: 4611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.48, #queue-req: 0, 
[2025-07-28 01:05:42 DP0 TP0] Decode batch. #running-req: 1, #token: 5229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.92, #queue-req: 0, 
[2025-07-28 01:05:42 DP1 TP0] Decode batch. #running-req: 1, #token: 4651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.13, #queue-req: 0, 
[2025-07-28 01:05:43 DP0 TP0] Decode batch. #running-req: 1, #token: 5269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.98, #queue-req: 0, 
[2025-07-28 01:05:43 DP1 TP0] Decode batch. #running-req: 1, #token: 4691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.12, #queue-req: 0, 
[2025-07-28 01:05:43 DP0 TP0] Decode batch. #running-req: 1, #token: 5309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.79, #queue-req: 0, 
[2025-07-28 01:05:43 DP1 TP0] Decode batch. #running-req: 1, #token: 4731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.95, #queue-req: 0, 
[2025-07-28 01:05:43 DP0 TP0] Decode batch. #running-req: 1, #token: 5349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 235.03, #queue-req: 0, 
[2025-07-28 01:05:43 DP1 TP0] Decode batch. #running-req: 1, #token: 4771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.71, #queue-req: 0, 
[2025-07-28 01:05:43 DP0 TP0] Decode batch. #running-req: 1, #token: 5389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 233.28, #queue-req: 0, 
[2025-07-28 01:05:43 DP1 TP0] Decode batch. #running-req: 1, #token: 4811, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.32, #queue-req: 0, 
[2025-07-28 01:05:43 DP0 TP0] Decode batch. #running-req: 1, #token: 5429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 228.99, #queue-req: 0, 
[2025-07-28 01:05:43 DP1 TP0] Decode batch. #running-req: 1, #token: 4851, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.49, #queue-req: 0, 
[2025-07-28 01:05:43 DP0 TP0] Decode batch. #running-req: 1, #token: 5469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 232.94, #queue-req: 0, 
[2025-07-28 01:05:43 DP1 TP0] Decode batch. #running-req: 1, #token: 4891, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.67, #queue-req: 0, 
[2025-07-28 01:05:44 DP0 TP0] Decode batch. #running-req: 1, #token: 5509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 232.91, #queue-req: 0, 
[2025-07-28 01:05:44 DP1 TP0] Decode batch. #running-req: 1, #token: 4931, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.46, #queue-req: 0, 
[2025-07-28 01:05:44 DP0 TP0] Decode batch. #running-req: 1, #token: 5549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 233.50, #queue-req: 0, 
[2025-07-28 01:05:44 DP1 TP0] Decode batch. #running-req: 1, #token: 4971, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.88, #queue-req: 0, 
[2025-07-28 01:05:44 DP0 TP0] Decode batch. #running-req: 1, #token: 5589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 235.90, #queue-req: 0, 
[2025-07-28 01:05:44 DP1 TP0] Decode batch. #running-req: 1, #token: 5011, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.06, #queue-req: 0, 
[2025-07-28 01:05:44 DP0 TP0] Decode batch. #running-req: 1, #token: 5629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.16, #queue-req: 0, 
[2025-07-28 01:05:44 DP1 TP0] Decode batch. #running-req: 1, #token: 5051, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.16, #queue-req: 0, 
[2025-07-28 01:05:44 DP1 TP0] Decode batch. #running-req: 1, #token: 5091, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.26, #queue-req: 0, 
[2025-07-28 01:05:44 DP0 TP0] Decode batch. #running-req: 1, #token: 5669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.34, #queue-req: 0, 
[2025-07-28 01:05:44 DP1 TP0] Decode batch. #running-req: 1, #token: 5131, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.75, #queue-req: 0, 
[2025-07-28 01:05:44 DP0 TP0] Decode batch. #running-req: 1, #token: 5709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.84, #queue-req: 0, 
[2025-07-28 01:05:45 DP1 TP0] Decode batch. #running-req: 1, #token: 5171, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.41, #queue-req: 0, 
[2025-07-28 01:05:45 DP0 TP0] Decode batch. #running-req: 1, #token: 5749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.92, #queue-req: 0, 
[2025-07-28 01:05:45 DP1 TP0] Decode batch. #running-req: 1, #token: 5211, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.22, #queue-req: 0, 
[2025-07-28 01:05:45 DP0 TP0] Decode batch. #running-req: 1, #token: 5789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.33, #queue-req: 0, 
[2025-07-28 01:05:45 DP1 TP0] Decode batch. #running-req: 1, #token: 5251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.26, #queue-req: 0, 
[2025-07-28 01:05:45 DP0 TP0] Decode batch. #running-req: 1, #token: 5829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.13, #queue-req: 0, 
[2025-07-28 01:05:45 DP1 TP0] Decode batch. #running-req: 1, #token: 5291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.49, #queue-req: 0, 
[2025-07-28 01:05:45 DP0 TP0] Decode batch. #running-req: 1, #token: 5869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.52, #queue-req: 0, 
[2025-07-28 01:05:45 DP1 TP0] Decode batch. #running-req: 1, #token: 5331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.35, #queue-req: 0, 
[2025-07-28 01:05:45 DP0 TP0] Decode batch. #running-req: 1, #token: 5909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 234.30, #queue-req: 0, 
[2025-07-28 01:05:45 DP1 TP0] Decode batch. #running-req: 1, #token: 5371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.70, #queue-req: 0, 
[2025-07-28 01:05:45 DP0 TP0] Decode batch. #running-req: 1, #token: 5949, token usage: 0.00, cuda graph: True, gen throughput (token/s): 232.51, #queue-req: 0, 
[2025-07-28 01:05:46 DP1 TP0] Decode batch. #running-req: 1, #token: 5411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.19, #queue-req: 0, 
[2025-07-28 01:05:46 DP0 TP0] Decode batch. #running-req: 1, #token: 5989, token usage: 0.00, cuda graph: True, gen throughput (token/s): 232.62, #queue-req: 0, 
[2025-07-28 01:05:46 DP1 TP0] Decode batch. #running-req: 1, #token: 5451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.34, #queue-req: 0, 
[2025-07-28 01:05:46 DP0 TP0] Decode batch. #running-req: 1, #token: 6029, token usage: 0.00, cuda graph: True, gen throughput (token/s): 231.78, #queue-req: 0, 
[2025-07-28 01:05:46 DP1 TP0] Decode batch. #running-req: 1, #token: 5491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.28, #queue-req: 0, 
[2025-07-28 01:05:46 DP0 TP0] Decode batch. #running-req: 1, #token: 6069, token usage: 0.00, cuda graph: True, gen throughput (token/s): 232.74, #queue-req: 0, 
[2025-07-28 01:05:46 DP1 TP0] Decode batch. #running-req: 1, #token: 5531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.46, #queue-req: 0, 
[2025-07-28 01:05:46 DP0 TP0] Decode batch. #running-req: 1, #token: 6109, token usage: 0.00, cuda graph: True, gen throughput (token/s): 232.92, #queue-req: 0, 
[2025-07-28 01:05:46 DP1 TP0] Decode batch. #running-req: 1, #token: 5571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.84, #queue-req: 0, 
[2025-07-28 01:05:46 DP0 TP0] Decode batch. #running-req: 1, #token: 6149, token usage: 0.00, cuda graph: True, gen throughput (token/s): 232.85, #queue-req: 0, 
[2025-07-28 01:05:46 DP1 TP0] Decode batch. #running-req: 1, #token: 5611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.77, #queue-req: 0, 
[2025-07-28 01:05:47 DP0 TP0] Decode batch. #running-req: 1, #token: 6189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 232.09, #queue-req: 0, 
[2025-07-28 01:05:47 DP1 TP0] Decode batch. #running-req: 1, #token: 5651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.59, #queue-req: 0, 
[2025-07-28 01:05:47 DP0 TP0] Decode batch. #running-req: 1, #token: 6229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 231.95, #queue-req: 0, 
[2025-07-28 01:05:47 DP1 TP0] Decode batch. #running-req: 1, #token: 5691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.43, #queue-req: 0, 
[2025-07-28 01:05:47 DP0 TP0] Decode batch. #running-req: 1, #token: 6269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 231.95, #queue-req: 0, 
[2025-07-28 01:05:47 DP1 TP0] Decode batch. #running-req: 1, #token: 5731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.32, #queue-req: 0, 
[2025-07-28 01:05:47 DP0 TP0] Decode batch. #running-req: 1, #token: 6309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 233.26, #queue-req: 0, 
[2025-07-28 01:05:47 DP1 TP0] Decode batch. #running-req: 1, #token: 5771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.66, #queue-req: 0, 
[2025-07-28 01:05:47 DP0 TP0] Decode batch. #running-req: 1, #token: 6349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 235.38, #queue-req: 0, 
[2025-07-28 01:05:47 DP1 TP0] Decode batch. #running-req: 1, #token: 5811, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.90, #queue-req: 0, 
[2025-07-28 01:05:47 DP0 TP0] Decode batch. #running-req: 1, #token: 6389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.48, #queue-req: 0, 
[2025-07-28 01:05:47 DP1 TP0] Decode batch. #running-req: 1, #token: 5851, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.76, #queue-req: 0, 
[2025-07-28 01:05:48 DP0 TP0] Decode batch. #running-req: 1, #token: 6429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.94, #queue-req: 0, 
[2025-07-28 01:05:48 DP1 TP0] Decode batch. #running-req: 1, #token: 5891, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.91, #queue-req: 0, 
[2025-07-28 01:05:48 DP0 TP0] Decode batch. #running-req: 1, #token: 6469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.40, #queue-req: 0, 
[2025-07-28 01:05:48 DP1 TP0] Decode batch. #running-req: 1, #token: 5931, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.04, #queue-req: 0, 
[2025-07-28 01:05:48 DP0 TP0] Decode batch. #running-req: 1, #token: 6509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.53, #queue-req: 0, 
[2025-07-28 01:05:48 DP1 TP0] Decode batch. #running-req: 1, #token: 5971, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.50, #queue-req: 0, 
[2025-07-28 01:05:48 DP0 TP0] Decode batch. #running-req: 1, #token: 6549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.82, #queue-req: 0, 
[2025-07-28 01:05:48 DP1 TP0] Decode batch. #running-req: 1, #token: 6011, token usage: 0.00, cuda graph: True, gen throughput (token/s): 218.72, #queue-req: 0, 
[2025-07-28 01:05:48 DP0 TP0] Decode batch. #running-req: 1, #token: 6589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.15, #queue-req: 0, 
[2025-07-28 01:05:48 DP1 TP0] Decode batch. #running-req: 1, #token: 6051, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.20, #queue-req: 0, 
[2025-07-28 01:05:48 DP0 TP0] Decode batch. #running-req: 1, #token: 6629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.96, #queue-req: 0, 
[2025-07-28 01:05:48 DP1 TP0] Decode batch. #running-req: 1, #token: 6091, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.32, #queue-req: 0, 
[2025-07-28 01:05:49 DP0 TP0] Decode batch. #running-req: 1, #token: 6669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.05, #queue-req: 0, 
[2025-07-28 01:05:49 DP1 TP0] Decode batch. #running-req: 1, #token: 6131, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.93, #queue-req: 0, 
[2025-07-28 01:05:49 DP0 TP0] Decode batch. #running-req: 1, #token: 6709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.81, #queue-req: 0, 
[2025-07-28 01:05:49 DP1 TP0] Decode batch. #running-req: 1, #token: 6171, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.08, #queue-req: 0, 
[2025-07-28 01:05:49 DP0 TP0] Decode batch. #running-req: 1, #token: 6749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.89, #queue-req: 0, 
[2025-07-28 01:05:49 DP1 TP0] Decode batch. #running-req: 1, #token: 6211, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.70, #queue-req: 0, 
[2025-07-28 01:05:49 DP0 TP0] Decode batch. #running-req: 1, #token: 6789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.89, #queue-req: 0, 
[2025-07-28 01:05:49 DP1 TP0] Decode batch. #running-req: 1, #token: 6251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.52, #queue-req: 0, 
[2025-07-28 01:05:49 DP0 TP0] Decode batch. #running-req: 1, #token: 6829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.04, #queue-req: 0, 
[2025-07-28 01:05:49 DP1 TP0] Decode batch. #running-req: 1, #token: 6291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.96, #queue-req: 0, 
[2025-07-28 01:05:49 DP0 TP0] Decode batch. #running-req: 1, #token: 6869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.12, #queue-req: 0, 
[2025-07-28 01:05:49 DP1 TP0] Decode batch. #running-req: 1, #token: 6331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.92, #queue-req: 0, 
[2025-07-28 01:05:50 DP0 TP0] Decode batch. #running-req: 1, #token: 6909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.07, #queue-req: 0, 
[2025-07-28 01:05:50 DP1 TP0] Decode batch. #running-req: 1, #token: 6371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.50, #queue-req: 0, 
[2025-07-28 01:05:50 DP0 TP0] Decode batch. #running-req: 1, #token: 6949, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.74, #queue-req: 0, 
[2025-07-28 01:05:50 DP1 TP0] Decode batch. #running-req: 1, #token: 6411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.06, #queue-req: 0, 
[2025-07-28 01:05:50 DP0 TP0] Decode batch. #running-req: 1, #token: 6989, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.03, #queue-req: 0, 
[2025-07-28 01:05:50 DP1 TP0] Decode batch. #running-req: 1, #token: 6451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.90, #queue-req: 0, 
[2025-07-28 01:05:50 DP0 TP0] Decode batch. #running-req: 1, #token: 7029, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.52, #queue-req: 0, 
[2025-07-28 01:05:50 DP1 TP0] Decode batch. #running-req: 1, #token: 6491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.28, #queue-req: 0, 
[2025-07-28 01:05:50 DP0 TP0] Decode batch. #running-req: 1, #token: 7069, token usage: 0.00, cuda graph: True, gen throughput (token/s): 235.58, #queue-req: 0, 
[2025-07-28 01:05:50 DP1 TP0] Decode batch. #running-req: 1, #token: 6531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.50, #queue-req: 0, 
[2025-07-28 01:05:50 DP0 TP0] Decode batch. #running-req: 1, #token: 7109, token usage: 0.00, cuda graph: True, gen throughput (token/s): 234.95, #queue-req: 0, 
[2025-07-28 01:05:50 DP1 TP0] Decode batch. #running-req: 1, #token: 6571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.18, #queue-req: 0, 
[2025-07-28 01:05:51 DP0 TP0] Decode batch. #running-req: 1, #token: 7149, token usage: 0.00, cuda graph: True, gen throughput (token/s): 235.91, #queue-req: 0, 
[2025-07-28 01:05:51 DP1 TP0] Decode batch. #running-req: 1, #token: 6611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.08, #queue-req: 0, 
[2025-07-28 01:05:51 DP0 TP0] Decode batch. #running-req: 1, #token: 7189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.02, #queue-req: 0, 
[2025-07-28 01:05:51 DP1 TP0] Decode batch. #running-req: 1, #token: 6651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.31, #queue-req: 0, 
[2025-07-28 01:05:51 DP0 TP0] Decode batch. #running-req: 1, #token: 7229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.02, #queue-req: 0, 
[2025-07-28 01:05:51 DP1 TP0] Decode batch. #running-req: 1, #token: 6691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.33, #queue-req: 0, 
[2025-07-28 01:05:51 DP0 TP0] Decode batch. #running-req: 1, #token: 7269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.27, #queue-req: 0, 
[2025-07-28 01:05:51 DP1 TP0] Decode batch. #running-req: 1, #token: 6731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.96, #queue-req: 0, 
[2025-07-28 01:05:51 DP0 TP0] Decode batch. #running-req: 1, #token: 7309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.48, #queue-req: 0, 
[2025-07-28 01:05:51 DP1 TP0] Decode batch. #running-req: 1, #token: 6771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.70, #queue-req: 0, 
[2025-07-28 01:05:51 DP0 TP0] Decode batch. #running-req: 1, #token: 7349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.16, #queue-req: 0, 
[2025-07-28 01:05:51 DP1 TP0] Decode batch. #running-req: 1, #token: 6811, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.54, #queue-req: 0, 
[2025-07-28 01:05:52 DP0 TP0] Decode batch. #running-req: 1, #token: 7389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.68, #queue-req: 0, 
[2025-07-28 01:05:52 DP1 TP0] Decode batch. #running-req: 1, #token: 6851, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.75, #queue-req: 0, 
[2025-07-28 01:05:52 DP0 TP0] Decode batch. #running-req: 1, #token: 7429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.08, #queue-req: 0, 
[2025-07-28 01:05:52 DP1 TP0] Decode batch. #running-req: 1, #token: 6891, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.36, #queue-req: 0, 
[2025-07-28 01:05:52 DP0 TP0] Decode batch. #running-req: 1, #token: 7469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.58, #queue-req: 0, 
[2025-07-28 01:05:52 DP1 TP0] Decode batch. #running-req: 1, #token: 6931, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.09, #queue-req: 0, 
[2025-07-28 01:05:52 DP0 TP0] Decode batch. #running-req: 1, #token: 7509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.58, #queue-req: 0, 
[2025-07-28 01:05:52 DP1 TP0] Decode batch. #running-req: 1, #token: 6971, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.79, #queue-req: 0, 
[2025-07-28 01:05:52 DP0 TP0] Decode batch. #running-req: 1, #token: 7549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.68, #queue-req: 0, 
[2025-07-28 01:05:52 DP1 TP0] Decode batch. #running-req: 1, #token: 7011, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.50, #queue-req: 0, 
[2025-07-28 01:05:52 DP0 TP0] Decode batch. #running-req: 1, #token: 7589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.98, #queue-req: 0, 
[2025-07-28 01:05:53 DP1 TP0] Decode batch. #running-req: 1, #token: 7051, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.68, #queue-req: 0, 
[2025-07-28 01:05:53 DP0 TP0] Decode batch. #running-req: 1, #token: 7629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.86, #queue-req: 0, 
[2025-07-28 01:05:53 DP1 TP0] Decode batch. #running-req: 1, #token: 7091, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.80, #queue-req: 0, 
[2025-07-28 01:05:53 DP0 TP0] Decode batch. #running-req: 1, #token: 7669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.02, #queue-req: 0, 
[2025-07-28 01:05:53 DP1 TP0] Decode batch. #running-req: 1, #token: 7131, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.45, #queue-req: 0, 
[2025-07-28 01:05:53 DP0 TP0] Decode batch. #running-req: 1, #token: 7709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.88, #queue-req: 0, 
[2025-07-28 01:05:53 DP1 TP0] Decode batch. #running-req: 1, #token: 7171, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.10, #queue-req: 0, 
[2025-07-28 01:05:53 DP0 TP0] Decode batch. #running-req: 1, #token: 7749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.67, #queue-req: 0, 
[2025-07-28 01:05:53 DP1 TP0] Decode batch. #running-req: 1, #token: 7211, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.01, #queue-req: 0, 
[2025-07-28 01:05:53 DP0 TP0] Decode batch. #running-req: 1, #token: 7789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.72, #queue-req: 0, 
[2025-07-28 01:05:53 DP1 TP0] Decode batch. #running-req: 1, #token: 7251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.00, #queue-req: 0, 
[2025-07-28 01:05:53 DP0 TP0] Decode batch. #running-req: 1, #token: 7829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.76, #queue-req: 0, 
[2025-07-28 01:05:54 DP1 TP0] Decode batch. #running-req: 1, #token: 7291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.10, #queue-req: 0, 
[2025-07-28 01:05:54 DP0 TP0] Decode batch. #running-req: 1, #token: 7869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.97, #queue-req: 0, 
[2025-07-28 01:05:54 DP1 TP0] Decode batch. #running-req: 1, #token: 7331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.10, #queue-req: 0, 
[2025-07-28 01:05:54 DP0 TP0] Decode batch. #running-req: 1, #token: 7909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.75, #queue-req: 0, 
[2025-07-28 01:05:54 DP1 TP0] Decode batch. #running-req: 1, #token: 7371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.71, #queue-req: 0, 
[2025-07-28 01:05:54 DP0 TP0] Decode batch. #running-req: 1, #token: 7949, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.64, #queue-req: 0, 
[2025-07-28 01:05:54 DP1 TP0] Decode batch. #running-req: 1, #token: 7411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.19, #queue-req: 0, 
[2025-07-28 01:05:54 DP0 TP0] Decode batch. #running-req: 1, #token: 7989, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.52, #queue-req: 0, 
[2025-07-28 01:05:54 DP1 TP0] Decode batch. #running-req: 1, #token: 7451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.21, #queue-req: 0, 
[2025-07-28 01:05:54 DP0 TP0] Decode batch. #running-req: 1, #token: 8029, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.54, #queue-req: 0, 
[2025-07-28 01:05:54 DP1 TP0] Decode batch. #running-req: 1, #token: 7491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.04, #queue-req: 0, 
[2025-07-28 01:05:54 DP0 TP0] Decode batch. #running-req: 1, #token: 8069, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.50, #queue-req: 0, 
[2025-07-28 01:05:55 DP1 TP0] Decode batch. #running-req: 1, #token: 7531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.70, #queue-req: 0, 
[2025-07-28 01:05:55 DP0 TP0] Decode batch. #running-req: 1, #token: 8109, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.82, #queue-req: 0, 
[2025-07-28 01:05:55 DP1 TP0] Decode batch. #running-req: 1, #token: 7571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.62, #queue-req: 0, 
[2025-07-28 01:05:55 DP0 TP0] Decode batch. #running-req: 1, #token: 8149, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.86, #queue-req: 0, 
[2025-07-28 01:05:55 DP1 TP0] Decode batch. #running-req: 1, #token: 7611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.31, #queue-req: 0, 
[2025-07-28 01:05:55 DP0 TP0] Decode batch. #running-req: 1, #token: 8189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.41, #queue-req: 0, 
[2025-07-28 01:05:55 DP1 TP0] Decode batch. #running-req: 1, #token: 7651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.49, #queue-req: 0, 
[2025-07-28 01:05:55 DP0 TP0] Decode batch. #running-req: 1, #token: 8229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.61, #queue-req: 0, 
[2025-07-28 01:05:55 DP1 TP0] Decode batch. #running-req: 1, #token: 7691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.05, #queue-req: 0, 
[2025-07-28 01:05:55 DP0 TP0] Decode batch. #running-req: 1, #token: 8269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.89, #queue-req: 0, 
[2025-07-28 01:05:55 DP1 TP0] Decode batch. #running-req: 1, #token: 7731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.55, #queue-req: 0, 
[2025-07-28 01:05:55 DP0 TP0] Decode batch. #running-req: 1, #token: 8309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.76, #queue-req: 0, 
[2025-07-28 01:05:56 DP1 TP0] Decode batch. #running-req: 1, #token: 7771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.25, #queue-req: 0, 
[2025-07-28 01:05:56 DP0 TP0] Decode batch. #running-req: 1, #token: 8349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.97, #queue-req: 0, 
[2025-07-28 01:05:56 DP1 TP0] Decode batch. #running-req: 1, #token: 7811, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.95, #queue-req: 0, 
[2025-07-28 01:05:56 DP0 TP0] Decode batch. #running-req: 1, #token: 8389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.52, #queue-req: 0, 
[2025-07-28 01:05:56 DP1 TP0] Decode batch. #running-req: 1, #token: 7851, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.27, #queue-req: 0, 
[2025-07-28 01:05:56] INFO:     127.0.0.1:56414 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:05:56 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:05:56 DP0 TP0] Decode batch. #running-req: 1, #token: 250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 175.70, #queue-req: 0, 
[2025-07-28 01:05:56 DP1 TP0] Decode batch. #running-req: 1, #token: 7891, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.24, #queue-req: 0, 
[2025-07-28 01:05:56 DP0 TP0] Decode batch. #running-req: 1, #token: 290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.69, #queue-req: 0, 
[2025-07-28 01:05:56 DP1 TP0] Decode batch. #running-req: 1, #token: 7931, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.78, #queue-req: 0, 
[2025-07-28 01:05:56 DP0 TP0] Decode batch. #running-req: 1, #token: 330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.63, #queue-req: 0, 
[2025-07-28 01:05:56 DP1 TP0] Decode batch. #running-req: 1, #token: 7971, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.27, #queue-req: 0, 
[2025-07-28 01:05:57 DP0 TP0] Decode batch. #running-req: 1, #token: 370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.48, #queue-req: 0, 
[2025-07-28 01:05:57 DP1 TP0] Decode batch. #running-req: 1, #token: 8011, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.88, #queue-req: 0, 
[2025-07-28 01:05:57 DP0 TP0] Decode batch. #running-req: 1, #token: 410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.75, #queue-req: 0, 
[2025-07-28 01:05:57 DP1 TP0] Decode batch. #running-req: 1, #token: 8051, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.75, #queue-req: 0, 
[2025-07-28 01:05:57 DP0 TP0] Decode batch. #running-req: 1, #token: 450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.76, #queue-req: 0, 
[2025-07-28 01:05:57 DP1 TP0] Decode batch. #running-req: 1, #token: 8091, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.98, #queue-req: 0, 
[2025-07-28 01:05:57 DP0 TP0] Decode batch. #running-req: 1, #token: 490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.88, #queue-req: 0, 
[2025-07-28 01:05:57 DP1 TP0] Decode batch. #running-req: 1, #token: 8131, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.75, #queue-req: 0, 
[2025-07-28 01:05:57 DP0 TP0] Decode batch. #running-req: 1, #token: 530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.45, #queue-req: 0, 
[2025-07-28 01:05:57 DP1 TP0] Decode batch. #running-req: 1, #token: 8171, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.38, #queue-req: 0, 
[2025-07-28 01:05:57 DP0 TP0] Decode batch. #running-req: 1, #token: 570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.85, #queue-req: 0, 
[2025-07-28 01:05:57 DP1 TP0] Decode batch. #running-req: 1, #token: 8211, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.90, #queue-req: 0, 
[2025-07-28 01:05:57 DP0 TP0] Decode batch. #running-req: 1, #token: 610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.85, #queue-req: 0, 
[2025-07-28 01:05:58 DP1 TP0] Decode batch. #running-req: 1, #token: 8251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.31, #queue-req: 0, 
[2025-07-28 01:05:58 DP0 TP0] Decode batch. #running-req: 1, #token: 650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 233.43, #queue-req: 0, 
[2025-07-28 01:05:58 DP1 TP0] Decode batch. #running-req: 1, #token: 8291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.34, #queue-req: 0, 
[2025-07-28 01:05:58 DP0 TP0] Decode batch. #running-req: 1, #token: 690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.80, #queue-req: 0, 
[2025-07-28 01:05:58 DP1 TP0] Decode batch. #running-req: 1, #token: 8331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.13, #queue-req: 0, 
[2025-07-28 01:05:58 DP0 TP0] Decode batch. #running-req: 1, #token: 730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.75, #queue-req: 0, 
[2025-07-28 01:05:58 DP1 TP0] Decode batch. #running-req: 1, #token: 8371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.93, #queue-req: 0, 
[2025-07-28 01:05:58 DP0 TP0] Decode batch. #running-req: 1, #token: 770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.80, #queue-req: 0, 
[2025-07-28 01:05:58 DP1 TP0] Decode batch. #running-req: 1, #token: 8411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 234.10, #queue-req: 0, 
[2025-07-28 01:05:58] INFO:     127.0.0.1:49516 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:05:58 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 95, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:05:58 DP0 TP0] Decode batch. #running-req: 1, #token: 810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.21, #queue-req: 0, 
[2025-07-28 01:05:58 DP1 TP0] Decode batch. #running-req: 1, #token: 210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 171.50, #queue-req: 0, 
[2025-07-28 01:05:58 DP0 TP0] Decode batch. #running-req: 1, #token: 850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.26, #queue-req: 0, 
[2025-07-28 01:05:59 DP1 TP0] Decode batch. #running-req: 1, #token: 250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.60, #queue-req: 0, 
[2025-07-28 01:05:59 DP0 TP0] Decode batch. #running-req: 1, #token: 890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.23, #queue-req: 0, 
[2025-07-28 01:05:59 DP1 TP0] Decode batch. #running-req: 1, #token: 290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.62, #queue-req: 0, 
[2025-07-28 01:05:59 DP0 TP0] Decode batch. #running-req: 1, #token: 930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.52, #queue-req: 0, 
[2025-07-28 01:05:59 DP1 TP0] Decode batch. #running-req: 1, #token: 330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.70, #queue-req: 0, 
[2025-07-28 01:05:59 DP0 TP0] Decode batch. #running-req: 1, #token: 970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.62, #queue-req: 0, 
[2025-07-28 01:05:59 DP1 TP0] Decode batch. #running-req: 1, #token: 370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.81, #queue-req: 0, 
[2025-07-28 01:05:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.47, #queue-req: 0, 
[2025-07-28 01:05:59 DP1 TP0] Decode batch. #running-req: 1, #token: 410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.86, #queue-req: 0, 
[2025-07-28 01:05:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.63, #queue-req: 0, 
[2025-07-28 01:05:59 DP1 TP0] Decode batch. #running-req: 1, #token: 450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.90, #queue-req: 0, 
[2025-07-28 01:05:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.13, #queue-req: 0, 
[2025-07-28 01:06:00 DP1 TP0] Decode batch. #running-req: 1, #token: 490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.06, #queue-req: 0, 
[2025-07-28 01:06:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.26, #queue-req: 0, 
[2025-07-28 01:06:00 DP1 TP0] Decode batch. #running-req: 1, #token: 530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.60, #queue-req: 0, 
[2025-07-28 01:06:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.36, #queue-req: 0, 
[2025-07-28 01:06:00 DP1 TP0] Decode batch. #running-req: 1, #token: 570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.45, #queue-req: 0, 
[2025-07-28 01:06:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.51, #queue-req: 0, 
[2025-07-28 01:06:00 DP1 TP0] Decode batch. #running-req: 1, #token: 610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.40, #queue-req: 0, 
[2025-07-28 01:06:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.59, #queue-req: 0, 
[2025-07-28 01:06:00 DP1 TP0] Decode batch. #running-req: 1, #token: 650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.24, #queue-req: 0, 
[2025-07-28 01:06:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.46, #queue-req: 0, 
[2025-07-28 01:06:00 DP1 TP0] Decode batch. #running-req: 1, #token: 690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.70, #queue-req: 0, 
[2025-07-28 01:06:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.88, #queue-req: 0, 
[2025-07-28 01:06:01 DP1 TP0] Decode batch. #running-req: 1, #token: 730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.08, #queue-req: 0, 
[2025-07-28 01:06:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.53, #queue-req: 0, 
[2025-07-28 01:06:01 DP1 TP0] Decode batch. #running-req: 1, #token: 770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.03, #queue-req: 0, 
[2025-07-28 01:06:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.42, #queue-req: 0, 
[2025-07-28 01:06:01 DP1 TP0] Decode batch. #running-req: 1, #token: 810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.58, #queue-req: 0, 
[2025-07-28 01:06:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.94, #queue-req: 0, 
[2025-07-28 01:06:01 DP1 TP0] Decode batch. #running-req: 1, #token: 850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.58, #queue-req: 0, 
[2025-07-28 01:06:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.91, #queue-req: 0, 
[2025-07-28 01:06:01 DP1 TP0] Decode batch. #running-req: 1, #token: 890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.32, #queue-req: 0, 
[2025-07-28 01:06:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.09, #queue-req: 0, 
[2025-07-28 01:06:01 DP1 TP0] Decode batch. #running-req: 1, #token: 930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.78, #queue-req: 0, 
[2025-07-28 01:06:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.37, #queue-req: 0, 
[2025-07-28 01:06:02 DP1 TP0] Decode batch. #running-req: 1, #token: 970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.95, #queue-req: 0, 
[2025-07-28 01:06:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.07, #queue-req: 0, 
[2025-07-28 01:06:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.85, #queue-req: 0, 
[2025-07-28 01:06:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.08, #queue-req: 0, 
[2025-07-28 01:06:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.15, #queue-req: 0, 
[2025-07-28 01:06:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.30, #queue-req: 0, 
[2025-07-28 01:06:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.58, #queue-req: 0, 
[2025-07-28 01:06:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.43, #queue-req: 0, 
[2025-07-28 01:06:02] INFO:     127.0.0.1:58530 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:06:02 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:06:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.69, #queue-req: 0, 
[2025-07-28 01:06:02 DP0 TP0] Decode batch. #running-req: 1, #token: 261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 173.71, #queue-req: 0, 
[2025-07-28 01:06:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.97, #queue-req: 0, 
[2025-07-28 01:06:02 DP0 TP0] Decode batch. #running-req: 1, #token: 301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.10, #queue-req: 0, 
[2025-07-28 01:06:03 DP1 TP0] Decode batch. #running-req: 1, #token: 1210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.08, #queue-req: 0, 
[2025-07-28 01:06:03 DP0 TP0] Decode batch. #running-req: 1, #token: 341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.96, #queue-req: 0, 
[2025-07-28 01:06:03 DP1 TP0] Decode batch. #running-req: 1, #token: 1250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.46, #queue-req: 0, 
[2025-07-28 01:06:03 DP0 TP0] Decode batch. #running-req: 1, #token: 381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.97, #queue-req: 0, 
[2025-07-28 01:06:03] INFO:     127.0.0.1:58536 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:06:03 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 78, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:06:03 DP1 TP0] Decode batch. #running-req: 1, #token: 172, token usage: 0.00, cuda graph: True, gen throughput (token/s): 175.41, #queue-req: 0, 
[2025-07-28 01:06:03 DP0 TP0] Decode batch. #running-req: 1, #token: 421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.12, #queue-req: 0, 
[2025-07-28 01:06:03 DP1 TP0] Decode batch. #running-req: 1, #token: 212, token usage: 0.00, cuda graph: True, gen throughput (token/s): 250.52, #queue-req: 0, 
[2025-07-28 01:06:03 DP0 TP0] Decode batch. #running-req: 1, #token: 461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.31, #queue-req: 0, 
[2025-07-28 01:06:03 DP1 TP0] Decode batch. #running-req: 1, #token: 252, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.13, #queue-req: 0, 
[2025-07-28 01:06:03 DP0 TP0] Decode batch. #running-req: 1, #token: 501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.11, #queue-req: 0, 
[2025-07-28 01:06:03 DP1 TP0] Decode batch. #running-req: 1, #token: 292, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.04, #queue-req: 0, 
[2025-07-28 01:06:03 DP0 TP0] Decode batch. #running-req: 1, #token: 541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.85, #queue-req: 0, 
[2025-07-28 01:06:04 DP1 TP0] Decode batch. #running-req: 1, #token: 332, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.83, #queue-req: 0, 
[2025-07-28 01:06:04 DP0 TP0] Decode batch. #running-req: 1, #token: 581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.66, #queue-req: 0, 
[2025-07-28 01:06:04 DP1 TP0] Decode batch. #running-req: 1, #token: 372, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.70, #queue-req: 0, 
[2025-07-28 01:06:04 DP0 TP0] Decode batch. #running-req: 1, #token: 621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.70, #queue-req: 0, 
[2025-07-28 01:06:04 DP1 TP0] Decode batch. #running-req: 1, #token: 412, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.89, #queue-req: 0, 
[2025-07-28 01:06:04 DP0 TP0] Decode batch. #running-req: 1, #token: 661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.77, #queue-req: 0, 
[2025-07-28 01:06:04 DP1 TP0] Decode batch. #running-req: 1, #token: 452, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.86, #queue-req: 0, 
[2025-07-28 01:06:04 DP0 TP0] Decode batch. #running-req: 1, #token: 701, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.00, #queue-req: 0, 
[2025-07-28 01:06:04 DP1 TP0] Decode batch. #running-req: 1, #token: 492, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.83, #queue-req: 0, 
[2025-07-28 01:06:04 DP0 TP0] Decode batch. #running-req: 1, #token: 741, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.99, #queue-req: 0, 
[2025-07-28 01:06:04 DP1 TP0] Decode batch. #running-req: 1, #token: 532, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.57, #queue-req: 0, 
[2025-07-28 01:06:04 DP0 TP0] Decode batch. #running-req: 1, #token: 781, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.74, #queue-req: 0, 
[2025-07-28 01:06:05 DP1 TP0] Decode batch. #running-req: 1, #token: 572, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.09, #queue-req: 0, 
[2025-07-28 01:06:05 DP0 TP0] Decode batch. #running-req: 1, #token: 821, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.24, #queue-req: 0, 
[2025-07-28 01:06:05 DP1 TP0] Decode batch. #running-req: 1, #token: 612, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.86, #queue-req: 0, 
[2025-07-28 01:06:05 DP0 TP0] Decode batch. #running-req: 1, #token: 861, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.21, #queue-req: 0, 
[2025-07-28 01:06:05 DP1 TP0] Decode batch. #running-req: 1, #token: 652, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.13, #queue-req: 0, 
[2025-07-28 01:06:05 DP0 TP0] Decode batch. #running-req: 1, #token: 901, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.24, #queue-req: 0, 
[2025-07-28 01:06:05 DP1 TP0] Decode batch. #running-req: 1, #token: 692, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.33, #queue-req: 0, 
[2025-07-28 01:06:05 DP0 TP0] Decode batch. #running-req: 1, #token: 941, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.72, #queue-req: 0, 
[2025-07-28 01:06:05 DP1 TP0] Decode batch. #running-req: 1, #token: 732, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.39, #queue-req: 0, 
[2025-07-28 01:06:05 DP0 TP0] Decode batch. #running-req: 1, #token: 981, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.77, #queue-req: 0, 
[2025-07-28 01:06:05 DP1 TP0] Decode batch. #running-req: 1, #token: 772, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.15, #queue-req: 0, 
[2025-07-28 01:06:05 DP0 TP0] Decode batch. #running-req: 1, #token: 1021, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.89, #queue-req: 0, 
[2025-07-28 01:06:06 DP1 TP0] Decode batch. #running-req: 1, #token: 812, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.88, #queue-req: 0, 
[2025-07-28 01:06:06 DP0 TP0] Decode batch. #running-req: 1, #token: 1061, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.64, #queue-req: 0, 
[2025-07-28 01:06:06 DP1 TP0] Decode batch. #running-req: 1, #token: 852, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.00, #queue-req: 0, 
[2025-07-28 01:06:06 DP0 TP0] Decode batch. #running-req: 1, #token: 1101, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.52, #queue-req: 0, 
[2025-07-28 01:06:06 DP1 TP0] Decode batch. #running-req: 1, #token: 892, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.77, #queue-req: 0, 
[2025-07-28 01:06:06 DP0 TP0] Decode batch. #running-req: 1, #token: 1141, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.28, #queue-req: 0, 
[2025-07-28 01:06:06 DP1 TP0] Decode batch. #running-req: 1, #token: 932, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.13, #queue-req: 0, 
[2025-07-28 01:06:06 DP0 TP0] Decode batch. #running-req: 1, #token: 1181, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.99, #queue-req: 0, 
[2025-07-28 01:06:06 DP1 TP0] Decode batch. #running-req: 1, #token: 972, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.33, #queue-req: 0, 
[2025-07-28 01:06:06 DP0 TP0] Decode batch. #running-req: 1, #token: 1221, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.00, #queue-req: 0, 
[2025-07-28 01:06:06 DP1 TP0] Decode batch. #running-req: 1, #token: 1012, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.02, #queue-req: 0, 
[2025-07-28 01:06:06 DP0 TP0] Decode batch. #running-req: 1, #token: 1261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.13, #queue-req: 0, 
[2025-07-28 01:06:07 DP1 TP0] Decode batch. #running-req: 1, #token: 1052, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.47, #queue-req: 0, 
[2025-07-28 01:06:07 DP0 TP0] Decode batch. #running-req: 1, #token: 1301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.88, #queue-req: 0, 
[2025-07-28 01:06:07 DP1 TP0] Decode batch. #running-req: 1, #token: 1092, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.79, #queue-req: 0, 
[2025-07-28 01:06:07 DP0 TP0] Decode batch. #running-req: 1, #token: 1341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.61, #queue-req: 0, 
[2025-07-28 01:06:07 DP1 TP0] Decode batch. #running-req: 1, #token: 1132, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.81, #queue-req: 0, 
[2025-07-28 01:06:07 DP0 TP0] Decode batch. #running-req: 1, #token: 1381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.62, #queue-req: 0, 
[2025-07-28 01:06:07 DP1 TP0] Decode batch. #running-req: 1, #token: 1172, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.99, #queue-req: 0, 
[2025-07-28 01:06:07 DP0 TP0] Decode batch. #running-req: 1, #token: 1421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.68, #queue-req: 0, 
[2025-07-28 01:06:07 DP1 TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.81, #queue-req: 0, 
[2025-07-28 01:06:07] INFO:     127.0.0.1:49248 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:06:07 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 146, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:06:07 DP0 TP0] Decode batch. #running-req: 2, #token: 1621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.21, #queue-req: 0, 
[2025-07-28 01:06:07 DP0 TP0] Decode batch. #running-req: 2, #token: 1701, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.72, #queue-req: 0, 
[2025-07-28 01:06:08 DP0 TP0] Decode batch. #running-req: 2, #token: 1781, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.97, #queue-req: 0, 
[2025-07-28 01:06:08 DP0 TP0] Decode batch. #running-req: 2, #token: 1861, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.10, #queue-req: 0, 
[2025-07-28 01:06:08 DP0 TP0] Decode batch. #running-req: 2, #token: 1941, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.82, #queue-req: 0, 
[2025-07-28 01:06:08 DP0 TP0] Decode batch. #running-req: 2, #token: 2021, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.80, #queue-req: 0, 
[2025-07-28 01:06:08 DP0 TP0] Decode batch. #running-req: 2, #token: 2101, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.78, #queue-req: 0, 
[2025-07-28 01:06:09 DP0 TP0] Decode batch. #running-req: 2, #token: 2181, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.78, #queue-req: 0, 
[2025-07-28 01:06:09 DP0 TP0] Decode batch. #running-req: 2, #token: 2261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.72, #queue-req: 0, 
[2025-07-28 01:06:09 DP0 TP0] Decode batch. #running-req: 2, #token: 2341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.52, #queue-req: 0, 
[2025-07-28 01:06:09 DP0 TP0] Decode batch. #running-req: 2, #token: 2421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.39, #queue-req: 0, 
[2025-07-28 01:06:09 DP0 TP0] Decode batch. #running-req: 2, #token: 2501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.79, #queue-req: 0, 
[2025-07-28 01:06:09 DP0 TP0] Decode batch. #running-req: 2, #token: 2581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.34, #queue-req: 0, 
[2025-07-28 01:06:10 DP0 TP0] Decode batch. #running-req: 2, #token: 2661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.66, #queue-req: 0, 
[2025-07-28 01:06:10 DP0 TP0] Decode batch. #running-req: 2, #token: 2741, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.17, #queue-req: 0, 
[2025-07-28 01:06:10 DP0 TP0] Decode batch. #running-req: 2, #token: 2821, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.17, #queue-req: 0, 
[2025-07-28 01:06:10 DP0 TP0] Decode batch. #running-req: 2, #token: 2901, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.46, #queue-req: 0, 
[2025-07-28 01:06:10 DP0 TP0] Decode batch. #running-req: 2, #token: 2981, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.50, #queue-req: 0, 
[2025-07-28 01:06:10 DP0 TP0] Decode batch. #running-req: 2, #token: 3061, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.56, #queue-req: 0, 
[2025-07-28 01:06:11 DP0 TP0] Decode batch. #running-req: 2, #token: 3141, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.76, #queue-req: 0, 
[2025-07-28 01:06:11 DP0 TP0] Decode batch. #running-req: 2, #token: 3221, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.53, #queue-req: 0, 
[2025-07-28 01:06:11 DP0 TP0] Decode batch. #running-req: 2, #token: 3301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.45, #queue-req: 0, 
[2025-07-28 01:06:11] INFO:     127.0.0.1:49236 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:06:11 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:06:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1125, token usage: 0.00, cuda graph: True, gen throughput (token/s): 259.42, #queue-req: 0, 
[2025-07-28 01:06:11 DP1 TP0] Decode batch. #running-req: 1, #token: 211, token usage: 0.00, cuda graph: True, gen throughput (token/s): 9.95, #queue-req: 0, 
[2025-07-28 01:06:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1165, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.27, #queue-req: 0, 
[2025-07-28 01:06:11 DP1 TP0] Decode batch. #running-req: 1, #token: 251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 235.12, #queue-req: 0, 
[2025-07-28 01:06:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1205, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.49, #queue-req: 0, 
[2025-07-28 01:06:12 DP1 TP0] Decode batch. #running-req: 1, #token: 291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.95, #queue-req: 0, 
[2025-07-28 01:06:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1245, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.67, #queue-req: 0, 
[2025-07-28 01:06:12 DP1 TP0] Decode batch. #running-req: 1, #token: 331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.21, #queue-req: 0, 
[2025-07-28 01:06:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1285, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.66, #queue-req: 0, 
[2025-07-28 01:06:12 DP1 TP0] Decode batch. #running-req: 1, #token: 371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.42, #queue-req: 0, 
[2025-07-28 01:06:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1325, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.95, #queue-req: 0, 
[2025-07-28 01:06:12 DP1 TP0] Decode batch. #running-req: 1, #token: 411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.30, #queue-req: 0, 
[2025-07-28 01:06:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1365, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.13, #queue-req: 0, 
[2025-07-28 01:06:12 DP1 TP0] Decode batch. #running-req: 1, #token: 451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.11, #queue-req: 0, 
[2025-07-28 01:06:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1405, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.00, #queue-req: 0, 
[2025-07-28 01:06:12 DP1 TP0] Decode batch. #running-req: 1, #token: 491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.19, #queue-req: 0, 
[2025-07-28 01:06:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1445, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.38, #queue-req: 0, 
[2025-07-28 01:06:13 DP1 TP0] Decode batch. #running-req: 1, #token: 531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.84, #queue-req: 0, 
[2025-07-28 01:06:13] INFO:     127.0.0.1:49254 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:06:13 DP0 TP0] Decode batch. #running-req: 1, #token: 0, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.75, #queue-req: 0, 
[2025-07-28 01:06:13 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 173, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:06:13 DP1 TP0] Decode batch. #running-req: 1, #token: 571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.42, #queue-req: 0, 
[2025-07-28 01:06:13 DP0 TP0] Decode batch. #running-req: 1, #token: 301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 181.28, #queue-req: 0, 
[2025-07-28 01:06:13 DP1 TP0] Decode batch. #running-req: 1, #token: 611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.00, #queue-req: 0, 
[2025-07-28 01:06:13 DP0 TP0] Decode batch. #running-req: 1, #token: 341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.02, #queue-req: 0, 
[2025-07-28 01:06:13 DP1 TP0] Decode batch. #running-req: 1, #token: 651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.81, #queue-req: 0, 
[2025-07-28 01:06:13 DP0 TP0] Decode batch. #running-req: 1, #token: 381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.91, #queue-req: 0, 
[2025-07-28 01:06:13 DP1 TP0] Decode batch. #running-req: 1, #token: 691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.03, #queue-req: 0, 
[2025-07-28 01:06:13 DP0 TP0] Decode batch. #running-req: 1, #token: 421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.13, #queue-req: 0, 
[2025-07-28 01:06:13 DP1 TP0] Decode batch. #running-req: 1, #token: 731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.09, #queue-req: 0, 
[2025-07-28 01:06:13 DP0 TP0] Decode batch. #running-req: 1, #token: 461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.31, #queue-req: 0, 
[2025-07-28 01:06:14 DP1 TP0] Decode batch. #running-req: 1, #token: 771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.71, #queue-req: 0, 
[2025-07-28 01:06:14 DP0 TP0] Decode batch. #running-req: 1, #token: 501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.17, #queue-req: 0, 
[2025-07-28 01:06:14 DP1 TP0] Decode batch. #running-req: 1, #token: 811, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.76, #queue-req: 0, 
[2025-07-28 01:06:14 DP0 TP0] Decode batch. #running-req: 1, #token: 541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.82, #queue-req: 0, 
[2025-07-28 01:06:14 DP1 TP0] Decode batch. #running-req: 1, #token: 851, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.43, #queue-req: 0, 
[2025-07-28 01:06:14 DP0 TP0] Decode batch. #running-req: 1, #token: 581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.41, #queue-req: 0, 
[2025-07-28 01:06:14 DP1 TP0] Decode batch. #running-req: 1, #token: 891, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.44, #queue-req: 0, 
[2025-07-28 01:06:14 DP0 TP0] Decode batch. #running-req: 1, #token: 621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.47, #queue-req: 0, 
[2025-07-28 01:06:14 DP1 TP0] Decode batch. #running-req: 1, #token: 931, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.60, #queue-req: 0, 
[2025-07-28 01:06:14 DP0 TP0] Decode batch. #running-req: 1, #token: 661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.75, #queue-req: 0, 
[2025-07-28 01:06:14 DP1 TP0] Decode batch. #running-req: 1, #token: 971, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.71, #queue-req: 0, 
[2025-07-28 01:06:14 DP0 TP0] Decode batch. #running-req: 1, #token: 701, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.91, #queue-req: 0, 
[2025-07-28 01:06:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1011, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.95, #queue-req: 0, 
[2025-07-28 01:06:15 DP0 TP0] Decode batch. #running-req: 1, #token: 741, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.99, #queue-req: 0, 
[2025-07-28 01:06:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1051, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.01, #queue-req: 0, 
[2025-07-28 01:06:15 DP0 TP0] Decode batch. #running-req: 1, #token: 781, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.66, #queue-req: 0, 
[2025-07-28 01:06:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1091, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.37, #queue-req: 0, 
[2025-07-28 01:06:15 DP0 TP0] Decode batch. #running-req: 1, #token: 821, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.17, #queue-req: 0, 
[2025-07-28 01:06:15] INFO:     127.0.0.1:49266 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:06:15 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 178, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:06:15 DP1 TP0] Decode batch. #running-req: 1, #token: 275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 174.55, #queue-req: 0, 
[2025-07-28 01:06:15 DP0 TP0] Decode batch. #running-req: 1, #token: 861, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.33, #queue-req: 0, 
[2025-07-28 01:06:15 DP1 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.01, #queue-req: 0, 
[2025-07-28 01:06:15 DP0 TP0] Decode batch. #running-req: 1, #token: 901, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.30, #queue-req: 0, 
[2025-07-28 01:06:15 DP1 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.43, #queue-req: 0, 
[2025-07-28 01:06:15 DP0 TP0] Decode batch. #running-req: 1, #token: 941, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.71, #queue-req: 0, 
[2025-07-28 01:06:16 DP1 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.22, #queue-req: 0, 
[2025-07-28 01:06:16 DP0 TP0] Decode batch. #running-req: 1, #token: 981, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.72, #queue-req: 0, 
[2025-07-28 01:06:16 DP1 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.42, #queue-req: 0, 
[2025-07-28 01:06:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1021, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.82, #queue-req: 0, 
[2025-07-28 01:06:16 DP1 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.45, #queue-req: 0, 
[2025-07-28 01:06:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1061, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.64, #queue-req: 0, 
[2025-07-28 01:06:16 DP1 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.07, #queue-req: 0, 
[2025-07-28 01:06:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1101, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.36, #queue-req: 0, 
[2025-07-28 01:06:16 DP1 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.84, #queue-req: 0, 
[2025-07-28 01:06:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1141, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.47, #queue-req: 0, 
[2025-07-28 01:06:16 DP1 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.81, #queue-req: 0, 
[2025-07-28 01:06:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1181, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.81, #queue-req: 0, 
[2025-07-28 01:06:17 DP1 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.70, #queue-req: 0, 
[2025-07-28 01:06:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1221, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.00, #queue-req: 0, 
[2025-07-28 01:06:17 DP1 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.95, #queue-req: 0, 
[2025-07-28 01:06:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.94, #queue-req: 0, 
[2025-07-28 01:06:17] INFO:     127.0.0.1:38122 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:06:17 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 88, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:06:17 DP1 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.67, #queue-req: 0, 
[2025-07-28 01:06:17 DP0 TP0] Decode batch. #running-req: 1, #token: 198, token usage: 0.00, cuda graph: True, gen throughput (token/s): 180.14, #queue-req: 0, 
[2025-07-28 01:06:17 DP1 TP0] Decode batch. #running-req: 1, #token: 755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.18, #queue-req: 0, 
[2025-07-28 01:06:17 DP0 TP0] Decode batch. #running-req: 1, #token: 238, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.37, #queue-req: 0, 
[2025-07-28 01:06:17 DP1 TP0] Decode batch. #running-req: 1, #token: 795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.56, #queue-req: 0, 
[2025-07-28 01:06:17 DP0 TP0] Decode batch. #running-req: 1, #token: 278, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.49, #queue-req: 0, 
[2025-07-28 01:06:17 DP1 TP0] Decode batch. #running-req: 1, #token: 835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.36, #queue-req: 0, 
[2025-07-28 01:06:17 DP0 TP0] Decode batch. #running-req: 1, #token: 318, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.94, #queue-req: 0, 
[2025-07-28 01:06:17] INFO:     127.0.0.1:38138 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:06:17 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 153, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:06:18 DP1 TP0] Decode batch. #running-req: 1, #token: 258, token usage: 0.00, cuda graph: True, gen throughput (token/s): 178.25, #queue-req: 0, 
[2025-07-28 01:06:18 DP0 TP0] Decode batch. #running-req: 1, #token: 358, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.06, #queue-req: 0, 
[2025-07-28 01:06:18 DP1 TP0] Decode batch. #running-req: 1, #token: 298, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.67, #queue-req: 0, 
[2025-07-28 01:06:18 DP0 TP0] Decode batch. #running-req: 1, #token: 398, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.97, #queue-req: 0, 
[2025-07-28 01:06:18 DP1 TP0] Decode batch. #running-req: 1, #token: 338, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.56, #queue-req: 0, 
[2025-07-28 01:06:18 DP0 TP0] Decode batch. #running-req: 1, #token: 438, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.29, #queue-req: 0, 
[2025-07-28 01:06:18 DP1 TP0] Decode batch. #running-req: 1, #token: 378, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.49, #queue-req: 0, 
[2025-07-28 01:06:18 DP0 TP0] Decode batch. #running-req: 1, #token: 478, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.29, #queue-req: 0, 
[2025-07-28 01:06:18 DP1 TP0] Decode batch. #running-req: 1, #token: 418, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.71, #queue-req: 0, 
[2025-07-28 01:06:18 DP0 TP0] Decode batch. #running-req: 1, #token: 518, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.31, #queue-req: 0, 
[2025-07-28 01:06:18 DP1 TP0] Decode batch. #running-req: 1, #token: 458, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.83, #queue-req: 0, 
[2025-07-28 01:06:18 DP0 TP0] Decode batch. #running-req: 1, #token: 558, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.73, #queue-req: 0, 
[2025-07-28 01:06:19 DP1 TP0] Decode batch. #running-req: 1, #token: 498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.87, #queue-req: 0, 
[2025-07-28 01:06:19 DP0 TP0] Decode batch. #running-req: 1, #token: 598, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.72, #queue-req: 0, 
[2025-07-28 01:06:19 DP1 TP0] Decode batch. #running-req: 1, #token: 538, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.31, #queue-req: 0, 
[2025-07-28 01:06:19 DP0 TP0] Decode batch. #running-req: 1, #token: 638, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.56, #queue-req: 0, 
[2025-07-28 01:06:19 DP1 TP0] Decode batch. #running-req: 1, #token: 578, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.06, #queue-req: 0, 
[2025-07-28 01:06:19 DP0 TP0] Decode batch. #running-req: 1, #token: 678, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.74, #queue-req: 0, 
[2025-07-28 01:06:19 DP1 TP0] Decode batch. #running-req: 1, #token: 618, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.75, #queue-req: 0, 
[2025-07-28 01:06:19 DP0 TP0] Decode batch. #running-req: 1, #token: 718, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.65, #queue-req: 0, 
[2025-07-28 01:06:19 DP1 TP0] Decode batch. #running-req: 1, #token: 658, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.16, #queue-req: 0, 
[2025-07-28 01:06:19] INFO:     127.0.0.1:38142 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:06:19 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 123, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:06:19 DP0 TP0] Decode batch. #running-req: 1, #token: 213, token usage: 0.00, cuda graph: True, gen throughput (token/s): 176.67, #queue-req: 0, 
[2025-07-28 01:06:19 DP1 TP0] Decode batch. #running-req: 1, #token: 698, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.16, #queue-req: 0, 
[2025-07-28 01:06:19 DP0 TP0] Decode batch. #running-req: 1, #token: 253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.43, #queue-req: 0, 
[2025-07-28 01:06:20 DP1 TP0] Decode batch. #running-req: 1, #token: 738, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.22, #queue-req: 0, 
[2025-07-28 01:06:20 DP0 TP0] Decode batch. #running-req: 1, #token: 293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.57, #queue-req: 0, 
[2025-07-28 01:06:20 DP1 TP0] Decode batch. #running-req: 1, #token: 778, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.97, #queue-req: 0, 
[2025-07-28 01:06:20 DP0 TP0] Decode batch. #running-req: 1, #token: 333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.49, #queue-req: 0, 
[2025-07-28 01:06:20 DP1 TP0] Decode batch. #running-req: 1, #token: 818, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.69, #queue-req: 0, 
[2025-07-28 01:06:20 DP0 TP0] Decode batch. #running-req: 1, #token: 373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.55, #queue-req: 0, 
[2025-07-28 01:06:20 DP1 TP0] Decode batch. #running-req: 1, #token: 858, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.67, #queue-req: 0, 
[2025-07-28 01:06:20 DP0 TP0] Decode batch. #running-req: 1, #token: 413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.65, #queue-req: 0, 
[2025-07-28 01:06:20 DP1 TP0] Decode batch. #running-req: 1, #token: 898, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.61, #queue-req: 0, 
[2025-07-28 01:06:20 DP0 TP0] Decode batch. #running-req: 1, #token: 453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.72, #queue-req: 0, 
[2025-07-28 01:06:20 DP1 TP0] Decode batch. #running-req: 1, #token: 938, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.98, #queue-req: 0, 
[2025-07-28 01:06:20 DP0 TP0] Decode batch. #running-req: 1, #token: 493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.72, #queue-req: 0, 
[2025-07-28 01:06:21 DP1 TP0] Decode batch. #running-req: 1, #token: 978, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.04, #queue-req: 0, 
[2025-07-28 01:06:21 DP0 TP0] Decode batch. #running-req: 1, #token: 533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.08, #queue-req: 0, 
[2025-07-28 01:06:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1018, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.99, #queue-req: 0, 
[2025-07-28 01:06:21 DP0 TP0] Decode batch. #running-req: 1, #token: 573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.08, #queue-req: 0, 
[2025-07-28 01:06:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1058, token usage: 0.00, cuda graph: True, gen throughput (token/s): 219.58, #queue-req: 0, 
[2025-07-28 01:06:21 DP0 TP0] Decode batch. #running-req: 1, #token: 613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.97, #queue-req: 0, 
[2025-07-28 01:06:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1098, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.03, #queue-req: 0, 
[2025-07-28 01:06:21 DP0 TP0] Decode batch. #running-req: 1, #token: 653, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.92, #queue-req: 0, 
[2025-07-28 01:06:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1138, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.38, #queue-req: 0, 
[2025-07-28 01:06:21 DP0 TP0] Decode batch. #running-req: 1, #token: 693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.95, #queue-req: 0, 
[2025-07-28 01:06:21] INFO:     127.0.0.1:38144 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:06:21 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 129, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:06:21 DP1 TP0] Decode batch. #running-req: 1, #token: 223, token usage: 0.00, cuda graph: True, gen throughput (token/s): 177.04, #queue-req: 0, 
[2025-07-28 01:06:21 DP0 TP0] Decode batch. #running-req: 1, #token: 733, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.34, #queue-req: 0, 
[2025-07-28 01:06:22 DP1 TP0] Decode batch. #running-req: 1, #token: 263, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.09, #queue-req: 0, 
[2025-07-28 01:06:22 DP0 TP0] Decode batch. #running-req: 1, #token: 773, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.07, #queue-req: 0, 
[2025-07-28 01:06:22 DP1 TP0] Decode batch. #running-req: 1, #token: 303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.14, #queue-req: 0, 
[2025-07-28 01:06:22 DP0 TP0] Decode batch. #running-req: 1, #token: 813, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.82, #queue-req: 0, 
[2025-07-28 01:06:22 DP1 TP0] Decode batch. #running-req: 1, #token: 343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.94, #queue-req: 0, 
[2025-07-28 01:06:22 DP0 TP0] Decode batch. #running-req: 1, #token: 853, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.19, #queue-req: 0, 
[2025-07-28 01:06:22 DP1 TP0] Decode batch. #running-req: 1, #token: 383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.06, #queue-req: 0, 
[2025-07-28 01:06:22 DP0 TP0] Decode batch. #running-req: 1, #token: 893, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.37, #queue-req: 0, 
[2025-07-28 01:06:22 DP1 TP0] Decode batch. #running-req: 1, #token: 423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.32, #queue-req: 0, 
[2025-07-28 01:06:22 DP0 TP0] Decode batch. #running-req: 1, #token: 933, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.09, #queue-req: 0, 
[2025-07-28 01:06:22 DP1 TP0] Decode batch. #running-req: 1, #token: 463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.38, #queue-req: 0, 
[2025-07-28 01:06:22 DP0 TP0] Decode batch. #running-req: 1, #token: 973, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.37, #queue-req: 0, 
[2025-07-28 01:06:23 DP1 TP0] Decode batch. #running-req: 1, #token: 503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.36, #queue-req: 0, 
[2025-07-28 01:06:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1013, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.36, #queue-req: 0, 
[2025-07-28 01:06:23 DP1 TP0] Decode batch. #running-req: 1, #token: 543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.79, #queue-req: 0, 
[2025-07-28 01:06:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1053, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.44, #queue-req: 0, 
[2025-07-28 01:06:23 DP1 TP0] Decode batch. #running-req: 1, #token: 583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.91, #queue-req: 0, 
[2025-07-28 01:06:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1093, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.23, #queue-req: 0, 
[2025-07-28 01:06:23 DP1 TP0] Decode batch. #running-req: 1, #token: 623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.78, #queue-req: 0, 
[2025-07-28 01:06:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1133, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.42, #queue-req: 0, 
[2025-07-28 01:06:23 DP1 TP0] Decode batch. #running-req: 1, #token: 663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.41, #queue-req: 0, 
[2025-07-28 01:06:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1173, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.87, #queue-req: 0, 
[2025-07-28 01:06:23 DP1 TP0] Decode batch. #running-req: 1, #token: 703, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.01, #queue-req: 0, 
[2025-07-28 01:06:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1213, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.45, #queue-req: 0, 
[2025-07-28 01:06:24 DP1 TP0] Decode batch. #running-req: 1, #token: 743, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.70, #queue-req: 0, 
[2025-07-28 01:06:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.86, #queue-req: 0, 
[2025-07-28 01:06:24 DP1 TP0] Decode batch. #running-req: 1, #token: 783, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.77, #queue-req: 0, 
[2025-07-28 01:06:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.56, #queue-req: 0, 
[2025-07-28 01:06:24 DP1 TP0] Decode batch. #running-req: 1, #token: 823, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.67, #queue-req: 0, 
[2025-07-28 01:06:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.24, #queue-req: 0, 
[2025-07-28 01:06:24 DP1 TP0] Decode batch. #running-req: 1, #token: 863, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.31, #queue-req: 0, 
[2025-07-28 01:06:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.49, #queue-req: 0, 
[2025-07-28 01:06:24 DP1 TP0] Decode batch. #running-req: 1, #token: 903, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.63, #queue-req: 0, 
[2025-07-28 01:06:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.64, #queue-req: 0, 
[2025-07-28 01:06:24 DP1 TP0] Decode batch. #running-req: 1, #token: 943, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.97, #queue-req: 0, 
[2025-07-28 01:06:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.88, #queue-req: 0, 
[2025-07-28 01:06:25 DP1 TP0] Decode batch. #running-req: 1, #token: 983, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.12, #queue-req: 0, 
[2025-07-28 01:06:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.23, #queue-req: 0, 
[2025-07-28 01:06:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1023, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.01, #queue-req: 0, 
[2025-07-28 01:06:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.91, #queue-req: 0, 
[2025-07-28 01:06:25] INFO:     127.0.0.1:38162 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:06:25 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 91, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:06:25 DP0 TP0] Decode batch. #running-req: 2, #token: 1715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 220.46, #queue-req: 0, 
[2025-07-28 01:06:25 DP0 TP0] Decode batch. #running-req: 2, #token: 1795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 459.59, #queue-req: 0, 
[2025-07-28 01:06:25 DP0 TP0] Decode batch. #running-req: 2, #token: 1875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 455.68, #queue-req: 0, 
[2025-07-28 01:06:26 DP0 TP0] Decode batch. #running-req: 2, #token: 1955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 454.86, #queue-req: 0, 
[2025-07-28 01:06:26 DP0 TP0] Decode batch. #running-req: 2, #token: 2035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 455.32, #queue-req: 0, 
[2025-07-28 01:06:26 DP0 TP0] Decode batch. #running-req: 2, #token: 2115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 454.11, #queue-req: 0, 
[2025-07-28 01:06:26 DP0 TP0] Decode batch. #running-req: 2, #token: 2195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 458.48, #queue-req: 0, 
[2025-07-28 01:06:26 DP0 TP0] Decode batch. #running-req: 2, #token: 2275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.52, #queue-req: 0, 
[2025-07-28 01:06:26 DP0 TP0] Decode batch. #running-req: 2, #token: 2355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.87, #queue-req: 0, 
[2025-07-28 01:06:27 DP0 TP0] Decode batch. #running-req: 2, #token: 2435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.43, #queue-req: 0, 
[2025-07-28 01:06:27 DP0 TP0] Decode batch. #running-req: 2, #token: 2515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.82, #queue-req: 0, 
[2025-07-28 01:06:27 DP0 TP0] Decode batch. #running-req: 2, #token: 2595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.20, #queue-req: 0, 
[2025-07-28 01:06:27 DP0 TP0] Decode batch. #running-req: 2, #token: 2675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.21, #queue-req: 0, 
[2025-07-28 01:06:27 DP0 TP0] Decode batch. #running-req: 2, #token: 2755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.11, #queue-req: 0, 
[2025-07-28 01:06:27 DP0 TP0] Decode batch. #running-req: 2, #token: 2835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.20, #queue-req: 0, 
[2025-07-28 01:06:28 DP0 TP0] Decode batch. #running-req: 2, #token: 2915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.44, #queue-req: 0, 
[2025-07-28 01:06:28 DP0 TP0] Decode batch. #running-req: 2, #token: 2995, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.71, #queue-req: 0, 
[2025-07-28 01:06:28 DP0 TP0] Decode batch. #running-req: 2, #token: 3075, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.08, #queue-req: 0, 
[2025-07-28 01:06:28 DP0 TP0] Decode batch. #running-req: 2, #token: 3155, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.99, #queue-req: 0, 
[2025-07-28 01:06:28 DP0 TP0] Decode batch. #running-req: 2, #token: 3235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.94, #queue-req: 0, 
[2025-07-28 01:06:28 DP0 TP0] Decode batch. #running-req: 2, #token: 3315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.80, #queue-req: 0, 
[2025-07-28 01:06:29 DP0 TP0] Decode batch. #running-req: 2, #token: 3395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.95, #queue-req: 0, 
[2025-07-28 01:06:29 DP0 TP0] Decode batch. #running-req: 2, #token: 3475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.36, #queue-req: 0, 
[2025-07-28 01:06:29 DP0 TP0] Decode batch. #running-req: 2, #token: 3555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.09, #queue-req: 0, 
[2025-07-28 01:06:29 DP0 TP0] Decode batch. #running-req: 2, #token: 3635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.87, #queue-req: 0, 
[2025-07-28 01:06:29 DP0 TP0] Decode batch. #running-req: 2, #token: 3715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.70, #queue-req: 0, 
[2025-07-28 01:06:29 DP0 TP0] Decode batch. #running-req: 2, #token: 3795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.66, #queue-req: 0, 
[2025-07-28 01:06:30 DP0 TP0] Decode batch. #running-req: 2, #token: 3875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.52, #queue-req: 0, 
[2025-07-28 01:06:30 DP0 TP0] Decode batch. #running-req: 2, #token: 3955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.63, #queue-req: 0, 
[2025-07-28 01:06:30 DP0 TP0] Decode batch. #running-req: 2, #token: 4035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.61, #queue-req: 0, 
[2025-07-28 01:06:30 DP0 TP0] Decode batch. #running-req: 2, #token: 4115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.43, #queue-req: 0, 
[2025-07-28 01:06:30 DP0 TP0] Decode batch. #running-req: 2, #token: 4195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.49, #queue-req: 0, 
[2025-07-28 01:06:30 DP0 TP0] Decode batch. #running-req: 2, #token: 4275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.28, #queue-req: 0, 
[2025-07-28 01:06:31] INFO:     127.0.0.1:44446 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:06:31 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 92, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:06:31 DP1 TP0] Decode batch. #running-req: 1, #token: 242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 6.75, #queue-req: 0, 
[2025-07-28 01:06:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2893, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.03, #queue-req: 0, 
[2025-07-28 01:06:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2933, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.25, #queue-req: 0, 
[2025-07-28 01:06:31 DP1 TP0] Decode batch. #running-req: 1, #token: 282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 216.56, #queue-req: 0, 
[2025-07-28 01:06:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2973, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.63, #queue-req: 0, 
[2025-07-28 01:06:31 DP1 TP0] Decode batch. #running-req: 1, #token: 322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 225.40, #queue-req: 0, 
[2025-07-28 01:06:31 DP0 TP0] Decode batch. #running-req: 1, #token: 3013, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.51, #queue-req: 0, 
[2025-07-28 01:06:31 DP1 TP0] Decode batch. #running-req: 1, #token: 362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.36, #queue-req: 0, 
[2025-07-28 01:06:31 DP0 TP0] Decode batch. #running-req: 1, #token: 3053, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.52, #queue-req: 0, 
[2025-07-28 01:06:31 DP1 TP0] Decode batch. #running-req: 1, #token: 402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.50, #queue-req: 0, 
[2025-07-28 01:06:31 DP0 TP0] Decode batch. #running-req: 1, #token: 3093, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.29, #queue-req: 0, 
[2025-07-28 01:06:32 DP1 TP0] Decode batch. #running-req: 1, #token: 442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.22, #queue-req: 0, 
[2025-07-28 01:06:32 DP0 TP0] Decode batch. #running-req: 1, #token: 3133, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.96, #queue-req: 0, 
[2025-07-28 01:06:32 DP1 TP0] Decode batch. #running-req: 1, #token: 482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.56, #queue-req: 0, 
[2025-07-28 01:06:32 DP0 TP0] Decode batch. #running-req: 1, #token: 3173, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.11, #queue-req: 0, 
[2025-07-28 01:06:32 DP1 TP0] Decode batch. #running-req: 1, #token: 522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.67, #queue-req: 0, 
[2025-07-28 01:06:32 DP0 TP0] Decode batch. #running-req: 1, #token: 3213, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.04, #queue-req: 0, 
[2025-07-28 01:06:32 DP1 TP0] Decode batch. #running-req: 1, #token: 562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.49, #queue-req: 0, 
[2025-07-28 01:06:32 DP0 TP0] Decode batch. #running-req: 1, #token: 3253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.31, #queue-req: 0, 
[2025-07-28 01:06:32 DP1 TP0] Decode batch. #running-req: 1, #token: 602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.48, #queue-req: 0, 
[2025-07-28 01:06:32 DP0 TP0] Decode batch. #running-req: 1, #token: 3293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.28, #queue-req: 0, 
[2025-07-28 01:06:32 DP1 TP0] Decode batch. #running-req: 1, #token: 642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.94, #queue-req: 0, 
[2025-07-28 01:06:32 DP0 TP0] Decode batch. #running-req: 1, #token: 3333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.07, #queue-req: 0, 
[2025-07-28 01:06:32 DP1 TP0] Decode batch. #running-req: 1, #token: 682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.91, #queue-req: 0, 
[2025-07-28 01:06:33 DP0 TP0] Decode batch. #running-req: 1, #token: 3373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 228.24, #queue-req: 0, 
[2025-07-28 01:06:33 DP1 TP0] Decode batch. #running-req: 1, #token: 722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.22, #queue-req: 0, 
[2025-07-28 01:06:33 DP1 TP0] Decode batch. #running-req: 1, #token: 762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.11, #queue-req: 0, 
[2025-07-28 01:06:33 DP0 TP0] Decode batch. #running-req: 1, #token: 3413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.56, #queue-req: 0, 
[2025-07-28 01:06:33 DP1 TP0] Decode batch. #running-req: 1, #token: 802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.68, #queue-req: 0, 
[2025-07-28 01:06:33 DP0 TP0] Decode batch. #running-req: 1, #token: 3453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.17, #queue-req: 0, 
[2025-07-28 01:06:33 DP1 TP0] Decode batch. #running-req: 1, #token: 842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.33, #queue-req: 0, 
[2025-07-28 01:06:33 DP0 TP0] Decode batch. #running-req: 1, #token: 3493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.42, #queue-req: 0, 
[2025-07-28 01:06:33 DP1 TP0] Decode batch. #running-req: 1, #token: 882, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.53, #queue-req: 0, 
[2025-07-28 01:06:33 DP0 TP0] Decode batch. #running-req: 1, #token: 3533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.42, #queue-req: 0, 
[2025-07-28 01:06:33] INFO:     127.0.0.1:38150 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:06:33 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 103, #cached-token: 91, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:06:33 DP1 TP0] Decode batch. #running-req: 1, #token: 922, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.76, #queue-req: 0, 
[2025-07-28 01:06:34 DP0 TP0] Decode batch. #running-req: 1, #token: 220, token usage: 0.00, cuda graph: True, gen throughput (token/s): 188.40, #queue-req: 0, 
[2025-07-28 01:06:34 DP1 TP0] Decode batch. #running-req: 1, #token: 962, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.89, #queue-req: 0, 
[2025-07-28 01:06:34 DP0 TP0] Decode batch. #running-req: 1, #token: 260, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.56, #queue-req: 0, 
[2025-07-28 01:06:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1002, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.89, #queue-req: 0, 
[2025-07-28 01:06:34 DP0 TP0] Decode batch. #running-req: 1, #token: 300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.44, #queue-req: 0, 
[2025-07-28 01:06:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1042, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.30, #queue-req: 0, 
[2025-07-28 01:06:34 DP0 TP0] Decode batch. #running-req: 1, #token: 340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.40, #queue-req: 0, 
[2025-07-28 01:06:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1082, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.64, #queue-req: 0, 
[2025-07-28 01:06:34 DP0 TP0] Decode batch. #running-req: 1, #token: 380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.25, #queue-req: 0, 
[2025-07-28 01:06:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1122, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.53, #queue-req: 0, 
[2025-07-28 01:06:34 DP0 TP0] Decode batch. #running-req: 1, #token: 420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.46, #queue-req: 0, 
[2025-07-28 01:06:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1162, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.83, #queue-req: 0, 
[2025-07-28 01:06:35 DP0 TP0] Decode batch. #running-req: 1, #token: 460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.39, #queue-req: 0, 
[2025-07-28 01:06:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1202, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.04, #queue-req: 0, 
[2025-07-28 01:06:35 DP0 TP0] Decode batch. #running-req: 1, #token: 500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.55, #queue-req: 0, 
[2025-07-28 01:06:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.08, #queue-req: 0, 
[2025-07-28 01:06:35 DP0 TP0] Decode batch. #running-req: 1, #token: 540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.93, #queue-req: 0, 
[2025-07-28 01:06:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.01, #queue-req: 0, 
[2025-07-28 01:06:35 DP0 TP0] Decode batch. #running-req: 1, #token: 580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.76, #queue-req: 0, 
[2025-07-28 01:06:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.52, #queue-req: 0, 
[2025-07-28 01:06:35 DP0 TP0] Decode batch. #running-req: 1, #token: 620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.94, #queue-req: 0, 
[2025-07-28 01:06:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.71, #queue-req: 0, 
[2025-07-28 01:06:35 DP0 TP0] Decode batch. #running-req: 1, #token: 660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.92, #queue-req: 0, 
[2025-07-28 01:06:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.55, #queue-req: 0, 
[2025-07-28 01:06:35 DP0 TP0] Decode batch. #running-req: 1, #token: 700, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.19, #queue-req: 0, 
[2025-07-28 01:06:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.76, #queue-req: 0, 
[2025-07-28 01:06:36 DP0 TP0] Decode batch. #running-req: 1, #token: 740, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.27, #queue-req: 0, 
[2025-07-28 01:06:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.95, #queue-req: 0, 
[2025-07-28 01:06:36 DP0 TP0] Decode batch. #running-req: 1, #token: 780, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.04, #queue-req: 0, 
[2025-07-28 01:06:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.01, #queue-req: 0, 
[2025-07-28 01:06:36 DP0 TP0] Decode batch. #running-req: 1, #token: 820, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.61, #queue-req: 0, 
[2025-07-28 01:06:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.80, #queue-req: 0, 
[2025-07-28 01:06:36 DP0 TP0] Decode batch. #running-req: 1, #token: 860, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.63, #queue-req: 0, 
[2025-07-28 01:06:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.30, #queue-req: 0, 
[2025-07-28 01:06:36 DP0 TP0] Decode batch. #running-req: 1, #token: 900, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.57, #queue-req: 0, 
[2025-07-28 01:06:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.43, #queue-req: 0, 
[2025-07-28 01:06:36 DP0 TP0] Decode batch. #running-req: 1, #token: 940, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.06, #queue-req: 0, 
[2025-07-28 01:06:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.66, #queue-req: 0, 
[2025-07-28 01:06:37 DP0 TP0] Decode batch. #running-req: 1, #token: 980, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.09, #queue-req: 0, 
[2025-07-28 01:06:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.81, #queue-req: 0, 
[2025-07-28 01:06:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1020, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.11, #queue-req: 0, 
[2025-07-28 01:06:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.54, #queue-req: 0, 
[2025-07-28 01:06:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1060, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.98, #queue-req: 0, 
[2025-07-28 01:06:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.48, #queue-req: 0, 
[2025-07-28 01:06:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1100, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.81, #queue-req: 0, 
[2025-07-28 01:06:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.33, #queue-req: 0, 
[2025-07-28 01:06:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1140, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.00, #queue-req: 0, 
[2025-07-28 01:06:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1882, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.25, #queue-req: 0, 
[2025-07-28 01:06:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1180, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.34, #queue-req: 0, 
[2025-07-28 01:06:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1922, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.19, #queue-req: 0, 
[2025-07-28 01:06:38 DP0 TP0] Decode batch. #running-req: 1, #token: 1220, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.38, #queue-req: 0, 
[2025-07-28 01:06:38] INFO:     127.0.0.1:42572 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:06:38 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 89, #cached-token: 91, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:06:38 DP1 TP0] Decode batch. #running-req: 2, #token: 2061, token usage: 0.00, cuda graph: True, gen throughput (token/s): 213.12, #queue-req: 0, 
[2025-07-28 01:06:38 DP1 TP0] Decode batch. #running-req: 2, #token: 2141, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.60, #queue-req: 0, 
[2025-07-28 01:06:38 DP1 TP0] Decode batch. #running-req: 2, #token: 2221, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.34, #queue-req: 0, 
[2025-07-28 01:06:38 DP1 TP0] Decode batch. #running-req: 2, #token: 2301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.29, #queue-req: 0, 
[2025-07-28 01:06:38 DP1 TP0] Decode batch. #running-req: 2, #token: 2381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.28, #queue-req: 0, 
[2025-07-28 01:06:39 DP1 TP0] Decode batch. #running-req: 2, #token: 2461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.64, #queue-req: 0, 
[2025-07-28 01:06:39 DP1 TP0] Decode batch. #running-req: 2, #token: 2541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.45, #queue-req: 0, 
[2025-07-28 01:06:39 DP1 TP0] Decode batch. #running-req: 2, #token: 2621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.41, #queue-req: 0, 
[2025-07-28 01:06:39 DP1 TP0] Decode batch. #running-req: 2, #token: 2701, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.35, #queue-req: 0, 
[2025-07-28 01:06:39 DP1 TP0] Decode batch. #running-req: 2, #token: 2781, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.37, #queue-req: 0, 
[2025-07-28 01:06:40 DP1 TP0] Decode batch. #running-req: 2, #token: 2861, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.58, #queue-req: 0, 
[2025-07-28 01:06:40 DP1 TP0] Decode batch. #running-req: 2, #token: 2941, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.84, #queue-req: 0, 
[2025-07-28 01:06:40 DP1 TP0] Decode batch. #running-req: 2, #token: 3021, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.68, #queue-req: 0, 
[2025-07-28 01:06:40 DP1 TP0] Decode batch. #running-req: 2, #token: 3101, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.31, #queue-req: 0, 
[2025-07-28 01:06:40 DP1 TP0] Decode batch. #running-req: 2, #token: 3181, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.78, #queue-req: 0, 
[2025-07-28 01:06:40 DP1 TP0] Decode batch. #running-req: 2, #token: 3261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.99, #queue-req: 0, 
[2025-07-28 01:06:41 DP1 TP0] Decode batch. #running-req: 2, #token: 3341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.34, #queue-req: 0, 
[2025-07-28 01:06:41 DP1 TP0] Decode batch. #running-req: 2, #token: 3421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.04, #queue-req: 0, 
[2025-07-28 01:06:41] INFO:     127.0.0.1:42576 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:06:41 DP1 TP0] Decode batch. #running-req: 1, #token: 2682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 377.90, #queue-req: 0, 
[2025-07-28 01:06:41 DP1 TP0] Decode batch. #running-req: 1, #token: 2722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.01, #queue-req: 0, 
[2025-07-28 01:06:41 DP1 TP0] Decode batch. #running-req: 1, #token: 2762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.00, #queue-req: 0, 
[2025-07-28 01:06:41 DP1 TP0] Decode batch. #running-req: 1, #token: 2802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.99, #queue-req: 0, 
[2025-07-28 01:06:42 DP1 TP0] Decode batch. #running-req: 1, #token: 2842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.71, #queue-req: 0, 
[2025-07-28 01:06:42] INFO:     127.0.0.1:44450 - "POST /v1/chat/completions HTTP/1.1" 200 OK

nohup: ignoring input
端口 8001 已被占用,尝试下一个端口
Finished /data/home/<USER>/repos/anesbench/share/models/gemma-3-1b-it
端口 8001 已被占用,尝试下一个端口
Finished /data/home/<USER>/repos/anesbench/share/models/gemma-3-4b-it
端口 8001 已被占用,尝试下一个端口
端口 8002 已被占用,尝试下一个端口
Finished /data/home/<USER>/repos/anesbench/share/models/gemma-3-12b-it
端口 8001 已被占用,尝试下一个端口
端口 8002 已被占用,尝试下一个端口
端口 8003 已被占用,尝试下一个端口
Finished /data/home/<USER>/repos/anesbench/share/models/gemma-3-27b-it
端口 8001 已被占用,尝试下一个端口
Failed to start server for /data/home/<USER>/repos/anesbench/share/models/Qwen2.5-0.5B-Instruct
端口 8001 已被占用,尝试下一个端口
Finished /data/home/<USER>/repos/anesbench/share/models/Qwen2.5-1.5B-Instruct
端口 8001 已被占用,尝试下一个端口
端口 8002 已被占用,尝试下一个端口
Finished /data/home/<USER>/repos/anesbench/share/models/Qwen2.5-3B-Instruct
端口 8001 已被占用,尝试下一个端口
端口 8002 已被占用,尝试下一个端口
端口 8003 已被占用,尝试下一个端口
Finished /data/home/<USER>/repos/anesbench/share/models/Qwen2.5-7B-Instruct
端口 8001 已被占用,尝试下一个端口
端口 8002 已被占用,尝试下一个端口
Finished /data/home/<USER>/repos/anesbench/share/models/Qwen2.5-14B-Instruct
端口 8001 已被占用,尝试下一个端口
端口 8002 已被占用,尝试下一个端口
端口 8003 已被占用,尝试下一个端口
Finished /data/home/<USER>/repos/anesbench/share/models/Qwen2.5-32B-Instruct
端口 8001 已被占用,尝试下一个端口
端口 8002 已被占用,尝试下一个端口
端口 8003 已被占用,尝试下一个端口
端口 8004 已被占用,尝试下一个端口
Finished /data/home/<USER>/repos/anesbench/share/models/Qwen2.5-72B-Instruct
端口 8001 已被占用,尝试下一个端口
端口 8002 已被占用,尝试下一个端口
Finished /data/home/<USER>/repos/anesbench/share/models/Qwen3-0.6B
端口 8001 已被占用,尝试下一个端口
端口 8002 已被占用,尝试下一个端口
端口 8003 已被占用,尝试下一个端口
Finished /data/home/<USER>/repos/anesbench/share/models/Qwen3-1.7B
端口 8001 已被占用,尝试下一个端口
端口 8002 已被占用,尝试下一个端口
Finished /data/home/<USER>/repos/anesbench/share/models/Qwen3-4B
端口 8001 已被占用,尝试下一个端口
端口 8002 已被占用,尝试下一个端口
端口 8003 已被占用,尝试下一个端口
端口 8004 已被占用,尝试下一个端口
端口 8005 已被占用,尝试下一个端口
Finished /data/home/<USER>/repos/anesbench/share/models/Qwen3-8B
端口 8001 已被占用,尝试下一个端口
端口 8002 已被占用,尝试下一个端口
端口 8003 已被占用,尝试下一个端口
端口 8004 已被占用,尝试下一个端口
端口 8005 已被占用,尝试下一个端口
端口 8006 已被占用,尝试下一个端口
Finished /data/home/<USER>/repos/anesbench/share/models/Qwen3-14B
端口 8001 已被占用,尝试下一个端口
端口 8002 已被占用,尝试下一个端口
Finished /data/home/<USER>/repos/anesbench/share/models/Qwen3-30B-A3B
端口 8001 已被占用,尝试下一个端口
端口 8002 已被占用,尝试下一个端口
端口 8003 已被占用,尝试下一个端口
端口 8004 已被占用,尝试下一个端口
端口 8005 已被占用,尝试下一个端口
Finished /data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Llama-8B
端口 8001 已被占用,尝试下一个端口
端口 8002 已被占用,尝试下一个端口
Failed to start server for /data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Llama-70B
端口 8001 已被占用,尝试下一个端口
端口 8002 已被占用,尝试下一个端口
Finished /data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Qwen-1.5B
端口 8001 已被占用,尝试下一个端口
端口 8002 已被占用,尝试下一个端口
Finished /data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Qwen-7B
端口 8001 已被占用,尝试下一个端口
端口 8002 已被占用,尝试下一个端口
端口 8003 已被占用,尝试下一个端口
端口 8004 已被占用,尝试下一个端口
端口 8005 已被占用,尝试下一个端口
端口 8006 已被占用,尝试下一个端口
端口 8007 已被占用,尝试下一个端口
Finished /data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Qwen-14B
端口 8001 已被占用,尝试下一个端口
端口 8002 已被占用,尝试下一个端口
端口 8003 已被占用,尝试下一个端口
端口 8004 已被占用,尝试下一个端口
端口 8005 已被占用,尝试下一个端口
端口 8006 已被占用,尝试下一个端口
端口 8007 已被占用,尝试下一个端口
端口 8008 已被占用,尝试下一个端口
Traceback (most recent call last):
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/urllib3/connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/urllib3/connection.py", line 565, in getresponse
    httplib_response = super().getresponse()
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/http/client.py", line 1368, in getresponse
    response.begin()
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/http/client.py", line 317, in begin
    version, status, reason = self._read_status()
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/http/client.py", line 278, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
TimeoutError: timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/urllib3/connectionpool.py", line 841, in urlopen
    retries = retries.increment(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/urllib3/util/retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/urllib3/util/util.py", line 39, in reraise
    raise value
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/urllib3/connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/urllib3/connectionpool.py", line 536, in _make_request
    self._raise_timeout(err=e, url=url, timeout_value=read_timeout)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/urllib3/connectionpool.py", line 367, in _raise_timeout
    raise ReadTimeoutError(
urllib3.exceptions.ReadTimeoutError: HTTPConnectionPool(host='127.0.0.1', port=8009): Read timed out. (read timeout=120)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/data/share/jingzhang/anesthesia/datasets/anesthesia/benchmark/fine/rebuttal/openqa/open-source/sglang_infer.py", line 100, in sglang_server
    yield base_url
  File "/data/share/jingzhang/anesthesia/datasets/anesthesia/benchmark/fine/rebuttal/openqa/open-source/sglang_infer.py", line 170, in <module>
    model_res.append(future.result())
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/concurrent/futures/_base.py", line 438, in result
    return self.__get_result()
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/concurrent/futures/_base.py", line 390, in __get_result
    raise self._exception
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/concurrent/futures/thread.py", line 52, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/data/share/jingzhang/anesthesia/datasets/anesthesia/benchmark/fine/rebuttal/openqa/open-source/sglang_infer.py", line 163, in fetch_response
    r = requests.post(f"{endpoint}/v1/chat/completions", json=payload, timeout=120)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/requests/api.py", line 115, in post
    return request("post", url, data=data, json=json, **kwargs)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/requests/api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/site-packages/requests/adapters.py", line 713, in send
    raise ReadTimeout(e, request=request)
requests.exceptions.ReadTimeout: HTTPConnectionPool(host='127.0.0.1', port=8009): Read timed out. (read timeout=120)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/data/share/jingzhang/anesthesia/datasets/anesthesia/benchmark/fine/rebuttal/openqa/open-source/sglang_infer.py", line 147, in <module>
    with sglang_server(model, log_file=f"./logs/server_{model.split('/')[-1]}.log") as endpoint:
  File "/data/home/<USER>/toolkits/miniconda/envs/sglang_server/lib/python3.10/contextlib.py", line 185, in __exit__
    raise RuntimeError("generator didn't stop after throw()")
RuntimeError: generator didn't stop after throw()

#!/usr/bin/env python3
"""
Script to format evaluation data for OpenAI Batch API submission.
Processes merged_output_final.jsonl and creates batch API requests.
"""

import json
import sys

def load_evaluation_prompt():
    """Load the evaluation prompt from file."""
    try:
        with open('evaluation_prompt.txt', 'r') as f:
            return f.read().strip()
    except FileNotFoundError:
        print("Error: evaluation_prompt.txt not found")
        sys.exit(1)

def extract_model_answer(response_text):
    """Extract the final answer from the model response."""
    # Look for <answer> tags first
    if '<answer>' in response_text and '</answer>' in response_text:
        start = response_text.find('<answer>') + len('<answer>')
        end = response_text.find('</answer>')
        return response_text[start:end].strip()
    
    # If no answer tags, try to find the last substantial line
    lines = response_text.strip().split('\n')
    for line in reversed(lines):
        line = line.strip()
        if line and not line.startswith('<') and len(line) > 10:
            return line
    
    # Fallback: return last 200 characters
    return response_text[-200:].strip()

def create_evaluation_message(prompt, target, response, evaluation_prompt):
    """Create the evaluation message for the API."""
    # model_answer = extract_model_answer(response)
    model_answer = response
    
    evaluation_input = f"""Question: {prompt}

Target Answer: {target}

Model Response: {model_answer}"""
    
    return [
        {
            "role": "system",
            "content": evaluation_prompt
        },
        {
            "role": "user", 
            "content": evaluation_input
        }
    ]

def process_jsonl_to_batch_format(input_file, output_file):
    """Process JSONL file and create OpenAI Batch API format."""
    evaluation_prompt = load_evaluation_prompt()
    
    batch_requests = []
    
    print(f"Processing {input_file}...")
    
    with open(input_file, 'r') as f:
        for line_num, line in enumerate(f, 1):
            try:
                data = json.loads(line.strip())
                
                # Extract required fields
                prompt = data.get('prompt', '')
                target = data.get('target', '')
                response = data.get('response', '')
                record_id = data.get('id', f'unknown_{line_num}')
                
                # Create evaluation messages
                messages = create_evaluation_message(prompt, target, response, evaluation_prompt)
                
                # Create batch request
                batch_request = {
                    "custom_id": f"eval-{record_id}",
                    "method": "POST",
                    "url": "/v1/chat/completions",
                    "body": {
                        "model": "gpt-4.1-2025-04-14",
                        "messages": messages,
                        "max_tokens": 2048,
                        "temperature": 0.1,
                        "n": 3
                    }
                }
                
                batch_requests.append(batch_request)
                
                if line_num % 100 == 0:
                    print(f"Processed {line_num} records...")
                    
            except json.JSONDecodeError as e:
                print(f"Error parsing line {line_num}: {e}")
                continue
            except Exception as e:
                print(f"Error processing line {line_num}: {e}")
                continue
    
    # Write batch requests to output file
    print(f"Writing {len(batch_requests)} batch requests to {output_file}...")
    
    with open(output_file, 'w') as f:
        for request in batch_requests:
            f.write(json.dumps(request) + '\n')
    
    print(f"Successfully created {len(batch_requests)} batch requests")
    print(f"Output saved to: {output_file}")
    
    # Print sample request for verification
    if batch_requests:
        print("\nSample batch request:")
        print(json.dumps(batch_requests[0], indent=2))

if __name__ == "__main__":
    input_file = "merged_output_final.jsonl"
    output_file = "batch_evaluation_requests.jsonl"
    
    process_jsonl_to_batch_format(input_file, output_file)

[2025-07-28 01:45:30] server_args=ServerArgs(model_path='/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Qwen-14B', tokenizer_path='/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Qwen-14B', tokenizer_mode='auto', skip_tokenizer_init=False, load_format='auto', model_loader_extra_config='{}', trust_remote_code=False, context_length=32768, is_embedding=False, enable_multimodal=None, revision=None, model_impl='auto', host='127.0.0.1', port=8008, skip_server_warmup=False, warmups=None, nccl_port=None, dtype='auto', quantization=None, quantization_param_path=None, kv_cache_dtype='auto', mem_fraction_static=0.85, max_running_requests=None, max_total_tokens=None, chunked_prefill_size=8192, max_prefill_tokens=16384, schedule_policy='fcfs', schedule_conservativeness=1.0, cpu_offload_gb=0, page_size=1, hybrid_kvcache_ratio=None, swa_full_tokens_ratio=0.8, disable_hybrid_swa_memory=False, device='cuda', tp_size=4, pp_size=1, max_micro_batch_size=None, stream_interval=1, stream_output=False, random_seed=171533514, constrained_json_whitespace_pattern=None, watchdog_timeout=300, dist_timeout=None, download_dir=None, base_gpu_id=0, gpu_id_step=1, sleep_on_idle=False, log_level='info', log_level_http=None, log_requests=False, log_requests_level=0, crash_dump_folder=None, show_time_cost=True, enable_metrics=True, enable_metrics_for_all_schedulers=False, bucket_time_to_first_token=None, bucket_inter_token_latency=None, bucket_e2e_request_latency=None, collect_tokens_histogram=False, decode_log_interval=40, enable_request_time_stats_logging=False, kv_events_config=None, api_key=None, served_model_name='/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Qwen-14B', chat_template=None, completion_template=None, file_storage_path='sglang_storage', enable_cache_report=False, reasoning_parser=None, tool_call_parser=None, dp_size=2, load_balance_method='round_robin', dist_init_addr=None, nnodes=1, node_rank=0, json_model_override_args='{}', preferred_sampling_params=None, enable_lora=None, max_lora_rank=None, lora_target_modules=None, lora_paths=None, max_loras_per_batch=8, lora_backend='triton', attention_backend=None, sampling_backend='flashinfer', grammar_backend='xgrammar', mm_attention_backend=None, speculative_algorithm=None, speculative_draft_model_path=None, speculative_num_steps=None, speculative_eagle_topk=None, speculative_num_draft_tokens=None, speculative_accept_threshold_single=1.0, speculative_accept_threshold_acc=1.0, speculative_token_map=None, ep_size=1, enable_ep_moe=False, enable_deepep_moe=False, enable_flashinfer_moe=False, enable_flashinfer_allreduce_fusion=False, deepep_mode='auto', ep_num_redundant_experts=0, ep_dispatch_algorithm='static', init_expert_location='trivial', enable_eplb=False, eplb_algorithm='auto', eplb_rebalance_num_iterations=1000, eplb_rebalance_layers_per_chunk=None, expert_distribution_recorder_mode=None, expert_distribution_recorder_buffer_size=1000, enable_expert_distribution_metrics=False, deepep_config=None, moe_dense_tp_size=None, enable_hierarchical_cache=False, hicache_ratio=2.0, hicache_size=0, hicache_write_policy='write_through_selective', hicache_io_backend='', hicache_storage_backend=None, enable_double_sparsity=False, ds_channel_config_path=None, ds_heavy_channel_num=32, ds_heavy_token_num=256, ds_heavy_channel_type='qk', ds_sparse_decode_threshold=4096, disable_radix_cache=False, cuda_graph_max_bs=None, cuda_graph_bs=None, disable_cuda_graph=False, disable_cuda_graph_padding=False, enable_profile_cuda_graph=False, enable_nccl_nvls=False, enable_tokenizer_batch_encode=False, disable_outlines_disk_cache=False, disable_custom_all_reduce=False, enable_mscclpp=False, disable_overlap_schedule=False, enable_mixed_chunk=False, enable_dp_attention=False, enable_dp_lm_head=False, enable_two_batch_overlap=False, enable_torch_compile=False, torch_compile_max_bs=32, torchao_config='', enable_nan_detection=False, enable_p2p_check=False, triton_attention_reduce_in_fp32=False, triton_attention_num_kv_splits=8, num_continuous_decode_steps=1, delete_ckpt_after_loading=False, enable_memory_saver=False, allow_auto_truncate=False, enable_custom_logit_processor=False, flashinfer_mla_disable_ragged=False, disable_shared_experts_fusion=False, disable_chunked_prefix_cache=False, disable_fast_image_processor=False, enable_return_hidden_states=False, enable_triton_kernel_moe=False, debug_tensor_dump_output_folder=None, debug_tensor_dump_input_file=None, debug_tensor_dump_inject=False, debug_tensor_dump_prefill_only=False, disaggregation_mode='null', disaggregation_transfer_backend='mooncake', disaggregation_bootstrap_port=8998, disaggregation_decode_tp=None, disaggregation_decode_dp=None, disaggregation_prefill_pp=1, disaggregation_ib_device=None, num_reserved_decode_tokens=512, pdlb_url=None, custom_weight_loader=[], weight_loader_disable_mmap=False, enable_pdmux=False, sm_group_num=3)
[2025-07-28 01:45:36] Launch DP0 starting at GPU #0.
[2025-07-28 01:45:36] Launch DP1 starting at GPU #4.
[2025-07-28 01:45:43 DP0 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 01:45:43 DP0 TP0] Init torch distributed begin.
[2025-07-28 01:45:44 DP1 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 01:45:44 DP1 TP0] Init torch distributed begin.
[2025-07-28 01:45:45 DP0 TP0] sglang is using nccl==2.26.2
[2025-07-28 01:45:45 DP1 TP0] sglang is using nccl==2.26.2
[2025-07-28 01:45:46 DP0 TP0] Init torch distributed ends. mem usage=0.65 GB
[2025-07-28 01:45:46 DP1 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 01:45:47 DP0 TP0] Load weight begin. avail mem=78.21 GB
[2025-07-28 01:45:47 DP1 TP0] Load weight begin. avail mem=78.21 GB

Loading safetensors checkpoint shards:   0% Completed | 0/4 [00:00<?, ?it/s]

Loading safetensors checkpoint shards:   0% Completed | 0/4 [00:00<?, ?it/s]

Loading safetensors checkpoint shards:  25% Completed | 1/4 [00:01<00:03,  1.11s/it]

Loading safetensors checkpoint shards:  25% Completed | 1/4 [00:01<00:03,  1.06s/it]

Loading safetensors checkpoint shards:  50% Completed | 2/4 [00:04<00:04,  2.42s/it]

Loading safetensors checkpoint shards:  50% Completed | 2/4 [00:04<00:04,  2.44s/it]

Loading safetensors checkpoint shards:  75% Completed | 3/4 [00:07<00:02,  2.74s/it]

Loading safetensors checkpoint shards:  75% Completed | 3/4 [00:07<00:02,  2.73s/it]

Loading safetensors checkpoint shards: 100% Completed | 4/4 [00:11<00:00,  3.07s/it]

Loading safetensors checkpoint shards: 100% Completed | 4/4 [00:11<00:00,  3.07s/it]

Loading safetensors checkpoint shards: 100% Completed | 4/4 [00:11<00:00,  2.79s/it]


Loading safetensors checkpoint shards: 100% Completed | 4/4 [00:11<00:00,  2.78s/it]

[2025-07-28 01:45:59 DP1 TP0] Load weight end. type=Qwen2ForCausalLM, dtype=torch.bfloat16, avail mem=71.14 GB, mem usage=7.07 GB.
[2025-07-28 01:45:59 DP0 TP0] Load weight end. type=Qwen2ForCausalLM, dtype=torch.bfloat16, avail mem=71.14 GB, mem usage=7.07 GB.
[2025-07-28 01:45:59 DP0 TP0] KV Cache is allocated. #tokens: 1295971, K size: 29.66 GB, V size: 29.66 GB
[2025-07-28 01:45:59 DP0 TP0] Memory pool end. avail mem=11.20 GB
[2025-07-28 01:45:59 DP0 TP3] KV Cache is allocated. #tokens: 1295971, K size: 29.66 GB, V size: 29.66 GB
[2025-07-28 01:45:59 DP1 TP1] KV Cache is allocated. #tokens: 1295971, K size: 29.66 GB, V size: 29.66 GB
[2025-07-28 01:45:59 DP0 TP2] KV Cache is allocated. #tokens: 1295971, K size: 29.66 GB, V size: 29.66 GB
[2025-07-28 01:45:59 DP1 TP2] KV Cache is allocated. #tokens: 1295971, K size: 29.66 GB, V size: 29.66 GB
[2025-07-28 01:45:59 DP1 TP3] KV Cache is allocated. #tokens: 1295971, K size: 29.66 GB, V size: 29.66 GB
[2025-07-28 01:45:59 DP1 TP0] KV Cache is allocated. #tokens: 1295971, K size: 29.66 GB, V size: 29.66 GB
[2025-07-28 01:45:59 DP1 TP0] Memory pool end. avail mem=11.20 GB
[2025-07-28 01:45:59 DP0 TP1] KV Cache is allocated. #tokens: 1295971, K size: 29.66 GB, V size: 29.66 GB
[2025-07-28 01:45:59 DP0 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.56 GB
[2025-07-28 01:45:59 DP1 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.56 GB
[2025-07-28 01:45:59 DP1 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.54 GB):   0%|          | 0/23 [00:00<?, ?it/s][2025-07-28 01:45:59 DP0 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.54 GB):   0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.54 GB):   4%|▍         | 1/23 [00:01<00:23,  1.06s/it]
Capturing batches (bs=152 avail_mem=10.28 GB):   4%|▍         | 1/23 [00:01<00:23,  1.06s/it]
Capturing batches (bs=160 avail_mem=10.54 GB):   4%|▍         | 1/23 [00:01<00:24,  1.11s/it]
Capturing batches (bs=152 avail_mem=10.28 GB):   4%|▍         | 1/23 [00:01<00:24,  1.11s/it]
Capturing batches (bs=152 avail_mem=10.28 GB):   9%|▊         | 2/23 [00:01<00:14,  1.49it/s]
Capturing batches (bs=144 avail_mem=10.16 GB):   9%|▊         | 2/23 [00:01<00:14,  1.49it/s]
Capturing batches (bs=152 avail_mem=10.28 GB):   9%|▊         | 2/23 [00:01<00:14,  1.50it/s]
Capturing batches (bs=144 avail_mem=10.16 GB):   9%|▊         | 2/23 [00:01<00:14,  1.50it/s]
Capturing batches (bs=144 avail_mem=10.16 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.69it/s]
Capturing batches (bs=136 avail_mem=10.07 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.69it/s]
Capturing batches (bs=144 avail_mem=10.16 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.68it/s]
Capturing batches (bs=136 avail_mem=10.07 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.68it/s]
Capturing batches (bs=136 avail_mem=10.07 GB):  17%|█▋        | 4/23 [00:02<00:09,  2.10it/s]
Capturing batches (bs=128 avail_mem=9.96 GB):  17%|█▋        | 4/23 [00:02<00:09,  2.10it/s] 
Capturing batches (bs=136 avail_mem=10.07 GB):  17%|█▋        | 4/23 [00:02<00:09,  2.08it/s]
Capturing batches (bs=128 avail_mem=9.96 GB):  17%|█▋        | 4/23 [00:02<00:09,  2.08it/s] 
Capturing batches (bs=128 avail_mem=9.96 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.41it/s]
Capturing batches (bs=120 avail_mem=9.87 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.41it/s]
Capturing batches (bs=128 avail_mem=9.96 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.39it/s]
Capturing batches (bs=120 avail_mem=9.87 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.39it/s]
Capturing batches (bs=120 avail_mem=9.87 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.66it/s]
Capturing batches (bs=112 avail_mem=9.77 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.66it/s]
Capturing batches (bs=120 avail_mem=9.87 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.63it/s]
Capturing batches (bs=112 avail_mem=9.77 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.63it/s]
Capturing batches (bs=112 avail_mem=9.77 GB):  30%|███       | 7/23 [00:03<00:05,  2.84it/s]
Capturing batches (bs=104 avail_mem=9.70 GB):  30%|███       | 7/23 [00:03<00:05,  2.84it/s]
Capturing batches (bs=112 avail_mem=9.77 GB):  30%|███       | 7/23 [00:03<00:05,  2.81it/s]
Capturing batches (bs=104 avail_mem=9.70 GB):  30%|███       | 7/23 [00:03<00:05,  2.81it/s]
Capturing batches (bs=104 avail_mem=9.70 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.99it/s]
Capturing batches (bs=96 avail_mem=9.61 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.99it/s] 
Capturing batches (bs=104 avail_mem=9.70 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.87it/s]
Capturing batches (bs=96 avail_mem=9.61 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.87it/s] 
Capturing batches (bs=96 avail_mem=9.61 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.08it/s]
Capturing batches (bs=88 avail_mem=9.53 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.08it/s]
Capturing batches (bs=96 avail_mem=9.61 GB):  39%|███▉      | 9/23 [00:03<00:04,  2.95it/s]
Capturing batches (bs=88 avail_mem=9.53 GB):  39%|███▉      | 9/23 [00:03<00:04,  2.95it/s]
Capturing batches (bs=88 avail_mem=9.53 GB):  43%|████▎     | 10/23 [00:04<00:04,  3.14it/s]
Capturing batches (bs=80 avail_mem=9.45 GB):  43%|████▎     | 10/23 [00:04<00:04,  3.14it/s]
Capturing batches (bs=88 avail_mem=9.53 GB):  43%|████▎     | 10/23 [00:04<00:04,  3.02it/s]
Capturing batches (bs=80 avail_mem=9.45 GB):  43%|████▎     | 10/23 [00:04<00:04,  3.02it/s]
Capturing batches (bs=80 avail_mem=9.45 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.20it/s]
Capturing batches (bs=72 avail_mem=9.44 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.20it/s]
Capturing batches (bs=80 avail_mem=9.45 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.10it/s]
Capturing batches (bs=72 avail_mem=9.44 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.10it/s]
Capturing batches (bs=72 avail_mem=9.44 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.24it/s]
Capturing batches (bs=64 avail_mem=9.37 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.24it/s]
Capturing batches (bs=72 avail_mem=9.44 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.17it/s]
Capturing batches (bs=64 avail_mem=9.37 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.17it/s]
Capturing batches (bs=64 avail_mem=9.37 GB):  57%|█████▋    | 13/23 [00:04<00:03,  3.26it/s]
Capturing batches (bs=56 avail_mem=9.32 GB):  57%|█████▋    | 13/23 [00:04<00:03,  3.26it/s]
Capturing batches (bs=64 avail_mem=9.37 GB):  57%|█████▋    | 13/23 [00:05<00:03,  3.12it/s]
Capturing batches (bs=56 avail_mem=9.32 GB):  57%|█████▋    | 13/23 [00:05<00:03,  3.12it/s]
Capturing batches (bs=56 avail_mem=9.32 GB):  61%|██████    | 14/23 [00:05<00:02,  3.31it/s]
Capturing batches (bs=48 avail_mem=9.26 GB):  61%|██████    | 14/23 [00:05<00:02,  3.31it/s]
Capturing batches (bs=56 avail_mem=9.32 GB):  61%|██████    | 14/23 [00:05<00:02,  3.11it/s]
Capturing batches (bs=48 avail_mem=9.26 GB):  61%|██████    | 14/23 [00:05<00:02,  3.11it/s]
Capturing batches (bs=48 avail_mem=9.26 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.33it/s]
Capturing batches (bs=40 avail_mem=9.25 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.33it/s]
Capturing batches (bs=48 avail_mem=9.26 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.16it/s]
Capturing batches (bs=40 avail_mem=9.25 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.16it/s]
Capturing batches (bs=40 avail_mem=9.25 GB):  70%|██████▉   | 16/23 [00:05<00:02,  3.33it/s]
Capturing batches (bs=32 avail_mem=9.20 GB):  70%|██████▉   | 16/23 [00:05<00:02,  3.33it/s]
Capturing batches (bs=40 avail_mem=9.25 GB):  70%|██████▉   | 16/23 [00:06<00:02,  3.19it/s]
Capturing batches (bs=32 avail_mem=9.20 GB):  70%|██████▉   | 16/23 [00:06<00:02,  3.19it/s]
Capturing batches (bs=32 avail_mem=9.20 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.31it/s]
Capturing batches (bs=24 avail_mem=9.19 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.31it/s]
Capturing batches (bs=32 avail_mem=9.20 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.27it/s]
Capturing batches (bs=24 avail_mem=9.19 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.27it/s]
Capturing batches (bs=24 avail_mem=9.19 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.33it/s]
Capturing batches (bs=16 avail_mem=9.15 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.33it/s]
Capturing batches (bs=24 avail_mem=9.19 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.30it/s]
Capturing batches (bs=16 avail_mem=9.15 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.30it/s]
Capturing batches (bs=16 avail_mem=9.15 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.37it/s]
Capturing batches (bs=8 avail_mem=9.15 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.37it/s] 
Capturing batches (bs=16 avail_mem=9.15 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.25it/s]
Capturing batches (bs=8 avail_mem=9.15 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.25it/s] 
Capturing batches (bs=8 avail_mem=9.15 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.39it/s]
Capturing batches (bs=4 avail_mem=9.12 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.39it/s]
Capturing batches (bs=8 avail_mem=9.15 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.27it/s]
Capturing batches (bs=4 avail_mem=9.12 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.27it/s]
Capturing batches (bs=4 avail_mem=9.12 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.33it/s]
Capturing batches (bs=2 avail_mem=9.11 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.33it/s]
Capturing batches (bs=4 avail_mem=9.12 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.30it/s]
Capturing batches (bs=2 avail_mem=9.11 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.30it/s]
Capturing batches (bs=2 avail_mem=9.11 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.37it/s]
Capturing batches (bs=1 avail_mem=9.08 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.37it/s]
Capturing batches (bs=2 avail_mem=9.11 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.33it/s]
Capturing batches (bs=1 avail_mem=9.08 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.33it/s][2025-07-28 01:46:07 DP1 TP2] Registering 2231 cuda graph addresses
[2025-07-28 01:46:07 DP1 TP3] Registering 2231 cuda graph addresses

Capturing batches (bs=1 avail_mem=9.08 GB): 100%|██████████| 23/23 [00:07<00:00,  3.35it/s]
Capturing batches (bs=1 avail_mem=9.08 GB): 100%|██████████| 23/23 [00:07<00:00,  2.89it/s]
[2025-07-28 01:46:07 DP1 TP0] Registering 2231 cuda graph addresses
[2025-07-28 01:46:07 DP1 TP1] Registering 2231 cuda graph addresses
[2025-07-28 01:46:07 DP1 TP0] Capture cuda graph end. Time elapsed: 8.17 s. mem usage=1.48 GB. avail mem=9.08 GB.

Capturing batches (bs=1 avail_mem=9.08 GB): 100%|██████████| 23/23 [00:08<00:00,  3.34it/s]
Capturing batches (bs=1 avail_mem=9.08 GB): 100%|██████████| 23/23 [00:08<00:00,  2.84it/s]
[2025-07-28 01:46:07 DP0 TP3] Registering 2231 cuda graph addresses
[2025-07-28 01:46:07 DP0 TP0] Registering 2231 cuda graph addresses
[2025-07-28 01:46:07 DP0 TP2] Registering 2231 cuda graph addresses
[2025-07-28 01:46:07 DP0 TP1] Registering 2231 cuda graph addresses
[2025-07-28 01:46:07 DP0 TP0] Capture cuda graph end. Time elapsed: 8.36 s. mem usage=1.48 GB. avail mem=9.08 GB.
[2025-07-28 01:46:08 DP1 TP0] max_total_num_tokens=1295971, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=9.08 GB
[2025-07-28 01:46:08 DP0 TP0] max_total_num_tokens=1295971, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=9.08 GB
[2025-07-28 01:46:08] INFO:     Started server process [1881035]
[2025-07-28 01:46:08] INFO:     Waiting for application startup.
[2025-07-28 01:46:08] INFO:     Application startup complete.
[2025-07-28 01:46:08] INFO:     Uvicorn running on http://127.0.0.1:8008 (Press CTRL+C to quit)
[2025-07-28 01:46:09] INFO:     127.0.0.1:50978 - "GET /get_model_info HTTP/1.1" 200 OK
[2025-07-28 01:46:10 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 7, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:46:10 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 7, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:46:10] INFO:     127.0.0.1:50986 - "POST /generate HTTP/1.1" 200 OK
[2025-07-28 01:46:10] The server is fired up and ready to roll!
[2025-07-28 01:46:10] INFO:     127.0.0.1:50996 - "GET /health HTTP/1.1" 200 OK
[2025-07-28 01:46:11 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 231, #cached-token: 1, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:46:11 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 190, #cached-token: 1, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:46:11 DP1 TP0] Decode batch. #running-req: 1, #token: 224, token usage: 0.00, cuda graph: True, gen throughput (token/s): 11.46, #queue-req: 0, 
[2025-07-28 01:46:11 DP0 TP0] Decode batch. #running-req: 1, #token: 265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 11.76, #queue-req: 0, 
[2025-07-28 01:46:11 DP1 TP0] Decode batch. #running-req: 1, #token: 264, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.48, #queue-req: 0, 
[2025-07-28 01:46:12 DP0 TP0] Decode batch. #running-req: 1, #token: 305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.34, #queue-req: 0, 
[2025-07-28 01:46:12 DP1 TP0] Decode batch. #running-req: 1, #token: 304, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 01:46:12 DP0 TP0] Decode batch. #running-req: 1, #token: 345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.38, #queue-req: 0, 
[2025-07-28 01:46:12 DP1 TP0] Decode batch. #running-req: 1, #token: 344, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.94, #queue-req: 0, 
[2025-07-28 01:46:12 DP0 TP0] Decode batch. #running-req: 1, #token: 385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 01:46:12 DP1 TP0] Decode batch. #running-req: 1, #token: 384, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.78, #queue-req: 0, 
[2025-07-28 01:46:12 DP0 TP0] Decode batch. #running-req: 1, #token: 425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.57, #queue-req: 0, 
[2025-07-28 01:46:13 DP1 TP0] Decode batch. #running-req: 1, #token: 424, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 01:46:13 DP0 TP0] Decode batch. #running-req: 1, #token: 465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.00, #queue-req: 0, 
[2025-07-28 01:46:13 DP1 TP0] Decode batch. #running-req: 1, #token: 464, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.00, #queue-req: 0, 
[2025-07-28 01:46:13 DP0 TP0] Decode batch. #running-req: 1, #token: 505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.10, #queue-req: 0, 
[2025-07-28 01:46:13 DP1 TP0] Decode batch. #running-req: 1, #token: 504, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.03, #queue-req: 0, 
[2025-07-28 01:46:13 DP0 TP0] Decode batch. #running-req: 1, #token: 545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.92, #queue-req: 0, 
[2025-07-28 01:46:14 DP1 TP0] Decode batch. #running-req: 1, #token: 544, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.72, #queue-req: 0, 
[2025-07-28 01:46:14 DP0 TP0] Decode batch. #running-req: 1, #token: 585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.01, #queue-req: 0, 
[2025-07-28 01:46:14 DP1 TP0] Decode batch. #running-req: 1, #token: 584, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.95, #queue-req: 0, 
[2025-07-28 01:46:14 DP0 TP0] Decode batch. #running-req: 1, #token: 625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.05, #queue-req: 0, 
[2025-07-28 01:46:14 DP1 TP0] Decode batch. #running-req: 1, #token: 624, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.01, #queue-req: 0, 
[2025-07-28 01:46:14] INFO:     127.0.0.1:51022 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:46:14 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 82, #cached-token: 84, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:46:14 DP0 TP0] Decode batch. #running-req: 2, #token: 752, token usage: 0.00, cuda graph: True, gen throughput (token/s): 88.04, #queue-req: 0, 
[2025-07-28 01:46:15 DP0 TP0] Decode batch. #running-req: 2, #token: 832, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.92, #queue-req: 0, 
[2025-07-28 01:46:15 DP0 TP0] Decode batch. #running-req: 2, #token: 912, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.59, #queue-req: 0, 
[2025-07-28 01:46:15 DP0 TP0] Decode batch. #running-req: 2, #token: 992, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.24, #queue-req: 0, 
[2025-07-28 01:46:16 DP0 TP0] Decode batch. #running-req: 2, #token: 1072, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.20, #queue-req: 0, 
[2025-07-28 01:46:16 DP0 TP0] Decode batch. #running-req: 2, #token: 1152, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.49, #queue-req: 0, 
[2025-07-28 01:46:16 DP0 TP0] Decode batch. #running-req: 2, #token: 1232, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.67, #queue-req: 0, 
[2025-07-28 01:46:17 DP0 TP0] Decode batch. #running-req: 2, #token: 1312, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.75, #queue-req: 0, 
[2025-07-28 01:46:17 DP0 TP0] Decode batch. #running-req: 2, #token: 1392, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.49, #queue-req: 0, 
[2025-07-28 01:46:17 DP0 TP0] Decode batch. #running-req: 2, #token: 1472, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.52, #queue-req: 0, 
[2025-07-28 01:46:18 DP0 TP0] Decode batch. #running-req: 2, #token: 1552, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.14, #queue-req: 0, 
[2025-07-28 01:46:18 DP0 TP0] Decode batch. #running-req: 2, #token: 1632, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.86, #queue-req: 0, 
[2025-07-28 01:46:18 DP0 TP0] Decode batch. #running-req: 2, #token: 1712, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.36, #queue-req: 0, 
[2025-07-28 01:46:18 DP0 TP0] Decode batch. #running-req: 2, #token: 1792, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.24, #queue-req: 0, 
[2025-07-28 01:46:19] INFO:     127.0.0.1:51012 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:46:19 DP0 TP0] Decode batch. #running-req: 1, #token: 731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.23, #queue-req: 0, 
[2025-07-28 01:46:19 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 205, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:46:19 DP0 TP0] Decode batch. #running-req: 1, #token: 771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 01:46:19 DP1 TP0] Decode batch. #running-req: 1, #token: 324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 7.94, #queue-req: 0, 
[2025-07-28 01:46:19 DP0 TP0] Decode batch. #running-req: 1, #token: 811, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.03, #queue-req: 0, 
[2025-07-28 01:46:19 DP1 TP0] Decode batch. #running-req: 1, #token: 364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.14, #queue-req: 0, 
[2025-07-28 01:46:20 DP0 TP0] Decode batch. #running-req: 1, #token: 851, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.99, #queue-req: 0, 
[2025-07-28 01:46:20 DP1 TP0] Decode batch. #running-req: 1, #token: 404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.89, #queue-req: 0, 
[2025-07-28 01:46:20 DP0 TP0] Decode batch. #running-req: 1, #token: 891, token usage: 0.00, cuda graph: True, gen throughput (token/s): 131.93, #queue-req: 0, 
[2025-07-28 01:46:20 DP1 TP0] Decode batch. #running-req: 1, #token: 444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.79, #queue-req: 0, 
[2025-07-28 01:46:20 DP0 TP0] Decode batch. #running-req: 1, #token: 931, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.05, #queue-req: 0, 
[2025-07-28 01:46:20 DP1 TP0] Decode batch. #running-req: 1, #token: 484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.75, #queue-req: 0, 
[2025-07-28 01:46:20] INFO:     127.0.0.1:56208 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:46:20 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 125, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:46:21 DP1 TP0] Decode batch. #running-req: 1, #token: 524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.79, #queue-req: 0, 
[2025-07-28 01:46:21 DP0 TP0] Decode batch. #running-req: 1, #token: 228, token usage: 0.00, cuda graph: True, gen throughput (token/s): 116.24, #queue-req: 0, 
[2025-07-28 01:46:21 DP1 TP0] Decode batch. #running-req: 1, #token: 564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.29, #queue-req: 0, 
[2025-07-28 01:46:21 DP0 TP0] Decode batch. #running-req: 1, #token: 268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 01:46:21 DP1 TP0] Decode batch. #running-req: 1, #token: 604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.83, #queue-req: 0, 
[2025-07-28 01:46:21 DP0 TP0] Decode batch. #running-req: 1, #token: 308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:46:21 DP1 TP0] Decode batch. #running-req: 1, #token: 644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.23, #queue-req: 0, 
[2025-07-28 01:46:21 DP0 TP0] Decode batch. #running-req: 1, #token: 348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 01:46:22 DP1 TP0] Decode batch. #running-req: 1, #token: 684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.10, #queue-req: 0, 
[2025-07-28 01:46:22 DP0 TP0] Decode batch. #running-req: 1, #token: 388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 01:46:22 DP1 TP0] Decode batch. #running-req: 1, #token: 724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 01:46:22 DP0 TP0] Decode batch. #running-req: 1, #token: 428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.03, #queue-req: 0, 
[2025-07-28 01:46:22 DP1 TP0] Decode batch. #running-req: 1, #token: 764, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.24, #queue-req: 0, 
[2025-07-28 01:46:22 DP0 TP0] Decode batch. #running-req: 1, #token: 468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.05, #queue-req: 0, 
[2025-07-28 01:46:23 DP1 TP0] Decode batch. #running-req: 1, #token: 804, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 01:46:23 DP0 TP0] Decode batch. #running-req: 1, #token: 508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.09, #queue-req: 0, 
[2025-07-28 01:46:23 DP1 TP0] Decode batch. #running-req: 1, #token: 844, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.12, #queue-req: 0, 
[2025-07-28 01:46:23 DP0 TP0] Decode batch. #running-req: 1, #token: 548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.09, #queue-req: 0, 
[2025-07-28 01:46:23 DP1 TP0] Decode batch. #running-req: 1, #token: 884, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.08, #queue-req: 0, 
[2025-07-28 01:46:23 DP0 TP0] Decode batch. #running-req: 1, #token: 588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.05, #queue-req: 0, 
[2025-07-28 01:46:24 DP1 TP0] Decode batch. #running-req: 1, #token: 924, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.10, #queue-req: 0, 
[2025-07-28 01:46:24 DP0 TP0] Decode batch. #running-req: 1, #token: 628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:46:24 DP1 TP0] Decode batch. #running-req: 1, #token: 964, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 01:46:24 DP0 TP0] Decode batch. #running-req: 1, #token: 668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 01:46:24 DP1 TP0] Decode batch. #running-req: 1, #token: 1004, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:46:24 DP0 TP0] Decode batch. #running-req: 1, #token: 708, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 01:46:24 DP1 TP0] Decode batch. #running-req: 1, #token: 1044, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.02, #queue-req: 0, 
[2025-07-28 01:46:24 DP0 TP0] Decode batch. #running-req: 1, #token: 748, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.14, #queue-req: 0, 
[2025-07-28 01:46:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1084, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.86, #queue-req: 0, 
[2025-07-28 01:46:25 DP0 TP0] Decode batch. #running-req: 1, #token: 788, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 01:46:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1124, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.82, #queue-req: 0, 
[2025-07-28 01:46:25 DP0 TP0] Decode batch. #running-req: 1, #token: 828, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.17, #queue-req: 0, 
[2025-07-28 01:46:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1164, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.81, #queue-req: 0, 
[2025-07-28 01:46:25 DP0 TP0] Decode batch. #running-req: 1, #token: 868, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 01:46:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1204, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.69, #queue-req: 0, 
[2025-07-28 01:46:26 DP0 TP0] Decode batch. #running-req: 1, #token: 908, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.13, #queue-req: 0, 
[2025-07-28 01:46:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.78, #queue-req: 0, 
[2025-07-28 01:46:26 DP0 TP0] Decode batch. #running-req: 1, #token: 948, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.86, #queue-req: 0, 
[2025-07-28 01:46:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.75, #queue-req: 0, 
[2025-07-28 01:46:26 DP0 TP0] Decode batch. #running-req: 1, #token: 988, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.01, #queue-req: 0, 
[2025-07-28 01:46:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.76, #queue-req: 0, 
[2025-07-28 01:46:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1028, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.06, #queue-req: 0, 
[2025-07-28 01:46:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.68, #queue-req: 0, 
[2025-07-28 01:46:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1068, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.69, #queue-req: 0, 
[2025-07-28 01:46:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.73, #queue-req: 0, 
[2025-07-28 01:46:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1108, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.80, #queue-req: 0, 
[2025-07-28 01:46:27] INFO:     127.0.0.1:56224 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:46:27 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 131, #cached-token: 84, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:46:28 DP1 TP0] Decode batch. #running-req: 2, #token: 1585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 96.70, #queue-req: 0, 
[2025-07-28 01:46:28 DP1 TP0] Decode batch. #running-req: 2, #token: 1665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.06, #queue-req: 0, 
[2025-07-28 01:46:28 DP1 TP0] Decode batch. #running-req: 2, #token: 1745, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.70, #queue-req: 0, 
[2025-07-28 01:46:28 DP1 TP0] Decode batch. #running-req: 2, #token: 1825, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.80, #queue-req: 0, 
[2025-07-28 01:46:29 DP1 TP0] Decode batch. #running-req: 2, #token: 1905, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.34, #queue-req: 0, 
[2025-07-28 01:46:29 DP1 TP0] Decode batch. #running-req: 2, #token: 1985, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.40, #queue-req: 0, 
[2025-07-28 01:46:29 DP1 TP0] Decode batch. #running-req: 2, #token: 2065, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.17, #queue-req: 0, 
[2025-07-28 01:46:30 DP1 TP0] Decode batch. #running-req: 2, #token: 2145, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.36, #queue-req: 0, 
[2025-07-28 01:46:30 DP1 TP0] Decode batch. #running-req: 2, #token: 2225, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.81, #queue-req: 0, 
[2025-07-28 01:46:30 DP1 TP0] Decode batch. #running-req: 2, #token: 2305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.66, #queue-req: 0, 
[2025-07-28 01:46:31 DP1 TP0] Decode batch. #running-req: 2, #token: 2385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.64, #queue-req: 0, 
[2025-07-28 01:46:31 DP1 TP0] Decode batch. #running-req: 2, #token: 2465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.56, #queue-req: 0, 
[2025-07-28 01:46:31] INFO:     127.0.0.1:56222 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:46:31 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 184, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:46:31 DP1 TP0] Decode batch. #running-req: 1, #token: 705, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.38, #queue-req: 0, 
[2025-07-28 01:46:31 DP0 TP0] Decode batch. #running-req: 1, #token: 281, token usage: 0.00, cuda graph: True, gen throughput (token/s): 9.20, #queue-req: 0, 
[2025-07-28 01:46:32 DP1 TP0] Decode batch. #running-req: 1, #token: 745, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 01:46:32 DP0 TP0] Decode batch. #running-req: 1, #token: 321, token usage: 0.00, cuda graph: True, gen throughput (token/s): 128.32, #queue-req: 0, 
[2025-07-28 01:46:32 DP1 TP0] Decode batch. #running-req: 1, #token: 785, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 01:46:32 DP0 TP0] Decode batch. #running-req: 1, #token: 361, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.40, #queue-req: 0, 
[2025-07-28 01:46:32 DP1 TP0] Decode batch. #running-req: 1, #token: 825, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 01:46:32 DP0 TP0] Decode batch. #running-req: 1, #token: 401, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.36, #queue-req: 0, 
[2025-07-28 01:46:32 DP1 TP0] Decode batch. #running-req: 1, #token: 865, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 01:46:33 DP0 TP0] Decode batch. #running-req: 1, #token: 441, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 01:46:33 DP1 TP0] Decode batch. #running-req: 1, #token: 905, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 01:46:33 DP0 TP0] Decode batch. #running-req: 1, #token: 481, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.24, #queue-req: 0, 
[2025-07-28 01:46:33 DP1 TP0] Decode batch. #running-req: 1, #token: 945, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.17, #queue-req: 0, 
[2025-07-28 01:46:33 DP0 TP0] Decode batch. #running-req: 1, #token: 521, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 01:46:33] INFO:     127.0.0.1:45978 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:46:33 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 171, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:46:33 DP1 TP0] Decode batch. #running-req: 1, #token: 268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.42, #queue-req: 0, 
[2025-07-28 01:46:33 DP0 TP0] Decode batch. #running-req: 1, #token: 561, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.34, #queue-req: 0, 
[2025-07-28 01:46:34 DP1 TP0] Decode batch. #running-req: 1, #token: 308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.37, #queue-req: 0, 
[2025-07-28 01:46:34 DP0 TP0] Decode batch. #running-req: 1, #token: 601, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.38, #queue-req: 0, 
[2025-07-28 01:46:34 DP1 TP0] Decode batch. #running-req: 1, #token: 348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 01:46:34 DP0 TP0] Decode batch. #running-req: 1, #token: 641, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 01:46:34 DP1 TP0] Decode batch. #running-req: 1, #token: 388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 01:46:34 DP0 TP0] Decode batch. #running-req: 1, #token: 681, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 01:46:35 DP1 TP0] Decode batch. #running-req: 1, #token: 428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 01:46:35 DP0 TP0] Decode batch. #running-req: 1, #token: 721, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 01:46:35 DP1 TP0] Decode batch. #running-req: 1, #token: 468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.59, #queue-req: 0, 
[2025-07-28 01:46:35 DP0 TP0] Decode batch. #running-req: 1, #token: 761, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.31, #queue-req: 0, 
[2025-07-28 01:46:35 DP1 TP0] Decode batch. #running-req: 1, #token: 508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.10, #queue-req: 0, 
[2025-07-28 01:46:35 DP0 TP0] Decode batch. #running-req: 1, #token: 801, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 01:46:35 DP1 TP0] Decode batch. #running-req: 1, #token: 548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 01:46:35 DP0 TP0] Decode batch. #running-req: 1, #token: 841, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 01:46:36 DP1 TP0] Decode batch. #running-req: 1, #token: 588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 01:46:36 DP0 TP0] Decode batch. #running-req: 1, #token: 881, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 01:46:36 DP1 TP0] Decode batch. #running-req: 1, #token: 628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.10, #queue-req: 0, 
[2025-07-28 01:46:36 DP0 TP0] Decode batch. #running-req: 1, #token: 921, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 01:46:36 DP1 TP0] Decode batch. #running-req: 1, #token: 668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.05, #queue-req: 0, 
[2025-07-28 01:46:36] INFO:     127.0.0.1:45980 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:46:36 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 224, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:46:36 DP0 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.03, #queue-req: 0, 
[2025-07-28 01:46:37 DP1 TP0] Decode batch. #running-req: 1, #token: 708, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:46:37 DP0 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.34, #queue-req: 0, 
[2025-07-28 01:46:37 DP1 TP0] Decode batch. #running-req: 1, #token: 748, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.09, #queue-req: 0, 
[2025-07-28 01:46:37 DP0 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.39, #queue-req: 0, 
[2025-07-28 01:46:37 DP1 TP0] Decode batch. #running-req: 1, #token: 788, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.12, #queue-req: 0, 
[2025-07-28 01:46:37 DP0 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 01:46:37 DP1 TP0] Decode batch. #running-req: 1, #token: 828, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.01, #queue-req: 0, 
[2025-07-28 01:46:38 DP0 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:46:38 DP1 TP0] Decode batch. #running-req: 1, #token: 868, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.08, #queue-req: 0, 
[2025-07-28 01:46:38 DP0 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 01:46:38 DP1 TP0] Decode batch. #running-req: 1, #token: 908, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.06, #queue-req: 0, 
[2025-07-28 01:46:38 DP0 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 01:46:38 DP1 TP0] Decode batch. #running-req: 1, #token: 948, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.08, #queue-req: 0, 
[2025-07-28 01:46:38 DP0 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.34, #queue-req: 0, 
[2025-07-28 01:46:39 DP1 TP0] Decode batch. #running-req: 1, #token: 988, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.98, #queue-req: 0, 
[2025-07-28 01:46:39 DP0 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 01:46:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1028, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.03, #queue-req: 0, 
[2025-07-28 01:46:39 DP0 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.12, #queue-req: 0, 
[2025-07-28 01:46:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1068, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.76, #queue-req: 0, 
[2025-07-28 01:46:39 DP0 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 01:46:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1108, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.69, #queue-req: 0, 
[2025-07-28 01:46:40 DP0 TP0] Decode batch. #running-req: 1, #token: 755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 01:46:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1148, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.51, #queue-req: 0, 
[2025-07-28 01:46:40 DP0 TP0] Decode batch. #running-req: 1, #token: 795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 01:46:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1188, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.59, #queue-req: 0, 
[2025-07-28 01:46:40 DP0 TP0] Decode batch. #running-req: 1, #token: 835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.30, #queue-req: 0, 
[2025-07-28 01:46:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1228, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.59, #queue-req: 0, 
[2025-07-28 01:46:40 DP0 TP0] Decode batch. #running-req: 1, #token: 875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 01:46:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.65, #queue-req: 0, 
[2025-07-28 01:46:41 DP0 TP0] Decode batch. #running-req: 1, #token: 915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 01:46:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.67, #queue-req: 0, 
[2025-07-28 01:46:41 DP0 TP0] Decode batch. #running-req: 1, #token: 955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 01:46:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.68, #queue-req: 0, 
[2025-07-28 01:46:41 DP0 TP0] Decode batch. #running-req: 1, #token: 995, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.84, #queue-req: 0, 
[2025-07-28 01:46:42 DP1 TP0] Decode batch. #running-req: 1, #token: 1388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.63, #queue-req: 0, 
[2025-07-28 01:46:42 DP0 TP0] Decode batch. #running-req: 1, #token: 1035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.12, #queue-req: 0, 
[2025-07-28 01:46:42 DP1 TP0] Decode batch. #running-req: 1, #token: 1428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.28, #queue-req: 0, 
[2025-07-28 01:46:42 DP0 TP0] Decode batch. #running-req: 1, #token: 1075, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.88, #queue-req: 0, 
[2025-07-28 01:46:42 DP1 TP0] Decode batch. #running-req: 1, #token: 1468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.57, #queue-req: 0, 
[2025-07-28 01:46:42 DP0 TP0] Decode batch. #running-req: 1, #token: 1115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.86, #queue-req: 0, 
[2025-07-28 01:46:42 DP1 TP0] Decode batch. #running-req: 1, #token: 1508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.51, #queue-req: 0, 
[2025-07-28 01:46:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1155, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.87, #queue-req: 0, 
[2025-07-28 01:46:43 DP1 TP0] Decode batch. #running-req: 1, #token: 1548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.55, #queue-req: 0, 
[2025-07-28 01:46:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.87, #queue-req: 0, 
[2025-07-28 01:46:43 DP1 TP0] Decode batch. #running-req: 1, #token: 1588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.61, #queue-req: 0, 
[2025-07-28 01:46:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.82, #queue-req: 0, 
[2025-07-28 01:46:43 DP1 TP0] Decode batch. #running-req: 1, #token: 1628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.63, #queue-req: 0, 
[2025-07-28 01:46:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.81, #queue-req: 0, 
[2025-07-28 01:46:43] INFO:     127.0.0.1:43566 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:46:43 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 166, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:46:44 DP1 TP0] Decode batch. #running-req: 2, #token: 1853, token usage: 0.00, cuda graph: True, gen throughput (token/s): 167.87, #queue-req: 0, 
[2025-07-28 01:46:44 DP1 TP0] Decode batch. #running-req: 2, #token: 1933, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.76, #queue-req: 0, 
[2025-07-28 01:46:44 DP1 TP0] Decode batch. #running-req: 2, #token: 2013, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.08, #queue-req: 0, 
[2025-07-28 01:46:45 DP1 TP0] Decode batch. #running-req: 2, #token: 2093, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.39, #queue-req: 0, 
[2025-07-28 01:46:45 DP1 TP0] Decode batch. #running-req: 2, #token: 2173, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.02, #queue-req: 0, 
[2025-07-28 01:46:45 DP1 TP0] Decode batch. #running-req: 2, #token: 2253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.08, #queue-req: 0, 
[2025-07-28 01:46:46 DP1 TP0] Decode batch. #running-req: 2, #token: 2333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.16, #queue-req: 0, 
[2025-07-28 01:46:46 DP1 TP0] Decode batch. #running-req: 2, #token: 2413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.95, #queue-req: 0, 
[2025-07-28 01:46:46] INFO:     127.0.0.1:43560 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:46:46 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 152, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:46:46 DP1 TP0] Decode batch. #running-req: 1, #token: 591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 206.99, #queue-req: 0, 
[2025-07-28 01:46:46 DP0 TP0] Decode batch. #running-req: 1, #token: 270, token usage: 0.00, cuda graph: True, gen throughput (token/s): 13.49, #queue-req: 0, 
[2025-07-28 01:46:46 DP1 TP0] Decode batch. #running-req: 1, #token: 631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.12, #queue-req: 0, 
[2025-07-28 01:46:47 DP0 TP0] Decode batch. #running-req: 1, #token: 310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.39, #queue-req: 0, 
[2025-07-28 01:46:47 DP1 TP0] Decode batch. #running-req: 1, #token: 671, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 01:46:47 DP0 TP0] Decode batch. #running-req: 1, #token: 350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 01:46:47 DP1 TP0] Decode batch. #running-req: 1, #token: 711, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.10, #queue-req: 0, 
[2025-07-28 01:46:47 DP0 TP0] Decode batch. #running-req: 1, #token: 390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:46:47 DP1 TP0] Decode batch. #running-req: 1, #token: 751, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.52, #queue-req: 0, 
[2025-07-28 01:46:48 DP0 TP0] Decode batch. #running-req: 1, #token: 430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.01, #queue-req: 0, 
[2025-07-28 01:46:48 DP1 TP0] Decode batch. #running-req: 1, #token: 791, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.33, #queue-req: 0, 
[2025-07-28 01:46:48 DP0 TP0] Decode batch. #running-req: 1, #token: 470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 132.42, #queue-req: 0, 
[2025-07-28 01:46:48 DP1 TP0] Decode batch. #running-req: 1, #token: 831, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 01:46:48 DP0 TP0] Decode batch. #running-req: 1, #token: 510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.85, #queue-req: 0, 
[2025-07-28 01:46:48 DP1 TP0] Decode batch. #running-req: 1, #token: 871, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.23, #queue-req: 0, 
[2025-07-28 01:46:48 DP0 TP0] Decode batch. #running-req: 1, #token: 550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:46:49 DP1 TP0] Decode batch. #running-req: 1, #token: 911, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:46:49 DP0 TP0] Decode batch. #running-req: 1, #token: 590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 01:46:49 DP1 TP0] Decode batch. #running-req: 1, #token: 951, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 01:46:49 DP0 TP0] Decode batch. #running-req: 1, #token: 630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 01:46:49 DP1 TP0] Decode batch. #running-req: 1, #token: 991, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.13, #queue-req: 0, 
[2025-07-28 01:46:49 DP0 TP0] Decode batch. #running-req: 1, #token: 670, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 01:46:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1031, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 01:46:50 DP0 TP0] Decode batch. #running-req: 1, #token: 710, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.13, #queue-req: 0, 
[2025-07-28 01:46:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1071, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.89, #queue-req: 0, 
[2025-07-28 01:46:50 DP0 TP0] Decode batch. #running-req: 1, #token: 750, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 01:46:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1111, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.88, #queue-req: 0, 
[2025-07-28 01:46:50 DP0 TP0] Decode batch. #running-req: 1, #token: 790, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.10, #queue-req: 0, 
[2025-07-28 01:46:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1151, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.86, #queue-req: 0, 
[2025-07-28 01:46:50 DP0 TP0] Decode batch. #running-req: 1, #token: 830, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.04, #queue-req: 0, 
[2025-07-28 01:46:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1191, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.83, #queue-req: 0, 
[2025-07-28 01:46:51 DP0 TP0] Decode batch. #running-req: 1, #token: 870, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 01:46:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1231, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.79, #queue-req: 0, 
[2025-07-28 01:46:51 DP0 TP0] Decode batch. #running-req: 1, #token: 910, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.08, #queue-req: 0, 
[2025-07-28 01:46:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1271, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.76, #queue-req: 0, 
[2025-07-28 01:46:51 DP0 TP0] Decode batch. #running-req: 1, #token: 950, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.09, #queue-req: 0, 
[2025-07-28 01:46:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1311, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.72, #queue-req: 0, 
[2025-07-28 01:46:52 DP0 TP0] Decode batch. #running-req: 1, #token: 990, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 01:46:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1351, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.67, #queue-req: 0, 
[2025-07-28 01:46:52] INFO:     127.0.0.1:51972 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:46:52 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 139, #cached-token: 84, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:46:52 DP1 TP0] Decode batch. #running-req: 2, #token: 1556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 187.83, #queue-req: 0, 
[2025-07-28 01:46:52 DP1 TP0] Decode batch. #running-req: 2, #token: 1636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.42, #queue-req: 0, 
[2025-07-28 01:46:53] INFO:     127.0.0.1:51958 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:46:53 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 190, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:46:53 DP1 TP0] Decode batch. #running-req: 1, #token: 329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 227.40, #queue-req: 0, 
[2025-07-28 01:46:53 DP0 TP0] Decode batch. #running-req: 1, #token: 294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 34.02, #queue-req: 0, 
[2025-07-28 01:46:53 DP1 TP0] Decode batch. #running-req: 1, #token: 369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:46:53 DP0 TP0] Decode batch. #running-req: 1, #token: 334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.82, #queue-req: 0, 
[2025-07-28 01:46:53 DP1 TP0] Decode batch. #running-req: 1, #token: 409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 01:46:53 DP0 TP0] Decode batch. #running-req: 1, #token: 374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 01:46:54 DP1 TP0] Decode batch. #running-req: 1, #token: 449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.08, #queue-req: 0, 
[2025-07-28 01:46:54 DP0 TP0] Decode batch. #running-req: 1, #token: 414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.07, #queue-req: 0, 
[2025-07-28 01:46:54 DP1 TP0] Decode batch. #running-req: 1, #token: 489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.08, #queue-req: 0, 
[2025-07-28 01:46:54 DP0 TP0] Decode batch. #running-req: 1, #token: 454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.01, #queue-req: 0, 
[2025-07-28 01:46:54 DP1 TP0] Decode batch. #running-req: 1, #token: 529, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.09, #queue-req: 0, 
[2025-07-28 01:46:54 DP0 TP0] Decode batch. #running-req: 1, #token: 494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.04, #queue-req: 0, 
[2025-07-28 01:46:54 DP1 TP0] Decode batch. #running-req: 1, #token: 569, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.31, #queue-req: 0, 
[2025-07-28 01:46:55 DP0 TP0] Decode batch. #running-req: 1, #token: 534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.08, #queue-req: 0, 
[2025-07-28 01:46:55 DP1 TP0] Decode batch. #running-req: 1, #token: 609, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 01:46:55 DP0 TP0] Decode batch. #running-req: 1, #token: 574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.12, #queue-req: 0, 
[2025-07-28 01:46:55 DP1 TP0] Decode batch. #running-req: 1, #token: 649, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 01:46:55 DP0 TP0] Decode batch. #running-req: 1, #token: 614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.12, #queue-req: 0, 
[2025-07-28 01:46:55 DP1 TP0] Decode batch. #running-req: 1, #token: 689, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.12, #queue-req: 0, 
[2025-07-28 01:46:55 DP0 TP0] Decode batch. #running-req: 1, #token: 654, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.10, #queue-req: 0, 
[2025-07-28 01:46:56 DP1 TP0] Decode batch. #running-req: 1, #token: 729, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.93, #queue-req: 0, 
[2025-07-28 01:46:56 DP0 TP0] Decode batch. #running-req: 1, #token: 694, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.10, #queue-req: 0, 
[2025-07-28 01:46:56 DP1 TP0] Decode batch. #running-req: 1, #token: 769, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.94, #queue-req: 0, 
[2025-07-28 01:46:56 DP0 TP0] Decode batch. #running-req: 1, #token: 734, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.83, #queue-req: 0, 
[2025-07-28 01:46:56 DP1 TP0] Decode batch. #running-req: 1, #token: 809, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 01:46:56 DP0 TP0] Decode batch. #running-req: 1, #token: 774, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 01:46:56 DP1 TP0] Decode batch. #running-req: 1, #token: 849, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.10, #queue-req: 0, 
[2025-07-28 01:46:57 DP0 TP0] Decode batch. #running-req: 1, #token: 814, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.09, #queue-req: 0, 
[2025-07-28 01:46:57 DP1 TP0] Decode batch. #running-req: 1, #token: 889, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.14, #queue-req: 0, 
[2025-07-28 01:46:57 DP0 TP0] Decode batch. #running-req: 1, #token: 854, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.96, #queue-req: 0, 
[2025-07-28 01:46:57 DP1 TP0] Decode batch. #running-req: 1, #token: 929, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.02, #queue-req: 0, 
[2025-07-28 01:46:57 DP0 TP0] Decode batch. #running-req: 1, #token: 894, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.01, #queue-req: 0, 
[2025-07-28 01:46:57 DP1 TP0] Decode batch. #running-req: 1, #token: 969, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 01:46:57 DP0 TP0] Decode batch. #running-req: 1, #token: 934, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.04, #queue-req: 0, 
[2025-07-28 01:46:58 DP1 TP0] Decode batch. #running-req: 1, #token: 1009, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:46:58 DP0 TP0] Decode batch. #running-req: 1, #token: 974, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.03, #queue-req: 0, 
[2025-07-28 01:46:58 DP1 TP0] Decode batch. #running-req: 1, #token: 1049, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.02, #queue-req: 0, 
[2025-07-28 01:46:58] INFO:     127.0.0.1:44048 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:46:58 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 188, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:46:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1014, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.02, #queue-req: 0, 
[2025-07-28 01:46:58 DP1 TP0] Decode batch. #running-req: 1, #token: 302, token usage: 0.00, cuda graph: True, gen throughput (token/s): 115.38, #queue-req: 0, 
[2025-07-28 01:46:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1054, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.76, #queue-req: 0, 
[2025-07-28 01:46:59 DP1 TP0] Decode batch. #running-req: 1, #token: 342, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.38, #queue-req: 0, 
[2025-07-28 01:46:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1094, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.66, #queue-req: 0, 
[2025-07-28 01:46:59 DP1 TP0] Decode batch. #running-req: 1, #token: 382, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.38, #queue-req: 0, 
[2025-07-28 01:46:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1134, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.59, #queue-req: 0, 
[2025-07-28 01:46:59 DP1 TP0] Decode batch. #running-req: 1, #token: 422, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.17, #queue-req: 0, 
[2025-07-28 01:46:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1174, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.68, #queue-req: 0, 
[2025-07-28 01:46:59 DP1 TP0] Decode batch. #running-req: 1, #token: 462, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.10, #queue-req: 0, 
[2025-07-28 01:47:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1214, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.45, #queue-req: 0, 
[2025-07-28 01:47:00 DP1 TP0] Decode batch. #running-req: 1, #token: 502, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.08, #queue-req: 0, 
[2025-07-28 01:47:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1254, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.60, #queue-req: 0, 
[2025-07-28 01:47:00 DP1 TP0] Decode batch. #running-req: 1, #token: 542, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.99, #queue-req: 0, 
[2025-07-28 01:47:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.69, #queue-req: 0, 
[2025-07-28 01:47:00 DP1 TP0] Decode batch. #running-req: 1, #token: 582, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:47:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.56, #queue-req: 0, 
[2025-07-28 01:47:01 DP1 TP0] Decode batch. #running-req: 1, #token: 622, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 01:47:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.56, #queue-req: 0, 
[2025-07-28 01:47:01 DP1 TP0] Decode batch. #running-req: 1, #token: 662, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 01:47:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.57, #queue-req: 0, 
[2025-07-28 01:47:01 DP1 TP0] Decode batch. #running-req: 1, #token: 702, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.17, #queue-req: 0, 
[2025-07-28 01:47:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.37, #queue-req: 0, 
[2025-07-28 01:47:01 DP1 TP0] Decode batch. #running-req: 1, #token: 742, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 01:47:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.54, #queue-req: 0, 
[2025-07-28 01:47:02 DP1 TP0] Decode batch. #running-req: 1, #token: 782, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.14, #queue-req: 0, 
[2025-07-28 01:47:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.45, #queue-req: 0, 
[2025-07-28 01:47:02 DP1 TP0] Decode batch. #running-req: 1, #token: 822, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.96, #queue-req: 0, 
[2025-07-28 01:47:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.35, #queue-req: 0, 
[2025-07-28 01:47:02 DP1 TP0] Decode batch. #running-req: 1, #token: 862, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.06, #queue-req: 0, 
[2025-07-28 01:47:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.46, #queue-req: 0, 
[2025-07-28 01:47:03 DP1 TP0] Decode batch. #running-req: 1, #token: 902, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.09, #queue-req: 0, 
[2025-07-28 01:47:03 DP0 TP0] Decode batch. #running-req: 1, #token: 1654, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.45, #queue-req: 0, 
[2025-07-28 01:47:03 DP1 TP0] Decode batch. #running-req: 1, #token: 942, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.12, #queue-req: 0, 
[2025-07-28 01:47:03 DP0 TP0] Decode batch. #running-req: 1, #token: 1694, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.51, #queue-req: 0, 
[2025-07-28 01:47:03 DP1 TP0] Decode batch. #running-req: 1, #token: 982, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.05, #queue-req: 0, 
[2025-07-28 01:47:03 DP0 TP0] Decode batch. #running-req: 1, #token: 1734, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.42, #queue-req: 0, 
[2025-07-28 01:47:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1022, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.99, #queue-req: 0, 
[2025-07-28 01:47:04 DP0 TP0] Decode batch. #running-req: 1, #token: 1774, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.48, #queue-req: 0, 
[2025-07-28 01:47:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1062, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.75, #queue-req: 0, 
[2025-07-28 01:47:04 DP0 TP0] Decode batch. #running-req: 1, #token: 1814, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.51, #queue-req: 0, 
[2025-07-28 01:47:04] INFO:     127.0.0.1:44050 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:47:04 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 135, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:47:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1102, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.75, #queue-req: 0, 
[2025-07-28 01:47:04 DP0 TP0] Decode batch. #running-req: 1, #token: 253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.13, #queue-req: 0, 
[2025-07-28 01:47:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1142, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.76, #queue-req: 0, 
[2025-07-28 01:47:05 DP0 TP0] Decode batch. #running-req: 1, #token: 293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.30, #queue-req: 0, 
[2025-07-28 01:47:05 DP1 TP0] Decode batch. #running-req: 1, #token: 1182, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.78, #queue-req: 0, 
[2025-07-28 01:47:05 DP0 TP0] Decode batch. #running-req: 1, #token: 333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 01:47:05 DP1 TP0] Decode batch. #running-req: 1, #token: 1222, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.71, #queue-req: 0, 
[2025-07-28 01:47:05 DP0 TP0] Decode batch. #running-req: 1, #token: 373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 01:47:05 DP1 TP0] Decode batch. #running-req: 1, #token: 1262, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.68, #queue-req: 0, 
[2025-07-28 01:47:05 DP0 TP0] Decode batch. #running-req: 1, #token: 413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 01:47:06 DP1 TP0] Decode batch. #running-req: 1, #token: 1302, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.64, #queue-req: 0, 
[2025-07-28 01:47:06 DP0 TP0] Decode batch. #running-req: 1, #token: 453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.06, #queue-req: 0, 
[2025-07-28 01:47:06 DP1 TP0] Decode batch. #running-req: 1, #token: 1342, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.51, #queue-req: 0, 
[2025-07-28 01:47:06 DP0 TP0] Decode batch. #running-req: 1, #token: 493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.05, #queue-req: 0, 
[2025-07-28 01:47:06 DP1 TP0] Decode batch. #running-req: 1, #token: 1382, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.40, #queue-req: 0, 
[2025-07-28 01:47:06 DP0 TP0] Decode batch. #running-req: 1, #token: 533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.10, #queue-req: 0, 
[2025-07-28 01:47:06 DP1 TP0] Decode batch. #running-req: 1, #token: 1422, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.64, #queue-req: 0, 
[2025-07-28 01:47:07 DP0 TP0] Decode batch. #running-req: 1, #token: 573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:47:07 DP1 TP0] Decode batch. #running-req: 1, #token: 1462, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.50, #queue-req: 0, 
[2025-07-28 01:47:07 DP0 TP0] Decode batch. #running-req: 1, #token: 613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 01:47:07 DP1 TP0] Decode batch. #running-req: 1, #token: 1502, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.51, #queue-req: 0, 
[2025-07-28 01:47:07] INFO:     127.0.0.1:44066 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:47:07 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 130, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:47:07 DP0 TP0] Decode batch. #running-req: 1, #token: 653, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.10, #queue-req: 0, 
[2025-07-28 01:47:07 DP1 TP0] Decode batch. #running-req: 1, #token: 251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 116.72, #queue-req: 0, 
[2025-07-28 01:47:07 DP0 TP0] Decode batch. #running-req: 1, #token: 693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.06, #queue-req: 0, 
[2025-07-28 01:47:08 DP1 TP0] Decode batch. #running-req: 1, #token: 291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.51, #queue-req: 0, 
[2025-07-28 01:47:08 DP0 TP0] Decode batch. #running-req: 1, #token: 733, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.12, #queue-req: 0, 
[2025-07-28 01:47:08 DP1 TP0] Decode batch. #running-req: 1, #token: 331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.45, #queue-req: 0, 
[2025-07-28 01:47:08 DP0 TP0] Decode batch. #running-req: 1, #token: 773, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.09, #queue-req: 0, 
[2025-07-28 01:47:08 DP1 TP0] Decode batch. #running-req: 1, #token: 371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.50, #queue-req: 0, 
[2025-07-28 01:47:08 DP0 TP0] Decode batch. #running-req: 1, #token: 813, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.99, #queue-req: 0, 
[2025-07-28 01:47:09 DP1 TP0] Decode batch. #running-req: 1, #token: 411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.36, #queue-req: 0, 
[2025-07-28 01:47:09 DP0 TP0] Decode batch. #running-req: 1, #token: 853, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.02, #queue-req: 0, 
[2025-07-28 01:47:09 DP1 TP0] Decode batch. #running-req: 1, #token: 451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 01:47:09 DP0 TP0] Decode batch. #running-req: 1, #token: 893, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 01:47:09 DP1 TP0] Decode batch. #running-req: 1, #token: 491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 01:47:09 DP0 TP0] Decode batch. #running-req: 1, #token: 933, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.04, #queue-req: 0, 
[2025-07-28 01:47:09 DP1 TP0] Decode batch. #running-req: 1, #token: 531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.34, #queue-req: 0, 
[2025-07-28 01:47:10 DP0 TP0] Decode batch. #running-req: 1, #token: 973, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.04, #queue-req: 0, 
[2025-07-28 01:47:10 DP1 TP0] Decode batch. #running-req: 1, #token: 571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.39, #queue-req: 0, 
[2025-07-28 01:47:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1013, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.03, #queue-req: 0, 
[2025-07-28 01:47:10 DP1 TP0] Decode batch. #running-req: 1, #token: 611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.39, #queue-req: 0, 
[2025-07-28 01:47:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1053, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.80, #queue-req: 0, 
[2025-07-28 01:47:10 DP1 TP0] Decode batch. #running-req: 1, #token: 651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.39, #queue-req: 0, 
[2025-07-28 01:47:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1093, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.70, #queue-req: 0, 
[2025-07-28 01:47:11 DP1 TP0] Decode batch. #running-req: 1, #token: 691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.34, #queue-req: 0, 
[2025-07-28 01:47:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1133, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.55, #queue-req: 0, 
[2025-07-28 01:47:11 DP1 TP0] Decode batch. #running-req: 1, #token: 731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 01:47:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1173, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.64, #queue-req: 0, 
[2025-07-28 01:47:11 DP1 TP0] Decode batch. #running-req: 1, #token: 771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 01:47:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1213, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.56, #queue-req: 0, 
[2025-07-28 01:47:11 DP1 TP0] Decode batch. #running-req: 1, #token: 811, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.31, #queue-req: 0, 
[2025-07-28 01:47:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.67, #queue-req: 0, 
[2025-07-28 01:47:12 DP1 TP0] Decode batch. #running-req: 1, #token: 851, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 01:47:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.65, #queue-req: 0, 
[2025-07-28 01:47:12 DP1 TP0] Decode batch. #running-req: 1, #token: 891, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:47:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.58, #queue-req: 0, 
[2025-07-28 01:47:12 DP1 TP0] Decode batch. #running-req: 1, #token: 931, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.23, #queue-req: 0, 
[2025-07-28 01:47:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.63, #queue-req: 0, 
[2025-07-28 01:47:13 DP1 TP0] Decode batch. #running-req: 1, #token: 971, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 01:47:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.52, #queue-req: 0, 
[2025-07-28 01:47:13 DP1 TP0] Decode batch. #running-req: 1, #token: 1011, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.17, #queue-req: 0, 
[2025-07-28 01:47:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.36, #queue-req: 0, 
[2025-07-28 01:47:13 DP1 TP0] Decode batch. #running-req: 1, #token: 1051, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.01, #queue-req: 0, 
[2025-07-28 01:47:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.43, #queue-req: 0, 
[2025-07-28 01:47:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1091, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.86, #queue-req: 0, 
[2025-07-28 01:47:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.40, #queue-req: 0, 
[2025-07-28 01:47:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1131, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.89, #queue-req: 0, 
[2025-07-28 01:47:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.44, #queue-req: 0, 
[2025-07-28 01:47:14] INFO:     127.0.0.1:35096 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:47:14 DP1 TP0] Decode batch. #running-req: 1, #token: 0, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.95, #queue-req: 0, 
[2025-07-28 01:47:14 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 197, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:47:14 DP0 TP0] Decode batch. #running-req: 2, #token: 1826, token usage: 0.00, cuda graph: True, gen throughput (token/s): 153.98, #queue-req: 0, 
[2025-07-28 01:47:15 DP0 TP0] Decode batch. #running-req: 2, #token: 1906, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.07, #queue-req: 0, 
[2025-07-28 01:47:15 DP0 TP0] Decode batch. #running-req: 2, #token: 1986, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.95, #queue-req: 0, 
[2025-07-28 01:47:15 DP0 TP0] Decode batch. #running-req: 2, #token: 2066, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.94, #queue-req: 0, 
[2025-07-28 01:47:16 DP0 TP0] Decode batch. #running-req: 2, #token: 2146, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.75, #queue-req: 0, 
[2025-07-28 01:47:16 DP0 TP0] Decode batch. #running-req: 2, #token: 2226, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.96, #queue-req: 0, 
[2025-07-28 01:47:16 DP0 TP0] Decode batch. #running-req: 2, #token: 2306, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.83, #queue-req: 0, 
[2025-07-28 01:47:16 DP0 TP0] Decode batch. #running-req: 2, #token: 2386, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.70, #queue-req: 0, 
[2025-07-28 01:47:17 DP0 TP0] Decode batch. #running-req: 2, #token: 2466, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.54, #queue-req: 0, 
[2025-07-28 01:47:17 DP0 TP0] Decode batch. #running-req: 2, #token: 2546, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.53, #queue-req: 0, 
[2025-07-28 01:47:17 DP0 TP0] Decode batch. #running-req: 2, #token: 2626, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.54, #queue-req: 0, 
[2025-07-28 01:47:18 DP0 TP0] Decode batch. #running-req: 2, #token: 2706, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.44, #queue-req: 0, 
[2025-07-28 01:47:18 DP0 TP0] Decode batch. #running-req: 2, #token: 2786, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.07, #queue-req: 0, 
[2025-07-28 01:47:18] INFO:     127.0.0.1:35092 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:47:18 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 192, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:47:18 DP0 TP0] Decode batch. #running-req: 1, #token: 817, token usage: 0.00, cuda graph: True, gen throughput (token/s): 143.35, #queue-req: 0, 
[2025-07-28 01:47:18 DP1 TP0] Decode batch. #running-req: 1, #token: 319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 9.22, #queue-req: 0, 
[2025-07-28 01:47:19 DP0 TP0] Decode batch. #running-req: 1, #token: 857, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.08, #queue-req: 0, 
[2025-07-28 01:47:19 DP1 TP0] Decode batch. #running-req: 1, #token: 359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.50, #queue-req: 0, 
[2025-07-28 01:47:19 DP0 TP0] Decode batch. #running-req: 1, #token: 897, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.05, #queue-req: 0, 
[2025-07-28 01:47:19 DP1 TP0] Decode batch. #running-req: 1, #token: 399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 01:47:19 DP0 TP0] Decode batch. #running-req: 1, #token: 937, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:47:19 DP1 TP0] Decode batch. #running-req: 1, #token: 439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.30, #queue-req: 0, 
[2025-07-28 01:47:19 DP0 TP0] Decode batch. #running-req: 1, #token: 977, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.05, #queue-req: 0, 
[2025-07-28 01:47:20 DP1 TP0] Decode batch. #running-req: 1, #token: 479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 01:47:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1017, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 01:47:20 DP1 TP0] Decode batch. #running-req: 1, #token: 519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.30, #queue-req: 0, 
[2025-07-28 01:47:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1057, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.80, #queue-req: 0, 
[2025-07-28 01:47:20 DP1 TP0] Decode batch. #running-req: 1, #token: 559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.34, #queue-req: 0, 
[2025-07-28 01:47:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1097, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.70, #queue-req: 0, 
[2025-07-28 01:47:20 DP1 TP0] Decode batch. #running-req: 1, #token: 599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 01:47:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1137, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.65, #queue-req: 0, 
[2025-07-28 01:47:21 DP1 TP0] Decode batch. #running-req: 1, #token: 639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.36, #queue-req: 0, 
[2025-07-28 01:47:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1177, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.73, #queue-req: 0, 
[2025-07-28 01:47:21 DP1 TP0] Decode batch. #running-req: 1, #token: 679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.30, #queue-req: 0, 
[2025-07-28 01:47:21] INFO:     127.0.0.1:40664 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:47:21 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 165, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:47:21 DP0 TP0] Decode batch. #running-req: 1, #token: 275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 112.77, #queue-req: 0, 
[2025-07-28 01:47:21 DP1 TP0] Decode batch. #running-req: 1, #token: 719, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.37, #queue-req: 0, 
[2025-07-28 01:47:22 DP0 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 01:47:22 DP1 TP0] Decode batch. #running-req: 1, #token: 759, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 01:47:22 DP0 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.94, #queue-req: 0, 
[2025-07-28 01:47:22 DP1 TP0] Decode batch. #running-req: 1, #token: 799, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.30, #queue-req: 0, 
[2025-07-28 01:47:22 DP0 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.10, #queue-req: 0, 
[2025-07-28 01:47:22 DP1 TP0] Decode batch. #running-req: 1, #token: 839, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.23, #queue-req: 0, 
[2025-07-28 01:47:22 DP0 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.97, #queue-req: 0, 
[2025-07-28 01:47:23 DP1 TP0] Decode batch. #running-req: 1, #token: 879, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 01:47:23 DP0 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.96, #queue-req: 0, 
[2025-07-28 01:47:23 DP1 TP0] Decode batch. #running-req: 1, #token: 919, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:47:23 DP0 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.93, #queue-req: 0, 
[2025-07-28 01:47:23 DP1 TP0] Decode batch. #running-req: 1, #token: 959, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:47:23] INFO:     127.0.0.1:40680 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:47:23 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 170, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:47:23 DP0 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.10, #queue-req: 0, 
[2025-07-28 01:47:23 DP1 TP0] Decode batch. #running-req: 1, #token: 286, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.52, #queue-req: 0, 
[2025-07-28 01:47:24 DP0 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.05, #queue-req: 0, 
[2025-07-28 01:47:24 DP1 TP0] Decode batch. #running-req: 1, #token: 326, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 01:47:24 DP0 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.07, #queue-req: 0, 
[2025-07-28 01:47:24 DP1 TP0] Decode batch. #running-req: 1, #token: 366, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 01:47:24 DP0 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.08, #queue-req: 0, 
[2025-07-28 01:47:24 DP1 TP0] Decode batch. #running-req: 1, #token: 406, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.07, #queue-req: 0, 
[2025-07-28 01:47:25 DP0 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.94, #queue-req: 0, 
[2025-07-28 01:47:25 DP1 TP0] Decode batch. #running-req: 1, #token: 446, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.02, #queue-req: 0, 
[2025-07-28 01:47:25 DP0 TP0] Decode batch. #running-req: 1, #token: 755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.06, #queue-req: 0, 
[2025-07-28 01:47:25 DP1 TP0] Decode batch. #running-req: 1, #token: 486, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.05, #queue-req: 0, 
[2025-07-28 01:47:25 DP0 TP0] Decode batch. #running-req: 1, #token: 795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.05, #queue-req: 0, 
[2025-07-28 01:47:25 DP1 TP0] Decode batch. #running-req: 1, #token: 526, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.02, #queue-req: 0, 
[2025-07-28 01:47:25 DP0 TP0] Decode batch. #running-req: 1, #token: 835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.09, #queue-req: 0, 
[2025-07-28 01:47:25 DP1 TP0] Decode batch. #running-req: 1, #token: 566, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.07, #queue-req: 0, 
[2025-07-28 01:47:26 DP0 TP0] Decode batch. #running-req: 1, #token: 875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 01:47:26 DP1 TP0] Decode batch. #running-req: 1, #token: 606, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.09, #queue-req: 0, 
[2025-07-28 01:47:26 DP0 TP0] Decode batch. #running-req: 1, #token: 915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.14, #queue-req: 0, 
[2025-07-28 01:47:26 DP1 TP0] Decode batch. #running-req: 1, #token: 646, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 01:47:26 DP0 TP0] Decode batch. #running-req: 1, #token: 955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.12, #queue-req: 0, 
[2025-07-28 01:47:26 DP1 TP0] Decode batch. #running-req: 1, #token: 686, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.06, #queue-req: 0, 
[2025-07-28 01:47:27 DP0 TP0] Decode batch. #running-req: 1, #token: 995, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:47:27 DP1 TP0] Decode batch. #running-req: 1, #token: 726, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.07, #queue-req: 0, 
[2025-07-28 01:47:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.08, #queue-req: 0, 
[2025-07-28 01:47:27 DP1 TP0] Decode batch. #running-req: 1, #token: 766, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.93, #queue-req: 0, 
[2025-07-28 01:47:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1075, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.80, #queue-req: 0, 
[2025-07-28 01:47:27 DP1 TP0] Decode batch. #running-req: 1, #token: 806, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.96, #queue-req: 0, 
[2025-07-28 01:47:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.79, #queue-req: 0, 
[2025-07-28 01:47:28 DP1 TP0] Decode batch. #running-req: 1, #token: 846, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.01, #queue-req: 0, 
[2025-07-28 01:47:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1155, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.83, #queue-req: 0, 
[2025-07-28 01:47:28 DP1 TP0] Decode batch. #running-req: 1, #token: 886, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.04, #queue-req: 0, 
[2025-07-28 01:47:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.79, #queue-req: 0, 
[2025-07-28 01:47:28 DP1 TP0] Decode batch. #running-req: 1, #token: 926, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.96, #queue-req: 0, 
[2025-07-28 01:47:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.74, #queue-req: 0, 
[2025-07-28 01:47:28 DP1 TP0] Decode batch. #running-req: 1, #token: 966, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.04, #queue-req: 0, 
[2025-07-28 01:47:29] INFO:     127.0.0.1:40692 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:47:29 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 217, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:47:29 DP0 TP0] Decode batch. #running-req: 1, #token: 310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 116.06, #queue-req: 0, 
[2025-07-28 01:47:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1006, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.93, #queue-req: 0, 
[2025-07-28 01:47:29 DP0 TP0] Decode batch. #running-req: 1, #token: 350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.39, #queue-req: 0, 
[2025-07-28 01:47:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1046, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.96, #queue-req: 0, 
[2025-07-28 01:47:29 DP0 TP0] Decode batch. #running-req: 1, #token: 390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.31, #queue-req: 0, 
[2025-07-28 01:47:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1086, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.75, #queue-req: 0, 
[2025-07-28 01:47:30 DP0 TP0] Decode batch. #running-req: 1, #token: 430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 01:47:30 DP1 TP0] Decode batch. #running-req: 1, #token: 1126, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.70, #queue-req: 0, 
[2025-07-28 01:47:30 DP0 TP0] Decode batch. #running-req: 1, #token: 470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.05, #queue-req: 0, 
[2025-07-28 01:47:30] INFO:     127.0.0.1:46738 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:47:30 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 153, #cached-token: 89, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:47:30 DP1 TP0] Decode batch. #running-req: 1, #token: 244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 116.66, #queue-req: 0, 
[2025-07-28 01:47:30 DP0 TP0] Decode batch. #running-req: 1, #token: 510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.10, #queue-req: 0, 
[2025-07-28 01:47:30 DP1 TP0] Decode batch. #running-req: 1, #token: 284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.56, #queue-req: 0, 
[2025-07-28 01:47:30 DP0 TP0] Decode batch. #running-req: 1, #token: 550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 01:47:30 DP1 TP0] Decode batch. #running-req: 1, #token: 324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 01:47:31 DP0 TP0] Decode batch. #running-req: 1, #token: 590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.30, #queue-req: 0, 
[2025-07-28 01:47:31 DP1 TP0] Decode batch. #running-req: 1, #token: 364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.49, #queue-req: 0, 
[2025-07-28 01:47:31 DP0 TP0] Decode batch. #running-req: 1, #token: 630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.38, #queue-req: 0, 
[2025-07-28 01:47:31 DP1 TP0] Decode batch. #running-req: 1, #token: 404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.45, #queue-req: 0, 
[2025-07-28 01:47:31 DP0 TP0] Decode batch. #running-req: 1, #token: 670, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 01:47:31 DP1 TP0] Decode batch. #running-req: 1, #token: 444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 01:47:32 DP0 TP0] Decode batch. #running-req: 1, #token: 710, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 01:47:32 DP1 TP0] Decode batch. #running-req: 1, #token: 484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 01:47:32 DP0 TP0] Decode batch. #running-req: 1, #token: 750, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 01:47:32 DP1 TP0] Decode batch. #running-req: 1, #token: 524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 01:47:32 DP0 TP0] Decode batch. #running-req: 1, #token: 790, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 01:47:32 DP1 TP0] Decode batch. #running-req: 1, #token: 564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.39, #queue-req: 0, 
[2025-07-28 01:47:32 DP0 TP0] Decode batch. #running-req: 1, #token: 830, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 01:47:33 DP1 TP0] Decode batch. #running-req: 1, #token: 604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.38, #queue-req: 0, 
[2025-07-28 01:47:33 DP0 TP0] Decode batch. #running-req: 1, #token: 870, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:47:33 DP1 TP0] Decode batch. #running-req: 1, #token: 644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 01:47:33 DP0 TP0] Decode batch. #running-req: 1, #token: 910, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 01:47:33 DP1 TP0] Decode batch. #running-req: 1, #token: 684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.34, #queue-req: 0, 
[2025-07-28 01:47:33 DP0 TP0] Decode batch. #running-req: 1, #token: 950, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.17, #queue-req: 0, 
[2025-07-28 01:47:33 DP1 TP0] Decode batch. #running-req: 1, #token: 724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.34, #queue-req: 0, 
[2025-07-28 01:47:34 DP0 TP0] Decode batch. #running-req: 1, #token: 990, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 01:47:34 DP1 TP0] Decode batch. #running-req: 1, #token: 764, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 01:47:34 DP0 TP0] Decode batch. #running-req: 1, #token: 1030, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.14, #queue-req: 0, 
[2025-07-28 01:47:34 DP1 TP0] Decode batch. #running-req: 1, #token: 804, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.30, #queue-req: 0, 
[2025-07-28 01:47:34 DP0 TP0] Decode batch. #running-req: 1, #token: 1070, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.78, #queue-req: 0, 
[2025-07-28 01:47:34 DP1 TP0] Decode batch. #running-req: 1, #token: 844, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 01:47:34 DP0 TP0] Decode batch. #running-req: 1, #token: 1110, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.81, #queue-req: 0, 
[2025-07-28 01:47:35 DP1 TP0] Decode batch. #running-req: 1, #token: 884, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 01:47:35 DP0 TP0] Decode batch. #running-req: 1, #token: 1150, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.75, #queue-req: 0, 
[2025-07-28 01:47:35 DP1 TP0] Decode batch. #running-req: 1, #token: 924, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:47:35 DP0 TP0] Decode batch. #running-req: 1, #token: 1190, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.64, #queue-req: 0, 
[2025-07-28 01:47:35 DP1 TP0] Decode batch. #running-req: 1, #token: 964, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 01:47:35 DP0 TP0] Decode batch. #running-req: 1, #token: 1230, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.73, #queue-req: 0, 
[2025-07-28 01:47:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1004, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 01:47:36 DP0 TP0] Decode batch. #running-req: 1, #token: 1270, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.73, #queue-req: 0, 
[2025-07-28 01:47:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1044, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.04, #queue-req: 0, 
[2025-07-28 01:47:36 DP0 TP0] Decode batch. #running-req: 1, #token: 1310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.81, #queue-req: 0, 
[2025-07-28 01:47:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1084, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.89, #queue-req: 0, 
[2025-07-28 01:47:36 DP0 TP0] Decode batch. #running-req: 1, #token: 1350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.77, #queue-req: 0, 
[2025-07-28 01:47:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1124, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.78, #queue-req: 0, 
[2025-07-28 01:47:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.63, #queue-req: 0, 
[2025-07-28 01:47:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1164, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.86, #queue-req: 0, 
[2025-07-28 01:47:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.64, #queue-req: 0, 
[2025-07-28 01:47:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1204, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.92, #queue-req: 0, 
[2025-07-28 01:47:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.69, #queue-req: 0, 
[2025-07-28 01:47:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.91, #queue-req: 0, 
[2025-07-28 01:47:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.56, #queue-req: 0, 
[2025-07-28 01:47:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.76, #queue-req: 0, 
[2025-07-28 01:47:38] INFO:     127.0.0.1:46754 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:47:38 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 173, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:47:38 DP0 TP0] Decode batch. #running-req: 1, #token: 263, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.84, #queue-req: 0, 
[2025-07-28 01:47:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.87, #queue-req: 0, 
[2025-07-28 01:47:38 DP0 TP0] Decode batch. #running-req: 1, #token: 303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 01:47:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.84, #queue-req: 0, 
[2025-07-28 01:47:38 DP0 TP0] Decode batch. #running-req: 1, #token: 343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.45, #queue-req: 0, 
[2025-07-28 01:47:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.83, #queue-req: 0, 
[2025-07-28 01:47:39 DP0 TP0] Decode batch. #running-req: 1, #token: 383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 01:47:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.69, #queue-req: 0, 
[2025-07-28 01:47:39 DP0 TP0] Decode batch. #running-req: 1, #token: 423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 01:47:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.64, #queue-req: 0, 
[2025-07-28 01:47:39 DP0 TP0] Decode batch. #running-req: 1, #token: 463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 01:47:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.65, #queue-req: 0, 
[2025-07-28 01:47:39 DP0 TP0] Decode batch. #running-req: 1, #token: 503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.14, #queue-req: 0, 
[2025-07-28 01:47:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.70, #queue-req: 0, 
[2025-07-28 01:47:40 DP0 TP0] Decode batch. #running-req: 1, #token: 543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.08, #queue-req: 0, 
[2025-07-28 01:47:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.64, #queue-req: 0, 
[2025-07-28 01:47:40 DP0 TP0] Decode batch. #running-req: 1, #token: 583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 01:47:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.73, #queue-req: 0, 
[2025-07-28 01:47:40 DP0 TP0] Decode batch. #running-req: 1, #token: 623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.14, #queue-req: 0, 
[2025-07-28 01:47:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.54, #queue-req: 0, 
[2025-07-28 01:47:41 DP0 TP0] Decode batch. #running-req: 1, #token: 663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 01:47:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.80, #queue-req: 0, 
[2025-07-28 01:47:41 DP0 TP0] Decode batch. #running-req: 1, #token: 703, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 01:47:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1764, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.73, #queue-req: 0, 
[2025-07-28 01:47:41 DP0 TP0] Decode batch. #running-req: 1, #token: 743, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:47:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1804, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.66, #queue-req: 0, 
[2025-07-28 01:47:42 DP0 TP0] Decode batch. #running-req: 1, #token: 783, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.23, #queue-req: 0, 
[2025-07-28 01:47:42 DP1 TP0] Decode batch. #running-req: 1, #token: 1844, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.69, #queue-req: 0, 
[2025-07-28 01:47:42 DP0 TP0] Decode batch. #running-req: 1, #token: 823, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 01:47:42 DP1 TP0] Decode batch. #running-req: 1, #token: 1884, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.66, #queue-req: 0, 
[2025-07-28 01:47:42] INFO:     127.0.0.1:46762 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:47:42 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 92, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:47:42 DP0 TP0] Decode batch. #running-req: 1, #token: 863, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:47:42 DP1 TP0] Decode batch. #running-req: 1, #token: 189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.66, #queue-req: 0, 
[2025-07-28 01:47:42 DP0 TP0] Decode batch. #running-req: 1, #token: 903, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.17, #queue-req: 0, 
[2025-07-28 01:47:43 DP1 TP0] Decode batch. #running-req: 1, #token: 229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.58, #queue-req: 0, 
[2025-07-28 01:47:43 DP0 TP0] Decode batch. #running-req: 1, #token: 943, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 01:47:43 DP1 TP0] Decode batch. #running-req: 1, #token: 269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.52, #queue-req: 0, 
[2025-07-28 01:47:43 DP0 TP0] Decode batch. #running-req: 1, #token: 983, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 01:47:43 DP1 TP0] Decode batch. #running-req: 1, #token: 309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 01:47:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1023, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:47:43 DP1 TP0] Decode batch. #running-req: 1, #token: 349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.37, #queue-req: 0, 
[2025-07-28 01:47:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1063, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.84, #queue-req: 0, 
[2025-07-28 01:47:44 DP1 TP0] Decode batch. #running-req: 1, #token: 389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 01:47:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1103, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.84, #queue-req: 0, 
[2025-07-28 01:47:44 DP1 TP0] Decode batch. #running-req: 1, #token: 429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 01:47:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1143, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.80, #queue-req: 0, 
[2025-07-28 01:47:44 DP1 TP0] Decode batch. #running-req: 1, #token: 469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 01:47:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1183, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.77, #queue-req: 0, 
[2025-07-28 01:47:45 DP1 TP0] Decode batch. #running-req: 1, #token: 509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 01:47:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1223, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.80, #queue-req: 0, 
[2025-07-28 01:47:45 DP1 TP0] Decode batch. #running-req: 1, #token: 549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 01:47:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1263, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.78, #queue-req: 0, 
[2025-07-28 01:47:45 DP1 TP0] Decode batch. #running-req: 1, #token: 589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 01:47:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.77, #queue-req: 0, 
[2025-07-28 01:47:45 DP1 TP0] Decode batch. #running-req: 1, #token: 629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:47:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.80, #queue-req: 0, 
[2025-07-28 01:47:46 DP1 TP0] Decode batch. #running-req: 1, #token: 669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 01:47:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.78, #queue-req: 0, 
[2025-07-28 01:47:46 DP1 TP0] Decode batch. #running-req: 1, #token: 709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.24, #queue-req: 0, 
[2025-07-28 01:47:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.69, #queue-req: 0, 
[2025-07-28 01:47:46 DP1 TP0] Decode batch. #running-req: 1, #token: 749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 01:47:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.50, #queue-req: 0, 
[2025-07-28 01:47:47 DP1 TP0] Decode batch. #running-req: 1, #token: 789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.09, #queue-req: 0, 
[2025-07-28 01:47:47 DP0 TP0] Decode batch. #running-req: 1, #token: 1503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.55, #queue-req: 0, 
[2025-07-28 01:47:47 DP1 TP0] Decode batch. #running-req: 1, #token: 829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.17, #queue-req: 0, 
[2025-07-28 01:47:47 DP0 TP0] Decode batch. #running-req: 1, #token: 1543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.58, #queue-req: 0, 
[2025-07-28 01:47:47 DP1 TP0] Decode batch. #running-req: 1, #token: 869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.17, #queue-req: 0, 
[2025-07-28 01:47:47 DP0 TP0] Decode batch. #running-req: 1, #token: 1583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.64, #queue-req: 0, 
[2025-07-28 01:47:47 DP1 TP0] Decode batch. #running-req: 1, #token: 909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.13, #queue-req: 0, 
[2025-07-28 01:47:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.62, #queue-req: 0, 
[2025-07-28 01:47:48 DP1 TP0] Decode batch. #running-req: 1, #token: 949, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.09, #queue-req: 0, 
[2025-07-28 01:47:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.42, #queue-req: 0, 
[2025-07-28 01:47:48 DP1 TP0] Decode batch. #running-req: 1, #token: 989, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.07, #queue-req: 0, 
[2025-07-28 01:47:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1703, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.47, #queue-req: 0, 
[2025-07-28 01:47:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1029, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.06, #queue-req: 0, 
[2025-07-28 01:47:49 DP0 TP0] Decode batch. #running-req: 1, #token: 1743, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.49, #queue-req: 0, 
[2025-07-28 01:47:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1069, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.67, #queue-req: 0, 
[2025-07-28 01:47:49] INFO:     127.0.0.1:42186 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:47:49 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 113, #cached-token: 84, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:47:49 DP0 TP0] Decode batch. #running-req: 2, #token: 1910, token usage: 0.00, cuda graph: True, gen throughput (token/s): 157.20, #queue-req: 0, 
[2025-07-28 01:47:49 DP0 TP0] Decode batch. #running-req: 2, #token: 1990, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.32, #queue-req: 0, 
[2025-07-28 01:47:50 DP0 TP0] Decode batch. #running-req: 2, #token: 2070, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.86, #queue-req: 0, 
[2025-07-28 01:47:50 DP0 TP0] Decode batch. #running-req: 2, #token: 2150, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.23, #queue-req: 0, 
[2025-07-28 01:47:50 DP0 TP0] Decode batch. #running-req: 2, #token: 2230, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.79, #queue-req: 0, 
[2025-07-28 01:47:50 DP0 TP0] Decode batch. #running-req: 2, #token: 2310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.62, #queue-req: 0, 
[2025-07-28 01:47:51 DP0 TP0] Decode batch. #running-req: 2, #token: 2390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.58, #queue-req: 0, 
[2025-07-28 01:47:51 DP0 TP0] Decode batch. #running-req: 2, #token: 2470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.14, #queue-req: 0, 
[2025-07-28 01:47:51] INFO:     127.0.0.1:45150 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:47:51 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 148, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:47:51 DP0 TP0] Decode batch. #running-req: 1, #token: 531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 194.95, #queue-req: 0, 
[2025-07-28 01:47:52 DP1 TP0] Decode batch. #running-req: 1, #token: 263, token usage: 0.00, cuda graph: True, gen throughput (token/s): 13.70, #queue-req: 0, 
[2025-07-28 01:47:52 DP0 TP0] Decode batch. #running-req: 1, #token: 571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 01:47:52 DP1 TP0] Decode batch. #running-req: 1, #token: 303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.42, #queue-req: 0, 
[2025-07-28 01:47:52 DP0 TP0] Decode batch. #running-req: 1, #token: 611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 01:47:52 DP1 TP0] Decode batch. #running-req: 1, #token: 343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 01:47:52 DP0 TP0] Decode batch. #running-req: 1, #token: 651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 01:47:52 DP1 TP0] Decode batch. #running-req: 1, #token: 383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.36, #queue-req: 0, 
[2025-07-28 01:47:53 DP0 TP0] Decode batch. #running-req: 1, #token: 691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.30, #queue-req: 0, 
[2025-07-28 01:47:53 DP1 TP0] Decode batch. #running-req: 1, #token: 423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 01:47:53 DP0 TP0] Decode batch. #running-req: 1, #token: 731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.30, #queue-req: 0, 
[2025-07-28 01:47:53 DP1 TP0] Decode batch. #running-req: 1, #token: 463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.64, #queue-req: 0, 
[2025-07-28 01:47:53 DP0 TP0] Decode batch. #running-req: 1, #token: 771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.13, #queue-req: 0, 
[2025-07-28 01:47:53 DP1 TP0] Decode batch. #running-req: 1, #token: 503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.87, #queue-req: 0, 
[2025-07-28 01:47:53 DP0 TP0] Decode batch. #running-req: 1, #token: 811, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.17, #queue-req: 0, 
[2025-07-28 01:47:54 DP1 TP0] Decode batch. #running-req: 1, #token: 543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 01:47:54 DP0 TP0] Decode batch. #running-req: 1, #token: 851, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 01:47:54 DP1 TP0] Decode batch. #running-req: 1, #token: 583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 01:47:54 DP0 TP0] Decode batch. #running-req: 1, #token: 891, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 01:47:54 DP1 TP0] Decode batch. #running-req: 1, #token: 623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 01:47:54 DP0 TP0] Decode batch. #running-req: 1, #token: 931, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 01:47:54 DP1 TP0] Decode batch. #running-req: 1, #token: 663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 01:47:55 DP0 TP0] Decode batch. #running-req: 1, #token: 971, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.31, #queue-req: 0, 
[2025-07-28 01:47:55 DP1 TP0] Decode batch. #running-req: 1, #token: 703, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 01:47:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1011, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 01:47:55 DP1 TP0] Decode batch. #running-req: 1, #token: 743, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.13, #queue-req: 0, 
[2025-07-28 01:47:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1051, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.00, #queue-req: 0, 
[2025-07-28 01:47:55 DP1 TP0] Decode batch. #running-req: 1, #token: 783, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.08, #queue-req: 0, 
[2025-07-28 01:47:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1091, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.87, #queue-req: 0, 
[2025-07-28 01:47:56 DP1 TP0] Decode batch. #running-req: 1, #token: 823, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.08, #queue-req: 0, 
[2025-07-28 01:47:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1131, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.81, #queue-req: 0, 
[2025-07-28 01:47:56 DP1 TP0] Decode batch. #running-req: 1, #token: 863, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 01:47:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1171, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.92, #queue-req: 0, 
[2025-07-28 01:47:56 DP1 TP0] Decode batch. #running-req: 1, #token: 903, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 01:47:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1211, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.88, #queue-req: 0, 
[2025-07-28 01:47:57 DP1 TP0] Decode batch. #running-req: 1, #token: 943, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.13, #queue-req: 0, 
[2025-07-28 01:47:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.81, #queue-req: 0, 
[2025-07-28 01:47:57 DP1 TP0] Decode batch. #running-req: 1, #token: 983, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.08, #queue-req: 0, 
[2025-07-28 01:47:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.86, #queue-req: 0, 
[2025-07-28 01:47:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1023, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.00, #queue-req: 0, 
[2025-07-28 01:47:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.87, #queue-req: 0, 
[2025-07-28 01:47:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1063, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.66, #queue-req: 0, 
[2025-07-28 01:47:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.80, #queue-req: 0, 
[2025-07-28 01:47:58 DP1 TP0] Decode batch. #running-req: 1, #token: 1103, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.71, #queue-req: 0, 
[2025-07-28 01:47:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.79, #queue-req: 0, 
[2025-07-28 01:47:58 DP1 TP0] Decode batch. #running-req: 1, #token: 1143, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.74, #queue-req: 0, 
[2025-07-28 01:47:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.64, #queue-req: 0, 
[2025-07-28 01:47:58 DP1 TP0] Decode batch. #running-req: 1, #token: 1183, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.74, #queue-req: 0, 
[2025-07-28 01:47:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.71, #queue-req: 0, 
[2025-07-28 01:47:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1223, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.68, #queue-req: 0, 
[2025-07-28 01:47:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.65, #queue-req: 0, 
[2025-07-28 01:47:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1263, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.67, #queue-req: 0, 
[2025-07-28 01:47:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.73, #queue-req: 0, 
[2025-07-28 01:47:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.67, #queue-req: 0, 
[2025-07-28 01:47:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.72, #queue-req: 0, 
[2025-07-28 01:47:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.65, #queue-req: 0, 
[2025-07-28 01:48:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.74, #queue-req: 0, 
[2025-07-28 01:48:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.67, #queue-req: 0, 
[2025-07-28 01:48:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.74, #queue-req: 0, 
[2025-07-28 01:48:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.63, #queue-req: 0, 
[2025-07-28 01:48:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.76, #queue-req: 0, 
[2025-07-28 01:48:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.50, #queue-req: 0, 
[2025-07-28 01:48:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.74, #queue-req: 0, 
[2025-07-28 01:48:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.53, #queue-req: 0, 
[2025-07-28 01:48:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1811, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.72, #queue-req: 0, 
[2025-07-28 01:48:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.55, #queue-req: 0, 
[2025-07-28 01:48:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1851, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.68, #queue-req: 0, 
[2025-07-28 01:48:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.55, #queue-req: 0, 
[2025-07-28 01:48:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1891, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.72, #queue-req: 0, 
[2025-07-28 01:48:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.47, #queue-req: 0, 
[2025-07-28 01:48:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1931, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.49, #queue-req: 0, 
[2025-07-28 01:48:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.50, #queue-req: 0, 
[2025-07-28 01:48:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1971, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.66, #queue-req: 0, 
[2025-07-28 01:48:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1703, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.53, #queue-req: 0, 
[2025-07-28 01:48:02] INFO:     127.0.0.1:42214 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:48:02 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 146, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:48:02 DP0 TP0] Decode batch. #running-req: 2, #token: 2171, token usage: 0.00, cuda graph: True, gen throughput (token/s): 149.46, #queue-req: 0, 
[2025-07-28 01:48:03 DP0 TP0] Decode batch. #running-req: 2, #token: 2251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.87, #queue-req: 0, 
[2025-07-28 01:48:03 DP0 TP0] Decode batch. #running-req: 2, #token: 2331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.59, #queue-req: 0, 
[2025-07-28 01:48:03 DP0 TP0] Decode batch. #running-req: 2, #token: 2411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.74, #queue-req: 0, 
[2025-07-28 01:48:04 DP0 TP0] Decode batch. #running-req: 2, #token: 2491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.49, #queue-req: 0, 
[2025-07-28 01:48:04 DP0 TP0] Decode batch. #running-req: 2, #token: 2571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.42, #queue-req: 0, 
[2025-07-28 01:48:04 DP0 TP0] Decode batch. #running-req: 2, #token: 2651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.47, #queue-req: 0, 
[2025-07-28 01:48:04 DP0 TP0] Decode batch. #running-req: 2, #token: 2731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.55, #queue-req: 0, 
[2025-07-28 01:48:05 DP0 TP0] Decode batch. #running-req: 2, #token: 2811, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.59, #queue-req: 0, 
[2025-07-28 01:48:05 DP0 TP0] Decode batch. #running-req: 2, #token: 2891, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.62, #queue-req: 0, 
[2025-07-28 01:48:05 DP0 TP0] Decode batch. #running-req: 2, #token: 2971, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.63, #queue-req: 0, 
[2025-07-28 01:48:06 DP0 TP0] Decode batch. #running-req: 2, #token: 3051, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.44, #queue-req: 0, 
[2025-07-28 01:48:06 DP0 TP0] Decode batch. #running-req: 2, #token: 3131, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.43, #queue-req: 0, 
[2025-07-28 01:48:06 DP0 TP0] Decode batch. #running-req: 2, #token: 3211, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.34, #queue-req: 0, 
[2025-07-28 01:48:07 DP0 TP0] Decode batch. #running-req: 2, #token: 3291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.70, #queue-req: 0, 
[2025-07-28 01:48:07 DP0 TP0] Decode batch. #running-req: 2, #token: 3371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.23, #queue-req: 0, 
[2025-07-28 01:48:07 DP0 TP0] Decode batch. #running-req: 2, #token: 3451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.98, #queue-req: 0, 
[2025-07-28 01:48:08 DP0 TP0] Decode batch. #running-req: 2, #token: 3531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.97, #queue-req: 0, 
[2025-07-28 01:48:08 DP0 TP0] Decode batch. #running-req: 2, #token: 3611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.79, #queue-req: 0, 
[2025-07-28 01:48:08 DP0 TP0] Decode batch. #running-req: 2, #token: 3691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.78, #queue-req: 0, 
[2025-07-28 01:48:09 DP0 TP0] Decode batch. #running-req: 2, #token: 3771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.69, #queue-req: 0, 
[2025-07-28 01:48:09 DP0 TP0] Decode batch. #running-req: 2, #token: 3851, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.59, #queue-req: 0, 
[2025-07-28 01:48:09 DP0 TP0] Decode batch. #running-req: 2, #token: 3931, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.67, #queue-req: 0, 
[2025-07-28 01:48:09 DP0 TP0] Decode batch. #running-req: 2, #token: 4011, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.86, #queue-req: 0, 
[2025-07-28 01:48:10 DP0 TP0] Decode batch. #running-req: 2, #token: 4091, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.64, #queue-req: 0, 
[2025-07-28 01:48:10 DP0 TP0] Decode batch. #running-req: 2, #token: 4171, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.59, #queue-req: 0, 
[2025-07-28 01:48:10 DP0 TP0] Decode batch. #running-req: 2, #token: 4251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.42, #queue-req: 0, 
[2025-07-28 01:48:11 DP0 TP0] Decode batch. #running-req: 2, #token: 4331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.29, #queue-req: 0, 
[2025-07-28 01:48:11 DP0 TP0] Decode batch. #running-req: 2, #token: 4411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.20, #queue-req: 0, 
[2025-07-28 01:48:11 DP0 TP0] Decode batch. #running-req: 2, #token: 4491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.14, #queue-req: 0, 
[2025-07-28 01:48:12 DP0 TP0] Decode batch. #running-req: 2, #token: 4571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.07, #queue-req: 0, 
[2025-07-28 01:48:12 DP0 TP0] Decode batch. #running-req: 2, #token: 4651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.00, #queue-req: 0, 
[2025-07-28 01:48:12] INFO:     127.0.0.1:45552 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:48:12 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 158, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:48:12 DP0 TP0] Decode batch. #running-req: 1, #token: 3291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 219.50, #queue-req: 0, 
[2025-07-28 01:48:13 DP1 TP0] Decode batch. #running-req: 1, #token: 279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 3.79, #queue-req: 0, 
[2025-07-28 01:48:13 DP0 TP0] Decode batch. #running-req: 1, #token: 3331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.88, #queue-req: 0, 
[2025-07-28 01:48:13 DP1 TP0] Decode batch. #running-req: 1, #token: 319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.34, #queue-req: 0, 
[2025-07-28 01:48:13 DP0 TP0] Decode batch. #running-req: 1, #token: 3371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.85, #queue-req: 0, 
[2025-07-28 01:48:13 DP1 TP0] Decode batch. #running-req: 1, #token: 359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.14, #queue-req: 0, 
[2025-07-28 01:48:13 DP0 TP0] Decode batch. #running-req: 1, #token: 3411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.88, #queue-req: 0, 
[2025-07-28 01:48:13 DP1 TP0] Decode batch. #running-req: 1, #token: 399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.23, #queue-req: 0, 
[2025-07-28 01:48:14 DP0 TP0] Decode batch. #running-req: 1, #token: 3451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.91, #queue-req: 0, 
[2025-07-28 01:48:14 DP1 TP0] Decode batch. #running-req: 1, #token: 439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.06, #queue-req: 0, 
[2025-07-28 01:48:14 DP0 TP0] Decode batch. #running-req: 1, #token: 3491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.79, #queue-req: 0, 
[2025-07-28 01:48:14 DP1 TP0] Decode batch. #running-req: 1, #token: 479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.89, #queue-req: 0, 
[2025-07-28 01:48:14 DP0 TP0] Decode batch. #running-req: 1, #token: 3531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.74, #queue-req: 0, 
[2025-07-28 01:48:14 DP1 TP0] Decode batch. #running-req: 1, #token: 519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.96, #queue-req: 0, 
[2025-07-28 01:48:14 DP0 TP0] Decode batch. #running-req: 1, #token: 3571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.72, #queue-req: 0, 
[2025-07-28 01:48:15 DP1 TP0] Decode batch. #running-req: 1, #token: 559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.10, #queue-req: 0, 
[2025-07-28 01:48:15 DP0 TP0] Decode batch. #running-req: 1, #token: 3611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.76, #queue-req: 0, 
[2025-07-28 01:48:15 DP1 TP0] Decode batch. #running-req: 1, #token: 599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.07, #queue-req: 0, 
[2025-07-28 01:48:15 DP0 TP0] Decode batch. #running-req: 1, #token: 3651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.74, #queue-req: 0, 
[2025-07-28 01:48:15 DP1 TP0] Decode batch. #running-req: 1, #token: 639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.08, #queue-req: 0, 
[2025-07-28 01:48:15 DP0 TP0] Decode batch. #running-req: 1, #token: 3691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.81, #queue-req: 0, 
[2025-07-28 01:48:16 DP1 TP0] Decode batch. #running-req: 1, #token: 679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.03, #queue-req: 0, 
[2025-07-28 01:48:16 DP0 TP0] Decode batch. #running-req: 1, #token: 3731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.83, #queue-req: 0, 
[2025-07-28 01:48:16 DP1 TP0] Decode batch. #running-req: 1, #token: 719, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.99, #queue-req: 0, 
[2025-07-28 01:48:16 DP0 TP0] Decode batch. #running-req: 1, #token: 3771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.79, #queue-req: 0, 
[2025-07-28 01:48:16 DP1 TP0] Decode batch. #running-req: 1, #token: 759, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.05, #queue-req: 0, 
[2025-07-28 01:48:16 DP0 TP0] Decode batch. #running-req: 1, #token: 3811, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.79, #queue-req: 0, 
[2025-07-28 01:48:16 DP1 TP0] Decode batch. #running-req: 1, #token: 799, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.03, #queue-req: 0, 
[2025-07-28 01:48:16 DP0 TP0] Decode batch. #running-req: 1, #token: 3851, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.77, #queue-req: 0, 
[2025-07-28 01:48:17 DP1 TP0] Decode batch. #running-req: 1, #token: 839, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.01, #queue-req: 0, 
[2025-07-28 01:48:17 DP0 TP0] Decode batch. #running-req: 1, #token: 3891, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.74, #queue-req: 0, 
[2025-07-28 01:48:17 DP1 TP0] Decode batch. #running-req: 1, #token: 879, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.02, #queue-req: 0, 
[2025-07-28 01:48:17 DP0 TP0] Decode batch. #running-req: 1, #token: 3931, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.76, #queue-req: 0, 
[2025-07-28 01:48:17 DP1 TP0] Decode batch. #running-req: 1, #token: 919, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.14, #queue-req: 0, 
[2025-07-28 01:48:17 DP0 TP0] Decode batch. #running-req: 1, #token: 3971, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.74, #queue-req: 0, 
[2025-07-28 01:48:18] INFO:     127.0.0.1:42200 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:48:18 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 112, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:48:18 DP1 TP0] Decode batch. #running-req: 1, #token: 959, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.05, #queue-req: 0, 
[2025-07-28 01:48:18 DP0 TP0] Decode batch. #running-req: 1, #token: 213, token usage: 0.00, cuda graph: True, gen throughput (token/s): 115.47, #queue-req: 0, 
[2025-07-28 01:48:18 DP1 TP0] Decode batch. #running-req: 1, #token: 999, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 01:48:18 DP0 TP0] Decode batch. #running-req: 1, #token: 253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.51, #queue-req: 0, 
[2025-07-28 01:48:18 DP1 TP0] Decode batch. #running-req: 1, #token: 1039, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.00, #queue-req: 0, 
[2025-07-28 01:48:18 DP0 TP0] Decode batch. #running-req: 1, #token: 293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.46, #queue-req: 0, 
[2025-07-28 01:48:18 DP1 TP0] Decode batch. #running-req: 1, #token: 1079, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.77, #queue-req: 0, 
[2025-07-28 01:48:19 DP0 TP0] Decode batch. #running-req: 1, #token: 333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.45, #queue-req: 0, 
[2025-07-28 01:48:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1119, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.70, #queue-req: 0, 
[2025-07-28 01:48:19 DP0 TP0] Decode batch. #running-req: 1, #token: 373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.49, #queue-req: 0, 
[2025-07-28 01:48:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1159, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.78, #queue-req: 0, 
[2025-07-28 01:48:19 DP0 TP0] Decode batch. #running-req: 1, #token: 413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 01:48:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1199, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.75, #queue-req: 0, 
[2025-07-28 01:48:19 DP0 TP0] Decode batch. #running-req: 1, #token: 453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 01:48:20 DP1 TP0] Decode batch. #running-req: 1, #token: 1239, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.71, #queue-req: 0, 
[2025-07-28 01:48:20 DP0 TP0] Decode batch. #running-req: 1, #token: 493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 01:48:20 DP1 TP0] Decode batch. #running-req: 1, #token: 1279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.68, #queue-req: 0, 
[2025-07-28 01:48:20 DP0 TP0] Decode batch. #running-req: 1, #token: 533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 01:48:20 DP1 TP0] Decode batch. #running-req: 1, #token: 1319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.73, #queue-req: 0, 
[2025-07-28 01:48:20 DP0 TP0] Decode batch. #running-req: 1, #token: 573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 01:48:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.65, #queue-req: 0, 
[2025-07-28 01:48:21 DP0 TP0] Decode batch. #running-req: 1, #token: 613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.40, #queue-req: 0, 
[2025-07-28 01:48:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.67, #queue-req: 0, 
[2025-07-28 01:48:21 DP0 TP0] Decode batch. #running-req: 1, #token: 653, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.38, #queue-req: 0, 
[2025-07-28 01:48:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.53, #queue-req: 0, 
[2025-07-28 01:48:21 DP0 TP0] Decode batch. #running-req: 1, #token: 693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 01:48:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.48, #queue-req: 0, 
[2025-07-28 01:48:21 DP0 TP0] Decode batch. #running-req: 1, #token: 733, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 01:48:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.51, #queue-req: 0, 
[2025-07-28 01:48:22 DP0 TP0] Decode batch. #running-req: 1, #token: 773, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 01:48:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.53, #queue-req: 0, 
[2025-07-28 01:48:22 DP0 TP0] Decode batch. #running-req: 1, #token: 813, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.06, #queue-req: 0, 
[2025-07-28 01:48:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.50, #queue-req: 0, 
[2025-07-28 01:48:22 DP0 TP0] Decode batch. #running-req: 1, #token: 853, token usage: 0.00, cuda graph: True, gen throughput (token/s): 132.23, #queue-req: 0, 
[2025-07-28 01:48:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.52, #queue-req: 0, 
[2025-07-28 01:48:23 DP0 TP0] Decode batch. #running-req: 1, #token: 893, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.91, #queue-req: 0, 
[2025-07-28 01:48:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.58, #queue-req: 0, 
[2025-07-28 01:48:23 DP0 TP0] Decode batch. #running-req: 1, #token: 933, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 01:48:23] INFO:     127.0.0.1:55960 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:48:23 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 78, #cached-token: 84, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:48:23 DP1 TP0] Decode batch. #running-req: 2, #token: 1820, token usage: 0.00, cuda graph: True, gen throughput (token/s): 181.63, #queue-req: 0, 
[2025-07-28 01:48:23 DP1 TP0] Decode batch. #running-req: 2, #token: 1900, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.41, #queue-req: 0, 
[2025-07-28 01:48:24 DP1 TP0] Decode batch. #running-req: 2, #token: 1980, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.23, #queue-req: 0, 
[2025-07-28 01:48:24 DP1 TP0] Decode batch. #running-req: 2, #token: 2060, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.85, #queue-req: 0, 
[2025-07-28 01:48:24 DP1 TP0] Decode batch. #running-req: 2, #token: 2140, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.17, #queue-req: 0, 
[2025-07-28 01:48:25 DP1 TP0] Decode batch. #running-req: 2, #token: 2220, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.82, #queue-req: 0, 
[2025-07-28 01:48:25 DP1 TP0] Decode batch. #running-req: 2, #token: 2300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.04, #queue-req: 0, 
[2025-07-28 01:48:25 DP1 TP0] Decode batch. #running-req: 2, #token: 2380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.92, #queue-req: 0, 
[2025-07-28 01:48:26 DP1 TP0] Decode batch. #running-req: 2, #token: 2460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.78, #queue-req: 0, 
[2025-07-28 01:48:26 DP1 TP0] Decode batch. #running-req: 2, #token: 2540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.34, #queue-req: 0, 
[2025-07-28 01:48:26 DP1 TP0] Decode batch. #running-req: 2, #token: 2620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.23, #queue-req: 0, 
[2025-07-28 01:48:27 DP1 TP0] Decode batch. #running-req: 2, #token: 2700, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.10, #queue-req: 0, 
[2025-07-28 01:48:27 DP1 TP0] Decode batch. #running-req: 2, #token: 2780, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.21, #queue-req: 0, 
[2025-07-28 01:48:27 DP1 TP0] Decode batch. #running-req: 2, #token: 2860, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.21, #queue-req: 0, 
[2025-07-28 01:48:28 DP1 TP0] Decode batch. #running-req: 2, #token: 2940, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.20, #queue-req: 0, 
[2025-07-28 01:48:28 DP1 TP0] Decode batch. #running-req: 2, #token: 3020, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.14, #queue-req: 0, 
[2025-07-28 01:48:28 DP1 TP0] Decode batch. #running-req: 2, #token: 3100, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.23, #queue-req: 0, 
[2025-07-28 01:48:29 DP1 TP0] Decode batch. #running-req: 2, #token: 3180, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.89, #queue-req: 0, 
[2025-07-28 01:48:29 DP1 TP0] Decode batch. #running-req: 2, #token: 3260, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.94, #queue-req: 0, 
[2025-07-28 01:48:29 DP1 TP0] Decode batch. #running-req: 2, #token: 3340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.52, #queue-req: 0, 
[2025-07-28 01:48:29 DP1 TP0] Decode batch. #running-req: 2, #token: 3420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.66, #queue-req: 0, 
[2025-07-28 01:48:30 DP1 TP0] Decode batch. #running-req: 2, #token: 3500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.67, #queue-req: 0, 
[2025-07-28 01:48:30 DP1 TP0] Decode batch. #running-req: 2, #token: 3580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.43, #queue-req: 0, 
[2025-07-28 01:48:30 DP1 TP0] Decode batch. #running-req: 2, #token: 3660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.63, #queue-req: 0, 
[2025-07-28 01:48:31 DP1 TP0] Decode batch. #running-req: 2, #token: 3740, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.90, #queue-req: 0, 
[2025-07-28 01:48:31 DP1 TP0] Decode batch. #running-req: 2, #token: 3820, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.55, #queue-req: 0, 
[2025-07-28 01:48:31 DP1 TP0] Decode batch. #running-req: 2, #token: 3900, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.12, #queue-req: 0, 
[2025-07-28 01:48:32 DP1 TP0] Decode batch. #running-req: 2, #token: 3980, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.93, #queue-req: 0, 
[2025-07-28 01:48:32 DP1 TP0] Decode batch. #running-req: 2, #token: 4060, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.90, #queue-req: 0, 
[2025-07-28 01:48:32 DP1 TP0] Decode batch. #running-req: 2, #token: 4140, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.82, #queue-req: 0, 
[2025-07-28 01:48:33 DP1 TP0] Decode batch. #running-req: 2, #token: 4220, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.00, #queue-req: 0, 
[2025-07-28 01:48:33 DP1 TP0] Decode batch. #running-req: 2, #token: 4300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.68, #queue-req: 0, 
[2025-07-28 01:48:33] INFO:     127.0.0.1:55946 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:48:33 DP1 TP0] Decode batch. #running-req: 1, #token: 1465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 250.78, #queue-req: 0, 
[2025-07-28 01:48:33 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 134, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:48:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.50, #queue-req: 0, 
[2025-07-28 01:48:34 DP0 TP0] Decode batch. #running-req: 1, #token: 256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 3.73, #queue-req: 0, 
[2025-07-28 01:48:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.57, #queue-req: 0, 
[2025-07-28 01:48:34 DP0 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 01:48:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.55, #queue-req: 0, 
[2025-07-28 01:48:34 DP0 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.24, #queue-req: 0, 
[2025-07-28 01:48:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.57, #queue-req: 0, 
[2025-07-28 01:48:35 DP0 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.36, #queue-req: 0, 
[2025-07-28 01:48:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.64, #queue-req: 0, 
[2025-07-28 01:48:35 DP0 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 01:48:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1705, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.64, #queue-req: 0, 
[2025-07-28 01:48:35 DP0 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:48:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1745, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.60, #queue-req: 0, 
[2025-07-28 01:48:35 DP0 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.07, #queue-req: 0, 
[2025-07-28 01:48:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1785, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.68, #queue-req: 0, 
[2025-07-28 01:48:36 DP0 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.31, #queue-req: 0, 
[2025-07-28 01:48:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1825, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.63, #queue-req: 0, 
[2025-07-28 01:48:36 DP0 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 01:48:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1865, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.54, #queue-req: 0, 
[2025-07-28 01:48:36 DP0 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 01:48:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1905, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.50, #queue-req: 0, 
[2025-07-28 01:48:37 DP0 TP0] Decode batch. #running-req: 1, #token: 656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.30, #queue-req: 0, 
[2025-07-28 01:48:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1945, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.60, #queue-req: 0, 
[2025-07-28 01:48:37 DP0 TP0] Decode batch. #running-req: 1, #token: 696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:48:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1985, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.60, #queue-req: 0, 
[2025-07-28 01:48:37 DP0 TP0] Decode batch. #running-req: 1, #token: 736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:48:37 DP1 TP0] Decode batch. #running-req: 1, #token: 2025, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.59, #queue-req: 0, 
[2025-07-28 01:48:37 DP0 TP0] Decode batch. #running-req: 1, #token: 776, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 01:48:38 DP1 TP0] Decode batch. #running-req: 1, #token: 2065, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.53, #queue-req: 0, 
[2025-07-28 01:48:38 DP0 TP0] Decode batch. #running-req: 1, #token: 816, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 01:48:38 DP1 TP0] Decode batch. #running-req: 1, #token: 2105, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.33, #queue-req: 0, 
[2025-07-28 01:48:38 DP0 TP0] Decode batch. #running-req: 1, #token: 856, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 01:48:38] INFO:     127.0.0.1:56170 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:48:38 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 135, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:48:38 DP1 TP0] Decode batch. #running-req: 2, #token: 2306, token usage: 0.00, cuda graph: True, gen throughput (token/s): 180.03, #queue-req: 0, 
[2025-07-28 01:48:39 DP1 TP0] Decode batch. #running-req: 2, #token: 2386, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.96, #queue-req: 0, 
[2025-07-28 01:48:39 DP1 TP0] Decode batch. #running-req: 2, #token: 2466, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.02, #queue-req: 0, 
[2025-07-28 01:48:39 DP1 TP0] Decode batch. #running-req: 2, #token: 2546, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.56, #queue-req: 0, 
[2025-07-28 01:48:40 DP1 TP0] Decode batch. #running-req: 2, #token: 2626, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.62, #queue-req: 0, 
[2025-07-28 01:48:40 DP1 TP0] Decode batch. #running-req: 2, #token: 2706, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.67, #queue-req: 0, 
[2025-07-28 01:48:40 DP1 TP0] Decode batch. #running-req: 2, #token: 2786, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.63, #queue-req: 0, 
[2025-07-28 01:48:40 DP1 TP0] Decode batch. #running-req: 2, #token: 2866, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.46, #queue-req: 0, 
[2025-07-28 01:48:41 DP1 TP0] Decode batch. #running-req: 2, #token: 2946, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.41, #queue-req: 0, 
[2025-07-28 01:48:41 DP1 TP0] Decode batch. #running-req: 2, #token: 3026, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.49, #queue-req: 0, 
[2025-07-28 01:48:41 DP1 TP0] Decode batch. #running-req: 2, #token: 3106, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.52, #queue-req: 0, 
[2025-07-28 01:48:42 DP1 TP0] Decode batch. #running-req: 2, #token: 3186, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.33, #queue-req: 0, 
[2025-07-28 01:48:42 DP1 TP0] Decode batch. #running-req: 2, #token: 3266, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.44, #queue-req: 0, 
[2025-07-28 01:48:42 DP1 TP0] Decode batch. #running-req: 2, #token: 3346, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.92, #queue-req: 0, 
[2025-07-28 01:48:43 DP1 TP0] Decode batch. #running-req: 2, #token: 3426, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.31, #queue-req: 0, 
[2025-07-28 01:48:43 DP1 TP0] Decode batch. #running-req: 2, #token: 3506, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.20, #queue-req: 0, 
[2025-07-28 01:48:43 DP1 TP0] Decode batch. #running-req: 2, #token: 3586, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.14, #queue-req: 0, 
[2025-07-28 01:48:44 DP1 TP0] Decode batch. #running-req: 2, #token: 3666, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.12, #queue-req: 0, 
[2025-07-28 01:48:44 DP1 TP0] Decode batch. #running-req: 2, #token: 3746, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.11, #queue-req: 0, 
[2025-07-28 01:48:44] INFO:     127.0.0.1:56172 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:48:44 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 83, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:48:44 DP1 TP0] Decode batch. #running-req: 1, #token: 2905, token usage: 0.00, cuda graph: True, gen throughput (token/s): 188.14, #queue-req: 0, 
[2025-07-28 01:48:44 DP0 TP0] Decode batch. #running-req: 1, #token: 204, token usage: 0.00, cuda graph: True, gen throughput (token/s): 6.22, #queue-req: 0, 
[2025-07-28 01:48:45 DP1 TP0] Decode batch. #running-req: 1, #token: 2945, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.97, #queue-req: 0, 
[2025-07-28 01:48:45 DP0 TP0] Decode batch. #running-req: 1, #token: 244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.44, #queue-req: 0, 
[2025-07-28 01:48:45 DP1 TP0] Decode batch. #running-req: 1, #token: 2985, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.02, #queue-req: 0, 
[2025-07-28 01:48:45 DP0 TP0] Decode batch. #running-req: 1, #token: 284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.44, #queue-req: 0, 
[2025-07-28 01:48:45 DP1 TP0] Decode batch. #running-req: 1, #token: 3025, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.02, #queue-req: 0, 
[2025-07-28 01:48:45] INFO:     127.0.0.1:45370 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:48:45 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 155, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:48:45 DP0 TP0] Decode batch. #running-req: 1, #token: 324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 01:48:45 DP1 TP0] Decode batch. #running-req: 1, #token: 274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 116.93, #queue-req: 0, 
[2025-07-28 01:48:46 DP0 TP0] Decode batch. #running-req: 1, #token: 364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.43, #queue-req: 0, 
[2025-07-28 01:48:46 DP1 TP0] Decode batch. #running-req: 1, #token: 314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.47, #queue-req: 0, 
[2025-07-28 01:48:46 DP0 TP0] Decode batch. #running-req: 1, #token: 404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 01:48:46 DP1 TP0] Decode batch. #running-req: 1, #token: 354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.39, #queue-req: 0, 
[2025-07-28 01:48:46 DP0 TP0] Decode batch. #running-req: 1, #token: 444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 01:48:46 DP1 TP0] Decode batch. #running-req: 1, #token: 394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.37, #queue-req: 0, 
[2025-07-28 01:48:46 DP0 TP0] Decode batch. #running-req: 1, #token: 484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.17, #queue-req: 0, 
[2025-07-28 01:48:47 DP1 TP0] Decode batch. #running-req: 1, #token: 434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.31, #queue-req: 0, 
[2025-07-28 01:48:47 DP0 TP0] Decode batch. #running-req: 1, #token: 524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.37, #queue-req: 0, 
[2025-07-28 01:48:47 DP1 TP0] Decode batch. #running-req: 1, #token: 474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 01:48:47 DP0 TP0] Decode batch. #running-req: 1, #token: 564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 01:48:47 DP1 TP0] Decode batch. #running-req: 1, #token: 514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.99, #queue-req: 0, 
[2025-07-28 01:48:47 DP0 TP0] Decode batch. #running-req: 1, #token: 604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.38, #queue-req: 0, 
[2025-07-28 01:48:48 DP1 TP0] Decode batch. #running-req: 1, #token: 554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 01:48:48 DP0 TP0] Decode batch. #running-req: 1, #token: 644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.42, #queue-req: 0, 
[2025-07-28 01:48:48 DP1 TP0] Decode batch. #running-req: 1, #token: 594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.38, #queue-req: 0, 
[2025-07-28 01:48:48 DP0 TP0] Decode batch. #running-req: 1, #token: 684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.30, #queue-req: 0, 
[2025-07-28 01:48:48 DP1 TP0] Decode batch. #running-req: 1, #token: 634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.34, #queue-req: 0, 
[2025-07-28 01:48:48 DP0 TP0] Decode batch. #running-req: 1, #token: 724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.34, #queue-req: 0, 
[2025-07-28 01:48:48 DP1 TP0] Decode batch. #running-req: 1, #token: 674, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 01:48:49 DP0 TP0] Decode batch. #running-req: 1, #token: 764, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 01:48:49 DP1 TP0] Decode batch. #running-req: 1, #token: 714, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:48:49 DP0 TP0] Decode batch. #running-req: 1, #token: 804, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 01:48:49 DP1 TP0] Decode batch. #running-req: 1, #token: 754, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 01:48:49 DP0 TP0] Decode batch. #running-req: 1, #token: 844, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.30, #queue-req: 0, 
[2025-07-28 01:48:49 DP1 TP0] Decode batch. #running-req: 1, #token: 794, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.23, #queue-req: 0, 
[2025-07-28 01:48:49 DP0 TP0] Decode batch. #running-req: 1, #token: 884, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.30, #queue-req: 0, 
[2025-07-28 01:48:50 DP1 TP0] Decode batch. #running-req: 1, #token: 834, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.24, #queue-req: 0, 
[2025-07-28 01:48:50 DP0 TP0] Decode batch. #running-req: 1, #token: 924, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:48:50 DP1 TP0] Decode batch. #running-req: 1, #token: 874, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.10, #queue-req: 0, 
[2025-07-28 01:48:50 DP0 TP0] Decode batch. #running-req: 1, #token: 964, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 01:48:50 DP1 TP0] Decode batch. #running-req: 1, #token: 914, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 01:48:50 DP0 TP0] Decode batch. #running-req: 1, #token: 1004, token usage: 0.00, cuda graph: True, gen throughput (token/s): 134.80, #queue-req: 0, 
[2025-07-28 01:48:50 DP1 TP0] Decode batch. #running-req: 1, #token: 954, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:48:51 DP0 TP0] Decode batch. #running-req: 1, #token: 1044, token usage: 0.00, cuda graph: True, gen throughput (token/s): 134.42, #queue-req: 0, 
[2025-07-28 01:48:51 DP1 TP0] Decode batch. #running-req: 1, #token: 994, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.04, #queue-req: 0, 
[2025-07-28 01:48:51 DP0 TP0] Decode batch. #running-req: 1, #token: 1084, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.90, #queue-req: 0, 
[2025-07-28 01:48:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1034, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 01:48:51 DP0 TP0] Decode batch. #running-req: 1, #token: 1124, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.91, #queue-req: 0, 
[2025-07-28 01:48:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1074, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.87, #queue-req: 0, 
[2025-07-28 01:48:51 DP0 TP0] Decode batch. #running-req: 1, #token: 1164, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.80, #queue-req: 0, 
[2025-07-28 01:48:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1114, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.87, #queue-req: 0, 
[2025-07-28 01:48:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1204, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.85, #queue-req: 0, 
[2025-07-28 01:48:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1154, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.81, #queue-req: 0, 
[2025-07-28 01:48:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.88, #queue-req: 0, 
[2025-07-28 01:48:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1194, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.84, #queue-req: 0, 
[2025-07-28 01:48:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.88, #queue-req: 0, 
[2025-07-28 01:48:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1234, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.86, #queue-req: 0, 
[2025-07-28 01:48:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.81, #queue-req: 0, 
[2025-07-28 01:48:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.80, #queue-req: 0, 
[2025-07-28 01:48:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.71, #queue-req: 0, 
[2025-07-28 01:48:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.80, #queue-req: 0, 
[2025-07-28 01:48:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.76, #queue-req: 0, 
[2025-07-28 01:48:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.78, #queue-req: 0, 
[2025-07-28 01:48:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.58, #queue-req: 0, 
[2025-07-28 01:48:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.73, #queue-req: 0, 
[2025-07-28 01:48:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.66, #queue-req: 0, 
[2025-07-28 01:48:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.69, #queue-req: 0, 
[2025-07-28 01:48:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.65, #queue-req: 0, 
[2025-07-28 01:48:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.58, #queue-req: 0, 
[2025-07-28 01:48:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.70, #queue-req: 0, 
[2025-07-28 01:48:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.60, #queue-req: 0, 
[2025-07-28 01:48:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.69, #queue-req: 0, 
[2025-07-28 01:48:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.59, #queue-req: 0, 
[2025-07-28 01:48:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.68, #queue-req: 0, 
[2025-07-28 01:48:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.73, #queue-req: 0, 
[2025-07-28 01:48:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.62, #queue-req: 0, 
[2025-07-28 01:48:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.69, #queue-req: 0, 
[2025-07-28 01:48:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.73, #queue-req: 0, 
[2025-07-28 01:48:56] INFO:     127.0.0.1:43724 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:48:56 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 148, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:48:56 DP0 TP0] Decode batch. #running-req: 2, #token: 1952, token usage: 0.00, cuda graph: True, gen throughput (token/s): 218.67, #queue-req: 0, 
[2025-07-28 01:48:56 DP0 TP0] Decode batch. #running-req: 2, #token: 2032, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.27, #queue-req: 0, 
[2025-07-28 01:48:57 DP0 TP0] Decode batch. #running-req: 2, #token: 2112, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.25, #queue-req: 0, 
[2025-07-28 01:48:57 DP0 TP0] Decode batch. #running-req: 2, #token: 2192, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.27, #queue-req: 0, 
[2025-07-28 01:48:57 DP0 TP0] Decode batch. #running-req: 2, #token: 2272, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.04, #queue-req: 0, 
[2025-07-28 01:48:57 DP0 TP0] Decode batch. #running-req: 2, #token: 2352, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.75, #queue-req: 0, 
[2025-07-28 01:48:58 DP0 TP0] Decode batch. #running-req: 2, #token: 2432, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.18, #queue-req: 0, 
[2025-07-28 01:48:58 DP0 TP0] Decode batch. #running-req: 2, #token: 2512, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.67, #queue-req: 0, 
[2025-07-28 01:48:58 DP0 TP0] Decode batch. #running-req: 2, #token: 2592, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.21, #queue-req: 0, 
[2025-07-28 01:48:59 DP0 TP0] Decode batch. #running-req: 2, #token: 2672, token usage: 0.00, cuda graph: True, gen throughput (token/s): 252.93, #queue-req: 0, 
[2025-07-28 01:48:59 DP0 TP0] Decode batch. #running-req: 2, #token: 2752, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.05, #queue-req: 0, 
[2025-07-28 01:48:59 DP0 TP0] Decode batch. #running-req: 2, #token: 2832, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.83, #queue-req: 0, 
[2025-07-28 01:49:00 DP0 TP0] Decode batch. #running-req: 2, #token: 2912, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.19, #queue-req: 0, 
[2025-07-28 01:49:00 DP0 TP0] Decode batch. #running-req: 2, #token: 2992, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.20, #queue-req: 0, 
[2025-07-28 01:49:00 DP0 TP0] Decode batch. #running-req: 2, #token: 3072, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.21, #queue-req: 0, 
[2025-07-28 01:49:01 DP0 TP0] Decode batch. #running-req: 2, #token: 3152, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.14, #queue-req: 0, 
[2025-07-28 01:49:01 DP0 TP0] Decode batch. #running-req: 2, #token: 3232, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.20, #queue-req: 0, 
[2025-07-28 01:49:01 DP0 TP0] Decode batch. #running-req: 2, #token: 3312, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.11, #queue-req: 0, 
[2025-07-28 01:49:02 DP0 TP0] Decode batch. #running-req: 2, #token: 3392, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.00, #queue-req: 0, 
[2025-07-28 01:49:02] INFO:     127.0.0.1:35812 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:49:02 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 109, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:49:02 DP0 TP0] Decode batch. #running-req: 1, #token: 2524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.14, #queue-req: 0, 
[2025-07-28 01:49:02 DP1 TP0] Decode batch. #running-req: 1, #token: 211, token usage: 0.00, cuda graph: True, gen throughput (token/s): 5.98, #queue-req: 0, 
[2025-07-28 01:49:02 DP0 TP0] Decode batch. #running-req: 1, #token: 2564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.00, #queue-req: 0, 
[2025-07-28 01:49:02 DP1 TP0] Decode batch. #running-req: 1, #token: 251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 133.87, #queue-req: 0, 
[2025-07-28 01:49:02 DP0 TP0] Decode batch. #running-req: 1, #token: 2604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.98, #queue-req: 0, 
[2025-07-28 01:49:03 DP1 TP0] Decode batch. #running-req: 1, #token: 291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.56, #queue-req: 0, 
[2025-07-28 01:49:03 DP0 TP0] Decode batch. #running-req: 1, #token: 2644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.06, #queue-req: 0, 
[2025-07-28 01:49:03] INFO:     127.0.0.1:43714 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:49:03 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 93, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:49:03 DP1 TP0] Decode batch. #running-req: 1, #token: 331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.46, #queue-req: 0, 
[2025-07-28 01:49:03 DP0 TP0] Decode batch. #running-req: 1, #token: 192, token usage: 0.00, cuda graph: True, gen throughput (token/s): 119.14, #queue-req: 0, 
[2025-07-28 01:49:03 DP1 TP0] Decode batch. #running-req: 1, #token: 371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.43, #queue-req: 0, 
[2025-07-28 01:49:03 DP0 TP0] Decode batch. #running-req: 1, #token: 232, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.52, #queue-req: 0, 
[2025-07-28 01:49:04 DP1 TP0] Decode batch. #running-req: 1, #token: 411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.37, #queue-req: 0, 
[2025-07-28 01:49:04 DP0 TP0] Decode batch. #running-req: 1, #token: 272, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.50, #queue-req: 0, 
[2025-07-28 01:49:04 DP1 TP0] Decode batch. #running-req: 1, #token: 451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.31, #queue-req: 0, 
[2025-07-28 01:49:04 DP0 TP0] Decode batch. #running-req: 1, #token: 312, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.46, #queue-req: 0, 
[2025-07-28 01:49:04 DP1 TP0] Decode batch. #running-req: 1, #token: 491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:49:04 DP0 TP0] Decode batch. #running-req: 1, #token: 352, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.47, #queue-req: 0, 
[2025-07-28 01:49:04 DP1 TP0] Decode batch. #running-req: 1, #token: 531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 01:49:05 DP0 TP0] Decode batch. #running-req: 1, #token: 392, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.37, #queue-req: 0, 
[2025-07-28 01:49:05 DP1 TP0] Decode batch. #running-req: 1, #token: 571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 01:49:05 DP0 TP0] Decode batch. #running-req: 1, #token: 432, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 01:49:05 DP1 TP0] Decode batch. #running-req: 1, #token: 611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.45, #queue-req: 0, 
[2025-07-28 01:49:05 DP0 TP0] Decode batch. #running-req: 1, #token: 472, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 01:49:05 DP1 TP0] Decode batch. #running-req: 1, #token: 651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 01:49:05 DP0 TP0] Decode batch. #running-req: 1, #token: 512, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:49:06 DP1 TP0] Decode batch. #running-req: 1, #token: 691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.24, #queue-req: 0, 
[2025-07-28 01:49:06 DP0 TP0] Decode batch. #running-req: 1, #token: 552, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.38, #queue-req: 0, 
[2025-07-28 01:49:06 DP1 TP0] Decode batch. #running-req: 1, #token: 731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.31, #queue-req: 0, 
[2025-07-28 01:49:06 DP0 TP0] Decode batch. #running-req: 1, #token: 592, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.39, #queue-req: 0, 
[2025-07-28 01:49:06 DP1 TP0] Decode batch. #running-req: 1, #token: 771, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 01:49:06 DP0 TP0] Decode batch. #running-req: 1, #token: 632, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.36, #queue-req: 0, 
[2025-07-28 01:49:06 DP1 TP0] Decode batch. #running-req: 1, #token: 811, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 01:49:07 DP0 TP0] Decode batch. #running-req: 1, #token: 672, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:49:07 DP1 TP0] Decode batch. #running-req: 1, #token: 851, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:49:07 DP0 TP0] Decode batch. #running-req: 1, #token: 712, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.31, #queue-req: 0, 
[2025-07-28 01:49:07 DP1 TP0] Decode batch. #running-req: 1, #token: 891, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.12, #queue-req: 0, 
[2025-07-28 01:49:07 DP0 TP0] Decode batch. #running-req: 1, #token: 752, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:49:07 DP1 TP0] Decode batch. #running-req: 1, #token: 931, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:49:07 DP0 TP0] Decode batch. #running-req: 1, #token: 792, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.37, #queue-req: 0, 
[2025-07-28 01:49:08 DP1 TP0] Decode batch. #running-req: 1, #token: 971, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.23, #queue-req: 0, 
[2025-07-28 01:49:08 DP0 TP0] Decode batch. #running-req: 1, #token: 832, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 01:49:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1011, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.24, #queue-req: 0, 
[2025-07-28 01:49:08 DP0 TP0] Decode batch. #running-req: 1, #token: 872, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.31, #queue-req: 0, 
[2025-07-28 01:49:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1051, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.99, #queue-req: 0, 
[2025-07-28 01:49:08 DP0 TP0] Decode batch. #running-req: 1, #token: 912, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 01:49:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1091, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.85, #queue-req: 0, 
[2025-07-28 01:49:09 DP0 TP0] Decode batch. #running-req: 1, #token: 952, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 01:49:09] INFO:     127.0.0.1:36740 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:49:09 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 87, #cached-token: 84, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:49:09 DP1 TP0] Decode batch. #running-req: 2, #token: 1234, token usage: 0.00, cuda graph: True, gen throughput (token/s): 163.80, #queue-req: 0, 
[2025-07-28 01:49:09] INFO:     127.0.0.1:36738 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:49:09 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 113, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:49:09 DP1 TP0] Decode batch. #running-req: 1, #token: 227, token usage: 0.00, cuda graph: True, gen throughput (token/s): 233.25, #queue-req: 0, 
[2025-07-28 01:49:09 DP0 TP0] Decode batch. #running-req: 1, #token: 233, token usage: 0.00, cuda graph: True, gen throughput (token/s): 51.04, #queue-req: 0, 
[2025-07-28 01:49:09 DP1 TP0] Decode batch. #running-req: 1, #token: 267, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.55, #queue-req: 0, 
[2025-07-28 01:49:10 DP0 TP0] Decode batch. #running-req: 1, #token: 273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.34, #queue-req: 0, 
[2025-07-28 01:49:10 DP1 TP0] Decode batch. #running-req: 1, #token: 307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.38, #queue-req: 0, 
[2025-07-28 01:49:10 DP0 TP0] Decode batch. #running-req: 1, #token: 313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.37, #queue-req: 0, 
[2025-07-28 01:49:10 DP1 TP0] Decode batch. #running-req: 1, #token: 347, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.38, #queue-req: 0, 
[2025-07-28 01:49:10 DP0 TP0] Decode batch. #running-req: 1, #token: 353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.88, #queue-req: 0, 
[2025-07-28 01:49:10 DP1 TP0] Decode batch. #running-req: 1, #token: 387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 01:49:11 DP0 TP0] Decode batch. #running-req: 1, #token: 393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.85, #queue-req: 0, 
[2025-07-28 01:49:11 DP1 TP0] Decode batch. #running-req: 1, #token: 427, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 01:49:11 DP0 TP0] Decode batch. #running-req: 1, #token: 433, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.91, #queue-req: 0, 
[2025-07-28 01:49:11 DP1 TP0] Decode batch. #running-req: 1, #token: 467, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.85, #queue-req: 0, 
[2025-07-28 01:49:11 DP0 TP0] Decode batch. #running-req: 1, #token: 473, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.88, #queue-req: 0, 
[2025-07-28 01:49:11 DP1 TP0] Decode batch. #running-req: 1, #token: 507, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.41, #queue-req: 0, 
[2025-07-28 01:49:11 DP0 TP0] Decode batch. #running-req: 1, #token: 513, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 01:49:11 DP1 TP0] Decode batch. #running-req: 1, #token: 547, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 01:49:12 DP0 TP0] Decode batch. #running-req: 1, #token: 553, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.42, #queue-req: 0, 
[2025-07-28 01:49:12 DP1 TP0] Decode batch. #running-req: 1, #token: 587, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.30, #queue-req: 0, 
[2025-07-28 01:49:12 DP0 TP0] Decode batch. #running-req: 1, #token: 593, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.36, #queue-req: 0, 
[2025-07-28 01:49:12 DP1 TP0] Decode batch. #running-req: 1, #token: 627, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 01:49:12 DP0 TP0] Decode batch. #running-req: 1, #token: 633, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 01:49:12 DP1 TP0] Decode batch. #running-req: 1, #token: 667, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 01:49:13 DP0 TP0] Decode batch. #running-req: 1, #token: 673, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.39, #queue-req: 0, 
[2025-07-28 01:49:13 DP1 TP0] Decode batch. #running-req: 1, #token: 707, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 01:49:13 DP0 TP0] Decode batch. #running-req: 1, #token: 713, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.31, #queue-req: 0, 
[2025-07-28 01:49:13 DP1 TP0] Decode batch. #running-req: 1, #token: 747, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.23, #queue-req: 0, 
[2025-07-28 01:49:13 DP0 TP0] Decode batch. #running-req: 1, #token: 753, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.13, #queue-req: 0, 
[2025-07-28 01:49:13 DP1 TP0] Decode batch. #running-req: 1, #token: 787, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.09, #queue-req: 0, 
[2025-07-28 01:49:13 DP0 TP0] Decode batch. #running-req: 1, #token: 793, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.38, #queue-req: 0, 
[2025-07-28 01:49:14 DP1 TP0] Decode batch. #running-req: 1, #token: 827, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 01:49:14 DP0 TP0] Decode batch. #running-req: 1, #token: 833, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 01:49:14 DP1 TP0] Decode batch. #running-req: 1, #token: 867, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.14, #queue-req: 0, 
[2025-07-28 01:49:14 DP0 TP0] Decode batch. #running-req: 1, #token: 873, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 01:49:14 DP1 TP0] Decode batch. #running-req: 1, #token: 907, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 01:49:14 DP0 TP0] Decode batch. #running-req: 1, #token: 913, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 01:49:14 DP1 TP0] Decode batch. #running-req: 1, #token: 947, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.14, #queue-req: 0, 
[2025-07-28 01:49:15 DP0 TP0] Decode batch. #running-req: 1, #token: 953, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 01:49:15 DP1 TP0] Decode batch. #running-req: 1, #token: 987, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 01:49:15] INFO:     127.0.0.1:36744 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:49:15 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 162, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:49:15 DP0 TP0] Decode batch. #running-req: 1, #token: 993, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 01:49:15 DP1 TP0] Decode batch. #running-req: 1, #token: 268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.44, #queue-req: 0, 
[2025-07-28 01:49:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1033, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:49:15 DP1 TP0] Decode batch. #running-req: 1, #token: 308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 01:49:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1073, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.96, #queue-req: 0, 
[2025-07-28 01:49:16 DP1 TP0] Decode batch. #running-req: 1, #token: 348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.94, #queue-req: 0, 
[2025-07-28 01:49:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1113, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.91, #queue-req: 0, 
[2025-07-28 01:49:16 DP1 TP0] Decode batch. #running-req: 1, #token: 388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.95, #queue-req: 0, 
[2025-07-28 01:49:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1153, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.86, #queue-req: 0, 
[2025-07-28 01:49:16 DP1 TP0] Decode batch. #running-req: 1, #token: 428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.89, #queue-req: 0, 
[2025-07-28 01:49:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1193, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.86, #queue-req: 0, 
[2025-07-28 01:49:16 DP1 TP0] Decode batch. #running-req: 1, #token: 468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.85, #queue-req: 0, 
[2025-07-28 01:49:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1233, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.77, #queue-req: 0, 
[2025-07-28 01:49:17 DP1 TP0] Decode batch. #running-req: 1, #token: 508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.84, #queue-req: 0, 
[2025-07-28 01:49:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.95, #queue-req: 0, 
[2025-07-28 01:49:17 DP1 TP0] Decode batch. #running-req: 1, #token: 548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.74, #queue-req: 0, 
[2025-07-28 01:49:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.88, #queue-req: 0, 
[2025-07-28 01:49:17 DP1 TP0] Decode batch. #running-req: 1, #token: 588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.03, #queue-req: 0, 
[2025-07-28 01:49:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.85, #queue-req: 0, 
[2025-07-28 01:49:18 DP1 TP0] Decode batch. #running-req: 1, #token: 628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.86, #queue-req: 0, 
[2025-07-28 01:49:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.82, #queue-req: 0, 
[2025-07-28 01:49:18 DP1 TP0] Decode batch. #running-req: 1, #token: 668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.82, #queue-req: 0, 
[2025-07-28 01:49:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1433, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.77, #queue-req: 0, 
[2025-07-28 01:49:18 DP1 TP0] Decode batch. #running-req: 1, #token: 708, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.76, #queue-req: 0, 
[2025-07-28 01:49:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1473, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.69, #queue-req: 0, 
[2025-07-28 01:49:19 DP1 TP0] Decode batch. #running-req: 1, #token: 748, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.60, #queue-req: 0, 
[2025-07-28 01:49:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1513, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.65, #queue-req: 0, 
[2025-07-28 01:49:19 DP1 TP0] Decode batch. #running-req: 1, #token: 788, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:49:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1553, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.72, #queue-req: 0, 
[2025-07-28 01:49:19 DP1 TP0] Decode batch. #running-req: 1, #token: 828, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 01:49:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1593, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.80, #queue-req: 0, 
[2025-07-28 01:49:19 DP1 TP0] Decode batch. #running-req: 1, #token: 868, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 01:49:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1633, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.75, #queue-req: 0, 
[2025-07-28 01:49:20 DP1 TP0] Decode batch. #running-req: 1, #token: 908, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 01:49:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1673, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.80, #queue-req: 0, 
[2025-07-28 01:49:20 DP1 TP0] Decode batch. #running-req: 1, #token: 948, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.03, #queue-req: 0, 
[2025-07-28 01:49:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1713, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.69, #queue-req: 0, 
[2025-07-28 01:49:20 DP1 TP0] Decode batch. #running-req: 1, #token: 988, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 01:49:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1753, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.71, #queue-req: 0, 
[2025-07-28 01:49:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1028, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.07, #queue-req: 0, 
[2025-07-28 01:49:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1793, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.69, #queue-req: 0, 
[2025-07-28 01:49:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1068, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.75, #queue-req: 0, 
[2025-07-28 01:49:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1833, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.69, #queue-req: 0, 
[2025-07-28 01:49:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1108, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.77, #queue-req: 0, 
[2025-07-28 01:49:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1873, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.64, #queue-req: 0, 
[2025-07-28 01:49:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1148, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.65, #queue-req: 0, 
[2025-07-28 01:49:22 DP0 TP0] Decode batch. #running-req: 1, #token: 1913, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.66, #queue-req: 0, 
[2025-07-28 01:49:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1188, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.72, #queue-req: 0, 
[2025-07-28 01:49:22 DP0 TP0] Decode batch. #running-req: 1, #token: 1953, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.65, #queue-req: 0, 
[2025-07-28 01:49:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1228, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.72, #queue-req: 0, 
[2025-07-28 01:49:22 DP0 TP0] Decode batch. #running-req: 1, #token: 1993, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.65, #queue-req: 0, 
[2025-07-28 01:49:22 DP1 TP0] Decode batch. #running-req: 1, #token: 1268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.67, #queue-req: 0, 
[2025-07-28 01:49:23 DP0 TP0] Decode batch. #running-req: 1, #token: 2033, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.67, #queue-req: 0, 
[2025-07-28 01:49:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.70, #queue-req: 0, 
[2025-07-28 01:49:23 DP0 TP0] Decode batch. #running-req: 1, #token: 2073, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.51, #queue-req: 0, 
[2025-07-28 01:49:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.61, #queue-req: 0, 
[2025-07-28 01:49:23 DP0 TP0] Decode batch. #running-req: 1, #token: 2113, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.35, #queue-req: 0, 
[2025-07-28 01:49:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.66, #queue-req: 0, 
[2025-07-28 01:49:23 DP0 TP0] Decode batch. #running-req: 1, #token: 2153, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.36, #queue-req: 0, 
[2025-07-28 01:49:24 DP1 TP0] Decode batch. #running-req: 1, #token: 1428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.39, #queue-req: 0, 
[2025-07-28 01:49:24 DP0 TP0] Decode batch. #running-req: 1, #token: 2193, token usage: 0.00, cuda graph: True, gen throughput (token/s): 131.23, #queue-req: 0, 
[2025-07-28 01:49:24 DP1 TP0] Decode batch. #running-req: 1, #token: 1468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.50, #queue-req: 0, 
[2025-07-28 01:49:24 DP0 TP0] Decode batch. #running-req: 1, #token: 2233, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.35, #queue-req: 0, 
[2025-07-28 01:49:24 DP1 TP0] Decode batch. #running-req: 1, #token: 1508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.54, #queue-req: 0, 
[2025-07-28 01:49:24 DP0 TP0] Decode batch. #running-req: 1, #token: 2273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.32, #queue-req: 0, 
[2025-07-28 01:49:24 DP1 TP0] Decode batch. #running-req: 1, #token: 1548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.48, #queue-req: 0, 
[2025-07-28 01:49:25 DP0 TP0] Decode batch. #running-req: 1, #token: 2313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.32, #queue-req: 0, 
[2025-07-28 01:49:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.66, #queue-req: 0, 
[2025-07-28 01:49:25 DP0 TP0] Decode batch. #running-req: 1, #token: 2353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.35, #queue-req: 0, 
[2025-07-28 01:49:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.61, #queue-req: 0, 
[2025-07-28 01:49:25 DP0 TP0] Decode batch. #running-req: 1, #token: 2393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.25, #queue-req: 0, 
[2025-07-28 01:49:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.52, #queue-req: 0, 
[2025-07-28 01:49:26 DP0 TP0] Decode batch. #running-req: 1, #token: 2433, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.36, #queue-req: 0, 
[2025-07-28 01:49:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1708, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.39, #queue-req: 0, 
[2025-07-28 01:49:26 DP0 TP0] Decode batch. #running-req: 1, #token: 2473, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.11, #queue-req: 0, 
[2025-07-28 01:49:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1748, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.07, #queue-req: 0, 
[2025-07-28 01:49:26 DP0 TP0] Decode batch. #running-req: 1, #token: 2513, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.16, #queue-req: 0, 
[2025-07-28 01:49:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1788, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.22, #queue-req: 0, 
[2025-07-28 01:49:26 DP0 TP0] Decode batch. #running-req: 1, #token: 2553, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.11, #queue-req: 0, 
[2025-07-28 01:49:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1828, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.10, #queue-req: 0, 
[2025-07-28 01:49:27 DP0 TP0] Decode batch. #running-req: 1, #token: 2593, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.17, #queue-req: 0, 
[2025-07-28 01:49:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1868, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.19, #queue-req: 0, 
[2025-07-28 01:49:27 DP0 TP0] Decode batch. #running-req: 1, #token: 2633, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.17, #queue-req: 0, 
[2025-07-28 01:49:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1908, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.16, #queue-req: 0, 
[2025-07-28 01:49:27 DP0 TP0] Decode batch. #running-req: 1, #token: 2673, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.14, #queue-req: 0, 
[2025-07-28 01:49:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1948, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.06, #queue-req: 0, 
[2025-07-28 01:49:28 DP0 TP0] Decode batch. #running-req: 1, #token: 2713, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.14, #queue-req: 0, 
[2025-07-28 01:49:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1988, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.22, #queue-req: 0, 
[2025-07-28 01:49:28 DP0 TP0] Decode batch. #running-req: 1, #token: 2753, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.16, #queue-req: 0, 
[2025-07-28 01:49:28 DP1 TP0] Decode batch. #running-req: 1, #token: 2028, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.16, #queue-req: 0, 
[2025-07-28 01:49:28] INFO:     127.0.0.1:36756 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:49:28 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 141, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:49:28 DP0 TP0] Decode batch. #running-req: 1, #token: 247, token usage: 0.00, cuda graph: True, gen throughput (token/s): 116.27, #queue-req: 0, 
[2025-07-28 01:49:28 DP1 TP0] Decode batch. #running-req: 1, #token: 2068, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.28, #queue-req: 0, 
[2025-07-28 01:49:28 DP0 TP0] Decode batch. #running-req: 1, #token: 287, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.40, #queue-req: 0, 
[2025-07-28 01:49:28 DP1 TP0] Decode batch. #running-req: 1, #token: 2108, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.18, #queue-req: 0, 
[2025-07-28 01:49:29 DP0 TP0] Decode batch. #running-req: 1, #token: 327, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.37, #queue-req: 0, 
[2025-07-28 01:49:29 DP1 TP0] Decode batch. #running-req: 1, #token: 2148, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.14, #queue-req: 0, 
[2025-07-28 01:49:29 DP0 TP0] Decode batch. #running-req: 1, #token: 367, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 01:49:29 DP1 TP0] Decode batch. #running-req: 1, #token: 2188, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.25, #queue-req: 0, 
[2025-07-28 01:49:29 DP0 TP0] Decode batch. #running-req: 1, #token: 407, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:49:29 DP1 TP0] Decode batch. #running-req: 1, #token: 2228, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.15, #queue-req: 0, 
[2025-07-28 01:49:30 DP0 TP0] Decode batch. #running-req: 1, #token: 447, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.10, #queue-req: 0, 
[2025-07-28 01:49:30 DP1 TP0] Decode batch. #running-req: 1, #token: 2268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.20, #queue-req: 0, 
[2025-07-28 01:49:30 DP0 TP0] Decode batch. #running-req: 1, #token: 487, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 01:49:30 DP1 TP0] Decode batch. #running-req: 1, #token: 2308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.18, #queue-req: 0, 
[2025-07-28 01:49:30 DP0 TP0] Decode batch. #running-req: 1, #token: 527, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 01:49:30 DP1 TP0] Decode batch. #running-req: 1, #token: 2348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.04, #queue-req: 0, 
[2025-07-28 01:49:31 DP0 TP0] Decode batch. #running-req: 1, #token: 567, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 01:49:31 DP1 TP0] Decode batch. #running-req: 1, #token: 2388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.25, #queue-req: 0, 
[2025-07-28 01:49:31 DP0 TP0] Decode batch. #running-req: 1, #token: 607, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.36, #queue-req: 0, 
[2025-07-28 01:49:31 DP1 TP0] Decode batch. #running-req: 1, #token: 2428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.10, #queue-req: 0, 
[2025-07-28 01:49:31 DP0 TP0] Decode batch. #running-req: 1, #token: 647, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 01:49:31 DP1 TP0] Decode batch. #running-req: 1, #token: 2468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.02, #queue-req: 0, 
[2025-07-28 01:49:31 DP0 TP0] Decode batch. #running-req: 1, #token: 687, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 01:49:31 DP1 TP0] Decode batch. #running-req: 1, #token: 2508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.92, #queue-req: 0, 
[2025-07-28 01:49:32 DP0 TP0] Decode batch. #running-req: 1, #token: 727, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 01:49:32 DP1 TP0] Decode batch. #running-req: 1, #token: 2548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.85, #queue-req: 0, 
[2025-07-28 01:49:32 DP0 TP0] Decode batch. #running-req: 1, #token: 767, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.23, #queue-req: 0, 
[2025-07-28 01:49:32 DP1 TP0] Decode batch. #running-req: 1, #token: 2588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.10, #queue-req: 0, 
[2025-07-28 01:49:32 DP0 TP0] Decode batch. #running-req: 1, #token: 807, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 01:49:32 DP1 TP0] Decode batch. #running-req: 1, #token: 2628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.09, #queue-req: 0, 
[2025-07-28 01:49:33 DP0 TP0] Decode batch. #running-req: 1, #token: 847, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 01:49:33 DP1 TP0] Decode batch. #running-req: 1, #token: 2668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.99, #queue-req: 0, 
[2025-07-28 01:49:33 DP0 TP0] Decode batch. #running-req: 1, #token: 887, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 01:49:33 DP1 TP0] Decode batch. #running-req: 1, #token: 2708, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.95, #queue-req: 0, 
[2025-07-28 01:49:33 DP0 TP0] Decode batch. #running-req: 1, #token: 927, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 01:49:33 DP1 TP0] Decode batch. #running-req: 1, #token: 2748, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.92, #queue-req: 0, 
[2025-07-28 01:49:33 DP0 TP0] Decode batch. #running-req: 1, #token: 967, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 01:49:33 DP1 TP0] Decode batch. #running-req: 1, #token: 2788, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.05, #queue-req: 0, 
[2025-07-28 01:49:34 DP0 TP0] Decode batch. #running-req: 1, #token: 1007, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.13, #queue-req: 0, 
[2025-07-28 01:49:34 DP1 TP0] Decode batch. #running-req: 1, #token: 2828, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.92, #queue-req: 0, 
[2025-07-28 01:49:34 DP0 TP0] Decode batch. #running-req: 1, #token: 1047, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.91, #queue-req: 0, 
[2025-07-28 01:49:34 DP1 TP0] Decode batch. #running-req: 1, #token: 2868, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.94, #queue-req: 0, 
[2025-07-28 01:49:34 DP0 TP0] Decode batch. #running-req: 1, #token: 1087, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.83, #queue-req: 0, 
[2025-07-28 01:49:34 DP1 TP0] Decode batch. #running-req: 1, #token: 2908, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.93, #queue-req: 0, 
[2025-07-28 01:49:35 DP0 TP0] Decode batch. #running-req: 1, #token: 1127, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.66, #queue-req: 0, 
[2025-07-28 01:49:35 DP1 TP0] Decode batch. #running-req: 1, #token: 2948, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.88, #queue-req: 0, 
[2025-07-28 01:49:35 DP0 TP0] Decode batch. #running-req: 1, #token: 1167, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.73, #queue-req: 0, 
[2025-07-28 01:49:35 DP1 TP0] Decode batch. #running-req: 1, #token: 2988, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.01, #queue-req: 0, 
[2025-07-28 01:49:35 DP0 TP0] Decode batch. #running-req: 1, #token: 1207, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.75, #queue-req: 0, 
[2025-07-28 01:49:35 DP1 TP0] Decode batch. #running-req: 1, #token: 3028, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.96, #queue-req: 0, 
[2025-07-28 01:49:35 DP0 TP0] Decode batch. #running-req: 1, #token: 1247, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.80, #queue-req: 0, 
[2025-07-28 01:49:36 DP1 TP0] Decode batch. #running-req: 1, #token: 3068, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.92, #queue-req: 0, 
[2025-07-28 01:49:36 DP0 TP0] Decode batch. #running-req: 1, #token: 1287, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.78, #queue-req: 0, 
[2025-07-28 01:49:36 DP1 TP0] Decode batch. #running-req: 1, #token: 3108, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.85, #queue-req: 0, 
[2025-07-28 01:49:36 DP0 TP0] Decode batch. #running-req: 1, #token: 1327, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.72, #queue-req: 0, 
[2025-07-28 01:49:36 DP1 TP0] Decode batch. #running-req: 1, #token: 3148, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.64, #queue-req: 0, 
[2025-07-28 01:49:36 DP0 TP0] Decode batch. #running-req: 1, #token: 1367, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.82, #queue-req: 0, 
[2025-07-28 01:49:36 DP1 TP0] Decode batch. #running-req: 1, #token: 3188, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.86, #queue-req: 0, 
[2025-07-28 01:49:37] INFO:     127.0.0.1:60938 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:49:37 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 115, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:49:37 DP1 TP0] Decode batch. #running-req: 2, #token: 3358, token usage: 0.00, cuda graph: True, gen throughput (token/s): 156.44, #queue-req: 0, 
[2025-07-28 01:49:37 DP1 TP0] Decode batch. #running-req: 2, #token: 3438, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.05, #queue-req: 0, 
[2025-07-28 01:49:37 DP1 TP0] Decode batch. #running-req: 2, #token: 3518, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.18, #queue-req: 0, 
[2025-07-28 01:49:38 DP1 TP0] Decode batch. #running-req: 2, #token: 3598, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.08, #queue-req: 0, 
[2025-07-28 01:49:38 DP1 TP0] Decode batch. #running-req: 2, #token: 3678, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.17, #queue-req: 0, 
[2025-07-28 01:49:38 DP1 TP0] Decode batch. #running-req: 2, #token: 3758, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.07, #queue-req: 0, 
[2025-07-28 01:49:39 DP1 TP0] Decode batch. #running-req: 2, #token: 3838, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.07, #queue-req: 0, 
[2025-07-28 01:49:39 DP1 TP0] Decode batch. #running-req: 2, #token: 3918, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.15, #queue-req: 0, 
[2025-07-28 01:49:39 DP1 TP0] Decode batch. #running-req: 2, #token: 3998, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.89, #queue-req: 0, 
[2025-07-28 01:49:40 DP1 TP0] Decode batch. #running-req: 2, #token: 4078, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.75, #queue-req: 0, 
[2025-07-28 01:49:40 DP1 TP0] Decode batch. #running-req: 2, #token: 4158, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.56, #queue-req: 0, 
[2025-07-28 01:49:40 DP1 TP0] Decode batch. #running-req: 2, #token: 4238, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.51, #queue-req: 0, 
[2025-07-28 01:49:41 DP1 TP0] Decode batch. #running-req: 2, #token: 4318, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.62, #queue-req: 0, 
[2025-07-28 01:49:41] INFO:     127.0.0.1:33384 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:49:41 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 123, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:49:41 DP1 TP0] Decode batch. #running-req: 1, #token: 736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.92, #queue-req: 0, 
[2025-07-28 01:49:41 DP0 TP0] Decode batch. #running-req: 1, #token: 216, token usage: 0.00, cuda graph: True, gen throughput (token/s): 8.72, #queue-req: 0, 
[2025-07-28 01:49:41 DP1 TP0] Decode batch. #running-req: 1, #token: 776, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 01:49:41] INFO:     127.0.0.1:34848 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:49:41 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 194, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:49:41 DP0 TP0] Decode batch. #running-req: 1, #token: 256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 126.31, #queue-req: 0, 
[2025-07-28 01:49:41 DP1 TP0] Decode batch. #running-req: 1, #token: 317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 116.77, #queue-req: 0, 
[2025-07-28 01:49:42 DP0 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.43, #queue-req: 0, 
[2025-07-28 01:49:42 DP1 TP0] Decode batch. #running-req: 1, #token: 357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.30, #queue-req: 0, 
[2025-07-28 01:49:42 DP0 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.39, #queue-req: 0, 
[2025-07-28 01:49:42 DP1 TP0] Decode batch. #running-req: 1, #token: 397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 01:49:42 DP0 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.46, #queue-req: 0, 
[2025-07-28 01:49:42 DP1 TP0] Decode batch. #running-req: 1, #token: 437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.04, #queue-req: 0, 
[2025-07-28 01:49:42 DP0 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 01:49:43 DP1 TP0] Decode batch. #running-req: 1, #token: 477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.06, #queue-req: 0, 
[2025-07-28 01:49:43 DP0 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.12, #queue-req: 0, 
[2025-07-28 01:49:43 DP1 TP0] Decode batch. #running-req: 1, #token: 517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.04, #queue-req: 0, 
[2025-07-28 01:49:43 DP0 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.13, #queue-req: 0, 
[2025-07-28 01:49:43 DP1 TP0] Decode batch. #running-req: 1, #token: 557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.17, #queue-req: 0, 
[2025-07-28 01:49:43 DP0 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:49:44 DP1 TP0] Decode batch. #running-req: 1, #token: 597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 01:49:44 DP0 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 01:49:44 DP1 TP0] Decode batch. #running-req: 1, #token: 637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 01:49:44 DP0 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:49:44 DP1 TP0] Decode batch. #running-req: 1, #token: 677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 01:49:44 DP0 TP0] Decode batch. #running-req: 1, #token: 656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.23, #queue-req: 0, 
[2025-07-28 01:49:44 DP1 TP0] Decode batch. #running-req: 1, #token: 717, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.14, #queue-req: 0, 
[2025-07-28 01:49:44 DP0 TP0] Decode batch. #running-req: 1, #token: 696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 01:49:45 DP1 TP0] Decode batch. #running-req: 1, #token: 757, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.08, #queue-req: 0, 
[2025-07-28 01:49:45 DP0 TP0] Decode batch. #running-req: 1, #token: 736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 01:49:45 DP1 TP0] Decode batch. #running-req: 1, #token: 797, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.13, #queue-req: 0, 
[2025-07-28 01:49:45 DP0 TP0] Decode batch. #running-req: 1, #token: 776, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 01:49:45 DP1 TP0] Decode batch. #running-req: 1, #token: 837, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:49:45 DP0 TP0] Decode batch. #running-req: 1, #token: 816, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.17, #queue-req: 0, 
[2025-07-28 01:49:46 DP1 TP0] Decode batch. #running-req: 1, #token: 877, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 01:49:46 DP0 TP0] Decode batch. #running-req: 1, #token: 856, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 01:49:46] INFO:     127.0.0.1:34856 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:49:46 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 82, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:49:46 DP1 TP0] Decode batch. #running-req: 1, #token: 917, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.87, #queue-req: 0, 
[2025-07-28 01:49:46 DP0 TP0] Decode batch. #running-req: 1, #token: 202, token usage: 0.00, cuda graph: True, gen throughput (token/s): 118.30, #queue-req: 0, 
[2025-07-28 01:49:46 DP1 TP0] Decode batch. #running-req: 1, #token: 957, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.03, #queue-req: 0, 
[2025-07-28 01:49:46 DP0 TP0] Decode batch. #running-req: 1, #token: 242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.39, #queue-req: 0, 
[2025-07-28 01:49:46 DP1 TP0] Decode batch. #running-req: 1, #token: 997, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.06, #queue-req: 0, 
[2025-07-28 01:49:47 DP0 TP0] Decode batch. #running-req: 1, #token: 282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.48, #queue-req: 0, 
[2025-07-28 01:49:47 DP1 TP0] Decode batch. #running-req: 1, #token: 1037, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.00, #queue-req: 0, 
[2025-07-28 01:49:47 DP0 TP0] Decode batch. #running-req: 1, #token: 322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 01:49:47 DP1 TP0] Decode batch. #running-req: 1, #token: 1077, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.78, #queue-req: 0, 
[2025-07-28 01:49:47 DP0 TP0] Decode batch. #running-req: 1, #token: 362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 01:49:47 DP1 TP0] Decode batch. #running-req: 1, #token: 1117, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.79, #queue-req: 0, 
[2025-07-28 01:49:47 DP0 TP0] Decode batch. #running-req: 1, #token: 402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 01:49:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1157, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.82, #queue-req: 0, 
[2025-07-28 01:49:48 DP0 TP0] Decode batch. #running-req: 1, #token: 442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.14, #queue-req: 0, 
[2025-07-28 01:49:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1197, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.72, #queue-req: 0, 
[2025-07-28 01:49:48 DP0 TP0] Decode batch. #running-req: 1, #token: 482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.13, #queue-req: 0, 
[2025-07-28 01:49:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1237, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.75, #queue-req: 0, 
[2025-07-28 01:49:48 DP0 TP0] Decode batch. #running-req: 1, #token: 522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 01:49:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.54, #queue-req: 0, 
[2025-07-28 01:49:49 DP0 TP0] Decode batch. #running-req: 1, #token: 562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:49:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.67, #queue-req: 0, 
[2025-07-28 01:49:49 DP0 TP0] Decode batch. #running-req: 1, #token: 602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.30, #queue-req: 0, 
[2025-07-28 01:49:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.66, #queue-req: 0, 
[2025-07-28 01:49:49 DP0 TP0] Decode batch. #running-req: 1, #token: 642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.17, #queue-req: 0, 
[2025-07-28 01:49:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.66, #queue-req: 0, 
[2025-07-28 01:49:49 DP0 TP0] Decode batch. #running-req: 1, #token: 682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 01:49:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.60, #queue-req: 0, 
[2025-07-28 01:49:50 DP0 TP0] Decode batch. #running-req: 1, #token: 722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.24, #queue-req: 0, 
[2025-07-28 01:49:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.45, #queue-req: 0, 
[2025-07-28 01:49:50 DP0 TP0] Decode batch. #running-req: 1, #token: 762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 01:49:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.49, #queue-req: 0, 
[2025-07-28 01:49:50 DP0 TP0] Decode batch. #running-req: 1, #token: 802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 01:49:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.56, #queue-req: 0, 
[2025-07-28 01:49:51 DP0 TP0] Decode batch. #running-req: 1, #token: 842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.14, #queue-req: 0, 
[2025-07-28 01:49:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.62, #queue-req: 0, 
[2025-07-28 01:49:51] INFO:     127.0.0.1:52892 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:49:51 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 152, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:49:51 DP1 TP0] Decode batch. #running-req: 2, #token: 1827, token usage: 0.00, cuda graph: True, gen throughput (token/s): 219.12, #queue-req: 0, 
[2025-07-28 01:49:52 DP1 TP0] Decode batch. #running-req: 2, #token: 1907, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.11, #queue-req: 0, 
[2025-07-28 01:49:52 DP1 TP0] Decode batch. #running-req: 2, #token: 1987, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.12, #queue-req: 0, 
[2025-07-28 01:49:52 DP1 TP0] Decode batch. #running-req: 2, #token: 2067, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.23, #queue-req: 0, 
[2025-07-28 01:49:52 DP1 TP0] Decode batch. #running-req: 2, #token: 2147, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.16, #queue-req: 0, 
[2025-07-28 01:49:53 DP1 TP0] Decode batch. #running-req: 2, #token: 2227, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.30, #queue-req: 0, 
[2025-07-28 01:49:53 DP1 TP0] Decode batch. #running-req: 2, #token: 2307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.72, #queue-req: 0, 
[2025-07-28 01:49:53 DP1 TP0] Decode batch. #running-req: 2, #token: 2387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.19, #queue-req: 0, 
[2025-07-28 01:49:54 DP1 TP0] Decode batch. #running-req: 2, #token: 2467, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.15, #queue-req: 0, 
[2025-07-28 01:49:54 DP1 TP0] Decode batch. #running-req: 2, #token: 2547, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.03, #queue-req: 0, 
[2025-07-28 01:49:54 DP1 TP0] Decode batch. #running-req: 2, #token: 2627, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.13, #queue-req: 0, 
[2025-07-28 01:49:55 DP1 TP0] Decode batch. #running-req: 2, #token: 2707, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.71, #queue-req: 0, 
[2025-07-28 01:49:55 DP1 TP0] Decode batch. #running-req: 2, #token: 2787, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.52, #queue-req: 0, 
[2025-07-28 01:49:55 DP1 TP0] Decode batch. #running-req: 2, #token: 2867, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.56, #queue-req: 0, 
[2025-07-28 01:49:56 DP1 TP0] Decode batch. #running-req: 2, #token: 2947, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.48, #queue-req: 0, 
[2025-07-28 01:49:56 DP1 TP0] Decode batch. #running-req: 2, #token: 3027, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.51, #queue-req: 0, 
[2025-07-28 01:49:56 DP1 TP0] Decode batch. #running-req: 2, #token: 3107, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.49, #queue-req: 0, 
[2025-07-28 01:49:57 DP1 TP0] Decode batch. #running-req: 2, #token: 3187, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.49, #queue-req: 0, 
[2025-07-28 01:49:57 DP1 TP0] Decode batch. #running-req: 2, #token: 3267, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.53, #queue-req: 0, 
[2025-07-28 01:49:57 DP1 TP0] Decode batch. #running-req: 2, #token: 3347, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.46, #queue-req: 0, 
[2025-07-28 01:49:57 DP1 TP0] Decode batch. #running-req: 2, #token: 3427, token usage: 0.00, cuda graph: True, gen throughput (token/s): 252.74, #queue-req: 0, 
[2025-07-28 01:49:58 DP1 TP0] Decode batch. #running-req: 2, #token: 3507, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.08, #queue-req: 0, 
[2025-07-28 01:49:58 DP1 TP0] Decode batch. #running-req: 2, #token: 3587, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.44, #queue-req: 0, 
[2025-07-28 01:49:58 DP1 TP0] Decode batch. #running-req: 2, #token: 3667, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.43, #queue-req: 0, 
[2025-07-28 01:49:59 DP1 TP0] Decode batch. #running-req: 2, #token: 3747, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.51, #queue-req: 0, 
[2025-07-28 01:49:59 DP1 TP0] Decode batch. #running-req: 2, #token: 3827, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.24, #queue-req: 0, 
[2025-07-28 01:49:59 DP1 TP0] Decode batch. #running-req: 2, #token: 3907, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.66, #queue-req: 0, 
[2025-07-28 01:50:00 DP1 TP0] Decode batch. #running-req: 2, #token: 3987, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.87, #queue-req: 0, 
[2025-07-28 01:50:00 DP1 TP0] Decode batch. #running-req: 2, #token: 4067, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.02, #queue-req: 0, 
[2025-07-28 01:50:00 DP1 TP0] Decode batch. #running-req: 2, #token: 4147, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.97, #queue-req: 0, 
[2025-07-28 01:50:01 DP1 TP0] Decode batch. #running-req: 2, #token: 4227, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.92, #queue-req: 0, 
[2025-07-28 01:50:01 DP1 TP0] Decode batch. #running-req: 2, #token: 4307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.65, #queue-req: 0, 
[2025-07-28 01:50:01 DP1 TP0] Decode batch. #running-req: 2, #token: 4387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.68, #queue-req: 0, 
[2025-07-28 01:50:02 DP1 TP0] Decode batch. #running-req: 2, #token: 4467, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.51, #queue-req: 0, 
[2025-07-28 01:50:02 DP1 TP0] Decode batch. #running-req: 2, #token: 4547, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.51, #queue-req: 0, 
[2025-07-28 01:50:02 DP1 TP0] Decode batch. #running-req: 2, #token: 4627, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.49, #queue-req: 0, 
[2025-07-28 01:50:02] INFO:     127.0.0.1:34862 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:50:02 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 126, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:50:03 DP1 TP0] Decode batch. #running-req: 1, #token: 1716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 234.19, #queue-req: 0, 
[2025-07-28 01:50:03 DP0 TP0] Decode batch. #running-req: 1, #token: 224, token usage: 0.00, cuda graph: True, gen throughput (token/s): 3.33, #queue-req: 0, 
[2025-07-28 01:50:03 DP1 TP0] Decode batch. #running-req: 1, #token: 1756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.69, #queue-req: 0, 
[2025-07-28 01:50:03 DP0 TP0] Decode batch. #running-req: 1, #token: 264, token usage: 0.00, cuda graph: True, gen throughput (token/s): 132.83, #queue-req: 0, 
[2025-07-28 01:50:03 DP1 TP0] Decode batch. #running-req: 1, #token: 1796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.60, #queue-req: 0, 
[2025-07-28 01:50:03 DP0 TP0] Decode batch. #running-req: 1, #token: 304, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 01:50:03 DP1 TP0] Decode batch. #running-req: 1, #token: 1836, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.54, #queue-req: 0, 
[2025-07-28 01:50:04 DP0 TP0] Decode batch. #running-req: 1, #token: 344, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.48, #queue-req: 0, 
[2025-07-28 01:50:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1876, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.63, #queue-req: 0, 
[2025-07-28 01:50:04 DP0 TP0] Decode batch. #running-req: 1, #token: 384, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.38, #queue-req: 0, 
[2025-07-28 01:50:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1916, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.63, #queue-req: 0, 
[2025-07-28 01:50:04 DP0 TP0] Decode batch. #running-req: 1, #token: 424, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.23, #queue-req: 0, 
[2025-07-28 01:50:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1956, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.56, #queue-req: 0, 
[2025-07-28 01:50:04] INFO:     127.0.0.1:52906 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:50:04 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 142, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:50:04 DP0 TP0] Decode batch. #running-req: 1, #token: 464, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 01:50:05 DP1 TP0] Decode batch. #running-req: 1, #token: 265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 119.16, #queue-req: 0, 
[2025-07-28 01:50:05 DP0 TP0] Decode batch. #running-req: 1, #token: 504, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 01:50:05 DP1 TP0] Decode batch. #running-req: 1, #token: 305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.51, #queue-req: 0, 
[2025-07-28 01:50:05 DP0 TP0] Decode batch. #running-req: 1, #token: 544, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 01:50:05 DP1 TP0] Decode batch. #running-req: 1, #token: 345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.42, #queue-req: 0, 
[2025-07-28 01:50:05 DP0 TP0] Decode batch. #running-req: 1, #token: 584, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 01:50:05 DP1 TP0] Decode batch. #running-req: 1, #token: 385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.47, #queue-req: 0, 
[2025-07-28 01:50:06 DP0 TP0] Decode batch. #running-req: 1, #token: 624, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 01:50:06 DP1 TP0] Decode batch. #running-req: 1, #token: 425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.31, #queue-req: 0, 
[2025-07-28 01:50:06 DP0 TP0] Decode batch. #running-req: 1, #token: 664, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.31, #queue-req: 0, 
[2025-07-28 01:50:06 DP1 TP0] Decode batch. #running-req: 1, #token: 465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:50:06 DP0 TP0] Decode batch. #running-req: 1, #token: 704, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 01:50:06 DP1 TP0] Decode batch. #running-req: 1, #token: 505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 01:50:06 DP0 TP0] Decode batch. #running-req: 1, #token: 744, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.23, #queue-req: 0, 
[2025-07-28 01:50:07 DP1 TP0] Decode batch. #running-req: 1, #token: 545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 01:50:07 DP0 TP0] Decode batch. #running-req: 1, #token: 784, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:50:07 DP1 TP0] Decode batch. #running-req: 1, #token: 585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 01:50:07 DP0 TP0] Decode batch. #running-req: 1, #token: 824, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 01:50:07 DP1 TP0] Decode batch. #running-req: 1, #token: 625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 01:50:07 DP0 TP0] Decode batch. #running-req: 1, #token: 864, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:50:08 DP1 TP0] Decode batch. #running-req: 1, #token: 665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.31, #queue-req: 0, 
[2025-07-28 01:50:08 DP0 TP0] Decode batch. #running-req: 1, #token: 904, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 01:50:08 DP1 TP0] Decode batch. #running-req: 1, #token: 705, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 01:50:08 DP0 TP0] Decode batch. #running-req: 1, #token: 944, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:50:08 DP1 TP0] Decode batch. #running-req: 1, #token: 745, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 01:50:08 DP0 TP0] Decode batch. #running-req: 1, #token: 984, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:50:08] INFO:     127.0.0.1:40178 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:50:08 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 138, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:50:09 DP0 TP0] Decode batch. #running-req: 2, #token: 1199, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.51, #queue-req: 0, 
[2025-07-28 01:50:09 DP0 TP0] Decode batch. #running-req: 2, #token: 1279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.61, #queue-req: 0, 
[2025-07-28 01:50:09 DP0 TP0] Decode batch. #running-req: 2, #token: 1359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.42, #queue-req: 0, 
[2025-07-28 01:50:09 DP0 TP0] Decode batch. #running-req: 2, #token: 1439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.34, #queue-req: 0, 
[2025-07-28 01:50:10 DP0 TP0] Decode batch. #running-req: 2, #token: 1519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.02, #queue-req: 0, 
[2025-07-28 01:50:10 DP0 TP0] Decode batch. #running-req: 2, #token: 1599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.32, #queue-req: 0, 
[2025-07-28 01:50:10 DP0 TP0] Decode batch. #running-req: 2, #token: 1679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.29, #queue-req: 0, 
[2025-07-28 01:50:11 DP0 TP0] Decode batch. #running-req: 2, #token: 1759, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.18, #queue-req: 0, 
[2025-07-28 01:50:11 DP0 TP0] Decode batch. #running-req: 2, #token: 1839, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.10, #queue-req: 0, 
[2025-07-28 01:50:11 DP0 TP0] Decode batch. #running-req: 2, #token: 1919, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.11, #queue-req: 0, 
[2025-07-28 01:50:12 DP0 TP0] Decode batch. #running-req: 2, #token: 1999, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.11, #queue-req: 0, 
[2025-07-28 01:50:12 DP0 TP0] Decode batch. #running-req: 2, #token: 2079, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.99, #queue-req: 0, 
[2025-07-28 01:50:12 DP0 TP0] Decode batch. #running-req: 2, #token: 2159, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.98, #queue-req: 0, 
[2025-07-28 01:50:13 DP0 TP0] Decode batch. #running-req: 2, #token: 2239, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.79, #queue-req: 0, 
[2025-07-28 01:50:13 DP0 TP0] Decode batch. #running-req: 2, #token: 2319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.90, #queue-req: 0, 
[2025-07-28 01:50:13 DP0 TP0] Decode batch. #running-req: 2, #token: 2399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.98, #queue-req: 0, 
[2025-07-28 01:50:14 DP0 TP0] Decode batch. #running-req: 2, #token: 2479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.95, #queue-req: 0, 
[2025-07-28 01:50:14 DP0 TP0] Decode batch. #running-req: 2, #token: 2559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.89, #queue-req: 0, 
[2025-07-28 01:50:14] INFO:     127.0.0.1:40192 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:50:14 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 93, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:50:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1744, token usage: 0.00, cuda graph: True, gen throughput (token/s): 194.64, #queue-req: 0, 
[2025-07-28 01:50:14 DP1 TP0] Decode batch. #running-req: 1, #token: 203, token usage: 0.00, cuda graph: True, gen throughput (token/s): 6.46, #queue-req: 0, 
[2025-07-28 01:50:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1784, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.58, #queue-req: 0, 
[2025-07-28 01:50:15 DP1 TP0] Decode batch. #running-req: 1, #token: 243, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.03, #queue-req: 0, 
[2025-07-28 01:50:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1824, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.51, #queue-req: 0, 
[2025-07-28 01:50:15 DP1 TP0] Decode batch. #running-req: 1, #token: 283, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.46, #queue-req: 0, 
[2025-07-28 01:50:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1864, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.58, #queue-req: 0, 
[2025-07-28 01:50:15 DP1 TP0] Decode batch. #running-req: 1, #token: 323, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 01:50:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1904, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.52, #queue-req: 0, 
[2025-07-28 01:50:15 DP1 TP0] Decode batch. #running-req: 1, #token: 363, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.30, #queue-req: 0, 
[2025-07-28 01:50:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1944, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.53, #queue-req: 0, 
[2025-07-28 01:50:16 DP1 TP0] Decode batch. #running-req: 1, #token: 403, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 01:50:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1984, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.46, #queue-req: 0, 
[2025-07-28 01:50:16 DP1 TP0] Decode batch. #running-req: 1, #token: 443, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.05, #queue-req: 0, 
[2025-07-28 01:50:16 DP0 TP0] Decode batch. #running-req: 1, #token: 2024, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.45, #queue-req: 0, 
[2025-07-28 01:50:16 DP1 TP0] Decode batch. #running-req: 1, #token: 483, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.17, #queue-req: 0, 
[2025-07-28 01:50:17 DP0 TP0] Decode batch. #running-req: 1, #token: 2064, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.40, #queue-req: 0, 
[2025-07-28 01:50:17 DP1 TP0] Decode batch. #running-req: 1, #token: 523, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 01:50:17 DP0 TP0] Decode batch. #running-req: 1, #token: 2104, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.25, #queue-req: 0, 
[2025-07-28 01:50:17 DP1 TP0] Decode batch. #running-req: 1, #token: 563, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.12, #queue-req: 0, 
[2025-07-28 01:50:17 DP0 TP0] Decode batch. #running-req: 1, #token: 2144, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.20, #queue-req: 0, 
[2025-07-28 01:50:17 DP1 TP0] Decode batch. #running-req: 1, #token: 603, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 01:50:17 DP0 TP0] Decode batch. #running-req: 1, #token: 2184, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.23, #queue-req: 0, 
[2025-07-28 01:50:18 DP1 TP0] Decode batch. #running-req: 1, #token: 643, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:50:18] INFO:     127.0.0.1:49274 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:50:18 DP0 TP0] Decode batch. #running-req: 1, #token: 2224, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.18, #queue-req: 0, 
[2025-07-28 01:50:18 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 138, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:50:18 DP0 TP0] Decode batch. #running-req: 2, #token: 2442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 226.06, #queue-req: 0, 
[2025-07-28 01:50:18 DP0 TP0] Decode batch. #running-req: 2, #token: 2522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.63, #queue-req: 0, 
[2025-07-28 01:50:19 DP0 TP0] Decode batch. #running-req: 2, #token: 2602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.40, #queue-req: 0, 
[2025-07-28 01:50:19 DP0 TP0] Decode batch. #running-req: 2, #token: 2682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.48, #queue-req: 0, 
[2025-07-28 01:50:19 DP0 TP0] Decode batch. #running-req: 2, #token: 2762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.33, #queue-req: 0, 
[2025-07-28 01:50:20 DP0 TP0] Decode batch. #running-req: 2, #token: 2842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.61, #queue-req: 0, 
[2025-07-28 01:50:20 DP0 TP0] Decode batch. #running-req: 2, #token: 2922, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.46, #queue-req: 0, 
[2025-07-28 01:50:20 DP0 TP0] Decode batch. #running-req: 2, #token: 3002, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.38, #queue-req: 0, 
[2025-07-28 01:50:21 DP0 TP0] Decode batch. #running-req: 2, #token: 3082, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.15, #queue-req: 0, 
[2025-07-28 01:50:21 DP0 TP0] Decode batch. #running-req: 2, #token: 3162, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.20, #queue-req: 0, 
[2025-07-28 01:50:21 DP0 TP0] Decode batch. #running-req: 2, #token: 3242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.46, #queue-req: 0, 
[2025-07-28 01:50:22 DP0 TP0] Decode batch. #running-req: 2, #token: 3322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.27, #queue-req: 0, 
[2025-07-28 01:50:22 DP0 TP0] Decode batch. #running-req: 2, #token: 3402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.23, #queue-req: 0, 
[2025-07-28 01:50:22 DP0 TP0] Decode batch. #running-req: 2, #token: 3482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.12, #queue-req: 0, 
[2025-07-28 01:50:22 DP0 TP0] Decode batch. #running-req: 2, #token: 3562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.01, #queue-req: 0, 
[2025-07-28 01:50:23 DP0 TP0] Decode batch. #running-req: 2, #token: 3642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.04, #queue-req: 0, 
[2025-07-28 01:50:23 DP0 TP0] Decode batch. #running-req: 2, #token: 3722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.09, #queue-req: 0, 
[2025-07-28 01:50:23 DP0 TP0] Decode batch. #running-req: 2, #token: 3802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.20, #queue-req: 0, 
[2025-07-28 01:50:24 DP0 TP0] Decode batch. #running-req: 2, #token: 3882, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.07, #queue-req: 0, 
[2025-07-28 01:50:24 DP0 TP0] Decode batch. #running-req: 2, #token: 3962, token usage: 0.00, cuda graph: True, gen throughput (token/s): 252.72, #queue-req: 0, 
[2025-07-28 01:50:24 DP0 TP0] Decode batch. #running-req: 2, #token: 4042, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.02, #queue-req: 0, 
[2025-07-28 01:50:25 DP0 TP0] Decode batch. #running-req: 2, #token: 4122, token usage: 0.00, cuda graph: True, gen throughput (token/s): 252.64, #queue-req: 0, 
[2025-07-28 01:50:25] INFO:     127.0.0.1:40168 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:50:25 DP0 TP0] Decode batch. #running-req: 2, #token: 1143, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.16, #queue-req: 0, 
[2025-07-28 01:50:25 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 76, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:50:25 DP1 TP0] Decode batch. #running-req: 1, #token: 176, token usage: 0.00, cuda graph: True, gen throughput (token/s): 5.20, #queue-req: 0, 
[2025-07-28 01:50:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1183, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.51, #queue-req: 0, 
[2025-07-28 01:50:25 DP1 TP0] Decode batch. #running-req: 1, #token: 216, token usage: 0.00, cuda graph: True, gen throughput (token/s): 132.59, #queue-req: 0, 
[2025-07-28 01:50:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1223, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.35, #queue-req: 0, 
[2025-07-28 01:50:26 DP1 TP0] Decode batch. #running-req: 1, #token: 256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.43, #queue-req: 0, 
[2025-07-28 01:50:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1263, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.47, #queue-req: 0, 
[2025-07-28 01:50:26 DP1 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.46, #queue-req: 0, 
[2025-07-28 01:50:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.45, #queue-req: 0, 
[2025-07-28 01:50:26 DP1 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 01:50:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.37, #queue-req: 0, 
[2025-07-28 01:50:27 DP1 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 01:50:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.34, #queue-req: 0, 
[2025-07-28 01:50:27 DP1 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 01:50:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.46, #queue-req: 0, 
[2025-07-28 01:50:27 DP1 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:50:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.25, #queue-req: 0, 
[2025-07-28 01:50:28 DP1 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.07, #queue-req: 0, 
[2025-07-28 01:50:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.23, #queue-req: 0, 
[2025-07-28 01:50:28 DP1 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 01:50:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.21, #queue-req: 0, 
[2025-07-28 01:50:28 DP1 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.06, #queue-req: 0, 
[2025-07-28 01:50:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.50, #queue-req: 0, 
[2025-07-28 01:50:28 DP1 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 01:50:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.47, #queue-req: 0, 
[2025-07-28 01:50:29 DP1 TP0] Decode batch. #running-req: 1, #token: 656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:50:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.65, #queue-req: 0, 
[2025-07-28 01:50:29 DP1 TP0] Decode batch. #running-req: 1, #token: 696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.24, #queue-req: 0, 
[2025-07-28 01:50:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1703, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.57, #queue-req: 0, 
[2025-07-28 01:50:29 DP1 TP0] Decode batch. #running-req: 1, #token: 736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.23, #queue-req: 0, 
[2025-07-28 01:50:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1743, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.60, #queue-req: 0, 
[2025-07-28 01:50:29] INFO:     127.0.0.1:49284 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:50:29 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:50:30 DP1 TP0] Decode batch. #running-req: 1, #token: 776, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 01:50:30 DP0 TP0] Decode batch. #running-req: 1, #token: 268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 118.00, #queue-req: 0, 
[2025-07-28 01:50:30] INFO:     127.0.0.1:42478 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:50:30 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 82, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:50:30 DP1 TP0] Decode batch. #running-req: 1, #token: 170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 118.31, #queue-req: 0, 
[2025-07-28 01:50:30 DP0 TP0] Decode batch. #running-req: 1, #token: 308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 01:50:30 DP1 TP0] Decode batch. #running-req: 1, #token: 210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.69, #queue-req: 0, 
[2025-07-28 01:50:30 DP0 TP0] Decode batch. #running-req: 1, #token: 348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.45, #queue-req: 0, 
[2025-07-28 01:50:30 DP1 TP0] Decode batch. #running-req: 1, #token: 250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.60, #queue-req: 0, 
[2025-07-28 01:50:31 DP0 TP0] Decode batch. #running-req: 1, #token: 388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 01:50:31 DP1 TP0] Decode batch. #running-req: 1, #token: 290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.52, #queue-req: 0, 
[2025-07-28 01:50:31 DP0 TP0] Decode batch. #running-req: 1, #token: 428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:50:31 DP1 TP0] Decode batch. #running-req: 1, #token: 330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.50, #queue-req: 0, 
[2025-07-28 01:50:31 DP0 TP0] Decode batch. #running-req: 1, #token: 468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.23, #queue-req: 0, 
[2025-07-28 01:50:31 DP1 TP0] Decode batch. #running-req: 1, #token: 370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.48, #queue-req: 0, 
[2025-07-28 01:50:31 DP0 TP0] Decode batch. #running-req: 1, #token: 508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.13, #queue-req: 0, 
[2025-07-28 01:50:32 DP1 TP0] Decode batch. #running-req: 1, #token: 410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 01:50:32 DP0 TP0] Decode batch. #running-req: 1, #token: 548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 01:50:32 DP1 TP0] Decode batch. #running-req: 1, #token: 450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:50:32 DP0 TP0] Decode batch. #running-req: 1, #token: 588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.30, #queue-req: 0, 
[2025-07-28 01:50:32 DP1 TP0] Decode batch. #running-req: 1, #token: 490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:50:32 DP0 TP0] Decode batch. #running-req: 1, #token: 628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 01:50:33 DP1 TP0] Decode batch. #running-req: 1, #token: 530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 01:50:33 DP0 TP0] Decode batch. #running-req: 1, #token: 668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 01:50:33 DP1 TP0] Decode batch. #running-req: 1, #token: 570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 01:50:33 DP0 TP0] Decode batch. #running-req: 1, #token: 708, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.24, #queue-req: 0, 
[2025-07-28 01:50:33 DP1 TP0] Decode batch. #running-req: 1, #token: 610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 01:50:33 DP0 TP0] Decode batch. #running-req: 1, #token: 748, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 01:50:33 DP1 TP0] Decode batch. #running-req: 1, #token: 650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.30, #queue-req: 0, 
[2025-07-28 01:50:34 DP0 TP0] Decode batch. #running-req: 1, #token: 788, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 01:50:34 DP1 TP0] Decode batch. #running-req: 1, #token: 690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 01:50:34 DP0 TP0] Decode batch. #running-req: 1, #token: 828, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 01:50:34 DP1 TP0] Decode batch. #running-req: 1, #token: 730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 01:50:34 DP0 TP0] Decode batch. #running-req: 1, #token: 868, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:50:34 DP1 TP0] Decode batch. #running-req: 1, #token: 770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 01:50:34 DP0 TP0] Decode batch. #running-req: 1, #token: 908, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 01:50:35 DP1 TP0] Decode batch. #running-req: 1, #token: 810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.23, #queue-req: 0, 
[2025-07-28 01:50:35 DP0 TP0] Decode batch. #running-req: 1, #token: 948, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:50:35 DP1 TP0] Decode batch. #running-req: 1, #token: 850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 01:50:35 DP0 TP0] Decode batch. #running-req: 1, #token: 988, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 01:50:35 DP1 TP0] Decode batch. #running-req: 1, #token: 890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.24, #queue-req: 0, 
[2025-07-28 01:50:35 DP0 TP0] Decode batch. #running-req: 1, #token: 1028, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:50:35 DP1 TP0] Decode batch. #running-req: 1, #token: 930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 01:50:36 DP0 TP0] Decode batch. #running-req: 1, #token: 1068, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.78, #queue-req: 0, 
[2025-07-28 01:50:36] INFO:     127.0.0.1:42504 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:50:36 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 171, #cached-token: 86, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:50:36 DP0 TP0] Decode batch. #running-req: 2, #token: 1303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 184.62, #queue-req: 0, 
[2025-07-28 01:50:36 DP0 TP0] Decode batch. #running-req: 2, #token: 1383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.75, #queue-req: 0, 
[2025-07-28 01:50:37 DP0 TP0] Decode batch. #running-req: 2, #token: 1463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.08, #queue-req: 0, 
[2025-07-28 01:50:37 DP0 TP0] Decode batch. #running-req: 2, #token: 1543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.76, #queue-req: 0, 
[2025-07-28 01:50:37 DP0 TP0] Decode batch. #running-req: 2, #token: 1623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.37, #queue-req: 0, 
[2025-07-28 01:50:37 DP0 TP0] Decode batch. #running-req: 2, #token: 1703, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.20, #queue-req: 0, 
[2025-07-28 01:50:38 DP0 TP0] Decode batch. #running-req: 2, #token: 1783, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.04, #queue-req: 0, 
[2025-07-28 01:50:38 DP0 TP0] Decode batch. #running-req: 2, #token: 1863, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.26, #queue-req: 0, 
[2025-07-28 01:50:38] INFO:     127.0.0.1:42488 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:50:38 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 176, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:50:38 DP1 TP0] Decode batch. #running-req: 1, #token: 275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 13.87, #queue-req: 0, 
[2025-07-28 01:50:38 DP0 TP0] Decode batch. #running-req: 1, #token: 601, token usage: 0.00, cuda graph: True, gen throughput (token/s): 165.36, #queue-req: 0, 
[2025-07-28 01:50:39 DP1 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 133.21, #queue-req: 0, 
[2025-07-28 01:50:39 DP0 TP0] Decode batch. #running-req: 1, #token: 641, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 01:50:39 DP1 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 01:50:39 DP0 TP0] Decode batch. #running-req: 1, #token: 681, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 01:50:39 DP1 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 01:50:39 DP0 TP0] Decode batch. #running-req: 1, #token: 721, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.23, #queue-req: 0, 
[2025-07-28 01:50:40 DP1 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 01:50:40 DP0 TP0] Decode batch. #running-req: 1, #token: 761, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 01:50:40 DP1 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 01:50:40 DP0 TP0] Decode batch. #running-req: 1, #token: 801, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.12, #queue-req: 0, 
[2025-07-28 01:50:40 DP1 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:50:40 DP0 TP0] Decode batch. #running-req: 1, #token: 841, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:50:40 DP1 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 01:50:40 DP0 TP0] Decode batch. #running-req: 1, #token: 881, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 01:50:41 DP1 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:50:41 DP0 TP0] Decode batch. #running-req: 1, #token: 921, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 01:50:41 DP1 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 01:50:41 DP0 TP0] Decode batch. #running-req: 1, #token: 961, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 01:50:41 DP1 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 01:50:41 DP0 TP0] Decode batch. #running-req: 1, #token: 1001, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 01:50:42 DP1 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 01:50:42 DP0 TP0] Decode batch. #running-req: 1, #token: 1041, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 01:50:42] INFO:     127.0.0.1:59924 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:50:42 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 86, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:50:42 DP1 TP0] Decode batch. #running-req: 1, #token: 755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 01:50:42 DP0 TP0] Decode batch. #running-req: 1, #token: 209, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.68, #queue-req: 0, 
[2025-07-28 01:50:42 DP1 TP0] Decode batch. #running-req: 1, #token: 795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 01:50:42 DP0 TP0] Decode batch. #running-req: 1, #token: 249, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.43, #queue-req: 0, 
[2025-07-28 01:50:42 DP1 TP0] Decode batch. #running-req: 1, #token: 835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 01:50:43 DP0 TP0] Decode batch. #running-req: 1, #token: 289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.43, #queue-req: 0, 
[2025-07-28 01:50:43 DP1 TP0] Decode batch. #running-req: 1, #token: 875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 01:50:43 DP0 TP0] Decode batch. #running-req: 1, #token: 329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 01:50:43 DP1 TP0] Decode batch. #running-req: 1, #token: 915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.14, #queue-req: 0, 
[2025-07-28 01:50:43 DP0 TP0] Decode batch. #running-req: 1, #token: 369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.36, #queue-req: 0, 
[2025-07-28 01:50:43 DP1 TP0] Decode batch. #running-req: 1, #token: 955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.55, #queue-req: 0, 
[2025-07-28 01:50:43 DP0 TP0] Decode batch. #running-req: 1, #token: 409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 01:50:43] INFO:     127.0.0.1:59928 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:50:43 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 151, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:50:44 DP1 TP0] Decode batch. #running-req: 1, #token: 256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.51, #queue-req: 0, 
[2025-07-28 01:50:44 DP0 TP0] Decode batch. #running-req: 1, #token: 449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.13, #queue-req: 0, 
[2025-07-28 01:50:44 DP1 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 01:50:44 DP0 TP0] Decode batch. #running-req: 1, #token: 489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.24, #queue-req: 0, 
[2025-07-28 01:50:44 DP1 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 01:50:44 DP0 TP0] Decode batch. #running-req: 1, #token: 529, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 01:50:45 DP1 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 01:50:45 DP0 TP0] Decode batch. #running-req: 1, #token: 569, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:50:45 DP1 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.05, #queue-req: 0, 
[2025-07-28 01:50:45 DP0 TP0] Decode batch. #running-req: 1, #token: 609, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 01:50:45 DP1 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.07, #queue-req: 0, 
[2025-07-28 01:50:45 DP0 TP0] Decode batch. #running-req: 1, #token: 649, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 01:50:45 DP1 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.71, #queue-req: 0, 
[2025-07-28 01:50:45 DP0 TP0] Decode batch. #running-req: 1, #token: 689, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.98, #queue-req: 0, 
[2025-07-28 01:50:46 DP1 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 01:50:46 DP0 TP0] Decode batch. #running-req: 1, #token: 729, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:50:46 DP1 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.23, #queue-req: 0, 
[2025-07-28 01:50:46 DP0 TP0] Decode batch. #running-req: 1, #token: 769, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 01:50:46] INFO:     127.0.0.1:59934 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:50:46 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 121, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:50:46 DP1 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.89, #queue-req: 0, 
[2025-07-28 01:50:46 DP0 TP0] Decode batch. #running-req: 1, #token: 238, token usage: 0.00, cuda graph: True, gen throughput (token/s): 118.09, #queue-req: 0, 
[2025-07-28 01:50:47 DP1 TP0] Decode batch. #running-req: 1, #token: 656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.68, #queue-req: 0, 
[2025-07-28 01:50:47 DP0 TP0] Decode batch. #running-req: 1, #token: 278, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.34, #queue-req: 0, 
[2025-07-28 01:50:47 DP1 TP0] Decode batch. #running-req: 1, #token: 696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.90, #queue-req: 0, 
[2025-07-28 01:50:47 DP0 TP0] Decode batch. #running-req: 1, #token: 318, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:50:47 DP1 TP0] Decode batch. #running-req: 1, #token: 736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.13, #queue-req: 0, 
[2025-07-28 01:50:47 DP0 TP0] Decode batch. #running-req: 1, #token: 358, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.17, #queue-req: 0, 
[2025-07-28 01:50:47 DP1 TP0] Decode batch. #running-req: 1, #token: 776, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:50:48 DP0 TP0] Decode batch. #running-req: 1, #token: 398, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 01:50:48 DP1 TP0] Decode batch. #running-req: 1, #token: 816, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 01:50:48 DP0 TP0] Decode batch. #running-req: 1, #token: 438, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.96, #queue-req: 0, 
[2025-07-28 01:50:48 DP1 TP0] Decode batch. #running-req: 1, #token: 856, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.00, #queue-req: 0, 
[2025-07-28 01:50:48 DP0 TP0] Decode batch. #running-req: 1, #token: 478, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.93, #queue-req: 0, 
[2025-07-28 01:50:48 DP1 TP0] Decode batch. #running-req: 1, #token: 896, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.96, #queue-req: 0, 
[2025-07-28 01:50:48 DP0 TP0] Decode batch. #running-req: 1, #token: 518, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.96, #queue-req: 0, 
[2025-07-28 01:50:49 DP1 TP0] Decode batch. #running-req: 1, #token: 936, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.98, #queue-req: 0, 
[2025-07-28 01:50:49 DP0 TP0] Decode batch. #running-req: 1, #token: 558, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.06, #queue-req: 0, 
[2025-07-28 01:50:49 DP1 TP0] Decode batch. #running-req: 1, #token: 976, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.05, #queue-req: 0, 
[2025-07-28 01:50:49 DP0 TP0] Decode batch. #running-req: 1, #token: 598, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.07, #queue-req: 0, 
[2025-07-28 01:50:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1016, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.04, #queue-req: 0, 
[2025-07-28 01:50:49 DP0 TP0] Decode batch. #running-req: 1, #token: 638, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.04, #queue-req: 0, 
[2025-07-28 01:50:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1056, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.83, #queue-req: 0, 
[2025-07-28 01:50:50 DP0 TP0] Decode batch. #running-req: 1, #token: 678, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.10, #queue-req: 0, 
[2025-07-28 01:50:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1096, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.32, #queue-req: 0, 
[2025-07-28 01:50:50 DP0 TP0] Decode batch. #running-req: 1, #token: 718, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.14, #queue-req: 0, 
[2025-07-28 01:50:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1136, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.25, #queue-req: 0, 
[2025-07-28 01:50:50 DP0 TP0] Decode batch. #running-req: 1, #token: 758, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.09, #queue-req: 0, 
[2025-07-28 01:50:50] INFO:     127.0.0.1:35332 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:50:50 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:50:50 DP1 TP0] Decode batch. #running-req: 1, #token: 239, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.09, #queue-req: 0, 
[2025-07-28 01:50:50 DP0 TP0] Decode batch. #running-req: 1, #token: 798, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.06, #queue-req: 0, 
[2025-07-28 01:50:51 DP1 TP0] Decode batch. #running-req: 1, #token: 279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.93, #queue-req: 0, 
[2025-07-28 01:50:51 DP0 TP0] Decode batch. #running-req: 1, #token: 838, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.05, #queue-req: 0, 
[2025-07-28 01:50:51 DP1 TP0] Decode batch. #running-req: 1, #token: 319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.83, #queue-req: 0, 
[2025-07-28 01:50:51 DP0 TP0] Decode batch. #running-req: 1, #token: 878, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.03, #queue-req: 0, 
[2025-07-28 01:50:51 DP1 TP0] Decode batch. #running-req: 1, #token: 359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.82, #queue-req: 0, 
[2025-07-28 01:50:51 DP0 TP0] Decode batch. #running-req: 1, #token: 918, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.06, #queue-req: 0, 
[2025-07-28 01:50:52 DP1 TP0] Decode batch. #running-req: 1, #token: 399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.12, #queue-req: 0, 
[2025-07-28 01:50:52 DP0 TP0] Decode batch. #running-req: 1, #token: 958, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.69, #queue-req: 0, 
[2025-07-28 01:50:52 DP1 TP0] Decode batch. #running-req: 1, #token: 439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 01:50:52 DP0 TP0] Decode batch. #running-req: 1, #token: 998, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.12, #queue-req: 0, 
[2025-07-28 01:50:52 DP1 TP0] Decode batch. #running-req: 1, #token: 479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.14, #queue-req: 0, 
[2025-07-28 01:50:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1038, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.96, #queue-req: 0, 
[2025-07-28 01:50:52 DP1 TP0] Decode batch. #running-req: 1, #token: 519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.10, #queue-req: 0, 
[2025-07-28 01:50:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1078, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.72, #queue-req: 0, 
[2025-07-28 01:50:53 DP1 TP0] Decode batch. #running-req: 1, #token: 559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 01:50:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1118, token usage: 0.00, cuda graph: True, gen throughput (token/s): 131.90, #queue-req: 0, 
[2025-07-28 01:50:53 DP1 TP0] Decode batch. #running-req: 1, #token: 599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 01:50:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1158, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.45, #queue-req: 0, 
[2025-07-28 01:50:53 DP1 TP0] Decode batch. #running-req: 1, #token: 639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 01:50:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1198, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.63, #queue-req: 0, 
[2025-07-28 01:50:54 DP1 TP0] Decode batch. #running-req: 1, #token: 679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 01:50:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1238, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.61, #queue-req: 0, 
[2025-07-28 01:50:54 DP1 TP0] Decode batch. #running-req: 1, #token: 719, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.14, #queue-req: 0, 
[2025-07-28 01:50:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1278, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.63, #queue-req: 0, 
[2025-07-28 01:50:54 DP1 TP0] Decode batch. #running-req: 1, #token: 759, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 01:50:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1318, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.58, #queue-req: 0, 
[2025-07-28 01:50:54 DP1 TP0] Decode batch. #running-req: 1, #token: 799, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.17, #queue-req: 0, 
[2025-07-28 01:50:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1358, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.61, #queue-req: 0, 
[2025-07-28 01:50:55 DP1 TP0] Decode batch. #running-req: 1, #token: 839, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 01:50:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1398, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.60, #queue-req: 0, 
[2025-07-28 01:50:55 DP1 TP0] Decode batch. #running-req: 1, #token: 879, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 01:50:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1438, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.52, #queue-req: 0, 
[2025-07-28 01:50:55] INFO:     127.0.0.1:35348 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:50:55 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 125, #cached-token: 90, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:50:55] INFO:     127.0.0.1:35352 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:50:55 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 142, #cached-token: 91, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:50:55 DP1 TP0] Decode batch. #running-req: 1, #token: 249, token usage: 0.00, cuda graph: True, gen throughput (token/s): 118.56, #queue-req: 0, 
[2025-07-28 01:50:55 DP0 TP0] Decode batch. #running-req: 1, #token: 242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.28, #queue-req: 0, 
[2025-07-28 01:50:56 DP1 TP0] Decode batch. #running-req: 1, #token: 289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.42, #queue-req: 0, 
[2025-07-28 01:50:56 DP0 TP0] Decode batch. #running-req: 1, #token: 282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.39, #queue-req: 0, 
[2025-07-28 01:50:56 DP1 TP0] Decode batch. #running-req: 1, #token: 329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.39, #queue-req: 0, 
[2025-07-28 01:50:56 DP0 TP0] Decode batch. #running-req: 1, #token: 322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.31, #queue-req: 0, 
[2025-07-28 01:50:56 DP1 TP0] Decode batch. #running-req: 1, #token: 369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 01:50:56 DP0 TP0] Decode batch. #running-req: 1, #token: 362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 01:50:57 DP1 TP0] Decode batch. #running-req: 1, #token: 409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.17, #queue-req: 0, 
[2025-07-28 01:50:57 DP0 TP0] Decode batch. #running-req: 1, #token: 402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 01:50:57 DP1 TP0] Decode batch. #running-req: 1, #token: 449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.14, #queue-req: 0, 
[2025-07-28 01:50:57 DP0 TP0] Decode batch. #running-req: 1, #token: 442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 01:50:57 DP1 TP0] Decode batch. #running-req: 1, #token: 489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.04, #queue-req: 0, 
[2025-07-28 01:50:57 DP0 TP0] Decode batch. #running-req: 1, #token: 482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.12, #queue-req: 0, 
[2025-07-28 01:50:57 DP1 TP0] Decode batch. #running-req: 1, #token: 529, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 01:50:58 DP0 TP0] Decode batch. #running-req: 1, #token: 522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:50:58 DP1 TP0] Decode batch. #running-req: 1, #token: 569, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 01:50:58 DP0 TP0] Decode batch. #running-req: 1, #token: 562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 01:50:58 DP1 TP0] Decode batch. #running-req: 1, #token: 609, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.23, #queue-req: 0, 
[2025-07-28 01:50:58 DP0 TP0] Decode batch. #running-req: 1, #token: 602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.24, #queue-req: 0, 
[2025-07-28 01:50:58 DP1 TP0] Decode batch. #running-req: 1, #token: 649, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 01:50:58 DP0 TP0] Decode batch. #running-req: 1, #token: 642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.24, #queue-req: 0, 
[2025-07-28 01:50:59 DP1 TP0] Decode batch. #running-req: 1, #token: 689, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 01:50:59 DP0 TP0] Decode batch. #running-req: 1, #token: 682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 01:50:59 DP1 TP0] Decode batch. #running-req: 1, #token: 729, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 01:50:59 DP0 TP0] Decode batch. #running-req: 1, #token: 722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 01:50:59 DP1 TP0] Decode batch. #running-req: 1, #token: 769, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 01:50:59 DP0 TP0] Decode batch. #running-req: 1, #token: 762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 01:50:59 DP1 TP0] Decode batch. #running-req: 1, #token: 809, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 01:51:00 DP0 TP0] Decode batch. #running-req: 1, #token: 802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 01:51:00 DP1 TP0] Decode batch. #running-req: 1, #token: 849, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.14, #queue-req: 0, 
[2025-07-28 01:51:00 DP0 TP0] Decode batch. #running-req: 1, #token: 842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 01:51:00 DP1 TP0] Decode batch. #running-req: 1, #token: 889, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 01:51:00 DP0 TP0] Decode batch. #running-req: 1, #token: 882, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:51:00 DP1 TP0] Decode batch. #running-req: 1, #token: 929, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.17, #queue-req: 0, 
[2025-07-28 01:51:00 DP0 TP0] Decode batch. #running-req: 1, #token: 922, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.10, #queue-req: 0, 
[2025-07-28 01:51:01 DP1 TP0] Decode batch. #running-req: 1, #token: 969, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.13, #queue-req: 0, 
[2025-07-28 01:51:01 DP0 TP0] Decode batch. #running-req: 1, #token: 962, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.09, #queue-req: 0, 
[2025-07-28 01:51:01] INFO:     127.0.0.1:39608 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:51:01 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 101, #cached-token: 90, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:51:01 DP0 TP0] Decode batch. #running-req: 2, #token: 1129, token usage: 0.00, cuda graph: True, gen throughput (token/s): 190.85, #queue-req: 0, 
[2025-07-28 01:51:01 DP0 TP0] Decode batch. #running-req: 2, #token: 1209, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.79, #queue-req: 0, 
[2025-07-28 01:51:02 DP0 TP0] Decode batch. #running-req: 2, #token: 1289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.41, #queue-req: 0, 
[2025-07-28 01:51:02 DP0 TP0] Decode batch. #running-req: 2, #token: 1369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.42, #queue-req: 0, 
[2025-07-28 01:51:02] INFO:     127.0.0.1:39592 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:51:02 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 87, #cached-token: 90, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:51:02 DP0 TP0] Decode batch. #running-req: 1, #token: 377, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.66, #queue-req: 0, 
[2025-07-28 01:51:02 DP1 TP0] Decode batch. #running-req: 1, #token: 198, token usage: 0.00, cuda graph: True, gen throughput (token/s): 21.97, #queue-req: 0, 
[2025-07-28 01:51:03 DP0 TP0] Decode batch. #running-req: 1, #token: 417, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 01:51:03 DP1 TP0] Decode batch. #running-req: 1, #token: 238, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 01:51:03 DP0 TP0] Decode batch. #running-req: 1, #token: 457, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.17, #queue-req: 0, 
[2025-07-28 01:51:03 DP1 TP0] Decode batch. #running-req: 1, #token: 278, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.44, #queue-req: 0, 
[2025-07-28 01:51:03 DP0 TP0] Decode batch. #running-req: 1, #token: 497, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.12, #queue-req: 0, 
[2025-07-28 01:51:03 DP1 TP0] Decode batch. #running-req: 1, #token: 318, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 01:51:03 DP0 TP0] Decode batch. #running-req: 1, #token: 537, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.13, #queue-req: 0, 
[2025-07-28 01:51:04 DP1 TP0] Decode batch. #running-req: 1, #token: 358, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.36, #queue-req: 0, 
[2025-07-28 01:51:04 DP0 TP0] Decode batch. #running-req: 1, #token: 577, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.23, #queue-req: 0, 
[2025-07-28 01:51:04 DP1 TP0] Decode batch. #running-req: 1, #token: 398, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 01:51:04 DP0 TP0] Decode batch. #running-req: 1, #token: 617, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 01:51:04 DP1 TP0] Decode batch. #running-req: 1, #token: 438, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 01:51:04 DP0 TP0] Decode batch. #running-req: 1, #token: 657, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 01:51:05 DP1 TP0] Decode batch. #running-req: 1, #token: 478, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 01:51:05 DP0 TP0] Decode batch. #running-req: 1, #token: 697, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.09, #queue-req: 0, 
[2025-07-28 01:51:05 DP1 TP0] Decode batch. #running-req: 1, #token: 518, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.13, #queue-req: 0, 
[2025-07-28 01:51:05 DP0 TP0] Decode batch. #running-req: 1, #token: 737, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.08, #queue-req: 0, 
[2025-07-28 01:51:05 DP1 TP0] Decode batch. #running-req: 1, #token: 558, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 01:51:05 DP0 TP0] Decode batch. #running-req: 1, #token: 777, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.02, #queue-req: 0, 
[2025-07-28 01:51:05 DP1 TP0] Decode batch. #running-req: 1, #token: 598, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.05, #queue-req: 0, 
[2025-07-28 01:51:06 DP0 TP0] Decode batch. #running-req: 1, #token: 817, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.07, #queue-req: 0, 
[2025-07-28 01:51:06 DP1 TP0] Decode batch. #running-req: 1, #token: 638, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.04, #queue-req: 0, 
[2025-07-28 01:51:06 DP0 TP0] Decode batch. #running-req: 1, #token: 857, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.12, #queue-req: 0, 
[2025-07-28 01:51:06 DP1 TP0] Decode batch. #running-req: 1, #token: 678, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 01:51:06 DP0 TP0] Decode batch. #running-req: 1, #token: 897, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 01:51:06 DP1 TP0] Decode batch. #running-req: 1, #token: 718, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 01:51:06 DP0 TP0] Decode batch. #running-req: 1, #token: 937, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 01:51:07 DP1 TP0] Decode batch. #running-req: 1, #token: 758, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 01:51:07 DP0 TP0] Decode batch. #running-req: 1, #token: 977, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.23, #queue-req: 0, 
[2025-07-28 01:51:07 DP1 TP0] Decode batch. #running-req: 1, #token: 798, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.17, #queue-req: 0, 
[2025-07-28 01:51:07 DP0 TP0] Decode batch. #running-req: 1, #token: 1017, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 01:51:07 DP1 TP0] Decode batch. #running-req: 1, #token: 838, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.14, #queue-req: 0, 
[2025-07-28 01:51:07 DP0 TP0] Decode batch. #running-req: 1, #token: 1057, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.83, #queue-req: 0, 
[2025-07-28 01:51:07 DP1 TP0] Decode batch. #running-req: 1, #token: 878, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.04, #queue-req: 0, 
[2025-07-28 01:51:08] INFO:     127.0.0.1:35818 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:51:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1097, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.39, #queue-req: 0, 
[2025-07-28 01:51:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1137, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.61, #queue-req: 0, 
[2025-07-28 01:51:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1177, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.69, #queue-req: 0, 
[2025-07-28 01:51:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1217, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.66, #queue-req: 0, 
[2025-07-28 01:51:09 DP0 TP0] Decode batch. #running-req: 1, #token: 1257, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.63, #queue-req: 0, 
[2025-07-28 01:51:09] INFO:     127.0.0.1:39610 - "POST /v1/chat/completions HTTP/1.1" 200 OK

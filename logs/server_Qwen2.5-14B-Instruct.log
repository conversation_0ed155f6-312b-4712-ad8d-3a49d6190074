[2025-07-28 00:38:32] server_args=ServerArgs(model_path='/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-14B-Instruct', tokenizer_path='/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-14B-Instruct', tokenizer_mode='auto', skip_tokenizer_init=False, load_format='auto', model_loader_extra_config='{}', trust_remote_code=False, context_length=32768, is_embedding=False, enable_multimodal=None, revision=None, model_impl='auto', host='127.0.0.1', port=8003, skip_server_warmup=False, warmups=None, nccl_port=None, dtype='auto', quantization=None, quantization_param_path=None, kv_cache_dtype='auto', mem_fraction_static=0.85, max_running_requests=None, max_total_tokens=None, chunked_prefill_size=8192, max_prefill_tokens=16384, schedule_policy='fcfs', schedule_conservativeness=1.0, cpu_offload_gb=0, page_size=1, hybrid_kvcache_ratio=None, swa_full_tokens_ratio=0.8, disable_hybrid_swa_memory=False, device='cuda', tp_size=4, pp_size=1, max_micro_batch_size=None, stream_interval=1, stream_output=False, random_seed=487652013, constrained_json_whitespace_pattern=None, watchdog_timeout=300, dist_timeout=None, download_dir=None, base_gpu_id=0, gpu_id_step=1, sleep_on_idle=False, log_level='info', log_level_http=None, log_requests=False, log_requests_level=0, crash_dump_folder=None, show_time_cost=True, enable_metrics=True, enable_metrics_for_all_schedulers=False, bucket_time_to_first_token=None, bucket_inter_token_latency=None, bucket_e2e_request_latency=None, collect_tokens_histogram=False, decode_log_interval=40, enable_request_time_stats_logging=False, kv_events_config=None, api_key=None, served_model_name='/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-14B-Instruct', chat_template=None, completion_template=None, file_storage_path='sglang_storage', enable_cache_report=False, reasoning_parser=None, tool_call_parser=None, dp_size=2, load_balance_method='round_robin', dist_init_addr=None, nnodes=1, node_rank=0, json_model_override_args='{}', preferred_sampling_params=None, enable_lora=None, max_lora_rank=None, lora_target_modules=None, lora_paths=None, max_loras_per_batch=8, lora_backend='triton', attention_backend=None, sampling_backend='flashinfer', grammar_backend='xgrammar', mm_attention_backend=None, speculative_algorithm=None, speculative_draft_model_path=None, speculative_num_steps=None, speculative_eagle_topk=None, speculative_num_draft_tokens=None, speculative_accept_threshold_single=1.0, speculative_accept_threshold_acc=1.0, speculative_token_map=None, ep_size=1, enable_ep_moe=False, enable_deepep_moe=False, enable_flashinfer_moe=False, enable_flashinfer_allreduce_fusion=False, deepep_mode='auto', ep_num_redundant_experts=0, ep_dispatch_algorithm='static', init_expert_location='trivial', enable_eplb=False, eplb_algorithm='auto', eplb_rebalance_num_iterations=1000, eplb_rebalance_layers_per_chunk=None, expert_distribution_recorder_mode=None, expert_distribution_recorder_buffer_size=1000, enable_expert_distribution_metrics=False, deepep_config=None, moe_dense_tp_size=None, enable_hierarchical_cache=False, hicache_ratio=2.0, hicache_size=0, hicache_write_policy='write_through_selective', hicache_io_backend='', hicache_storage_backend=None, enable_double_sparsity=False, ds_channel_config_path=None, ds_heavy_channel_num=32, ds_heavy_token_num=256, ds_heavy_channel_type='qk', ds_sparse_decode_threshold=4096, disable_radix_cache=False, cuda_graph_max_bs=None, cuda_graph_bs=None, disable_cuda_graph=False, disable_cuda_graph_padding=False, enable_profile_cuda_graph=False, enable_nccl_nvls=False, enable_tokenizer_batch_encode=False, disable_outlines_disk_cache=False, disable_custom_all_reduce=False, enable_mscclpp=False, disable_overlap_schedule=False, enable_mixed_chunk=False, enable_dp_attention=False, enable_dp_lm_head=False, enable_two_batch_overlap=False, enable_torch_compile=False, torch_compile_max_bs=32, torchao_config='', enable_nan_detection=False, enable_p2p_check=False, triton_attention_reduce_in_fp32=False, triton_attention_num_kv_splits=8, num_continuous_decode_steps=1, delete_ckpt_after_loading=False, enable_memory_saver=False, allow_auto_truncate=False, enable_custom_logit_processor=False, flashinfer_mla_disable_ragged=False, disable_shared_experts_fusion=False, disable_chunked_prefix_cache=False, disable_fast_image_processor=False, enable_return_hidden_states=False, enable_triton_kernel_moe=False, debug_tensor_dump_output_folder=None, debug_tensor_dump_input_file=None, debug_tensor_dump_inject=False, debug_tensor_dump_prefill_only=False, disaggregation_mode='null', disaggregation_transfer_backend='mooncake', disaggregation_bootstrap_port=8998, disaggregation_decode_tp=None, disaggregation_decode_dp=None, disaggregation_prefill_pp=1, disaggregation_ib_device=None, num_reserved_decode_tokens=512, pdlb_url=None, custom_weight_loader=[], weight_loader_disable_mmap=False, enable_pdmux=False, sm_group_num=3)
[2025-07-28 00:38:38] Launch DP0 starting at GPU #0.
[2025-07-28 00:38:38] Launch DP1 starting at GPU #4.
[2025-07-28 00:38:46 DP0 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 00:38:46 DP0 TP0] Init torch distributed begin.
[2025-07-28 00:38:46 DP1 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 00:38:46 DP1 TP0] Init torch distributed begin.
[2025-07-28 00:38:48 DP1 TP0] sglang is using nccl==2.26.2
[2025-07-28 00:38:48 DP0 TP0] sglang is using nccl==2.26.2
[2025-07-28 00:38:49 DP1 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 00:38:49 DP0 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 00:38:49 DP1 TP0] Load weight begin. avail mem=78.21 GB
[2025-07-28 00:38:49 DP0 TP0] Load weight begin. avail mem=78.21 GB

Loading safetensors checkpoint shards:   0% Completed | 0/8 [00:00<?, ?it/s]

Loading safetensors checkpoint shards:   0% Completed | 0/8 [00:00<?, ?it/s]

Loading safetensors checkpoint shards:  12% Completed | 1/8 [00:01<00:11,  1.63s/it]

Loading safetensors checkpoint shards:  12% Completed | 1/8 [00:01<00:11,  1.63s/it]

Loading safetensors checkpoint shards:  25% Completed | 2/8 [00:03<00:08,  1.50s/it]

Loading safetensors checkpoint shards:  25% Completed | 2/8 [00:03<00:08,  1.50s/it]

Loading safetensors checkpoint shards:  38% Completed | 3/8 [00:04<00:08,  1.61s/it]

Loading safetensors checkpoint shards:  38% Completed | 3/8 [00:04<00:08,  1.61s/it]

Loading safetensors checkpoint shards:  50% Completed | 4/8 [00:05<00:04,  1.09s/it]

Loading safetensors checkpoint shards:  50% Completed | 4/8 [00:05<00:04,  1.14s/it]

Loading safetensors checkpoint shards:  62% Completed | 5/8 [00:06<00:04,  1.34s/it]

Loading safetensors checkpoint shards:  62% Completed | 5/8 [00:06<00:04,  1.35s/it]

Loading safetensors checkpoint shards:  75% Completed | 6/8 [00:08<00:02,  1.47s/it]

Loading safetensors checkpoint shards:  75% Completed | 6/8 [00:08<00:02,  1.48s/it]

Loading safetensors checkpoint shards:  88% Completed | 7/8 [00:10<00:01,  1.56s/it]

Loading safetensors checkpoint shards:  88% Completed | 7/8 [00:10<00:01,  1.56s/it]

Loading safetensors checkpoint shards: 100% Completed | 8/8 [00:12<00:00,  1.63s/it]

Loading safetensors checkpoint shards: 100% Completed | 8/8 [00:12<00:00,  1.63s/it]

Loading safetensors checkpoint shards: 100% Completed | 8/8 [00:12<00:00,  1.52s/it]

Loading safetensors checkpoint shards: 100% Completed | 8/8 [00:12<00:00,  1.52s/it]


[2025-07-28 00:39:01 DP0 TP0] Load weight end. type=Qwen2ForCausalLM, dtype=torch.bfloat16, avail mem=71.18 GB, mem usage=7.03 GB.
[2025-07-28 00:39:02 DP1 TP0] Load weight end. type=Qwen2ForCausalLM, dtype=torch.bfloat16, avail mem=71.18 GB, mem usage=7.03 GB.
[2025-07-28 00:39:02 DP0 TP1] KV Cache is allocated. #tokens: 1296952, K size: 29.68 GB, V size: 29.68 GB
[2025-07-28 00:39:02 DP0 TP2] KV Cache is allocated. #tokens: 1296952, K size: 29.68 GB, V size: 29.68 GB
[2025-07-28 00:39:02 DP0 TP3] KV Cache is allocated. #tokens: 1296952, K size: 29.68 GB, V size: 29.68 GB
[2025-07-28 00:39:02 DP1 TP1] KV Cache is allocated. #tokens: 1296952, K size: 29.68 GB, V size: 29.68 GB
[2025-07-28 00:39:02 DP1 TP2] KV Cache is allocated. #tokens: 1296952, K size: 29.68 GB, V size: 29.68 GB
[2025-07-28 00:39:02 DP0 TP0] KV Cache is allocated. #tokens: 1296952, K size: 29.68 GB, V size: 29.68 GB
[2025-07-28 00:39:02 DP1 TP0] KV Cache is allocated. #tokens: 1296952, K size: 29.68 GB, V size: 29.68 GB
[2025-07-28 00:39:02 DP0 TP0] Memory pool end. avail mem=11.24 GB
[2025-07-28 00:39:02 DP1 TP0] Memory pool end. avail mem=11.24 GB
[2025-07-28 00:39:02 DP1 TP3] KV Cache is allocated. #tokens: 1296952, K size: 29.68 GB, V size: 29.68 GB
[2025-07-28 00:39:02 DP1 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.60 GB
[2025-07-28 00:39:02 DP0 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.60 GB
[2025-07-28 00:39:02 DP0 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.58 GB):   0%|          | 0/23 [00:00<?, ?it/s][2025-07-28 00:39:02 DP1 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.58 GB):   0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.58 GB):   4%|▍         | 1/23 [00:01<00:23,  1.09s/it]
Capturing batches (bs=152 avail_mem=10.33 GB):   4%|▍         | 1/23 [00:01<00:23,  1.09s/it]
Capturing batches (bs=160 avail_mem=10.58 GB):   4%|▍         | 1/23 [00:01<00:25,  1.14s/it]
Capturing batches (bs=152 avail_mem=10.33 GB):   4%|▍         | 1/23 [00:01<00:25,  1.14s/it]
Capturing batches (bs=152 avail_mem=10.33 GB):   9%|▊         | 2/23 [00:01<00:14,  1.45it/s]
Capturing batches (bs=144 avail_mem=10.21 GB):   9%|▊         | 2/23 [00:01<00:14,  1.45it/s]
Capturing batches (bs=152 avail_mem=10.33 GB):   9%|▊         | 2/23 [00:01<00:14,  1.43it/s]
Capturing batches (bs=144 avail_mem=10.21 GB):   9%|▊         | 2/23 [00:01<00:14,  1.43it/s]
Capturing batches (bs=144 avail_mem=10.21 GB):  13%|█▎        | 3/23 [00:02<00:12,  1.63it/s]
Capturing batches (bs=136 avail_mem=10.11 GB):  13%|█▎        | 3/23 [00:02<00:12,  1.63it/s]
Capturing batches (bs=144 avail_mem=10.21 GB):  13%|█▎        | 3/23 [00:02<00:12,  1.61it/s]
Capturing batches (bs=136 avail_mem=10.11 GB):  13%|█▎        | 3/23 [00:02<00:12,  1.61it/s]
Capturing batches (bs=136 avail_mem=10.11 GB):  17%|█▋        | 4/23 [00:02<00:09,  2.00it/s]
Capturing batches (bs=128 avail_mem=10.00 GB):  17%|█▋        | 4/23 [00:02<00:09,  2.00it/s]
Capturing batches (bs=136 avail_mem=10.11 GB):  17%|█▋        | 4/23 [00:02<00:09,  1.98it/s]
Capturing batches (bs=128 avail_mem=10.00 GB):  17%|█▋        | 4/23 [00:02<00:09,  1.98it/s]
Capturing batches (bs=128 avail_mem=10.00 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.30it/s]
Capturing batches (bs=120 avail_mem=9.92 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.30it/s] 
Capturing batches (bs=128 avail_mem=10.00 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.26it/s]
Capturing batches (bs=120 avail_mem=9.92 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.26it/s] 
Capturing batches (bs=120 avail_mem=9.92 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.52it/s]
Capturing batches (bs=112 avail_mem=9.82 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.52it/s]
Capturing batches (bs=120 avail_mem=9.92 GB):  26%|██▌       | 6/23 [00:03<00:06,  2.47it/s]
Capturing batches (bs=112 avail_mem=9.82 GB):  26%|██▌       | 6/23 [00:03<00:06,  2.47it/s]
Capturing batches (bs=112 avail_mem=9.82 GB):  30%|███       | 7/23 [00:03<00:05,  2.68it/s]
Capturing batches (bs=104 avail_mem=9.74 GB):  30%|███       | 7/23 [00:03<00:05,  2.68it/s]
Capturing batches (bs=112 avail_mem=9.82 GB):  30%|███       | 7/23 [00:03<00:06,  2.61it/s]
Capturing batches (bs=104 avail_mem=9.74 GB):  30%|███       | 7/23 [00:03<00:06,  2.61it/s]
Capturing batches (bs=104 avail_mem=9.74 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.74it/s]
Capturing batches (bs=96 avail_mem=9.65 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.74it/s] 
Capturing batches (bs=104 avail_mem=9.74 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.74it/s]
Capturing batches (bs=96 avail_mem=9.65 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.74it/s] 
Capturing batches (bs=96 avail_mem=9.65 GB):  39%|███▉      | 9/23 [00:03<00:04,  2.86it/s]
Capturing batches (bs=88 avail_mem=9.58 GB):  39%|███▉      | 9/23 [00:03<00:04,  2.86it/s]
Capturing batches (bs=96 avail_mem=9.65 GB):  39%|███▉      | 9/23 [00:04<00:04,  2.85it/s]
Capturing batches (bs=88 avail_mem=9.58 GB):  39%|███▉      | 9/23 [00:04<00:04,  2.85it/s]
Capturing batches (bs=88 avail_mem=9.58 GB):  43%|████▎     | 10/23 [00:04<00:04,  2.98it/s]
Capturing batches (bs=80 avail_mem=9.50 GB):  43%|████▎     | 10/23 [00:04<00:04,  2.98it/s]
Capturing batches (bs=88 avail_mem=9.58 GB):  43%|████▎     | 10/23 [00:04<00:04,  2.89it/s]
Capturing batches (bs=80 avail_mem=9.50 GB):  43%|████▎     | 10/23 [00:04<00:04,  2.89it/s]
Capturing batches (bs=80 avail_mem=9.50 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.03it/s]
Capturing batches (bs=72 avail_mem=9.49 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.03it/s]
Capturing batches (bs=80 avail_mem=9.50 GB):  48%|████▊     | 11/23 [00:04<00:04,  2.98it/s]
Capturing batches (bs=72 avail_mem=9.49 GB):  48%|████▊     | 11/23 [00:04<00:04,  2.98it/s]
Capturing batches (bs=72 avail_mem=9.49 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.11it/s]
Capturing batches (bs=64 avail_mem=9.41 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.11it/s]
Capturing batches (bs=72 avail_mem=9.49 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.10it/s]
Capturing batches (bs=64 avail_mem=9.41 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.10it/s]
Capturing batches (bs=64 avail_mem=9.41 GB):  57%|█████▋    | 13/23 [00:05<00:03,  3.14it/s]
Capturing batches (bs=56 avail_mem=9.37 GB):  57%|█████▋    | 13/23 [00:05<00:03,  3.14it/s]
Capturing batches (bs=64 avail_mem=9.41 GB):  57%|█████▋    | 13/23 [00:05<00:03,  3.14it/s]
Capturing batches (bs=56 avail_mem=9.37 GB):  57%|█████▋    | 13/23 [00:05<00:03,  3.14it/s]
Capturing batches (bs=56 avail_mem=9.37 GB):  61%|██████    | 14/23 [00:05<00:02,  3.20it/s]
Capturing batches (bs=48 avail_mem=9.31 GB):  61%|██████    | 14/23 [00:05<00:02,  3.20it/s]
Capturing batches (bs=56 avail_mem=9.37 GB):  61%|██████    | 14/23 [00:05<00:02,  3.20it/s]
Capturing batches (bs=48 avail_mem=9.31 GB):  61%|██████    | 14/23 [00:05<00:02,  3.20it/s]
Capturing batches (bs=48 avail_mem=9.31 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.24it/s]
Capturing batches (bs=40 avail_mem=9.30 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.24it/s]
Capturing batches (bs=48 avail_mem=9.31 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.24it/s]
Capturing batches (bs=40 avail_mem=9.30 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.24it/s]
Capturing batches (bs=40 avail_mem=9.30 GB):  70%|██████▉   | 16/23 [00:06<00:02,  3.27it/s]
Capturing batches (bs=32 avail_mem=9.25 GB):  70%|██████▉   | 16/23 [00:06<00:02,  3.27it/s]
Capturing batches (bs=40 avail_mem=9.30 GB):  70%|██████▉   | 16/23 [00:06<00:02,  3.27it/s]
Capturing batches (bs=32 avail_mem=9.25 GB):  70%|██████▉   | 16/23 [00:06<00:02,  3.27it/s]
Capturing batches (bs=32 avail_mem=9.25 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.29it/s]
Capturing batches (bs=24 avail_mem=9.24 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.29it/s]
Capturing batches (bs=32 avail_mem=9.25 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.29it/s]
Capturing batches (bs=24 avail_mem=9.24 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.29it/s]
Capturing batches (bs=24 avail_mem=9.24 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.32it/s]
Capturing batches (bs=16 avail_mem=9.20 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.32it/s]
Capturing batches (bs=24 avail_mem=9.24 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.31it/s]
Capturing batches (bs=16 avail_mem=9.20 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.31it/s]
Capturing batches (bs=16 avail_mem=9.20 GB):  83%|████████▎ | 19/23 [00:07<00:01,  3.34it/s]
Capturing batches (bs=8 avail_mem=9.19 GB):  83%|████████▎ | 19/23 [00:07<00:01,  3.34it/s] 
Capturing batches (bs=16 avail_mem=9.20 GB):  83%|████████▎ | 19/23 [00:07<00:01,  3.33it/s]
Capturing batches (bs=8 avail_mem=9.19 GB):  83%|████████▎ | 19/23 [00:07<00:01,  3.33it/s] 
Capturing batches (bs=8 avail_mem=9.19 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.35it/s]
Capturing batches (bs=4 avail_mem=9.16 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.35it/s]
Capturing batches (bs=8 avail_mem=9.19 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.34it/s]
Capturing batches (bs=4 avail_mem=9.16 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.34it/s]
Capturing batches (bs=4 avail_mem=9.16 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.34it/s]
Capturing batches (bs=2 avail_mem=9.15 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.34it/s]
Capturing batches (bs=4 avail_mem=9.16 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.34it/s]
Capturing batches (bs=2 avail_mem=9.15 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.34it/s]
Capturing batches (bs=2 avail_mem=9.15 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.19it/s]
Capturing batches (bs=1 avail_mem=9.13 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.19it/s]
Capturing batches (bs=2 avail_mem=9.15 GB):  96%|█████████▌| 22/23 [00:08<00:00,  3.20it/s]
Capturing batches (bs=1 avail_mem=9.13 GB):  96%|█████████▌| 22/23 [00:08<00:00,  3.20it/s][2025-07-28 00:39:10 DP0 TP3] Registering 2231 cuda graph addresses

Capturing batches (bs=1 avail_mem=9.13 GB): 100%|██████████| 23/23 [00:08<00:00,  3.26it/s]
Capturing batches (bs=1 avail_mem=9.13 GB): 100%|██████████| 23/23 [00:08<00:00,  2.79it/s]
[2025-07-28 00:39:10 DP0 TP2] Registering 2231 cuda graph addresses
[2025-07-28 00:39:10 DP0 TP0] Registering 2231 cuda graph addresses
[2025-07-28 00:39:10 DP0 TP1] Registering 2231 cuda graph addresses
[2025-07-28 00:39:11 DP0 TP0] Capture cuda graph end. Time elapsed: 8.44 s. mem usage=1.48 GB. avail mem=9.12 GB.
[2025-07-28 00:39:11 DP1 TP2] Registering 2231 cuda graph addresses
[2025-07-28 00:39:11 DP1 TP1] Registering 2231 cuda graph addresses

Capturing batches (bs=1 avail_mem=9.13 GB): 100%|██████████| 23/23 [00:08<00:00,  3.25it/s]
Capturing batches (bs=1 avail_mem=9.13 GB): 100%|██████████| 23/23 [00:08<00:00,  2.77it/s]
[2025-07-28 00:39:11 DP1 TP0] Registering 2231 cuda graph addresses
[2025-07-28 00:39:11 DP1 TP3] Registering 2231 cuda graph addresses
[2025-07-28 00:39:11 DP1 TP0] Capture cuda graph end. Time elapsed: 8.58 s. mem usage=1.48 GB. avail mem=9.12 GB.
[2025-07-28 00:39:11 DP0 TP0] max_total_num_tokens=1296952, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=9.12 GB
[2025-07-28 00:39:11 DP1 TP0] max_total_num_tokens=1296952, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=9.12 GB
[2025-07-28 00:39:11] INFO:     Started server process [1768721]
[2025-07-28 00:39:11] INFO:     Waiting for application startup.
[2025-07-28 00:39:11] INFO:     Application startup complete.
[2025-07-28 00:39:11] INFO:     Uvicorn running on http://127.0.0.1:8003 (Press CTRL+C to quit)
[2025-07-28 00:39:12] INFO:     127.0.0.1:36060 - "GET /get_model_info HTTP/1.1" 200 OK
[2025-07-28 00:39:13 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:39:13 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:39:13] INFO:     127.0.0.1:36074 - "GET /health HTTP/1.1" 200 OK
[2025-07-28 00:39:13 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 256, #cached-token: 0, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:39:13 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 215, #cached-token: 0, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:39:14] INFO:     127.0.0.1:36064 - "POST /generate HTTP/1.1" 200 OK
[2025-07-28 00:39:14] The server is fired up and ready to roll!
[2025-07-28 00:39:14 DP0 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 15.51, #queue-req: 0, 
[2025-07-28 00:39:14 DP1 TP0] Decode batch. #running-req: 1, #token: 255, token usage: 0.00, cuda graph: True, gen throughput (token/s): 15.46, #queue-req: 0, 
[2025-07-28 00:39:14 DP0 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.31, #queue-req: 0, 
[2025-07-28 00:39:14 DP1 TP0] Decode batch. #running-req: 1, #token: 295, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.46, #queue-req: 0, 
[2025-07-28 00:39:14 DP0 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.40, #queue-req: 0, 
[2025-07-28 00:39:15 DP1 TP0] Decode batch. #running-req: 1, #token: 335, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.55, #queue-req: 0, 
[2025-07-28 00:39:15 DP0 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.39, #queue-req: 0, 
[2025-07-28 00:39:15 DP1 TP0] Decode batch. #running-req: 1, #token: 375, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.57, #queue-req: 0, 
[2025-07-28 00:39:15 DP0 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.24, #queue-req: 0, 
[2025-07-28 00:39:15 DP1 TP0] Decode batch. #running-req: 1, #token: 415, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.74, #queue-req: 0, 
[2025-07-28 00:39:15 DP0 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 00:39:15 DP1 TP0] Decode batch. #running-req: 1, #token: 455, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 00:39:16 DP0 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.91, #queue-req: 0, 
[2025-07-28 00:39:16] INFO:     127.0.0.1:36102 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:39:16 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 106, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:39:16] INFO:     127.0.0.1:36086 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:39:16 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 207, #cached-token: 107, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:39:16 DP1 TP0] Decode batch. #running-req: 1, #token: 324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 73.67, #queue-req: 0, 
[2025-07-28 00:39:16 DP0 TP0] Decode batch. #running-req: 1, #token: 217, token usage: 0.00, cuda graph: True, gen throughput (token/s): 76.48, #queue-req: 0, 
[2025-07-28 00:39:16 DP1 TP0] Decode batch. #running-req: 1, #token: 364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.81, #queue-req: 0, 
[2025-07-28 00:39:16 DP0 TP0] Decode batch. #running-req: 1, #token: 257, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.45, #queue-req: 0, 
[2025-07-28 00:39:17 DP1 TP0] Decode batch. #running-req: 1, #token: 404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.34, #queue-req: 0, 
[2025-07-28 00:39:17 DP0 TP0] Decode batch. #running-req: 1, #token: 297, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.23, #queue-req: 0, 
[2025-07-28 00:39:17 DP1 TP0] Decode batch. #running-req: 1, #token: 444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 00:39:17 DP0 TP0] Decode batch. #running-req: 1, #token: 337, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 00:39:17 DP1 TP0] Decode batch. #running-req: 1, #token: 484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 00:39:17 DP0 TP0] Decode batch. #running-req: 1, #token: 377, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.44, #queue-req: 0, 
[2025-07-28 00:39:17 DP1 TP0] Decode batch. #running-req: 1, #token: 524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.36, #queue-req: 0, 
[2025-07-28 00:39:18 DP0 TP0] Decode batch. #running-req: 1, #token: 417, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.12, #queue-req: 0, 
[2025-07-28 00:39:18 DP1 TP0] Decode batch. #running-req: 1, #token: 564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 00:39:18 DP0 TP0] Decode batch. #running-req: 1, #token: 457, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 00:39:18 DP1 TP0] Decode batch. #running-req: 1, #token: 604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 00:39:18 DP0 TP0] Decode batch. #running-req: 1, #token: 497, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 00:39:18 DP1 TP0] Decode batch. #running-req: 1, #token: 644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 00:39:18 DP0 TP0] Decode batch. #running-req: 1, #token: 537, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.92, #queue-req: 0, 
[2025-07-28 00:39:19 DP1 TP0] Decode batch. #running-req: 1, #token: 684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.23, #queue-req: 0, 
[2025-07-28 00:39:19 DP0 TP0] Decode batch. #running-req: 1, #token: 577, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.78, #queue-req: 0, 
[2025-07-28 00:39:19] INFO:     127.0.0.1:36118 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:39:19 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 107, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:39:19 DP1 TP0] Decode batch. #running-req: 1, #token: 724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 00:39:19 DP0 TP0] Decode batch. #running-req: 1, #token: 263, token usage: 0.00, cuda graph: True, gen throughput (token/s): 109.53, #queue-req: 0, 
[2025-07-28 00:39:19] INFO:     127.0.0.1:36122 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:39:19 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 133, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:39:19 DP1 TP0] Decode batch. #running-req: 1, #token: 247, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.78, #queue-req: 0, 
[2025-07-28 00:39:19 DP0 TP0] Decode batch. #running-req: 1, #token: 303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.73, #queue-req: 0, 
[2025-07-28 00:39:19 DP1 TP0] Decode batch. #running-req: 1, #token: 287, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.37, #queue-req: 0, 
[2025-07-28 00:39:20 DP0 TP0] Decode batch. #running-req: 1, #token: 343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.09, #queue-req: 0, 
[2025-07-28 00:39:20 DP1 TP0] Decode batch. #running-req: 1, #token: 327, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.47, #queue-req: 0, 
[2025-07-28 00:39:20 DP0 TP0] Decode batch. #running-req: 1, #token: 383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 00:39:20 DP1 TP0] Decode batch. #running-req: 1, #token: 367, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.61, #queue-req: 0, 
[2025-07-28 00:39:20 DP0 TP0] Decode batch. #running-req: 1, #token: 423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.08, #queue-req: 0, 
[2025-07-28 00:39:20 DP1 TP0] Decode batch. #running-req: 1, #token: 407, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 00:39:21 DP0 TP0] Decode batch. #running-req: 1, #token: 463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 00:39:21 DP1 TP0] Decode batch. #running-req: 1, #token: 447, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 00:39:21 DP0 TP0] Decode batch. #running-req: 1, #token: 503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.10, #queue-req: 0, 
[2025-07-28 00:39:21 DP1 TP0] Decode batch. #running-req: 1, #token: 487, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.50, #queue-req: 0, 
[2025-07-28 00:39:21 DP0 TP0] Decode batch. #running-req: 1, #token: 543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 00:39:21 DP1 TP0] Decode batch. #running-req: 1, #token: 527, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 00:39:21 DP0 TP0] Decode batch. #running-req: 1, #token: 583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.02, #queue-req: 0, 
[2025-07-28 00:39:22 DP1 TP0] Decode batch. #running-req: 1, #token: 567, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 00:39:22] INFO:     127.0.0.1:36126 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:39:22 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 186, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:39:22 DP0 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 111.45, #queue-req: 0, 
[2025-07-28 00:39:22 DP1 TP0] Decode batch. #running-req: 1, #token: 607, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.40, #queue-req: 0, 
[2025-07-28 00:39:22 DP0 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.10, #queue-req: 0, 
[2025-07-28 00:39:22 DP1 TP0] Decode batch. #running-req: 1, #token: 647, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 00:39:22 DP0 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 00:39:22 DP1 TP0] Decode batch. #running-req: 1, #token: 687, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.42, #queue-req: 0, 
[2025-07-28 00:39:23 DP0 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.08, #queue-req: 0, 
[2025-07-28 00:39:23 DP1 TP0] Decode batch. #running-req: 1, #token: 727, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.86, #queue-req: 0, 
[2025-07-28 00:39:23 DP0 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.00, #queue-req: 0, 
[2025-07-28 00:39:23] INFO:     127.0.0.1:36142 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:39:23 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 173, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:39:23 DP1 TP0] Decode batch. #running-req: 1, #token: 288, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.51, #queue-req: 0, 
[2025-07-28 00:39:23 DP0 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.70, #queue-req: 0, 
[2025-07-28 00:39:23 DP1 TP0] Decode batch. #running-req: 1, #token: 328, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.60, #queue-req: 0, 
[2025-07-28 00:39:23 DP0 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.58, #queue-req: 0, 
[2025-07-28 00:39:24 DP1 TP0] Decode batch. #running-req: 1, #token: 368, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.56, #queue-req: 0, 
[2025-07-28 00:39:24 DP0 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 00:39:24 DP1 TP0] Decode batch. #running-req: 1, #token: 408, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.51, #queue-req: 0, 
[2025-07-28 00:39:24 DP0 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.12, #queue-req: 0, 
[2025-07-28 00:39:24 DP1 TP0] Decode batch. #running-req: 1, #token: 448, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.39, #queue-req: 0, 
[2025-07-28 00:39:24 DP0 TP0] Decode batch. #running-req: 1, #token: 656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 132.56, #queue-req: 0, 
[2025-07-28 00:39:24 DP1 TP0] Decode batch. #running-req: 1, #token: 488, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.24, #queue-req: 0, 
[2025-07-28 00:39:25 DP0 TP0] Decode batch. #running-req: 1, #token: 696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.84, #queue-req: 0, 
[2025-07-28 00:39:25 DP1 TP0] Decode batch. #running-req: 1, #token: 528, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.31, #queue-req: 0, 
[2025-07-28 00:39:25 DP0 TP0] Decode batch. #running-req: 1, #token: 736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.00, #queue-req: 0, 
[2025-07-28 00:39:25 DP1 TP0] Decode batch. #running-req: 1, #token: 568, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 00:39:25] INFO:     127.0.0.1:36156 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:39:25 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 226, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:39:25 DP0 TP0] Decode batch. #running-req: 1, #token: 346, token usage: 0.00, cuda graph: True, gen throughput (token/s): 110.11, #queue-req: 0, 
[2025-07-28 00:39:25 DP1 TP0] Decode batch. #running-req: 1, #token: 608, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 00:39:26 DP0 TP0] Decode batch. #running-req: 1, #token: 386, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 00:39:26 DP1 TP0] Decode batch. #running-req: 1, #token: 648, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.34, #queue-req: 0, 
[2025-07-28 00:39:26 DP0 TP0] Decode batch. #running-req: 1, #token: 426, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 00:39:26 DP1 TP0] Decode batch. #running-req: 1, #token: 688, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 00:39:26 DP0 TP0] Decode batch. #running-req: 1, #token: 466, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.14, #queue-req: 0, 
[2025-07-28 00:39:26 DP1 TP0] Decode batch. #running-req: 1, #token: 728, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.46, #queue-req: 0, 
[2025-07-28 00:39:26] INFO:     127.0.0.1:40186 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:39:26 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 168, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:39:26 DP0 TP0] Decode batch. #running-req: 1, #token: 506, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.31, #queue-req: 0, 
[2025-07-28 00:39:27 DP1 TP0] Decode batch. #running-req: 1, #token: 304, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.26, #queue-req: 0, 
[2025-07-28 00:39:27 DP0 TP0] Decode batch. #running-req: 1, #token: 546, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 00:39:27 DP1 TP0] Decode batch. #running-req: 1, #token: 344, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.57, #queue-req: 0, 
[2025-07-28 00:39:27 DP0 TP0] Decode batch. #running-req: 1, #token: 586, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.13, #queue-req: 0, 
[2025-07-28 00:39:27 DP1 TP0] Decode batch. #running-req: 1, #token: 384, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.51, #queue-req: 0, 
[2025-07-28 00:39:27 DP0 TP0] Decode batch. #running-req: 1, #token: 626, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.04, #queue-req: 0, 
[2025-07-28 00:39:27 DP1 TP0] Decode batch. #running-req: 1, #token: 424, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.98, #queue-req: 0, 
[2025-07-28 00:39:28 DP0 TP0] Decode batch. #running-req: 1, #token: 666, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 00:39:28] INFO:     127.0.0.1:40188 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:39:28 DP1 TP0] Decode batch. #running-req: 1, #token: 464, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.47, #queue-req: 0, 
[2025-07-28 00:39:28 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 154, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:39:28 DP0 TP0] Decode batch. #running-req: 1, #token: 292, token usage: 0.00, cuda graph: True, gen throughput (token/s): 110.74, #queue-req: 0, 
[2025-07-28 00:39:28 DP1 TP0] Decode batch. #running-req: 1, #token: 504, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 00:39:28 DP0 TP0] Decode batch. #running-req: 1, #token: 332, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.99, #queue-req: 0, 
[2025-07-28 00:39:28 DP1 TP0] Decode batch. #running-req: 1, #token: 544, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.34, #queue-req: 0, 
[2025-07-28 00:39:29 DP0 TP0] Decode batch. #running-req: 1, #token: 372, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 00:39:29 DP1 TP0] Decode batch. #running-req: 1, #token: 584, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 00:39:29 DP0 TP0] Decode batch. #running-req: 1, #token: 412, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 00:39:29 DP1 TP0] Decode batch. #running-req: 1, #token: 624, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 00:39:29] INFO:     127.0.0.1:40198 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:39:29 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 141, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:39:29 DP0 TP0] Decode batch. #running-req: 1, #token: 452, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 00:39:29 DP1 TP0] Decode batch. #running-req: 1, #token: 258, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.03, #queue-req: 0, 
[2025-07-28 00:39:29 DP0 TP0] Decode batch. #running-req: 1, #token: 492, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.14, #queue-req: 0, 
[2025-07-28 00:39:30 DP1 TP0] Decode batch. #running-req: 1, #token: 298, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.54, #queue-req: 0, 
[2025-07-28 00:39:30 DP0 TP0] Decode batch. #running-req: 1, #token: 532, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 00:39:30 DP1 TP0] Decode batch. #running-req: 1, #token: 338, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.63, #queue-req: 0, 
[2025-07-28 00:39:30 DP0 TP0] Decode batch. #running-req: 1, #token: 572, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 00:39:30 DP1 TP0] Decode batch. #running-req: 1, #token: 378, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.62, #queue-req: 0, 
[2025-07-28 00:39:30 DP0 TP0] Decode batch. #running-req: 1, #token: 612, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.01, #queue-req: 0, 
[2025-07-28 00:39:30 DP1 TP0] Decode batch. #running-req: 1, #token: 418, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.49, #queue-req: 0, 
[2025-07-28 00:39:31] INFO:     127.0.0.1:40214 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:39:31 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 192, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:39:31 DP0 TP0] Decode batch. #running-req: 1, #token: 312, token usage: 0.00, cuda graph: True, gen throughput (token/s): 116.04, #queue-req: 0, 
[2025-07-28 00:39:31 DP1 TP0] Decode batch. #running-req: 1, #token: 458, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.43, #queue-req: 0, 
[2025-07-28 00:39:31 DP0 TP0] Decode batch. #running-req: 1, #token: 352, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 00:39:31 DP1 TP0] Decode batch. #running-req: 1, #token: 498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 00:39:31 DP0 TP0] Decode batch. #running-req: 1, #token: 392, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 00:39:31 DP1 TP0] Decode batch. #running-req: 1, #token: 538, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 00:39:32 DP0 TP0] Decode batch. #running-req: 1, #token: 432, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.12, #queue-req: 0, 
[2025-07-28 00:39:32 DP1 TP0] Decode batch. #running-req: 1, #token: 578, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 00:39:32 DP0 TP0] Decode batch. #running-req: 1, #token: 472, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.92, #queue-req: 0, 
[2025-07-28 00:39:32 DP1 TP0] Decode batch. #running-req: 1, #token: 618, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.36, #queue-req: 0, 
[2025-07-28 00:39:32 DP0 TP0] Decode batch. #running-req: 1, #token: 512, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.17, #queue-req: 0, 
[2025-07-28 00:39:32 DP1 TP0] Decode batch. #running-req: 1, #token: 658, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 00:39:32 DP0 TP0] Decode batch. #running-req: 1, #token: 552, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 00:39:32 DP1 TP0] Decode batch. #running-req: 1, #token: 698, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 00:39:33] INFO:     127.0.0.1:40226 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:39:33 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 190, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:39:33 DP0 TP0] Decode batch. #running-req: 1, #token: 592, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 00:39:33 DP1 TP0] Decode batch. #running-req: 1, #token: 304, token usage: 0.00, cuda graph: True, gen throughput (token/s): 114.93, #queue-req: 0, 
[2025-07-28 00:39:33 DP0 TP0] Decode batch. #running-req: 1, #token: 632, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.10, #queue-req: 0, 
[2025-07-28 00:39:33 DP1 TP0] Decode batch. #running-req: 1, #token: 344, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.62, #queue-req: 0, 
[2025-07-28 00:39:33 DP0 TP0] Decode batch. #running-req: 1, #token: 672, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 00:39:33 DP1 TP0] Decode batch. #running-req: 1, #token: 384, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.66, #queue-req: 0, 
[2025-07-28 00:39:34 DP0 TP0] Decode batch. #running-req: 1, #token: 712, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.24, #queue-req: 0, 
[2025-07-28 00:39:34 DP1 TP0] Decode batch. #running-req: 1, #token: 424, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.53, #queue-req: 0, 
[2025-07-28 00:39:34 DP0 TP0] Decode batch. #running-req: 1, #token: 752, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.24, #queue-req: 0, 
[2025-07-28 00:39:34 DP1 TP0] Decode batch. #running-req: 1, #token: 464, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.53, #queue-req: 0, 
[2025-07-28 00:39:34 DP0 TP0] Decode batch. #running-req: 1, #token: 792, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 00:39:34 DP1 TP0] Decode batch. #running-req: 1, #token: 504, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.54, #queue-req: 0, 
[2025-07-28 00:39:34 DP0 TP0] Decode batch. #running-req: 1, #token: 832, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.17, #queue-req: 0, 
[2025-07-28 00:39:35 DP1 TP0] Decode batch. #running-req: 1, #token: 544, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.36, #queue-req: 0, 
[2025-07-28 00:39:35 DP0 TP0] Decode batch. #running-req: 1, #token: 872, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.05, #queue-req: 0, 
[2025-07-28 00:39:35 DP1 TP0] Decode batch. #running-req: 1, #token: 584, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.43, #queue-req: 0, 
[2025-07-28 00:39:35 DP0 TP0] Decode batch. #running-req: 1, #token: 912, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.13, #queue-req: 0, 
[2025-07-28 00:39:35] INFO:     127.0.0.1:40234 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:39:35 DP1 TP0] Decode batch. #running-req: 1, #token: 624, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 00:39:35 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 137, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:39:35 DP1 TP0] Decode batch. #running-req: 1, #token: 664, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.40, #queue-req: 0, 
[2025-07-28 00:39:35 DP0 TP0] Decode batch. #running-req: 1, #token: 279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 113.13, #queue-req: 0, 
[2025-07-28 00:39:36 DP1 TP0] Decode batch. #running-req: 1, #token: 704, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.42, #queue-req: 0, 
[2025-07-28 00:39:36 DP0 TP0] Decode batch. #running-req: 1, #token: 319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.93, #queue-req: 0, 
[2025-07-28 00:39:36 DP1 TP0] Decode batch. #running-req: 1, #token: 744, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.42, #queue-req: 0, 
[2025-07-28 00:39:36 DP0 TP0] Decode batch. #running-req: 1, #token: 359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 00:39:36 DP1 TP0] Decode batch. #running-req: 1, #token: 784, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.38, #queue-req: 0, 
[2025-07-28 00:39:36 DP0 TP0] Decode batch. #running-req: 1, #token: 399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 00:39:36] INFO:     127.0.0.1:54914 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:39:36 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 132, #cached-token: 110, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:39:37 DP0 TP0] Decode batch. #running-req: 1, #token: 439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 00:39:37 DP1 TP0] Decode batch. #running-req: 1, #token: 280, token usage: 0.00, cuda graph: True, gen throughput (token/s): 116.74, #queue-req: 0, 
[2025-07-28 00:39:37 DP0 TP0] Decode batch. #running-req: 1, #token: 479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 00:39:37 DP1 TP0] Decode batch. #running-req: 1, #token: 320, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.67, #queue-req: 0, 
[2025-07-28 00:39:37 DP0 TP0] Decode batch. #running-req: 1, #token: 519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 00:39:37 DP1 TP0] Decode batch. #running-req: 1, #token: 360, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.61, #queue-req: 0, 
[2025-07-28 00:39:37 DP0 TP0] Decode batch. #running-req: 1, #token: 559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 00:39:38 DP1 TP0] Decode batch. #running-req: 1, #token: 400, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.56, #queue-req: 0, 
[2025-07-28 00:39:38] INFO:     127.0.0.1:54922 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:39:38 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 199, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:39:38 DP1 TP0] Decode batch. #running-req: 1, #token: 440, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.49, #queue-req: 0, 
[2025-07-28 00:39:38 DP0 TP0] Decode batch. #running-req: 1, #token: 326, token usage: 0.00, cuda graph: True, gen throughput (token/s): 116.41, #queue-req: 0, 
[2025-07-28 00:39:38 DP1 TP0] Decode batch. #running-req: 1, #token: 480, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.51, #queue-req: 0, 
[2025-07-28 00:39:38 DP0 TP0] Decode batch. #running-req: 1, #token: 366, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 00:39:38 DP1 TP0] Decode batch. #running-req: 1, #token: 520, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.87, #queue-req: 0, 
[2025-07-28 00:39:38 DP0 TP0] Decode batch. #running-req: 1, #token: 406, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 00:39:39 DP1 TP0] Decode batch. #running-req: 1, #token: 560, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.46, #queue-req: 0, 
[2025-07-28 00:39:39 DP0 TP0] Decode batch. #running-req: 1, #token: 446, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 00:39:39 DP1 TP0] Decode batch. #running-req: 1, #token: 600, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 00:39:39 DP0 TP0] Decode batch. #running-req: 1, #token: 486, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.06, #queue-req: 0, 
[2025-07-28 00:39:39 DP1 TP0] Decode batch. #running-req: 1, #token: 640, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.45, #queue-req: 0, 
[2025-07-28 00:39:39 DP0 TP0] Decode batch. #running-req: 1, #token: 526, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 00:39:40 DP1 TP0] Decode batch. #running-req: 1, #token: 680, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.46, #queue-req: 0, 
[2025-07-28 00:39:40 DP0 TP0] Decode batch. #running-req: 1, #token: 566, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 00:39:40 DP1 TP0] Decode batch. #running-req: 1, #token: 720, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 00:39:40 DP0 TP0] Decode batch. #running-req: 1, #token: 606, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.13, #queue-req: 0, 
[2025-07-28 00:39:40] INFO:     127.0.0.1:54936 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:39:40 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 194, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:39:40 DP0 TP0] Decode batch. #running-req: 1, #token: 646, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.07, #queue-req: 0, 
[2025-07-28 00:39:40 DP1 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 116.59, #queue-req: 0, 
[2025-07-28 00:39:40 DP0 TP0] Decode batch. #running-req: 1, #token: 686, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.12, #queue-req: 0, 
[2025-07-28 00:39:40 DP1 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.56, #queue-req: 0, 
[2025-07-28 00:39:41 DP0 TP0] Decode batch. #running-req: 1, #token: 726, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.23, #queue-req: 0, 
[2025-07-28 00:39:41 DP1 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.46, #queue-req: 0, 
[2025-07-28 00:39:41 DP0 TP0] Decode batch. #running-req: 1, #token: 766, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 00:39:41 DP1 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.51, #queue-req: 0, 
[2025-07-28 00:39:41 DP0 TP0] Decode batch. #running-req: 1, #token: 806, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.00, #queue-req: 0, 
[2025-07-28 00:39:41 DP1 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 00:39:42 DP0 TP0] Decode batch. #running-req: 1, #token: 846, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.01, #queue-req: 0, 
[2025-07-28 00:39:42 DP1 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.42, #queue-req: 0, 
[2025-07-28 00:39:42] INFO:     127.0.0.1:54946 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:39:42 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 167, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:39:42 DP1 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.46, #queue-req: 0, 
[2025-07-28 00:39:42 DP0 TP0] Decode batch. #running-req: 1, #token: 295, token usage: 0.00, cuda graph: True, gen throughput (token/s): 116.89, #queue-req: 0, 
[2025-07-28 00:39:42 DP1 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.40, #queue-req: 0, 
[2025-07-28 00:39:42 DP0 TP0] Decode batch. #running-req: 1, #token: 335, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 00:39:43 DP1 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.48, #queue-req: 0, 
[2025-07-28 00:39:43 DP0 TP0] Decode batch. #running-req: 1, #token: 375, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 00:39:43 DP1 TP0] Decode batch. #running-req: 1, #token: 676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.24, #queue-req: 0, 
[2025-07-28 00:39:43] INFO:     127.0.0.1:54954 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:39:43 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 172, #cached-token: 110, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:39:43 DP0 TP0] Decode batch. #running-req: 1, #token: 415, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 00:39:43 DP0 TP0] Decode batch. #running-req: 1, #token: 455, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 00:39:43 DP1 TP0] Decode batch. #running-req: 1, #token: 321, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.49, #queue-req: 0, 
[2025-07-28 00:39:43 DP0 TP0] Decode batch. #running-req: 1, #token: 495, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 00:39:43 DP1 TP0] Decode batch. #running-req: 1, #token: 361, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.58, #queue-req: 0, 
[2025-07-28 00:39:44 DP0 TP0] Decode batch. #running-req: 1, #token: 535, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 00:39:44 DP1 TP0] Decode batch. #running-req: 1, #token: 401, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.50, #queue-req: 0, 
[2025-07-28 00:39:44 DP0 TP0] Decode batch. #running-req: 1, #token: 575, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 00:39:44 DP1 TP0] Decode batch. #running-req: 1, #token: 441, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.45, #queue-req: 0, 
[2025-07-28 00:39:44 DP0 TP0] Decode batch. #running-req: 1, #token: 615, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.31, #queue-req: 0, 
[2025-07-28 00:39:44 DP1 TP0] Decode batch. #running-req: 1, #token: 481, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.37, #queue-req: 0, 
[2025-07-28 00:39:45 DP0 TP0] Decode batch. #running-req: 1, #token: 655, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 00:39:45 DP1 TP0] Decode batch. #running-req: 1, #token: 521, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.39, #queue-req: 0, 
[2025-07-28 00:39:45 DP0 TP0] Decode batch. #running-req: 1, #token: 695, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.23, #queue-req: 0, 
[2025-07-28 00:39:45 DP1 TP0] Decode batch. #running-req: 1, #token: 561, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.34, #queue-req: 0, 
[2025-07-28 00:39:45 DP0 TP0] Decode batch. #running-req: 1, #token: 735, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 00:39:45 DP1 TP0] Decode batch. #running-req: 1, #token: 601, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.39, #queue-req: 0, 
[2025-07-28 00:39:45 DP0 TP0] Decode batch. #running-req: 1, #token: 775, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.03, #queue-req: 0, 
[2025-07-28 00:39:45 DP1 TP0] Decode batch. #running-req: 1, #token: 641, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.31, #queue-req: 0, 
[2025-07-28 00:39:45] INFO:     127.0.0.1:60652 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:39:46 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 219, #cached-token: 108, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:39:46] INFO:     127.0.0.1:54968 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:39:46 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 155, #cached-token: 111, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:39:46 DP0 TP0] Decode batch. #running-req: 1, #token: 357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 160.40, #queue-req: 0, 
[2025-07-28 00:39:46 DP1 TP0] Decode batch. #running-req: 1, #token: 302, token usage: 0.00, cuda graph: True, gen throughput (token/s): 74.20, #queue-req: 0, 
[2025-07-28 00:39:46 DP0 TP0] Decode batch. #running-req: 1, #token: 397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 00:39:46 DP1 TP0] Decode batch. #running-req: 1, #token: 342, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.46, #queue-req: 0, 
[2025-07-28 00:39:46 DP0 TP0] Decode batch. #running-req: 1, #token: 437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.24, #queue-req: 0, 
[2025-07-28 00:39:47 DP1 TP0] Decode batch. #running-req: 1, #token: 382, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.42, #queue-req: 0, 
[2025-07-28 00:39:47 DP0 TP0] Decode batch. #running-req: 1, #token: 477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 00:39:47 DP1 TP0] Decode batch. #running-req: 1, #token: 422, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 00:39:47 DP0 TP0] Decode batch. #running-req: 1, #token: 517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.01, #queue-req: 0, 
[2025-07-28 00:39:47 DP1 TP0] Decode batch. #running-req: 1, #token: 462, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.31, #queue-req: 0, 
[2025-07-28 00:39:47 DP0 TP0] Decode batch. #running-req: 1, #token: 557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.12, #queue-req: 0, 
[2025-07-28 00:39:47 DP1 TP0] Decode batch. #running-req: 1, #token: 502, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 00:39:48 DP0 TP0] Decode batch. #running-req: 1, #token: 597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.14, #queue-req: 0, 
[2025-07-28 00:39:48 DP1 TP0] Decode batch. #running-req: 1, #token: 542, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 00:39:48 DP0 TP0] Decode batch. #running-req: 1, #token: 637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.11, #queue-req: 0, 
[2025-07-28 00:39:48 DP1 TP0] Decode batch. #running-req: 1, #token: 582, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.14, #queue-req: 0, 
[2025-07-28 00:39:48 DP0 TP0] Decode batch. #running-req: 1, #token: 677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.10, #queue-req: 0, 
[2025-07-28 00:39:48 DP1 TP0] Decode batch. #running-req: 1, #token: 622, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.13, #queue-req: 0, 
[2025-07-28 00:39:48 DP0 TP0] Decode batch. #running-req: 1, #token: 717, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 00:39:49 DP1 TP0] Decode batch. #running-req: 1, #token: 662, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 00:39:49 DP0 TP0] Decode batch. #running-req: 1, #token: 757, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 00:39:49 DP1 TP0] Decode batch. #running-req: 1, #token: 702, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 00:39:49 DP0 TP0] Decode batch. #running-req: 1, #token: 797, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.13, #queue-req: 0, 
[2025-07-28 00:39:49 DP1 TP0] Decode batch. #running-req: 1, #token: 742, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 00:39:49 DP0 TP0] Decode batch. #running-req: 1, #token: 837, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.89, #queue-req: 0, 
[2025-07-28 00:39:49] INFO:     127.0.0.1:60660 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:39:49 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 175, #cached-token: 110, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:39:50 DP1 TP0] Decode batch. #running-req: 1, #token: 782, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.03, #queue-req: 0, 
[2025-07-28 00:39:50 DP0 TP0] Decode batch. #running-req: 1, #token: 298, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.20, #queue-req: 0, 
[2025-07-28 00:39:50 DP1 TP0] Decode batch. #running-req: 1, #token: 822, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 00:39:50 DP0 TP0] Decode batch. #running-req: 1, #token: 338, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.39, #queue-req: 0, 
[2025-07-28 00:39:50] INFO:     127.0.0.1:60662 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:39:50 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 94, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:39:50 DP1 TP0] Decode batch. #running-req: 1, #token: 203, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.04, #queue-req: 0, 
[2025-07-28 00:39:50 DP0 TP0] Decode batch. #running-req: 1, #token: 378, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 00:39:50 DP1 TP0] Decode batch. #running-req: 1, #token: 243, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.64, #queue-req: 0, 
[2025-07-28 00:39:50 DP0 TP0] Decode batch. #running-req: 1, #token: 418, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 00:39:51 DP1 TP0] Decode batch. #running-req: 1, #token: 283, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.49, #queue-req: 0, 
[2025-07-28 00:39:51 DP0 TP0] Decode batch. #running-req: 1, #token: 458, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 00:39:51 DP1 TP0] Decode batch. #running-req: 1, #token: 323, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.59, #queue-req: 0, 
[2025-07-28 00:39:51 DP0 TP0] Decode batch. #running-req: 1, #token: 498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.17, #queue-req: 0, 
[2025-07-28 00:39:51 DP1 TP0] Decode batch. #running-req: 1, #token: 363, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.61, #queue-req: 0, 
[2025-07-28 00:39:51 DP0 TP0] Decode batch. #running-req: 1, #token: 538, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 00:39:52 DP1 TP0] Decode batch. #running-req: 1, #token: 403, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.31, #queue-req: 0, 
[2025-07-28 00:39:52 DP0 TP0] Decode batch. #running-req: 1, #token: 578, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.04, #queue-req: 0, 
[2025-07-28 00:39:52 DP1 TP0] Decode batch. #running-req: 1, #token: 443, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.46, #queue-req: 0, 
[2025-07-28 00:39:52 DP0 TP0] Decode batch. #running-req: 1, #token: 618, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.02, #queue-req: 0, 
[2025-07-28 00:39:52 DP1 TP0] Decode batch. #running-req: 1, #token: 483, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.51, #queue-req: 0, 
[2025-07-28 00:39:52 DP0 TP0] Decode batch. #running-req: 1, #token: 658, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.95, #queue-req: 0, 
[2025-07-28 00:39:52 DP1 TP0] Decode batch. #running-req: 1, #token: 523, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.42, #queue-req: 0, 
[2025-07-28 00:39:53 DP0 TP0] Decode batch. #running-req: 1, #token: 698, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.73, #queue-req: 0, 
[2025-07-28 00:39:53] INFO:     127.0.0.1:60666 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:39:53 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 115, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:39:53] INFO:     127.0.0.1:60680 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:39:53 DP1 TP0] Decode batch. #running-req: 1, #token: 0, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 00:39:53 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 150, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:39:53 DP0 TP0] Decode batch. #running-req: 1, #token: 258, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.37, #queue-req: 0, 
[2025-07-28 00:39:53 DP1 TP0] Decode batch. #running-req: 1, #token: 300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.42, #queue-req: 0, 
[2025-07-28 00:39:53 DP0 TP0] Decode batch. #running-req: 1, #token: 298, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.47, #queue-req: 0, 
[2025-07-28 00:39:53 DP1 TP0] Decode batch. #running-req: 1, #token: 340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.52, #queue-req: 0, 
[2025-07-28 00:39:53 DP0 TP0] Decode batch. #running-req: 1, #token: 338, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.47, #queue-req: 0, 
[2025-07-28 00:39:54 DP1 TP0] Decode batch. #running-req: 1, #token: 380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.57, #queue-req: 0, 
[2025-07-28 00:39:54 DP0 TP0] Decode batch. #running-req: 1, #token: 378, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 00:39:54 DP1 TP0] Decode batch. #running-req: 1, #token: 420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.48, #queue-req: 0, 
[2025-07-28 00:39:54 DP0 TP0] Decode batch. #running-req: 1, #token: 418, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 00:39:54 DP1 TP0] Decode batch. #running-req: 1, #token: 460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 00:39:54 DP0 TP0] Decode batch. #running-req: 1, #token: 458, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.36, #queue-req: 0, 
[2025-07-28 00:39:55 DP1 TP0] Decode batch. #running-req: 1, #token: 500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.43, #queue-req: 0, 
[2025-07-28 00:39:55 DP0 TP0] Decode batch. #running-req: 1, #token: 498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.37, #queue-req: 0, 
[2025-07-28 00:39:55 DP1 TP0] Decode batch. #running-req: 1, #token: 540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 00:39:55 DP0 TP0] Decode batch. #running-req: 1, #token: 538, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 00:39:55 DP1 TP0] Decode batch. #running-req: 1, #token: 580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.37, #queue-req: 0, 
[2025-07-28 00:39:55 DP0 TP0] Decode batch. #running-req: 1, #token: 578, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 00:39:55 DP1 TP0] Decode batch. #running-req: 1, #token: 620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 00:39:56 DP0 TP0] Decode batch. #running-req: 1, #token: 618, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 00:39:56] INFO:     127.0.0.1:53248 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:39:56 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 148, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:39:56 DP1 TP0] Decode batch. #running-req: 1, #token: 660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.37, #queue-req: 0, 
[2025-07-28 00:39:56 DP0 TP0] Decode batch. #running-req: 1, #token: 283, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.98, #queue-req: 0, 
[2025-07-28 00:39:56 DP1 TP0] Decode batch. #running-req: 1, #token: 700, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 00:39:56 DP0 TP0] Decode batch. #running-req: 1, #token: 323, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.36, #queue-req: 0, 
[2025-07-28 00:39:56 DP1 TP0] Decode batch. #running-req: 1, #token: 740, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.34, #queue-req: 0, 
[2025-07-28 00:39:56 DP0 TP0] Decode batch. #running-req: 1, #token: 363, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.30, #queue-req: 0, 
[2025-07-28 00:39:57 DP1 TP0] Decode batch. #running-req: 1, #token: 780, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.39, #queue-req: 0, 
[2025-07-28 00:39:57 DP0 TP0] Decode batch. #running-req: 1, #token: 403, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 00:39:57 DP1 TP0] Decode batch. #running-req: 1, #token: 820, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 00:39:57 DP0 TP0] Decode batch. #running-req: 1, #token: 443, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.23, #queue-req: 0, 
[2025-07-28 00:39:57 DP1 TP0] Decode batch. #running-req: 1, #token: 860, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 00:39:57 DP0 TP0] Decode batch. #running-req: 1, #token: 483, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 00:39:57 DP1 TP0] Decode batch. #running-req: 1, #token: 900, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 00:39:58] INFO:     127.0.0.1:53264 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:39:58 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 160, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:39:58 DP0 TP0] Decode batch. #running-req: 1, #token: 523, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 00:39:58 DP1 TP0] Decode batch. #running-req: 1, #token: 301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 118.81, #queue-req: 0, 
[2025-07-28 00:39:58 DP0 TP0] Decode batch. #running-req: 1, #token: 563, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.21, #queue-req: 0, 
[2025-07-28 00:39:58 DP1 TP0] Decode batch. #running-req: 1, #token: 341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.65, #queue-req: 0, 
[2025-07-28 00:39:58 DP0 TP0] Decode batch. #running-req: 1, #token: 603, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.09, #queue-req: 0, 
[2025-07-28 00:39:58 DP1 TP0] Decode batch. #running-req: 1, #token: 381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.56, #queue-req: 0, 
[2025-07-28 00:39:58 DP0 TP0] Decode batch. #running-req: 1, #token: 643, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 00:39:59 DP1 TP0] Decode batch. #running-req: 1, #token: 421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.56, #queue-req: 0, 
[2025-07-28 00:39:59 DP0 TP0] Decode batch. #running-req: 1, #token: 683, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 00:39:59 DP1 TP0] Decode batch. #running-req: 1, #token: 461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.51, #queue-req: 0, 
[2025-07-28 00:39:59 DP0 TP0] Decode batch. #running-req: 1, #token: 723, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 00:39:59 DP1 TP0] Decode batch. #running-req: 1, #token: 501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.58, #queue-req: 0, 
[2025-07-28 00:39:59 DP0 TP0] Decode batch. #running-req: 1, #token: 763, token usage: 0.00, cuda graph: True, gen throughput (token/s): 132.18, #queue-req: 0, 
[2025-07-28 00:40:00 DP1 TP0] Decode batch. #running-req: 1, #token: 541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.51, #queue-req: 0, 
[2025-07-28 00:40:00 DP0 TP0] Decode batch. #running-req: 1, #token: 803, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.12, #queue-req: 0, 
[2025-07-28 00:40:00 DP1 TP0] Decode batch. #running-req: 1, #token: 581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.52, #queue-req: 0, 
[2025-07-28 00:40:00 DP0 TP0] Decode batch. #running-req: 1, #token: 843, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.10, #queue-req: 0, 
[2025-07-28 00:40:00 DP1 TP0] Decode batch. #running-req: 1, #token: 621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.43, #queue-req: 0, 
[2025-07-28 00:40:00 DP0 TP0] Decode batch. #running-req: 1, #token: 883, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.17, #queue-req: 0, 
[2025-07-28 00:40:00 DP1 TP0] Decode batch. #running-req: 1, #token: 661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.43, #queue-req: 0, 
[2025-07-28 00:40:01 DP0 TP0] Decode batch. #running-req: 1, #token: 923, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 00:40:01 DP1 TP0] Decode batch. #running-req: 1, #token: 701, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.50, #queue-req: 0, 
[2025-07-28 00:40:01 DP0 TP0] Decode batch. #running-req: 1, #token: 963, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 00:40:01 DP1 TP0] Decode batch. #running-req: 1, #token: 741, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.43, #queue-req: 0, 
[2025-07-28 00:40:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1003, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.12, #queue-req: 0, 
[2025-07-28 00:40:01 DP1 TP0] Decode batch. #running-req: 1, #token: 781, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.47, #queue-req: 0, 
[2025-07-28 00:40:01] INFO:     127.0.0.1:53268 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1043, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.95, #queue-req: 0, 
[2025-07-28 00:40:01 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 114, #cached-token: 109, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:40:02] INFO:     127.0.0.1:53266 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:02 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 80, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:02 DP0 TP0] Decode batch. #running-req: 1, #token: 263, token usage: 0.00, cuda graph: True, gen throughput (token/s): 176.71, #queue-req: 0, 
[2025-07-28 00:40:02 DP1 TP0] Decode batch. #running-req: 1, #token: 214, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.32, #queue-req: 0, 
[2025-07-28 00:40:02 DP0 TP0] Decode batch. #running-req: 1, #token: 303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.47, #queue-req: 0, 
[2025-07-28 00:40:02 DP1 TP0] Decode batch. #running-req: 1, #token: 254, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.52, #queue-req: 0, 
[2025-07-28 00:40:02 DP0 TP0] Decode batch. #running-req: 1, #token: 343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.39, #queue-req: 0, 
[2025-07-28 00:40:02 DP1 TP0] Decode batch. #running-req: 1, #token: 294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.69, #queue-req: 0, 
[2025-07-28 00:40:03 DP0 TP0] Decode batch. #running-req: 1, #token: 383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 00:40:03 DP1 TP0] Decode batch. #running-req: 1, #token: 334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.46, #queue-req: 0, 
[2025-07-28 00:40:03 DP0 TP0] Decode batch. #running-req: 1, #token: 423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 00:40:03 DP1 TP0] Decode batch. #running-req: 1, #token: 374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.68, #queue-req: 0, 
[2025-07-28 00:40:03 DP0 TP0] Decode batch. #running-req: 1, #token: 463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.30, #queue-req: 0, 
[2025-07-28 00:40:03 DP1 TP0] Decode batch. #running-req: 1, #token: 414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.48, #queue-req: 0, 
[2025-07-28 00:40:03 DP0 TP0] Decode batch. #running-req: 1, #token: 503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 00:40:04 DP1 TP0] Decode batch. #running-req: 1, #token: 454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.48, #queue-req: 0, 
[2025-07-28 00:40:04 DP0 TP0] Decode batch. #running-req: 1, #token: 543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.13, #queue-req: 0, 
[2025-07-28 00:40:04 DP1 TP0] Decode batch. #running-req: 1, #token: 494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.48, #queue-req: 0, 
[2025-07-28 00:40:04 DP0 TP0] Decode batch. #running-req: 1, #token: 583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.99, #queue-req: 0, 
[2025-07-28 00:40:04 DP1 TP0] Decode batch. #running-req: 1, #token: 534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 00:40:04 DP0 TP0] Decode batch. #running-req: 1, #token: 623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.07, #queue-req: 0, 
[2025-07-28 00:40:04 DP1 TP0] Decode batch. #running-req: 1, #token: 574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.43, #queue-req: 0, 
[2025-07-28 00:40:05] INFO:     127.0.0.1:53274 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:05 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 136, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:05 DP0 TP0] Decode batch. #running-req: 1, #token: 260, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.58, #queue-req: 0, 
[2025-07-28 00:40:05 DP1 TP0] Decode batch. #running-req: 1, #token: 614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.46, #queue-req: 0, 
[2025-07-28 00:40:05 DP0 TP0] Decode batch. #running-req: 1, #token: 300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 00:40:05 DP1 TP0] Decode batch. #running-req: 1, #token: 654, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.31, #queue-req: 0, 
[2025-07-28 00:40:05 DP0 TP0] Decode batch. #running-req: 1, #token: 340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 00:40:05 DP1 TP0] Decode batch. #running-req: 1, #token: 694, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.46, #queue-req: 0, 
[2025-07-28 00:40:06 DP0 TP0] Decode batch. #running-req: 1, #token: 380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.42, #queue-req: 0, 
[2025-07-28 00:40:06 DP1 TP0] Decode batch. #running-req: 1, #token: 734, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 00:40:06 DP0 TP0] Decode batch. #running-req: 1, #token: 420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 00:40:06 DP1 TP0] Decode batch. #running-req: 1, #token: 774, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.37, #queue-req: 0, 
[2025-07-28 00:40:06] INFO:     127.0.0.1:53276 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:06 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 137, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:06 DP0 TP0] Decode batch. #running-req: 1, #token: 460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 00:40:06 DP1 TP0] Decode batch. #running-req: 1, #token: 272, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.14, #queue-req: 0, 
[2025-07-28 00:40:06 DP0 TP0] Decode batch. #running-req: 1, #token: 500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 00:40:07 DP1 TP0] Decode batch. #running-req: 1, #token: 312, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.70, #queue-req: 0, 
[2025-07-28 00:40:07 DP0 TP0] Decode batch. #running-req: 1, #token: 540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.13, #queue-req: 0, 
[2025-07-28 00:40:07 DP1 TP0] Decode batch. #running-req: 1, #token: 352, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.69, #queue-req: 0, 
[2025-07-28 00:40:07 DP0 TP0] Decode batch. #running-req: 1, #token: 580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.24, #queue-req: 0, 
[2025-07-28 00:40:07 DP1 TP0] Decode batch. #running-req: 1, #token: 392, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.62, #queue-req: 0, 
[2025-07-28 00:40:07 DP0 TP0] Decode batch. #running-req: 1, #token: 620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 00:40:07 DP1 TP0] Decode batch. #running-req: 1, #token: 432, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.56, #queue-req: 0, 
[2025-07-28 00:40:08 DP0 TP0] Decode batch. #running-req: 1, #token: 660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 00:40:08 DP1 TP0] Decode batch. #running-req: 1, #token: 472, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.38, #queue-req: 0, 
[2025-07-28 00:40:08 DP0 TP0] Decode batch. #running-req: 1, #token: 700, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 00:40:08] INFO:     127.0.0.1:56900 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:08 DP1 TP0] Decode batch. #running-req: 1, #token: 512, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.57, #queue-req: 0, 
[2025-07-28 00:40:08 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 85, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:08 DP0 TP0] Decode batch. #running-req: 1, #token: 214, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.76, #queue-req: 0, 
[2025-07-28 00:40:08 DP1 TP0] Decode batch. #running-req: 1, #token: 552, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.48, #queue-req: 0, 
[2025-07-28 00:40:09 DP0 TP0] Decode batch. #running-req: 1, #token: 254, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.44, #queue-req: 0, 
[2025-07-28 00:40:09 DP1 TP0] Decode batch. #running-req: 1, #token: 592, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.49, #queue-req: 0, 
[2025-07-28 00:40:09 DP0 TP0] Decode batch. #running-req: 1, #token: 294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.39, #queue-req: 0, 
[2025-07-28 00:40:09 DP1 TP0] Decode batch. #running-req: 1, #token: 632, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.52, #queue-req: 0, 
[2025-07-28 00:40:09] INFO:     127.0.0.1:56908 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:09 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 157, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:09 DP0 TP0] Decode batch. #running-req: 1, #token: 334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 00:40:09 DP1 TP0] Decode batch. #running-req: 1, #token: 280, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.20, #queue-req: 0, 
[2025-07-28 00:40:09 DP0 TP0] Decode batch. #running-req: 1, #token: 374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.31, #queue-req: 0, 
[2025-07-28 00:40:10 DP1 TP0] Decode batch. #running-req: 1, #token: 320, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.64, #queue-req: 0, 
[2025-07-28 00:40:10 DP0 TP0] Decode batch. #running-req: 1, #token: 414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.06, #queue-req: 0, 
[2025-07-28 00:40:10 DP1 TP0] Decode batch. #running-req: 1, #token: 360, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.59, #queue-req: 0, 
[2025-07-28 00:40:10 DP0 TP0] Decode batch. #running-req: 1, #token: 454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.07, #queue-req: 0, 
[2025-07-28 00:40:10 DP1 TP0] Decode batch. #running-req: 1, #token: 400, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.52, #queue-req: 0, 
[2025-07-28 00:40:10 DP0 TP0] Decode batch. #running-req: 1, #token: 494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.00, #queue-req: 0, 
[2025-07-28 00:40:10 DP1 TP0] Decode batch. #running-req: 1, #token: 440, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.48, #queue-req: 0, 
[2025-07-28 00:40:11 DP0 TP0] Decode batch. #running-req: 1, #token: 534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.12, #queue-req: 0, 
[2025-07-28 00:40:11 DP1 TP0] Decode batch. #running-req: 1, #token: 480, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 00:40:11 DP0 TP0] Decode batch. #running-req: 1, #token: 574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 00:40:11 DP1 TP0] Decode batch. #running-req: 1, #token: 520, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.46, #queue-req: 0, 
[2025-07-28 00:40:11] INFO:     127.0.0.1:56914 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:11 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 150, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:11 DP0 TP0] Decode batch. #running-req: 1, #token: 264, token usage: 0.00, cuda graph: True, gen throughput (token/s): 116.87, #queue-req: 0, 
[2025-07-28 00:40:11 DP1 TP0] Decode batch. #running-req: 1, #token: 560, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.42, #queue-req: 0, 
[2025-07-28 00:40:12 DP0 TP0] Decode batch. #running-req: 1, #token: 304, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 00:40:12 DP1 TP0] Decode batch. #running-req: 1, #token: 600, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.40, #queue-req: 0, 
[2025-07-28 00:40:12 DP0 TP0] Decode batch. #running-req: 1, #token: 344, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 00:40:12 DP1 TP0] Decode batch. #running-req: 1, #token: 640, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.40, #queue-req: 0, 
[2025-07-28 00:40:12 DP0 TP0] Decode batch. #running-req: 1, #token: 384, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 00:40:12 DP1 TP0] Decode batch. #running-req: 1, #token: 680, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.44, #queue-req: 0, 
[2025-07-28 00:40:12 DP0 TP0] Decode batch. #running-req: 1, #token: 424, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.13, #queue-req: 0, 
[2025-07-28 00:40:12 DP1 TP0] Decode batch. #running-req: 1, #token: 720, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 00:40:13 DP0 TP0] Decode batch. #running-req: 1, #token: 464, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 00:40:13 DP1 TP0] Decode batch. #running-req: 1, #token: 760, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.38, #queue-req: 0, 
[2025-07-28 00:40:13 DP0 TP0] Decode batch. #running-req: 1, #token: 504, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.18, #queue-req: 0, 
[2025-07-28 00:40:13 DP1 TP0] Decode batch. #running-req: 1, #token: 800, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.37, #queue-req: 0, 
[2025-07-28 00:40:13 DP0 TP0] Decode batch. #running-req: 1, #token: 544, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 00:40:13 DP1 TP0] Decode batch. #running-req: 1, #token: 840, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.37, #queue-req: 0, 
[2025-07-28 00:40:14 DP0 TP0] Decode batch. #running-req: 1, #token: 584, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.17, #queue-req: 0, 
[2025-07-28 00:40:14] INFO:     127.0.0.1:56926 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:14 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 111, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:14 DP1 TP0] Decode batch. #running-req: 1, #token: 221, token usage: 0.00, cuda graph: True, gen throughput (token/s): 118.02, #queue-req: 0, 
[2025-07-28 00:40:14 DP0 TP0] Decode batch. #running-req: 1, #token: 624, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.07, #queue-req: 0, 
[2025-07-28 00:40:14 DP1 TP0] Decode batch. #running-req: 1, #token: 261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.72, #queue-req: 0, 
[2025-07-28 00:40:14] INFO:     127.0.0.1:56942 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:14 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 95, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:14 DP0 TP0] Decode batch. #running-req: 1, #token: 208, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.95, #queue-req: 0, 
[2025-07-28 00:40:14 DP1 TP0] Decode batch. #running-req: 1, #token: 301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.62, #queue-req: 0, 
[2025-07-28 00:40:14 DP0 TP0] Decode batch. #running-req: 1, #token: 248, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.50, #queue-req: 0, 
[2025-07-28 00:40:15 DP1 TP0] Decode batch. #running-req: 1, #token: 341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.52, #queue-req: 0, 
[2025-07-28 00:40:15 DP0 TP0] Decode batch. #running-req: 1, #token: 288, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.48, #queue-req: 0, 
[2025-07-28 00:40:15 DP1 TP0] Decode batch. #running-req: 1, #token: 381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.50, #queue-req: 0, 
[2025-07-28 00:40:15 DP0 TP0] Decode batch. #running-req: 1, #token: 328, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.45, #queue-req: 0, 
[2025-07-28 00:40:15 DP1 TP0] Decode batch. #running-req: 1, #token: 421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.46, #queue-req: 0, 
[2025-07-28 00:40:15 DP0 TP0] Decode batch. #running-req: 1, #token: 368, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 00:40:15 DP1 TP0] Decode batch. #running-req: 1, #token: 461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 00:40:16 DP0 TP0] Decode batch. #running-req: 1, #token: 408, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.40, #queue-req: 0, 
[2025-07-28 00:40:16 DP1 TP0] Decode batch. #running-req: 1, #token: 501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.45, #queue-req: 0, 
[2025-07-28 00:40:16 DP0 TP0] Decode batch. #running-req: 1, #token: 448, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 00:40:16 DP1 TP0] Decode batch. #running-req: 1, #token: 541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 00:40:16] INFO:     127.0.0.1:35960 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:16 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 89, #cached-token: 106, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:40:16 DP1 TP0] Decode batch. #running-req: 2, #token: 707, token usage: 0.00, cuda graph: True, gen throughput (token/s): 219.52, #queue-req: 0, 
[2025-07-28 00:40:17] INFO:     127.0.0.1:35952 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:17 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 115, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:17 DP1 TP0] Decode batch. #running-req: 1, #token: 272, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.16, #queue-req: 0, 
[2025-07-28 00:40:17 DP0 TP0] Decode batch. #running-req: 1, #token: 251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.34, #queue-req: 0, 
[2025-07-28 00:40:17 DP1 TP0] Decode batch. #running-req: 1, #token: 312, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.58, #queue-req: 0, 
[2025-07-28 00:40:17 DP0 TP0] Decode batch. #running-req: 1, #token: 291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.30, #queue-req: 0, 
[2025-07-28 00:40:17 DP1 TP0] Decode batch. #running-req: 1, #token: 352, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.53, #queue-req: 0, 
[2025-07-28 00:40:17 DP0 TP0] Decode batch. #running-req: 1, #token: 331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.34, #queue-req: 0, 
[2025-07-28 00:40:18 DP1 TP0] Decode batch. #running-req: 1, #token: 392, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.42, #queue-req: 0, 
[2025-07-28 00:40:18 DP0 TP0] Decode batch. #running-req: 1, #token: 371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 00:40:18 DP1 TP0] Decode batch. #running-req: 1, #token: 432, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.46, #queue-req: 0, 
[2025-07-28 00:40:18 DP0 TP0] Decode batch. #running-req: 1, #token: 411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.45, #queue-req: 0, 
[2025-07-28 00:40:18 DP1 TP0] Decode batch. #running-req: 1, #token: 472, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.40, #queue-req: 0, 
[2025-07-28 00:40:18 DP0 TP0] Decode batch. #running-req: 1, #token: 451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.17, #queue-req: 0, 
[2025-07-28 00:40:18 DP1 TP0] Decode batch. #running-req: 1, #token: 512, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.40, #queue-req: 0, 
[2025-07-28 00:40:19 DP0 TP0] Decode batch. #running-req: 1, #token: 491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.36, #queue-req: 0, 
[2025-07-28 00:40:19 DP1 TP0] Decode batch. #running-req: 1, #token: 552, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 00:40:19 DP0 TP0] Decode batch. #running-req: 1, #token: 531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 00:40:19 DP1 TP0] Decode batch. #running-req: 1, #token: 592, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.36, #queue-req: 0, 
[2025-07-28 00:40:19] INFO:     127.0.0.1:35976 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:19 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 164, #cached-token: 108, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:40:19 DP1 TP0] Decode batch. #running-req: 2, #token: 806, token usage: 0.00, cuda graph: True, gen throughput (token/s): 141.93, #queue-req: 0, 
[2025-07-28 00:40:19] INFO:     127.0.0.1:35974 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:19 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 143, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:20 DP0 TP0] Decode batch. #running-req: 1, #token: 256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 65.06, #queue-req: 0, 
[2025-07-28 00:40:20 DP1 TP0] Decode batch. #running-req: 1, #token: 320, token usage: 0.00, cuda graph: True, gen throughput (token/s): 190.10, #queue-req: 0, 
[2025-07-28 00:40:20 DP0 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.48, #queue-req: 0, 
[2025-07-28 00:40:20 DP1 TP0] Decode batch. #running-req: 1, #token: 360, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.61, #queue-req: 0, 
[2025-07-28 00:40:20 DP0 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.42, #queue-req: 0, 
[2025-07-28 00:40:20 DP1 TP0] Decode batch. #running-req: 1, #token: 400, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.49, #queue-req: 0, 
[2025-07-28 00:40:20 DP0 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 00:40:20 DP1 TP0] Decode batch. #running-req: 1, #token: 440, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.40, #queue-req: 0, 
[2025-07-28 00:40:21 DP0 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.30, #queue-req: 0, 
[2025-07-28 00:40:21 DP1 TP0] Decode batch. #running-req: 1, #token: 480, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.44, #queue-req: 0, 
[2025-07-28 00:40:21 DP0 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 00:40:21 DP1 TP0] Decode batch. #running-req: 1, #token: 520, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.37, #queue-req: 0, 
[2025-07-28 00:40:21 DP0 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 00:40:21 DP1 TP0] Decode batch. #running-req: 1, #token: 560, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 00:40:22 DP0 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.08, #queue-req: 0, 
[2025-07-28 00:40:22 DP1 TP0] Decode batch. #running-req: 1, #token: 600, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.31, #queue-req: 0, 
[2025-07-28 00:40:22] INFO:     127.0.0.1:36008 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:22 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 117, #cached-token: 109, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:40:22 DP1 TP0] Decode batch. #running-req: 2, #token: 794, token usage: 0.00, cuda graph: True, gen throughput (token/s): 216.88, #queue-req: 0, 
[2025-07-28 00:40:22 DP1 TP0] Decode batch. #running-req: 2, #token: 874, token usage: 0.00, cuda graph: True, gen throughput (token/s): 256.60, #queue-req: 0, 
[2025-07-28 00:40:23 DP1 TP0] Decode batch. #running-req: 2, #token: 954, token usage: 0.00, cuda graph: True, gen throughput (token/s): 256.40, #queue-req: 0, 
[2025-07-28 00:40:23 DP1 TP0] Decode batch. #running-req: 2, #token: 1034, token usage: 0.00, cuda graph: True, gen throughput (token/s): 256.44, #queue-req: 0, 
[2025-07-28 00:40:23] INFO:     127.0.0.1:35992 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:23 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 125, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:23 DP1 TP0] Decode batch. #running-req: 1, #token: 422, token usage: 0.00, cuda graph: True, gen throughput (token/s): 233.87, #queue-req: 0, 
[2025-07-28 00:40:24 DP0 TP0] Decode batch. #running-req: 1, #token: 267, token usage: 0.00, cuda graph: True, gen throughput (token/s): 21.12, #queue-req: 0, 
[2025-07-28 00:40:24 DP1 TP0] Decode batch. #running-req: 1, #token: 462, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.38, #queue-req: 0, 
[2025-07-28 00:40:24 DP0 TP0] Decode batch. #running-req: 1, #token: 307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.38, #queue-req: 0, 
[2025-07-28 00:40:24 DP1 TP0] Decode batch. #running-req: 1, #token: 502, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.51, #queue-req: 0, 
[2025-07-28 00:40:24 DP0 TP0] Decode batch. #running-req: 1, #token: 347, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.49, #queue-req: 0, 
[2025-07-28 00:40:24 DP1 TP0] Decode batch. #running-req: 1, #token: 542, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 00:40:24 DP0 TP0] Decode batch. #running-req: 1, #token: 387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.47, #queue-req: 0, 
[2025-07-28 00:40:24 DP1 TP0] Decode batch. #running-req: 1, #token: 582, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.39, #queue-req: 0, 
[2025-07-28 00:40:25 DP0 TP0] Decode batch. #running-req: 1, #token: 427, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 00:40:25 DP1 TP0] Decode batch. #running-req: 1, #token: 622, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 00:40:25] INFO:     127.0.0.1:36012 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:25 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 196, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:25 DP0 TP0] Decode batch. #running-req: 1, #token: 467, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 00:40:25 DP1 TP0] Decode batch. #running-req: 1, #token: 344, token usage: 0.00, cuda graph: True, gen throughput (token/s): 116.86, #queue-req: 0, 
[2025-07-28 00:40:25 DP0 TP0] Decode batch. #running-req: 1, #token: 507, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 00:40:25 DP1 TP0] Decode batch. #running-req: 1, #token: 384, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.46, #queue-req: 0, 
[2025-07-28 00:40:26 DP0 TP0] Decode batch. #running-req: 1, #token: 547, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.04, #queue-req: 0, 
[2025-07-28 00:40:26 DP1 TP0] Decode batch. #running-req: 1, #token: 424, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 00:40:26 DP0 TP0] Decode batch. #running-req: 1, #token: 587, token usage: 0.00, cuda graph: True, gen throughput (token/s): 132.15, #queue-req: 0, 
[2025-07-28 00:40:26 DP1 TP0] Decode batch. #running-req: 1, #token: 464, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.39, #queue-req: 0, 
[2025-07-28 00:40:26 DP0 TP0] Decode batch. #running-req: 1, #token: 627, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.17, #queue-req: 0, 
[2025-07-28 00:40:26] INFO:     127.0.0.1:37240 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:26 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:26 DP1 TP0] Decode batch. #running-req: 1, #token: 504, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.38, #queue-req: 0, 
[2025-07-28 00:40:26 DP0 TP0] Decode batch. #running-req: 1, #token: 225, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.93, #queue-req: 0, 
[2025-07-28 00:40:26 DP1 TP0] Decode batch. #running-req: 1, #token: 544, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.37, #queue-req: 0, 
[2025-07-28 00:40:27 DP0 TP0] Decode batch. #running-req: 1, #token: 265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.49, #queue-req: 0, 
[2025-07-28 00:40:27 DP1 TP0] Decode batch. #running-req: 1, #token: 584, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 00:40:27 DP0 TP0] Decode batch. #running-req: 1, #token: 305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.38, #queue-req: 0, 
[2025-07-28 00:40:27 DP1 TP0] Decode batch. #running-req: 1, #token: 624, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 00:40:27 DP0 TP0] Decode batch. #running-req: 1, #token: 345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.40, #queue-req: 0, 
[2025-07-28 00:40:27 DP1 TP0] Decode batch. #running-req: 1, #token: 664, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 00:40:28 DP0 TP0] Decode batch. #running-req: 1, #token: 385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.45, #queue-req: 0, 
[2025-07-28 00:40:28 DP1 TP0] Decode batch. #running-req: 1, #token: 704, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 00:40:28] INFO:     127.0.0.1:37248 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:28 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 154, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:28 DP0 TP0] Decode batch. #running-req: 1, #token: 425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.37, #queue-req: 0, 
[2025-07-28 00:40:28 DP1 TP0] Decode batch. #running-req: 1, #token: 294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.92, #queue-req: 0, 
[2025-07-28 00:40:28 DP0 TP0] Decode batch. #running-req: 1, #token: 465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 00:40:28] INFO:     127.0.0.1:37262 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:28 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 128, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:28 DP1 TP0] Decode batch. #running-req: 1, #token: 334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.63, #queue-req: 0, 
[2025-07-28 00:40:29 DP0 TP0] Decode batch. #running-req: 1, #token: 271, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.64, #queue-req: 0, 
[2025-07-28 00:40:29 DP1 TP0] Decode batch. #running-req: 1, #token: 374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.51, #queue-req: 0, 
[2025-07-28 00:40:29 DP0 TP0] Decode batch. #running-req: 1, #token: 311, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.44, #queue-req: 0, 
[2025-07-28 00:40:29 DP1 TP0] Decode batch. #running-req: 1, #token: 414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.48, #queue-req: 0, 
[2025-07-28 00:40:29 DP0 TP0] Decode batch. #running-req: 1, #token: 351, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.31, #queue-req: 0, 
[2025-07-28 00:40:29 DP1 TP0] Decode batch. #running-req: 1, #token: 454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.51, #queue-req: 0, 
[2025-07-28 00:40:29 DP0 TP0] Decode batch. #running-req: 1, #token: 391, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.37, #queue-req: 0, 
[2025-07-28 00:40:29 DP1 TP0] Decode batch. #running-req: 1, #token: 494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.47, #queue-req: 0, 
[2025-07-28 00:40:30 DP0 TP0] Decode batch. #running-req: 1, #token: 431, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 00:40:30 DP1 TP0] Decode batch. #running-req: 1, #token: 534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.39, #queue-req: 0, 
[2025-07-28 00:40:30 DP0 TP0] Decode batch. #running-req: 1, #token: 471, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 00:40:30 DP1 TP0] Decode batch. #running-req: 1, #token: 574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.46, #queue-req: 0, 
[2025-07-28 00:40:30 DP0 TP0] Decode batch. #running-req: 1, #token: 511, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 00:40:30 DP1 TP0] Decode batch. #running-req: 1, #token: 614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.42, #queue-req: 0, 
[2025-07-28 00:40:31 DP0 TP0] Decode batch. #running-req: 1, #token: 551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 134.27, #queue-req: 0, 
[2025-07-28 00:40:31 DP1 TP0] Decode batch. #running-req: 1, #token: 654, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.40, #queue-req: 0, 
[2025-07-28 00:40:31 DP0 TP0] Decode batch. #running-req: 1, #token: 591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.20, #queue-req: 0, 
[2025-07-28 00:40:31 DP1 TP0] Decode batch. #running-req: 1, #token: 694, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.38, #queue-req: 0, 
[2025-07-28 00:40:31] INFO:     127.0.0.1:37272 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:31 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:31] INFO:     127.0.0.1:37280 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:31 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:31 DP0 TP0] Decode batch. #running-req: 1, #token: 262, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.04, #queue-req: 0, 
[2025-07-28 00:40:31 DP1 TP0] Decode batch. #running-req: 1, #token: 270, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.78, #queue-req: 0, 
[2025-07-28 00:40:32 DP1 TP0] Decode batch. #running-req: 1, #token: 310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.60, #queue-req: 0, 
[2025-07-28 00:40:32 DP0 TP0] Decode batch. #running-req: 1, #token: 302, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 00:40:32 DP1 TP0] Decode batch. #running-req: 1, #token: 350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.60, #queue-req: 0, 
[2025-07-28 00:40:32 DP0 TP0] Decode batch. #running-req: 1, #token: 342, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 00:40:32 DP1 TP0] Decode batch. #running-req: 1, #token: 390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.55, #queue-req: 0, 
[2025-07-28 00:40:32 DP0 TP0] Decode batch. #running-req: 1, #token: 382, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.43, #queue-req: 0, 
[2025-07-28 00:40:32 DP1 TP0] Decode batch. #running-req: 1, #token: 430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 00:40:32 DP0 TP0] Decode batch. #running-req: 1, #token: 422, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 00:40:33 DP1 TP0] Decode batch. #running-req: 1, #token: 470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.47, #queue-req: 0, 
[2025-07-28 00:40:33 DP0 TP0] Decode batch. #running-req: 1, #token: 462, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.23, #queue-req: 0, 
[2025-07-28 00:40:33 DP1 TP0] Decode batch. #running-req: 1, #token: 510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.46, #queue-req: 0, 
[2025-07-28 00:40:33 DP0 TP0] Decode batch. #running-req: 1, #token: 502, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.17, #queue-req: 0, 
[2025-07-28 00:40:33 DP1 TP0] Decode batch. #running-req: 1, #token: 550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 00:40:33 DP0 TP0] Decode batch. #running-req: 1, #token: 542, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.05, #queue-req: 0, 
[2025-07-28 00:40:34] INFO:     127.0.0.1:37288 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:34 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 95, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:34] INFO:     127.0.0.1:37296 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:34 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 107, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:34 DP1 TP0] Decode batch. #running-req: 1, #token: 210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 118.08, #queue-req: 0, 
[2025-07-28 00:40:34 DP0 TP0] Decode batch. #running-req: 1, #token: 252, token usage: 0.00, cuda graph: True, gen throughput (token/s): 118.65, #queue-req: 0, 
[2025-07-28 00:40:34 DP1 TP0] Decode batch. #running-req: 1, #token: 250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.73, #queue-req: 0, 
[2025-07-28 00:40:34 DP0 TP0] Decode batch. #running-req: 1, #token: 292, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.45, #queue-req: 0, 
[2025-07-28 00:40:34 DP1 TP0] Decode batch. #running-req: 1, #token: 290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.65, #queue-req: 0, 
[2025-07-28 00:40:34 DP0 TP0] Decode batch. #running-req: 1, #token: 332, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.31, #queue-req: 0, 
[2025-07-28 00:40:34 DP1 TP0] Decode batch. #running-req: 1, #token: 330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.68, #queue-req: 0, 
[2025-07-28 00:40:34 DP0 TP0] Decode batch. #running-req: 1, #token: 372, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.34, #queue-req: 0, 
[2025-07-28 00:40:35 DP1 TP0] Decode batch. #running-req: 1, #token: 370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.61, #queue-req: 0, 
[2025-07-28 00:40:35 DP0 TP0] Decode batch. #running-req: 1, #token: 412, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 00:40:35 DP1 TP0] Decode batch. #running-req: 1, #token: 410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.54, #queue-req: 0, 
[2025-07-28 00:40:35 DP0 TP0] Decode batch. #running-req: 1, #token: 452, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 00:40:35 DP1 TP0] Decode batch. #running-req: 1, #token: 450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.49, #queue-req: 0, 
[2025-07-28 00:40:35 DP0 TP0] Decode batch. #running-req: 1, #token: 492, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 00:40:36 DP1 TP0] Decode batch. #running-req: 1, #token: 490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 00:40:36 DP0 TP0] Decode batch. #running-req: 1, #token: 532, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 00:40:36] INFO:     127.0.0.1:35252 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:36 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 78, #cached-token: 106, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:40:36 DP1 TP0] Decode batch. #running-req: 2, #token: 640, token usage: 0.00, cuda graph: True, gen throughput (token/s): 206.97, #queue-req: 0, 
[2025-07-28 00:40:36 DP1 TP0] Decode batch. #running-req: 2, #token: 720, token usage: 0.00, cuda graph: True, gen throughput (token/s): 256.50, #queue-req: 0, 
[2025-07-28 00:40:37 DP1 TP0] Decode batch. #running-req: 2, #token: 800, token usage: 0.00, cuda graph: True, gen throughput (token/s): 256.43, #queue-req: 0, 
[2025-07-28 00:40:37] INFO:     127.0.0.1:35242 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:37 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 146, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:37 DP1 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.11, #queue-req: 0, 
[2025-07-28 00:40:37 DP0 TP0] Decode batch. #running-req: 1, #token: 289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 27.26, #queue-req: 0, 
[2025-07-28 00:40:37 DP1 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.56, #queue-req: 0, 
[2025-07-28 00:40:37 DP0 TP0] Decode batch. #running-req: 1, #token: 329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.39, #queue-req: 0, 
[2025-07-28 00:40:38 DP1 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.46, #queue-req: 0, 
[2025-07-28 00:40:38 DP0 TP0] Decode batch. #running-req: 1, #token: 369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.48, #queue-req: 0, 
[2025-07-28 00:40:38 DP1 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.46, #queue-req: 0, 
[2025-07-28 00:40:38 DP0 TP0] Decode batch. #running-req: 1, #token: 409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.37, #queue-req: 0, 
[2025-07-28 00:40:38 DP1 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.48, #queue-req: 0, 
[2025-07-28 00:40:38] INFO:     127.0.0.1:35256 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:38 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:38 DP0 TP0] Decode batch. #running-req: 1, #token: 449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 00:40:38 DP1 TP0] Decode batch. #running-req: 1, #token: 217, token usage: 0.00, cuda graph: True, gen throughput (token/s): 119.28, #queue-req: 0, 
[2025-07-28 00:40:39 DP0 TP0] Decode batch. #running-req: 1, #token: 489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 00:40:39 DP1 TP0] Decode batch. #running-req: 1, #token: 257, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.51, #queue-req: 0, 
[2025-07-28 00:40:39 DP0 TP0] Decode batch. #running-req: 1, #token: 529, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.30, #queue-req: 0, 
[2025-07-28 00:40:39 DP1 TP0] Decode batch. #running-req: 1, #token: 297, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.30, #queue-req: 0, 
[2025-07-28 00:40:39 DP0 TP0] Decode batch. #running-req: 1, #token: 569, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.31, #queue-req: 0, 
[2025-07-28 00:40:39 DP1 TP0] Decode batch. #running-req: 1, #token: 337, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 00:40:39 DP0 TP0] Decode batch. #running-req: 1, #token: 609, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 00:40:40 DP1 TP0] Decode batch. #running-req: 1, #token: 377, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 00:40:40 DP0 TP0] Decode batch. #running-req: 1, #token: 649, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 00:40:40] INFO:     127.0.0.1:35272 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:40 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 173, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:40 DP1 TP0] Decode batch. #running-req: 1, #token: 417, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 00:40:40 DP0 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.61, #queue-req: 0, 
[2025-07-28 00:40:40 DP1 TP0] Decode batch. #running-req: 1, #token: 457, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.37, #queue-req: 0, 
[2025-07-28 00:40:40 DP0 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.44, #queue-req: 0, 
[2025-07-28 00:40:40 DP1 TP0] Decode batch. #running-req: 1, #token: 497, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 00:40:41 DP0 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 00:40:41 DP1 TP0] Decode batch. #running-req: 1, #token: 537, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 00:40:41 DP0 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 00:40:41 DP1 TP0] Decode batch. #running-req: 1, #token: 577, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 00:40:41] INFO:     127.0.0.1:35286 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:41 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 178, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:41 DP0 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 00:40:41 DP1 TP0] Decode batch. #running-req: 1, #token: 308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 118.29, #queue-req: 0, 
[2025-07-28 00:40:42 DP0 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.37, #queue-req: 0, 
[2025-07-28 00:40:42 DP1 TP0] Decode batch. #running-req: 1, #token: 348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.63, #queue-req: 0, 
[2025-07-28 00:40:42 DP0 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 00:40:42 DP1 TP0] Decode batch. #running-req: 1, #token: 388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.52, #queue-req: 0, 
[2025-07-28 00:40:42 DP0 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.30, #queue-req: 0, 
[2025-07-28 00:40:42 DP1 TP0] Decode batch. #running-req: 1, #token: 428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.45, #queue-req: 0, 
[2025-07-28 00:40:42 DP0 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 00:40:43 DP1 TP0] Decode batch. #running-req: 1, #token: 468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.39, #queue-req: 0, 
[2025-07-28 00:40:43 DP0 TP0] Decode batch. #running-req: 1, #token: 676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.30, #queue-req: 0, 
[2025-07-28 00:40:43 DP1 TP0] Decode batch. #running-req: 1, #token: 508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 00:40:43 DP0 TP0] Decode batch. #running-req: 1, #token: 716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 00:40:43] INFO:     127.0.0.1:35290 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:43 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 88, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:43 DP1 TP0] Decode batch. #running-req: 1, #token: 548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 00:40:43 DP0 TP0] Decode batch. #running-req: 1, #token: 220, token usage: 0.00, cuda graph: True, gen throughput (token/s): 118.41, #queue-req: 0, 
[2025-07-28 00:40:43 DP1 TP0] Decode batch. #running-req: 1, #token: 588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.44, #queue-req: 0, 
[2025-07-28 00:40:44 DP0 TP0] Decode batch. #running-req: 1, #token: 260, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.51, #queue-req: 0, 
[2025-07-28 00:40:44 DP1 TP0] Decode batch. #running-req: 1, #token: 628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.40, #queue-req: 0, 
[2025-07-28 00:40:44 DP0 TP0] Decode batch. #running-req: 1, #token: 300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.45, #queue-req: 0, 
[2025-07-28 00:40:44 DP1 TP0] Decode batch. #running-req: 1, #token: 668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.37, #queue-req: 0, 
[2025-07-28 00:40:44] INFO:     127.0.0.1:35292 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:44 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 153, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:44 DP0 TP0] Decode batch. #running-req: 1, #token: 340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.42, #queue-req: 0, 
[2025-07-28 00:40:44 DP1 TP0] Decode batch. #running-req: 1, #token: 286, token usage: 0.00, cuda graph: True, gen throughput (token/s): 119.18, #queue-req: 0, 
[2025-07-28 00:40:45 DP0 TP0] Decode batch. #running-req: 1, #token: 380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.47, #queue-req: 0, 
[2025-07-28 00:40:45 DP1 TP0] Decode batch. #running-req: 1, #token: 326, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.52, #queue-req: 0, 
[2025-07-28 00:40:45 DP0 TP0] Decode batch. #running-req: 1, #token: 420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.24, #queue-req: 0, 
[2025-07-28 00:40:45 DP1 TP0] Decode batch. #running-req: 1, #token: 366, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.54, #queue-req: 0, 
[2025-07-28 00:40:45 DP0 TP0] Decode batch. #running-req: 1, #token: 460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.30, #queue-req: 0, 
[2025-07-28 00:40:45 DP1 TP0] Decode batch. #running-req: 1, #token: 406, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.47, #queue-req: 0, 
[2025-07-28 00:40:45 DP0 TP0] Decode batch. #running-req: 1, #token: 500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 00:40:45 DP1 TP0] Decode batch. #running-req: 1, #token: 446, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.30, #queue-req: 0, 
[2025-07-28 00:40:46] INFO:     127.0.0.1:56004 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:46 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 123, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:46 DP0 TP0] Decode batch. #running-req: 1, #token: 243, token usage: 0.00, cuda graph: True, gen throughput (token/s): 118.87, #queue-req: 0, 
[2025-07-28 00:40:46 DP1 TP0] Decode batch. #running-req: 1, #token: 486, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.40, #queue-req: 0, 
[2025-07-28 00:40:46 DP0 TP0] Decode batch. #running-req: 1, #token: 283, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.50, #queue-req: 0, 
[2025-07-28 00:40:46 DP1 TP0] Decode batch. #running-req: 1, #token: 526, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 00:40:46 DP0 TP0] Decode batch. #running-req: 1, #token: 323, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.46, #queue-req: 0, 
[2025-07-28 00:40:46 DP1 TP0] Decode batch. #running-req: 1, #token: 566, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 00:40:47 DP0 TP0] Decode batch. #running-req: 1, #token: 363, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.49, #queue-req: 0, 
[2025-07-28 00:40:47 DP1 TP0] Decode batch. #running-req: 1, #token: 606, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.43, #queue-req: 0, 
[2025-07-28 00:40:47] INFO:     127.0.0.1:56006 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:47 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 129, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:47 DP0 TP0] Decode batch. #running-req: 1, #token: 403, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 00:40:47 DP1 TP0] Decode batch. #running-req: 1, #token: 251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 118.06, #queue-req: 0, 
[2025-07-28 00:40:47 DP0 TP0] Decode batch. #running-req: 1, #token: 443, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.19, #queue-req: 0, 
[2025-07-28 00:40:47 DP1 TP0] Decode batch. #running-req: 1, #token: 291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.61, #queue-req: 0, 
[2025-07-28 00:40:47 DP0 TP0] Decode batch. #running-req: 1, #token: 483, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.24, #queue-req: 0, 
[2025-07-28 00:40:48 DP1 TP0] Decode batch. #running-req: 1, #token: 331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.53, #queue-req: 0, 
[2025-07-28 00:40:48 DP0 TP0] Decode batch. #running-req: 1, #token: 523, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 00:40:48 DP1 TP0] Decode batch. #running-req: 1, #token: 371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.53, #queue-req: 0, 
[2025-07-28 00:40:48 DP0 TP0] Decode batch. #running-req: 1, #token: 563, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 00:40:48 DP1 TP0] Decode batch. #running-req: 1, #token: 411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 00:40:48 DP0 TP0] Decode batch. #running-req: 1, #token: 603, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 00:40:48 DP1 TP0] Decode batch. #running-req: 1, #token: 451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.44, #queue-req: 0, 
[2025-07-28 00:40:49 DP0 TP0] Decode batch. #running-req: 1, #token: 643, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.16, #queue-req: 0, 
[2025-07-28 00:40:49 DP1 TP0] Decode batch. #running-req: 1, #token: 491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.42, #queue-req: 0, 
[2025-07-28 00:40:49 DP0 TP0] Decode batch. #running-req: 1, #token: 683, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.34, #queue-req: 0, 
[2025-07-28 00:40:49 DP1 TP0] Decode batch. #running-req: 1, #token: 531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.36, #queue-req: 0, 
[2025-07-28 00:40:49 DP0 TP0] Decode batch. #running-req: 1, #token: 723, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 00:40:49] INFO:     127.0.0.1:56020 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:49 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 112, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:49 DP1 TP0] Decode batch. #running-req: 1, #token: 571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.34, #queue-req: 0, 
[2025-07-28 00:40:50 DP0 TP0] Decode batch. #running-req: 1, #token: 276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 116.88, #queue-req: 0, 
[2025-07-28 00:40:50 DP1 TP0] Decode batch. #running-req: 1, #token: 611, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.33, #queue-req: 0, 
[2025-07-28 00:40:50 DP0 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 00:40:50 DP1 TP0] Decode batch. #running-req: 1, #token: 651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 00:40:50 DP0 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 00:40:50 DP1 TP0] Decode batch. #running-req: 1, #token: 691, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.36, #queue-req: 0, 
[2025-07-28 00:40:50] INFO:     127.0.0.1:56024 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:50 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 113, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:50 DP0 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.38, #queue-req: 0, 
[2025-07-28 00:40:51 DP1 TP0] Decode batch. #running-req: 1, #token: 281, token usage: 0.00, cuda graph: True, gen throughput (token/s): 119.09, #queue-req: 0, 
[2025-07-28 00:40:51 DP0 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.32, #queue-req: 0, 
[2025-07-28 00:40:51 DP1 TP0] Decode batch. #running-req: 1, #token: 321, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.59, #queue-req: 0, 
[2025-07-28 00:40:51 DP0 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.34, #queue-req: 0, 
[2025-07-28 00:40:51 DP1 TP0] Decode batch. #running-req: 1, #token: 361, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.54, #queue-req: 0, 
[2025-07-28 00:40:51 DP0 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.36, #queue-req: 0, 
[2025-07-28 00:40:51 DP1 TP0] Decode batch. #running-req: 1, #token: 401, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.52, #queue-req: 0, 
[2025-07-28 00:40:52 DP0 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 00:40:52 DP1 TP0] Decode batch. #running-req: 1, #token: 441, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.28, #queue-req: 0, 
[2025-07-28 00:40:52 DP0 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.29, #queue-req: 0, 
[2025-07-28 00:40:52 DP1 TP0] Decode batch. #running-req: 1, #token: 481, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.24, #queue-req: 0, 
[2025-07-28 00:40:52 DP0 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.25, #queue-req: 0, 
[2025-07-28 00:40:52 DP1 TP0] Decode batch. #running-req: 1, #token: 521, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 00:40:52 DP0 TP0] Decode batch. #running-req: 1, #token: 676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 00:40:52] INFO:     127.0.0.1:56030 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:53 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 103, #cached-token: 112, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:53 DP1 TP0] Decode batch. #running-req: 1, #token: 561, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.27, #queue-req: 0, 
[2025-07-28 00:40:53 DP0 TP0] Decode batch. #running-req: 1, #token: 252, token usage: 0.00, cuda graph: True, gen throughput (token/s): 116.64, #queue-req: 0, 
[2025-07-28 00:40:53 DP1 TP0] Decode batch. #running-req: 1, #token: 601, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.37, #queue-req: 0, 
[2025-07-28 00:40:53] INFO:     127.0.0.1:56032 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:53 DP0 TP0] Decode batch. #running-req: 1, #token: 292, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.24, #queue-req: 0, 
[2025-07-28 00:40:53 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 89, #cached-token: 112, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:40:53 DP1 TP0] Decode batch. #running-req: 1, #token: 211, token usage: 0.00, cuda graph: True, gen throughput (token/s): 119.15, #queue-req: 0, 
[2025-07-28 00:40:53 DP0 TP0] Decode batch. #running-req: 1, #token: 332, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.35, #queue-req: 0, 
[2025-07-28 00:40:53 DP1 TP0] Decode batch. #running-req: 1, #token: 251, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.69, #queue-req: 0, 
[2025-07-28 00:40:54 DP0 TP0] Decode batch. #running-req: 1, #token: 372, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.34, #queue-req: 0, 
[2025-07-28 00:40:54 DP1 TP0] Decode batch. #running-req: 1, #token: 291, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.79, #queue-req: 0, 
[2025-07-28 00:40:54 DP0 TP0] Decode batch. #running-req: 1, #token: 412, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.26, #queue-req: 0, 
[2025-07-28 00:40:54 DP1 TP0] Decode batch. #running-req: 1, #token: 331, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.59, #queue-req: 0, 
[2025-07-28 00:40:54 DP0 TP0] Decode batch. #running-req: 1, #token: 452, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 00:40:54 DP1 TP0] Decode batch. #running-req: 1, #token: 371, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.66, #queue-req: 0, 
[2025-07-28 00:40:55 DP0 TP0] Decode batch. #running-req: 1, #token: 492, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 00:40:55 DP1 TP0] Decode batch. #running-req: 1, #token: 411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.56, #queue-req: 0, 
[2025-07-28 00:40:55 DP0 TP0] Decode batch. #running-req: 1, #token: 532, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.24, #queue-req: 0, 
[2025-07-28 00:40:55 DP1 TP0] Decode batch. #running-req: 1, #token: 451, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.45, #queue-req: 0, 
[2025-07-28 00:40:55 DP0 TP0] Decode batch. #running-req: 1, #token: 572, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.21, #queue-req: 0, 
[2025-07-28 00:40:55 DP1 TP0] Decode batch. #running-req: 1, #token: 491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.60, #queue-req: 0, 
[2025-07-28 00:40:55 DP0 TP0] Decode batch. #running-req: 1, #token: 612, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 00:40:56 DP1 TP0] Decode batch. #running-req: 1, #token: 531, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.42, #queue-req: 0, 
[2025-07-28 00:40:56 DP0 TP0] Decode batch. #running-req: 1, #token: 652, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.15, #queue-req: 0, 
[2025-07-28 00:40:56 DP1 TP0] Decode batch. #running-req: 1, #token: 571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.41, #queue-req: 0, 
[2025-07-28 00:40:56 DP0 TP0] Decode batch. #running-req: 1, #token: 692, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.22, #queue-req: 0, 
[2025-07-28 00:40:56] INFO:     127.0.0.1:48896 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:40:56] INFO:     127.0.0.1:48890 - "POST /v1/chat/completions HTTP/1.1" 200 OK

[2025-07-28 09:31:50] server_args=ServerArgs(model_path='/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Qwen-32B', tokenizer_path='/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Qwen-32B', tokenizer_mode='auto', skip_tokenizer_init=False, load_format='auto', model_loader_extra_config='{}', trust_remote_code=False, context_length=32768, is_embedding=False, enable_multimodal=None, revision=None, model_impl='auto', host='127.0.0.1', port=8002, skip_server_warmup=False, warmups=None, nccl_port=None, dtype='auto', quantization=None, quantization_param_path=None, kv_cache_dtype='auto', mem_fraction_static=0.85, max_running_requests=None, max_total_tokens=None, chunked_prefill_size=8192, max_prefill_tokens=16384, schedule_policy='fcfs', schedule_conservativeness=1.0, cpu_offload_gb=0, page_size=1, hybrid_kvcache_ratio=None, swa_full_tokens_ratio=0.8, disable_hybrid_swa_memory=False, device='cuda', tp_size=4, pp_size=1, max_micro_batch_size=None, stream_interval=1, stream_output=False, random_seed=107402298, constrained_json_whitespace_pattern=None, watchdog_timeout=300, dist_timeout=None, download_dir=None, base_gpu_id=0, gpu_id_step=1, sleep_on_idle=False, log_level='info', log_level_http=None, log_requests=False, log_requests_level=0, crash_dump_folder=None, show_time_cost=True, enable_metrics=True, enable_metrics_for_all_schedulers=False, bucket_time_to_first_token=None, bucket_inter_token_latency=None, bucket_e2e_request_latency=None, collect_tokens_histogram=False, decode_log_interval=40, enable_request_time_stats_logging=False, kv_events_config=None, api_key=None, served_model_name='/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Qwen-32B', chat_template=None, completion_template=None, file_storage_path='sglang_storage', enable_cache_report=False, reasoning_parser=None, tool_call_parser=None, dp_size=2, load_balance_method='round_robin', dist_init_addr=None, nnodes=1, node_rank=0, json_model_override_args='{}', preferred_sampling_params=None, enable_lora=None, max_lora_rank=None, lora_target_modules=None, lora_paths=None, max_loras_per_batch=8, lora_backend='triton', attention_backend=None, sampling_backend='flashinfer', grammar_backend='xgrammar', mm_attention_backend=None, speculative_algorithm=None, speculative_draft_model_path=None, speculative_num_steps=None, speculative_eagle_topk=None, speculative_num_draft_tokens=None, speculative_accept_threshold_single=1.0, speculative_accept_threshold_acc=1.0, speculative_token_map=None, ep_size=1, enable_ep_moe=False, enable_deepep_moe=False, enable_flashinfer_moe=False, enable_flashinfer_allreduce_fusion=False, deepep_mode='auto', ep_num_redundant_experts=0, ep_dispatch_algorithm='static', init_expert_location='trivial', enable_eplb=False, eplb_algorithm='auto', eplb_rebalance_num_iterations=1000, eplb_rebalance_layers_per_chunk=None, expert_distribution_recorder_mode=None, expert_distribution_recorder_buffer_size=1000, enable_expert_distribution_metrics=False, deepep_config=None, moe_dense_tp_size=None, enable_hierarchical_cache=False, hicache_ratio=2.0, hicache_size=0, hicache_write_policy='write_through_selective', hicache_io_backend='', hicache_storage_backend=None, enable_double_sparsity=False, ds_channel_config_path=None, ds_heavy_channel_num=32, ds_heavy_token_num=256, ds_heavy_channel_type='qk', ds_sparse_decode_threshold=4096, disable_radix_cache=False, cuda_graph_max_bs=None, cuda_graph_bs=None, disable_cuda_graph=False, disable_cuda_graph_padding=False, enable_profile_cuda_graph=False, enable_nccl_nvls=False, enable_tokenizer_batch_encode=False, disable_outlines_disk_cache=False, disable_custom_all_reduce=False, enable_mscclpp=False, disable_overlap_schedule=False, enable_mixed_chunk=False, enable_dp_attention=False, enable_dp_lm_head=False, enable_two_batch_overlap=False, enable_torch_compile=False, torch_compile_max_bs=32, torchao_config='', enable_nan_detection=False, enable_p2p_check=False, triton_attention_reduce_in_fp32=False, triton_attention_num_kv_splits=8, num_continuous_decode_steps=1, delete_ckpt_after_loading=False, enable_memory_saver=False, allow_auto_truncate=False, enable_custom_logit_processor=False, flashinfer_mla_disable_ragged=False, disable_shared_experts_fusion=False, disable_chunked_prefix_cache=False, disable_fast_image_processor=False, enable_return_hidden_states=False, enable_triton_kernel_moe=False, debug_tensor_dump_output_folder=None, debug_tensor_dump_input_file=None, debug_tensor_dump_inject=False, debug_tensor_dump_prefill_only=False, disaggregation_mode='null', disaggregation_transfer_backend='mooncake', disaggregation_bootstrap_port=8998, disaggregation_decode_tp=None, disaggregation_decode_dp=None, disaggregation_prefill_pp=1, disaggregation_ib_device=None, num_reserved_decode_tokens=512, pdlb_url=None, custom_weight_loader=[], weight_loader_disable_mmap=False, enable_pdmux=False, sm_group_num=3)
[2025-07-28 09:31:56] Launch DP0 starting at GPU #0.
[2025-07-28 09:31:56] Launch DP1 starting at GPU #4.
[2025-07-28 09:32:03 DP1 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 09:32:03 DP1 TP0] Init torch distributed begin.
[2025-07-28 09:32:04 DP0 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 09:32:04 DP0 TP0] Init torch distributed begin.
[2025-07-28 09:32:05 DP1 TP0] sglang is using nccl==2.26.2
[2025-07-28 09:32:05 DP0 TP0] sglang is using nccl==2.26.2
[2025-07-28 09:32:06 DP1 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 09:32:06 DP0 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 09:32:06 DP1 TP0] Load weight begin. avail mem=78.21 GB

Loading safetensors checkpoint shards:   0% Completed | 0/8 [00:00<?, ?it/s]
[2025-07-28 09:32:07 DP0 TP0] Load weight begin. avail mem=78.21 GB

Loading safetensors checkpoint shards:   0% Completed | 0/8 [00:00<?, ?it/s]

Loading safetensors checkpoint shards:  12% Completed | 1/8 [00:00<00:05,  1.22it/s]

Loading safetensors checkpoint shards:  12% Completed | 1/8 [00:00<00:05,  1.22it/s]

Loading safetensors checkpoint shards:  25% Completed | 2/8 [00:01<00:05,  1.11it/s]

Loading safetensors checkpoint shards:  25% Completed | 2/8 [00:01<00:05,  1.14it/s]

Loading safetensors checkpoint shards:  38% Completed | 3/8 [00:02<00:03,  1.45it/s]

Loading safetensors checkpoint shards:  38% Completed | 3/8 [00:02<00:03,  1.49it/s]

Loading safetensors checkpoint shards:  50% Completed | 4/8 [00:03<00:03,  1.29it/s]

Loading safetensors checkpoint shards:  50% Completed | 4/8 [00:03<00:03,  1.33it/s]

Loading safetensors checkpoint shards:  62% Completed | 5/8 [00:04<00:02,  1.19it/s]

Loading safetensors checkpoint shards:  62% Completed | 5/8 [00:03<00:02,  1.23it/s]

Loading safetensors checkpoint shards:  75% Completed | 6/8 [00:05<00:01,  1.14it/s]

Loading safetensors checkpoint shards:  75% Completed | 6/8 [00:04<00:01,  1.17it/s]

Loading safetensors checkpoint shards:  88% Completed | 7/8 [00:05<00:00,  1.16it/s]

Loading safetensors checkpoint shards:  88% Completed | 7/8 [00:05<00:00,  1.18it/s]

Loading safetensors checkpoint shards: 100% Completed | 8/8 [00:06<00:00,  1.16it/s]

Loading safetensors checkpoint shards: 100% Completed | 8/8 [00:06<00:00,  1.19it/s]


Loading safetensors checkpoint shards: 100% Completed | 8/8 [00:06<00:00,  1.18it/s]

Loading safetensors checkpoint shards: 100% Completed | 8/8 [00:06<00:00,  1.22it/s]

[2025-07-28 09:32:13 DP1 TP0] Load weight end. type=Qwen2ForCausalLM, dtype=torch.bfloat16, avail mem=62.67 GB, mem usage=15.54 GB.
[2025-07-28 09:32:13 DP0 TP0] Load weight end. type=Qwen2ForCausalLM, dtype=torch.bfloat16, avail mem=62.67 GB, mem usage=15.54 GB.
[2025-07-28 09:32:13 DP1 TP0] KV Cache is allocated. #tokens: 833226, K size: 25.43 GB, V size: 25.43 GB
[2025-07-28 09:32:13 DP1 TP0] Memory pool end. avail mem=11.17 GB
[2025-07-28 09:32:13 DP1 TP1] KV Cache is allocated. #tokens: 833226, K size: 25.43 GB, V size: 25.43 GB
[2025-07-28 09:32:13 DP1 TP2] KV Cache is allocated. #tokens: 833226, K size: 25.43 GB, V size: 25.43 GB
[2025-07-28 09:32:13 DP1 TP3] KV Cache is allocated. #tokens: 833226, K size: 25.43 GB, V size: 25.43 GB
[2025-07-28 09:32:14 DP1 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.53 GB
[2025-07-28 09:32:14 DP0 TP2] KV Cache is allocated. #tokens: 833226, K size: 25.43 GB, V size: 25.43 GB
[2025-07-28 09:32:14 DP0 TP1] KV Cache is allocated. #tokens: 833226, K size: 25.43 GB, V size: 25.43 GB
[2025-07-28 09:32:14 DP0 TP3] KV Cache is allocated. #tokens: 833226, K size: 25.43 GB, V size: 25.43 GB
[2025-07-28 09:32:14 DP0 TP0] KV Cache is allocated. #tokens: 833226, K size: 25.43 GB, V size: 25.43 GB
[2025-07-28 09:32:14 DP0 TP0] Memory pool end. avail mem=11.17 GB
[2025-07-28 09:32:14 DP1 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.51 GB):   0%|          | 0/23 [00:00<?, ?it/s][2025-07-28 09:32:14 DP0 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.53 GB
[2025-07-28 09:32:14 DP0 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.51 GB):   0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.51 GB):   4%|▍         | 1/23 [00:01<00:24,  1.12s/it]
Capturing batches (bs=152 avail_mem=10.25 GB):   4%|▍         | 1/23 [00:01<00:24,  1.12s/it]
Capturing batches (bs=160 avail_mem=10.51 GB):   4%|▍         | 1/23 [00:01<00:23,  1.06s/it]
Capturing batches (bs=152 avail_mem=10.25 GB):   4%|▍         | 1/23 [00:01<00:23,  1.06s/it]
Capturing batches (bs=152 avail_mem=10.25 GB):   9%|▊         | 2/23 [00:01<00:14,  1.41it/s]
Capturing batches (bs=144 avail_mem=10.12 GB):   9%|▊         | 2/23 [00:01<00:14,  1.41it/s]
Capturing batches (bs=152 avail_mem=10.25 GB):   9%|▊         | 2/23 [00:01<00:14,  1.46it/s]
Capturing batches (bs=144 avail_mem=10.12 GB):   9%|▊         | 2/23 [00:01<00:14,  1.46it/s]
Capturing batches (bs=144 avail_mem=10.12 GB):  13%|█▎        | 3/23 [00:02<00:12,  1.58it/s]
Capturing batches (bs=136 avail_mem=10.03 GB):  13%|█▎        | 3/23 [00:02<00:12,  1.58it/s]
Capturing batches (bs=144 avail_mem=10.12 GB):  13%|█▎        | 3/23 [00:02<00:12,  1.59it/s]
Capturing batches (bs=136 avail_mem=10.03 GB):  13%|█▎        | 3/23 [00:02<00:12,  1.59it/s]
Capturing batches (bs=136 avail_mem=10.03 GB):  17%|█▋        | 4/23 [00:02<00:09,  1.93it/s]
Capturing batches (bs=128 avail_mem=9.92 GB):  17%|█▋        | 4/23 [00:02<00:09,  1.93it/s] 
Capturing batches (bs=136 avail_mem=10.03 GB):  17%|█▋        | 4/23 [00:02<00:09,  1.92it/s]
Capturing batches (bs=128 avail_mem=9.92 GB):  17%|█▋        | 4/23 [00:02<00:09,  1.92it/s] 
Capturing batches (bs=128 avail_mem=9.92 GB):  22%|██▏       | 5/23 [00:02<00:08,  2.18it/s]
Capturing batches (bs=120 avail_mem=9.83 GB):  22%|██▏       | 5/23 [00:02<00:08,  2.18it/s]
Capturing batches (bs=128 avail_mem=9.92 GB):  22%|██▏       | 5/23 [00:02<00:08,  2.16it/s]
Capturing batches (bs=120 avail_mem=9.83 GB):  22%|██▏       | 5/23 [00:02<00:08,  2.16it/s]
Capturing batches (bs=120 avail_mem=9.83 GB):  26%|██▌       | 6/23 [00:03<00:07,  2.36it/s]
Capturing batches (bs=112 avail_mem=9.73 GB):  26%|██▌       | 6/23 [00:03<00:07,  2.36it/s]
Capturing batches (bs=120 avail_mem=9.83 GB):  26%|██▌       | 6/23 [00:03<00:07,  2.35it/s]
Capturing batches (bs=112 avail_mem=9.73 GB):  26%|██▌       | 6/23 [00:03<00:07,  2.35it/s]
Capturing batches (bs=112 avail_mem=9.73 GB):  30%|███       | 7/23 [00:03<00:06,  2.51it/s]
Capturing batches (bs=104 avail_mem=9.65 GB):  30%|███       | 7/23 [00:03<00:06,  2.51it/s]
Capturing batches (bs=112 avail_mem=9.73 GB):  30%|███       | 7/23 [00:03<00:06,  2.47it/s]
Capturing batches (bs=104 avail_mem=9.65 GB):  30%|███       | 7/23 [00:03<00:06,  2.47it/s]
Capturing batches (bs=104 avail_mem=9.65 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.61it/s]
Capturing batches (bs=96 avail_mem=9.55 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.61it/s] 
Capturing batches (bs=104 avail_mem=9.65 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.58it/s]
Capturing batches (bs=96 avail_mem=9.55 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.58it/s] 
Capturing batches (bs=96 avail_mem=9.55 GB):  39%|███▉      | 9/23 [00:04<00:05,  2.65it/s]
Capturing batches (bs=88 avail_mem=9.47 GB):  39%|███▉      | 9/23 [00:04<00:05,  2.65it/s]
Capturing batches (bs=96 avail_mem=9.55 GB):  39%|███▉      | 9/23 [00:04<00:05,  2.64it/s]
Capturing batches (bs=88 avail_mem=9.47 GB):  39%|███▉      | 9/23 [00:04<00:05,  2.64it/s]
Capturing batches (bs=88 avail_mem=9.47 GB):  43%|████▎     | 10/23 [00:04<00:04,  2.74it/s]
Capturing batches (bs=80 avail_mem=9.39 GB):  43%|████▎     | 10/23 [00:04<00:04,  2.74it/s]
Capturing batches (bs=80 avail_mem=9.39 GB):  48%|████▊     | 11/23 [00:04<00:04,  2.81it/s]
Capturing batches (bs=72 avail_mem=9.37 GB):  48%|████▊     | 11/23 [00:04<00:04,  2.81it/s]
Capturing batches (bs=88 avail_mem=9.47 GB):  43%|████▎     | 10/23 [00:04<00:04,  2.68it/s]
Capturing batches (bs=80 avail_mem=9.39 GB):  43%|████▎     | 10/23 [00:04<00:04,  2.68it/s]
Capturing batches (bs=80 avail_mem=9.39 GB):  48%|████▊     | 11/23 [00:04<00:04,  2.74it/s]
Capturing batches (bs=72 avail_mem=9.37 GB):  48%|████▊     | 11/23 [00:04<00:04,  2.74it/s]
Capturing batches (bs=72 avail_mem=9.37 GB):  52%|█████▏    | 12/23 [00:05<00:03,  2.79it/s]
Capturing batches (bs=64 avail_mem=9.30 GB):  52%|█████▏    | 12/23 [00:05<00:03,  2.79it/s]
Capturing batches (bs=72 avail_mem=9.37 GB):  52%|█████▏    | 12/23 [00:05<00:03,  2.77it/s]
Capturing batches (bs=64 avail_mem=9.30 GB):  52%|█████▏    | 12/23 [00:05<00:03,  2.77it/s]
Capturing batches (bs=64 avail_mem=9.30 GB):  57%|█████▋    | 13/23 [00:05<00:03,  2.77it/s]
Capturing batches (bs=56 avail_mem=9.25 GB):  57%|█████▋    | 13/23 [00:05<00:03,  2.77it/s]
Capturing batches (bs=64 avail_mem=9.30 GB):  57%|█████▋    | 13/23 [00:05<00:03,  2.77it/s]
Capturing batches (bs=56 avail_mem=9.25 GB):  57%|█████▋    | 13/23 [00:05<00:03,  2.77it/s]
Capturing batches (bs=56 avail_mem=9.25 GB):  61%|██████    | 14/23 [00:05<00:03,  2.72it/s]
Capturing batches (bs=48 avail_mem=9.18 GB):  61%|██████    | 14/23 [00:05<00:03,  2.72it/s]
Capturing batches (bs=56 avail_mem=9.25 GB):  61%|██████    | 14/23 [00:05<00:03,  2.79it/s]
Capturing batches (bs=48 avail_mem=9.18 GB):  61%|██████    | 14/23 [00:05<00:03,  2.79it/s]
Capturing batches (bs=48 avail_mem=9.18 GB):  65%|██████▌   | 15/23 [00:06<00:02,  2.84it/s]
Capturing batches (bs=40 avail_mem=9.17 GB):  65%|██████▌   | 15/23 [00:06<00:02,  2.84it/s]
Capturing batches (bs=40 avail_mem=9.17 GB):  70%|██████▉   | 16/23 [00:06<00:02,  2.92it/s]
Capturing batches (bs=32 avail_mem=9.12 GB):  70%|██████▉   | 16/23 [00:06<00:02,  2.92it/s]
Capturing batches (bs=48 avail_mem=9.18 GB):  65%|██████▌   | 15/23 [00:06<00:02,  2.80it/s]
Capturing batches (bs=40 avail_mem=9.17 GB):  65%|██████▌   | 15/23 [00:06<00:02,  2.80it/s]
Capturing batches (bs=32 avail_mem=9.12 GB):  74%|███████▍  | 17/23 [00:06<00:02,  2.97it/s]
Capturing batches (bs=24 avail_mem=9.11 GB):  74%|███████▍  | 17/23 [00:06<00:02,  2.97it/s]
Capturing batches (bs=40 avail_mem=9.17 GB):  70%|██████▉   | 16/23 [00:06<00:02,  2.78it/s]
Capturing batches (bs=32 avail_mem=9.12 GB):  70%|██████▉   | 16/23 [00:06<00:02,  2.78it/s]
Capturing batches (bs=24 avail_mem=9.11 GB):  78%|███████▊  | 18/23 [00:07<00:01,  3.02it/s]
Capturing batches (bs=16 avail_mem=9.06 GB):  78%|███████▊  | 18/23 [00:07<00:01,  3.02it/s]
Capturing batches (bs=32 avail_mem=9.12 GB):  74%|███████▍  | 17/23 [00:07<00:02,  2.75it/s]
Capturing batches (bs=24 avail_mem=9.11 GB):  74%|███████▍  | 17/23 [00:07<00:02,  2.75it/s]
Capturing batches (bs=16 avail_mem=9.06 GB):  83%|████████▎ | 19/23 [00:07<00:01,  3.07it/s]
Capturing batches (bs=8 avail_mem=9.05 GB):  83%|████████▎ | 19/23 [00:07<00:01,  3.07it/s] 
Capturing batches (bs=24 avail_mem=9.11 GB):  78%|███████▊  | 18/23 [00:07<00:01,  2.80it/s]
Capturing batches (bs=16 avail_mem=9.06 GB):  78%|███████▊  | 18/23 [00:07<00:01,  2.80it/s]
Capturing batches (bs=8 avail_mem=9.05 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.12it/s]
Capturing batches (bs=4 avail_mem=9.02 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.12it/s]
Capturing batches (bs=16 avail_mem=9.06 GB):  83%|████████▎ | 19/23 [00:07<00:01,  2.83it/s]
Capturing batches (bs=8 avail_mem=9.05 GB):  83%|████████▎ | 19/23 [00:07<00:01,  2.83it/s] 
Capturing batches (bs=4 avail_mem=9.02 GB):  91%|█████████▏| 21/23 [00:08<00:00,  3.14it/s]
Capturing batches (bs=2 avail_mem=9.01 GB):  91%|█████████▏| 21/23 [00:08<00:00,  3.14it/s]
Capturing batches (bs=8 avail_mem=9.05 GB):  87%|████████▋ | 20/23 [00:08<00:01,  2.77it/s]
Capturing batches (bs=4 avail_mem=9.02 GB):  87%|████████▋ | 20/23 [00:08<00:01,  2.77it/s]
Capturing batches (bs=2 avail_mem=9.01 GB):  96%|█████████▌| 22/23 [00:08<00:00,  3.10it/s]
Capturing batches (bs=1 avail_mem=8.98 GB):  96%|█████████▌| 22/23 [00:08<00:00,  3.10it/s]
Capturing batches (bs=4 avail_mem=9.02 GB):  91%|█████████▏| 21/23 [00:08<00:00,  2.81it/s]
Capturing batches (bs=2 avail_mem=9.01 GB):  91%|█████████▏| 21/23 [00:08<00:00,  2.81it/s][2025-07-28 09:32:23 DP1 TP2] Registering 2967 cuda graph addresses
[2025-07-28 09:32:23 DP1 TP3] Registering 2967 cuda graph addresses
[2025-07-28 09:32:23 DP1 TP1] Registering 2967 cuda graph addresses

Capturing batches (bs=1 avail_mem=8.98 GB): 100%|██████████| 23/23 [00:08<00:00,  3.07it/s]
Capturing batches (bs=1 avail_mem=8.98 GB): 100%|██████████| 23/23 [00:08<00:00,  2.60it/s]
[2025-07-28 09:32:23 DP1 TP0] Registering 2967 cuda graph addresses
[2025-07-28 09:32:23 DP1 TP0] Capture cuda graph end. Time elapsed: 9.13 s. mem usage=1.56 GB. avail mem=8.97 GB.

Capturing batches (bs=2 avail_mem=9.01 GB):  96%|█████████▌| 22/23 [00:08<00:00,  2.65it/s]
Capturing batches (bs=1 avail_mem=8.98 GB):  96%|█████████▌| 22/23 [00:08<00:00,  2.65it/s][2025-07-28 09:32:23 DP1 TP0] max_total_num_tokens=833226, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=8.97 GB

Capturing batches (bs=1 avail_mem=8.98 GB): 100%|██████████| 23/23 [00:09<00:00,  2.77it/s]
Capturing batches (bs=1 avail_mem=8.98 GB): 100%|██████████| 23/23 [00:09<00:00,  2.50it/s]
[2025-07-28 09:32:23 DP0 TP0] Registering 2967 cuda graph addresses
[2025-07-28 09:32:23 DP0 TP1] Registering 2967 cuda graph addresses
[2025-07-28 09:32:23 DP0 TP2] Registering 2967 cuda graph addresses
[2025-07-28 09:32:23 DP0 TP3] Registering 2967 cuda graph addresses
[2025-07-28 09:32:23 DP0 TP0] Capture cuda graph end. Time elapsed: 9.45 s. mem usage=1.56 GB. avail mem=8.97 GB.
[2025-07-28 09:32:24 DP0 TP0] max_total_num_tokens=833226, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=8.97 GB
[2025-07-28 09:32:24] INFO:     Started server process [2241211]
[2025-07-28 09:32:24] INFO:     Waiting for application startup.
[2025-07-28 09:32:24] INFO:     Application startup complete.
[2025-07-28 09:32:24] INFO:     Uvicorn running on http://127.0.0.1:8002 (Press CTRL+C to quit)
[2025-07-28 09:32:25] INFO:     127.0.0.1:45922 - "GET /get_model_info HTTP/1.1" 200 OK
[2025-07-28 09:32:25 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 7, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:32:25 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 7, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:32:26] INFO:     127.0.0.1:45946 - "GET /health HTTP/1.1" 200 OK
[2025-07-28 09:32:26 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 188, #cached-token: 1, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 09:32:26 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 229, #cached-token: 1, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 09:32:27] INFO:     127.0.0.1:45936 - "POST /generate HTTP/1.1" 200 OK
[2025-07-28 09:32:27] The server is fired up and ready to roll!
[2025-07-28 09:32:27 DP1 TP0] Decode batch. #running-req: 1, #token: 229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 11.10, #queue-req: 0, 
[2025-07-28 09:32:27 DP0 TP0] Decode batch. #running-req: 1, #token: 270, token usage: 0.00, cuda graph: True, gen throughput (token/s): 13.40, #queue-req: 0, 
[2025-07-28 09:32:28 DP1 TP0] Decode batch. #running-req: 1, #token: 269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.10, #queue-req: 0, 
[2025-07-28 09:32:28 DP0 TP0] Decode batch. #running-req: 1, #token: 310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.55, #queue-req: 0, 
[2025-07-28 09:32:28 DP1 TP0] Decode batch. #running-req: 1, #token: 309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.67, #queue-req: 0, 
[2025-07-28 09:32:29 DP0 TP0] Decode batch. #running-req: 1, #token: 350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.49, #queue-req: 0, 
[2025-07-28 09:32:29 DP1 TP0] Decode batch. #running-req: 1, #token: 349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.64, #queue-req: 0, 
[2025-07-28 09:32:29 DP0 TP0] Decode batch. #running-req: 1, #token: 390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 09:32:30 DP1 TP0] Decode batch. #running-req: 1, #token: 389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.42, #queue-req: 0, 
[2025-07-28 09:32:30 DP0 TP0] Decode batch. #running-req: 1, #token: 430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:32:30 DP1 TP0] Decode batch. #running-req: 1, #token: 429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:32:30 DP0 TP0] Decode batch. #running-req: 1, #token: 470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:32:31 DP1 TP0] Decode batch. #running-req: 1, #token: 469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.18, #queue-req: 0, 
[2025-07-28 09:32:31 DP0 TP0] Decode batch. #running-req: 1, #token: 510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.12, #queue-req: 0, 
[2025-07-28 09:32:31 DP1 TP0] Decode batch. #running-req: 1, #token: 509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:32:31 DP0 TP0] Decode batch. #running-req: 1, #token: 550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:32:32 DP1 TP0] Decode batch. #running-req: 1, #token: 549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:32:32 DP0 TP0] Decode batch. #running-req: 1, #token: 590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 09:32:32 DP1 TP0] Decode batch. #running-req: 1, #token: 589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:32:33 DP0 TP0] Decode batch. #running-req: 1, #token: 630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.10, #queue-req: 0, 
[2025-07-28 09:32:33 DP1 TP0] Decode batch. #running-req: 1, #token: 629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:32:33 DP0 TP0] Decode batch. #running-req: 1, #token: 670, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.88, #queue-req: 0, 
[2025-07-28 09:32:33] INFO:     127.0.0.1:45968 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:32:33 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 80, #cached-token: 84, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 09:32:34 DP0 TP0] Decode batch. #running-req: 2, #token: 822, token usage: 0.00, cuda graph: True, gen throughput (token/s): 110.61, #queue-req: 0, 
[2025-07-28 09:32:34 DP0 TP0] Decode batch. #running-req: 2, #token: 902, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.37, #queue-req: 0, 
[2025-07-28 09:32:35 DP0 TP0] Decode batch. #running-req: 2, #token: 982, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.79, #queue-req: 0, 
[2025-07-28 09:32:36 DP0 TP0] Decode batch. #running-req: 2, #token: 1062, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.96, #queue-req: 0, 
[2025-07-28 09:32:36] INFO:     127.0.0.1:45958 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:32:36 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 203, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:32:36 DP0 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 92.96, #queue-req: 0, 
[2025-07-28 09:32:36 DP1 TP0] Decode batch. #running-req: 1, #token: 317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 12.57, #queue-req: 0, 
[2025-07-28 09:32:37 DP0 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 09:32:37 DP1 TP0] Decode batch. #running-req: 1, #token: 357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.52, #queue-req: 0, 
[2025-07-28 09:32:37 DP0 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 09:32:37 DP1 TP0] Decode batch. #running-req: 1, #token: 397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.39, #queue-req: 0, 
[2025-07-28 09:32:38 DP0 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 67.87, #queue-req: 0, 
[2025-07-28 09:32:38 DP1 TP0] Decode batch. #running-req: 1, #token: 437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:32:38 DP0 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.89, #queue-req: 0, 
[2025-07-28 09:32:39 DP1 TP0] Decode batch. #running-req: 1, #token: 477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:32:39 DP0 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.04, #queue-req: 0, 
[2025-07-28 09:32:39 DP1 TP0] Decode batch. #running-req: 1, #token: 517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:32:40 DP0 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.15, #queue-req: 0, 
[2025-07-28 09:32:40 DP1 TP0] Decode batch. #running-req: 1, #token: 557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:32:40 DP0 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:32:40 DP1 TP0] Decode batch. #running-req: 1, #token: 597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:32:41 DP0 TP0] Decode batch. #running-req: 1, #token: 676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.15, #queue-req: 0, 
[2025-07-28 09:32:41 DP1 TP0] Decode batch. #running-req: 1, #token: 637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:32:41 DP0 TP0] Decode batch. #running-req: 1, #token: 716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.18, #queue-req: 0, 
[2025-07-28 09:32:41 DP1 TP0] Decode batch. #running-req: 1, #token: 677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:32:42 DP0 TP0] Decode batch. #running-req: 1, #token: 756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:32:42 DP1 TP0] Decode batch. #running-req: 1, #token: 717, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:32:42 DP0 TP0] Decode batch. #running-req: 1, #token: 796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.03, #queue-req: 0, 
[2025-07-28 09:32:43 DP1 TP0] Decode batch. #running-req: 1, #token: 757, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:32:43 DP0 TP0] Decode batch. #running-req: 1, #token: 836, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:32:43 DP1 TP0] Decode batch. #running-req: 1, #token: 797, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:32:44 DP0 TP0] Decode batch. #running-req: 1, #token: 876, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.17, #queue-req: 0, 
[2025-07-28 09:32:44 DP1 TP0] Decode batch. #running-req: 1, #token: 837, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 09:32:44 DP0 TP0] Decode batch. #running-req: 1, #token: 916, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:32:44 DP1 TP0] Decode batch. #running-req: 1, #token: 877, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:32:45 DP0 TP0] Decode batch. #running-req: 1, #token: 956, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.01, #queue-req: 0, 
[2025-07-28 09:32:45 DP1 TP0] Decode batch. #running-req: 1, #token: 917, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:32:45 DP0 TP0] Decode batch. #running-req: 1, #token: 996, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.94, #queue-req: 0, 
[2025-07-28 09:32:45 DP1 TP0] Decode batch. #running-req: 1, #token: 957, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:32:46] INFO:     127.0.0.1:48178 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:32:46 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 123, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:32:46 DP0 TP0] Decode batch. #running-req: 1, #token: 217, token usage: 0.00, cuda graph: True, gen throughput (token/s): 62.28, #queue-req: 0, 
[2025-07-28 09:32:46 DP1 TP0] Decode batch. #running-req: 1, #token: 997, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:32:47 DP0 TP0] Decode batch. #running-req: 1, #token: 257, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.12, #queue-req: 0, 
[2025-07-28 09:32:47 DP1 TP0] Decode batch. #running-req: 1, #token: 1037, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:32:47 DP0 TP0] Decode batch. #running-req: 1, #token: 297, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 09:32:47 DP1 TP0] Decode batch. #running-req: 1, #token: 1077, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.11, #queue-req: 0, 
[2025-07-28 09:32:48 DP0 TP0] Decode batch. #running-req: 1, #token: 337, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.60, #queue-req: 0, 
[2025-07-28 09:32:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1117, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:32:48 DP0 TP0] Decode batch. #running-req: 1, #token: 377, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.39, #queue-req: 0, 
[2025-07-28 09:32:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1157, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.15, #queue-req: 0, 
[2025-07-28 09:32:49 DP0 TP0] Decode batch. #running-req: 1, #token: 417, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:32:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1197, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.15, #queue-req: 0, 
[2025-07-28 09:32:49 DP0 TP0] Decode batch. #running-req: 1, #token: 457, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.17, #queue-req: 0, 
[2025-07-28 09:32:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1237, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 09:32:50 DP0 TP0] Decode batch. #running-req: 1, #token: 497, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 09:32:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.17, #queue-req: 0, 
[2025-07-28 09:32:51 DP0 TP0] Decode batch. #running-req: 1, #token: 537, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 09:32:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.13, #queue-req: 0, 
[2025-07-28 09:32:51 DP0 TP0] Decode batch. #running-req: 1, #token: 577, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.97, #queue-req: 0, 
[2025-07-28 09:32:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.13, #queue-req: 0, 
[2025-07-28 09:32:52] INFO:     127.0.0.1:48182 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:32:52 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 129, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:32:52 DP0 TP0] Decode batch. #running-req: 1, #token: 617, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.17, #queue-req: 0, 
[2025-07-28 09:32:52 DP1 TP0] Decode batch. #running-req: 1, #token: 231, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.32, #queue-req: 0, 
[2025-07-28 09:32:52 DP0 TP0] Decode batch. #running-req: 1, #token: 657, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.15, #queue-req: 0, 
[2025-07-28 09:32:52 DP1 TP0] Decode batch. #running-req: 1, #token: 271, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.09, #queue-req: 0, 
[2025-07-28 09:32:53 DP0 TP0] Decode batch. #running-req: 1, #token: 697, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:32:53 DP1 TP0] Decode batch. #running-req: 1, #token: 311, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.69, #queue-req: 0, 
[2025-07-28 09:32:54 DP0 TP0] Decode batch. #running-req: 1, #token: 737, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.08, #queue-req: 0, 
[2025-07-28 09:32:54 DP1 TP0] Decode batch. #running-req: 1, #token: 351, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.65, #queue-req: 0, 
[2025-07-28 09:32:54] INFO:     127.0.0.1:43404 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:32:54 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 182, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:32:54 DP0 TP0] Decode batch. #running-req: 1, #token: 278, token usage: 0.00, cuda graph: True, gen throughput (token/s): 62.68, #queue-req: 0, 
[2025-07-28 09:32:54 DP1 TP0] Decode batch. #running-req: 1, #token: 391, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.49, #queue-req: 0, 
[2025-07-28 09:32:55 DP0 TP0] Decode batch. #running-req: 1, #token: 318, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.61, #queue-req: 0, 
[2025-07-28 09:32:55 DP1 TP0] Decode batch. #running-req: 1, #token: 431, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:32:55 DP0 TP0] Decode batch. #running-req: 1, #token: 358, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.56, #queue-req: 0, 
[2025-07-28 09:32:55 DP1 TP0] Decode batch. #running-req: 1, #token: 471, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:32:56 DP0 TP0] Decode batch. #running-req: 1, #token: 398, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.39, #queue-req: 0, 
[2025-07-28 09:32:56 DP1 TP0] Decode batch. #running-req: 1, #token: 511, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:32:56 DP0 TP0] Decode batch. #running-req: 1, #token: 438, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.15, #queue-req: 0, 
[2025-07-28 09:32:57 DP1 TP0] Decode batch. #running-req: 1, #token: 551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:32:57 DP0 TP0] Decode batch. #running-req: 1, #token: 478, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:32:57 DP1 TP0] Decode batch. #running-req: 1, #token: 591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:32:58 DP0 TP0] Decode batch. #running-req: 1, #token: 518, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.17, #queue-req: 0, 
[2025-07-28 09:32:58 DP1 TP0] Decode batch. #running-req: 1, #token: 631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:32:58 DP0 TP0] Decode batch. #running-req: 1, #token: 558, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:32:58 DP1 TP0] Decode batch. #running-req: 1, #token: 671, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:32:59 DP0 TP0] Decode batch. #running-req: 1, #token: 598, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:32:59 DP1 TP0] Decode batch. #running-req: 1, #token: 711, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:32:59 DP0 TP0] Decode batch. #running-req: 1, #token: 638, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:32:59 DP1 TP0] Decode batch. #running-req: 1, #token: 751, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:33:00 DP0 TP0] Decode batch. #running-req: 1, #token: 678, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.17, #queue-req: 0, 
[2025-07-28 09:33:00 DP1 TP0] Decode batch. #running-req: 1, #token: 791, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:33:01 DP0 TP0] Decode batch. #running-req: 1, #token: 718, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.09, #queue-req: 0, 
[2025-07-28 09:33:01 DP1 TP0] Decode batch. #running-req: 1, #token: 831, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:33:01] INFO:     127.0.0.1:43414 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:33:01 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 169, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:33:01 DP0 TP0] Decode batch. #running-req: 1, #token: 758, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 09:33:01 DP1 TP0] Decode batch. #running-req: 1, #token: 283, token usage: 0.00, cuda graph: True, gen throughput (token/s): 62.66, #queue-req: 0, 
[2025-07-28 09:33:02 DP0 TP0] Decode batch. #running-req: 1, #token: 798, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.06, #queue-req: 0, 
[2025-07-28 09:33:02 DP1 TP0] Decode batch. #running-req: 1, #token: 323, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.69, #queue-req: 0, 
[2025-07-28 09:33:02 DP0 TP0] Decode batch. #running-req: 1, #token: 838, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.04, #queue-req: 0, 
[2025-07-28 09:33:02 DP1 TP0] Decode batch. #running-req: 1, #token: 363, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.60, #queue-req: 0, 
[2025-07-28 09:33:03 DP0 TP0] Decode batch. #running-req: 1, #token: 878, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.13, #queue-req: 0, 
[2025-07-28 09:33:03 DP1 TP0] Decode batch. #running-req: 1, #token: 403, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.46, #queue-req: 0, 
[2025-07-28 09:33:03 DP0 TP0] Decode batch. #running-req: 1, #token: 918, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:33:03 DP1 TP0] Decode batch. #running-req: 1, #token: 443, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:33:04] INFO:     127.0.0.1:39126 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:33:04 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 222, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:33:04 DP1 TP0] Decode batch. #running-req: 1, #token: 483, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 09:33:04 DP0 TP0] Decode batch. #running-req: 1, #token: 321, token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.47, #queue-req: 0, 
[2025-07-28 09:33:05 DP1 TP0] Decode batch. #running-req: 1, #token: 523, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:33:05 DP0 TP0] Decode batch. #running-req: 1, #token: 361, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.53, #queue-req: 0, 
[2025-07-28 09:33:05 DP1 TP0] Decode batch. #running-req: 1, #token: 563, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:33:05 DP0 TP0] Decode batch. #running-req: 1, #token: 401, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:33:06 DP1 TP0] Decode batch. #running-req: 1, #token: 603, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:33:06 DP0 TP0] Decode batch. #running-req: 1, #token: 441, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 09:33:06 DP1 TP0] Decode batch. #running-req: 1, #token: 643, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:33:06 DP0 TP0] Decode batch. #running-req: 1, #token: 481, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.15, #queue-req: 0, 
[2025-07-28 09:33:07 DP1 TP0] Decode batch. #running-req: 1, #token: 683, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 09:33:07 DP0 TP0] Decode batch. #running-req: 1, #token: 521, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:33:08 DP1 TP0] Decode batch. #running-req: 1, #token: 723, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:33:08 DP0 TP0] Decode batch. #running-req: 1, #token: 561, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:33:08 DP1 TP0] Decode batch. #running-req: 1, #token: 763, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:33:08 DP0 TP0] Decode batch. #running-req: 1, #token: 601, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 09:33:09 DP1 TP0] Decode batch. #running-req: 1, #token: 803, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:33:09 DP0 TP0] Decode batch. #running-req: 1, #token: 641, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.18, #queue-req: 0, 
[2025-07-28 09:33:09 DP1 TP0] Decode batch. #running-req: 1, #token: 843, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:33:09 DP0 TP0] Decode batch. #running-req: 1, #token: 681, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.11, #queue-req: 0, 
[2025-07-28 09:33:10 DP1 TP0] Decode batch. #running-req: 1, #token: 883, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:33:10 DP0 TP0] Decode batch. #running-req: 1, #token: 721, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.13, #queue-req: 0, 
[2025-07-28 09:33:10 DP1 TP0] Decode batch. #running-req: 1, #token: 923, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:33:10 DP0 TP0] Decode batch. #running-req: 1, #token: 761, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:33:11 DP1 TP0] Decode batch. #running-req: 1, #token: 963, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:33:11 DP0 TP0] Decode batch. #running-req: 1, #token: 801, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:33:12 DP1 TP0] Decode batch. #running-req: 1, #token: 1003, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:33:12 DP0 TP0] Decode batch. #running-req: 1, #token: 841, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.18, #queue-req: 0, 
[2025-07-28 09:33:12 DP1 TP0] Decode batch. #running-req: 1, #token: 1043, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:33:12 DP0 TP0] Decode batch. #running-req: 1, #token: 881, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:33:13 DP1 TP0] Decode batch. #running-req: 1, #token: 1083, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:33:13 DP0 TP0] Decode batch. #running-req: 1, #token: 921, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:33:13 DP1 TP0] Decode batch. #running-req: 1, #token: 1123, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 09:33:13 DP0 TP0] Decode batch. #running-req: 1, #token: 961, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:33:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1163, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.15, #queue-req: 0, 
[2025-07-28 09:33:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1001, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:33:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1203, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.17, #queue-req: 0, 
[2025-07-28 09:33:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1041, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:33:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1243, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.17, #queue-req: 0, 
[2025-07-28 09:33:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1081, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.08, #queue-req: 0, 
[2025-07-28 09:33:16 DP1 TP0] Decode batch. #running-req: 1, #token: 1283, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:33:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1121, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.10, #queue-req: 0, 
[2025-07-28 09:33:16 DP1 TP0] Decode batch. #running-req: 1, #token: 1323, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 09:33:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1161, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.99, #queue-req: 0, 
[2025-07-28 09:33:17 DP1 TP0] Decode batch. #running-req: 1, #token: 1363, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.11, #queue-req: 0, 
[2025-07-28 09:33:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1201, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.11, #queue-req: 0, 
[2025-07-28 09:33:17 DP1 TP0] Decode batch. #running-req: 1, #token: 1403, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.07, #queue-req: 0, 
[2025-07-28 09:33:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1241, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.11, #queue-req: 0, 
[2025-07-28 09:33:18 DP1 TP0] Decode batch. #running-req: 1, #token: 1443, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.05, #queue-req: 0, 
[2025-07-28 09:33:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1281, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.02, #queue-req: 0, 
[2025-07-28 09:33:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1483, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.11, #queue-req: 0, 
[2025-07-28 09:33:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1321, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.11, #queue-req: 0, 
[2025-07-28 09:33:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1523, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.06, #queue-req: 0, 
[2025-07-28 09:33:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1361, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.12, #queue-req: 0, 
[2025-07-28 09:33:20 DP1 TP0] Decode batch. #running-req: 1, #token: 1563, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.08, #queue-req: 0, 
[2025-07-28 09:33:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1401, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.12, #queue-req: 0, 
[2025-07-28 09:33:20 DP1 TP0] Decode batch. #running-req: 1, #token: 1603, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.07, #queue-req: 0, 
[2025-07-28 09:33:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1441, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.02, #queue-req: 0, 
[2025-07-28 09:33:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1643, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.06, #queue-req: 0, 
[2025-07-28 09:33:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1481, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.05, #queue-req: 0, 
[2025-07-28 09:33:21 DP1 TP0] Decode batch. #running-req: 1, #token: 1683, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.08, #queue-req: 0, 
[2025-07-28 09:33:21 DP0 TP0] Decode batch. #running-req: 1, #token: 1521, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.05, #queue-req: 0, 
[2025-07-28 09:33:21] INFO:     127.0.0.1:39128 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:33:21 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 164, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:33:22 DP0 TP0] Decode batch. #running-req: 1, #token: 1561, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.93, #queue-req: 0, 
[2025-07-28 09:33:22 DP1 TP0] Decode batch. #running-req: 1, #token: 289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.80, #queue-req: 0, 
[2025-07-28 09:33:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1601, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.04, #queue-req: 0, 
[2025-07-28 09:33:23 DP1 TP0] Decode batch. #running-req: 1, #token: 329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.71, #queue-req: 0, 
[2025-07-28 09:33:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1641, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.02, #queue-req: 0, 
[2025-07-28 09:33:23 DP1 TP0] Decode batch. #running-req: 1, #token: 369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.55, #queue-req: 0, 
[2025-07-28 09:33:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1681, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.03, #queue-req: 0, 
[2025-07-28 09:33:24 DP1 TP0] Decode batch. #running-req: 1, #token: 409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.42, #queue-req: 0, 
[2025-07-28 09:33:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1721, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.06, #queue-req: 0, 
[2025-07-28 09:33:24 DP1 TP0] Decode batch. #running-req: 1, #token: 449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:33:25] INFO:     127.0.0.1:55676 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:33:25 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 150, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:33:25 DP1 TP0] Decode batch. #running-req: 1, #token: 489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:33:25 DP0 TP0] Decode batch. #running-req: 1, #token: 263, token usage: 0.00, cuda graph: True, gen throughput (token/s): 62.83, #queue-req: 0, 
[2025-07-28 09:33:25 DP1 TP0] Decode batch. #running-req: 1, #token: 529, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:33:26 DP0 TP0] Decode batch. #running-req: 1, #token: 303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.65, #queue-req: 0, 
[2025-07-28 09:33:26 DP1 TP0] Decode batch. #running-req: 1, #token: 569, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:33:26 DP0 TP0] Decode batch. #running-req: 1, #token: 343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.63, #queue-req: 0, 
[2025-07-28 09:33:27 DP1 TP0] Decode batch. #running-req: 1, #token: 609, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:33:27 DP0 TP0] Decode batch. #running-req: 1, #token: 383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.47, #queue-req: 0, 
[2025-07-28 09:33:27 DP1 TP0] Decode batch. #running-req: 1, #token: 649, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:33:27 DP0 TP0] Decode batch. #running-req: 1, #token: 423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 09:33:28 DP1 TP0] Decode batch. #running-req: 1, #token: 689, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:33:28 DP0 TP0] Decode batch. #running-req: 1, #token: 463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:33:28 DP1 TP0] Decode batch. #running-req: 1, #token: 729, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 09:33:28 DP0 TP0] Decode batch. #running-req: 1, #token: 503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 09:33:29 DP1 TP0] Decode batch. #running-req: 1, #token: 769, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:33:29 DP0 TP0] Decode batch. #running-req: 1, #token: 543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:33:30 DP1 TP0] Decode batch. #running-req: 1, #token: 809, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:33:30 DP0 TP0] Decode batch. #running-req: 1, #token: 583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:33:30 DP1 TP0] Decode batch. #running-req: 1, #token: 849, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:33:30 DP0 TP0] Decode batch. #running-req: 1, #token: 623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:33:31 DP1 TP0] Decode batch. #running-req: 1, #token: 889, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:33:31 DP0 TP0] Decode batch. #running-req: 1, #token: 663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:33:31 DP1 TP0] Decode batch. #running-req: 1, #token: 929, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:33:31 DP0 TP0] Decode batch. #running-req: 1, #token: 703, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:33:32 DP1 TP0] Decode batch. #running-req: 1, #token: 969, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:33:32 DP0 TP0] Decode batch. #running-req: 1, #token: 743, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:33:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1009, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:33:32 DP0 TP0] Decode batch. #running-req: 1, #token: 783, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:33:33 DP1 TP0] Decode batch. #running-req: 1, #token: 1049, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:33:33 DP0 TP0] Decode batch. #running-req: 1, #token: 823, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:33:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1089, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:33:34 DP0 TP0] Decode batch. #running-req: 1, #token: 863, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.12, #queue-req: 0, 
[2025-07-28 09:33:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1129, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:33:34 DP0 TP0] Decode batch. #running-req: 1, #token: 903, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:33:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1169, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 09:33:35 DP0 TP0] Decode batch. #running-req: 1, #token: 943, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:33:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1209, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.18, #queue-req: 0, 
[2025-07-28 09:33:35 DP0 TP0] Decode batch. #running-req: 1, #token: 983, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:33:36] INFO:     127.0.0.1:58540 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:33:36 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 137, #cached-token: 84, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 09:33:36 DP1 TP0] Decode batch. #running-req: 2, #token: 1392, token usage: 0.00, cuda graph: True, gen throughput (token/s): 71.74, #queue-req: 0, 
[2025-07-28 09:33:37 DP1 TP0] Decode batch. #running-req: 2, #token: 1472, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.04, #queue-req: 0, 
[2025-07-28 09:33:37 DP1 TP0] Decode batch. #running-req: 2, #token: 1552, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.98, #queue-req: 0, 
[2025-07-28 09:33:38 DP1 TP0] Decode batch. #running-req: 2, #token: 1632, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.10, #queue-req: 0, 
[2025-07-28 09:33:38 DP1 TP0] Decode batch. #running-req: 2, #token: 1712, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.02, #queue-req: 0, 
[2025-07-28 09:33:39 DP1 TP0] Decode batch. #running-req: 2, #token: 1792, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.88, #queue-req: 0, 
[2025-07-28 09:33:39 DP1 TP0] Decode batch. #running-req: 2, #token: 1872, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.94, #queue-req: 0, 
[2025-07-28 09:33:40 DP1 TP0] Decode batch. #running-req: 2, #token: 1952, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.89, #queue-req: 0, 
[2025-07-28 09:33:41 DP1 TP0] Decode batch. #running-req: 2, #token: 2032, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.90, #queue-req: 0, 
[2025-07-28 09:33:41 DP1 TP0] Decode batch. #running-req: 2, #token: 2112, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.89, #queue-req: 0, 
[2025-07-28 09:33:42 DP1 TP0] Decode batch. #running-req: 2, #token: 2192, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.87, #queue-req: 0, 
[2025-07-28 09:33:42] INFO:     127.0.0.1:43100 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:33:42 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 188, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:33:42 DP0 TP0] Decode batch. #running-req: 1, #token: 285, token usage: 0.00, cuda graph: True, gen throughput (token/s): 5.70, #queue-req: 0, 
[2025-07-28 09:33:42 DP1 TP0] Decode batch. #running-req: 1, #token: 667, token usage: 0.00, cuda graph: True, gen throughput (token/s): 102.83, #queue-req: 0, 
[2025-07-28 09:33:43 DP0 TP0] Decode batch. #running-req: 1, #token: 325, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.80, #queue-req: 0, 
[2025-07-28 09:33:43 DP1 TP0] Decode batch. #running-req: 1, #token: 707, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:33:44 DP0 TP0] Decode batch. #running-req: 1, #token: 365, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.58, #queue-req: 0, 
[2025-07-28 09:33:44 DP1 TP0] Decode batch. #running-req: 1, #token: 747, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:33:44 DP0 TP0] Decode batch. #running-req: 1, #token: 405, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.41, #queue-req: 0, 
[2025-07-28 09:33:44 DP1 TP0] Decode batch. #running-req: 1, #token: 787, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:33:45 DP0 TP0] Decode batch. #running-req: 1, #token: 445, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:33:45 DP1 TP0] Decode batch. #running-req: 1, #token: 827, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:33:45 DP0 TP0] Decode batch. #running-req: 1, #token: 485, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:33:45 DP1 TP0] Decode batch. #running-req: 1, #token: 867, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:33:46] INFO:     127.0.0.1:40118 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:33:46 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 186, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:33:46 DP0 TP0] Decode batch. #running-req: 1, #token: 525, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:33:46 DP1 TP0] Decode batch. #running-req: 1, #token: 299, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.12, #queue-req: 0, 
[2025-07-28 09:33:46 DP0 TP0] Decode batch. #running-req: 1, #token: 565, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:33:47 DP1 TP0] Decode batch. #running-req: 1, #token: 339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.68, #queue-req: 0, 
[2025-07-28 09:33:47 DP0 TP0] Decode batch. #running-req: 1, #token: 605, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:33:47 DP1 TP0] Decode batch. #running-req: 1, #token: 379, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.55, #queue-req: 0, 
[2025-07-28 09:33:48 DP0 TP0] Decode batch. #running-req: 1, #token: 645, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:33:48 DP1 TP0] Decode batch. #running-req: 1, #token: 419, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 09:33:48 DP0 TP0] Decode batch. #running-req: 1, #token: 685, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:33:48 DP1 TP0] Decode batch. #running-req: 1, #token: 459, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 09:33:49 DP0 TP0] Decode batch. #running-req: 1, #token: 725, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:33:49 DP1 TP0] Decode batch. #running-req: 1, #token: 499, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 09:33:49 DP0 TP0] Decode batch. #running-req: 1, #token: 765, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:33:49 DP1 TP0] Decode batch. #running-req: 1, #token: 539, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:33:50 DP0 TP0] Decode batch. #running-req: 1, #token: 805, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:33:50 DP1 TP0] Decode batch. #running-req: 1, #token: 579, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:33:50 DP0 TP0] Decode batch. #running-req: 1, #token: 845, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:33:51 DP1 TP0] Decode batch. #running-req: 1, #token: 619, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:33:51 DP0 TP0] Decode batch. #running-req: 1, #token: 885, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:33:51 DP1 TP0] Decode batch. #running-req: 1, #token: 659, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:33:52 DP0 TP0] Decode batch. #running-req: 1, #token: 925, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 09:33:52 DP1 TP0] Decode batch. #running-req: 1, #token: 699, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:33:52 DP0 TP0] Decode batch. #running-req: 1, #token: 965, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:33:52 DP1 TP0] Decode batch. #running-req: 1, #token: 739, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:33:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1005, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:33:53 DP1 TP0] Decode batch. #running-req: 1, #token: 779, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:33:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1045, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.13, #queue-req: 0, 
[2025-07-28 09:33:53 DP1 TP0] Decode batch. #running-req: 1, #token: 819, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:33:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1085, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.11, #queue-req: 0, 
[2025-07-28 09:33:54 DP1 TP0] Decode batch. #running-req: 1, #token: 859, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:33:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1125, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.09, #queue-req: 0, 
[2025-07-28 09:33:55 DP1 TP0] Decode batch. #running-req: 1, #token: 899, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:33:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1165, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.13, #queue-req: 0, 
[2025-07-28 09:33:55 DP1 TP0] Decode batch. #running-req: 1, #token: 939, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:33:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1205, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.13, #queue-req: 0, 
[2025-07-28 09:33:56 DP1 TP0] Decode batch. #running-req: 1, #token: 979, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:33:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1245, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.12, #queue-req: 0, 
[2025-07-28 09:33:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1019, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:33:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1285, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.11, #queue-req: 0, 
[2025-07-28 09:33:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1059, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.17, #queue-req: 0, 
[2025-07-28 09:33:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1325, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.10, #queue-req: 0, 
[2025-07-28 09:33:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1099, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.15, #queue-req: 0, 
[2025-07-28 09:33:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1365, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:33:58 DP1 TP0] Decode batch. #running-req: 1, #token: 1139, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.13, #queue-req: 0, 
[2025-07-28 09:33:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1405, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.11, #queue-req: 0, 
[2025-07-28 09:33:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1179, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 09:33:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1445, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.05, #queue-req: 0, 
[2025-07-28 09:33:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1219, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.17, #queue-req: 0, 
[2025-07-28 09:34:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1485, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.05, #queue-req: 0, 
[2025-07-28 09:34:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1259, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 09:34:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1525, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.01, #queue-req: 0, 
[2025-07-28 09:34:00] INFO:     127.0.0.1:34840 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:34:00 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 133, #cached-token: 86, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 09:34:01 DP0 TP0] Decode batch. #running-req: 2, #token: 1738, token usage: 0.00, cuda graph: True, gen throughput (token/s): 120.96, #queue-req: 0, 
[2025-07-28 09:34:02 DP0 TP0] Decode batch. #running-req: 2, #token: 1818, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.84, #queue-req: 0, 
[2025-07-28 09:34:02 DP0 TP0] Decode batch. #running-req: 2, #token: 1898, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.86, #queue-req: 0, 
[2025-07-28 09:34:02] INFO:     127.0.0.1:34838 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:34:02 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 128, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:34:03 DP1 TP0] Decode batch. #running-req: 1, #token: 220, token usage: 0.00, cuda graph: True, gen throughput (token/s): 14.49, #queue-req: 0, 
[2025-07-28 09:34:03 DP0 TP0] Decode batch. #running-req: 1, #token: 377, token usage: 0.00, cuda graph: True, gen throughput (token/s): 104.68, #queue-req: 0, 
[2025-07-28 09:34:03 DP1 TP0] Decode batch. #running-req: 1, #token: 260, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.96, #queue-req: 0, 
[2025-07-28 09:34:03 DP0 TP0] Decode batch. #running-req: 1, #token: 417, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:34:04 DP1 TP0] Decode batch. #running-req: 1, #token: 300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.77, #queue-req: 0, 
[2025-07-28 09:34:04 DP0 TP0] Decode batch. #running-req: 1, #token: 457, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:34:04 DP1 TP0] Decode batch. #running-req: 1, #token: 340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.73, #queue-req: 0, 
[2025-07-28 09:34:04 DP0 TP0] Decode batch. #running-req: 1, #token: 497, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:34:05 DP1 TP0] Decode batch. #running-req: 1, #token: 380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.54, #queue-req: 0, 
[2025-07-28 09:34:05 DP0 TP0] Decode batch. #running-req: 1, #token: 537, token usage: 0.00, cuda graph: True, gen throughput (token/s): 67.85, #queue-req: 0, 
[2025-07-28 09:34:05 DP1 TP0] Decode batch. #running-req: 1, #token: 420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 09:34:06 DP0 TP0] Decode batch. #running-req: 1, #token: 577, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.15, #queue-req: 0, 
[2025-07-28 09:34:06 DP1 TP0] Decode batch. #running-req: 1, #token: 460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 09:34:06 DP0 TP0] Decode batch. #running-req: 1, #token: 617, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:34:07 DP1 TP0] Decode batch. #running-req: 1, #token: 500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 09:34:07 DP0 TP0] Decode batch. #running-req: 1, #token: 657, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:34:07 DP1 TP0] Decode batch. #running-req: 1, #token: 540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:34:07 DP0 TP0] Decode batch. #running-req: 1, #token: 697, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:34:08 DP1 TP0] Decode batch. #running-req: 1, #token: 580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 09:34:08 DP0 TP0] Decode batch. #running-req: 1, #token: 737, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:34:08 DP1 TP0] Decode batch. #running-req: 1, #token: 620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:34:08 DP0 TP0] Decode batch. #running-req: 1, #token: 777, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:34:09 DP1 TP0] Decode batch. #running-req: 1, #token: 660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 09:34:09 DP0 TP0] Decode batch. #running-req: 1, #token: 817, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:34:09 DP1 TP0] Decode batch. #running-req: 1, #token: 700, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:34:10 DP0 TP0] Decode batch. #running-req: 1, #token: 857, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:34:10 DP1 TP0] Decode batch. #running-req: 1, #token: 740, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 09:34:10 DP0 TP0] Decode batch. #running-req: 1, #token: 897, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:34:11 DP1 TP0] Decode batch. #running-req: 1, #token: 780, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:34:11 DP0 TP0] Decode batch. #running-req: 1, #token: 937, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:34:11 DP1 TP0] Decode batch. #running-req: 1, #token: 820, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:34:11 DP0 TP0] Decode batch. #running-req: 1, #token: 977, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:34:12 DP1 TP0] Decode batch. #running-req: 1, #token: 860, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:34:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1017, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:34:12 DP1 TP0] Decode batch. #running-req: 1, #token: 900, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:34:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1057, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.12, #queue-req: 0, 
[2025-07-28 09:34:13 DP1 TP0] Decode batch. #running-req: 1, #token: 940, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:34:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1097, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.10, #queue-req: 0, 
[2025-07-28 09:34:14 DP1 TP0] Decode batch. #running-req: 1, #token: 980, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:34:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1137, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.07, #queue-req: 0, 
[2025-07-28 09:34:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1020, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:34:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1177, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.11, #queue-req: 0, 
[2025-07-28 09:34:15 DP1 TP0] Decode batch. #running-req: 1, #token: 1060, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.17, #queue-req: 0, 
[2025-07-28 09:34:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1217, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.12, #queue-req: 0, 
[2025-07-28 09:34:15] INFO:     127.0.0.1:40844 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:34:15 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 195, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 09:34:15 DP0 TP0] Decode batch. #running-req: 2, #token: 1469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 83.99, #queue-req: 0, 
[2025-07-28 09:34:16 DP0 TP0] Decode batch. #running-req: 2, #token: 1549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.94, #queue-req: 0, 
[2025-07-28 09:34:17 DP0 TP0] Decode batch. #running-req: 2, #token: 1629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.93, #queue-req: 0, 
[2025-07-28 09:34:17 DP0 TP0] Decode batch. #running-req: 2, #token: 1709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.92, #queue-req: 0, 
[2025-07-28 09:34:18 DP0 TP0] Decode batch. #running-req: 2, #token: 1789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.93, #queue-req: 0, 
[2025-07-28 09:34:18 DP0 TP0] Decode batch. #running-req: 2, #token: 1869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.91, #queue-req: 0, 
[2025-07-28 09:34:19] INFO:     127.0.0.1:41102 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:34:19 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 190, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:34:19 DP1 TP0] Decode batch. #running-req: 1, #token: 279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 9.58, #queue-req: 0, 
[2025-07-28 09:34:19 DP0 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.15, #queue-req: 0, 
[2025-07-28 09:34:19 DP1 TP0] Decode batch. #running-req: 1, #token: 319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.06, #queue-req: 0, 
[2025-07-28 09:34:20 DP0 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:34:20 DP1 TP0] Decode batch. #running-req: 1, #token: 359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.48, #queue-req: 0, 
[2025-07-28 09:34:20 DP0 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:34:21 DP1 TP0] Decode batch. #running-req: 1, #token: 399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 09:34:21 DP0 TP0] Decode batch. #running-req: 1, #token: 656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:34:21 DP1 TP0] Decode batch. #running-req: 1, #token: 439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:34:21 DP0 TP0] Decode batch. #running-req: 1, #token: 696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:34:22 DP1 TP0] Decode batch. #running-req: 1, #token: 479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 09:34:22 DP0 TP0] Decode batch. #running-req: 1, #token: 736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:34:22 DP1 TP0] Decode batch. #running-req: 1, #token: 519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:34:22 DP0 TP0] Decode batch. #running-req: 1, #token: 776, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:34:23 DP1 TP0] Decode batch. #running-req: 1, #token: 559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 09:34:23 DP0 TP0] Decode batch. #running-req: 1, #token: 816, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:34:23 DP1 TP0] Decode batch. #running-req: 1, #token: 599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 09:34:24 DP0 TP0] Decode batch. #running-req: 1, #token: 856, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:34:24 DP1 TP0] Decode batch. #running-req: 1, #token: 639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:34:24 DP0 TP0] Decode batch. #running-req: 1, #token: 896, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:34:25 DP1 TP0] Decode batch. #running-req: 1, #token: 679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 09:34:25 DP0 TP0] Decode batch. #running-req: 1, #token: 936, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:34:25 DP1 TP0] Decode batch. #running-req: 1, #token: 719, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 09:34:25 DP0 TP0] Decode batch. #running-req: 1, #token: 976, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:34:26 DP1 TP0] Decode batch. #running-req: 1, #token: 759, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 09:34:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1016, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:34:26 DP1 TP0] Decode batch. #running-req: 1, #token: 799, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:34:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1056, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.11, #queue-req: 0, 
[2025-07-28 09:34:27 DP1 TP0] Decode batch. #running-req: 1, #token: 839, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:34:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1096, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.08, #queue-req: 0, 
[2025-07-28 09:34:28 DP1 TP0] Decode batch. #running-req: 1, #token: 879, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:34:28] INFO:     127.0.0.1:41728 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:34:28 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 163, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:34:28 DP0 TP0] Decode batch. #running-req: 1, #token: 255, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.17, #queue-req: 0, 
[2025-07-28 09:34:28 DP1 TP0] Decode batch. #running-req: 1, #token: 919, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:34:28] INFO:     127.0.0.1:41742 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:34:28 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 168, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:34:28 DP0 TP0] Decode batch. #running-req: 1, #token: 295, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.73, #queue-req: 0, 
[2025-07-28 09:34:29 DP1 TP0] Decode batch. #running-req: 1, #token: 293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 64.06, #queue-req: 0, 
[2025-07-28 09:34:29 DP0 TP0] Decode batch. #running-req: 1, #token: 335, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.69, #queue-req: 0, 
[2025-07-28 09:34:29 DP1 TP0] Decode batch. #running-req: 1, #token: 333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.72, #queue-req: 0, 
[2025-07-28 09:34:29 DP0 TP0] Decode batch. #running-req: 1, #token: 375, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.53, #queue-req: 0, 
[2025-07-28 09:34:30 DP1 TP0] Decode batch. #running-req: 1, #token: 373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.59, #queue-req: 0, 
[2025-07-28 09:34:30 DP0 TP0] Decode batch. #running-req: 1, #token: 415, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 09:34:30 DP1 TP0] Decode batch. #running-req: 1, #token: 413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.39, #queue-req: 0, 
[2025-07-28 09:34:31 DP0 TP0] Decode batch. #running-req: 1, #token: 455, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:34:31 DP1 TP0] Decode batch. #running-req: 1, #token: 453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 09:34:31 DP0 TP0] Decode batch. #running-req: 1, #token: 495, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:34:32 DP1 TP0] Decode batch. #running-req: 1, #token: 493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 09:34:32 DP0 TP0] Decode batch. #running-req: 1, #token: 535, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:34:32 DP1 TP0] Decode batch. #running-req: 1, #token: 533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 09:34:32 DP0 TP0] Decode batch. #running-req: 1, #token: 575, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:34:33 DP1 TP0] Decode batch. #running-req: 1, #token: 573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:34:33 DP0 TP0] Decode batch. #running-req: 1, #token: 615, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:34:33 DP1 TP0] Decode batch. #running-req: 1, #token: 613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 09:34:33 DP0 TP0] Decode batch. #running-req: 1, #token: 655, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 09:34:34 DP1 TP0] Decode batch. #running-req: 1, #token: 653, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 09:34:34 DP0 TP0] Decode batch. #running-req: 1, #token: 695, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:34:34 DP1 TP0] Decode batch. #running-req: 1, #token: 693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:34:35 DP0 TP0] Decode batch. #running-req: 1, #token: 735, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:34:35 DP1 TP0] Decode batch. #running-req: 1, #token: 733, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 09:34:35 DP0 TP0] Decode batch. #running-req: 1, #token: 775, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:34:36 DP1 TP0] Decode batch. #running-req: 1, #token: 773, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 09:34:36 DP0 TP0] Decode batch. #running-req: 1, #token: 815, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:34:36 DP1 TP0] Decode batch. #running-req: 1, #token: 813, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:34:36 DP0 TP0] Decode batch. #running-req: 1, #token: 855, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:34:37 DP1 TP0] Decode batch. #running-req: 1, #token: 853, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:34:37 DP0 TP0] Decode batch. #running-req: 1, #token: 895, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:34:37 DP1 TP0] Decode batch. #running-req: 1, #token: 893, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:34:38 DP0 TP0] Decode batch. #running-req: 1, #token: 935, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:34:38 DP1 TP0] Decode batch. #running-req: 1, #token: 933, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:34:38 DP0 TP0] Decode batch. #running-req: 1, #token: 975, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:34:39 DP1 TP0] Decode batch. #running-req: 1, #token: 973, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:34:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1015, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:34:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1013, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:34:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1055, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:34:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1053, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 09:34:40 DP0 TP0] Decode batch. #running-req: 1, #token: 1095, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.07, #queue-req: 0, 
[2025-07-28 09:34:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1093, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.17, #queue-req: 0, 
[2025-07-28 09:34:40 DP0 TP0] Decode batch. #running-req: 1, #token: 1135, token usage: 0.00, cuda graph: True, gen throughput (token/s): 67.63, #queue-req: 0, 
[2025-07-28 09:34:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1133, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.15, #queue-req: 0, 
[2025-07-28 09:34:41 DP0 TP0] Decode batch. #running-req: 1, #token: 1175, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.10, #queue-req: 0, 
[2025-07-28 09:34:41] INFO:     127.0.0.1:33058 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:34:41 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 215, #cached-token: 86, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 09:34:42 DP0 TP0] Decode batch. #running-req: 2, #token: 1450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 93.33, #queue-req: 0, 
[2025-07-28 09:34:42 DP0 TP0] Decode batch. #running-req: 2, #token: 1530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.04, #queue-req: 0, 
[2025-07-28 09:34:43 DP0 TP0] Decode batch. #running-req: 2, #token: 1610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.97, #queue-req: 0, 
[2025-07-28 09:34:43 DP0 TP0] Decode batch. #running-req: 2, #token: 1690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.98, #queue-req: 0, 
[2025-07-28 09:34:44 DP0 TP0] Decode batch. #running-req: 2, #token: 1770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.99, #queue-req: 0, 
[2025-07-28 09:34:45 DP0 TP0] Decode batch. #running-req: 2, #token: 1850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.91, #queue-req: 0, 
[2025-07-28 09:34:45] INFO:     127.0.0.1:33054 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:34:45 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 151, #cached-token: 89, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:34:45 DP0 TP0] Decode batch. #running-req: 1, #token: 561, token usage: 0.00, cuda graph: True, gen throughput (token/s): 124.35, #queue-req: 0, 
[2025-07-28 09:34:45 DP1 TP0] Decode batch. #running-req: 1, #token: 248, token usage: 0.00, cuda graph: True, gen throughput (token/s): 8.94, #queue-req: 0, 
[2025-07-28 09:34:46 DP0 TP0] Decode batch. #running-req: 1, #token: 601, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:34:46 DP1 TP0] Decode batch. #running-req: 1, #token: 288, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 09:34:46 DP0 TP0] Decode batch. #running-req: 1, #token: 641, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:34:46 DP1 TP0] Decode batch. #running-req: 1, #token: 328, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.73, #queue-req: 0, 
[2025-07-28 09:34:47 DP0 TP0] Decode batch. #running-req: 1, #token: 681, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:34:47 DP1 TP0] Decode batch. #running-req: 1, #token: 368, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.59, #queue-req: 0, 
[2025-07-28 09:34:47 DP0 TP0] Decode batch. #running-req: 1, #token: 721, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:34:48 DP1 TP0] Decode batch. #running-req: 1, #token: 408, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.42, #queue-req: 0, 
[2025-07-28 09:34:48 DP0 TP0] Decode batch. #running-req: 1, #token: 761, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:34:48 DP1 TP0] Decode batch. #running-req: 1, #token: 448, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 09:34:49 DP0 TP0] Decode batch. #running-req: 1, #token: 801, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 09:34:49 DP1 TP0] Decode batch. #running-req: 1, #token: 488, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 09:34:49 DP0 TP0] Decode batch. #running-req: 1, #token: 841, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:34:49 DP1 TP0] Decode batch. #running-req: 1, #token: 528, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 09:34:50 DP0 TP0] Decode batch. #running-req: 1, #token: 881, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:34:50 DP1 TP0] Decode batch. #running-req: 1, #token: 568, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 09:34:50 DP0 TP0] Decode batch. #running-req: 1, #token: 921, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:34:50 DP1 TP0] Decode batch. #running-req: 1, #token: 608, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:34:51 DP0 TP0] Decode batch. #running-req: 1, #token: 961, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:34:51 DP1 TP0] Decode batch. #running-req: 1, #token: 648, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 09:34:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1001, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:34:52 DP1 TP0] Decode batch. #running-req: 1, #token: 688, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:34:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1041, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.18, #queue-req: 0, 
[2025-07-28 09:34:52 DP1 TP0] Decode batch. #running-req: 1, #token: 728, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 09:34:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1081, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.10, #queue-req: 0, 
[2025-07-28 09:34:53 DP1 TP0] Decode batch. #running-req: 1, #token: 768, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:34:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1121, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.09, #queue-req: 0, 
[2025-07-28 09:34:53 DP1 TP0] Decode batch. #running-req: 1, #token: 808, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 09:34:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1161, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.08, #queue-req: 0, 
[2025-07-28 09:34:54 DP1 TP0] Decode batch. #running-req: 1, #token: 848, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:34:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1201, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.09, #queue-req: 0, 
[2025-07-28 09:34:55 DP1 TP0] Decode batch. #running-req: 1, #token: 888, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:34:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1241, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.13, #queue-req: 0, 
[2025-07-28 09:34:55 DP1 TP0] Decode batch. #running-req: 1, #token: 928, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:34:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1281, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.12, #queue-req: 0, 
[2025-07-28 09:34:56 DP1 TP0] Decode batch. #running-req: 1, #token: 968, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:34:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1321, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.10, #queue-req: 0, 
[2025-07-28 09:34:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1008, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:34:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1361, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.09, #queue-req: 0, 
[2025-07-28 09:34:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1048, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:34:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1401, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.08, #queue-req: 0, 
[2025-07-28 09:34:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1088, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 09:34:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1441, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.08, #queue-req: 0, 
[2025-07-28 09:34:58 DP1 TP0] Decode batch. #running-req: 1, #token: 1128, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 09:34:58] INFO:     127.0.0.1:35234 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:34:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.90, #queue-req: 0, 
[2025-07-28 09:34:58] INFO:     127.0.0.1:40994 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:34:58 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 171, #cached-token: 88, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 09:34:59 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 90, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:34:59 DP1 TP0] Decode batch. #running-req: 1, #token: 181, token usage: 0.00, cuda graph: True, gen throughput (token/s): 61.08, #queue-req: 0, 
[2025-07-28 09:34:59 DP0 TP0] Decode batch. #running-req: 1, #token: 299, token usage: 0.00, cuda graph: True, gen throughput (token/s): 64.11, #queue-req: 0, 
[2025-07-28 09:34:59 DP1 TP0] Decode batch. #running-req: 1, #token: 221, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.33, #queue-req: 0, 
[2025-07-28 09:35:00 DP0 TP0] Decode batch. #running-req: 1, #token: 339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.52, #queue-req: 0, 
[2025-07-28 09:35:00 DP1 TP0] Decode batch. #running-req: 1, #token: 261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.24, #queue-req: 0, 
[2025-07-28 09:35:00 DP0 TP0] Decode batch. #running-req: 1, #token: 379, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.36, #queue-req: 0, 
[2025-07-28 09:35:00 DP1 TP0] Decode batch. #running-req: 1, #token: 301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.71, #queue-req: 0, 
[2025-07-28 09:35:01 DP0 TP0] Decode batch. #running-req: 1, #token: 419, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:35:01 DP1 TP0] Decode batch. #running-req: 1, #token: 341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.69, #queue-req: 0, 
[2025-07-28 09:35:01 DP0 TP0] Decode batch. #running-req: 1, #token: 459, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 09:35:02 DP1 TP0] Decode batch. #running-req: 1, #token: 381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.53, #queue-req: 0, 
[2025-07-28 09:35:02 DP0 TP0] Decode batch. #running-req: 1, #token: 499, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:35:02 DP1 TP0] Decode batch. #running-req: 1, #token: 421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 09:35:03 DP0 TP0] Decode batch. #running-req: 1, #token: 539, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:35:03 DP1 TP0] Decode batch. #running-req: 1, #token: 461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 09:35:03 DP0 TP0] Decode batch. #running-req: 1, #token: 579, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:35:03 DP1 TP0] Decode batch. #running-req: 1, #token: 501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:35:04 DP0 TP0] Decode batch. #running-req: 1, #token: 619, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 09:35:04 DP1 TP0] Decode batch. #running-req: 1, #token: 541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:35:04 DP0 TP0] Decode batch. #running-req: 1, #token: 659, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:35:04 DP1 TP0] Decode batch. #running-req: 1, #token: 581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:35:05 DP0 TP0] Decode batch. #running-req: 1, #token: 699, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:35:05 DP1 TP0] Decode batch. #running-req: 1, #token: 621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:35:05 DP0 TP0] Decode batch. #running-req: 1, #token: 739, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:35:06 DP1 TP0] Decode batch. #running-req: 1, #token: 661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:35:06 DP0 TP0] Decode batch. #running-req: 1, #token: 779, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.17, #queue-req: 0, 
[2025-07-28 09:35:06 DP1 TP0] Decode batch. #running-req: 1, #token: 701, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:35:07 DP0 TP0] Decode batch. #running-req: 1, #token: 819, token usage: 0.00, cuda graph: True, gen throughput (token/s): 67.74, #queue-req: 0, 
[2025-07-28 09:35:07 DP1 TP0] Decode batch. #running-req: 1, #token: 741, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:35:07 DP0 TP0] Decode batch. #running-req: 1, #token: 859, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:35:07 DP1 TP0] Decode batch. #running-req: 1, #token: 781, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:35:08 DP0 TP0] Decode batch. #running-req: 1, #token: 899, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:35:08 DP1 TP0] Decode batch. #running-req: 1, #token: 821, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:35:08 DP0 TP0] Decode batch. #running-req: 1, #token: 939, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:35:08 DP1 TP0] Decode batch. #running-req: 1, #token: 861, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:35:09] INFO:     127.0.0.1:57166 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:35:09 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 111, #cached-token: 84, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 09:35:09 DP0 TP0] Decode batch. #running-req: 2, #token: 1103, token usage: 0.00, cuda graph: True, gen throughput (token/s): 82.63, #queue-req: 0, 
[2025-07-28 09:35:10 DP0 TP0] Decode batch. #running-req: 2, #token: 1183, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.20, #queue-req: 0, 
[2025-07-28 09:35:10 DP0 TP0] Decode batch. #running-req: 2, #token: 1263, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.97, #queue-req: 0, 
[2025-07-28 09:35:11 DP0 TP0] Decode batch. #running-req: 2, #token: 1343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.09, #queue-req: 0, 
[2025-07-28 09:35:11 DP0 TP0] Decode batch. #running-req: 2, #token: 1423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.99, #queue-req: 0, 
[2025-07-28 09:35:12 DP0 TP0] Decode batch. #running-req: 2, #token: 1503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.00, #queue-req: 0, 
[2025-07-28 09:35:13 DP0 TP0] Decode batch. #running-req: 2, #token: 1583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.90, #queue-req: 0, 
[2025-07-28 09:35:13 DP0 TP0] Decode batch. #running-req: 2, #token: 1663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.92, #queue-req: 0, 
[2025-07-28 09:35:14 DP0 TP0] Decode batch. #running-req: 2, #token: 1743, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.93, #queue-req: 0, 
[2025-07-28 09:35:14 DP0 TP0] Decode batch. #running-req: 2, #token: 1823, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.86, #queue-req: 0, 
[2025-07-28 09:35:15 DP0 TP0] Decode batch. #running-req: 2, #token: 1903, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.88, #queue-req: 0, 
[2025-07-28 09:35:15 DP0 TP0] Decode batch. #running-req: 2, #token: 1983, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.82, #queue-req: 0, 
[2025-07-28 09:35:16 DP0 TP0] Decode batch. #running-req: 2, #token: 2063, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.70, #queue-req: 0, 
[2025-07-28 09:35:17 DP0 TP0] Decode batch. #running-req: 2, #token: 2143, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.83, #queue-req: 0, 
[2025-07-28 09:35:17 DP0 TP0] Decode batch. #running-req: 2, #token: 2223, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.79, #queue-req: 0, 
[2025-07-28 09:35:18] INFO:     127.0.0.1:57158 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:35:18 DP0 TP0] Decode batch. #running-req: 1, #token: 808, token usage: 0.00, cuda graph: True, gen throughput (token/s): 134.09, #queue-req: 0, 
[2025-07-28 09:35:18 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 146, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:35:18 DP1 TP0] Decode batch. #running-req: 1, #token: 252, token usage: 0.00, cuda graph: True, gen throughput (token/s): 4.09, #queue-req: 0, 
[2025-07-28 09:35:18 DP0 TP0] Decode batch. #running-req: 1, #token: 848, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:35:19 DP1 TP0] Decode batch. #running-req: 1, #token: 292, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.78, #queue-req: 0, 
[2025-07-28 09:35:19 DP0 TP0] Decode batch. #running-req: 1, #token: 888, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:35:19 DP1 TP0] Decode batch. #running-req: 1, #token: 332, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.68, #queue-req: 0, 
[2025-07-28 09:35:20 DP0 TP0] Decode batch. #running-req: 1, #token: 928, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:35:20 DP1 TP0] Decode batch. #running-req: 1, #token: 372, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.51, #queue-req: 0, 
[2025-07-28 09:35:20] INFO:     127.0.0.1:42116 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:35:20 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:35:20 DP0 TP0] Decode batch. #running-req: 1, #token: 240, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.66, #queue-req: 0, 
[2025-07-28 09:35:21 DP1 TP0] Decode batch. #running-req: 1, #token: 412, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.38, #queue-req: 0, 
[2025-07-28 09:35:21 DP0 TP0] Decode batch. #running-req: 1, #token: 280, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.92, #queue-req: 0, 
[2025-07-28 09:35:21 DP1 TP0] Decode batch. #running-req: 1, #token: 452, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:35:21 DP0 TP0] Decode batch. #running-req: 1, #token: 320, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.70, #queue-req: 0, 
[2025-07-28 09:35:22 DP1 TP0] Decode batch. #running-req: 1, #token: 492, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 09:35:22 DP0 TP0] Decode batch. #running-req: 1, #token: 360, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.55, #queue-req: 0, 
[2025-07-28 09:35:22 DP1 TP0] Decode batch. #running-req: 1, #token: 532, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:35:22 DP0 TP0] Decode batch. #running-req: 1, #token: 400, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.42, #queue-req: 0, 
[2025-07-28 09:35:23 DP1 TP0] Decode batch. #running-req: 1, #token: 572, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:35:23 DP0 TP0] Decode batch. #running-req: 1, #token: 440, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:35:23 DP1 TP0] Decode batch. #running-req: 1, #token: 612, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:35:24 DP0 TP0] Decode batch. #running-req: 1, #token: 480, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:35:24 DP1 TP0] Decode batch. #running-req: 1, #token: 652, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:35:24 DP0 TP0] Decode batch. #running-req: 1, #token: 520, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:35:25 DP1 TP0] Decode batch. #running-req: 1, #token: 692, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:35:25 DP0 TP0] Decode batch. #running-req: 1, #token: 560, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:35:25 DP1 TP0] Decode batch. #running-req: 1, #token: 732, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:35:25 DP0 TP0] Decode batch. #running-req: 1, #token: 600, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:35:26 DP1 TP0] Decode batch. #running-req: 1, #token: 772, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:35:26 DP0 TP0] Decode batch. #running-req: 1, #token: 640, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:35:26 DP1 TP0] Decode batch. #running-req: 1, #token: 812, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:35:27 DP0 TP0] Decode batch. #running-req: 1, #token: 680, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:35:27 DP1 TP0] Decode batch. #running-req: 1, #token: 852, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:35:27 DP0 TP0] Decode batch. #running-req: 1, #token: 720, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:35:27 DP1 TP0] Decode batch. #running-req: 1, #token: 892, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:35:28 DP0 TP0] Decode batch. #running-req: 1, #token: 760, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:35:28 DP1 TP0] Decode batch. #running-req: 1, #token: 932, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:35:28 DP0 TP0] Decode batch. #running-req: 1, #token: 800, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:35:29 DP1 TP0] Decode batch. #running-req: 1, #token: 972, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:35:29 DP0 TP0] Decode batch. #running-req: 1, #token: 840, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:35:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1012, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:35:29 DP0 TP0] Decode batch. #running-req: 1, #token: 880, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:35:30 DP1 TP0] Decode batch. #running-req: 1, #token: 1052, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:35:30 DP0 TP0] Decode batch. #running-req: 1, #token: 920, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:35:30 DP1 TP0] Decode batch. #running-req: 1, #token: 1092, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:35:31 DP0 TP0] Decode batch. #running-req: 1, #token: 960, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:35:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1132, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.11, #queue-req: 0, 
[2025-07-28 09:35:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1000, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:35:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1172, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 09:35:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1040, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:35:32] INFO:     127.0.0.1:36356 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:35:32 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 156, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:35:32 DP1 TP0] Decode batch. #running-req: 1, #token: 250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.68, #queue-req: 0, 
[2025-07-28 09:35:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1080, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.09, #queue-req: 0, 
[2025-07-28 09:35:33 DP1 TP0] Decode batch. #running-req: 1, #token: 290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.84, #queue-req: 0, 
[2025-07-28 09:35:33 DP0 TP0] Decode batch. #running-req: 1, #token: 1120, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.11, #queue-req: 0, 
[2025-07-28 09:35:33 DP1 TP0] Decode batch. #running-req: 1, #token: 330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.70, #queue-req: 0, 
[2025-07-28 09:35:33 DP0 TP0] Decode batch. #running-req: 1, #token: 1160, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.03, #queue-req: 0, 
[2025-07-28 09:35:34 DP1 TP0] Decode batch. #running-req: 1, #token: 370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.55, #queue-req: 0, 
[2025-07-28 09:35:34 DP0 TP0] Decode batch. #running-req: 1, #token: 1200, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.12, #queue-req: 0, 
[2025-07-28 09:35:34 DP1 TP0] Decode batch. #running-req: 1, #token: 410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.39, #queue-req: 0, 
[2025-07-28 09:35:35 DP0 TP0] Decode batch. #running-req: 1, #token: 1240, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.15, #queue-req: 0, 
[2025-07-28 09:35:35 DP1 TP0] Decode batch. #running-req: 1, #token: 450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 09:35:35 DP0 TP0] Decode batch. #running-req: 1, #token: 1280, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.13, #queue-req: 0, 
[2025-07-28 09:35:36 DP1 TP0] Decode batch. #running-req: 1, #token: 490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:35:36 DP0 TP0] Decode batch. #running-req: 1, #token: 1320, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:35:36 DP1 TP0] Decode batch. #running-req: 1, #token: 530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:35:36 DP0 TP0] Decode batch. #running-req: 1, #token: 1360, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.12, #queue-req: 0, 
[2025-07-28 09:35:37 DP1 TP0] Decode batch. #running-req: 1, #token: 570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:35:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1400, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.11, #queue-req: 0, 
[2025-07-28 09:35:37 DP1 TP0] Decode batch. #running-req: 1, #token: 610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:35:38 DP0 TP0] Decode batch. #running-req: 1, #token: 1440, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.08, #queue-req: 0, 
[2025-07-28 09:35:38 DP1 TP0] Decode batch. #running-req: 1, #token: 650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:35:38 DP0 TP0] Decode batch. #running-req: 1, #token: 1480, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.08, #queue-req: 0, 
[2025-07-28 09:35:38 DP1 TP0] Decode batch. #running-req: 1, #token: 690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:35:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1520, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.05, #queue-req: 0, 
[2025-07-28 09:35:39 DP1 TP0] Decode batch. #running-req: 1, #token: 730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:35:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1560, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.06, #queue-req: 0, 
[2025-07-28 09:35:40 DP1 TP0] Decode batch. #running-req: 1, #token: 770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:35:40 DP0 TP0] Decode batch. #running-req: 1, #token: 1600, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.04, #queue-req: 0, 
[2025-07-28 09:35:40 DP1 TP0] Decode batch. #running-req: 1, #token: 810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:35:40 DP0 TP0] Decode batch. #running-req: 1, #token: 1640, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.04, #queue-req: 0, 
[2025-07-28 09:35:41 DP1 TP0] Decode batch. #running-req: 1, #token: 850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:35:41 DP0 TP0] Decode batch. #running-req: 1, #token: 1680, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.06, #queue-req: 0, 
[2025-07-28 09:35:41 DP1 TP0] Decode batch. #running-req: 1, #token: 890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:35:42 DP0 TP0] Decode batch. #running-req: 1, #token: 1720, token usage: 0.00, cuda graph: True, gen throughput (token/s): 67.72, #queue-req: 0, 
[2025-07-28 09:35:42 DP1 TP0] Decode batch. #running-req: 1, #token: 930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:35:42 DP0 TP0] Decode batch. #running-req: 1, #token: 1760, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.98, #queue-req: 0, 
[2025-07-28 09:35:42 DP1 TP0] Decode batch. #running-req: 1, #token: 970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:35:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1800, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.05, #queue-req: 0, 
[2025-07-28 09:35:43 DP1 TP0] Decode batch. #running-req: 1, #token: 1010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:35:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1840, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.06, #queue-req: 0, 
[2025-07-28 09:35:44 DP1 TP0] Decode batch. #running-req: 1, #token: 1050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.13, #queue-req: 0, 
[2025-07-28 09:35:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1880, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.05, #queue-req: 0, 
[2025-07-28 09:35:44] INFO:     127.0.0.1:36362 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:35:44 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 110, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:35:44 DP1 TP0] Decode batch. #running-req: 1, #token: 1090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:35:45 DP0 TP0] Decode batch. #running-req: 1, #token: 223, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.72, #queue-req: 0, 
[2025-07-28 09:35:45 DP1 TP0] Decode batch. #running-req: 1, #token: 1130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.13, #queue-req: 0, 
[2025-07-28 09:35:45 DP0 TP0] Decode batch. #running-req: 1, #token: 263, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.11, #queue-req: 0, 
[2025-07-28 09:35:45 DP1 TP0] Decode batch. #running-req: 1, #token: 1170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.10, #queue-req: 0, 
[2025-07-28 09:35:46 DP0 TP0] Decode batch. #running-req: 1, #token: 303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.61, #queue-req: 0, 
[2025-07-28 09:35:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.15, #queue-req: 0, 
[2025-07-28 09:35:46 DP0 TP0] Decode batch. #running-req: 1, #token: 343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.60, #queue-req: 0, 
[2025-07-28 09:35:47 DP1 TP0] Decode batch. #running-req: 1, #token: 1250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.12, #queue-req: 0, 
[2025-07-28 09:35:47 DP0 TP0] Decode batch. #running-req: 1, #token: 383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.51, #queue-req: 0, 
[2025-07-28 09:35:47 DP1 TP0] Decode batch. #running-req: 1, #token: 1290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.15, #queue-req: 0, 
[2025-07-28 09:35:47 DP0 TP0] Decode batch. #running-req: 1, #token: 423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:35:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.11, #queue-req: 0, 
[2025-07-28 09:35:48 DP0 TP0] Decode batch. #running-req: 1, #token: 463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:35:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:35:49 DP0 TP0] Decode batch. #running-req: 1, #token: 503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:35:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.13, #queue-req: 0, 
[2025-07-28 09:35:49 DP0 TP0] Decode batch. #running-req: 1, #token: 543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:35:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.06, #queue-req: 0, 
[2025-07-28 09:35:50 DP0 TP0] Decode batch. #running-req: 1, #token: 583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:35:50 DP1 TP0] Decode batch. #running-req: 1, #token: 1490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.10, #queue-req: 0, 
[2025-07-28 09:35:50 DP0 TP0] Decode batch. #running-req: 1, #token: 623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:35:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.09, #queue-req: 0, 
[2025-07-28 09:35:51 DP0 TP0] Decode batch. #running-req: 1, #token: 663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:35:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.04, #queue-req: 0, 
[2025-07-28 09:35:51 DP0 TP0] Decode batch. #running-req: 1, #token: 703, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:35:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.04, #queue-req: 0, 
[2025-07-28 09:35:52 DP0 TP0] Decode batch. #running-req: 1, #token: 743, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:35:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.06, #queue-req: 0, 
[2025-07-28 09:35:53 DP0 TP0] Decode batch. #running-req: 1, #token: 783, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:35:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.06, #queue-req: 0, 
[2025-07-28 09:35:53 DP0 TP0] Decode batch. #running-req: 1, #token: 823, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:35:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.08, #queue-req: 0, 
[2025-07-28 09:35:54 DP0 TP0] Decode batch. #running-req: 1, #token: 863, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:35:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.08, #queue-req: 0, 
[2025-07-28 09:35:54 DP0 TP0] Decode batch. #running-req: 1, #token: 903, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:35:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.06, #queue-req: 0, 
[2025-07-28 09:35:55 DP0 TP0] Decode batch. #running-req: 1, #token: 943, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:35:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.03, #queue-req: 0, 
[2025-07-28 09:35:55 DP0 TP0] Decode batch. #running-req: 1, #token: 983, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:35:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.12, #queue-req: 0, 
[2025-07-28 09:35:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1023, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:35:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.08, #queue-req: 0, 
[2025-07-28 09:35:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1063, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.11, #queue-req: 0, 
[2025-07-28 09:35:57] INFO:     127.0.0.1:50356 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:35:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.05, #queue-req: 0, 
[2025-07-28 09:35:57 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 76, #cached-token: 84, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 09:35:58 DP1 TP0] Decode batch. #running-req: 2, #token: 2126, token usage: 0.00, cuda graph: True, gen throughput (token/s): 123.74, #queue-req: 0, 
[2025-07-28 09:35:58 DP1 TP0] Decode batch. #running-req: 2, #token: 2206, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.93, #queue-req: 0, 
[2025-07-28 09:35:59 DP1 TP0] Decode batch. #running-req: 2, #token: 2286, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.76, #queue-req: 0, 
[2025-07-28 09:35:59 DP1 TP0] Decode batch. #running-req: 2, #token: 2366, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.76, #queue-req: 0, 
[2025-07-28 09:36:00] INFO:     127.0.0.1:38564 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:36:00 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 132, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:36:00 DP1 TP0] Decode batch. #running-req: 1, #token: 360, token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.30, #queue-req: 0, 
[2025-07-28 09:36:00 DP0 TP0] Decode batch. #running-req: 1, #token: 235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 11.81, #queue-req: 0, 
[2025-07-28 09:36:01 DP1 TP0] Decode batch. #running-req: 1, #token: 400, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.42, #queue-req: 0, 
[2025-07-28 09:36:01 DP0 TP0] Decode batch. #running-req: 1, #token: 275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.02, #queue-req: 0, 
[2025-07-28 09:36:01 DP1 TP0] Decode batch. #running-req: 1, #token: 440, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:36:01 DP0 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.69, #queue-req: 0, 
[2025-07-28 09:36:02 DP1 TP0] Decode batch. #running-req: 1, #token: 480, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:36:02 DP0 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.62, #queue-req: 0, 
[2025-07-28 09:36:02 DP1 TP0] Decode batch. #running-req: 1, #token: 520, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:36:02 DP0 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.43, #queue-req: 0, 
[2025-07-28 09:36:03 DP1 TP0] Decode batch. #running-req: 1, #token: 560, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:36:03 DP0 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:36:03 DP1 TP0] Decode batch. #running-req: 1, #token: 600, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:36:03 DP0 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:36:04 DP1 TP0] Decode batch. #running-req: 1, #token: 640, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:36:04 DP0 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:36:05 DP1 TP0] Decode batch. #running-req: 1, #token: 680, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:36:05 DP0 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:36:05 DP1 TP0] Decode batch. #running-req: 1, #token: 720, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:36:05 DP0 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:36:06 DP1 TP0] Decode batch. #running-req: 1, #token: 760, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:36:06 DP0 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 09:36:06 DP1 TP0] Decode batch. #running-req: 1, #token: 800, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:36:06 DP0 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:36:07 DP1 TP0] Decode batch. #running-req: 1, #token: 840, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:36:07 DP0 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:36:07 DP1 TP0] Decode batch. #running-req: 1, #token: 880, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:36:08 DP0 TP0] Decode batch. #running-req: 1, #token: 755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 67.84, #queue-req: 0, 
[2025-07-28 09:36:08 DP1 TP0] Decode batch. #running-req: 1, #token: 920, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:36:08 DP0 TP0] Decode batch. #running-req: 1, #token: 795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.09, #queue-req: 0, 
[2025-07-28 09:36:09 DP1 TP0] Decode batch. #running-req: 1, #token: 960, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:36:09 DP0 TP0] Decode batch. #running-req: 1, #token: 835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:36:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1000, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:36:09 DP0 TP0] Decode batch. #running-req: 1, #token: 875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:36:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1040, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:36:10 DP0 TP0] Decode batch. #running-req: 1, #token: 915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:36:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1080, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:36:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.15, #queue-req: 0, 
[2025-07-28 09:36:10] INFO:     127.0.0.1:51912 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:36:10 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 133, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 09:36:11 DP1 TP0] Decode batch. #running-req: 2, #token: 1289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 113.84, #queue-req: 0, 
[2025-07-28 09:36:12 DP1 TP0] Decode batch. #running-req: 2, #token: 1369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.99, #queue-req: 0, 
[2025-07-28 09:36:12 DP1 TP0] Decode batch. #running-req: 2, #token: 1449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.98, #queue-req: 0, 
[2025-07-28 09:36:13] INFO:     127.0.0.1:51910 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:36:13 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 81, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:36:13 DP1 TP0] Decode batch. #running-req: 1, #token: 373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 126.15, #queue-req: 0, 
[2025-07-28 09:36:13 DP1 TP0] Decode batch. #running-req: 1, #token: 413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.38, #queue-req: 0, 
[2025-07-28 09:36:13 DP0 TP0] Decode batch. #running-req: 1, #token: 205, token usage: 0.00, cuda graph: True, gen throughput (token/s): 13.81, #queue-req: 0, 
[2025-07-28 09:36:14 DP0 TP0] Decode batch. #running-req: 1, #token: 245, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.26, #queue-req: 0, 
[2025-07-28 09:36:14 DP1 TP0] Decode batch. #running-req: 1, #token: 453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:36:14 DP0 TP0] Decode batch. #running-req: 1, #token: 285, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.88, #queue-req: 0, 
[2025-07-28 09:36:14 DP1 TP0] Decode batch. #running-req: 1, #token: 493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:36:15 DP0 TP0] Decode batch. #running-req: 1, #token: 325, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.68, #queue-req: 0, 
[2025-07-28 09:36:15 DP1 TP0] Decode batch. #running-req: 1, #token: 533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:36:16 DP0 TP0] Decode batch. #running-req: 1, #token: 365, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.53, #queue-req: 0, 
[2025-07-28 09:36:16 DP1 TP0] Decode batch. #running-req: 1, #token: 573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:36:16 DP0 TP0] Decode batch. #running-req: 1, #token: 405, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.42, #queue-req: 0, 
[2025-07-28 09:36:16 DP1 TP0] Decode batch. #running-req: 1, #token: 613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:36:17 DP0 TP0] Decode batch. #running-req: 1, #token: 445, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:36:17 DP1 TP0] Decode batch. #running-req: 1, #token: 653, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.15, #queue-req: 0, 
[2025-07-28 09:36:17 DP0 TP0] Decode batch. #running-req: 1, #token: 485, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:36:17 DP1 TP0] Decode batch. #running-req: 1, #token: 693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 09:36:18 DP0 TP0] Decode batch. #running-req: 1, #token: 525, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:36:18 DP1 TP0] Decode batch. #running-req: 1, #token: 733, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.17, #queue-req: 0, 
[2025-07-28 09:36:19 DP0 TP0] Decode batch. #running-req: 1, #token: 565, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:36:19 DP1 TP0] Decode batch. #running-req: 1, #token: 773, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:36:19 DP0 TP0] Decode batch. #running-req: 1, #token: 605, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:36:19 DP1 TP0] Decode batch. #running-req: 1, #token: 813, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.15, #queue-req: 0, 
[2025-07-28 09:36:20 DP0 TP0] Decode batch. #running-req: 1, #token: 645, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:36:20 DP1 TP0] Decode batch. #running-req: 1, #token: 853, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.17, #queue-req: 0, 
[2025-07-28 09:36:20] INFO:     127.0.0.1:49830 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:36:20 DP0 TP0] Decode batch. #running-req: 1, #token: 685, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:36:20 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 153, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:36:20 DP1 TP0] Decode batch. #running-req: 1, #token: 239, token usage: 0.00, cuda graph: True, gen throughput (token/s): 62.93, #queue-req: 0, 
[2025-07-28 09:36:21 DP0 TP0] Decode batch. #running-req: 1, #token: 725, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:36:21 DP1 TP0] Decode batch. #running-req: 1, #token: 279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.78, #queue-req: 0, 
[2025-07-28 09:36:21 DP0 TP0] Decode batch. #running-req: 1, #token: 765, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:36:21 DP1 TP0] Decode batch. #running-req: 1, #token: 319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.53, #queue-req: 0, 
[2025-07-28 09:36:22 DP0 TP0] Decode batch. #running-req: 1, #token: 805, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:36:22 DP1 TP0] Decode batch. #running-req: 1, #token: 359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.40, #queue-req: 0, 
[2025-07-28 09:36:23 DP0 TP0] Decode batch. #running-req: 1, #token: 845, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:36:23 DP1 TP0] Decode batch. #running-req: 1, #token: 399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:36:23 DP0 TP0] Decode batch. #running-req: 1, #token: 885, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:36:23 DP1 TP0] Decode batch. #running-req: 1, #token: 439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.04, #queue-req: 0, 
[2025-07-28 09:36:24 DP0 TP0] Decode batch. #running-req: 1, #token: 925, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:36:24 DP1 TP0] Decode batch. #running-req: 1, #token: 479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.10, #queue-req: 0, 
[2025-07-28 09:36:24 DP0 TP0] Decode batch. #running-req: 1, #token: 965, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:36:24 DP1 TP0] Decode batch. #running-req: 1, #token: 519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:36:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1005, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:36:25 DP1 TP0] Decode batch. #running-req: 1, #token: 559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:36:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1045, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.17, #queue-req: 0, 
[2025-07-28 09:36:26 DP1 TP0] Decode batch. #running-req: 1, #token: 599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:36:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1085, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.11, #queue-req: 0, 
[2025-07-28 09:36:26 DP1 TP0] Decode batch. #running-req: 1, #token: 639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.11, #queue-req: 0, 
[2025-07-28 09:36:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1125, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.10, #queue-req: 0, 
[2025-07-28 09:36:27 DP1 TP0] Decode batch. #running-req: 1, #token: 679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:36:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1165, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.09, #queue-req: 0, 
[2025-07-28 09:36:27 DP1 TP0] Decode batch. #running-req: 1, #token: 719, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:36:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1205, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.13, #queue-req: 0, 
[2025-07-28 09:36:28 DP1 TP0] Decode batch. #running-req: 1, #token: 759, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:36:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1245, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:36:28 DP1 TP0] Decode batch. #running-req: 1, #token: 799, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:36:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1285, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.11, #queue-req: 0, 
[2025-07-28 09:36:29 DP1 TP0] Decode batch. #running-req: 1, #token: 839, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:36:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1325, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.12, #queue-req: 0, 
[2025-07-28 09:36:30 DP1 TP0] Decode batch. #running-req: 1, #token: 879, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:36:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1365, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.08, #queue-req: 0, 
[2025-07-28 09:36:30 DP1 TP0] Decode batch. #running-req: 1, #token: 919, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:36:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1405, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.13, #queue-req: 0, 
[2025-07-28 09:36:31 DP1 TP0] Decode batch. #running-req: 1, #token: 959, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:36:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1445, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.06, #queue-req: 0, 
[2025-07-28 09:36:31 DP1 TP0] Decode batch. #running-req: 1, #token: 999, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:36:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1485, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.03, #queue-req: 0, 
[2025-07-28 09:36:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1039, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 09:36:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1525, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.05, #queue-req: 0, 
[2025-07-28 09:36:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1079, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.11, #queue-req: 0, 
[2025-07-28 09:36:33 DP0 TP0] Decode batch. #running-req: 1, #token: 1565, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.03, #queue-req: 0, 
[2025-07-28 09:36:33 DP1 TP0] Decode batch. #running-req: 1, #token: 1119, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.11, #queue-req: 0, 
[2025-07-28 09:36:34 DP0 TP0] Decode batch. #running-req: 1, #token: 1605, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.07, #queue-req: 0, 
[2025-07-28 09:36:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1159, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.09, #queue-req: 0, 
[2025-07-28 09:36:34] INFO:     127.0.0.1:37220 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:36:34 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 146, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:36:34 DP0 TP0] Decode batch. #running-req: 1, #token: 245, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.47, #queue-req: 0, 
[2025-07-28 09:36:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1199, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.15, #queue-req: 0, 
[2025-07-28 09:36:35 DP0 TP0] Decode batch. #running-req: 1, #token: 285, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.85, #queue-req: 0, 
[2025-07-28 09:36:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1239, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:36:35 DP0 TP0] Decode batch. #running-req: 1, #token: 325, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.69, #queue-req: 0, 
[2025-07-28 09:36:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1279, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.12, #queue-req: 0, 
[2025-07-28 09:36:36 DP0 TP0] Decode batch. #running-req: 1, #token: 365, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.54, #queue-req: 0, 
[2025-07-28 09:36:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.11, #queue-req: 0, 
[2025-07-28 09:36:36 DP0 TP0] Decode batch. #running-req: 1, #token: 405, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.37, #queue-req: 0, 
[2025-07-28 09:36:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.11, #queue-req: 0, 
[2025-07-28 09:36:37 DP0 TP0] Decode batch. #running-req: 1, #token: 445, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:36:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.12, #queue-req: 0, 
[2025-07-28 09:36:38 DP0 TP0] Decode batch. #running-req: 1, #token: 485, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:36:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.06, #queue-req: 0, 
[2025-07-28 09:36:38 DP0 TP0] Decode batch. #running-req: 1, #token: 525, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:36:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.08, #queue-req: 0, 
[2025-07-28 09:36:39 DP0 TP0] Decode batch. #running-req: 1, #token: 565, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:36:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.06, #queue-req: 0, 
[2025-07-28 09:36:39 DP0 TP0] Decode batch. #running-req: 1, #token: 605, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:36:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.04, #queue-req: 0, 
[2025-07-28 09:36:40 DP0 TP0] Decode batch. #running-req: 1, #token: 645, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:36:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.04, #queue-req: 0, 
[2025-07-28 09:36:41 DP0 TP0] Decode batch. #running-req: 1, #token: 685, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:36:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.04, #queue-req: 0, 
[2025-07-28 09:36:41 DP0 TP0] Decode batch. #running-req: 1, #token: 725, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:36:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.94, #queue-req: 0, 
[2025-07-28 09:36:42 DP0 TP0] Decode batch. #running-req: 1, #token: 765, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:36:42 DP1 TP0] Decode batch. #running-req: 1, #token: 1719, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.08, #queue-req: 0, 
[2025-07-28 09:36:42 DP0 TP0] Decode batch. #running-req: 1, #token: 805, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:36:42 DP1 TP0] Decode batch. #running-req: 1, #token: 1759, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.08, #queue-req: 0, 
[2025-07-28 09:36:43 DP0 TP0] Decode batch. #running-req: 1, #token: 845, token usage: 0.00, cuda graph: True, gen throughput (token/s): 67.83, #queue-req: 0, 
[2025-07-28 09:36:43 DP1 TP0] Decode batch. #running-req: 1, #token: 1799, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.07, #queue-req: 0, 
[2025-07-28 09:36:43 DP0 TP0] Decode batch. #running-req: 1, #token: 885, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.12, #queue-req: 0, 
[2025-07-28 09:36:43 DP1 TP0] Decode batch. #running-req: 1, #token: 1839, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.06, #queue-req: 0, 
[2025-07-28 09:36:44 DP0 TP0] Decode batch. #running-req: 1, #token: 925, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:36:44 DP1 TP0] Decode batch. #running-req: 1, #token: 1879, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.98, #queue-req: 0, 
[2025-07-28 09:36:45 DP0 TP0] Decode batch. #running-req: 1, #token: 965, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:36:45 DP1 TP0] Decode batch. #running-req: 1, #token: 1919, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.95, #queue-req: 0, 
[2025-07-28 09:36:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1005, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:36:45 DP1 TP0] Decode batch. #running-req: 1, #token: 1959, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.01, #queue-req: 0, 
[2025-07-28 09:36:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1045, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.17, #queue-req: 0, 
[2025-07-28 09:36:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1999, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.02, #queue-req: 0, 
[2025-07-28 09:36:46 DP0 TP0] Decode batch. #running-req: 1, #token: 1085, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.10, #queue-req: 0, 
[2025-07-28 09:36:46 DP1 TP0] Decode batch. #running-req: 1, #token: 2039, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.01, #queue-req: 0, 
[2025-07-28 09:36:47 DP0 TP0] Decode batch. #running-req: 1, #token: 1125, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.09, #queue-req: 0, 
[2025-07-28 09:36:47 DP1 TP0] Decode batch. #running-req: 1, #token: 2079, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.85, #queue-req: 0, 
[2025-07-28 09:36:47 DP0 TP0] Decode batch. #running-req: 1, #token: 1165, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.10, #queue-req: 0, 
[2025-07-28 09:36:48 DP1 TP0] Decode batch. #running-req: 1, #token: 2119, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.91, #queue-req: 0, 
[2025-07-28 09:36:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1205, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.10, #queue-req: 0, 
[2025-07-28 09:36:48 DP1 TP0] Decode batch. #running-req: 1, #token: 2159, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.88, #queue-req: 0, 
[2025-07-28 09:36:49 DP0 TP0] Decode batch. #running-req: 1, #token: 1245, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.12, #queue-req: 0, 
[2025-07-28 09:36:49 DP1 TP0] Decode batch. #running-req: 1, #token: 2199, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.88, #queue-req: 0, 
[2025-07-28 09:36:49 DP0 TP0] Decode batch. #running-req: 1, #token: 1285, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.13, #queue-req: 0, 
[2025-07-28 09:36:49 DP1 TP0] Decode batch. #running-req: 1, #token: 2239, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.86, #queue-req: 0, 
[2025-07-28 09:36:50] INFO:     127.0.0.1:58986 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:36:50 DP0 TP0] Decode batch. #running-req: 1, #token: 0, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.15, #queue-req: 0, 
[2025-07-28 09:36:50 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 107, #cached-token: 84, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 09:36:50 DP1 TP0] Decode batch. #running-req: 2, #token: 2389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 66.82, #queue-req: 0, 
[2025-07-28 09:36:50 DP1 TP0] Decode batch. #running-req: 2, #token: 2469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.73, #queue-req: 0, 
[2025-07-28 09:36:51 DP1 TP0] Decode batch. #running-req: 2, #token: 2549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.63, #queue-req: 0, 
[2025-07-28 09:36:52 DP1 TP0] Decode batch. #running-req: 2, #token: 2629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.62, #queue-req: 0, 
[2025-07-28 09:36:52] INFO:     127.0.0.1:37226 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:36:52 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 91, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:36:52 DP1 TP0] Decode batch. #running-req: 1, #token: 354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 74.66, #queue-req: 0, 
[2025-07-28 09:36:52 DP0 TP0] Decode batch. #running-req: 1, #token: 216, token usage: 0.00, cuda graph: True, gen throughput (token/s): 15.55, #queue-req: 0, 
[2025-07-28 09:36:53 DP1 TP0] Decode batch. #running-req: 1, #token: 394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.40, #queue-req: 0, 
[2025-07-28 09:36:53 DP0 TP0] Decode batch. #running-req: 1, #token: 256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.21, #queue-req: 0, 
[2025-07-28 09:36:53 DP1 TP0] Decode batch. #running-req: 1, #token: 434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 09:36:53 DP0 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.70, #queue-req: 0, 
[2025-07-28 09:36:54 DP1 TP0] Decode batch. #running-req: 1, #token: 474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:36:54 DP0 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.65, #queue-req: 0, 
[2025-07-28 09:36:55 DP1 TP0] Decode batch. #running-req: 1, #token: 514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:36:55 DP0 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.49, #queue-req: 0, 
[2025-07-28 09:36:55 DP1 TP0] Decode batch. #running-req: 1, #token: 554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:36:55 DP0 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 09:36:56 DP1 TP0] Decode batch. #running-req: 1, #token: 594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:36:56 DP0 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:36:56 DP1 TP0] Decode batch. #running-req: 1, #token: 634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:36:56 DP0 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:36:57 DP1 TP0] Decode batch. #running-req: 1, #token: 674, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:36:57 DP0 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:36:57 DP1 TP0] Decode batch. #running-req: 1, #token: 714, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:36:58 DP0 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:36:58 DP1 TP0] Decode batch. #running-req: 1, #token: 754, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:36:58 DP0 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:36:59 DP1 TP0] Decode batch. #running-req: 1, #token: 794, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:36:59 DP0 TP0] Decode batch. #running-req: 1, #token: 656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:36:59 DP1 TP0] Decode batch. #running-req: 1, #token: 834, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:36:59 DP0 TP0] Decode batch. #running-req: 1, #token: 696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:37:00 DP1 TP0] Decode batch. #running-req: 1, #token: 874, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:37:00 DP0 TP0] Decode batch. #running-req: 1, #token: 736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:37:00] INFO:     127.0.0.1:56508 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:37:00 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 85, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:37:00 DP1 TP0] Decode batch. #running-req: 1, #token: 193, token usage: 0.00, cuda graph: True, gen throughput (token/s): 64.02, #queue-req: 0, 
[2025-07-28 09:37:00 DP0 TP0] Decode batch. #running-req: 1, #token: 776, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:37:01 DP1 TP0] Decode batch. #running-req: 1, #token: 233, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.30, #queue-req: 0, 
[2025-07-28 09:37:01 DP0 TP0] Decode batch. #running-req: 1, #token: 816, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:37:01 DP1 TP0] Decode batch. #running-req: 1, #token: 273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.08, #queue-req: 0, 
[2025-07-28 09:37:02 DP0 TP0] Decode batch. #running-req: 1, #token: 856, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:37:02 DP1 TP0] Decode batch. #running-req: 1, #token: 313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.73, #queue-req: 0, 
[2025-07-28 09:37:02 DP0 TP0] Decode batch. #running-req: 1, #token: 896, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:37:03 DP1 TP0] Decode batch. #running-req: 1, #token: 353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.63, #queue-req: 0, 
[2025-07-28 09:37:03 DP0 TP0] Decode batch. #running-req: 1, #token: 936, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:37:03 DP1 TP0] Decode batch. #running-req: 1, #token: 393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.49, #queue-req: 0, 
[2025-07-28 09:37:03 DP0 TP0] Decode batch. #running-req: 1, #token: 976, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:37:04 DP1 TP0] Decode batch. #running-req: 1, #token: 433, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:37:04 DP0 TP0] Decode batch. #running-req: 1, #token: 1016, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:37:04 DP1 TP0] Decode batch. #running-req: 1, #token: 473, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 09:37:04 DP0 TP0] Decode batch. #running-req: 1, #token: 1056, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:37:05 DP1 TP0] Decode batch. #running-req: 1, #token: 513, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 09:37:05 DP0 TP0] Decode batch. #running-req: 1, #token: 1096, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.08, #queue-req: 0, 
[2025-07-28 09:37:06 DP1 TP0] Decode batch. #running-req: 1, #token: 553, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:37:06 DP0 TP0] Decode batch. #running-req: 1, #token: 1136, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.10, #queue-req: 0, 
[2025-07-28 09:37:06 DP1 TP0] Decode batch. #running-req: 1, #token: 593, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:37:06 DP0 TP0] Decode batch. #running-req: 1, #token: 1176, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.13, #queue-req: 0, 
[2025-07-28 09:37:07 DP1 TP0] Decode batch. #running-req: 1, #token: 633, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:37:07 DP0 TP0] Decode batch. #running-req: 1, #token: 1216, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:37:07 DP1 TP0] Decode batch. #running-req: 1, #token: 673, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:37:07 DP0 TP0] Decode batch. #running-req: 1, #token: 1256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.12, #queue-req: 0, 
[2025-07-28 09:37:08 DP1 TP0] Decode batch. #running-req: 1, #token: 713, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:37:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.13, #queue-req: 0, 
[2025-07-28 09:37:08 DP1 TP0] Decode batch. #running-req: 1, #token: 753, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:37:09 DP0 TP0] Decode batch. #running-req: 1, #token: 1336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.11, #queue-req: 0, 
[2025-07-28 09:37:09 DP1 TP0] Decode batch. #running-req: 1, #token: 793, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:37:09 DP0 TP0] Decode batch. #running-req: 1, #token: 1376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 67.64, #queue-req: 0, 
[2025-07-28 09:37:10 DP1 TP0] Decode batch. #running-req: 1, #token: 833, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:37:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.11, #queue-req: 0, 
[2025-07-28 09:37:10 DP1 TP0] Decode batch. #running-req: 1, #token: 873, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:37:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.04, #queue-req: 0, 
[2025-07-28 09:37:11 DP1 TP0] Decode batch. #running-req: 1, #token: 913, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:37:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.04, #queue-req: 0, 
[2025-07-28 09:37:11 DP1 TP0] Decode batch. #running-req: 1, #token: 953, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:37:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.06, #queue-req: 0, 
[2025-07-28 09:37:12 DP1 TP0] Decode batch. #running-req: 1, #token: 993, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:37:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.06, #queue-req: 0, 
[2025-07-28 09:37:12 DP1 TP0] Decode batch. #running-req: 1, #token: 1033, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:37:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.04, #queue-req: 0, 
[2025-07-28 09:37:13 DP1 TP0] Decode batch. #running-req: 1, #token: 1073, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:37:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.05, #queue-req: 0, 
[2025-07-28 09:37:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1113, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:37:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.04, #queue-req: 0, 
[2025-07-28 09:37:14] INFO:     127.0.0.1:36046 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:37:14 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 111, #cached-token: 84, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 09:37:14 DP0 TP0] Decode batch. #running-req: 2, #token: 1863, token usage: 0.00, cuda graph: True, gen throughput (token/s): 87.25, #queue-req: 0, 
[2025-07-28 09:37:15 DP0 TP0] Decode batch. #running-req: 2, #token: 1943, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.03, #queue-req: 0, 
[2025-07-28 09:37:16 DP0 TP0] Decode batch. #running-req: 2, #token: 2023, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.92, #queue-req: 0, 
[2025-07-28 09:37:16 DP0 TP0] Decode batch. #running-req: 2, #token: 2103, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.92, #queue-req: 0, 
[2025-07-28 09:37:17 DP0 TP0] Decode batch. #running-req: 2, #token: 2183, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.89, #queue-req: 0, 
[2025-07-28 09:37:17 DP0 TP0] Decode batch. #running-req: 2, #token: 2263, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.84, #queue-req: 0, 
[2025-07-28 09:37:18 DP0 TP0] Decode batch. #running-req: 2, #token: 2343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.84, #queue-req: 0, 
[2025-07-28 09:37:18 DP0 TP0] Decode batch. #running-req: 2, #token: 2423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.82, #queue-req: 0, 
[2025-07-28 09:37:19 DP0 TP0] Decode batch. #running-req: 2, #token: 2503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.78, #queue-req: 0, 
[2025-07-28 09:37:20 DP0 TP0] Decode batch. #running-req: 2, #token: 2583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.65, #queue-req: 0, 
[2025-07-28 09:37:20 DP0 TP0] Decode batch. #running-req: 2, #token: 2663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.58, #queue-req: 0, 
[2025-07-28 09:37:21 DP0 TP0] Decode batch. #running-req: 2, #token: 2743, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.52, #queue-req: 0, 
[2025-07-28 09:37:21 DP0 TP0] Decode batch. #running-req: 2, #token: 2823, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.53, #queue-req: 0, 
[2025-07-28 09:37:22 DP0 TP0] Decode batch. #running-req: 2, #token: 2903, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.48, #queue-req: 0, 
[2025-07-28 09:37:23 DP0 TP0] Decode batch. #running-req: 2, #token: 2983, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.49, #queue-req: 0, 
[2025-07-28 09:37:23 DP0 TP0] Decode batch. #running-req: 2, #token: 3063, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.49, #queue-req: 0, 
[2025-07-28 09:37:24 DP0 TP0] Decode batch. #running-req: 2, #token: 3143, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.38, #queue-req: 0, 
[2025-07-28 09:37:24 DP0 TP0] Decode batch. #running-req: 2, #token: 3223, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.41, #queue-req: 0, 
[2025-07-28 09:37:25 DP0 TP0] Decode batch. #running-req: 2, #token: 3303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.42, #queue-req: 0, 
[2025-07-28 09:37:26 DP0 TP0] Decode batch. #running-req: 2, #token: 3383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.41, #queue-req: 0, 
[2025-07-28 09:37:26 DP0 TP0] Decode batch. #running-req: 2, #token: 3463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.40, #queue-req: 0, 
[2025-07-28 09:37:27 DP0 TP0] Decode batch. #running-req: 2, #token: 3543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.36, #queue-req: 0, 
[2025-07-28 09:37:27 DP0 TP0] Decode batch. #running-req: 2, #token: 3623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.34, #queue-req: 0, 
[2025-07-28 09:37:28 DP0 TP0] Decode batch. #running-req: 2, #token: 3703, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.26, #queue-req: 0, 
[2025-07-28 09:37:29 DP0 TP0] Decode batch. #running-req: 2, #token: 3783, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.29, #queue-req: 0, 
[2025-07-28 09:37:29 DP0 TP0] Decode batch. #running-req: 2, #token: 3863, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.29, #queue-req: 0, 
[2025-07-28 09:37:30 DP0 TP0] Decode batch. #running-req: 2, #token: 3943, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.27, #queue-req: 0, 
[2025-07-28 09:37:30 DP0 TP0] Decode batch. #running-req: 2, #token: 4023, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.31, #queue-req: 0, 
[2025-07-28 09:37:31 DP0 TP0] Decode batch. #running-req: 2, #token: 4103, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.26, #queue-req: 0, 
[2025-07-28 09:37:31 DP0 TP0] Decode batch. #running-req: 2, #token: 4183, token usage: 0.01, cuda graph: True, gen throughput (token/s): 135.17, #queue-req: 0, 
[2025-07-28 09:37:32 DP0 TP0] Decode batch. #running-req: 2, #token: 4263, token usage: 0.01, cuda graph: True, gen throughput (token/s): 135.20, #queue-req: 0, 
[2025-07-28 09:37:33 DP0 TP0] Decode batch. #running-req: 2, #token: 4343, token usage: 0.01, cuda graph: True, gen throughput (token/s): 135.11, #queue-req: 0, 
[2025-07-28 09:37:33 DP0 TP0] Decode batch. #running-req: 2, #token: 4423, token usage: 0.01, cuda graph: True, gen throughput (token/s): 135.12, #queue-req: 0, 
[2025-07-28 09:37:34 DP0 TP0] Decode batch. #running-req: 2, #token: 4503, token usage: 0.01, cuda graph: True, gen throughput (token/s): 135.09, #queue-req: 0, 
[2025-07-28 09:37:34 DP0 TP0] Decode batch. #running-req: 2, #token: 4583, token usage: 0.01, cuda graph: True, gen throughput (token/s): 135.03, #queue-req: 0, 
[2025-07-28 09:37:35 DP0 TP0] Decode batch. #running-req: 2, #token: 4663, token usage: 0.01, cuda graph: True, gen throughput (token/s): 134.98, #queue-req: 0, 
[2025-07-28 09:37:36 DP0 TP0] Decode batch. #running-req: 2, #token: 4743, token usage: 0.01, cuda graph: True, gen throughput (token/s): 134.96, #queue-req: 0, 
[2025-07-28 09:37:36 DP0 TP0] Decode batch. #running-req: 2, #token: 4823, token usage: 0.01, cuda graph: True, gen throughput (token/s): 134.94, #queue-req: 0, 
[2025-07-28 09:37:37 DP0 TP0] Decode batch. #running-req: 2, #token: 4903, token usage: 0.01, cuda graph: True, gen throughput (token/s): 134.94, #queue-req: 0, 
[2025-07-28 09:37:37 DP0 TP0] Decode batch. #running-req: 2, #token: 4983, token usage: 0.01, cuda graph: True, gen throughput (token/s): 134.92, #queue-req: 0, 
[2025-07-28 09:37:38 DP0 TP0] Decode batch. #running-req: 2, #token: 5063, token usage: 0.01, cuda graph: True, gen throughput (token/s): 134.85, #queue-req: 0, 
[2025-07-28 09:37:39 DP0 TP0] Decode batch. #running-req: 2, #token: 5143, token usage: 0.01, cuda graph: True, gen throughput (token/s): 134.75, #queue-req: 0, 
[2025-07-28 09:37:39 DP0 TP0] Decode batch. #running-req: 2, #token: 5223, token usage: 0.01, cuda graph: True, gen throughput (token/s): 134.72, #queue-req: 0, 
[2025-07-28 09:37:40 DP0 TP0] Decode batch. #running-req: 2, #token: 5303, token usage: 0.01, cuda graph: True, gen throughput (token/s): 134.62, #queue-req: 0, 
[2025-07-28 09:37:40 DP0 TP0] Decode batch. #running-req: 2, #token: 5383, token usage: 0.01, cuda graph: True, gen throughput (token/s): 134.54, #queue-req: 0, 
[2025-07-28 09:37:41 DP0 TP0] Decode batch. #running-req: 2, #token: 5463, token usage: 0.01, cuda graph: True, gen throughput (token/s): 134.66, #queue-req: 0, 
[2025-07-28 09:37:42] INFO:     127.0.0.1:52816 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:37:42 DP0 TP0] Decode batch. #running-req: 2, #token: 3576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 134.41, #queue-req: 0, 
[2025-07-28 09:37:42 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 160, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:37:42 DP1 TP0] Decode batch. #running-req: 1, #token: 254, token usage: 0.00, cuda graph: True, gen throughput (token/s): 1.42, #queue-req: 0, 
[2025-07-28 09:37:42 DP0 TP0] Decode batch. #running-req: 1, #token: 3616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.76, #queue-req: 0, 
[2025-07-28 09:37:42 DP1 TP0] Decode batch. #running-req: 1, #token: 294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.12, #queue-req: 0, 
[2025-07-28 09:37:43 DP0 TP0] Decode batch. #running-req: 1, #token: 3656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.67, #queue-req: 0, 
[2025-07-28 09:37:43 DP1 TP0] Decode batch. #running-req: 1, #token: 334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.63, #queue-req: 0, 
[2025-07-28 09:37:43 DP0 TP0] Decode batch. #running-req: 1, #token: 3696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.67, #queue-req: 0, 
[2025-07-28 09:37:44 DP1 TP0] Decode batch. #running-req: 1, #token: 374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.44, #queue-req: 0, 
[2025-07-28 09:37:44 DP0 TP0] Decode batch. #running-req: 1, #token: 3736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.69, #queue-req: 0, 
[2025-07-28 09:37:44 DP1 TP0] Decode batch. #running-req: 1, #token: 414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:37:44 DP0 TP0] Decode batch. #running-req: 1, #token: 3776, token usage: 0.00, cuda graph: True, gen throughput (token/s): 67.24, #queue-req: 0, 
[2025-07-28 09:37:45 DP1 TP0] Decode batch. #running-req: 1, #token: 454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:37:45 DP0 TP0] Decode batch. #running-req: 1, #token: 3816, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.71, #queue-req: 0, 
[2025-07-28 09:37:45 DP1 TP0] Decode batch. #running-req: 1, #token: 494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:37:46 DP0 TP0] Decode batch. #running-req: 1, #token: 3856, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.68, #queue-req: 0, 
[2025-07-28 09:37:46 DP1 TP0] Decode batch. #running-req: 1, #token: 534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.15, #queue-req: 0, 
[2025-07-28 09:37:46 DP0 TP0] Decode batch. #running-req: 1, #token: 3896, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.70, #queue-req: 0, 
[2025-07-28 09:37:46 DP1 TP0] Decode batch. #running-req: 1, #token: 574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 09:37:47 DP0 TP0] Decode batch. #running-req: 1, #token: 3936, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.69, #queue-req: 0, 
[2025-07-28 09:37:47 DP1 TP0] Decode batch. #running-req: 1, #token: 614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.17, #queue-req: 0, 
[2025-07-28 09:37:47 DP0 TP0] Decode batch. #running-req: 1, #token: 3976, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.68, #queue-req: 0, 
[2025-07-28 09:37:48 DP1 TP0] Decode batch. #running-req: 1, #token: 654, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 09:37:48 DP0 TP0] Decode batch. #running-req: 1, #token: 4016, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.70, #queue-req: 0, 
[2025-07-28 09:37:48 DP1 TP0] Decode batch. #running-req: 1, #token: 694, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:37:49 DP0 TP0] Decode batch. #running-req: 1, #token: 4056, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.69, #queue-req: 0, 
[2025-07-28 09:37:49 DP1 TP0] Decode batch. #running-req: 1, #token: 734, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.18, #queue-req: 0, 
[2025-07-28 09:37:49 DP0 TP0] Decode batch. #running-req: 1, #token: 4096, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.68, #queue-req: 0, 
[2025-07-28 09:37:49 DP1 TP0] Decode batch. #running-req: 1, #token: 774, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 09:37:50 DP0 TP0] Decode batch. #running-req: 1, #token: 4136, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.54, #queue-req: 0, 
[2025-07-28 09:37:50 DP1 TP0] Decode batch. #running-req: 1, #token: 814, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:37:50 DP0 TP0] Decode batch. #running-req: 1, #token: 4176, token usage: 0.01, cuda graph: True, gen throughput (token/s): 68.53, #queue-req: 0, 
[2025-07-28 09:37:51 DP1 TP0] Decode batch. #running-req: 1, #token: 854, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 09:37:51 DP0 TP0] Decode batch. #running-req: 1, #token: 4216, token usage: 0.01, cuda graph: True, gen throughput (token/s): 68.54, #queue-req: 0, 
[2025-07-28 09:37:51 DP1 TP0] Decode batch. #running-req: 1, #token: 894, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:37:51 DP0 TP0] Decode batch. #running-req: 1, #token: 4256, token usage: 0.01, cuda graph: True, gen throughput (token/s): 68.54, #queue-req: 0, 
[2025-07-28 09:37:52 DP1 TP0] Decode batch. #running-req: 1, #token: 934, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:37:52 DP0 TP0] Decode batch. #running-req: 1, #token: 4296, token usage: 0.01, cuda graph: True, gen throughput (token/s): 68.54, #queue-req: 0, 
[2025-07-28 09:37:52 DP1 TP0] Decode batch. #running-req: 1, #token: 974, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:37:53 DP0 TP0] Decode batch. #running-req: 1, #token: 4336, token usage: 0.01, cuda graph: True, gen throughput (token/s): 68.55, #queue-req: 0, 
[2025-07-28 09:37:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1014, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:37:53 DP0 TP0] Decode batch. #running-req: 1, #token: 4376, token usage: 0.01, cuda graph: True, gen throughput (token/s): 68.54, #queue-req: 0, 
[2025-07-28 09:37:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1054, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:37:54 DP0 TP0] Decode batch. #running-req: 1, #token: 4416, token usage: 0.01, cuda graph: True, gen throughput (token/s): 68.54, #queue-req: 0, 
[2025-07-28 09:37:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1094, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:37:54 DP0 TP0] Decode batch. #running-req: 1, #token: 4456, token usage: 0.01, cuda graph: True, gen throughput (token/s): 68.54, #queue-req: 0, 
[2025-07-28 09:37:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1134, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.09, #queue-req: 0, 
[2025-07-28 09:37:55 DP0 TP0] Decode batch. #running-req: 1, #token: 4496, token usage: 0.01, cuda graph: True, gen throughput (token/s): 68.52, #queue-req: 0, 
[2025-07-28 09:37:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1174, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.04, #queue-req: 0, 
[2025-07-28 09:37:56 DP0 TP0] Decode batch. #running-req: 1, #token: 4536, token usage: 0.01, cuda graph: True, gen throughput (token/s): 68.49, #queue-req: 0, 
[2025-07-28 09:37:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1214, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.05, #queue-req: 0, 
[2025-07-28 09:37:56 DP0 TP0] Decode batch. #running-req: 1, #token: 4576, token usage: 0.01, cuda graph: True, gen throughput (token/s): 68.45, #queue-req: 0, 
[2025-07-28 09:37:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1254, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.05, #queue-req: 0, 
[2025-07-28 09:37:57 DP0 TP0] Decode batch. #running-req: 1, #token: 4616, token usage: 0.01, cuda graph: True, gen throughput (token/s): 68.45, #queue-req: 0, 
[2025-07-28 09:37:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 09:37:57 DP0 TP0] Decode batch. #running-req: 1, #token: 4656, token usage: 0.01, cuda graph: True, gen throughput (token/s): 68.45, #queue-req: 0, 
[2025-07-28 09:37:57 DP1 TP0] Decode batch. #running-req: 1, #token: 1334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:37:58 DP0 TP0] Decode batch. #running-req: 1, #token: 4696, token usage: 0.01, cuda graph: True, gen throughput (token/s): 68.44, #queue-req: 0, 
[2025-07-28 09:37:58 DP1 TP0] Decode batch. #running-req: 1, #token: 1374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.07, #queue-req: 0, 
[2025-07-28 09:37:58] INFO:     127.0.0.1:56522 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:37:58 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 139, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:37:59 DP0 TP0] Decode batch. #running-req: 1, #token: 231, token usage: 0.00, cuda graph: True, gen throughput (token/s): 62.29, #queue-req: 0, 
[2025-07-28 09:37:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.01, #queue-req: 0, 
[2025-07-28 09:37:59 DP0 TP0] Decode batch. #running-req: 1, #token: 271, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.05, #queue-req: 0, 
[2025-07-28 09:37:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.01, #queue-req: 0, 
[2025-07-28 09:38:00 DP0 TP0] Decode batch. #running-req: 1, #token: 311, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.67, #queue-req: 0, 
[2025-07-28 09:38:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.97, #queue-req: 0, 
[2025-07-28 09:38:00 DP0 TP0] Decode batch. #running-req: 1, #token: 351, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.58, #queue-req: 0, 
[2025-07-28 09:38:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.96, #queue-req: 0, 
[2025-07-28 09:38:01 DP0 TP0] Decode batch. #running-req: 1, #token: 391, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.44, #queue-req: 0, 
[2025-07-28 09:38:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.01, #queue-req: 0, 
[2025-07-28 09:38:01 DP0 TP0] Decode batch. #running-req: 1, #token: 431, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:38:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.94, #queue-req: 0, 
[2025-07-28 09:38:02 DP0 TP0] Decode batch. #running-req: 1, #token: 471, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:38:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1654, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.96, #queue-req: 0, 
[2025-07-28 09:38:03 DP0 TP0] Decode batch. #running-req: 1, #token: 511, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:38:03 DP1 TP0] Decode batch. #running-req: 1, #token: 1694, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.97, #queue-req: 0, 
[2025-07-28 09:38:03 DP0 TP0] Decode batch. #running-req: 1, #token: 551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:38:03 DP1 TP0] Decode batch. #running-req: 1, #token: 1734, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.99, #queue-req: 0, 
[2025-07-28 09:38:04 DP0 TP0] Decode batch. #running-req: 1, #token: 591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:38:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1774, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.97, #queue-req: 0, 
[2025-07-28 09:38:04 DP0 TP0] Decode batch. #running-req: 1, #token: 631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:38:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1814, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.04, #queue-req: 0, 
[2025-07-28 09:38:05 DP0 TP0] Decode batch. #running-req: 1, #token: 671, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:38:05 DP1 TP0] Decode batch. #running-req: 1, #token: 1854, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.96, #queue-req: 0, 
[2025-07-28 09:38:05 DP0 TP0] Decode batch. #running-req: 1, #token: 711, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:38:06 DP1 TP0] Decode batch. #running-req: 1, #token: 1894, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.96, #queue-req: 0, 
[2025-07-28 09:38:06 DP0 TP0] Decode batch. #running-req: 1, #token: 751, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:38:06 DP1 TP0] Decode batch. #running-req: 1, #token: 1934, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.96, #queue-req: 0, 
[2025-07-28 09:38:07 DP0 TP0] Decode batch. #running-req: 1, #token: 791, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:38:07 DP1 TP0] Decode batch. #running-req: 1, #token: 1974, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.95, #queue-req: 0, 
[2025-07-28 09:38:07 DP0 TP0] Decode batch. #running-req: 1, #token: 831, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:38:07 DP1 TP0] Decode batch. #running-req: 1, #token: 2014, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.93, #queue-req: 0, 
[2025-07-28 09:38:08 DP0 TP0] Decode batch. #running-req: 1, #token: 871, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:38:08 DP1 TP0] Decode batch. #running-req: 1, #token: 2054, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.96, #queue-req: 0, 
[2025-07-28 09:38:08 DP0 TP0] Decode batch. #running-req: 1, #token: 911, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:38:08 DP1 TP0] Decode batch. #running-req: 1, #token: 2094, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.82, #queue-req: 0, 
[2025-07-28 09:38:09 DP0 TP0] Decode batch. #running-req: 1, #token: 951, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:38:09 DP1 TP0] Decode batch. #running-req: 1, #token: 2134, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.81, #queue-req: 0, 
[2025-07-28 09:38:10 DP0 TP0] Decode batch. #running-req: 1, #token: 991, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:38:10 DP1 TP0] Decode batch. #running-req: 1, #token: 2174, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.83, #queue-req: 0, 
[2025-07-28 09:38:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1031, token usage: 0.00, cuda graph: True, gen throughput (token/s): 67.90, #queue-req: 0, 
[2025-07-28 09:38:10 DP1 TP0] Decode batch. #running-req: 1, #token: 2214, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.82, #queue-req: 0, 
[2025-07-28 09:38:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1071, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.92, #queue-req: 0, 
[2025-07-28 09:38:11 DP1 TP0] Decode batch. #running-req: 1, #token: 2254, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.82, #queue-req: 0, 
[2025-07-28 09:38:11] INFO:     127.0.0.1:60960 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:38:11 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 113, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:38:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1111, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.12, #queue-req: 0, 
[2025-07-28 09:38:11] INFO:     127.0.0.1:41396 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:38:11 DP1 TP0] Decode batch. #running-req: 1, #token: 215, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.34, #queue-req: 0, 
[2025-07-28 09:38:11 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 121, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:38:12 DP0 TP0] Decode batch. #running-req: 1, #token: 237, token usage: 0.00, cuda graph: True, gen throughput (token/s): 64.15, #queue-req: 0, 
[2025-07-28 09:38:12 DP1 TP0] Decode batch. #running-req: 1, #token: 255, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.21, #queue-req: 0, 
[2025-07-28 09:38:12 DP0 TP0] Decode batch. #running-req: 1, #token: 277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.00, #queue-req: 0, 
[2025-07-28 09:38:13 DP1 TP0] Decode batch. #running-req: 1, #token: 295, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.67, #queue-req: 0, 
[2025-07-28 09:38:13 DP0 TP0] Decode batch. #running-req: 1, #token: 317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.69, #queue-req: 0, 
[2025-07-28 09:38:13 DP1 TP0] Decode batch. #running-req: 1, #token: 335, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.60, #queue-req: 0, 
[2025-07-28 09:38:14 DP0 TP0] Decode batch. #running-req: 1, #token: 357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.58, #queue-req: 0, 
[2025-07-28 09:38:14 DP1 TP0] Decode batch. #running-req: 1, #token: 375, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.46, #queue-req: 0, 
[2025-07-28 09:38:14 DP0 TP0] Decode batch. #running-req: 1, #token: 397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.45, #queue-req: 0, 
[2025-07-28 09:38:14 DP1 TP0] Decode batch. #running-req: 1, #token: 415, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:38:15 DP0 TP0] Decode batch. #running-req: 1, #token: 437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:38:15 DP1 TP0] Decode batch. #running-req: 1, #token: 455, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:38:15 DP0 TP0] Decode batch. #running-req: 1, #token: 477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:38:15 DP1 TP0] Decode batch. #running-req: 1, #token: 495, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:38:16 DP0 TP0] Decode batch. #running-req: 1, #token: 517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:38:16 DP1 TP0] Decode batch. #running-req: 1, #token: 535, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.18, #queue-req: 0, 
[2025-07-28 09:38:16 DP0 TP0] Decode batch. #running-req: 1, #token: 557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:38:17 DP1 TP0] Decode batch. #running-req: 1, #token: 575, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 09:38:17 DP0 TP0] Decode batch. #running-req: 1, #token: 597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:38:17 DP1 TP0] Decode batch. #running-req: 1, #token: 615, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.18, #queue-req: 0, 
[2025-07-28 09:38:18 DP0 TP0] Decode batch. #running-req: 1, #token: 637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:38:18 DP1 TP0] Decode batch. #running-req: 1, #token: 655, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.18, #queue-req: 0, 
[2025-07-28 09:38:18 DP0 TP0] Decode batch. #running-req: 1, #token: 677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:38:18 DP1 TP0] Decode batch. #running-req: 1, #token: 695, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 09:38:19 DP0 TP0] Decode batch. #running-req: 1, #token: 717, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:38:19 DP1 TP0] Decode batch. #running-req: 1, #token: 735, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:38:19 DP0 TP0] Decode batch. #running-req: 1, #token: 757, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:38:19 DP1 TP0] Decode batch. #running-req: 1, #token: 775, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:38:20 DP0 TP0] Decode batch. #running-req: 1, #token: 797, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:38:20 DP1 TP0] Decode batch. #running-req: 1, #token: 815, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:38:21 DP0 TP0] Decode batch. #running-req: 1, #token: 837, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:38:21 DP1 TP0] Decode batch. #running-req: 1, #token: 855, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:38:21 DP0 TP0] Decode batch. #running-req: 1, #token: 877, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:38:21 DP1 TP0] Decode batch. #running-req: 1, #token: 895, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:38:21] INFO:     127.0.0.1:50778 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:38:21 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 192, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 09:38:22 DP1 TP0] Decode batch. #running-req: 2, #token: 1151, token usage: 0.00, cuda graph: True, gen throughput (token/s): 96.64, #queue-req: 0, 
[2025-07-28 09:38:22 DP1 TP0] Decode batch. #running-req: 2, #token: 1231, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.00, #queue-req: 0, 
[2025-07-28 09:38:23 DP1 TP0] Decode batch. #running-req: 2, #token: 1311, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.02, #queue-req: 0, 
[2025-07-28 09:38:24 DP1 TP0] Decode batch. #running-req: 2, #token: 1391, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.05, #queue-req: 0, 
[2025-07-28 09:38:24 DP1 TP0] Decode batch. #running-req: 2, #token: 1471, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.02, #queue-req: 0, 
[2025-07-28 09:38:25 DP1 TP0] Decode batch. #running-req: 2, #token: 1551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.84, #queue-req: 0, 
[2025-07-28 09:38:25 DP1 TP0] Decode batch. #running-req: 2, #token: 1631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.67, #queue-req: 0, 
[2025-07-28 09:38:26] INFO:     127.0.0.1:50772 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:38:26 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 80, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:38:26 DP1 TP0] Decode batch. #running-req: 1, #token: 582, token usage: 0.00, cuda graph: True, gen throughput (token/s): 130.80, #queue-req: 0, 
[2025-07-28 09:38:26 DP0 TP0] Decode batch. #running-req: 1, #token: 181, token usage: 0.00, cuda graph: True, gen throughput (token/s): 7.72, #queue-req: 0, 
[2025-07-28 09:38:27 DP1 TP0] Decode batch. #running-req: 1, #token: 622, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:38:27 DP0 TP0] Decode batch. #running-req: 1, #token: 221, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.26, #queue-req: 0, 
[2025-07-28 09:38:27 DP1 TP0] Decode batch. #running-req: 1, #token: 662, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:38:27 DP0 TP0] Decode batch. #running-req: 1, #token: 261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.19, #queue-req: 0, 
[2025-07-28 09:38:28 DP1 TP0] Decode batch. #running-req: 1, #token: 702, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:38:28 DP0 TP0] Decode batch. #running-req: 1, #token: 301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.70, #queue-req: 0, 
[2025-07-28 09:38:28 DP1 TP0] Decode batch. #running-req: 1, #token: 742, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.18, #queue-req: 0, 
[2025-07-28 09:38:29 DP0 TP0] Decode batch. #running-req: 1, #token: 341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.64, #queue-req: 0, 
[2025-07-28 09:38:29 DP1 TP0] Decode batch. #running-req: 1, #token: 782, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 09:38:29 DP0 TP0] Decode batch. #running-req: 1, #token: 381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.46, #queue-req: 0, 
[2025-07-28 09:38:29 DP1 TP0] Decode batch. #running-req: 1, #token: 822, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:38:30 DP0 TP0] Decode batch. #running-req: 1, #token: 421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:38:30 DP1 TP0] Decode batch. #running-req: 1, #token: 862, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:38:30 DP0 TP0] Decode batch. #running-req: 1, #token: 461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:38:31 DP1 TP0] Decode batch. #running-req: 1, #token: 902, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:38:31 DP0 TP0] Decode batch. #running-req: 1, #token: 501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:38:31 DP1 TP0] Decode batch. #running-req: 1, #token: 942, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:38:31 DP0 TP0] Decode batch. #running-req: 1, #token: 541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:38:32 DP1 TP0] Decode batch. #running-req: 1, #token: 982, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:38:32 DP0 TP0] Decode batch. #running-req: 1, #token: 581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:38:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1022, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:38:33 DP0 TP0] Decode batch. #running-req: 1, #token: 621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 09:38:33 DP1 TP0] Decode batch. #running-req: 1, #token: 1062, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:38:33 DP0 TP0] Decode batch. #running-req: 1, #token: 661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:38:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1102, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.13, #queue-req: 0, 
[2025-07-28 09:38:34 DP0 TP0] Decode batch. #running-req: 1, #token: 701, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:38:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1142, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.13, #queue-req: 0, 
[2025-07-28 09:38:34 DP0 TP0] Decode batch. #running-req: 1, #token: 741, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:38:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1182, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:38:35 DP0 TP0] Decode batch. #running-req: 1, #token: 781, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 09:38:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1222, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:38:36 DP0 TP0] Decode batch. #running-req: 1, #token: 821, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:38:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1262, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 09:38:36 DP0 TP0] Decode batch. #running-req: 1, #token: 861, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.18, #queue-req: 0, 
[2025-07-28 09:38:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1302, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.15, #queue-req: 0, 
[2025-07-28 09:38:37 DP0 TP0] Decode batch. #running-req: 1, #token: 901, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:38:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1342, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.15, #queue-req: 0, 
[2025-07-28 09:38:37 DP0 TP0] Decode batch. #running-req: 1, #token: 941, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:38:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1382, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:38:38 DP0 TP0] Decode batch. #running-req: 1, #token: 981, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:38:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1422, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.12, #queue-req: 0, 
[2025-07-28 09:38:38 DP0 TP0] Decode batch. #running-req: 1, #token: 1021, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:38:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1462, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.06, #queue-req: 0, 
[2025-07-28 09:38:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1061, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.11, #queue-req: 0, 
[2025-07-28 09:38:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1502, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.97, #queue-req: 0, 
[2025-07-28 09:38:40 DP0 TP0] Decode batch. #running-req: 1, #token: 1101, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.09, #queue-req: 0, 
[2025-07-28 09:38:40] INFO:     127.0.0.1:54956 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:38:40 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 150, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:38:40] INFO:     127.0.0.1:49044 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:38:40 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 124, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:38:40 DP1 TP0] Decode batch. #running-req: 1, #token: 258, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.35, #queue-req: 0, 
[2025-07-28 09:38:40 DP0 TP0] Decode batch. #running-req: 1, #token: 247, token usage: 0.00, cuda graph: True, gen throughput (token/s): 64.39, #queue-req: 0, 
[2025-07-28 09:38:40 DP1 TP0] Decode batch. #running-req: 1, #token: 298, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.64, #queue-req: 0, 
[2025-07-28 09:38:41 DP0 TP0] Decode batch. #running-req: 1, #token: 287, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.81, #queue-req: 0, 
[2025-07-28 09:38:41 DP1 TP0] Decode batch. #running-req: 1, #token: 338, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.61, #queue-req: 0, 
[2025-07-28 09:38:41 DP0 TP0] Decode batch. #running-req: 1, #token: 327, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.67, #queue-req: 0, 
[2025-07-28 09:38:42 DP1 TP0] Decode batch. #running-req: 1, #token: 378, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.51, #queue-req: 0, 
[2025-07-28 09:38:42 DP0 TP0] Decode batch. #running-req: 1, #token: 367, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.53, #queue-req: 0, 
[2025-07-28 09:38:42 DP1 TP0] Decode batch. #running-req: 1, #token: 418, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 09:38:42 DP0 TP0] Decode batch. #running-req: 1, #token: 407, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 09:38:43 DP1 TP0] Decode batch. #running-req: 1, #token: 458, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:38:43 DP0 TP0] Decode batch. #running-req: 1, #token: 447, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:38:43 DP1 TP0] Decode batch. #running-req: 1, #token: 498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 09:38:44 DP0 TP0] Decode batch. #running-req: 1, #token: 487, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:38:44 DP1 TP0] Decode batch. #running-req: 1, #token: 538, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:38:44 DP0 TP0] Decode batch. #running-req: 1, #token: 527, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:38:45 DP1 TP0] Decode batch. #running-req: 1, #token: 578, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:38:45 DP0 TP0] Decode batch. #running-req: 1, #token: 567, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:38:45 DP1 TP0] Decode batch. #running-req: 1, #token: 618, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.17, #queue-req: 0, 
[2025-07-28 09:38:45 DP0 TP0] Decode batch. #running-req: 1, #token: 607, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.18, #queue-req: 0, 
[2025-07-28 09:38:46 DP1 TP0] Decode batch. #running-req: 1, #token: 658, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 09:38:46 DP0 TP0] Decode batch. #running-req: 1, #token: 647, token usage: 0.00, cuda graph: True, gen throughput (token/s): 67.86, #queue-req: 0, 
[2025-07-28 09:38:46 DP1 TP0] Decode batch. #running-req: 1, #token: 698, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:38:47 DP0 TP0] Decode batch. #running-req: 1, #token: 687, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:38:47 DP1 TP0] Decode batch. #running-req: 1, #token: 738, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 09:38:47 DP0 TP0] Decode batch. #running-req: 1, #token: 727, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:38:47 DP1 TP0] Decode batch. #running-req: 1, #token: 778, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.18, #queue-req: 0, 
[2025-07-28 09:38:48 DP0 TP0] Decode batch. #running-req: 1, #token: 767, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 09:38:48 DP1 TP0] Decode batch. #running-req: 1, #token: 818, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.17, #queue-req: 0, 
[2025-07-28 09:38:48 DP0 TP0] Decode batch. #running-req: 1, #token: 807, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 09:38:49 DP1 TP0] Decode batch. #running-req: 1, #token: 858, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 09:38:49 DP0 TP0] Decode batch. #running-req: 1, #token: 847, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 09:38:49 DP1 TP0] Decode batch. #running-req: 1, #token: 898, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.17, #queue-req: 0, 
[2025-07-28 09:38:49 DP0 TP0] Decode batch. #running-req: 1, #token: 887, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:38:50 DP1 TP0] Decode batch. #running-req: 1, #token: 938, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:38:50 DP0 TP0] Decode batch. #running-req: 1, #token: 927, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:38:50 DP1 TP0] Decode batch. #running-req: 1, #token: 978, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.18, #queue-req: 0, 
[2025-07-28 09:38:51 DP0 TP0] Decode batch. #running-req: 1, #token: 967, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:38:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1018, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.17, #queue-req: 0, 
[2025-07-28 09:38:51 DP0 TP0] Decode batch. #running-req: 1, #token: 1007, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:38:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1058, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.04, #queue-req: 0, 
[2025-07-28 09:38:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1047, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:38:52] INFO:     127.0.0.1:33298 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:38:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1098, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.03, #queue-req: 0, 
[2025-07-28 09:38:52 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 86, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 09:38:53 DP1 TP0] Decode batch. #running-req: 2, #token: 1318, token usage: 0.00, cuda graph: True, gen throughput (token/s): 122.49, #queue-req: 0, 
[2025-07-28 09:38:53 DP1 TP0] Decode batch. #running-req: 2, #token: 1398, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.76, #queue-req: 0, 
[2025-07-28 09:38:54 DP1 TP0] Decode batch. #running-req: 2, #token: 1478, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.77, #queue-req: 0, 
[2025-07-28 09:38:54 DP1 TP0] Decode batch. #running-req: 2, #token: 1558, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.90, #queue-req: 0, 
[2025-07-28 09:38:55 DP1 TP0] Decode batch. #running-req: 2, #token: 1638, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.93, #queue-req: 0, 
[2025-07-28 09:38:56] INFO:     127.0.0.1:33282 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:38:56 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 136, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:38:56 DP1 TP0] Decode batch. #running-req: 1, #token: 466, token usage: 0.00, cuda graph: True, gen throughput (token/s): 132.76, #queue-req: 0, 
[2025-07-28 09:38:56 DP0 TP0] Decode batch. #running-req: 1, #token: 243, token usage: 0.00, cuda graph: True, gen throughput (token/s): 9.40, #queue-req: 0, 
[2025-07-28 09:38:56 DP1 TP0] Decode batch. #running-req: 1, #token: 506, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 09:38:57 DP0 TP0] Decode batch. #running-req: 1, #token: 283, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.89, #queue-req: 0, 
[2025-07-28 09:38:57 DP1 TP0] Decode batch. #running-req: 1, #token: 546, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:38:57 DP0 TP0] Decode batch. #running-req: 1, #token: 323, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.67, #queue-req: 0, 
[2025-07-28 09:38:57 DP1 TP0] Decode batch. #running-req: 1, #token: 586, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.17, #queue-req: 0, 
[2025-07-28 09:38:58 DP0 TP0] Decode batch. #running-req: 1, #token: 363, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.57, #queue-req: 0, 
[2025-07-28 09:38:58 DP1 TP0] Decode batch. #running-req: 1, #token: 626, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.18, #queue-req: 0, 
[2025-07-28 09:38:58 DP0 TP0] Decode batch. #running-req: 1, #token: 403, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:38:59 DP1 TP0] Decode batch. #running-req: 1, #token: 666, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:38:59 DP0 TP0] Decode batch. #running-req: 1, #token: 443, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:38:59 DP1 TP0] Decode batch. #running-req: 1, #token: 706, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.15, #queue-req: 0, 
[2025-07-28 09:38:59 DP0 TP0] Decode batch. #running-req: 1, #token: 483, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:39:00 DP1 TP0] Decode batch. #running-req: 1, #token: 746, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 09:39:00 DP0 TP0] Decode batch. #running-req: 1, #token: 523, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:39:00 DP1 TP0] Decode batch. #running-req: 1, #token: 786, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.18, #queue-req: 0, 
[2025-07-28 09:39:01 DP0 TP0] Decode batch. #running-req: 1, #token: 563, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:39:01 DP1 TP0] Decode batch. #running-req: 1, #token: 826, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.17, #queue-req: 0, 
[2025-07-28 09:39:01 DP0 TP0] Decode batch. #running-req: 1, #token: 603, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:39:01 DP1 TP0] Decode batch. #running-req: 1, #token: 866, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:39:02 DP0 TP0] Decode batch. #running-req: 1, #token: 643, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:39:02 DP1 TP0] Decode batch. #running-req: 1, #token: 906, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:39:02 DP0 TP0] Decode batch. #running-req: 1, #token: 683, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:39:03 DP1 TP0] Decode batch. #running-req: 1, #token: 946, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:39:03 DP0 TP0] Decode batch. #running-req: 1, #token: 723, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:39:03 DP1 TP0] Decode batch. #running-req: 1, #token: 986, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:39:03 DP0 TP0] Decode batch. #running-req: 1, #token: 763, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:39:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1026, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:39:04 DP0 TP0] Decode batch. #running-req: 1, #token: 803, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 09:39:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1066, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:39:05 DP0 TP0] Decode batch. #running-req: 1, #token: 843, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:39:05 DP1 TP0] Decode batch. #running-req: 1, #token: 1106, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.13, #queue-req: 0, 
[2025-07-28 09:39:05 DP0 TP0] Decode batch. #running-req: 1, #token: 883, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:39:05 DP1 TP0] Decode batch. #running-req: 1, #token: 1146, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:39:06 DP0 TP0] Decode batch. #running-req: 1, #token: 923, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 09:39:06 DP1 TP0] Decode batch. #running-req: 1, #token: 1186, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.15, #queue-req: 0, 
[2025-07-28 09:39:06 DP0 TP0] Decode batch. #running-req: 1, #token: 963, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:39:07 DP1 TP0] Decode batch. #running-req: 1, #token: 1226, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:39:07 DP0 TP0] Decode batch. #running-req: 1, #token: 1003, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:39:07 DP1 TP0] Decode batch. #running-req: 1, #token: 1266, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.18, #queue-req: 0, 
[2025-07-28 09:39:07] INFO:     127.0.0.1:40820 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:39:07 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 91, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:39:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1043, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.15, #queue-req: 0, 
[2025-07-28 09:39:08 DP1 TP0] Decode batch. #running-req: 1, #token: 210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 64.00, #queue-req: 0, 
[2025-07-28 09:39:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1083, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.06, #queue-req: 0, 
[2025-07-28 09:39:08 DP1 TP0] Decode batch. #running-req: 1, #token: 250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.31, #queue-req: 0, 
[2025-07-28 09:39:09 DP0 TP0] Decode batch. #running-req: 1, #token: 1123, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.01, #queue-req: 0, 
[2025-07-28 09:39:09 DP1 TP0] Decode batch. #running-req: 1, #token: 290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.84, #queue-req: 0, 
[2025-07-28 09:39:09 DP0 TP0] Decode batch. #running-req: 1, #token: 1163, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.08, #queue-req: 0, 
[2025-07-28 09:39:10 DP1 TP0] Decode batch. #running-req: 1, #token: 330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.71, #queue-req: 0, 
[2025-07-28 09:39:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1203, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.12, #queue-req: 0, 
[2025-07-28 09:39:10 DP1 TP0] Decode batch. #running-req: 1, #token: 370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.59, #queue-req: 0, 
[2025-07-28 09:39:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1243, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.11, #queue-req: 0, 
[2025-07-28 09:39:11 DP1 TP0] Decode batch. #running-req: 1, #token: 410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.41, #queue-req: 0, 
[2025-07-28 09:39:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1283, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.11, #queue-req: 0, 
[2025-07-28 09:39:11 DP1 TP0] Decode batch. #running-req: 1, #token: 450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 09:39:11] INFO:     127.0.0.1:40822 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:39:11 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 136, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:39:12 DP0 TP0] Decode batch. #running-req: 1, #token: 242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.53, #queue-req: 0, 
[2025-07-28 09:39:12 DP1 TP0] Decode batch. #running-req: 1, #token: 490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 09:39:12 DP0 TP0] Decode batch. #running-req: 1, #token: 282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.93, #queue-req: 0, 
[2025-07-28 09:39:12 DP1 TP0] Decode batch. #running-req: 1, #token: 530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 09:39:13 DP0 TP0] Decode batch. #running-req: 1, #token: 322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.70, #queue-req: 0, 
[2025-07-28 09:39:13 DP1 TP0] Decode batch. #running-req: 1, #token: 570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:39:13 DP0 TP0] Decode batch. #running-req: 1, #token: 362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.57, #queue-req: 0, 
[2025-07-28 09:39:14 DP1 TP0] Decode batch. #running-req: 1, #token: 610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:39:14 DP0 TP0] Decode batch. #running-req: 1, #token: 402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.42, #queue-req: 0, 
[2025-07-28 09:39:14 DP1 TP0] Decode batch. #running-req: 1, #token: 650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 09:39:15 DP0 TP0] Decode batch. #running-req: 1, #token: 442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:39:15 DP1 TP0] Decode batch. #running-req: 1, #token: 690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:39:15 DP0 TP0] Decode batch. #running-req: 1, #token: 482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.15, #queue-req: 0, 
[2025-07-28 09:39:15 DP1 TP0] Decode batch. #running-req: 1, #token: 730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 09:39:16 DP0 TP0] Decode batch. #running-req: 1, #token: 522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.14, #queue-req: 0, 
[2025-07-28 09:39:16 DP1 TP0] Decode batch. #running-req: 1, #token: 770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 09:39:16] INFO:     127.0.0.1:60010 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:39:16 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 74, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:39:16 DP0 TP0] Decode batch. #running-req: 1, #token: 562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.13, #queue-req: 0, 
[2025-07-28 09:39:17 DP1 TP0] Decode batch. #running-req: 1, #token: 175, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.49, #queue-req: 0, 
[2025-07-28 09:39:17 DP0 TP0] Decode batch. #running-req: 1, #token: 602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:39:17 DP1 TP0] Decode batch. #running-req: 1, #token: 215, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.30, #queue-req: 0, 
[2025-07-28 09:39:17 DP0 TP0] Decode batch. #running-req: 1, #token: 642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:39:18 DP1 TP0] Decode batch. #running-req: 1, #token: 255, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.29, #queue-req: 0, 
[2025-07-28 09:39:18 DP0 TP0] Decode batch. #running-req: 1, #token: 682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:39:18 DP1 TP0] Decode batch. #running-req: 1, #token: 295, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.75, #queue-req: 0, 
[2025-07-28 09:39:19 DP0 TP0] Decode batch. #running-req: 1, #token: 722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:39:19 DP1 TP0] Decode batch. #running-req: 1, #token: 335, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.69, #queue-req: 0, 
[2025-07-28 09:39:19 DP0 TP0] Decode batch. #running-req: 1, #token: 762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:39:19 DP1 TP0] Decode batch. #running-req: 1, #token: 375, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.48, #queue-req: 0, 
[2025-07-28 09:39:20 DP0 TP0] Decode batch. #running-req: 1, #token: 802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:39:20 DP1 TP0] Decode batch. #running-req: 1, #token: 415, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:39:20 DP0 TP0] Decode batch. #running-req: 1, #token: 842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:39:21 DP1 TP0] Decode batch. #running-req: 1, #token: 455, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:39:21 DP0 TP0] Decode batch. #running-req: 1, #token: 882, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:39:21 DP1 TP0] Decode batch. #running-req: 1, #token: 495, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:39:21 DP0 TP0] Decode batch. #running-req: 1, #token: 922, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:39:22 DP1 TP0] Decode batch. #running-req: 1, #token: 535, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:39:22 DP0 TP0] Decode batch. #running-req: 1, #token: 962, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:39:22 DP1 TP0] Decode batch. #running-req: 1, #token: 575, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:39:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1002, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:39:23 DP1 TP0] Decode batch. #running-req: 1, #token: 615, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 09:39:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1042, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.18, #queue-req: 0, 
[2025-07-28 09:39:23 DP1 TP0] Decode batch. #running-req: 1, #token: 655, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:39:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1082, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.13, #queue-req: 0, 
[2025-07-28 09:39:24 DP1 TP0] Decode batch. #running-req: 1, #token: 695, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:39:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1122, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.13, #queue-req: 0, 
[2025-07-28 09:39:24] INFO:     127.0.0.1:56508 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:39:24 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 142, #cached-token: 86, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 09:39:25 DP0 TP0] Decode batch. #running-req: 2, #token: 1341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 116.99, #queue-req: 0, 
[2025-07-28 09:39:26 DP0 TP0] Decode batch. #running-req: 2, #token: 1421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.03, #queue-req: 0, 
[2025-07-28 09:39:26 DP0 TP0] Decode batch. #running-req: 2, #token: 1501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.05, #queue-req: 0, 
[2025-07-28 09:39:27 DP0 TP0] Decode batch. #running-req: 2, #token: 1581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.03, #queue-req: 0, 
[2025-07-28 09:39:27 DP0 TP0] Decode batch. #running-req: 2, #token: 1661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.02, #queue-req: 0, 
[2025-07-28 09:39:28 DP0 TP0] Decode batch. #running-req: 2, #token: 1741, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.05, #queue-req: 0, 
[2025-07-28 09:39:29 DP0 TP0] Decode batch. #running-req: 2, #token: 1821, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.03, #queue-req: 0, 
[2025-07-28 09:39:29 DP0 TP0] Decode batch. #running-req: 2, #token: 1901, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.97, #queue-req: 0, 
[2025-07-28 09:39:30 DP0 TP0] Decode batch. #running-req: 2, #token: 1981, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.95, #queue-req: 0, 
[2025-07-28 09:39:30 DP0 TP0] Decode batch. #running-req: 2, #token: 2061, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.95, #queue-req: 0, 
[2025-07-28 09:39:31 DP0 TP0] Decode batch. #running-req: 2, #token: 2141, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.90, #queue-req: 0, 
[2025-07-28 09:39:31 DP0 TP0] Decode batch. #running-req: 2, #token: 2221, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.90, #queue-req: 0, 
[2025-07-28 09:39:32 DP0 TP0] Decode batch. #running-req: 2, #token: 2301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.81, #queue-req: 0, 
[2025-07-28 09:39:33 DP0 TP0] Decode batch. #running-req: 2, #token: 2381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.83, #queue-req: 0, 
[2025-07-28 09:39:33 DP0 TP0] Decode batch. #running-req: 2, #token: 2461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.80, #queue-req: 0, 
[2025-07-28 09:39:33] INFO:     127.0.0.1:60026 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:39:33 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 80, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:39:34 DP1 TP0] Decode batch. #running-req: 1, #token: 179, token usage: 0.00, cuda graph: True, gen throughput (token/s): 4.17, #queue-req: 0, 
[2025-07-28 09:39:34 DP0 TP0] Decode batch. #running-req: 1, #token: 863, token usage: 0.00, cuda graph: True, gen throughput (token/s): 77.65, #queue-req: 0, 
[2025-07-28 09:39:34 DP1 TP0] Decode batch. #running-req: 1, #token: 219, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.04, #queue-req: 0, 
[2025-07-28 09:39:34 DP0 TP0] Decode batch. #running-req: 1, #token: 903, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:39:35 DP1 TP0] Decode batch. #running-req: 1, #token: 259, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.15, #queue-req: 0, 
[2025-07-28 09:39:35 DP0 TP0] Decode batch. #running-req: 1, #token: 943, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 09:39:35 DP1 TP0] Decode batch. #running-req: 1, #token: 299, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.65, #queue-req: 0, 
[2025-07-28 09:39:36 DP0 TP0] Decode batch. #running-req: 1, #token: 983, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:39:36] INFO:     127.0.0.1:37732 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:39:36 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 169, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:39:36 DP1 TP0] Decode batch. #running-req: 1, #token: 339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.62, #queue-req: 0, 
[2025-07-28 09:39:36 DP0 TP0] Decode batch. #running-req: 1, #token: 273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.42, #queue-req: 0, 
[2025-07-28 09:39:36 DP1 TP0] Decode batch. #running-req: 1, #token: 379, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.45, #queue-req: 0, 
[2025-07-28 09:39:37 DP0 TP0] Decode batch. #running-req: 1, #token: 313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.66, #queue-req: 0, 
[2025-07-28 09:39:37 DP1 TP0] Decode batch. #running-req: 1, #token: 419, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:39:37 DP0 TP0] Decode batch. #running-req: 1, #token: 353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.60, #queue-req: 0, 
[2025-07-28 09:39:38 DP1 TP0] Decode batch. #running-req: 1, #token: 459, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:39:38 DP0 TP0] Decode batch. #running-req: 1, #token: 393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.45, #queue-req: 0, 
[2025-07-28 09:39:38 DP1 TP0] Decode batch. #running-req: 1, #token: 499, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 09:39:38 DP0 TP0] Decode batch. #running-req: 1, #token: 433, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:39:39 DP1 TP0] Decode batch. #running-req: 1, #token: 539, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 09:39:39 DP0 TP0] Decode batch. #running-req: 1, #token: 473, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:39:39 DP1 TP0] Decode batch. #running-req: 1, #token: 579, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:39:40 DP0 TP0] Decode batch. #running-req: 1, #token: 513, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:39:40 DP1 TP0] Decode batch. #running-req: 1, #token: 619, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:39:40 DP0 TP0] Decode batch. #running-req: 1, #token: 553, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:39:41 DP1 TP0] Decode batch. #running-req: 1, #token: 659, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 09:39:41 DP0 TP0] Decode batch. #running-req: 1, #token: 593, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:39:41 DP1 TP0] Decode batch. #running-req: 1, #token: 699, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 09:39:41 DP0 TP0] Decode batch. #running-req: 1, #token: 633, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:39:42 DP1 TP0] Decode batch. #running-req: 1, #token: 739, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:39:42 DP0 TP0] Decode batch. #running-req: 1, #token: 673, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 09:39:42 DP1 TP0] Decode batch. #running-req: 1, #token: 779, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 09:39:43 DP0 TP0] Decode batch. #running-req: 1, #token: 713, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 09:39:43 DP1 TP0] Decode batch. #running-req: 1, #token: 819, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:39:43 DP0 TP0] Decode batch. #running-req: 1, #token: 753, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:39:43 DP1 TP0] Decode batch. #running-req: 1, #token: 859, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:39:44 DP0 TP0] Decode batch. #running-req: 1, #token: 793, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:39:44 DP1 TP0] Decode batch. #running-req: 1, #token: 899, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:39:44 DP0 TP0] Decode batch. #running-req: 1, #token: 833, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:39:45 DP1 TP0] Decode batch. #running-req: 1, #token: 939, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:39:45 DP0 TP0] Decode batch. #running-req: 1, #token: 873, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:39:45 DP1 TP0] Decode batch. #running-req: 1, #token: 979, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:39:45 DP0 TP0] Decode batch. #running-req: 1, #token: 913, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:39:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1019, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 09:39:46 DP0 TP0] Decode batch. #running-req: 1, #token: 953, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:39:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1059, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:39:47 DP0 TP0] Decode batch. #running-req: 1, #token: 993, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 09:39:47 DP1 TP0] Decode batch. #running-req: 1, #token: 1099, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 09:39:47 DP0 TP0] Decode batch. #running-req: 1, #token: 1033, token usage: 0.00, cuda graph: True, gen throughput (token/s): 67.79, #queue-req: 0, 
[2025-07-28 09:39:47 DP1 TP0] Decode batch. #running-req: 1, #token: 1139, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.18, #queue-req: 0, 
[2025-07-28 09:39:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1073, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.10, #queue-req: 0, 
[2025-07-28 09:39:48] INFO:     127.0.0.1:50482 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:39:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1179, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.17, #queue-req: 0, 
[2025-07-28 09:39:48 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 174, #cached-token: 86, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 09:39:49 DP1 TP0] Decode batch. #running-req: 2, #token: 1434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 123.26, #queue-req: 0, 
[2025-07-28 09:39:49] INFO:     127.0.0.1:50472 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:39:49 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:39:49 DP0 TP0] Decode batch. #running-req: 1, #token: 189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 30.44, #queue-req: 0, 
[2025-07-28 09:39:49 DP1 TP0] Decode batch. #running-req: 1, #token: 339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 74.79, #queue-req: 0, 
[2025-07-28 09:39:50 DP0 TP0] Decode batch. #running-req: 1, #token: 229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.26, #queue-req: 0, 
[2025-07-28 09:39:50 DP1 TP0] Decode batch. #running-req: 1, #token: 379, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.55, #queue-req: 0, 
[2025-07-28 09:39:50 DP0 TP0] Decode batch. #running-req: 1, #token: 269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.08, #queue-req: 0, 
[2025-07-28 09:39:50 DP1 TP0] Decode batch. #running-req: 1, #token: 419, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.38, #queue-req: 0, 
[2025-07-28 09:39:51 DP0 TP0] Decode batch. #running-req: 1, #token: 309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.68, #queue-req: 0, 
[2025-07-28 09:39:51 DP1 TP0] Decode batch. #running-req: 1, #token: 459, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 09:39:51 DP0 TP0] Decode batch. #running-req: 1, #token: 349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.60, #queue-req: 0, 
[2025-07-28 09:39:52 DP1 TP0] Decode batch. #running-req: 1, #token: 499, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 09:39:52 DP0 TP0] Decode batch. #running-req: 1, #token: 389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.45, #queue-req: 0, 
[2025-07-28 09:39:52 DP1 TP0] Decode batch. #running-req: 1, #token: 539, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:39:52 DP0 TP0] Decode batch. #running-req: 1, #token: 429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:39:53 DP1 TP0] Decode batch. #running-req: 1, #token: 579, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 09:39:53 DP0 TP0] Decode batch. #running-req: 1, #token: 469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:39:53] INFO:     127.0.0.1:56346 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:39:53 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 149, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:39:53 DP1 TP0] Decode batch. #running-req: 1, #token: 244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.93, #queue-req: 0, 
[2025-07-28 09:39:54 DP0 TP0] Decode batch. #running-req: 1, #token: 509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 09:39:54 DP1 TP0] Decode batch. #running-req: 1, #token: 284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.92, #queue-req: 0, 
[2025-07-28 09:39:54 DP0 TP0] Decode batch. #running-req: 1, #token: 549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:39:54 DP1 TP0] Decode batch. #running-req: 1, #token: 324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.74, #queue-req: 0, 
[2025-07-28 09:39:55 DP0 TP0] Decode batch. #running-req: 1, #token: 589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.20, #queue-req: 0, 
[2025-07-28 09:39:55 DP1 TP0] Decode batch. #running-req: 1, #token: 364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.62, #queue-req: 0, 
[2025-07-28 09:39:55 DP0 TP0] Decode batch. #running-req: 1, #token: 629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:39:56 DP1 TP0] Decode batch. #running-req: 1, #token: 404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.45, #queue-req: 0, 
[2025-07-28 09:39:56 DP0 TP0] Decode batch. #running-req: 1, #token: 669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:39:56 DP1 TP0] Decode batch. #running-req: 1, #token: 444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.35, #queue-req: 0, 
[2025-07-28 09:39:56] INFO:     127.0.0.1:56354 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:39:56 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 119, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:39:57 DP0 TP0] Decode batch. #running-req: 1, #token: 219, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.86, #queue-req: 0, 
[2025-07-28 09:39:57 DP1 TP0] Decode batch. #running-req: 1, #token: 484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 09:39:57 DP0 TP0] Decode batch. #running-req: 1, #token: 259, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.23, #queue-req: 0, 
[2025-07-28 09:39:57 DP1 TP0] Decode batch. #running-req: 1, #token: 524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:39:58 DP0 TP0] Decode batch. #running-req: 1, #token: 299, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.70, #queue-req: 0, 
[2025-07-28 09:39:58 DP1 TP0] Decode batch. #running-req: 1, #token: 564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:39:58 DP0 TP0] Decode batch. #running-req: 1, #token: 339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.67, #queue-req: 0, 
[2025-07-28 09:39:58 DP1 TP0] Decode batch. #running-req: 1, #token: 604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:39:59 DP0 TP0] Decode batch. #running-req: 1, #token: 379, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.51, #queue-req: 0, 
[2025-07-28 09:39:59 DP1 TP0] Decode batch. #running-req: 1, #token: 644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:39:59 DP0 TP0] Decode batch. #running-req: 1, #token: 419, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 09:40:00 DP1 TP0] Decode batch. #running-req: 1, #token: 684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:40:00 DP0 TP0] Decode batch. #running-req: 1, #token: 459, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:40:00 DP1 TP0] Decode batch. #running-req: 1, #token: 724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:40:01 DP0 TP0] Decode batch. #running-req: 1, #token: 499, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:40:01 DP1 TP0] Decode batch. #running-req: 1, #token: 764, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:40:01 DP0 TP0] Decode batch. #running-req: 1, #token: 539, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:40:01 DP1 TP0] Decode batch. #running-req: 1, #token: 804, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:40:02 DP0 TP0] Decode batch. #running-req: 1, #token: 579, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:40:02 DP1 TP0] Decode batch. #running-req: 1, #token: 844, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.27, #queue-req: 0, 
[2025-07-28 09:40:02] INFO:     127.0.0.1:54752 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:40:02 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 125, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:40:02 DP0 TP0] Decode batch. #running-req: 1, #token: 619, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:40:03 DP1 TP0] Decode batch. #running-req: 1, #token: 236, token usage: 0.00, cuda graph: True, gen throughput (token/s): 64.19, #queue-req: 0, 
[2025-07-28 09:40:03 DP0 TP0] Decode batch. #running-req: 1, #token: 659, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:40:03 DP1 TP0] Decode batch. #running-req: 1, #token: 276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.03, #queue-req: 0, 
[2025-07-28 09:40:03 DP0 TP0] Decode batch. #running-req: 1, #token: 699, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:40:04 DP1 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.74, #queue-req: 0, 
[2025-07-28 09:40:04 DP0 TP0] Decode batch. #running-req: 1, #token: 739, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:40:04 DP1 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.64, #queue-req: 0, 
[2025-07-28 09:40:05 DP0 TP0] Decode batch. #running-req: 1, #token: 779, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:40:05 DP1 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.50, #queue-req: 0, 
[2025-07-28 09:40:05 DP0 TP0] Decode batch. #running-req: 1, #token: 819, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:40:05 DP1 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 09:40:06 DP0 TP0] Decode batch. #running-req: 1, #token: 859, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:40:06 DP1 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 09:40:06 DP0 TP0] Decode batch. #running-req: 1, #token: 899, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:40:07 DP1 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 09:40:07 DP0 TP0] Decode batch. #running-req: 1, #token: 939, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:40:07 DP1 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 09:40:08 DP0 TP0] Decode batch. #running-req: 1, #token: 979, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:40:08 DP1 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:40:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1019, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:40:08 DP1 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:40:08] INFO:     127.0.0.1:54764 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:40:08 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 123, #cached-token: 90, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:40:09 DP0 TP0] Decode batch. #running-req: 1, #token: 235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.87, #queue-req: 0, 
[2025-07-28 09:40:09 DP1 TP0] Decode batch. #running-req: 1, #token: 676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:40:09 DP0 TP0] Decode batch. #running-req: 1, #token: 275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.99, #queue-req: 0, 
[2025-07-28 09:40:09 DP1 TP0] Decode batch. #running-req: 1, #token: 716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:40:10 DP0 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.67, #queue-req: 0, 
[2025-07-28 09:40:10 DP1 TP0] Decode batch. #running-req: 1, #token: 756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 09:40:10 DP0 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.59, #queue-req: 0, 
[2025-07-28 09:40:11 DP1 TP0] Decode batch. #running-req: 1, #token: 796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 09:40:11 DP0 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.42, #queue-req: 0, 
[2025-07-28 09:40:11 DP1 TP0] Decode batch. #running-req: 1, #token: 836, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 09:40:12 DP0 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:40:12 DP1 TP0] Decode batch. #running-req: 1, #token: 876, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 09:40:12 DP0 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:40:12 DP1 TP0] Decode batch. #running-req: 1, #token: 916, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:40:13 DP0 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 67.80, #queue-req: 0, 
[2025-07-28 09:40:13 DP1 TP0] Decode batch. #running-req: 1, #token: 956, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 09:40:13 DP0 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:40:14 DP1 TP0] Decode batch. #running-req: 1, #token: 996, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:40:14 DP0 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:40:14 DP1 TP0] Decode batch. #running-req: 1, #token: 1036, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:40:14] INFO:     127.0.0.1:40790 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:40:14 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 91, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:40:15 DP0 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:40:15 DP1 TP0] Decode batch. #running-req: 1, #token: 268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 64.36, #queue-req: 0, 
[2025-07-28 09:40:15 DP0 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:40:15 DP1 TP0] Decode batch. #running-req: 1, #token: 308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.75, #queue-req: 0, 
[2025-07-28 09:40:16 DP0 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:40:16 DP1 TP0] Decode batch. #running-req: 1, #token: 348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.68, #queue-req: 0, 
[2025-07-28 09:40:16 DP0 TP0] Decode batch. #running-req: 1, #token: 755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:40:16 DP1 TP0] Decode batch. #running-req: 1, #token: 388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.49, #queue-req: 0, 
[2025-07-28 09:40:17 DP0 TP0] Decode batch. #running-req: 1, #token: 795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:40:17 DP1 TP0] Decode batch. #running-req: 1, #token: 428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:40:17 DP0 TP0] Decode batch. #running-req: 1, #token: 835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:40:18 DP1 TP0] Decode batch. #running-req: 1, #token: 468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.34, #queue-req: 0, 
[2025-07-28 09:40:18] INFO:     127.0.0.1:40792 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:40:18 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 99, #cached-token: 90, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 09:40:18 DP0 TP0] Decode batch. #running-req: 1, #token: 204, token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.99, #queue-req: 0, 
[2025-07-28 09:40:18 DP1 TP0] Decode batch. #running-req: 1, #token: 508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:40:19 DP0 TP0] Decode batch. #running-req: 1, #token: 244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 70.25, #queue-req: 0, 
[2025-07-28 09:40:19 DP1 TP0] Decode batch. #running-req: 1, #token: 548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 09:40:19 DP0 TP0] Decode batch. #running-req: 1, #token: 284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.88, #queue-req: 0, 
[2025-07-28 09:40:19 DP1 TP0] Decode batch. #running-req: 1, #token: 588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.31, #queue-req: 0, 
[2025-07-28 09:40:20 DP0 TP0] Decode batch. #running-req: 1, #token: 324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.67, #queue-req: 0, 
[2025-07-28 09:40:20 DP1 TP0] Decode batch. #running-req: 1, #token: 628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.29, #queue-req: 0, 
[2025-07-28 09:40:20 DP0 TP0] Decode batch. #running-req: 1, #token: 364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.56, #queue-req: 0, 
[2025-07-28 09:40:20 DP1 TP0] Decode batch. #running-req: 1, #token: 668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 09:40:21 DP0 TP0] Decode batch. #running-req: 1, #token: 404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.38, #queue-req: 0, 
[2025-07-28 09:40:21 DP1 TP0] Decode batch. #running-req: 1, #token: 708, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:40:21 DP0 TP0] Decode batch. #running-req: 1, #token: 444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.25, #queue-req: 0, 
[2025-07-28 09:40:22 DP1 TP0] Decode batch. #running-req: 1, #token: 748, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:40:22 DP0 TP0] Decode batch. #running-req: 1, #token: 484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:40:22 DP1 TP0] Decode batch. #running-req: 1, #token: 788, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 09:40:23 DP0 TP0] Decode batch. #running-req: 1, #token: 524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.24, #queue-req: 0, 
[2025-07-28 09:40:23 DP1 TP0] Decode batch. #running-req: 1, #token: 828, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.28, #queue-req: 0, 
[2025-07-28 09:40:23 DP0 TP0] Decode batch. #running-req: 1, #token: 564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:40:23 DP1 TP0] Decode batch. #running-req: 1, #token: 868, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 09:40:24 DP0 TP0] Decode batch. #running-req: 1, #token: 604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:40:24 DP1 TP0] Decode batch. #running-req: 1, #token: 908, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.30, #queue-req: 0, 
[2025-07-28 09:40:24 DP0 TP0] Decode batch. #running-req: 1, #token: 644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:40:25 DP1 TP0] Decode batch. #running-req: 1, #token: 948, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.32, #queue-req: 0, 
[2025-07-28 09:40:25 DP0 TP0] Decode batch. #running-req: 1, #token: 684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:40:25 DP1 TP0] Decode batch. #running-req: 1, #token: 988, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.33, #queue-req: 0, 
[2025-07-28 09:40:26 DP0 TP0] Decode batch. #running-req: 1, #token: 724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:40:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1028, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.26, #queue-req: 0, 
[2025-07-28 09:40:26 DP0 TP0] Decode batch. #running-req: 1, #token: 764, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:40:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1068, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.15, #queue-req: 0, 
[2025-07-28 09:40:27 DP0 TP0] Decode batch. #running-req: 1, #token: 804, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.21, #queue-req: 0, 
[2025-07-28 09:40:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1108, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.16, #queue-req: 0, 
[2025-07-28 09:40:27 DP0 TP0] Decode batch. #running-req: 1, #token: 844, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:40:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1148, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:40:28 DP0 TP0] Decode batch. #running-req: 1, #token: 884, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:40:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1188, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:40:28 DP0 TP0] Decode batch. #running-req: 1, #token: 924, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.23, #queue-req: 0, 
[2025-07-28 09:40:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1228, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.18, #queue-req: 0, 
[2025-07-28 09:40:29 DP0 TP0] Decode batch. #running-req: 1, #token: 964, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.22, #queue-req: 0, 
[2025-07-28 09:40:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 69.19, #queue-req: 0, 
[2025-07-28 09:40:29] INFO:     127.0.0.1:38102 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:40:29 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 85, #cached-token: 90, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 09:40:30 DP1 TP0] Decode batch. #running-req: 2, #token: 1429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 112.32, #queue-req: 0, 
[2025-07-28 09:40:30 DP1 TP0] Decode batch. #running-req: 2, #token: 1509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.17, #queue-req: 0, 
[2025-07-28 09:40:31 DP1 TP0] Decode batch. #running-req: 2, #token: 1589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.15, #queue-req: 0, 
[2025-07-28 09:40:32 DP1 TP0] Decode batch. #running-req: 2, #token: 1669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.14, #queue-req: 0, 
[2025-07-28 09:40:32 DP1 TP0] Decode batch. #running-req: 2, #token: 1749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.13, #queue-req: 0, 
[2025-07-28 09:40:33 DP1 TP0] Decode batch. #running-req: 2, #token: 1829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.08, #queue-req: 0, 
[2025-07-28 09:40:33 DP1 TP0] Decode batch. #running-req: 2, #token: 1909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.01, #queue-req: 0, 
[2025-07-28 09:40:34 DP1 TP0] Decode batch. #running-req: 2, #token: 1989, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.93, #queue-req: 0, 
[2025-07-28 09:40:34 DP1 TP0] Decode batch. #running-req: 2, #token: 2069, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.97, #queue-req: 0, 
[2025-07-28 09:40:35 DP1 TP0] Decode batch. #running-req: 2, #token: 2149, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.87, #queue-req: 0, 
[2025-07-28 09:40:36 DP1 TP0] Decode batch. #running-req: 2, #token: 2229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.87, #queue-req: 0, 
[2025-07-28 09:40:36 DP1 TP0] Decode batch. #running-req: 2, #token: 2309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.85, #queue-req: 0, 
[2025-07-28 09:40:37 DP1 TP0] Decode batch. #running-req: 2, #token: 2389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.84, #queue-req: 0, 
[2025-07-28 09:40:37 DP1 TP0] Decode batch. #running-req: 2, #token: 2469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.85, #queue-req: 0, 
[2025-07-28 09:40:38 DP1 TP0] Decode batch. #running-req: 2, #token: 2549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.81, #queue-req: 0, 
[2025-07-28 09:40:39 DP1 TP0] Decode batch. #running-req: 2, #token: 2629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.80, #queue-req: 0, 
[2025-07-28 09:40:39 DP1 TP0] Decode batch. #running-req: 2, #token: 2709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.74, #queue-req: 0, 
[2025-07-28 09:40:40 DP1 TP0] Decode batch. #running-req: 2, #token: 2789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.71, #queue-req: 0, 
[2025-07-28 09:40:40 DP1 TP0] Decode batch. #running-req: 2, #token: 2869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.69, #queue-req: 0, 
[2025-07-28 09:40:41 DP1 TP0] Decode batch. #running-req: 2, #token: 2949, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.61, #queue-req: 0, 
[2025-07-28 09:40:41] INFO:     127.0.0.1:56684 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 09:40:42 DP1 TP0] Decode batch. #running-req: 1, #token: 2108, token usage: 0.00, cuda graph: True, gen throughput (token/s): 94.16, #queue-req: 0, 
[2025-07-28 09:40:42 DP1 TP0] Decode batch. #running-req: 1, #token: 2148, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.92, #queue-req: 0, 
[2025-07-28 09:40:43 DP1 TP0] Decode batch. #running-req: 1, #token: 2188, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.95, #queue-req: 0, 
[2025-07-28 09:40:43 DP1 TP0] Decode batch. #running-req: 1, #token: 2228, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.95, #queue-req: 0, 
[2025-07-28 09:40:44 DP1 TP0] Decode batch. #running-req: 1, #token: 2268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.93, #queue-req: 0, 
[2025-07-28 09:40:44 DP1 TP0] Decode batch. #running-req: 1, #token: 2308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 68.92, #queue-req: 0, 
[2025-07-28 09:40:45] INFO:     127.0.0.1:38086 - "POST /v1/chat/completions HTTP/1.1" 200 OK

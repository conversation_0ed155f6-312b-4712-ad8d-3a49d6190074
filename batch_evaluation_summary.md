# OpenAI Batch API Evaluation Setup

## Overview
This document summarizes the evaluation setup for the `merged_output_final.jsonl` file, which has been prepared for submission to the OpenAI Batch API for automated scoring of medical question responses.

## Files Created

### 1. `evaluation_prompt.txt`
Contains the expert medical evaluator prompt that instructs the LLM to:
- Score responses on a 0-10 scale based on accuracy, completeness, clinical reasoning, and clarity
- Provide detailed reasoning before giving the final score
- Use the standardized format "Score: X/10" for easy extraction

### 2. `format_for_batch_api.py`
Python script that processes the original JSONL file and creates OpenAI Batch API requests:
- Extracts model answers from the full response text (includes thinking process)
- Creates properly formatted batch requests with unique custom_ids
- Uses GPT-4.1-2025-04-14 model for evaluation
- Sets appropriate parameters (max_tokens: 2048, temperature: 0.1, n: 3)
- **NEW**: Supports filtering by specific models using `--models` parameter
- **NEW**: Can list available models in dataset using `--list-models`

### 3. `batch_evaluation_requests.jsonl`
The final output file containing **1,728 batch requests** ready for OpenAI Batch API submission.

## Data Processing Summary

- **Input**: `merged_output_final.jsonl` (1,729 lines, 1 line was skipped due to processing)
- **Output**: `batch_evaluation_requests.jsonl` (1,728 valid batch requests for all models)
- **Available Models**: 27 different models in the dataset
- **Format**: Each request follows the exact OpenAI Batch API specification
- **Custom IDs**: Format `eval-{original_id}` for easy tracking
- **Model Filtering**: Can filter to evaluate specific models only

## Sample Request Structure

```json
{
  "custom_id": "eval-95891ae5-2289-52e2-ace0-823b1dfb435f",
  "method": "POST",
  "url": "/v1/chat/completions",
  "body": {
    "model": "gpt-4.1-2025-04-14",
    "messages": [
      {
        "role": "system",
        "content": "[Evaluation prompt with scoring criteria]"
      },
      {
        "role": "user",
        "content": "Question: [Medical scenario]\n\nTarget Answer: [Correct answer]\n\nModel Response: [Full model response including thinking]"
      }
    ],
    "max_tokens": 2048,
    "temperature": 0.1,
    "n": 3
  }
}
```

## Usage Examples

### List Available Models
```bash
python3 format_for_batch_api.py --list-models
```

### Evaluate All Models (Default)
```bash
python3 format_for_batch_api.py
```

### Evaluate Specific Models Only
```bash
python3 format_for_batch_api.py --models "DeepSeek-R1-Distill-Llama-8B" "gpt-4o-2024-11-20" --output selected_models_batch.jsonl
```

### Available Models in Dataset
- DeepSeek-R1-Distill-Llama-8B
- DeepSeek-R1-Distill-Qwen-1.5B, 7B, 14B, 32B
- Qwen2.5-1.5B, 3B, 7B, 14B, 32B, 72B-Instruct
- Qwen3-0.6B, 1.7B, 4B, 8B, 14B, 30B-A3B
- claude-3-7-sonnet-20250219
- deepseek-r1, deepseek-v3
- gemini-2.5-flash, gemini-2.5-pro
- gemma-3-1b, 4b, 12b, 27b-it
- gpt-4o-2024-11-20

## Next Steps for OpenAI Batch API Submission

### 1. Upload the Batch File
```bash
curl https://api.openai.com/v1/files \
  -H "Authorization: Bearer $OPENAI_API_KEY" \
  -F "purpose=batch" \
  -F "file=@batch_evaluation_requests.jsonl"
```

### 2. Create the Batch Job
```bash
curl https://api.openai.com/v1/batches \
  -H "Authorization: Bearer $OPENAI_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "input_file_id": "file-abc123",
    "endpoint": "/v1/chat/completions",
    "completion_window": "24h"
  }'
```

### 3. Monitor Batch Status
```bash
curl https://api.openai.com/v1/batches/$BATCH_ID \
  -H "Authorization: Bearer $OPENAI_API_KEY"
```

### 4. Download Results
Once completed, download the output file and process the scores.

## Expected Output Format

Each evaluation response will contain:
1. **Reasoning**: Detailed justification for the score
2. **Comparison**: Analysis of model response vs. target answer
3. **Strengths/Weaknesses**: Identified in the response
4. **Final Score**: In format "Score: X/10"

## Cost Estimation

- **Model**: GPT-4.1-2025-04-14
- **Requests**: 1,728 (all models) or fewer if filtered
- **Estimated tokens per request**: ~1200-1500 (input) + ~400-600 (output) × 3 responses
- **Estimated cost**: ~$15-25 USD for all models (based on current pricing)
- **Cost for filtered models**: Proportionally less (e.g., 2 models = ~$2-3 USD)

## Quality Assurance

- ✅ All JSON requests are valid format
- ✅ Custom IDs are unique and traceable
- ✅ Evaluation prompt includes clear scoring criteria
- ✅ Model responses are properly extracted
- ✅ Target answers are correctly included
- ✅ Ready for immediate batch submission

## Files Ready for Submission

- `batch_evaluation_requests.jsonl` - **Ready for OpenAI Batch API**
- `evaluation_prompt.txt` - Reference for the evaluation criteria
- `format_for_batch_api.py` - Script for future processing if needed

**Status**: ✅ Ready for OpenAI Batch API submission

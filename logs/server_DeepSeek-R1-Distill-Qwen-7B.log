[2025-07-28 01:41:55] server_args=ServerArgs(model_path='/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Qwen-7B', tokenizer_path='/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Qwen-7B', tokenizer_mode='auto', skip_tokenizer_init=False, load_format='auto', model_loader_extra_config='{}', trust_remote_code=False, context_length=32768, is_embedding=False, enable_multimodal=None, revision=None, model_impl='auto', host='127.0.0.1', port=8003, skip_server_warmup=False, warmups=None, nccl_port=None, dtype='auto', quantization=None, quantization_param_path=None, kv_cache_dtype='auto', mem_fraction_static=0.85, max_running_requests=None, max_total_tokens=None, chunked_prefill_size=8192, max_prefill_tokens=16384, schedule_policy='fcfs', schedule_conservativeness=1.0, cpu_offload_gb=0, page_size=1, hybrid_kvcache_ratio=None, swa_full_tokens_ratio=0.8, disable_hybrid_swa_memory=False, device='cuda', tp_size=4, pp_size=1, max_micro_batch_size=None, stream_interval=1, stream_output=False, random_seed=969074755, constrained_json_whitespace_pattern=None, watchdog_timeout=300, dist_timeout=None, download_dir=None, base_gpu_id=0, gpu_id_step=1, sleep_on_idle=False, log_level='info', log_level_http=None, log_requests=False, log_requests_level=0, crash_dump_folder=None, show_time_cost=True, enable_metrics=True, enable_metrics_for_all_schedulers=False, bucket_time_to_first_token=None, bucket_inter_token_latency=None, bucket_e2e_request_latency=None, collect_tokens_histogram=False, decode_log_interval=40, enable_request_time_stats_logging=False, kv_events_config=None, api_key=None, served_model_name='/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Qwen-7B', chat_template=None, completion_template=None, file_storage_path='sglang_storage', enable_cache_report=False, reasoning_parser=None, tool_call_parser=None, dp_size=2, load_balance_method='round_robin', dist_init_addr=None, nnodes=1, node_rank=0, json_model_override_args='{}', preferred_sampling_params=None, enable_lora=None, max_lora_rank=None, lora_target_modules=None, lora_paths=None, max_loras_per_batch=8, lora_backend='triton', attention_backend=None, sampling_backend='flashinfer', grammar_backend='xgrammar', mm_attention_backend=None, speculative_algorithm=None, speculative_draft_model_path=None, speculative_num_steps=None, speculative_eagle_topk=None, speculative_num_draft_tokens=None, speculative_accept_threshold_single=1.0, speculative_accept_threshold_acc=1.0, speculative_token_map=None, ep_size=1, enable_ep_moe=False, enable_deepep_moe=False, enable_flashinfer_moe=False, enable_flashinfer_allreduce_fusion=False, deepep_mode='auto', ep_num_redundant_experts=0, ep_dispatch_algorithm='static', init_expert_location='trivial', enable_eplb=False, eplb_algorithm='auto', eplb_rebalance_num_iterations=1000, eplb_rebalance_layers_per_chunk=None, expert_distribution_recorder_mode=None, expert_distribution_recorder_buffer_size=1000, enable_expert_distribution_metrics=False, deepep_config=None, moe_dense_tp_size=None, enable_hierarchical_cache=False, hicache_ratio=2.0, hicache_size=0, hicache_write_policy='write_through_selective', hicache_io_backend='', hicache_storage_backend=None, enable_double_sparsity=False, ds_channel_config_path=None, ds_heavy_channel_num=32, ds_heavy_token_num=256, ds_heavy_channel_type='qk', ds_sparse_decode_threshold=4096, disable_radix_cache=False, cuda_graph_max_bs=None, cuda_graph_bs=None, disable_cuda_graph=False, disable_cuda_graph_padding=False, enable_profile_cuda_graph=False, enable_nccl_nvls=False, enable_tokenizer_batch_encode=False, disable_outlines_disk_cache=False, disable_custom_all_reduce=False, enable_mscclpp=False, disable_overlap_schedule=False, enable_mixed_chunk=False, enable_dp_attention=False, enable_dp_lm_head=False, enable_two_batch_overlap=False, enable_torch_compile=False, torch_compile_max_bs=32, torchao_config='', enable_nan_detection=False, enable_p2p_check=False, triton_attention_reduce_in_fp32=False, triton_attention_num_kv_splits=8, num_continuous_decode_steps=1, delete_ckpt_after_loading=False, enable_memory_saver=False, allow_auto_truncate=False, enable_custom_logit_processor=False, flashinfer_mla_disable_ragged=False, disable_shared_experts_fusion=False, disable_chunked_prefix_cache=False, disable_fast_image_processor=False, enable_return_hidden_states=False, enable_triton_kernel_moe=False, debug_tensor_dump_output_folder=None, debug_tensor_dump_input_file=None, debug_tensor_dump_inject=False, debug_tensor_dump_prefill_only=False, disaggregation_mode='null', disaggregation_transfer_backend='mooncake', disaggregation_bootstrap_port=8998, disaggregation_decode_tp=None, disaggregation_decode_dp=None, disaggregation_prefill_pp=1, disaggregation_ib_device=None, num_reserved_decode_tokens=512, pdlb_url=None, custom_weight_loader=[], weight_loader_disable_mmap=False, enable_pdmux=False, sm_group_num=3)
[2025-07-28 01:42:01] Launch DP0 starting at GPU #0.
[2025-07-28 01:42:01] Launch DP1 starting at GPU #4.
[2025-07-28 01:42:09 DP0 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 01:42:09 DP0 TP0] Init torch distributed begin.
[2025-07-28 01:42:09 DP1 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 01:42:09 DP1 TP0] Init torch distributed begin.
[2025-07-28 01:42:10 DP0 TP0] sglang is using nccl==2.26.2
[2025-07-28 01:42:10 DP1 TP0] sglang is using nccl==2.26.2
[2025-07-28 01:42:12 DP0 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 01:42:12 DP1 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 01:42:12 DP0 TP0] Load weight begin. avail mem=78.21 GB

Loading safetensors checkpoint shards:   0% Completed | 0/2 [00:00<?, ?it/s]
[2025-07-28 01:42:12 DP1 TP0] Load weight begin. avail mem=78.21 GB

Loading safetensors checkpoint shards:   0% Completed | 0/2 [00:00<?, ?it/s]

Loading safetensors checkpoint shards:  50% Completed | 1/2 [00:02<00:02,  2.35s/it]

Loading safetensors checkpoint shards:  50% Completed | 1/2 [00:02<00:02,  2.24s/it]

Loading safetensors checkpoint shards: 100% Completed | 2/2 [00:05<00:00,  2.78s/it]

Loading safetensors checkpoint shards: 100% Completed | 2/2 [00:05<00:00,  2.71s/it]


Loading safetensors checkpoint shards: 100% Completed | 2/2 [00:05<00:00,  2.74s/it]

Loading safetensors checkpoint shards: 100% Completed | 2/2 [00:05<00:00,  2.66s/it]

[2025-07-28 01:42:18 DP0 TP0] Load weight end. type=Qwen2ForCausalLM, dtype=torch.bfloat16, avail mem=74.38 GB, mem usage=3.83 GB.
[2025-07-28 01:42:18 DP1 TP0] Load weight end. type=Qwen2ForCausalLM, dtype=torch.bfloat16, avail mem=74.38 GB, mem usage=3.83 GB.
[2025-07-28 01:42:18 DP1 TP3] KV Cache is allocated. #tokens: 4686164, K size: 31.28 GB, V size: 31.28 GB
[2025-07-28 01:42:18 DP1 TP1] KV Cache is allocated. #tokens: 4686164, K size: 31.28 GB, V size: 31.28 GB
[2025-07-28 01:42:18 DP1 TP2] KV Cache is allocated. #tokens: 4686164, K size: 31.28 GB, V size: 31.28 GB
[2025-07-28 01:42:18 DP0 TP0] KV Cache is allocated. #tokens: 4686164, K size: 31.28 GB, V size: 31.28 GB
[2025-07-28 01:42:18 DP0 TP3] KV Cache is allocated. #tokens: 4686164, K size: 31.28 GB, V size: 31.28 GB
[2025-07-28 01:42:18 DP0 TP1] KV Cache is allocated. #tokens: 4686164, K size: 31.28 GB, V size: 31.28 GB
[2025-07-28 01:42:18 DP0 TP2] KV Cache is allocated. #tokens: 4686164, K size: 31.28 GB, V size: 31.28 GB
[2025-07-28 01:42:18 DP0 TP0] Memory pool end. avail mem=11.17 GB
[2025-07-28 01:42:18 DP1 TP0] KV Cache is allocated. #tokens: 4686164, K size: 31.28 GB, V size: 31.28 GB
[2025-07-28 01:42:18 DP1 TP0] Memory pool end. avail mem=11.17 GB
[2025-07-28 01:42:19 DP0 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.55 GB
[2025-07-28 01:42:19 DP1 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.55 GB
[2025-07-28 01:42:19 DP0 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.53 GB):   0%|          | 0/23 [00:00<?, ?it/s][2025-07-28 01:42:19 DP1 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.53 GB):   0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.53 GB):   4%|▍         | 1/23 [00:01<00:25,  1.17s/it]
Capturing batches (bs=152 avail_mem=10.28 GB):   4%|▍         | 1/23 [00:01<00:25,  1.17s/it]
Capturing batches (bs=160 avail_mem=10.53 GB):   4%|▍         | 1/23 [00:01<00:27,  1.23s/it]
Capturing batches (bs=152 avail_mem=10.28 GB):   4%|▍         | 1/23 [00:01<00:27,  1.23s/it]
Capturing batches (bs=152 avail_mem=10.28 GB):   9%|▊         | 2/23 [00:01<00:14,  1.46it/s]
Capturing batches (bs=144 avail_mem=10.17 GB):   9%|▊         | 2/23 [00:01<00:14,  1.46it/s]
Capturing batches (bs=152 avail_mem=10.28 GB):   9%|▊         | 2/23 [00:01<00:15,  1.38it/s]
Capturing batches (bs=144 avail_mem=10.17 GB):   9%|▊         | 2/23 [00:01<00:15,  1.38it/s]
Capturing batches (bs=144 avail_mem=10.17 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.69it/s]
Capturing batches (bs=136 avail_mem=10.07 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.69it/s]
Capturing batches (bs=144 avail_mem=10.17 GB):  13%|█▎        | 3/23 [00:02<00:12,  1.61it/s]
Capturing batches (bs=136 avail_mem=10.07 GB):  13%|█▎        | 3/23 [00:02<00:12,  1.61it/s]
Capturing batches (bs=136 avail_mem=10.07 GB):  17%|█▋        | 4/23 [00:02<00:08,  2.13it/s]
Capturing batches (bs=128 avail_mem=9.97 GB):  17%|█▋        | 4/23 [00:02<00:08,  2.13it/s] 
Capturing batches (bs=136 avail_mem=10.07 GB):  17%|█▋        | 4/23 [00:02<00:09,  2.02it/s]
Capturing batches (bs=128 avail_mem=9.97 GB):  17%|█▋        | 4/23 [00:02<00:09,  2.02it/s] 
Capturing batches (bs=128 avail_mem=9.97 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.48it/s]
Capturing batches (bs=120 avail_mem=9.89 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.48it/s]
Capturing batches (bs=128 avail_mem=9.97 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.34it/s]
Capturing batches (bs=120 avail_mem=9.89 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.34it/s]
Capturing batches (bs=120 avail_mem=9.89 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.77it/s]
Capturing batches (bs=112 avail_mem=9.79 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.77it/s]
Capturing batches (bs=120 avail_mem=9.89 GB):  26%|██▌       | 6/23 [00:03<00:06,  2.61it/s]
Capturing batches (bs=112 avail_mem=9.79 GB):  26%|██▌       | 6/23 [00:03<00:06,  2.61it/s]
Capturing batches (bs=112 avail_mem=9.79 GB):  30%|███       | 7/23 [00:03<00:05,  2.99it/s]
Capturing batches (bs=104 avail_mem=9.72 GB):  30%|███       | 7/23 [00:03<00:05,  2.99it/s]
Capturing batches (bs=112 avail_mem=9.79 GB):  30%|███       | 7/23 [00:03<00:05,  2.81it/s]
Capturing batches (bs=104 avail_mem=9.72 GB):  30%|███       | 7/23 [00:03<00:05,  2.81it/s]
Capturing batches (bs=104 avail_mem=9.72 GB):  35%|███▍      | 8/23 [00:03<00:04,  3.15it/s]
Capturing batches (bs=96 avail_mem=9.64 GB):  35%|███▍      | 8/23 [00:03<00:04,  3.15it/s] 
Capturing batches (bs=104 avail_mem=9.72 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.96it/s]
Capturing batches (bs=96 avail_mem=9.64 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.96it/s] 
Capturing batches (bs=96 avail_mem=9.64 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.28it/s]
Capturing batches (bs=88 avail_mem=9.58 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.28it/s]
Capturing batches (bs=96 avail_mem=9.64 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.07it/s]
Capturing batches (bs=88 avail_mem=9.58 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.07it/s]
Capturing batches (bs=88 avail_mem=9.58 GB):  43%|████▎     | 10/23 [00:03<00:03,  3.38it/s]
Capturing batches (bs=80 avail_mem=9.50 GB):  43%|████▎     | 10/23 [00:03<00:03,  3.38it/s]
Capturing batches (bs=88 avail_mem=9.58 GB):  43%|████▎     | 10/23 [00:04<00:04,  3.16it/s]
Capturing batches (bs=80 avail_mem=9.50 GB):  43%|████▎     | 10/23 [00:04<00:04,  3.16it/s]
Capturing batches (bs=80 avail_mem=9.50 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.45it/s]
Capturing batches (bs=72 avail_mem=9.50 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.45it/s]
Capturing batches (bs=80 avail_mem=9.50 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.22it/s]
Capturing batches (bs=72 avail_mem=9.50 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.22it/s]
Capturing batches (bs=72 avail_mem=9.50 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.49it/s]
Capturing batches (bs=64 avail_mem=9.43 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.49it/s]
Capturing batches (bs=72 avail_mem=9.50 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.26it/s]
Capturing batches (bs=64 avail_mem=9.43 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.26it/s]
Capturing batches (bs=64 avail_mem=9.43 GB):  57%|█████▋    | 13/23 [00:04<00:02,  3.52it/s]
Capturing batches (bs=56 avail_mem=9.39 GB):  57%|█████▋    | 13/23 [00:04<00:02,  3.52it/s]
Capturing batches (bs=64 avail_mem=9.43 GB):  57%|█████▋    | 13/23 [00:05<00:03,  3.29it/s]
Capturing batches (bs=56 avail_mem=9.39 GB):  57%|█████▋    | 13/23 [00:05<00:03,  3.29it/s]
Capturing batches (bs=56 avail_mem=9.39 GB):  61%|██████    | 14/23 [00:05<00:02,  3.49it/s]
Capturing batches (bs=48 avail_mem=9.33 GB):  61%|██████    | 14/23 [00:05<00:02,  3.49it/s]
Capturing batches (bs=48 avail_mem=9.33 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.65it/s]
Capturing batches (bs=40 avail_mem=9.33 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.65it/s]
Capturing batches (bs=56 avail_mem=9.39 GB):  61%|██████    | 14/23 [00:05<00:02,  3.32it/s]
Capturing batches (bs=48 avail_mem=9.33 GB):  61%|██████    | 14/23 [00:05<00:02,  3.32it/s]
Capturing batches (bs=40 avail_mem=9.33 GB):  70%|██████▉   | 16/23 [00:05<00:01,  3.66it/s]
Capturing batches (bs=32 avail_mem=9.28 GB):  70%|██████▉   | 16/23 [00:05<00:01,  3.66it/s]
Capturing batches (bs=48 avail_mem=9.33 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.34it/s]
Capturing batches (bs=40 avail_mem=9.33 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.34it/s]
Capturing batches (bs=32 avail_mem=9.28 GB):  74%|███████▍  | 17/23 [00:05<00:01,  3.58it/s]
Capturing batches (bs=24 avail_mem=9.27 GB):  74%|███████▍  | 17/23 [00:05<00:01,  3.58it/s]
Capturing batches (bs=40 avail_mem=9.33 GB):  70%|██████▉   | 16/23 [00:05<00:02,  3.36it/s]
Capturing batches (bs=32 avail_mem=9.28 GB):  70%|██████▉   | 16/23 [00:05<00:02,  3.36it/s]
Capturing batches (bs=24 avail_mem=9.27 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.59it/s]
Capturing batches (bs=16 avail_mem=9.24 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.59it/s]
Capturing batches (bs=32 avail_mem=9.28 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.32it/s]
Capturing batches (bs=24 avail_mem=9.27 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.32it/s]
Capturing batches (bs=16 avail_mem=9.24 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.60it/s]
Capturing batches (bs=8 avail_mem=9.23 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.60it/s] 
Capturing batches (bs=24 avail_mem=9.27 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.34it/s]
Capturing batches (bs=16 avail_mem=9.24 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.34it/s]
Capturing batches (bs=8 avail_mem=9.23 GB):  87%|████████▋ | 20/23 [00:06<00:00,  3.62it/s]
Capturing batches (bs=4 avail_mem=9.21 GB):  87%|████████▋ | 20/23 [00:06<00:00,  3.62it/s]
Capturing batches (bs=16 avail_mem=9.24 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.33it/s]
Capturing batches (bs=8 avail_mem=9.23 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.33it/s] 
Capturing batches (bs=4 avail_mem=9.21 GB):  91%|█████████▏| 21/23 [00:06<00:00,  3.64it/s]
Capturing batches (bs=2 avail_mem=9.20 GB):  91%|█████████▏| 21/23 [00:06<00:00,  3.64it/s]
Capturing batches (bs=8 avail_mem=9.23 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.35it/s]
Capturing batches (bs=4 avail_mem=9.21 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.35it/s]
Capturing batches (bs=2 avail_mem=9.20 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.65it/s]
Capturing batches (bs=1 avail_mem=9.18 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.65it/s]
Capturing batches (bs=4 avail_mem=9.21 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.35it/s]
Capturing batches (bs=2 avail_mem=9.20 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.35it/s]
Capturing batches (bs=1 avail_mem=9.18 GB): 100%|██████████| 23/23 [00:07<00:00,  3.62it/s]
Capturing batches (bs=1 avail_mem=9.18 GB): 100%|██████████| 23/23 [00:07<00:00,  3.05it/s]
[2025-07-28 01:42:26 DP1 TP0] Registering 1311 cuda graph addresses
[2025-07-28 01:42:26 DP1 TP1] Registering 1311 cuda graph addresses
[2025-07-28 01:42:26 DP1 TP2] Registering 1311 cuda graph addresses
[2025-07-28 01:42:26 DP1 TP3] Registering 1311 cuda graph addresses
[2025-07-28 01:42:26 DP1 TP0] Capture cuda graph end. Time elapsed: 7.77 s. mem usage=1.38 GB. avail mem=9.17 GB.

Capturing batches (bs=2 avail_mem=9.20 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.38it/s]
Capturing batches (bs=1 avail_mem=9.18 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.38it/s][2025-07-28 01:42:27 DP1 TP0] max_total_num_tokens=4686164, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=9.17 GB
[2025-07-28 01:42:27 DP0 TP3] Registering 1311 cuda graph addresses

Capturing batches (bs=1 avail_mem=9.18 GB): 100%|██████████| 23/23 [00:08<00:00,  3.37it/s]
Capturing batches (bs=1 avail_mem=9.18 GB): 100%|██████████| 23/23 [00:08<00:00,  2.85it/s]
[2025-07-28 01:42:27 DP0 TP0] Registering 1311 cuda graph addresses
[2025-07-28 01:42:27 DP0 TP1] Registering 1311 cuda graph addresses
[2025-07-28 01:42:27 DP0 TP2] Registering 1311 cuda graph addresses
[2025-07-28 01:42:27 DP0 TP0] Capture cuda graph end. Time elapsed: 8.29 s. mem usage=1.38 GB. avail mem=9.17 GB.
[2025-07-28 01:42:27 DP0 TP0] max_total_num_tokens=4686164, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=9.17 GB
[2025-07-28 01:42:28] INFO:     Started server process [1873927]
[2025-07-28 01:42:28] INFO:     Waiting for application startup.
[2025-07-28 01:42:28] INFO:     Application startup complete.
[2025-07-28 01:42:28] INFO:     Uvicorn running on http://127.0.0.1:8003 (Press CTRL+C to quit)
[2025-07-28 01:42:28] INFO:     127.0.0.1:46904 - "GET /health HTTP/1.1" 200 OK
[2025-07-28 01:42:28 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 230, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:42:28 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 189, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:42:29] INFO:     127.0.0.1:46938 - "GET /get_model_info HTTP/1.1" 200 OK
[2025-07-28 01:42:29 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 1, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:42:29 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 1, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:42:29 DP0 TP0] Decode batch. #running-req: 1, #token: 271, token usage: 0.00, cuda graph: True, gen throughput (token/s): 22.69, #queue-req: 0, 
[2025-07-28 01:42:29] INFO:     127.0.0.1:46950 - "POST /generate HTTP/1.1" 200 OK
[2025-07-28 01:42:29] The server is fired up and ready to roll!
[2025-07-28 01:42:29 DP0 TP0] Decode batch. #running-req: 1, #token: 311, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.96, #queue-req: 0, 
[2025-07-28 01:42:29 DP1 TP0] Decode batch. #running-req: 1, #token: 230, token usage: 0.00, cuda graph: True, gen throughput (token/s): 17.00, #queue-req: 0, 
[2025-07-28 01:42:30 DP0 TP0] Decode batch. #running-req: 1, #token: 351, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.08, #queue-req: 0, 
[2025-07-28 01:42:30 DP1 TP0] Decode batch. #running-req: 1, #token: 270, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.34, #queue-req: 0, 
[2025-07-28 01:42:30 DP0 TP0] Decode batch. #running-req: 1, #token: 391, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.90, #queue-req: 0, 
[2025-07-28 01:42:30 DP1 TP0] Decode batch. #running-req: 1, #token: 310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.37, #queue-req: 0, 
[2025-07-28 01:42:30 DP0 TP0] Decode batch. #running-req: 1, #token: 431, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.49, #queue-req: 0, 
[2025-07-28 01:42:30 DP1 TP0] Decode batch. #running-req: 1, #token: 350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.11, #queue-req: 0, 
[2025-07-28 01:42:30 DP0 TP0] Decode batch. #running-req: 1, #token: 471, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.60, #queue-req: 0, 
[2025-07-28 01:42:30 DP1 TP0] Decode batch. #running-req: 1, #token: 390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.92, #queue-req: 0, 
[2025-07-28 01:42:30 DP0 TP0] Decode batch. #running-req: 1, #token: 511, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.63, #queue-req: 0, 
[2025-07-28 01:42:30 DP1 TP0] Decode batch. #running-req: 1, #token: 430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.53, #queue-req: 0, 
[2025-07-28 01:42:30 DP0 TP0] Decode batch. #running-req: 1, #token: 551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.59, #queue-req: 0, 
[2025-07-28 01:42:30 DP1 TP0] Decode batch. #running-req: 1, #token: 470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.77, #queue-req: 0, 
[2025-07-28 01:42:31 DP0 TP0] Decode batch. #running-req: 1, #token: 591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.48, #queue-req: 0, 
[2025-07-28 01:42:31 DP1 TP0] Decode batch. #running-req: 1, #token: 510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.23, #queue-req: 0, 
[2025-07-28 01:42:31 DP0 TP0] Decode batch. #running-req: 1, #token: 631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.38, #queue-req: 0, 
[2025-07-28 01:42:31 DP1 TP0] Decode batch. #running-req: 1, #token: 550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.27, #queue-req: 0, 
[2025-07-28 01:42:31 DP0 TP0] Decode batch. #running-req: 1, #token: 671, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.04, #queue-req: 0, 
[2025-07-28 01:42:31 DP1 TP0] Decode batch. #running-req: 1, #token: 590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.65, #queue-req: 0, 
[2025-07-28 01:42:31 DP0 TP0] Decode batch. #running-req: 1, #token: 711, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.96, #queue-req: 0, 
[2025-07-28 01:42:31 DP1 TP0] Decode batch. #running-req: 1, #token: 630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.75, #queue-req: 0, 
[2025-07-28 01:42:31 DP0 TP0] Decode batch. #running-req: 1, #token: 751, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.43, #queue-req: 0, 
[2025-07-28 01:42:31 DP1 TP0] Decode batch. #running-req: 1, #token: 670, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.36, #queue-req: 0, 
[2025-07-28 01:42:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.15, #queue-req: 0, 
[2025-07-28 01:42:31] INFO:     127.0.0.1:46916 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:42:31 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 80, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:42:31 DP1 TP0] Decode batch. #running-req: 1, #token: 710, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.52, #queue-req: 0, 
[2025-07-28 01:42:32] INFO:     127.0.0.1:46924 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:42:32 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 203, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:42:32 DP0 TP0] Decode batch. #running-req: 1, #token: 204, token usage: 0.00, cuda graph: True, gen throughput (token/s): 186.79, #queue-req: 0, 
[2025-07-28 01:42:32 DP1 TP0] Decode batch. #running-req: 1, #token: 314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 184.46, #queue-req: 0, 
[2025-07-28 01:42:32 DP0 TP0] Decode batch. #running-req: 1, #token: 244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.95, #queue-req: 0, 
[2025-07-28 01:42:32 DP1 TP0] Decode batch. #running-req: 1, #token: 354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.18, #queue-req: 0, 
[2025-07-28 01:42:32 DP0 TP0] Decode batch. #running-req: 1, #token: 284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.87, #queue-req: 0, 
[2025-07-28 01:42:32 DP1 TP0] Decode batch. #running-req: 1, #token: 394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.06, #queue-req: 0, 
[2025-07-28 01:42:32 DP0 TP0] Decode batch. #running-req: 1, #token: 324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.73, #queue-req: 0, 
[2025-07-28 01:42:32 DP1 TP0] Decode batch. #running-req: 1, #token: 434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.92, #queue-req: 0, 
[2025-07-28 01:42:32 DP0 TP0] Decode batch. #running-req: 1, #token: 364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.97, #queue-req: 0, 
[2025-07-28 01:42:32 DP1 TP0] Decode batch. #running-req: 1, #token: 474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.43, #queue-req: 0, 
[2025-07-28 01:42:32 DP0 TP0] Decode batch. #running-req: 1, #token: 404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.87, #queue-req: 0, 
[2025-07-28 01:42:32 DP1 TP0] Decode batch. #running-req: 1, #token: 514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.91, #queue-req: 0, 
[2025-07-28 01:42:33 DP0 TP0] Decode batch. #running-req: 1, #token: 444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.03, #queue-req: 0, 
[2025-07-28 01:42:33 DP1 TP0] Decode batch. #running-req: 1, #token: 554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.88, #queue-req: 0, 
[2025-07-28 01:42:33 DP0 TP0] Decode batch. #running-req: 1, #token: 484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.72, #queue-req: 0, 
[2025-07-28 01:42:33 DP1 TP0] Decode batch. #running-req: 1, #token: 594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.97, #queue-req: 0, 
[2025-07-28 01:42:33 DP0 TP0] Decode batch. #running-req: 1, #token: 524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.78, #queue-req: 0, 
[2025-07-28 01:42:33 DP1 TP0] Decode batch. #running-req: 1, #token: 634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.74, #queue-req: 0, 
[2025-07-28 01:42:33 DP0 TP0] Decode batch. #running-req: 1, #token: 564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.19, #queue-req: 0, 
[2025-07-28 01:42:33 DP1 TP0] Decode batch. #running-req: 1, #token: 674, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.14, #queue-req: 0, 
[2025-07-28 01:42:33 DP0 TP0] Decode batch. #running-req: 1, #token: 604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.91, #queue-req: 0, 
[2025-07-28 01:42:33 DP1 TP0] Decode batch. #running-req: 1, #token: 714, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.12, #queue-req: 0, 
[2025-07-28 01:42:33 DP0 TP0] Decode batch. #running-req: 1, #token: 644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.12, #queue-req: 0, 
[2025-07-28 01:42:33 DP1 TP0] Decode batch. #running-req: 1, #token: 754, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.95, #queue-req: 0, 
[2025-07-28 01:42:34 DP0 TP0] Decode batch. #running-req: 1, #token: 684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.84, #queue-req: 0, 
[2025-07-28 01:42:34 DP1 TP0] Decode batch. #running-req: 1, #token: 794, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.18, #queue-req: 0, 
[2025-07-28 01:42:34 DP0 TP0] Decode batch. #running-req: 1, #token: 724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.52, #queue-req: 0, 
[2025-07-28 01:42:34 DP1 TP0] Decode batch. #running-req: 1, #token: 834, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.80, #queue-req: 0, 
[2025-07-28 01:42:34 DP0 TP0] Decode batch. #running-req: 1, #token: 764, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.40, #queue-req: 0, 
[2025-07-28 01:42:34 DP1 TP0] Decode batch. #running-req: 1, #token: 874, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.46, #queue-req: 0, 
[2025-07-28 01:42:34 DP0 TP0] Decode batch. #running-req: 1, #token: 804, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.86, #queue-req: 0, 
[2025-07-28 01:42:34 DP1 TP0] Decode batch. #running-req: 1, #token: 914, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.61, #queue-req: 0, 
[2025-07-28 01:42:34 DP0 TP0] Decode batch. #running-req: 1, #token: 844, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.07, #queue-req: 0, 
[2025-07-28 01:42:34 DP1 TP0] Decode batch. #running-req: 1, #token: 954, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.70, #queue-req: 0, 
[2025-07-28 01:42:34] INFO:     127.0.0.1:46956 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:42:34 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 123, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:42:34 DP1 TP0] Decode batch. #running-req: 1, #token: 994, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.00, #queue-req: 0, 
[2025-07-28 01:42:34 DP0 TP0] Decode batch. #running-req: 1, #token: 231, token usage: 0.00, cuda graph: True, gen throughput (token/s): 175.75, #queue-req: 0, 
[2025-07-28 01:42:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1034, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.77, #queue-req: 0, 
[2025-07-28 01:42:35 DP0 TP0] Decode batch. #running-req: 1, #token: 271, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.07, #queue-req: 0, 
[2025-07-28 01:42:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1074, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.55, #queue-req: 0, 
[2025-07-28 01:42:35 DP0 TP0] Decode batch. #running-req: 1, #token: 311, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.32, #queue-req: 0, 
[2025-07-28 01:42:35 DP0 TP0] Decode batch. #running-req: 1, #token: 351, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.15, #queue-req: 0, 
[2025-07-28 01:42:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1114, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.64, #queue-req: 0, 
[2025-07-28 01:42:35 DP0 TP0] Decode batch. #running-req: 1, #token: 391, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.85, #queue-req: 0, 
[2025-07-28 01:42:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1154, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.55, #queue-req: 0, 
[2025-07-28 01:42:35 DP0 TP0] Decode batch. #running-req: 1, #token: 431, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.01, #queue-req: 0, 
[2025-07-28 01:42:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1194, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.43, #queue-req: 0, 
[2025-07-28 01:42:35] INFO:     127.0.0.1:46970 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:42:35 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 129, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:42:35 DP0 TP0] Decode batch. #running-req: 1, #token: 471, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.04, #queue-req: 0, 
[2025-07-28 01:42:36 DP1 TP0] Decode batch. #running-req: 1, #token: 225, token usage: 0.00, cuda graph: True, gen throughput (token/s): 160.31, #queue-req: 0, 
[2025-07-28 01:42:36 DP0 TP0] Decode batch. #running-req: 1, #token: 511, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.57, #queue-req: 0, 
[2025-07-28 01:42:36 DP1 TP0] Decode batch. #running-req: 1, #token: 265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.71, #queue-req: 0, 
[2025-07-28 01:42:36 DP0 TP0] Decode batch. #running-req: 1, #token: 551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.17, #queue-req: 0, 
[2025-07-28 01:42:36 DP1 TP0] Decode batch. #running-req: 1, #token: 305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.88, #queue-req: 0, 
[2025-07-28 01:42:36 DP0 TP0] Decode batch. #running-req: 1, #token: 591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.21, #queue-req: 0, 
[2025-07-28 01:42:36 DP1 TP0] Decode batch. #running-req: 1, #token: 345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.83, #queue-req: 0, 
[2025-07-28 01:42:36 DP0 TP0] Decode batch. #running-req: 1, #token: 631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.48, #queue-req: 0, 
[2025-07-28 01:42:36 DP1 TP0] Decode batch. #running-req: 1, #token: 385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.64, #queue-req: 0, 
[2025-07-28 01:42:36 DP0 TP0] Decode batch. #running-req: 1, #token: 671, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.31, #queue-req: 0, 
[2025-07-28 01:42:36 DP1 TP0] Decode batch. #running-req: 1, #token: 425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.21, #queue-req: 0, 
[2025-07-28 01:42:36 DP0 TP0] Decode batch. #running-req: 1, #token: 711, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.36, #queue-req: 0, 
[2025-07-28 01:42:37 DP1 TP0] Decode batch. #running-req: 1, #token: 465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.74, #queue-req: 0, 
[2025-07-28 01:42:37 DP0 TP0] Decode batch. #running-req: 1, #token: 751, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.64, #queue-req: 0, 
[2025-07-28 01:42:37 DP1 TP0] Decode batch. #running-req: 1, #token: 505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.58, #queue-req: 0, 
[2025-07-28 01:42:37 DP0 TP0] Decode batch. #running-req: 1, #token: 791, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.40, #queue-req: 0, 
[2025-07-28 01:42:37 DP1 TP0] Decode batch. #running-req: 1, #token: 545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.87, #queue-req: 0, 
[2025-07-28 01:42:37 DP0 TP0] Decode batch. #running-req: 1, #token: 831, token usage: 0.00, cuda graph: True, gen throughput (token/s): 235.75, #queue-req: 0, 
[2025-07-28 01:42:37 DP1 TP0] Decode batch. #running-req: 1, #token: 585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.06, #queue-req: 0, 
[2025-07-28 01:42:37 DP0 TP0] Decode batch. #running-req: 1, #token: 871, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.49, #queue-req: 0, 
[2025-07-28 01:42:37 DP1 TP0] Decode batch. #running-req: 1, #token: 625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.46, #queue-req: 0, 
[2025-07-28 01:42:37 DP0 TP0] Decode batch. #running-req: 1, #token: 911, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.21, #queue-req: 0, 
[2025-07-28 01:42:37 DP1 TP0] Decode batch. #running-req: 1, #token: 665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.44, #queue-req: 0, 
[2025-07-28 01:42:37 DP0 TP0] Decode batch. #running-req: 1, #token: 951, token usage: 0.00, cuda graph: True, gen throughput (token/s): 235.15, #queue-req: 0, 
[2025-07-28 01:42:38 DP1 TP0] Decode batch. #running-req: 1, #token: 705, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.38, #queue-req: 0, 
[2025-07-28 01:42:38] INFO:     127.0.0.1:39570 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:42:38 DP0 TP0] Decode batch. #running-req: 1, #token: 991, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.27, #queue-req: 0, 
[2025-07-28 01:42:38 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 182, #cached-token: 84, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:42:38 DP0 TP0] Decode batch. #running-req: 2, #token: 1249, token usage: 0.00, cuda graph: True, gen throughput (token/s): 341.49, #queue-req: 0, 
[2025-07-28 01:42:38 DP0 TP0] Decode batch. #running-req: 2, #token: 1329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.40, #queue-req: 0, 
[2025-07-28 01:42:38] INFO:     127.0.0.1:39562 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:42:38 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 169, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:42:38 DP0 TP0] Decode batch. #running-req: 1, #token: 382, token usage: 0.00, cuda graph: True, gen throughput (token/s): 283.30, #queue-req: 0, 
[2025-07-28 01:42:38 DP1 TP0] Decode batch. #running-req: 1, #token: 275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 58.79, #queue-req: 0, 
[2025-07-28 01:42:38 DP0 TP0] Decode batch. #running-req: 1, #token: 422, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.52, #queue-req: 0, 
[2025-07-28 01:42:38 DP1 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.26, #queue-req: 0, 
[2025-07-28 01:42:39 DP0 TP0] Decode batch. #running-req: 1, #token: 462, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.56, #queue-req: 0, 
[2025-07-28 01:42:39 DP1 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.03, #queue-req: 0, 
[2025-07-28 01:42:39 DP0 TP0] Decode batch. #running-req: 1, #token: 502, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.02, #queue-req: 0, 
[2025-07-28 01:42:39 DP1 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.11, #queue-req: 0, 
[2025-07-28 01:42:39 DP0 TP0] Decode batch. #running-req: 1, #token: 542, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.64, #queue-req: 0, 
[2025-07-28 01:42:39 DP1 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.78, #queue-req: 0, 
[2025-07-28 01:42:39 DP0 TP0] Decode batch. #running-req: 1, #token: 582, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.97, #queue-req: 0, 
[2025-07-28 01:42:39 DP1 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.22, #queue-req: 0, 
[2025-07-28 01:42:39 DP0 TP0] Decode batch. #running-req: 1, #token: 622, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.97, #queue-req: 0, 
[2025-07-28 01:42:39 DP1 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.07, #queue-req: 0, 
[2025-07-28 01:42:39 DP0 TP0] Decode batch. #running-req: 1, #token: 662, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.76, #queue-req: 0, 
[2025-07-28 01:42:39 DP1 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.12, #queue-req: 0, 
[2025-07-28 01:42:40 DP0 TP0] Decode batch. #running-req: 1, #token: 702, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.95, #queue-req: 0, 
[2025-07-28 01:42:40 DP1 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.68, #queue-req: 0, 
[2025-07-28 01:42:40 DP0 TP0] Decode batch. #running-req: 1, #token: 742, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.88, #queue-req: 0, 
[2025-07-28 01:42:40 DP1 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.82, #queue-req: 0, 
[2025-07-28 01:42:40 DP0 TP0] Decode batch. #running-req: 1, #token: 782, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.63, #queue-req: 0, 
[2025-07-28 01:42:40 DP1 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.73, #queue-req: 0, 
[2025-07-28 01:42:40 DP0 TP0] Decode batch. #running-req: 1, #token: 822, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.90, #queue-req: 0, 
[2025-07-28 01:42:40 DP1 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.81, #queue-req: 0, 
[2025-07-28 01:42:40 DP0 TP0] Decode batch. #running-req: 1, #token: 862, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.81, #queue-req: 0, 
[2025-07-28 01:42:40 DP1 TP0] Decode batch. #running-req: 1, #token: 755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.41, #queue-req: 0, 
[2025-07-28 01:42:40] INFO:     127.0.0.1:39580 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:42:40 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 222, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:42:40 DP1 TP0] Decode batch. #running-req: 1, #token: 795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.21, #queue-req: 0, 
[2025-07-28 01:42:40 DP0 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 171.00, #queue-req: 0, 
[2025-07-28 01:42:41 DP1 TP0] Decode batch. #running-req: 1, #token: 835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.59, #queue-req: 0, 
[2025-07-28 01:42:41 DP0 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 234.41, #queue-req: 0, 
[2025-07-28 01:42:41 DP1 TP0] Decode batch. #running-req: 1, #token: 875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.80, #queue-req: 0, 
[2025-07-28 01:42:41 DP0 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.21, #queue-req: 0, 
[2025-07-28 01:42:41 DP1 TP0] Decode batch. #running-req: 1, #token: 915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.21, #queue-req: 0, 
[2025-07-28 01:42:41 DP0 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.73, #queue-req: 0, 
[2025-07-28 01:42:41 DP1 TP0] Decode batch. #running-req: 1, #token: 955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.34, #queue-req: 0, 
[2025-07-28 01:42:41 DP0 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.74, #queue-req: 0, 
[2025-07-28 01:42:41 DP1 TP0] Decode batch. #running-req: 1, #token: 995, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.18, #queue-req: 0, 
[2025-07-28 01:42:41 DP0 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.79, #queue-req: 0, 
[2025-07-28 01:42:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.55, #queue-req: 0, 
[2025-07-28 01:42:41 DP0 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.41, #queue-req: 0, 
[2025-07-28 01:42:42 DP1 TP0] Decode batch. #running-req: 1, #token: 1075, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.82, #queue-req: 0, 
[2025-07-28 01:42:42 DP0 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.13, #queue-req: 0, 
[2025-07-28 01:42:42] INFO:     127.0.0.1:39582 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:42:42 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 164, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:42:42 DP1 TP0] Decode batch. #running-req: 1, #token: 253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 170.38, #queue-req: 0, 
[2025-07-28 01:42:42 DP0 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.65, #queue-req: 0, 
[2025-07-28 01:42:42 DP1 TP0] Decode batch. #running-req: 1, #token: 293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.58, #queue-req: 0, 
[2025-07-28 01:42:42 DP0 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.04, #queue-req: 0, 
[2025-07-28 01:42:42 DP1 TP0] Decode batch. #running-req: 1, #token: 333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.94, #queue-req: 0, 
[2025-07-28 01:42:42 DP0 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.15, #queue-req: 0, 
[2025-07-28 01:42:42 DP1 TP0] Decode batch. #running-req: 1, #token: 373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.82, #queue-req: 0, 
[2025-07-28 01:42:42 DP0 TP0] Decode batch. #running-req: 1, #token: 755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.49, #queue-req: 0, 
[2025-07-28 01:42:42 DP1 TP0] Decode batch. #running-req: 1, #token: 413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.47, #queue-req: 0, 
[2025-07-28 01:42:42 DP0 TP0] Decode batch. #running-req: 1, #token: 795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.76, #queue-req: 0, 
[2025-07-28 01:42:43 DP1 TP0] Decode batch. #running-req: 1, #token: 453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.72, #queue-req: 0, 
[2025-07-28 01:42:43 DP0 TP0] Decode batch. #running-req: 1, #token: 835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.18, #queue-req: 0, 
[2025-07-28 01:42:43 DP1 TP0] Decode batch. #running-req: 1, #token: 493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.99, #queue-req: 0, 
[2025-07-28 01:42:43 DP0 TP0] Decode batch. #running-req: 1, #token: 875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.02, #queue-req: 0, 
[2025-07-28 01:42:43 DP1 TP0] Decode batch. #running-req: 1, #token: 533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.90, #queue-req: 0, 
[2025-07-28 01:42:43 DP0 TP0] Decode batch. #running-req: 1, #token: 915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.19, #queue-req: 0, 
[2025-07-28 01:42:43 DP1 TP0] Decode batch. #running-req: 1, #token: 573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.43, #queue-req: 0, 
[2025-07-28 01:42:43 DP0 TP0] Decode batch. #running-req: 1, #token: 955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.60, #queue-req: 0, 
[2025-07-28 01:42:43 DP1 TP0] Decode batch. #running-req: 1, #token: 613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.35, #queue-req: 0, 
[2025-07-28 01:42:43 DP0 TP0] Decode batch. #running-req: 1, #token: 995, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.73, #queue-req: 0, 
[2025-07-28 01:42:43 DP1 TP0] Decode batch. #running-req: 1, #token: 653, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.55, #queue-req: 0, 
[2025-07-28 01:42:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.44, #queue-req: 0, 
[2025-07-28 01:42:44 DP1 TP0] Decode batch. #running-req: 1, #token: 693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.31, #queue-req: 0, 
[2025-07-28 01:42:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1075, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.23, #queue-req: 0, 
[2025-07-28 01:42:44 DP1 TP0] Decode batch. #running-req: 1, #token: 733, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.44, #queue-req: 0, 
[2025-07-28 01:42:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.08, #queue-req: 0, 
[2025-07-28 01:42:44 DP1 TP0] Decode batch. #running-req: 1, #token: 773, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.31, #queue-req: 0, 
[2025-07-28 01:42:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1155, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.33, #queue-req: 0, 
[2025-07-28 01:42:44 DP1 TP0] Decode batch. #running-req: 1, #token: 813, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.92, #queue-req: 0, 
[2025-07-28 01:42:44] INFO:     127.0.0.1:39590 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:42:44 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 150, #cached-token: 86, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:42:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1344, token usage: 0.00, cuda graph: True, gen throughput (token/s): 201.18, #queue-req: 0, 
[2025-07-28 01:42:44 DP0 TP0] Decode batch. #running-req: 2, #token: 1426, token usage: 0.00, cuda graph: True, gen throughput (token/s): 457.77, #queue-req: 0, 
[2025-07-28 01:42:44] INFO:     127.0.0.1:39586 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:42:44 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 137, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:42:44 DP0 TP0] Decode batch. #running-req: 1, #token: 317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 427.22, #queue-req: 0, 
[2025-07-28 01:42:45 DP0 TP0] Decode batch. #running-req: 1, #token: 357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.04, #queue-req: 0, 
[2025-07-28 01:42:45 DP1 TP0] Decode batch. #running-req: 1, #token: 253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 64.71, #queue-req: 0, 
[2025-07-28 01:42:45 DP0 TP0] Decode batch. #running-req: 1, #token: 397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.73, #queue-req: 0, 
[2025-07-28 01:42:45 DP1 TP0] Decode batch. #running-req: 1, #token: 293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.21, #queue-req: 0, 
[2025-07-28 01:42:45 DP0 TP0] Decode batch. #running-req: 1, #token: 437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.68, #queue-req: 0, 
[2025-07-28 01:42:45 DP1 TP0] Decode batch. #running-req: 1, #token: 333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.58, #queue-req: 0, 
[2025-07-28 01:42:45 DP0 TP0] Decode batch. #running-req: 1, #token: 477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.53, #queue-req: 0, 
[2025-07-28 01:42:45 DP1 TP0] Decode batch. #running-req: 1, #token: 373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.14, #queue-req: 0, 
[2025-07-28 01:42:45 DP0 TP0] Decode batch. #running-req: 1, #token: 517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.03, #queue-req: 0, 
[2025-07-28 01:42:45 DP1 TP0] Decode batch. #running-req: 1, #token: 413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.06, #queue-req: 0, 
[2025-07-28 01:42:45 DP0 TP0] Decode batch. #running-req: 1, #token: 557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.53, #queue-req: 0, 
[2025-07-28 01:42:45 DP1 TP0] Decode batch. #running-req: 1, #token: 453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.88, #queue-req: 0, 
[2025-07-28 01:42:46 DP0 TP0] Decode batch. #running-req: 1, #token: 597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.11, #queue-req: 0, 
[2025-07-28 01:42:46 DP1 TP0] Decode batch. #running-req: 1, #token: 493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.42, #queue-req: 0, 
[2025-07-28 01:42:46 DP0 TP0] Decode batch. #running-req: 1, #token: 637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.63, #queue-req: 0, 
[2025-07-28 01:42:46 DP1 TP0] Decode batch. #running-req: 1, #token: 533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.15, #queue-req: 0, 
[2025-07-28 01:42:46 DP0 TP0] Decode batch. #running-req: 1, #token: 677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.32, #queue-req: 0, 
[2025-07-28 01:42:46 DP1 TP0] Decode batch. #running-req: 1, #token: 573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.55, #queue-req: 0, 
[2025-07-28 01:42:46 DP0 TP0] Decode batch. #running-req: 1, #token: 717, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.87, #queue-req: 0, 
[2025-07-28 01:42:46 DP1 TP0] Decode batch. #running-req: 1, #token: 613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.44, #queue-req: 0, 
[2025-07-28 01:42:46] INFO:     127.0.0.1:33282 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:42:46 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 188, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:42:46 DP1 TP0] Decode batch. #running-req: 1, #token: 653, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.72, #queue-req: 0, 
[2025-07-28 01:42:46 DP0 TP0] Decode batch. #running-req: 1, #token: 307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 180.42, #queue-req: 0, 
[2025-07-28 01:42:46 DP1 TP0] Decode batch. #running-req: 1, #token: 693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.58, #queue-req: 0, 
[2025-07-28 01:42:46 DP0 TP0] Decode batch. #running-req: 1, #token: 347, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.86, #queue-req: 0, 
[2025-07-28 01:42:47 DP1 TP0] Decode batch. #running-req: 1, #token: 733, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.09, #queue-req: 0, 
[2025-07-28 01:42:47 DP0 TP0] Decode batch. #running-req: 1, #token: 387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.30, #queue-req: 0, 
[2025-07-28 01:42:47 DP1 TP0] Decode batch. #running-req: 1, #token: 773, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.29, #queue-req: 0, 
[2025-07-28 01:42:47 DP0 TP0] Decode batch. #running-req: 1, #token: 427, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.82, #queue-req: 0, 
[2025-07-28 01:42:47 DP1 TP0] Decode batch. #running-req: 1, #token: 813, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.89, #queue-req: 0, 
[2025-07-28 01:42:47 DP0 TP0] Decode batch. #running-req: 1, #token: 467, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.72, #queue-req: 0, 
[2025-07-28 01:42:47 DP1 TP0] Decode batch. #running-req: 1, #token: 853, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.96, #queue-req: 0, 
[2025-07-28 01:42:47 DP0 TP0] Decode batch. #running-req: 1, #token: 507, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.89, #queue-req: 0, 
[2025-07-28 01:42:47 DP1 TP0] Decode batch. #running-req: 1, #token: 893, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.10, #queue-req: 0, 
[2025-07-28 01:42:47 DP0 TP0] Decode batch. #running-req: 1, #token: 547, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.71, #queue-req: 0, 
[2025-07-28 01:42:47 DP1 TP0] Decode batch. #running-req: 1, #token: 933, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.52, #queue-req: 0, 
[2025-07-28 01:42:47 DP0 TP0] Decode batch. #running-req: 1, #token: 587, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.76, #queue-req: 0, 
[2025-07-28 01:42:48 DP1 TP0] Decode batch. #running-req: 1, #token: 973, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.87, #queue-req: 0, 
[2025-07-28 01:42:48 DP0 TP0] Decode batch. #running-req: 1, #token: 627, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.70, #queue-req: 0, 
[2025-07-28 01:42:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1013, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.82, #queue-req: 0, 
[2025-07-28 01:42:48 DP0 TP0] Decode batch. #running-req: 1, #token: 667, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.16, #queue-req: 0, 
[2025-07-28 01:42:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1053, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.57, #queue-req: 0, 
[2025-07-28 01:42:48 DP0 TP0] Decode batch. #running-req: 1, #token: 707, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.83, #queue-req: 0, 
[2025-07-28 01:42:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1093, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.98, #queue-req: 0, 
[2025-07-28 01:42:48 DP0 TP0] Decode batch. #running-req: 1, #token: 747, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.56, #queue-req: 0, 
[2025-07-28 01:42:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1133, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.85, #queue-req: 0, 
[2025-07-28 01:42:48 DP0 TP0] Decode batch. #running-req: 1, #token: 787, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.79, #queue-req: 0, 
[2025-07-28 01:42:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1173, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.83, #queue-req: 0, 
[2025-07-28 01:42:48 DP0 TP0] Decode batch. #running-req: 1, #token: 827, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.61, #queue-req: 0, 
[2025-07-28 01:42:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1213, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.35, #queue-req: 0, 
[2025-07-28 01:42:49 DP0 TP0] Decode batch. #running-req: 1, #token: 867, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.61, #queue-req: 0, 
[2025-07-28 01:42:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.95, #queue-req: 0, 
[2025-07-28 01:42:49 DP0 TP0] Decode batch. #running-req: 1, #token: 907, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.62, #queue-req: 0, 
[2025-07-28 01:42:49 DP1 TP0] Decode batch. #running-req: 1, #token: 1293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.79, #queue-req: 0, 
[2025-07-28 01:42:49] INFO:     127.0.0.1:33296 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:42:49 DP0 TP0] Decode batch. #running-req: 1, #token: 947, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.29, #queue-req: 0, 
[2025-07-28 01:42:49 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 186, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:42:49 DP0 TP0] Decode batch. #running-req: 1, #token: 987, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.28, #queue-req: 0, 
[2025-07-28 01:42:49 DP1 TP0] Decode batch. #running-req: 1, #token: 305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 183.06, #queue-req: 0, 
[2025-07-28 01:42:49 DP0 TP0] Decode batch. #running-req: 1, #token: 1027, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.37, #queue-req: 0, 
[2025-07-28 01:42:49 DP1 TP0] Decode batch. #running-req: 1, #token: 345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.11, #queue-req: 0, 
[2025-07-28 01:42:49 DP0 TP0] Decode batch. #running-req: 1, #token: 1067, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.99, #queue-req: 0, 
[2025-07-28 01:42:49 DP1 TP0] Decode batch. #running-req: 1, #token: 385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.92, #queue-req: 0, 
[2025-07-28 01:42:50 DP0 TP0] Decode batch. #running-req: 1, #token: 1107, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.16, #queue-req: 0, 
[2025-07-28 01:42:50 DP1 TP0] Decode batch. #running-req: 1, #token: 425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.13, #queue-req: 0, 
[2025-07-28 01:42:50 DP0 TP0] Decode batch. #running-req: 1, #token: 1147, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.93, #queue-req: 0, 
[2025-07-28 01:42:50 DP1 TP0] Decode batch. #running-req: 1, #token: 465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.63, #queue-req: 0, 
[2025-07-28 01:42:50] INFO:     127.0.0.1:33312 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:42:50 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 133, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:42:50 DP1 TP0] Decode batch. #running-req: 1, #token: 505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.72, #queue-req: 0, 
[2025-07-28 01:42:50 DP0 TP0] Decode batch. #running-req: 1, #token: 232, token usage: 0.00, cuda graph: True, gen throughput (token/s): 178.98, #queue-req: 0, 
[2025-07-28 01:42:50 DP1 TP0] Decode batch. #running-req: 1, #token: 545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.88, #queue-req: 0, 
[2025-07-28 01:42:50 DP0 TP0] Decode batch. #running-req: 1, #token: 272, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.73, #queue-req: 0, 
[2025-07-28 01:42:50 DP1 TP0] Decode batch. #running-req: 1, #token: 585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.99, #queue-req: 0, 
[2025-07-28 01:42:50 DP0 TP0] Decode batch. #running-req: 1, #token: 312, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.06, #queue-req: 0, 
[2025-07-28 01:42:50 DP1 TP0] Decode batch. #running-req: 1, #token: 625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.15, #queue-req: 0, 
[2025-07-28 01:42:50 DP0 TP0] Decode batch. #running-req: 1, #token: 352, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.85, #queue-req: 0, 
[2025-07-28 01:42:51 DP1 TP0] Decode batch. #running-req: 1, #token: 665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.08, #queue-req: 0, 
[2025-07-28 01:42:51 DP0 TP0] Decode batch. #running-req: 1, #token: 392, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.99, #queue-req: 0, 
[2025-07-28 01:42:51 DP1 TP0] Decode batch. #running-req: 1, #token: 705, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.20, #queue-req: 0, 
[2025-07-28 01:42:51 DP0 TP0] Decode batch. #running-req: 1, #token: 432, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.77, #queue-req: 0, 
[2025-07-28 01:42:51 DP1 TP0] Decode batch. #running-req: 1, #token: 745, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.25, #queue-req: 0, 
[2025-07-28 01:42:51 DP0 TP0] Decode batch. #running-req: 1, #token: 472, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.24, #queue-req: 0, 
[2025-07-28 01:42:51 DP1 TP0] Decode batch. #running-req: 1, #token: 785, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.94, #queue-req: 0, 
[2025-07-28 01:42:51 DP0 TP0] Decode batch. #running-req: 1, #token: 512, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.73, #queue-req: 0, 
[2025-07-28 01:42:51 DP1 TP0] Decode batch. #running-req: 1, #token: 825, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.11, #queue-req: 0, 
[2025-07-28 01:42:51 DP0 TP0] Decode batch. #running-req: 1, #token: 552, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.67, #queue-req: 0, 
[2025-07-28 01:42:51 DP1 TP0] Decode batch. #running-req: 1, #token: 865, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.03, #queue-req: 0, 
[2025-07-28 01:42:51 DP0 TP0] Decode batch. #running-req: 1, #token: 592, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.98, #queue-req: 0, 
[2025-07-28 01:42:52 DP1 TP0] Decode batch. #running-req: 1, #token: 905, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.13, #queue-req: 0, 
[2025-07-28 01:42:52 DP0 TP0] Decode batch. #running-req: 1, #token: 632, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.38, #queue-req: 0, 
[2025-07-28 01:42:52 DP1 TP0] Decode batch. #running-req: 1, #token: 945, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.10, #queue-req: 0, 
[2025-07-28 01:42:52 DP0 TP0] Decode batch. #running-req: 1, #token: 672, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.34, #queue-req: 0, 
[2025-07-28 01:42:52 DP1 TP0] Decode batch. #running-req: 1, #token: 985, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.88, #queue-req: 0, 
[2025-07-28 01:42:52 DP0 TP0] Decode batch. #running-req: 1, #token: 712, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.88, #queue-req: 0, 
[2025-07-28 01:42:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1025, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.01, #queue-req: 0, 
[2025-07-28 01:42:52 DP0 TP0] Decode batch. #running-req: 1, #token: 752, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.26, #queue-req: 0, 
[2025-07-28 01:42:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1065, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.16, #queue-req: 0, 
[2025-07-28 01:42:52 DP0 TP0] Decode batch. #running-req: 1, #token: 792, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.73, #queue-req: 0, 
[2025-07-28 01:42:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1105, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.04, #queue-req: 0, 
[2025-07-28 01:42:52 DP0 TP0] Decode batch. #running-req: 1, #token: 832, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.62, #queue-req: 0, 
[2025-07-28 01:42:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1145, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.27, #queue-req: 0, 
[2025-07-28 01:42:53 DP0 TP0] Decode batch. #running-req: 1, #token: 872, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.41, #queue-req: 0, 
[2025-07-28 01:42:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1185, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.24, #queue-req: 0, 
[2025-07-28 01:42:53 DP0 TP0] Decode batch. #running-req: 1, #token: 912, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.63, #queue-req: 0, 
[2025-07-28 01:42:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1225, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.07, #queue-req: 0, 
[2025-07-28 01:42:53 DP0 TP0] Decode batch. #running-req: 1, #token: 952, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.08, #queue-req: 0, 
[2025-07-28 01:42:53] INFO:     127.0.0.1:33336 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:42:53 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 128, #cached-token: 88, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:42:53 DP1 TP0] Decode batch. #running-req: 2, #token: 1407, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.95, #queue-req: 0, 
[2025-07-28 01:42:53] INFO:     127.0.0.1:33320 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:42:53 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 195, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:42:53 DP1 TP0] Decode batch. #running-req: 1, #token: 266, token usage: 0.00, cuda graph: True, gen throughput (token/s): 339.69, #queue-req: 0, 
[2025-07-28 01:42:53 DP0 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 97.20, #queue-req: 0, 
[2025-07-28 01:42:53 DP1 TP0] Decode batch. #running-req: 1, #token: 306, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.91, #queue-req: 0, 
[2025-07-28 01:42:54 DP0 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.78, #queue-req: 0, 
[2025-07-28 01:42:54 DP1 TP0] Decode batch. #running-req: 1, #token: 346, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.51, #queue-req: 0, 
[2025-07-28 01:42:54 DP0 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.98, #queue-req: 0, 
[2025-07-28 01:42:54 DP1 TP0] Decode batch. #running-req: 1, #token: 386, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.59, #queue-req: 0, 
[2025-07-28 01:42:54 DP0 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.30, #queue-req: 0, 
[2025-07-28 01:42:54 DP1 TP0] Decode batch. #running-req: 1, #token: 426, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.52, #queue-req: 0, 
[2025-07-28 01:42:54 DP0 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.16, #queue-req: 0, 
[2025-07-28 01:42:54 DP1 TP0] Decode batch. #running-req: 1, #token: 466, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.13, #queue-req: 0, 
[2025-07-28 01:42:54 DP0 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.39, #queue-req: 0, 
[2025-07-28 01:42:54 DP1 TP0] Decode batch. #running-req: 1, #token: 506, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.47, #queue-req: 0, 
[2025-07-28 01:42:54 DP0 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.68, #queue-req: 0, 
[2025-07-28 01:42:54 DP1 TP0] Decode batch. #running-req: 1, #token: 546, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.93, #queue-req: 0, 
[2025-07-28 01:42:55 DP0 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.65, #queue-req: 0, 
[2025-07-28 01:42:55 DP1 TP0] Decode batch. #running-req: 1, #token: 586, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.52, #queue-req: 0, 
[2025-07-28 01:42:55 DP0 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.04, #queue-req: 0, 
[2025-07-28 01:42:55 DP1 TP0] Decode batch. #running-req: 1, #token: 626, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.36, #queue-req: 0, 
[2025-07-28 01:42:55 DP0 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.87, #queue-req: 0, 
[2025-07-28 01:42:55 DP1 TP0] Decode batch. #running-req: 1, #token: 666, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.46, #queue-req: 0, 
[2025-07-28 01:42:55 DP0 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.05, #queue-req: 0, 
[2025-07-28 01:42:55 DP1 TP0] Decode batch. #running-req: 1, #token: 706, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.76, #queue-req: 0, 
[2025-07-28 01:42:55 DP0 TP0] Decode batch. #running-req: 1, #token: 755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.37, #queue-req: 0, 
[2025-07-28 01:42:55 DP1 TP0] Decode batch. #running-req: 1, #token: 746, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.20, #queue-req: 0, 
[2025-07-28 01:42:55 DP0 TP0] Decode batch. #running-req: 1, #token: 795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.27, #queue-req: 0, 
[2025-07-28 01:42:55 DP1 TP0] Decode batch. #running-req: 1, #token: 786, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.03, #queue-req: 0, 
[2025-07-28 01:42:55 DP0 TP0] Decode batch. #running-req: 1, #token: 835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.38, #queue-req: 0, 
[2025-07-28 01:42:56 DP1 TP0] Decode batch. #running-req: 1, #token: 826, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.48, #queue-req: 0, 
[2025-07-28 01:42:56 DP0 TP0] Decode batch. #running-req: 1, #token: 875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.55, #queue-req: 0, 
[2025-07-28 01:42:56 DP1 TP0] Decode batch. #running-req: 1, #token: 866, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.05, #queue-req: 0, 
[2025-07-28 01:42:56 DP0 TP0] Decode batch. #running-req: 1, #token: 915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.24, #queue-req: 0, 
[2025-07-28 01:42:56 DP1 TP0] Decode batch. #running-req: 1, #token: 906, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.39, #queue-req: 0, 
[2025-07-28 01:42:56 DP0 TP0] Decode batch. #running-req: 1, #token: 955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.19, #queue-req: 0, 
[2025-07-28 01:42:56 DP1 TP0] Decode batch. #running-req: 1, #token: 946, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.89, #queue-req: 0, 
[2025-07-28 01:42:56] INFO:     127.0.0.1:43618 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:42:56 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 190, #cached-token: 86, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:42:56 DP1 TP0] Decode batch. #running-req: 2, #token: 1206, token usage: 0.00, cuda graph: True, gen throughput (token/s): 332.10, #queue-req: 0, 
[2025-07-28 01:42:56 DP1 TP0] Decode batch. #running-req: 2, #token: 1286, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.14, #queue-req: 0, 
[2025-07-28 01:42:57] INFO:     127.0.0.1:43604 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:42:57 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 163, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:42:57 DP1 TP0] Decode batch. #running-req: 1, #token: 386, token usage: 0.00, cuda graph: True, gen throughput (token/s): 421.01, #queue-req: 0, 
[2025-07-28 01:42:57 DP0 TP0] Decode batch. #running-req: 1, #token: 276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 56.37, #queue-req: 0, 
[2025-07-28 01:42:57 DP1 TP0] Decode batch. #running-req: 1, #token: 426, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.94, #queue-req: 0, 
[2025-07-28 01:42:57 DP0 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.97, #queue-req: 0, 
[2025-07-28 01:42:57 DP1 TP0] Decode batch. #running-req: 1, #token: 466, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.01, #queue-req: 0, 
[2025-07-28 01:42:57 DP0 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.50, #queue-req: 0, 
[2025-07-28 01:42:57 DP1 TP0] Decode batch. #running-req: 1, #token: 506, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.84, #queue-req: 0, 
[2025-07-28 01:42:57 DP0 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.47, #queue-req: 0, 
[2025-07-28 01:42:57 DP1 TP0] Decode batch. #running-req: 1, #token: 546, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.60, #queue-req: 0, 
[2025-07-28 01:42:57 DP0 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.98, #queue-req: 0, 
[2025-07-28 01:42:57 DP1 TP0] Decode batch. #running-req: 1, #token: 586, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.82, #queue-req: 0, 
[2025-07-28 01:42:57 DP0 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.07, #queue-req: 0, 
[2025-07-28 01:42:58 DP1 TP0] Decode batch. #running-req: 1, #token: 626, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.26, #queue-req: 0, 
[2025-07-28 01:42:58 DP0 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.55, #queue-req: 0, 
[2025-07-28 01:42:58 DP1 TP0] Decode batch. #running-req: 1, #token: 666, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.25, #queue-req: 0, 
[2025-07-28 01:42:58 DP0 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.72, #queue-req: 0, 
[2025-07-28 01:42:58 DP1 TP0] Decode batch. #running-req: 1, #token: 706, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.84, #queue-req: 0, 
[2025-07-28 01:42:58 DP0 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.79, #queue-req: 0, 
[2025-07-28 01:42:58 DP1 TP0] Decode batch. #running-req: 1, #token: 746, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.96, #queue-req: 0, 
[2025-07-28 01:42:58 DP0 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.88, #queue-req: 0, 
[2025-07-28 01:42:58 DP1 TP0] Decode batch. #running-req: 1, #token: 786, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.63, #queue-req: 0, 
[2025-07-28 01:42:58 DP0 TP0] Decode batch. #running-req: 1, #token: 676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.61, #queue-req: 0, 
[2025-07-28 01:42:58 DP1 TP0] Decode batch. #running-req: 1, #token: 826, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.69, #queue-req: 0, 
[2025-07-28 01:42:58 DP0 TP0] Decode batch. #running-req: 1, #token: 716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.82, #queue-req: 0, 
[2025-07-28 01:42:58 DP1 TP0] Decode batch. #running-req: 1, #token: 866, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.02, #queue-req: 0, 
[2025-07-28 01:42:59 DP0 TP0] Decode batch. #running-req: 1, #token: 756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.10, #queue-req: 0, 
[2025-07-28 01:42:59 DP1 TP0] Decode batch. #running-req: 1, #token: 906, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.78, #queue-req: 0, 
[2025-07-28 01:42:59 DP0 TP0] Decode batch. #running-req: 1, #token: 796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.82, #queue-req: 0, 
[2025-07-28 01:42:59 DP1 TP0] Decode batch. #running-req: 1, #token: 946, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.53, #queue-req: 0, 
[2025-07-28 01:42:59 DP0 TP0] Decode batch. #running-req: 1, #token: 836, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.71, #queue-req: 0, 
[2025-07-28 01:42:59 DP1 TP0] Decode batch. #running-req: 1, #token: 986, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.42, #queue-req: 0, 
[2025-07-28 01:42:59 DP0 TP0] Decode batch. #running-req: 1, #token: 876, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.86, #queue-req: 0, 
[2025-07-28 01:42:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1026, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.28, #queue-req: 0, 
[2025-07-28 01:42:59 DP0 TP0] Decode batch. #running-req: 1, #token: 916, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.39, #queue-req: 0, 
[2025-07-28 01:42:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1066, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.38, #queue-req: 0, 
[2025-07-28 01:42:59 DP0 TP0] Decode batch. #running-req: 1, #token: 956, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.93, #queue-req: 0, 
[2025-07-28 01:42:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1106, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.17, #queue-req: 0, 
[2025-07-28 01:43:00 DP0 TP0] Decode batch. #running-req: 1, #token: 996, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.15, #queue-req: 0, 
[2025-07-28 01:43:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1146, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.10, #queue-req: 0, 
[2025-07-28 01:43:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1036, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.02, #queue-req: 0, 
[2025-07-28 01:43:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1186, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.16, #queue-req: 0, 
[2025-07-28 01:43:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1076, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.79, #queue-req: 0, 
[2025-07-28 01:43:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1226, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.67, #queue-req: 0, 
[2025-07-28 01:43:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1116, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.75, #queue-req: 0, 
[2025-07-28 01:43:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1266, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.55, #queue-req: 0, 
[2025-07-28 01:43:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1156, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.69, #queue-req: 0, 
[2025-07-28 01:43:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1306, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.04, #queue-req: 0, 
[2025-07-28 01:43:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1196, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.72, #queue-req: 0, 
[2025-07-28 01:43:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1346, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.96, #queue-req: 0, 
[2025-07-28 01:43:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1236, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.87, #queue-req: 0, 
[2025-07-28 01:43:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1386, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.08, #queue-req: 0, 
[2025-07-28 01:43:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.86, #queue-req: 0, 
[2025-07-28 01:43:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1426, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.75, #queue-req: 0, 
[2025-07-28 01:43:01] INFO:     127.0.0.1:43646 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:43:01 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 168, #cached-token: 88, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:43:01 DP1 TP0] Decode batch. #running-req: 2, #token: 1659, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.07, #queue-req: 0, 
[2025-07-28 01:43:01 DP1 TP0] Decode batch. #running-req: 2, #token: 1739, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.97, #queue-req: 0, 
[2025-07-28 01:43:01 DP1 TP0] Decode batch. #running-req: 2, #token: 1819, token usage: 0.00, cuda graph: True, gen throughput (token/s): 470.20, #queue-req: 0, 
[2025-07-28 01:43:01 DP1 TP0] Decode batch. #running-req: 2, #token: 1899, token usage: 0.00, cuda graph: True, gen throughput (token/s): 470.21, #queue-req: 0, 
[2025-07-28 01:43:02 DP1 TP0] Decode batch. #running-req: 2, #token: 1979, token usage: 0.00, cuda graph: True, gen throughput (token/s): 470.09, #queue-req: 0, 
[2025-07-28 01:43:02 DP1 TP0] Decode batch. #running-req: 2, #token: 2059, token usage: 0.00, cuda graph: True, gen throughput (token/s): 470.59, #queue-req: 0, 
[2025-07-28 01:43:02 DP1 TP0] Decode batch. #running-req: 2, #token: 2139, token usage: 0.00, cuda graph: True, gen throughput (token/s): 470.12, #queue-req: 0, 
[2025-07-28 01:43:02 DP1 TP0] Decode batch. #running-req: 2, #token: 2219, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.93, #queue-req: 0, 
[2025-07-28 01:43:02 DP1 TP0] Decode batch. #running-req: 2, #token: 2299, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.61, #queue-req: 0, 
[2025-07-28 01:43:03 DP1 TP0] Decode batch. #running-req: 2, #token: 2379, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.48, #queue-req: 0, 
[2025-07-28 01:43:03 DP1 TP0] Decode batch. #running-req: 2, #token: 2459, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.55, #queue-req: 0, 
[2025-07-28 01:43:03 DP1 TP0] Decode batch. #running-req: 2, #token: 2539, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.49, #queue-req: 0, 
[2025-07-28 01:43:03 DP1 TP0] Decode batch. #running-req: 2, #token: 2619, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.33, #queue-req: 0, 
[2025-07-28 01:43:03 DP1 TP0] Decode batch. #running-req: 2, #token: 2699, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.68, #queue-req: 0, 
[2025-07-28 01:43:03 DP1 TP0] Decode batch. #running-req: 2, #token: 2779, token usage: 0.00, cuda graph: True, gen throughput (token/s): 468.80, #queue-req: 0, 
[2025-07-28 01:43:04 DP1 TP0] Decode batch. #running-req: 2, #token: 2859, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.78, #queue-req: 0, 
[2025-07-28 01:43:04 DP1 TP0] Decode batch. #running-req: 2, #token: 2939, token usage: 0.00, cuda graph: True, gen throughput (token/s): 468.95, #queue-req: 0, 
[2025-07-28 01:43:04 DP1 TP0] Decode batch. #running-req: 2, #token: 3019, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.97, #queue-req: 0, 
[2025-07-28 01:43:04 DP1 TP0] Decode batch. #running-req: 2, #token: 3099, token usage: 0.00, cuda graph: True, gen throughput (token/s): 470.36, #queue-req: 0, 
[2025-07-28 01:43:04 DP1 TP0] Decode batch. #running-req: 2, #token: 3179, token usage: 0.00, cuda graph: True, gen throughput (token/s): 470.05, #queue-req: 0, 
[2025-07-28 01:43:04] INFO:     127.0.0.1:43650 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:43:04 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 215, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:43:04 DP1 TP0] Decode batch. #running-req: 1, #token: 2266, token usage: 0.00, cuda graph: True, gen throughput (token/s): 405.85, #queue-req: 0, 
[2025-07-28 01:43:05 DP0 TP0] Decode batch. #running-req: 1, #token: 327, token usage: 0.00, cuda graph: True, gen throughput (token/s): 10.60, #queue-req: 0, 
[2025-07-28 01:43:05 DP1 TP0] Decode batch. #running-req: 1, #token: 2306, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.26, #queue-req: 0, 
[2025-07-28 01:43:05 DP1 TP0] Decode batch. #running-req: 1, #token: 2346, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.84, #queue-req: 0, 
[2025-07-28 01:43:05 DP0 TP0] Decode batch. #running-req: 1, #token: 367, token usage: 0.00, cuda graph: True, gen throughput (token/s): 232.28, #queue-req: 0, 
[2025-07-28 01:43:05 DP1 TP0] Decode batch. #running-req: 1, #token: 2386, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.99, #queue-req: 0, 
[2025-07-28 01:43:05 DP0 TP0] Decode batch. #running-req: 1, #token: 407, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.71, #queue-req: 0, 
[2025-07-28 01:43:05 DP1 TP0] Decode batch. #running-req: 1, #token: 2426, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.55, #queue-req: 0, 
[2025-07-28 01:43:05 DP0 TP0] Decode batch. #running-req: 1, #token: 447, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.54, #queue-req: 0, 
[2025-07-28 01:43:05 DP1 TP0] Decode batch. #running-req: 1, #token: 2466, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.06, #queue-req: 0, 
[2025-07-28 01:43:05 DP0 TP0] Decode batch. #running-req: 1, #token: 487, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.71, #queue-req: 0, 
[2025-07-28 01:43:05 DP1 TP0] Decode batch. #running-req: 1, #token: 2506, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.79, #queue-req: 0, 
[2025-07-28 01:43:05 DP0 TP0] Decode batch. #running-req: 1, #token: 527, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.50, #queue-req: 0, 
[2025-07-28 01:43:06 DP1 TP0] Decode batch. #running-req: 1, #token: 2546, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.31, #queue-req: 0, 
[2025-07-28 01:43:06 DP0 TP0] Decode batch. #running-req: 1, #token: 567, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.58, #queue-req: 0, 
[2025-07-28 01:43:06 DP1 TP0] Decode batch. #running-req: 1, #token: 2586, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.00, #queue-req: 0, 
[2025-07-28 01:43:06 DP0 TP0] Decode batch. #running-req: 1, #token: 607, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.54, #queue-req: 0, 
[2025-07-28 01:43:06 DP1 TP0] Decode batch. #running-req: 1, #token: 2626, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.62, #queue-req: 0, 
[2025-07-28 01:43:06 DP0 TP0] Decode batch. #running-req: 1, #token: 647, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.11, #queue-req: 0, 
[2025-07-28 01:43:06 DP1 TP0] Decode batch. #running-req: 1, #token: 2666, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.23, #queue-req: 0, 
[2025-07-28 01:43:06 DP0 TP0] Decode batch. #running-req: 1, #token: 687, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.27, #queue-req: 0, 
[2025-07-28 01:43:06 DP1 TP0] Decode batch. #running-req: 1, #token: 2706, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.00, #queue-req: 0, 
[2025-07-28 01:43:06 DP0 TP0] Decode batch. #running-req: 1, #token: 727, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.39, #queue-req: 0, 
[2025-07-28 01:43:06 DP1 TP0] Decode batch. #running-req: 1, #token: 2746, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.59, #queue-req: 0, 
[2025-07-28 01:43:06 DP0 TP0] Decode batch. #running-req: 1, #token: 767, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.56, #queue-req: 0, 
[2025-07-28 01:43:06 DP1 TP0] Decode batch. #running-req: 1, #token: 2786, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.05, #queue-req: 0, 
[2025-07-28 01:43:07 DP0 TP0] Decode batch. #running-req: 1, #token: 807, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.61, #queue-req: 0, 
[2025-07-28 01:43:07 DP1 TP0] Decode batch. #running-req: 1, #token: 2826, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.74, #queue-req: 0, 
[2025-07-28 01:43:07 DP0 TP0] Decode batch. #running-req: 1, #token: 847, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.45, #queue-req: 0, 
[2025-07-28 01:43:07 DP1 TP0] Decode batch. #running-req: 1, #token: 2866, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.97, #queue-req: 0, 
[2025-07-28 01:43:07 DP0 TP0] Decode batch. #running-req: 1, #token: 887, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.97, #queue-req: 0, 
[2025-07-28 01:43:07 DP1 TP0] Decode batch. #running-req: 1, #token: 2906, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.04, #queue-req: 0, 
[2025-07-28 01:43:07 DP0 TP0] Decode batch. #running-req: 1, #token: 927, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.07, #queue-req: 0, 
[2025-07-28 01:43:07 DP1 TP0] Decode batch. #running-req: 1, #token: 2946, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.46, #queue-req: 0, 
[2025-07-28 01:43:07 DP0 TP0] Decode batch. #running-req: 1, #token: 967, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.70, #queue-req: 0, 
[2025-07-28 01:43:07 DP1 TP0] Decode batch. #running-req: 1, #token: 2986, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.79, #queue-req: 0, 
[2025-07-28 01:43:07 DP0 TP0] Decode batch. #running-req: 1, #token: 1007, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.83, #queue-req: 0, 
[2025-07-28 01:43:07 DP1 TP0] Decode batch. #running-req: 1, #token: 3026, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.20, #queue-req: 0, 
[2025-07-28 01:43:07 DP0 TP0] Decode batch. #running-req: 1, #token: 1047, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.49, #queue-req: 0, 
[2025-07-28 01:43:08 DP1 TP0] Decode batch. #running-req: 1, #token: 3066, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.44, #queue-req: 0, 
[2025-07-28 01:43:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1087, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.09, #queue-req: 0, 
[2025-07-28 01:43:08 DP1 TP0] Decode batch. #running-req: 1, #token: 3106, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.99, #queue-req: 0, 
[2025-07-28 01:43:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1127, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.29, #queue-req: 0, 
[2025-07-28 01:43:08 DP1 TP0] Decode batch. #running-req: 1, #token: 3146, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.90, #queue-req: 0, 
[2025-07-28 01:43:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1167, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.38, #queue-req: 0, 
[2025-07-28 01:43:08 DP1 TP0] Decode batch. #running-req: 1, #token: 3186, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.16, #queue-req: 0, 
[2025-07-28 01:43:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1207, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.87, #queue-req: 0, 
[2025-07-28 01:43:08 DP1 TP0] Decode batch. #running-req: 1, #token: 3226, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.84, #queue-req: 0, 
[2025-07-28 01:43:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1247, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.72, #queue-req: 0, 
[2025-07-28 01:43:08 DP1 TP0] Decode batch. #running-req: 1, #token: 3266, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.31, #queue-req: 0, 
[2025-07-28 01:43:08 DP0 TP0] Decode batch. #running-req: 1, #token: 1287, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.94, #queue-req: 0, 
[2025-07-28 01:43:09 DP1 TP0] Decode batch. #running-req: 1, #token: 3306, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.62, #queue-req: 0, 
[2025-07-28 01:43:09 DP0 TP0] Decode batch. #running-req: 1, #token: 1327, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.10, #queue-req: 0, 
[2025-07-28 01:43:09 DP1 TP0] Decode batch. #running-req: 1, #token: 3346, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.74, #queue-req: 0, 
[2025-07-28 01:43:09 DP0 TP0] Decode batch. #running-req: 1, #token: 1367, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.81, #queue-req: 0, 
[2025-07-28 01:43:09 DP1 TP0] Decode batch. #running-req: 1, #token: 3386, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.71, #queue-req: 0, 
[2025-07-28 01:43:09 DP0 TP0] Decode batch. #running-req: 1, #token: 1407, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.13, #queue-req: 0, 
[2025-07-28 01:43:09 DP1 TP0] Decode batch. #running-req: 1, #token: 3426, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.86, #queue-req: 0, 
[2025-07-28 01:43:09 DP0 TP0] Decode batch. #running-req: 1, #token: 1447, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.84, #queue-req: 0, 
[2025-07-28 01:43:09 DP1 TP0] Decode batch. #running-req: 1, #token: 3466, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.82, #queue-req: 0, 
[2025-07-28 01:43:09 DP0 TP0] Decode batch. #running-req: 1, #token: 1487, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.96, #queue-req: 0, 
[2025-07-28 01:43:09 DP1 TP0] Decode batch. #running-req: 1, #token: 3506, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.28, #queue-req: 0, 
[2025-07-28 01:43:09 DP0 TP0] Decode batch. #running-req: 1, #token: 1527, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.88, #queue-req: 0, 
[2025-07-28 01:43:10 DP1 TP0] Decode batch. #running-req: 1, #token: 3546, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.10, #queue-req: 0, 
[2025-07-28 01:43:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1567, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.72, #queue-req: 0, 
[2025-07-28 01:43:10 DP1 TP0] Decode batch. #running-req: 1, #token: 3586, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.20, #queue-req: 0, 
[2025-07-28 01:43:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1607, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.87, #queue-req: 0, 
[2025-07-28 01:43:10 DP1 TP0] Decode batch. #running-req: 1, #token: 3626, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.60, #queue-req: 0, 
[2025-07-28 01:43:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1647, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.92, #queue-req: 0, 
[2025-07-28 01:43:10 DP1 TP0] Decode batch. #running-req: 1, #token: 3666, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.20, #queue-req: 0, 
[2025-07-28 01:43:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1687, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.74, #queue-req: 0, 
[2025-07-28 01:43:10 DP1 TP0] Decode batch. #running-req: 1, #token: 3706, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.82, #queue-req: 0, 
[2025-07-28 01:43:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1727, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.88, #queue-req: 0, 
[2025-07-28 01:43:10 DP1 TP0] Decode batch. #running-req: 1, #token: 3746, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.74, #queue-req: 0, 
[2025-07-28 01:43:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1767, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.64, #queue-req: 0, 
[2025-07-28 01:43:11 DP1 TP0] Decode batch. #running-req: 1, #token: 3786, token usage: 0.00, cuda graph: True, gen throughput (token/s): 230.04, #queue-req: 0, 
[2025-07-28 01:43:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1807, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.42, #queue-req: 0, 
[2025-07-28 01:43:11 DP1 TP0] Decode batch. #running-req: 1, #token: 3826, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.95, #queue-req: 0, 
[2025-07-28 01:43:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1847, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.69, #queue-req: 0, 
[2025-07-28 01:43:11 DP1 TP0] Decode batch. #running-req: 1, #token: 3866, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.09, #queue-req: 0, 
[2025-07-28 01:43:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1887, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.11, #queue-req: 0, 
[2025-07-28 01:43:11 DP1 TP0] Decode batch. #running-req: 1, #token: 3906, token usage: 0.00, cuda graph: True, gen throughput (token/s): 234.86, #queue-req: 0, 
[2025-07-28 01:43:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1927, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.57, #queue-req: 0, 
[2025-07-28 01:43:11 DP1 TP0] Decode batch. #running-req: 1, #token: 3946, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.03, #queue-req: 0, 
[2025-07-28 01:43:11] INFO:     127.0.0.1:57660 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:43:11 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 151, #cached-token: 89, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:43:12 DP1 TP0] Decode batch. #running-req: 2, #token: 4176, token usage: 0.00, cuda graph: True, gen throughput (token/s): 335.57, #queue-req: 0, 
[2025-07-28 01:43:12 DP1 TP0] Decode batch. #running-req: 2, #token: 4256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 423.30, #queue-req: 0, 
[2025-07-28 01:43:12 DP1 TP0] Decode batch. #running-req: 2, #token: 4336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 458.57, #queue-req: 0, 
[2025-07-28 01:43:12 DP1 TP0] Decode batch. #running-req: 2, #token: 4416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.93, #queue-req: 0, 
[2025-07-28 01:43:12 DP1 TP0] Decode batch. #running-req: 2, #token: 4496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.38, #queue-req: 0, 
[2025-07-28 01:43:12 DP1 TP0] Decode batch. #running-req: 2, #token: 4576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.13, #queue-req: 0, 
[2025-07-28 01:43:13 DP1 TP0] Decode batch. #running-req: 2, #token: 4656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.29, #queue-req: 0, 
[2025-07-28 01:43:13 DP1 TP0] Decode batch. #running-req: 2, #token: 4736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.06, #queue-req: 0, 
[2025-07-28 01:43:13 DP1 TP0] Decode batch. #running-req: 2, #token: 4816, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.58, #queue-req: 0, 
[2025-07-28 01:43:13 DP1 TP0] Decode batch. #running-req: 2, #token: 4896, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.77, #queue-req: 0, 
[2025-07-28 01:43:13 DP1 TP0] Decode batch. #running-req: 2, #token: 4976, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.79, #queue-req: 0, 
[2025-07-28 01:43:13 DP1 TP0] Decode batch. #running-req: 2, #token: 5056, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.46, #queue-req: 0, 
[2025-07-28 01:43:14 DP1 TP0] Decode batch. #running-req: 2, #token: 5136, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.98, #queue-req: 0, 
[2025-07-28 01:43:14 DP1 TP0] Decode batch. #running-req: 2, #token: 5216, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.60, #queue-req: 0, 
[2025-07-28 01:43:14 DP1 TP0] Decode batch. #running-req: 2, #token: 5296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.66, #queue-req: 0, 
[2025-07-28 01:43:14 DP1 TP0] Decode batch. #running-req: 2, #token: 5376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.53, #queue-req: 0, 
[2025-07-28 01:43:14 DP1 TP0] Decode batch. #running-req: 2, #token: 5456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.64, #queue-req: 0, 
[2025-07-28 01:43:14 DP1 TP0] Decode batch. #running-req: 2, #token: 5536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.75, #queue-req: 0, 
[2025-07-28 01:43:15 DP1 TP0] Decode batch. #running-req: 2, #token: 5616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.00, #queue-req: 0, 
[2025-07-28 01:43:15] INFO:     127.0.0.1:57668 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:43:15 DP1 TP0] Decode batch. #running-req: 1, #token: 4746, token usage: 0.00, cuda graph: True, gen throughput (token/s): 455.29, #queue-req: 0, 
[2025-07-28 01:43:15 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 171, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:43:15 DP0 TP0] Decode batch. #running-req: 1, #token: 264, token usage: 0.00, cuda graph: True, gen throughput (token/s): 10.64, #queue-req: 0, 
[2025-07-28 01:43:15 DP1 TP0] Decode batch. #running-req: 1, #token: 4786, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.85, #queue-req: 0, 
[2025-07-28 01:43:15 DP0 TP0] Decode batch. #running-req: 1, #token: 304, token usage: 0.00, cuda graph: True, gen throughput (token/s): 222.22, #queue-req: 0, 
[2025-07-28 01:43:15 DP1 TP0] Decode batch. #running-req: 1, #token: 4826, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.53, #queue-req: 0, 
[2025-07-28 01:43:15 DP0 TP0] Decode batch. #running-req: 1, #token: 344, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.11, #queue-req: 0, 
[2025-07-28 01:43:15 DP1 TP0] Decode batch. #running-req: 1, #token: 4866, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.08, #queue-req: 0, 
[2025-07-28 01:43:15 DP0 TP0] Decode batch. #running-req: 1, #token: 384, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.88, #queue-req: 0, 
[2025-07-28 01:43:15 DP1 TP0] Decode batch. #running-req: 1, #token: 4906, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.21, #queue-req: 0, 
[2025-07-28 01:43:16 DP0 TP0] Decode batch. #running-req: 1, #token: 424, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.57, #queue-req: 0, 
[2025-07-28 01:43:16 DP1 TP0] Decode batch. #running-req: 1, #token: 4946, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.57, #queue-req: 0, 
[2025-07-28 01:43:16 DP0 TP0] Decode batch. #running-req: 1, #token: 464, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.53, #queue-req: 0, 
[2025-07-28 01:43:16 DP1 TP0] Decode batch. #running-req: 1, #token: 4986, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.09, #queue-req: 0, 
[2025-07-28 01:43:16 DP0 TP0] Decode batch. #running-req: 1, #token: 504, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.68, #queue-req: 0, 
[2025-07-28 01:43:16 DP1 TP0] Decode batch. #running-req: 1, #token: 5026, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.40, #queue-req: 0, 
[2025-07-28 01:43:16 DP0 TP0] Decode batch. #running-req: 1, #token: 544, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.98, #queue-req: 0, 
[2025-07-28 01:43:16 DP1 TP0] Decode batch. #running-req: 1, #token: 5066, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.34, #queue-req: 0, 
[2025-07-28 01:43:16 DP0 TP0] Decode batch. #running-req: 1, #token: 584, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.50, #queue-req: 0, 
[2025-07-28 01:43:16 DP1 TP0] Decode batch. #running-req: 1, #token: 5106, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.04, #queue-req: 0, 
[2025-07-28 01:43:16 DP0 TP0] Decode batch. #running-req: 1, #token: 624, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.13, #queue-req: 0, 
[2025-07-28 01:43:16 DP1 TP0] Decode batch. #running-req: 1, #token: 5146, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.81, #queue-req: 0, 
[2025-07-28 01:43:17 DP0 TP0] Decode batch. #running-req: 1, #token: 664, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.48, #queue-req: 0, 
[2025-07-28 01:43:17 DP1 TP0] Decode batch. #running-req: 1, #token: 5186, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.79, #queue-req: 0, 
[2025-07-28 01:43:17 DP0 TP0] Decode batch. #running-req: 1, #token: 704, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.61, #queue-req: 0, 
[2025-07-28 01:43:17 DP1 TP0] Decode batch. #running-req: 1, #token: 5226, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.43, #queue-req: 0, 
[2025-07-28 01:43:17 DP0 TP0] Decode batch. #running-req: 1, #token: 744, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.11, #queue-req: 0, 
[2025-07-28 01:43:17 DP1 TP0] Decode batch. #running-req: 1, #token: 5266, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.02, #queue-req: 0, 
[2025-07-28 01:43:17 DP0 TP0] Decode batch. #running-req: 1, #token: 784, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.18, #queue-req: 0, 
[2025-07-28 01:43:17 DP1 TP0] Decode batch. #running-req: 1, #token: 5306, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.60, #queue-req: 0, 
[2025-07-28 01:43:17 DP0 TP0] Decode batch. #running-req: 1, #token: 824, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.23, #queue-req: 0, 
[2025-07-28 01:43:17 DP1 TP0] Decode batch. #running-req: 1, #token: 5346, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.69, #queue-req: 0, 
[2025-07-28 01:43:17 DP0 TP0] Decode batch. #running-req: 1, #token: 864, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.04, #queue-req: 0, 
[2025-07-28 01:43:17 DP1 TP0] Decode batch. #running-req: 1, #token: 5386, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.67, #queue-req: 0, 
[2025-07-28 01:43:18 DP0 TP0] Decode batch. #running-req: 1, #token: 904, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.28, #queue-req: 0, 
[2025-07-28 01:43:18 DP1 TP0] Decode batch. #running-req: 1, #token: 5426, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.10, #queue-req: 0, 
[2025-07-28 01:43:18 DP0 TP0] Decode batch. #running-req: 1, #token: 944, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.86, #queue-req: 0, 
[2025-07-28 01:43:18 DP1 TP0] Decode batch. #running-req: 1, #token: 5466, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.96, #queue-req: 0, 
[2025-07-28 01:43:18 DP0 TP0] Decode batch. #running-req: 1, #token: 984, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.62, #queue-req: 0, 
[2025-07-28 01:43:18 DP1 TP0] Decode batch. #running-req: 1, #token: 5506, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.19, #queue-req: 0, 
[2025-07-28 01:43:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1024, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.78, #queue-req: 0, 
[2025-07-28 01:43:18 DP1 TP0] Decode batch. #running-req: 1, #token: 5546, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.99, #queue-req: 0, 
[2025-07-28 01:43:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1064, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.42, #queue-req: 0, 
[2025-07-28 01:43:18 DP1 TP0] Decode batch. #running-req: 1, #token: 5586, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.79, #queue-req: 0, 
[2025-07-28 01:43:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1104, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.77, #queue-req: 0, 
[2025-07-28 01:43:18 DP1 TP0] Decode batch. #running-req: 1, #token: 5626, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.82, #queue-req: 0, 
[2025-07-28 01:43:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1144, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.38, #queue-req: 0, 
[2025-07-28 01:43:19 DP1 TP0] Decode batch. #running-req: 1, #token: 5666, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.47, #queue-req: 0, 
[2025-07-28 01:43:19] INFO:     127.0.0.1:53724 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:43:19 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 90, #cached-token: 84, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:43:19 DP1 TP0] Decode batch. #running-req: 2, #token: 5818, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.30, #queue-req: 0, 
[2025-07-28 01:43:19 DP1 TP0] Decode batch. #running-req: 2, #token: 5898, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.44, #queue-req: 0, 
[2025-07-28 01:43:19 DP1 TP0] Decode batch. #running-req: 2, #token: 5978, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.41, #queue-req: 0, 
[2025-07-28 01:43:19 DP1 TP0] Decode batch. #running-req: 2, #token: 6058, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.82, #queue-req: 0, 
[2025-07-28 01:43:19 DP1 TP0] Decode batch. #running-req: 2, #token: 6138, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.26, #queue-req: 0, 
[2025-07-28 01:43:20 DP1 TP0] Decode batch. #running-req: 2, #token: 6218, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.06, #queue-req: 0, 
[2025-07-28 01:43:20 DP1 TP0] Decode batch. #running-req: 2, #token: 6298, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.20, #queue-req: 0, 
[2025-07-28 01:43:20 DP1 TP0] Decode batch. #running-req: 2, #token: 6378, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.00, #queue-req: 0, 
[2025-07-28 01:43:20 DP1 TP0] Decode batch. #running-req: 2, #token: 6458, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.34, #queue-req: 0, 
[2025-07-28 01:43:20 DP1 TP0] Decode batch. #running-req: 2, #token: 6538, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.27, #queue-req: 0, 
[2025-07-28 01:43:21 DP1 TP0] Decode batch. #running-req: 2, #token: 6618, token usage: 0.00, cuda graph: True, gen throughput (token/s): 459.24, #queue-req: 0, 
[2025-07-28 01:43:21 DP1 TP0] Decode batch. #running-req: 2, #token: 6698, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.18, #queue-req: 0, 
[2025-07-28 01:43:21 DP1 TP0] Decode batch. #running-req: 2, #token: 6778, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.33, #queue-req: 0, 
[2025-07-28 01:43:21 DP1 TP0] Decode batch. #running-req: 2, #token: 6858, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.93, #queue-req: 0, 
[2025-07-28 01:43:21 DP1 TP0] Decode batch. #running-req: 2, #token: 6938, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.12, #queue-req: 0, 
[2025-07-28 01:43:21 DP1 TP0] Decode batch. #running-req: 2, #token: 7018, token usage: 0.00, cuda graph: True, gen throughput (token/s): 459.42, #queue-req: 0, 
[2025-07-28 01:43:22 DP1 TP0] Decode batch. #running-req: 2, #token: 7098, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.20, #queue-req: 0, 
[2025-07-28 01:43:22 DP1 TP0] Decode batch. #running-req: 2, #token: 7178, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.19, #queue-req: 0, 
[2025-07-28 01:43:22 DP1 TP0] Decode batch. #running-req: 2, #token: 7258, token usage: 0.00, cuda graph: True, gen throughput (token/s): 459.54, #queue-req: 0, 
[2025-07-28 01:43:22 DP1 TP0] Decode batch. #running-req: 2, #token: 7338, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.20, #queue-req: 0, 
[2025-07-28 01:43:22 DP1 TP0] Decode batch. #running-req: 2, #token: 7418, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.00, #queue-req: 0, 
[2025-07-28 01:43:22 DP1 TP0] Decode batch. #running-req: 2, #token: 7498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.89, #queue-req: 0, 
[2025-07-28 01:43:23 DP1 TP0] Decode batch. #running-req: 2, #token: 7578, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.21, #queue-req: 0, 
[2025-07-28 01:43:23 DP1 TP0] Decode batch. #running-req: 2, #token: 7658, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.17, #queue-req: 0, 
[2025-07-28 01:43:23 DP1 TP0] Decode batch. #running-req: 2, #token: 7738, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.57, #queue-req: 0, 
[2025-07-28 01:43:23 DP1 TP0] Decode batch. #running-req: 2, #token: 7818, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.69, #queue-req: 0, 
[2025-07-28 01:43:23 DP1 TP0] Decode batch. #running-req: 2, #token: 7898, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.80, #queue-req: 0, 
[2025-07-28 01:43:23 DP1 TP0] Decode batch. #running-req: 2, #token: 7978, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.44, #queue-req: 0, 
[2025-07-28 01:43:24 DP1 TP0] Decode batch. #running-req: 2, #token: 8058, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.13, #queue-req: 0, 
[2025-07-28 01:43:24 DP1 TP0] Decode batch. #running-req: 2, #token: 8138, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.03, #queue-req: 0, 
[2025-07-28 01:43:24 DP1 TP0] Decode batch. #running-req: 2, #token: 8218, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.91, #queue-req: 0, 
[2025-07-28 01:43:24 DP1 TP0] Decode batch. #running-req: 2, #token: 8298, token usage: 0.00, cuda graph: True, gen throughput (token/s): 459.64, #queue-req: 0, 
[2025-07-28 01:43:24 DP1 TP0] Decode batch. #running-req: 2, #token: 8378, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.03, #queue-req: 0, 
[2025-07-28 01:43:25 DP1 TP0] Decode batch. #running-req: 2, #token: 8458, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.15, #queue-req: 0, 
[2025-07-28 01:43:25 DP1 TP0] Decode batch. #running-req: 2, #token: 8538, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.86, #queue-req: 0, 
[2025-07-28 01:43:25 DP1 TP0] Decode batch. #running-req: 2, #token: 8618, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.49, #queue-req: 0, 
[2025-07-28 01:43:25 DP1 TP0] Decode batch. #running-req: 2, #token: 8698, token usage: 0.00, cuda graph: True, gen throughput (token/s): 458.68, #queue-req: 0, 
[2025-07-28 01:43:25 DP1 TP0] Decode batch. #running-req: 2, #token: 8778, token usage: 0.00, cuda graph: True, gen throughput (token/s): 458.20, #queue-req: 0, 
[2025-07-28 01:43:25 DP1 TP0] Decode batch. #running-req: 2, #token: 8858, token usage: 0.00, cuda graph: True, gen throughput (token/s): 459.43, #queue-req: 0, 
[2025-07-28 01:43:26 DP1 TP0] Decode batch. #running-req: 2, #token: 8938, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.05, #queue-req: 0, 
[2025-07-28 01:43:26 DP1 TP0] Decode batch. #running-req: 2, #token: 9018, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.49, #queue-req: 0, 
[2025-07-28 01:43:26 DP1 TP0] Decode batch. #running-req: 2, #token: 9098, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.48, #queue-req: 0, 
[2025-07-28 01:43:26 DP1 TP0] Decode batch. #running-req: 2, #token: 9178, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.19, #queue-req: 0, 
[2025-07-28 01:43:26 DP1 TP0] Decode batch. #running-req: 2, #token: 9258, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.33, #queue-req: 0, 
[2025-07-28 01:43:26 DP1 TP0] Decode batch. #running-req: 2, #token: 9338, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.51, #queue-req: 0, 
[2025-07-28 01:43:27 DP1 TP0] Decode batch. #running-req: 2, #token: 9418, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.60, #queue-req: 0, 
[2025-07-28 01:43:27 DP1 TP0] Decode batch. #running-req: 2, #token: 9498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.50, #queue-req: 0, 
[2025-07-28 01:43:27 DP1 TP0] Decode batch. #running-req: 2, #token: 9578, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.11, #queue-req: 0, 
[2025-07-28 01:43:27 DP1 TP0] Decode batch. #running-req: 2, #token: 9658, token usage: 0.00, cuda graph: True, gen throughput (token/s): 459.57, #queue-req: 0, 
[2025-07-28 01:43:27] INFO:     127.0.0.1:53738 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:43:27 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 111, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:43:27 DP1 TP0] Decode batch. #running-req: 1, #token: 7666, token usage: 0.00, cuda graph: True, gen throughput (token/s): 321.24, #queue-req: 0, 
[2025-07-28 01:43:27 DP0 TP0] Decode batch. #running-req: 1, #token: 204, token usage: 0.00, cuda graph: True, gen throughput (token/s): 4.56, #queue-req: 0, 
[2025-07-28 01:43:27 DP1 TP0] Decode batch. #running-req: 1, #token: 7706, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.07, #queue-req: 0, 
[2025-07-28 01:43:27 DP0 TP0] Decode batch. #running-req: 1, #token: 244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 221.29, #queue-req: 0, 
[2025-07-28 01:43:28 DP1 TP0] Decode batch. #running-req: 1, #token: 7746, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.17, #queue-req: 0, 
[2025-07-28 01:43:28 DP0 TP0] Decode batch. #running-req: 1, #token: 284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.33, #queue-req: 0, 
[2025-07-28 01:43:28 DP1 TP0] Decode batch. #running-req: 1, #token: 7786, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.01, #queue-req: 0, 
[2025-07-28 01:43:28 DP0 TP0] Decode batch. #running-req: 1, #token: 324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.75, #queue-req: 0, 
[2025-07-28 01:43:28 DP1 TP0] Decode batch. #running-req: 1, #token: 7826, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.22, #queue-req: 0, 
[2025-07-28 01:43:28 DP0 TP0] Decode batch. #running-req: 1, #token: 364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.25, #queue-req: 0, 
[2025-07-28 01:43:28 DP1 TP0] Decode batch. #running-req: 1, #token: 7866, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.08, #queue-req: 0, 
[2025-07-28 01:43:28 DP0 TP0] Decode batch. #running-req: 1, #token: 404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.33, #queue-req: 0, 
[2025-07-28 01:43:28 DP1 TP0] Decode batch. #running-req: 1, #token: 7906, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.57, #queue-req: 0, 
[2025-07-28 01:43:28 DP0 TP0] Decode batch. #running-req: 1, #token: 444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.09, #queue-req: 0, 
[2025-07-28 01:43:28 DP1 TP0] Decode batch. #running-req: 1, #token: 7946, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.41, #queue-req: 0, 
[2025-07-28 01:43:28 DP0 TP0] Decode batch. #running-req: 1, #token: 484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.27, #queue-req: 0, 
[2025-07-28 01:43:29 DP1 TP0] Decode batch. #running-req: 1, #token: 7986, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.61, #queue-req: 0, 
[2025-07-28 01:43:29 DP0 TP0] Decode batch. #running-req: 1, #token: 524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.08, #queue-req: 0, 
[2025-07-28 01:43:29 DP1 TP0] Decode batch. #running-req: 1, #token: 8026, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.17, #queue-req: 0, 
[2025-07-28 01:43:29 DP0 TP0] Decode batch. #running-req: 1, #token: 564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.18, #queue-req: 0, 
[2025-07-28 01:43:29 DP1 TP0] Decode batch. #running-req: 1, #token: 8066, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.79, #queue-req: 0, 
[2025-07-28 01:43:29 DP0 TP0] Decode batch. #running-req: 1, #token: 604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.18, #queue-req: 0, 
[2025-07-28 01:43:29 DP0 TP0] Decode batch. #running-req: 1, #token: 644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.81, #queue-req: 0, 
[2025-07-28 01:43:29 DP1 TP0] Decode batch. #running-req: 1, #token: 8106, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.81, #queue-req: 0, 
[2025-07-28 01:43:29 DP0 TP0] Decode batch. #running-req: 1, #token: 684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.69, #queue-req: 0, 
[2025-07-28 01:43:29 DP1 TP0] Decode batch. #running-req: 1, #token: 8146, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.69, #queue-req: 0, 
[2025-07-28 01:43:29 DP0 TP0] Decode batch. #running-req: 1, #token: 724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.58, #queue-req: 0, 
[2025-07-28 01:43:29 DP1 TP0] Decode batch. #running-req: 1, #token: 8186, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.79, #queue-req: 0, 
[2025-07-28 01:43:30 DP0 TP0] Decode batch. #running-req: 1, #token: 764, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.59, #queue-req: 0, 
[2025-07-28 01:43:30 DP1 TP0] Decode batch. #running-req: 1, #token: 8226, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.72, #queue-req: 0, 
[2025-07-28 01:43:30 DP0 TP0] Decode batch. #running-req: 1, #token: 804, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.69, #queue-req: 0, 
[2025-07-28 01:43:30 DP1 TP0] Decode batch. #running-req: 1, #token: 8266, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.05, #queue-req: 0, 
[2025-07-28 01:43:30 DP0 TP0] Decode batch. #running-req: 1, #token: 844, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.79, #queue-req: 0, 
[2025-07-28 01:43:30] INFO:     127.0.0.1:45690 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:43:30 DP1 TP0] Decode batch. #running-req: 1, #token: 8306, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.72, #queue-req: 0, 
[2025-07-28 01:43:30 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 146, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:43:30 DP1 TP0] Decode batch. #running-req: 2, #token: 8533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 364.71, #queue-req: 0, 
[2025-07-28 01:43:30 DP1 TP0] Decode batch. #running-req: 2, #token: 8613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.02, #queue-req: 0, 
[2025-07-28 01:43:30 DP1 TP0] Decode batch. #running-req: 2, #token: 8693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 457.28, #queue-req: 0, 
[2025-07-28 01:43:31 DP1 TP0] Decode batch. #running-req: 2, #token: 8773, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.08, #queue-req: 0, 
[2025-07-28 01:43:31] INFO:     127.0.0.1:43634 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:43:31 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:43:31 DP1 TP0] Decode batch. #running-req: 1, #token: 431, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.18, #queue-req: 0, 
[2025-07-28 01:43:31 DP0 TP0] Decode batch. #running-req: 1, #token: 266, token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.42, #queue-req: 0, 
[2025-07-28 01:43:31 DP1 TP0] Decode batch. #running-req: 1, #token: 471, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.40, #queue-req: 0, 
[2025-07-28 01:43:31 DP0 TP0] Decode batch. #running-req: 1, #token: 306, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.26, #queue-req: 0, 
[2025-07-28 01:43:31 DP1 TP0] Decode batch. #running-req: 1, #token: 511, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.24, #queue-req: 0, 
[2025-07-28 01:43:31 DP0 TP0] Decode batch. #running-req: 1, #token: 346, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.64, #queue-req: 0, 
[2025-07-28 01:43:31 DP1 TP0] Decode batch. #running-req: 1, #token: 551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.12, #queue-req: 0, 
[2025-07-28 01:43:31 DP0 TP0] Decode batch. #running-req: 1, #token: 386, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.84, #queue-req: 0, 
[2025-07-28 01:43:31 DP1 TP0] Decode batch. #running-req: 1, #token: 591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.88, #queue-req: 0, 
[2025-07-28 01:43:32 DP0 TP0] Decode batch. #running-req: 1, #token: 426, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.63, #queue-req: 0, 
[2025-07-28 01:43:32 DP1 TP0] Decode batch. #running-req: 1, #token: 631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.77, #queue-req: 0, 
[2025-07-28 01:43:32 DP0 TP0] Decode batch. #running-req: 1, #token: 466, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.26, #queue-req: 0, 
[2025-07-28 01:43:32 DP1 TP0] Decode batch. #running-req: 1, #token: 671, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.50, #queue-req: 0, 
[2025-07-28 01:43:32 DP0 TP0] Decode batch. #running-req: 1, #token: 506, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.33, #queue-req: 0, 
[2025-07-28 01:43:32 DP1 TP0] Decode batch. #running-req: 1, #token: 711, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.77, #queue-req: 0, 
[2025-07-28 01:43:32 DP0 TP0] Decode batch. #running-req: 1, #token: 546, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.20, #queue-req: 0, 
[2025-07-28 01:43:32 DP1 TP0] Decode batch. #running-req: 1, #token: 751, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.77, #queue-req: 0, 
[2025-07-28 01:43:32 DP0 TP0] Decode batch. #running-req: 1, #token: 586, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.84, #queue-req: 0, 
[2025-07-28 01:43:32 DP1 TP0] Decode batch. #running-req: 1, #token: 791, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.61, #queue-req: 0, 
[2025-07-28 01:43:32 DP0 TP0] Decode batch. #running-req: 1, #token: 626, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.25, #queue-req: 0, 
[2025-07-28 01:43:32 DP1 TP0] Decode batch. #running-req: 1, #token: 831, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.71, #queue-req: 0, 
[2025-07-28 01:43:33 DP0 TP0] Decode batch. #running-req: 1, #token: 666, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.83, #queue-req: 0, 
[2025-07-28 01:43:33 DP1 TP0] Decode batch. #running-req: 1, #token: 871, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.70, #queue-req: 0, 
[2025-07-28 01:43:33 DP0 TP0] Decode batch. #running-req: 1, #token: 706, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.05, #queue-req: 0, 
[2025-07-28 01:43:33 DP1 TP0] Decode batch. #running-req: 1, #token: 911, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.52, #queue-req: 0, 
[2025-07-28 01:43:33 DP0 TP0] Decode batch. #running-req: 1, #token: 746, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.83, #queue-req: 0, 
[2025-07-28 01:43:33 DP1 TP0] Decode batch. #running-req: 1, #token: 951, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.46, #queue-req: 0, 
[2025-07-28 01:43:33 DP0 TP0] Decode batch. #running-req: 1, #token: 786, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.03, #queue-req: 0, 
[2025-07-28 01:43:33 DP1 TP0] Decode batch. #running-req: 1, #token: 991, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.47, #queue-req: 0, 
[2025-07-28 01:43:33 DP0 TP0] Decode batch. #running-req: 1, #token: 826, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.45, #queue-req: 0, 
[2025-07-28 01:43:33 DP1 TP0] Decode batch. #running-req: 1, #token: 1031, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.80, #queue-req: 0, 
[2025-07-28 01:43:33] INFO:     127.0.0.1:45692 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:43:33 DP0 TP0] Decode batch. #running-req: 1, #token: 866, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.86, #queue-req: 0, 
[2025-07-28 01:43:33 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 156, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:43:33 DP1 TP0] Decode batch. #running-req: 1, #token: 264, token usage: 0.00, cuda graph: True, gen throughput (token/s): 187.00, #queue-req: 0, 
[2025-07-28 01:43:33 DP0 TP0] Decode batch. #running-req: 1, #token: 906, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.03, #queue-req: 0, 
[2025-07-28 01:43:34 DP1 TP0] Decode batch. #running-req: 1, #token: 304, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.02, #queue-req: 0, 
[2025-07-28 01:43:34 DP0 TP0] Decode batch. #running-req: 1, #token: 946, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.85, #queue-req: 0, 
[2025-07-28 01:43:34 DP1 TP0] Decode batch. #running-req: 1, #token: 344, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.38, #queue-req: 0, 
[2025-07-28 01:43:34 DP0 TP0] Decode batch. #running-req: 1, #token: 986, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.77, #queue-req: 0, 
[2025-07-28 01:43:34 DP1 TP0] Decode batch. #running-req: 1, #token: 384, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.29, #queue-req: 0, 
[2025-07-28 01:43:34 DP0 TP0] Decode batch. #running-req: 1, #token: 1026, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.29, #queue-req: 0, 
[2025-07-28 01:43:34 DP1 TP0] Decode batch. #running-req: 1, #token: 424, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.49, #queue-req: 0, 
[2025-07-28 01:43:34 DP0 TP0] Decode batch. #running-req: 1, #token: 1066, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.08, #queue-req: 0, 
[2025-07-28 01:43:34 DP1 TP0] Decode batch. #running-req: 1, #token: 464, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.46, #queue-req: 0, 
[2025-07-28 01:43:34 DP0 TP0] Decode batch. #running-req: 1, #token: 1106, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.17, #queue-req: 0, 
[2025-07-28 01:43:34] INFO:     127.0.0.1:45702 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:43:34 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 110, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:43:34 DP1 TP0] Decode batch. #running-req: 1, #token: 504, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.55, #queue-req: 0, 
[2025-07-28 01:43:35 DP0 TP0] Decode batch. #running-req: 1, #token: 215, token usage: 0.00, cuda graph: True, gen throughput (token/s): 193.04, #queue-req: 0, 
[2025-07-28 01:43:35 DP1 TP0] Decode batch. #running-req: 1, #token: 544, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.58, #queue-req: 0, 
[2025-07-28 01:43:35 DP0 TP0] Decode batch. #running-req: 1, #token: 255, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.72, #queue-req: 0, 
[2025-07-28 01:43:35 DP1 TP0] Decode batch. #running-req: 1, #token: 584, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.71, #queue-req: 0, 
[2025-07-28 01:43:35 DP0 TP0] Decode batch. #running-req: 1, #token: 295, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.41, #queue-req: 0, 
[2025-07-28 01:43:35 DP1 TP0] Decode batch. #running-req: 1, #token: 624, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.09, #queue-req: 0, 
[2025-07-28 01:43:35 DP0 TP0] Decode batch. #running-req: 1, #token: 335, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.84, #queue-req: 0, 
[2025-07-28 01:43:35 DP1 TP0] Decode batch. #running-req: 1, #token: 664, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.85, #queue-req: 0, 
[2025-07-28 01:43:35 DP0 TP0] Decode batch. #running-req: 1, #token: 375, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.46, #queue-req: 0, 
[2025-07-28 01:43:35 DP1 TP0] Decode batch. #running-req: 1, #token: 704, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.00, #queue-req: 0, 
[2025-07-28 01:43:35 DP0 TP0] Decode batch. #running-req: 1, #token: 415, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.45, #queue-req: 0, 
[2025-07-28 01:43:35 DP1 TP0] Decode batch. #running-req: 1, #token: 744, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.10, #queue-req: 0, 
[2025-07-28 01:43:35 DP0 TP0] Decode batch. #running-req: 1, #token: 455, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.16, #queue-req: 0, 
[2025-07-28 01:43:36 DP1 TP0] Decode batch. #running-req: 1, #token: 784, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.05, #queue-req: 0, 
[2025-07-28 01:43:36 DP0 TP0] Decode batch. #running-req: 1, #token: 495, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.99, #queue-req: 0, 
[2025-07-28 01:43:36 DP1 TP0] Decode batch. #running-req: 1, #token: 824, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.15, #queue-req: 0, 
[2025-07-28 01:43:36 DP0 TP0] Decode batch. #running-req: 1, #token: 535, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.14, #queue-req: 0, 
[2025-07-28 01:43:36 DP1 TP0] Decode batch. #running-req: 1, #token: 864, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.91, #queue-req: 0, 
[2025-07-28 01:43:36 DP0 TP0] Decode batch. #running-req: 1, #token: 575, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.05, #queue-req: 0, 
[2025-07-28 01:43:36 DP1 TP0] Decode batch. #running-req: 1, #token: 904, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.05, #queue-req: 0, 
[2025-07-28 01:43:36 DP0 TP0] Decode batch. #running-req: 1, #token: 615, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.82, #queue-req: 0, 
[2025-07-28 01:43:36 DP1 TP0] Decode batch. #running-req: 1, #token: 944, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.78, #queue-req: 0, 
[2025-07-28 01:43:36 DP0 TP0] Decode batch. #running-req: 1, #token: 655, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.75, #queue-req: 0, 
[2025-07-28 01:43:36 DP1 TP0] Decode batch. #running-req: 1, #token: 984, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.95, #queue-req: 0, 
[2025-07-28 01:43:36 DP0 TP0] Decode batch. #running-req: 1, #token: 695, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.91, #queue-req: 0, 
[2025-07-28 01:43:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1024, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.85, #queue-req: 0, 
[2025-07-28 01:43:37 DP0 TP0] Decode batch. #running-req: 1, #token: 735, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.73, #queue-req: 0, 
[2025-07-28 01:43:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1064, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.36, #queue-req: 0, 
[2025-07-28 01:43:37 DP0 TP0] Decode batch. #running-req: 1, #token: 775, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.46, #queue-req: 0, 
[2025-07-28 01:43:37] INFO:     127.0.0.1:33932 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:43:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1104, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.34, #queue-req: 0, 
[2025-07-28 01:43:37 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 76, #cached-token: 84, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:43:37 DP1 TP0] Decode batch. #running-req: 2, #token: 1258, token usage: 0.00, cuda graph: True, gen throughput (token/s): 371.25, #queue-req: 0, 
[2025-07-28 01:43:37 DP1 TP0] Decode batch. #running-req: 2, #token: 1338, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.15, #queue-req: 0, 
[2025-07-28 01:43:37 DP1 TP0] Decode batch. #running-req: 2, #token: 1418, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.32, #queue-req: 0, 
[2025-07-28 01:43:38 DP1 TP0] Decode batch. #running-req: 2, #token: 1498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 468.16, #queue-req: 0, 
[2025-07-28 01:43:38 DP1 TP0] Decode batch. #running-req: 2, #token: 1578, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.01, #queue-req: 0, 
[2025-07-28 01:43:38 DP1 TP0] Decode batch. #running-req: 2, #token: 1658, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.01, #queue-req: 0, 
[2025-07-28 01:43:38 DP1 TP0] Decode batch. #running-req: 2, #token: 1738, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.08, #queue-req: 0, 
[2025-07-28 01:43:38 DP1 TP0] Decode batch. #running-req: 2, #token: 1818, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.07, #queue-req: 0, 
[2025-07-28 01:43:38 DP1 TP0] Decode batch. #running-req: 2, #token: 1898, token usage: 0.00, cuda graph: True, gen throughput (token/s): 470.36, #queue-req: 0, 
[2025-07-28 01:43:39 DP1 TP0] Decode batch. #running-req: 2, #token: 1978, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.90, #queue-req: 0, 
[2025-07-28 01:43:39 DP1 TP0] Decode batch. #running-req: 2, #token: 2058, token usage: 0.00, cuda graph: True, gen throughput (token/s): 468.51, #queue-req: 0, 
[2025-07-28 01:43:39 DP1 TP0] Decode batch. #running-req: 2, #token: 2138, token usage: 0.00, cuda graph: True, gen throughput (token/s): 468.48, #queue-req: 0, 
[2025-07-28 01:43:39 DP1 TP0] Decode batch. #running-req: 2, #token: 2218, token usage: 0.00, cuda graph: True, gen throughput (token/s): 470.21, #queue-req: 0, 
[2025-07-28 01:43:39 DP1 TP0] Decode batch. #running-req: 2, #token: 2298, token usage: 0.00, cuda graph: True, gen throughput (token/s): 470.31, #queue-req: 0, 
[2025-07-28 01:43:39] INFO:     127.0.0.1:33934 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:43:39 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 132, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:43:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1704, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.22, #queue-req: 0, 
[2025-07-28 01:43:39 DP0 TP0] Decode batch. #running-req: 1, #token: 237, token usage: 0.00, cuda graph: True, gen throughput (token/s): 14.79, #queue-req: 0, 
[2025-07-28 01:43:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1744, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.98, #queue-req: 0, 
[2025-07-28 01:43:40 DP0 TP0] Decode batch. #running-req: 1, #token: 277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 223.08, #queue-req: 0, 
[2025-07-28 01:43:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1784, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.06, #queue-req: 0, 
[2025-07-28 01:43:40 DP0 TP0] Decode batch. #running-req: 1, #token: 317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.42, #queue-req: 0, 
[2025-07-28 01:43:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1824, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.90, #queue-req: 0, 
[2025-07-28 01:43:40 DP0 TP0] Decode batch. #running-req: 1, #token: 357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.35, #queue-req: 0, 
[2025-07-28 01:43:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1864, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.34, #queue-req: 0, 
[2025-07-28 01:43:40 DP0 TP0] Decode batch. #running-req: 1, #token: 397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.56, #queue-req: 0, 
[2025-07-28 01:43:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1904, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.24, #queue-req: 0, 
[2025-07-28 01:43:40 DP0 TP0] Decode batch. #running-req: 1, #token: 437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.69, #queue-req: 0, 
[2025-07-28 01:43:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1944, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.05, #queue-req: 0, 
[2025-07-28 01:43:40 DP0 TP0] Decode batch. #running-req: 1, #token: 477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.36, #queue-req: 0, 
[2025-07-28 01:43:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1984, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.06, #queue-req: 0, 
[2025-07-28 01:43:41 DP0 TP0] Decode batch. #running-req: 1, #token: 517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.41, #queue-req: 0, 
[2025-07-28 01:43:41 DP1 TP0] Decode batch. #running-req: 1, #token: 2024, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.93, #queue-req: 0, 
[2025-07-28 01:43:41 DP0 TP0] Decode batch. #running-req: 1, #token: 557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.34, #queue-req: 0, 
[2025-07-28 01:43:41 DP1 TP0] Decode batch. #running-req: 1, #token: 2064, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.91, #queue-req: 0, 
[2025-07-28 01:43:41 DP0 TP0] Decode batch. #running-req: 1, #token: 597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.62, #queue-req: 0, 
[2025-07-28 01:43:41] INFO:     127.0.0.1:33920 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:43:41 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 133, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:43:41 DP0 TP0] Decode batch. #running-req: 1, #token: 637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.03, #queue-req: 0, 
[2025-07-28 01:43:41 DP1 TP0] Decode batch. #running-req: 1, #token: 240, token usage: 0.00, cuda graph: True, gen throughput (token/s): 181.50, #queue-req: 0, 
[2025-07-28 01:43:41 DP0 TP0] Decode batch. #running-req: 1, #token: 677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.23, #queue-req: 0, 
[2025-07-28 01:43:41 DP1 TP0] Decode batch. #running-req: 1, #token: 280, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.47, #queue-req: 0, 
[2025-07-28 01:43:41 DP0 TP0] Decode batch. #running-req: 1, #token: 717, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.65, #queue-req: 0, 
[2025-07-28 01:43:41 DP1 TP0] Decode batch. #running-req: 1, #token: 320, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.85, #queue-req: 0, 
[2025-07-28 01:43:42 DP0 TP0] Decode batch. #running-req: 1, #token: 757, token usage: 0.00, cuda graph: True, gen throughput (token/s): 249.70, #queue-req: 0, 
[2025-07-28 01:43:42 DP1 TP0] Decode batch. #running-req: 1, #token: 360, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.49, #queue-req: 0, 
[2025-07-28 01:43:42 DP0 TP0] Decode batch. #running-req: 1, #token: 797, token usage: 0.00, cuda graph: True, gen throughput (token/s): 249.31, #queue-req: 0, 
[2025-07-28 01:43:42 DP1 TP0] Decode batch. #running-req: 1, #token: 400, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.85, #queue-req: 0, 
[2025-07-28 01:43:42 DP0 TP0] Decode batch. #running-req: 1, #token: 837, token usage: 0.00, cuda graph: True, gen throughput (token/s): 233.43, #queue-req: 0, 
[2025-07-28 01:43:42 DP1 TP0] Decode batch. #running-req: 1, #token: 440, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.11, #queue-req: 0, 
[2025-07-28 01:43:42 DP0 TP0] Decode batch. #running-req: 1, #token: 877, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.13, #queue-req: 0, 
[2025-07-28 01:43:42 DP1 TP0] Decode batch. #running-req: 1, #token: 480, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.40, #queue-req: 0, 
[2025-07-28 01:43:42 DP0 TP0] Decode batch. #running-req: 1, #token: 917, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.00, #queue-req: 0, 
[2025-07-28 01:43:42 DP1 TP0] Decode batch. #running-req: 1, #token: 520, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.17, #queue-req: 0, 
[2025-07-28 01:43:42 DP0 TP0] Decode batch. #running-req: 1, #token: 957, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.07, #queue-req: 0, 
[2025-07-28 01:43:42 DP1 TP0] Decode batch. #running-req: 1, #token: 560, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.06, #queue-req: 0, 
[2025-07-28 01:43:43 DP0 TP0] Decode batch. #running-req: 1, #token: 997, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.08, #queue-req: 0, 
[2025-07-28 01:43:43 DP1 TP0] Decode batch. #running-req: 1, #token: 600, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.60, #queue-req: 0, 
[2025-07-28 01:43:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1037, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.63, #queue-req: 0, 
[2025-07-28 01:43:43 DP1 TP0] Decode batch. #running-req: 1, #token: 640, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.31, #queue-req: 0, 
[2025-07-28 01:43:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1077, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.19, #queue-req: 0, 
[2025-07-28 01:43:43 DP1 TP0] Decode batch. #running-req: 1, #token: 680, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.20, #queue-req: 0, 
[2025-07-28 01:43:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1117, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.03, #queue-req: 0, 
[2025-07-28 01:43:43 DP1 TP0] Decode batch. #running-req: 1, #token: 720, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.46, #queue-req: 0, 
[2025-07-28 01:43:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1157, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.43, #queue-req: 0, 
[2025-07-28 01:43:43 DP1 TP0] Decode batch. #running-req: 1, #token: 760, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.77, #queue-req: 0, 
[2025-07-28 01:43:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1197, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.94, #queue-req: 0, 
[2025-07-28 01:43:43 DP1 TP0] Decode batch. #running-req: 1, #token: 800, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.65, #queue-req: 0, 
[2025-07-28 01:43:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1237, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.67, #queue-req: 0, 
[2025-07-28 01:43:44 DP1 TP0] Decode batch. #running-req: 1, #token: 840, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.51, #queue-req: 0, 
[2025-07-28 01:43:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.48, #queue-req: 0, 
[2025-07-28 01:43:44 DP1 TP0] Decode batch. #running-req: 1, #token: 880, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.54, #queue-req: 0, 
[2025-07-28 01:43:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.63, #queue-req: 0, 
[2025-07-28 01:43:44 DP1 TP0] Decode batch. #running-req: 1, #token: 920, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.88, #queue-req: 0, 
[2025-07-28 01:43:44] INFO:     127.0.0.1:33960 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:43:44 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 81, #cached-token: 84, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:43:44 DP0 TP0] Decode batch. #running-req: 2, #token: 1471, token usage: 0.00, cuda graph: True, gen throughput (token/s): 346.84, #queue-req: 0, 
[2025-07-28 01:43:44 DP0 TP0] Decode batch. #running-req: 2, #token: 1551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 470.55, #queue-req: 0, 
[2025-07-28 01:43:44 DP0 TP0] Decode batch. #running-req: 2, #token: 1631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 470.80, #queue-req: 0, 
[2025-07-28 01:43:45 DP0 TP0] Decode batch. #running-req: 2, #token: 1711, token usage: 0.00, cuda graph: True, gen throughput (token/s): 470.52, #queue-req: 0, 
[2025-07-28 01:43:45 DP0 TP0] Decode batch. #running-req: 2, #token: 1791, token usage: 0.00, cuda graph: True, gen throughput (token/s): 470.31, #queue-req: 0, 
[2025-07-28 01:43:45 DP0 TP0] Decode batch. #running-req: 2, #token: 1871, token usage: 0.00, cuda graph: True, gen throughput (token/s): 470.11, #queue-req: 0, 
[2025-07-28 01:43:45 DP0 TP0] Decode batch. #running-req: 2, #token: 1951, token usage: 0.00, cuda graph: True, gen throughput (token/s): 470.51, #queue-req: 0, 
[2025-07-28 01:43:45 DP0 TP0] Decode batch. #running-req: 2, #token: 2031, token usage: 0.00, cuda graph: True, gen throughput (token/s): 472.02, #queue-req: 0, 
[2025-07-28 01:43:45 DP0 TP0] Decode batch. #running-req: 2, #token: 2111, token usage: 0.00, cuda graph: True, gen throughput (token/s): 473.24, #queue-req: 0, 
[2025-07-28 01:43:46 DP0 TP0] Decode batch. #running-req: 2, #token: 2191, token usage: 0.00, cuda graph: True, gen throughput (token/s): 473.80, #queue-req: 0, 
[2025-07-28 01:43:46 DP0 TP0] Decode batch. #running-req: 2, #token: 2271, token usage: 0.00, cuda graph: True, gen throughput (token/s): 473.69, #queue-req: 0, 
[2025-07-28 01:43:46 DP0 TP0] Decode batch. #running-req: 2, #token: 2351, token usage: 0.00, cuda graph: True, gen throughput (token/s): 473.24, #queue-req: 0, 
[2025-07-28 01:43:46 DP0 TP0] Decode batch. #running-req: 2, #token: 2431, token usage: 0.00, cuda graph: True, gen throughput (token/s): 473.78, #queue-req: 0, 
[2025-07-28 01:43:46 DP0 TP0] Decode batch. #running-req: 2, #token: 2511, token usage: 0.00, cuda graph: True, gen throughput (token/s): 472.70, #queue-req: 0, 
[2025-07-28 01:43:46 DP0 TP0] Decode batch. #running-req: 2, #token: 2591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 471.56, #queue-req: 0, 
[2025-07-28 01:43:47 DP0 TP0] Decode batch. #running-req: 2, #token: 2671, token usage: 0.00, cuda graph: True, gen throughput (token/s): 472.40, #queue-req: 0, 
[2025-07-28 01:43:47 DP0 TP0] Decode batch. #running-req: 2, #token: 2751, token usage: 0.00, cuda graph: True, gen throughput (token/s): 471.57, #queue-req: 0, 
[2025-07-28 01:43:47 DP0 TP0] Decode batch. #running-req: 2, #token: 2831, token usage: 0.00, cuda graph: True, gen throughput (token/s): 471.62, #queue-req: 0, 
[2025-07-28 01:43:47 DP0 TP0] Decode batch. #running-req: 2, #token: 2911, token usage: 0.00, cuda graph: True, gen throughput (token/s): 470.32, #queue-req: 0, 
[2025-07-28 01:43:47 DP0 TP0] Decode batch. #running-req: 2, #token: 2991, token usage: 0.00, cuda graph: True, gen throughput (token/s): 470.06, #queue-req: 0, 
[2025-07-28 01:43:47 DP0 TP0] Decode batch. #running-req: 2, #token: 3071, token usage: 0.00, cuda graph: True, gen throughput (token/s): 470.02, #queue-req: 0, 
[2025-07-28 01:43:48 DP0 TP0] Decode batch. #running-req: 2, #token: 3151, token usage: 0.00, cuda graph: True, gen throughput (token/s): 470.32, #queue-req: 0, 
[2025-07-28 01:43:48 DP0 TP0] Decode batch. #running-req: 2, #token: 3231, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.14, #queue-req: 0, 
[2025-07-28 01:43:48 DP0 TP0] Decode batch. #running-req: 2, #token: 3311, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.92, #queue-req: 0, 
[2025-07-28 01:43:48] INFO:     127.0.0.1:33946 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:43:48 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 153, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:43:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1158, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.79, #queue-req: 0, 
[2025-07-28 01:43:48 DP1 TP0] Decode batch. #running-req: 1, #token: 276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 9.12, #queue-req: 0, 
[2025-07-28 01:43:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1198, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.33, #queue-req: 0, 
[2025-07-28 01:43:48 DP1 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.21, #queue-req: 0, 
[2025-07-28 01:43:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1238, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.10, #queue-req: 0, 
[2025-07-28 01:43:49 DP1 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.15, #queue-req: 0, 
[2025-07-28 01:43:49 DP0 TP0] Decode batch. #running-req: 1, #token: 1278, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.57, #queue-req: 0, 
[2025-07-28 01:43:49 DP1 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.54, #queue-req: 0, 
[2025-07-28 01:43:49 DP0 TP0] Decode batch. #running-req: 1, #token: 1318, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.24, #queue-req: 0, 
[2025-07-28 01:43:49 DP1 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.74, #queue-req: 0, 
[2025-07-28 01:43:49 DP0 TP0] Decode batch. #running-req: 1, #token: 1358, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.39, #queue-req: 0, 
[2025-07-28 01:43:49 DP1 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.51, #queue-req: 0, 
[2025-07-28 01:43:49 DP0 TP0] Decode batch. #running-req: 1, #token: 1398, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.92, #queue-req: 0, 
[2025-07-28 01:43:49] INFO:     127.0.0.1:45852 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:43:49 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 146, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:43:49 DP1 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.35, #queue-req: 0, 
[2025-07-28 01:43:49 DP0 TP0] Decode batch. #running-req: 1, #token: 257, token usage: 0.00, cuda graph: True, gen throughput (token/s): 188.53, #queue-req: 0, 
[2025-07-28 01:43:49 DP1 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.35, #queue-req: 0, 
[2025-07-28 01:43:50 DP0 TP0] Decode batch. #running-req: 1, #token: 297, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.40, #queue-req: 0, 
[2025-07-28 01:43:50 DP1 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.58, #queue-req: 0, 
[2025-07-28 01:43:50 DP0 TP0] Decode batch. #running-req: 1, #token: 337, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.68, #queue-req: 0, 
[2025-07-28 01:43:50 DP1 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.60, #queue-req: 0, 
[2025-07-28 01:43:50 DP0 TP0] Decode batch. #running-req: 1, #token: 377, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.51, #queue-req: 0, 
[2025-07-28 01:43:50 DP1 TP0] Decode batch. #running-req: 1, #token: 676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.00, #queue-req: 0, 
[2025-07-28 01:43:50 DP0 TP0] Decode batch. #running-req: 1, #token: 417, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.69, #queue-req: 0, 
[2025-07-28 01:43:50 DP1 TP0] Decode batch. #running-req: 1, #token: 716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.19, #queue-req: 0, 
[2025-07-28 01:43:50 DP0 TP0] Decode batch. #running-req: 1, #token: 457, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.08, #queue-req: 0, 
[2025-07-28 01:43:50 DP1 TP0] Decode batch. #running-req: 1, #token: 756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.13, #queue-req: 0, 
[2025-07-28 01:43:50 DP0 TP0] Decode batch. #running-req: 1, #token: 497, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.52, #queue-req: 0, 
[2025-07-28 01:43:50 DP1 TP0] Decode batch. #running-req: 1, #token: 796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.87, #queue-req: 0, 
[2025-07-28 01:43:50 DP0 TP0] Decode batch. #running-req: 1, #token: 537, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.59, #queue-req: 0, 
[2025-07-28 01:43:51 DP1 TP0] Decode batch. #running-req: 1, #token: 836, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.98, #queue-req: 0, 
[2025-07-28 01:43:51 DP0 TP0] Decode batch. #running-req: 1, #token: 577, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.88, #queue-req: 0, 
[2025-07-28 01:43:51 DP1 TP0] Decode batch. #running-req: 1, #token: 876, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.93, #queue-req: 0, 
[2025-07-28 01:43:51 DP0 TP0] Decode batch. #running-req: 1, #token: 617, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.27, #queue-req: 0, 
[2025-07-28 01:43:51 DP1 TP0] Decode batch. #running-req: 1, #token: 916, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.22, #queue-req: 0, 
[2025-07-28 01:43:51 DP0 TP0] Decode batch. #running-req: 1, #token: 657, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.31, #queue-req: 0, 
[2025-07-28 01:43:51 DP1 TP0] Decode batch. #running-req: 1, #token: 956, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.04, #queue-req: 0, 
[2025-07-28 01:43:51 DP0 TP0] Decode batch. #running-req: 1, #token: 697, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.65, #queue-req: 0, 
[2025-07-28 01:43:51 DP1 TP0] Decode batch. #running-req: 1, #token: 996, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.69, #queue-req: 0, 
[2025-07-28 01:43:51 DP0 TP0] Decode batch. #running-req: 1, #token: 737, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.56, #queue-req: 0, 
[2025-07-28 01:43:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1036, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.53, #queue-req: 0, 
[2025-07-28 01:43:51 DP0 TP0] Decode batch. #running-req: 1, #token: 777, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.85, #queue-req: 0, 
[2025-07-28 01:43:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1076, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.53, #queue-req: 0, 
[2025-07-28 01:43:52 DP0 TP0] Decode batch. #running-req: 1, #token: 817, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.92, #queue-req: 0, 
[2025-07-28 01:43:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1116, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.56, #queue-req: 0, 
[2025-07-28 01:43:52 DP0 TP0] Decode batch. #running-req: 1, #token: 857, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.47, #queue-req: 0, 
[2025-07-28 01:43:52] INFO:     127.0.0.1:45876 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:43:52 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 107, #cached-token: 84, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:43:52 DP1 TP0] Decode batch. #running-req: 2, #token: 1282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 283.28, #queue-req: 0, 
[2025-07-28 01:43:52 DP1 TP0] Decode batch. #running-req: 2, #token: 1362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 470.51, #queue-req: 0, 
[2025-07-28 01:43:52 DP1 TP0] Decode batch. #running-req: 2, #token: 1442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.71, #queue-req: 0, 
[2025-07-28 01:43:52 DP1 TP0] Decode batch. #running-req: 2, #token: 1522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.62, #queue-req: 0, 
[2025-07-28 01:43:53 DP1 TP0] Decode batch. #running-req: 2, #token: 1602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 471.31, #queue-req: 0, 
[2025-07-28 01:43:53 DP1 TP0] Decode batch. #running-req: 2, #token: 1682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 471.43, #queue-req: 0, 
[2025-07-28 01:43:53 DP1 TP0] Decode batch. #running-req: 2, #token: 1762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 471.50, #queue-req: 0, 
[2025-07-28 01:43:53 DP1 TP0] Decode batch. #running-req: 2, #token: 1842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 470.71, #queue-req: 0, 
[2025-07-28 01:43:53 DP1 TP0] Decode batch. #running-req: 2, #token: 1922, token usage: 0.00, cuda graph: True, gen throughput (token/s): 470.81, #queue-req: 0, 
[2025-07-28 01:43:53 DP1 TP0] Decode batch. #running-req: 2, #token: 2002, token usage: 0.00, cuda graph: True, gen throughput (token/s): 470.43, #queue-req: 0, 
[2025-07-28 01:43:54 DP1 TP0] Decode batch. #running-req: 2, #token: 2082, token usage: 0.00, cuda graph: True, gen throughput (token/s): 468.91, #queue-req: 0, 
[2025-07-28 01:43:54 DP1 TP0] Decode batch. #running-req: 2, #token: 2162, token usage: 0.00, cuda graph: True, gen throughput (token/s): 468.91, #queue-req: 0, 
[2025-07-28 01:43:54 DP1 TP0] Decode batch. #running-req: 2, #token: 2242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 468.75, #queue-req: 0, 
[2025-07-28 01:43:54 DP1 TP0] Decode batch. #running-req: 2, #token: 2322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 468.35, #queue-req: 0, 
[2025-07-28 01:43:54 DP1 TP0] Decode batch. #running-req: 2, #token: 2402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 468.73, #queue-req: 0, 
[2025-07-28 01:43:54 DP1 TP0] Decode batch. #running-req: 2, #token: 2482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.39, #queue-req: 0, 
[2025-07-28 01:43:55] INFO:     127.0.0.1:45890 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:43:55 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 91, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:43:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 325.83, #queue-req: 0, 
[2025-07-28 01:43:55 DP0 TP0] Decode batch. #running-req: 1, #token: 214, token usage: 0.00, cuda graph: True, gen throughput (token/s): 13.40, #queue-req: 0, 
[2025-07-28 01:43:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1836, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.19, #queue-req: 0, 
[2025-07-28 01:43:55 DP0 TP0] Decode batch. #running-req: 1, #token: 254, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.03, #queue-req: 0, 
[2025-07-28 01:43:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1876, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.12, #queue-req: 0, 
[2025-07-28 01:43:55 DP0 TP0] Decode batch. #running-req: 1, #token: 294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.57, #queue-req: 0, 
[2025-07-28 01:43:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1916, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.18, #queue-req: 0, 
[2025-07-28 01:43:55 DP0 TP0] Decode batch. #running-req: 1, #token: 334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.88, #queue-req: 0, 
[2025-07-28 01:43:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1956, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.15, #queue-req: 0, 
[2025-07-28 01:43:55 DP0 TP0] Decode batch. #running-req: 1, #token: 374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.12, #queue-req: 0, 
[2025-07-28 01:43:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1996, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.16, #queue-req: 0, 
[2025-07-28 01:43:56 DP0 TP0] Decode batch. #running-req: 1, #token: 414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.89, #queue-req: 0, 
[2025-07-28 01:43:56 DP1 TP0] Decode batch. #running-req: 1, #token: 2036, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.15, #queue-req: 0, 
[2025-07-28 01:43:56 DP0 TP0] Decode batch. #running-req: 1, #token: 454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.13, #queue-req: 0, 
[2025-07-28 01:43:56 DP1 TP0] Decode batch. #running-req: 1, #token: 2076, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.73, #queue-req: 0, 
[2025-07-28 01:43:56 DP0 TP0] Decode batch. #running-req: 1, #token: 494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.02, #queue-req: 0, 
[2025-07-28 01:43:56 DP1 TP0] Decode batch. #running-req: 1, #token: 2116, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.42, #queue-req: 0, 
[2025-07-28 01:43:56 DP0 TP0] Decode batch. #running-req: 1, #token: 534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.94, #queue-req: 0, 
[2025-07-28 01:43:56 DP1 TP0] Decode batch. #running-req: 1, #token: 2156, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.48, #queue-req: 0, 
[2025-07-28 01:43:56 DP0 TP0] Decode batch. #running-req: 1, #token: 574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.81, #queue-req: 0, 
[2025-07-28 01:43:56 DP1 TP0] Decode batch. #running-req: 1, #token: 2196, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.94, #queue-req: 0, 
[2025-07-28 01:43:56 DP0 TP0] Decode batch. #running-req: 1, #token: 614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.00, #queue-req: 0, 
[2025-07-28 01:43:56 DP1 TP0] Decode batch. #running-req: 1, #token: 2236, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.45, #queue-req: 0, 
[2025-07-28 01:43:57 DP0 TP0] Decode batch. #running-req: 1, #token: 654, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.11, #queue-req: 0, 
[2025-07-28 01:43:57 DP1 TP0] Decode batch. #running-req: 1, #token: 2276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.65, #queue-req: 0, 
[2025-07-28 01:43:57 DP0 TP0] Decode batch. #running-req: 1, #token: 694, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.88, #queue-req: 0, 
[2025-07-28 01:43:57 DP1 TP0] Decode batch. #running-req: 1, #token: 2316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.48, #queue-req: 0, 
[2025-07-28 01:43:57] INFO:     127.0.0.1:44484 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:43:57 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 85, #cached-token: 84, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:43:57 DP1 TP0] Decode batch. #running-req: 2, #token: 2449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 225.27, #queue-req: 0, 
[2025-07-28 01:43:57 DP1 TP0] Decode batch. #running-req: 2, #token: 2529, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.89, #queue-req: 0, 
[2025-07-28 01:43:57 DP1 TP0] Decode batch. #running-req: 2, #token: 2609, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.97, #queue-req: 0, 
[2025-07-28 01:43:57 DP1 TP0] Decode batch. #running-req: 2, #token: 2689, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.45, #queue-req: 0, 
[2025-07-28 01:43:58 DP1 TP0] Decode batch. #running-req: 2, #token: 2769, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.82, #queue-req: 0, 
[2025-07-28 01:43:58 DP1 TP0] Decode batch. #running-req: 2, #token: 2849, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.59, #queue-req: 0, 
[2025-07-28 01:43:58 DP1 TP0] Decode batch. #running-req: 2, #token: 2929, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.29, #queue-req: 0, 
[2025-07-28 01:43:58 DP1 TP0] Decode batch. #running-req: 2, #token: 3009, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.18, #queue-req: 0, 
[2025-07-28 01:43:58 DP1 TP0] Decode batch. #running-req: 2, #token: 3089, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.10, #queue-req: 0, 
[2025-07-28 01:43:59 DP1 TP0] Decode batch. #running-req: 2, #token: 3169, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.11, #queue-req: 0, 
[2025-07-28 01:43:59 DP1 TP0] Decode batch. #running-req: 2, #token: 3249, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.08, #queue-req: 0, 
[2025-07-28 01:43:59 DP1 TP0] Decode batch. #running-req: 2, #token: 3329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.28, #queue-req: 0, 
[2025-07-28 01:43:59 DP1 TP0] Decode batch. #running-req: 2, #token: 3409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.36, #queue-req: 0, 
[2025-07-28 01:43:59 DP1 TP0] Decode batch. #running-req: 2, #token: 3489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.22, #queue-req: 0, 
[2025-07-28 01:43:59 DP1 TP0] Decode batch. #running-req: 2, #token: 3569, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.64, #queue-req: 0, 
[2025-07-28 01:44:00 DP1 TP0] Decode batch. #running-req: 2, #token: 3649, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.67, #queue-req: 0, 
[2025-07-28 01:44:00 DP1 TP0] Decode batch. #running-req: 2, #token: 3729, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.68, #queue-req: 0, 
[2025-07-28 01:44:00] INFO:     127.0.0.1:44494 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:44:00 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 111, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:44:00 DP0 TP0] Decode batch. #running-req: 1, #token: 198, token usage: 0.00, cuda graph: True, gen throughput (token/s): 12.90, #queue-req: 0, 
[2025-07-28 01:44:00 DP1 TP0] Decode batch. #running-req: 1, #token: 3036, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.81, #queue-req: 0, 
[2025-07-28 01:44:00 DP0 TP0] Decode batch. #running-req: 1, #token: 238, token usage: 0.00, cuda graph: True, gen throughput (token/s): 216.21, #queue-req: 0, 
[2025-07-28 01:44:00 DP1 TP0] Decode batch. #running-req: 1, #token: 3076, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.44, #queue-req: 0, 
[2025-07-28 01:44:00 DP0 TP0] Decode batch. #running-req: 1, #token: 278, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.54, #queue-req: 0, 
[2025-07-28 01:44:00 DP1 TP0] Decode batch. #running-req: 1, #token: 3116, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.18, #queue-req: 0, 
[2025-07-28 01:44:00 DP0 TP0] Decode batch. #running-req: 1, #token: 318, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.04, #queue-req: 0, 
[2025-07-28 01:44:00 DP1 TP0] Decode batch. #running-req: 1, #token: 3156, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.10, #queue-req: 0, 
[2025-07-28 01:44:01 DP0 TP0] Decode batch. #running-req: 1, #token: 358, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.38, #queue-req: 0, 
[2025-07-28 01:44:01 DP1 TP0] Decode batch. #running-req: 1, #token: 3196, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.19, #queue-req: 0, 
[2025-07-28 01:44:01 DP0 TP0] Decode batch. #running-req: 1, #token: 398, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.69, #queue-req: 0, 
[2025-07-28 01:44:01 DP1 TP0] Decode batch. #running-req: 1, #token: 3236, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.18, #queue-req: 0, 
[2025-07-28 01:44:01 DP0 TP0] Decode batch. #running-req: 1, #token: 438, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.50, #queue-req: 0, 
[2025-07-28 01:44:01 DP1 TP0] Decode batch. #running-req: 1, #token: 3276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.23, #queue-req: 0, 
[2025-07-28 01:44:01 DP0 TP0] Decode batch. #running-req: 1, #token: 478, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.92, #queue-req: 0, 
[2025-07-28 01:44:01 DP1 TP0] Decode batch. #running-req: 1, #token: 3316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.31, #queue-req: 0, 
[2025-07-28 01:44:01 DP0 TP0] Decode batch. #running-req: 1, #token: 518, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.15, #queue-req: 0, 
[2025-07-28 01:44:01 DP1 TP0] Decode batch. #running-req: 1, #token: 3356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.19, #queue-req: 0, 
[2025-07-28 01:44:01 DP0 TP0] Decode batch. #running-req: 1, #token: 558, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.01, #queue-req: 0, 
[2025-07-28 01:44:01 DP1 TP0] Decode batch. #running-req: 1, #token: 3396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.05, #queue-req: 0, 
[2025-07-28 01:44:01 DP0 TP0] Decode batch. #running-req: 1, #token: 598, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.41, #queue-req: 0, 
[2025-07-28 01:44:02 DP1 TP0] Decode batch. #running-req: 1, #token: 3436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.08, #queue-req: 0, 
[2025-07-28 01:44:02 DP0 TP0] Decode batch. #running-req: 1, #token: 638, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.04, #queue-req: 0, 
[2025-07-28 01:44:02 DP1 TP0] Decode batch. #running-req: 1, #token: 3476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.15, #queue-req: 0, 
[2025-07-28 01:44:02 DP0 TP0] Decode batch. #running-req: 1, #token: 678, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.63, #queue-req: 0, 
[2025-07-28 01:44:02 DP1 TP0] Decode batch. #running-req: 1, #token: 3516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.30, #queue-req: 0, 
[2025-07-28 01:44:02 DP0 TP0] Decode batch. #running-req: 1, #token: 718, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.31, #queue-req: 0, 
[2025-07-28 01:44:02 DP1 TP0] Decode batch. #running-req: 1, #token: 3556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.10, #queue-req: 0, 
[2025-07-28 01:44:02 DP0 TP0] Decode batch. #running-req: 1, #token: 758, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.95, #queue-req: 0, 
[2025-07-28 01:44:02 DP1 TP0] Decode batch. #running-req: 1, #token: 3596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.29, #queue-req: 0, 
[2025-07-28 01:44:02 DP0 TP0] Decode batch. #running-req: 1, #token: 798, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.68, #queue-req: 0, 
[2025-07-28 01:44:02 DP1 TP0] Decode batch. #running-req: 1, #token: 3636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.98, #queue-req: 0, 
[2025-07-28 01:44:02 DP0 TP0] Decode batch. #running-req: 1, #token: 838, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.74, #queue-req: 0, 
[2025-07-28 01:44:03 DP1 TP0] Decode batch. #running-req: 1, #token: 3676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.91, #queue-req: 0, 
[2025-07-28 01:44:03] INFO:     127.0.0.1:44508 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:44:03 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 160, #cached-token: 86, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:44:03 DP1 TP0] Decode batch. #running-req: 2, #token: 3893, token usage: 0.00, cuda graph: True, gen throughput (token/s): 261.59, #queue-req: 0, 
[2025-07-28 01:44:03 DP1 TP0] Decode batch. #running-req: 2, #token: 3973, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.86, #queue-req: 0, 
[2025-07-28 01:44:03 DP1 TP0] Decode batch. #running-req: 2, #token: 4053, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.21, #queue-req: 0, 
[2025-07-28 01:44:03 DP1 TP0] Decode batch. #running-req: 2, #token: 4133, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.53, #queue-req: 0, 
[2025-07-28 01:44:03 DP1 TP0] Decode batch. #running-req: 2, #token: 4213, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.90, #queue-req: 0, 
[2025-07-28 01:44:04 DP1 TP0] Decode batch. #running-req: 2, #token: 4293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.90, #queue-req: 0, 
[2025-07-28 01:44:04 DP1 TP0] Decode batch. #running-req: 2, #token: 4373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.08, #queue-req: 0, 
[2025-07-28 01:44:04 DP1 TP0] Decode batch. #running-req: 2, #token: 4453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.44, #queue-req: 0, 
[2025-07-28 01:44:04 DP1 TP0] Decode batch. #running-req: 2, #token: 4533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.42, #queue-req: 0, 
[2025-07-28 01:44:04 DP1 TP0] Decode batch. #running-req: 2, #token: 4613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.61, #queue-req: 0, 
[2025-07-28 01:44:04 DP1 TP0] Decode batch. #running-req: 2, #token: 4693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.72, #queue-req: 0, 
[2025-07-28 01:44:05 DP1 TP0] Decode batch. #running-req: 2, #token: 4773, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.64, #queue-req: 0, 
[2025-07-28 01:44:05 DP1 TP0] Decode batch. #running-req: 2, #token: 4853, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.04, #queue-req: 0, 
[2025-07-28 01:44:05 DP1 TP0] Decode batch. #running-req: 2, #token: 4933, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.52, #queue-req: 0, 
[2025-07-28 01:44:05 DP1 TP0] Decode batch. #running-req: 2, #token: 5013, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.42, #queue-req: 0, 
[2025-07-28 01:44:05 DP1 TP0] Decode batch. #running-req: 2, #token: 5093, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.47, #queue-req: 0, 
[2025-07-28 01:44:05 DP1 TP0] Decode batch. #running-req: 2, #token: 5173, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.23, #queue-req: 0, 
[2025-07-28 01:44:06 DP1 TP0] Decode batch. #running-req: 2, #token: 5253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.36, #queue-req: 0, 
[2025-07-28 01:44:06 DP1 TP0] Decode batch. #running-req: 2, #token: 5333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.48, #queue-req: 0, 
[2025-07-28 01:44:06 DP1 TP0] Decode batch. #running-req: 2, #token: 5413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.34, #queue-req: 0, 
[2025-07-28 01:44:06 DP1 TP0] Decode batch. #running-req: 2, #token: 5493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.26, #queue-req: 0, 
[2025-07-28 01:44:06] INFO:     127.0.0.1:43166 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:44:06 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 139, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:44:06 DP1 TP0] Decode batch. #running-req: 1, #token: 4556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.89, #queue-req: 0, 
[2025-07-28 01:44:06 DP0 TP0] Decode batch. #running-req: 1, #token: 231, token usage: 0.00, cuda graph: True, gen throughput (token/s): 10.34, #queue-req: 0, 
[2025-07-28 01:44:06 DP1 TP0] Decode batch. #running-req: 1, #token: 4596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.89, #queue-req: 0, 
[2025-07-28 01:44:07 DP0 TP0] Decode batch. #running-req: 1, #token: 271, token usage: 0.00, cuda graph: True, gen throughput (token/s): 220.16, #queue-req: 0, 
[2025-07-28 01:44:07 DP1 TP0] Decode batch. #running-req: 1, #token: 4636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.74, #queue-req: 0, 
[2025-07-28 01:44:07 DP0 TP0] Decode batch. #running-req: 1, #token: 311, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.58, #queue-req: 0, 
[2025-07-28 01:44:07 DP1 TP0] Decode batch. #running-req: 1, #token: 4676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.77, #queue-req: 0, 
[2025-07-28 01:44:07 DP0 TP0] Decode batch. #running-req: 1, #token: 351, token usage: 0.00, cuda graph: True, gen throughput (token/s): 239.29, #queue-req: 0, 
[2025-07-28 01:44:07 DP1 TP0] Decode batch. #running-req: 1, #token: 4716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.66, #queue-req: 0, 
[2025-07-28 01:44:07 DP0 TP0] Decode batch. #running-req: 1, #token: 391, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.51, #queue-req: 0, 
[2025-07-28 01:44:07 DP1 TP0] Decode batch. #running-req: 1, #token: 4756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 238.32, #queue-req: 0, 
[2025-07-28 01:44:07 DP0 TP0] Decode batch. #running-req: 1, #token: 431, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.80, #queue-req: 0, 
[2025-07-28 01:44:07 DP1 TP0] Decode batch. #running-req: 1, #token: 4796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.78, #queue-req: 0, 
[2025-07-28 01:44:07 DP0 TP0] Decode batch. #running-req: 1, #token: 471, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.58, #queue-req: 0, 
[2025-07-28 01:44:07 DP1 TP0] Decode batch. #running-req: 1, #token: 4836, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.83, #queue-req: 0, 
[2025-07-28 01:44:08 DP0 TP0] Decode batch. #running-req: 1, #token: 511, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.63, #queue-req: 0, 
[2025-07-28 01:44:08 DP1 TP0] Decode batch. #running-req: 1, #token: 4876, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.21, #queue-req: 0, 
[2025-07-28 01:44:08 DP0 TP0] Decode batch. #running-req: 1, #token: 551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.74, #queue-req: 0, 
[2025-07-28 01:44:08 DP1 TP0] Decode batch. #running-req: 1, #token: 4916, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.65, #queue-req: 0, 
[2025-07-28 01:44:08 DP0 TP0] Decode batch. #running-req: 1, #token: 591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.59, #queue-req: 0, 
[2025-07-28 01:44:08 DP1 TP0] Decode batch. #running-req: 1, #token: 4956, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.66, #queue-req: 0, 
[2025-07-28 01:44:08 DP0 TP0] Decode batch. #running-req: 1, #token: 631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.38, #queue-req: 0, 
[2025-07-28 01:44:08 DP1 TP0] Decode batch. #running-req: 1, #token: 4996, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.67, #queue-req: 0, 
[2025-07-28 01:44:08 DP0 TP0] Decode batch. #running-req: 1, #token: 671, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.71, #queue-req: 0, 
[2025-07-28 01:44:08 DP1 TP0] Decode batch. #running-req: 1, #token: 5036, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.34, #queue-req: 0, 
[2025-07-28 01:44:08 DP0 TP0] Decode batch. #running-req: 1, #token: 711, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.85, #queue-req: 0, 
[2025-07-28 01:44:08 DP1 TP0] Decode batch. #running-req: 1, #token: 5076, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.47, #queue-req: 0, 
[2025-07-28 01:44:08 DP0 TP0] Decode batch. #running-req: 1, #token: 751, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.96, #queue-req: 0, 
[2025-07-28 01:44:09 DP1 TP0] Decode batch. #running-req: 1, #token: 5116, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.69, #queue-req: 0, 
[2025-07-28 01:44:09 DP0 TP0] Decode batch. #running-req: 1, #token: 791, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.39, #queue-req: 0, 
[2025-07-28 01:44:09 DP1 TP0] Decode batch. #running-req: 1, #token: 5156, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.14, #queue-req: 0, 
[2025-07-28 01:44:09 DP0 TP0] Decode batch. #running-req: 1, #token: 831, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.36, #queue-req: 0, 
[2025-07-28 01:44:09 DP1 TP0] Decode batch. #running-req: 1, #token: 5196, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.87, #queue-req: 0, 
[2025-07-28 01:44:09 DP0 TP0] Decode batch. #running-req: 1, #token: 871, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.37, #queue-req: 0, 
[2025-07-28 01:44:09 DP1 TP0] Decode batch. #running-req: 1, #token: 5236, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.06, #queue-req: 0, 
[2025-07-28 01:44:09 DP0 TP0] Decode batch. #running-req: 1, #token: 911, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.23, #queue-req: 0, 
[2025-07-28 01:44:09] INFO:     127.0.0.1:43176 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:44:09 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 113, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:44:09 DP1 TP0] Decode batch. #running-req: 2, #token: 5415, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.14, #queue-req: 0, 
[2025-07-28 01:44:10 DP1 TP0] Decode batch. #running-req: 2, #token: 5495, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.20, #queue-req: 0, 
[2025-07-28 01:44:10 DP1 TP0] Decode batch. #running-req: 2, #token: 5575, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.25, #queue-req: 0, 
[2025-07-28 01:44:10 DP1 TP0] Decode batch. #running-req: 2, #token: 5655, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.23, #queue-req: 0, 
[2025-07-28 01:44:10 DP1 TP0] Decode batch. #running-req: 2, #token: 5735, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.94, #queue-req: 0, 
[2025-07-28 01:44:10 DP1 TP0] Decode batch. #running-req: 2, #token: 5815, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.65, #queue-req: 0, 
[2025-07-28 01:44:10 DP1 TP0] Decode batch. #running-req: 2, #token: 5895, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.51, #queue-req: 0, 
[2025-07-28 01:44:11 DP1 TP0] Decode batch. #running-req: 2, #token: 5975, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.20, #queue-req: 0, 
[2025-07-28 01:44:11 DP1 TP0] Decode batch. #running-req: 2, #token: 6055, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.94, #queue-req: 0, 
[2025-07-28 01:44:11 DP1 TP0] Decode batch. #running-req: 2, #token: 6135, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.02, #queue-req: 0, 
[2025-07-28 01:44:11 DP1 TP0] Decode batch. #running-req: 2, #token: 6215, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.59, #queue-req: 0, 
[2025-07-28 01:44:11 DP1 TP0] Decode batch. #running-req: 2, #token: 6295, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.51, #queue-req: 0, 
[2025-07-28 01:44:11] INFO:     127.0.0.1:43182 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:44:11 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 121, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:44:11 DP1 TP0] Decode batch. #running-req: 1, #token: 5756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 393.63, #queue-req: 0, 
[2025-07-28 01:44:12 DP0 TP0] Decode batch. #running-req: 1, #token: 234, token usage: 0.00, cuda graph: True, gen throughput (token/s): 16.47, #queue-req: 0, 
[2025-07-28 01:44:12 DP1 TP0] Decode batch. #running-req: 1, #token: 5796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.06, #queue-req: 0, 
[2025-07-28 01:44:12 DP1 TP0] Decode batch. #running-req: 1, #token: 5836, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.48, #queue-req: 0, 
[2025-07-28 01:44:12 DP0 TP0] Decode batch. #running-req: 1, #token: 274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 226.09, #queue-req: 0, 
[2025-07-28 01:44:12 DP0 TP0] Decode batch. #running-req: 1, #token: 314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.74, #queue-req: 0, 
[2025-07-28 01:44:12 DP1 TP0] Decode batch. #running-req: 1, #token: 5876, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.55, #queue-req: 0, 
[2025-07-28 01:44:12 DP0 TP0] Decode batch. #running-req: 1, #token: 354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.65, #queue-req: 0, 
[2025-07-28 01:44:12 DP1 TP0] Decode batch. #running-req: 1, #token: 5916, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.86, #queue-req: 0, 
[2025-07-28 01:44:12 DP0 TP0] Decode batch. #running-req: 1, #token: 394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.14, #queue-req: 0, 
[2025-07-28 01:44:12 DP1 TP0] Decode batch. #running-req: 1, #token: 5956, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.45, #queue-req: 0, 
[2025-07-28 01:44:12 DP0 TP0] Decode batch. #running-req: 1, #token: 434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.25, #queue-req: 0, 
[2025-07-28 01:44:12 DP1 TP0] Decode batch. #running-req: 1, #token: 5996, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.91, #queue-req: 0, 
[2025-07-28 01:44:13 DP0 TP0] Decode batch. #running-req: 1, #token: 474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.85, #queue-req: 0, 
[2025-07-28 01:44:13 DP1 TP0] Decode batch. #running-req: 1, #token: 6036, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.68, #queue-req: 0, 
[2025-07-28 01:44:13 DP0 TP0] Decode batch. #running-req: 1, #token: 514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.70, #queue-req: 0, 
[2025-07-28 01:44:13 DP1 TP0] Decode batch. #running-req: 1, #token: 6076, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.73, #queue-req: 0, 
[2025-07-28 01:44:13 DP0 TP0] Decode batch. #running-req: 1, #token: 554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.62, #queue-req: 0, 
[2025-07-28 01:44:13 DP1 TP0] Decode batch. #running-req: 1, #token: 6116, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.86, #queue-req: 0, 
[2025-07-28 01:44:13 DP0 TP0] Decode batch. #running-req: 1, #token: 594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.64, #queue-req: 0, 
[2025-07-28 01:44:13 DP1 TP0] Decode batch. #running-req: 1, #token: 6156, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.73, #queue-req: 0, 
[2025-07-28 01:44:13 DP0 TP0] Decode batch. #running-req: 1, #token: 634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.37, #queue-req: 0, 
[2025-07-28 01:44:13 DP1 TP0] Decode batch. #running-req: 1, #token: 6196, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.15, #queue-req: 0, 
[2025-07-28 01:44:13 DP0 TP0] Decode batch. #running-req: 1, #token: 674, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.89, #queue-req: 0, 
[2025-07-28 01:44:13 DP1 TP0] Decode batch. #running-req: 1, #token: 6236, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.37, #queue-req: 0, 
[2025-07-28 01:44:14 DP0 TP0] Decode batch. #running-req: 1, #token: 714, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.78, #queue-req: 0, 
[2025-07-28 01:44:14 DP1 TP0] Decode batch. #running-req: 1, #token: 6276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.35, #queue-req: 0, 
[2025-07-28 01:44:14 DP0 TP0] Decode batch. #running-req: 1, #token: 754, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.90, #queue-req: 0, 
[2025-07-28 01:44:14 DP1 TP0] Decode batch. #running-req: 1, #token: 6316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.18, #queue-req: 0, 
[2025-07-28 01:44:14 DP0 TP0] Decode batch. #running-req: 1, #token: 794, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.10, #queue-req: 0, 
[2025-07-28 01:44:14 DP1 TP0] Decode batch. #running-req: 1, #token: 6356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.73, #queue-req: 0, 
[2025-07-28 01:44:14 DP0 TP0] Decode batch. #running-req: 1, #token: 834, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.85, #queue-req: 0, 
[2025-07-28 01:44:14 DP1 TP0] Decode batch. #running-req: 1, #token: 6396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.79, #queue-req: 0, 
[2025-07-28 01:44:14] INFO:     127.0.0.1:43194 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:44:14 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 192, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:44:14 DP1 TP0] Decode batch. #running-req: 2, #token: 6666, token usage: 0.00, cuda graph: True, gen throughput (token/s): 358.28, #queue-req: 0, 
[2025-07-28 01:44:14 DP1 TP0] Decode batch. #running-req: 2, #token: 6746, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.35, #queue-req: 0, 
[2025-07-28 01:44:15 DP1 TP0] Decode batch. #running-req: 2, #token: 6826, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.10, #queue-req: 0, 
[2025-07-28 01:44:15 DP1 TP0] Decode batch. #running-req: 2, #token: 6906, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.78, #queue-req: 0, 
[2025-07-28 01:44:15 DP1 TP0] Decode batch. #running-req: 2, #token: 6986, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.25, #queue-req: 0, 
[2025-07-28 01:44:15 DP1 TP0] Decode batch. #running-req: 2, #token: 7066, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.13, #queue-req: 0, 
[2025-07-28 01:44:15 DP1 TP0] Decode batch. #running-req: 2, #token: 7146, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.05, #queue-req: 0, 
[2025-07-28 01:44:15 DP1 TP0] Decode batch. #running-req: 2, #token: 7226, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.00, #queue-req: 0, 
[2025-07-28 01:44:16 DP1 TP0] Decode batch. #running-req: 2, #token: 7306, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.00, #queue-req: 0, 
[2025-07-28 01:44:16 DP1 TP0] Decode batch. #running-req: 2, #token: 7386, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.01, #queue-req: 0, 
[2025-07-28 01:44:16 DP1 TP0] Decode batch. #running-req: 2, #token: 7466, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.79, #queue-req: 0, 
[2025-07-28 01:44:16 DP1 TP0] Decode batch. #running-req: 2, #token: 7546, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.90, #queue-req: 0, 
[2025-07-28 01:44:16 DP1 TP0] Decode batch. #running-req: 2, #token: 7626, token usage: 0.00, cuda graph: True, gen throughput (token/s): 459.73, #queue-req: 0, 
[2025-07-28 01:44:17 DP1 TP0] Decode batch. #running-req: 2, #token: 7706, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.04, #queue-req: 0, 
[2025-07-28 01:44:17 DP1 TP0] Decode batch. #running-req: 2, #token: 7786, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.53, #queue-req: 0, 
[2025-07-28 01:44:17 DP1 TP0] Decode batch. #running-req: 2, #token: 7866, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.37, #queue-req: 0, 
[2025-07-28 01:44:17] INFO:     127.0.0.1:40308 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:44:17 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 80, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:44:17 DP1 TP0] Decode batch. #running-req: 1, #token: 7076, token usage: 0.00, cuda graph: True, gen throughput (token/s): 322.72, #queue-req: 0, 
[2025-07-28 01:44:17 DP0 TP0] Decode batch. #running-req: 1, #token: 194, token usage: 0.00, cuda graph: True, gen throughput (token/s): 12.90, #queue-req: 0, 
[2025-07-28 01:44:17 DP1 TP0] Decode batch. #running-req: 1, #token: 7116, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.08, #queue-req: 0, 
[2025-07-28 01:44:17 DP0 TP0] Decode batch. #running-req: 1, #token: 234, token usage: 0.00, cuda graph: True, gen throughput (token/s): 229.99, #queue-req: 0, 
[2025-07-28 01:44:17 DP1 TP0] Decode batch. #running-req: 1, #token: 7156, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.98, #queue-req: 0, 
[2025-07-28 01:44:17 DP0 TP0] Decode batch. #running-req: 1, #token: 274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.64, #queue-req: 0, 
[2025-07-28 01:44:18 DP1 TP0] Decode batch. #running-req: 1, #token: 7196, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.90, #queue-req: 0, 
[2025-07-28 01:44:18 DP0 TP0] Decode batch. #running-req: 1, #token: 314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.22, #queue-req: 0, 
[2025-07-28 01:44:18 DP1 TP0] Decode batch. #running-req: 1, #token: 7236, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.87, #queue-req: 0, 
[2025-07-28 01:44:18 DP0 TP0] Decode batch. #running-req: 1, #token: 354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.53, #queue-req: 0, 
[2025-07-28 01:44:18 DP1 TP0] Decode batch. #running-req: 1, #token: 7276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.86, #queue-req: 0, 
[2025-07-28 01:44:18 DP0 TP0] Decode batch. #running-req: 1, #token: 394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.24, #queue-req: 0, 
[2025-07-28 01:44:18 DP1 TP0] Decode batch. #running-req: 1, #token: 7316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.77, #queue-req: 0, 
[2025-07-28 01:44:18 DP0 TP0] Decode batch. #running-req: 1, #token: 434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.10, #queue-req: 0, 
[2025-07-28 01:44:18 DP1 TP0] Decode batch. #running-req: 1, #token: 7356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.36, #queue-req: 0, 
[2025-07-28 01:44:18 DP0 TP0] Decode batch. #running-req: 1, #token: 474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.82, #queue-req: 0, 
[2025-07-28 01:44:18 DP1 TP0] Decode batch. #running-req: 1, #token: 7396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.75, #queue-req: 0, 
[2025-07-28 01:44:18 DP0 TP0] Decode batch. #running-req: 1, #token: 514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.54, #queue-req: 0, 
[2025-07-28 01:44:18 DP1 TP0] Decode batch. #running-req: 1, #token: 7436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.50, #queue-req: 0, 
[2025-07-28 01:44:19 DP0 TP0] Decode batch. #running-req: 1, #token: 554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.14, #queue-req: 0, 
[2025-07-28 01:44:19 DP1 TP0] Decode batch. #running-req: 1, #token: 7476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.24, #queue-req: 0, 
[2025-07-28 01:44:19 DP0 TP0] Decode batch. #running-req: 1, #token: 594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.97, #queue-req: 0, 
[2025-07-28 01:44:19 DP1 TP0] Decode batch. #running-req: 1, #token: 7516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.13, #queue-req: 0, 
[2025-07-28 01:44:19 DP0 TP0] Decode batch. #running-req: 1, #token: 634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.00, #queue-req: 0, 
[2025-07-28 01:44:19] INFO:     127.0.0.1:40314 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:44:19 DP1 TP0] Decode batch. #running-req: 1, #token: 7556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.20, #queue-req: 0, 
[2025-07-28 01:44:19 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 150, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:44:19 DP1 TP0] Decode batch. #running-req: 2, #token: 7786, token usage: 0.00, cuda graph: True, gen throughput (token/s): 370.67, #queue-req: 0, 
[2025-07-28 01:44:19 DP1 TP0] Decode batch. #running-req: 2, #token: 7866, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.28, #queue-req: 0, 
[2025-07-28 01:44:20 DP1 TP0] Decode batch. #running-req: 2, #token: 7946, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.97, #queue-req: 0, 
[2025-07-28 01:44:20 DP1 TP0] Decode batch. #running-req: 2, #token: 8026, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.62, #queue-req: 0, 
[2025-07-28 01:44:20 DP1 TP0] Decode batch. #running-req: 2, #token: 8106, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.67, #queue-req: 0, 
[2025-07-28 01:44:20 DP1 TP0] Decode batch. #running-req: 2, #token: 8186, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.99, #queue-req: 0, 
[2025-07-28 01:44:20 DP1 TP0] Decode batch. #running-req: 2, #token: 8266, token usage: 0.00, cuda graph: True, gen throughput (token/s): 411.87, #queue-req: 0, 
[2025-07-28 01:44:20 DP1 TP0] Decode batch. #running-req: 2, #token: 8346, token usage: 0.00, cuda graph: True, gen throughput (token/s): 453.36, #queue-req: 0, 
[2025-07-28 01:44:21 DP1 TP0] Decode batch. #running-req: 2, #token: 8426, token usage: 0.00, cuda graph: True, gen throughput (token/s): 448.95, #queue-req: 0, 
[2025-07-28 01:44:21 DP1 TP0] Decode batch. #running-req: 2, #token: 8506, token usage: 0.00, cuda graph: True, gen throughput (token/s): 445.30, #queue-req: 0, 
[2025-07-28 01:44:21 DP1 TP0] Decode batch. #running-req: 2, #token: 8586, token usage: 0.00, cuda graph: True, gen throughput (token/s): 445.90, #queue-req: 0, 
[2025-07-28 01:44:21 DP1 TP0] Decode batch. #running-req: 2, #token: 8666, token usage: 0.00, cuda graph: True, gen throughput (token/s): 452.87, #queue-req: 0, 
[2025-07-28 01:44:21 DP1 TP0] Decode batch. #running-req: 2, #token: 8746, token usage: 0.00, cuda graph: True, gen throughput (token/s): 457.81, #queue-req: 0, 
[2025-07-28 01:44:21 DP1 TP0] Decode batch. #running-req: 2, #token: 8826, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.10, #queue-req: 0, 
[2025-07-28 01:44:22 DP1 TP0] Decode batch. #running-req: 2, #token: 8906, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.67, #queue-req: 0, 
[2025-07-28 01:44:22 DP1 TP0] Decode batch. #running-req: 2, #token: 8986, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.16, #queue-req: 0, 
[2025-07-28 01:44:22 DP1 TP0] Decode batch. #running-req: 2, #token: 9066, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.80, #queue-req: 0, 
[2025-07-28 01:44:22 DP1 TP0] Decode batch. #running-req: 2, #token: 9146, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.97, #queue-req: 0, 
[2025-07-28 01:44:22 DP1 TP0] Decode batch. #running-req: 2, #token: 9226, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.88, #queue-req: 0, 
[2025-07-28 01:44:23 DP1 TP0] Decode batch. #running-req: 2, #token: 9306, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.94, #queue-req: 0, 
[2025-07-28 01:44:23 DP1 TP0] Decode batch. #running-req: 2, #token: 9386, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.88, #queue-req: 0, 
[2025-07-28 01:44:23] INFO:     127.0.0.1:45868 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:44:23 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 124, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:44:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1114, token usage: 0.00, cuda graph: True, gen throughput (token/s): 431.25, #queue-req: 0, 
[2025-07-28 01:44:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1154, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.60, #queue-req: 0, 
[2025-07-28 01:44:23 DP0 TP0] Decode batch. #running-req: 1, #token: 235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 9.69, #queue-req: 0, 
[2025-07-28 01:44:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1194, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.40, #queue-req: 0, 
[2025-07-28 01:44:23 DP0 TP0] Decode batch. #running-req: 1, #token: 275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 227.72, #queue-req: 0, 
[2025-07-28 01:44:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1234, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.39, #queue-req: 0, 
[2025-07-28 01:44:23 DP0 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.75, #queue-req: 0, 
[2025-07-28 01:44:24 DP1 TP0] Decode batch. #running-req: 1, #token: 1274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.34, #queue-req: 0, 
[2025-07-28 01:44:24 DP0 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.38, #queue-req: 0, 
[2025-07-28 01:44:24 DP1 TP0] Decode batch. #running-req: 1, #token: 1314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.33, #queue-req: 0, 
[2025-07-28 01:44:24 DP0 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.35, #queue-req: 0, 
[2025-07-28 01:44:24] INFO:     127.0.0.1:40316 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:44:24 DP1 TP0] Decode batch. #running-req: 1, #token: 0, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.61, #queue-req: 0, 
[2025-07-28 01:44:24 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:44:24 DP0 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.25, #queue-req: 0, 
[2025-07-28 01:44:24 DP0 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.88, #queue-req: 0, 
[2025-07-28 01:44:24 DP1 TP0] Decode batch. #running-req: 1, #token: 267, token usage: 0.00, cuda graph: True, gen throughput (token/s): 189.20, #queue-req: 0, 
[2025-07-28 01:44:24 DP0 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.96, #queue-req: 0, 
[2025-07-28 01:44:24 DP1 TP0] Decode batch. #running-req: 1, #token: 307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.68, #queue-req: 0, 
[2025-07-28 01:44:24 DP0 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.74, #queue-req: 0, 
[2025-07-28 01:44:24 DP1 TP0] Decode batch. #running-req: 1, #token: 347, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.51, #queue-req: 0, 
[2025-07-28 01:44:25 DP0 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.71, #queue-req: 0, 
[2025-07-28 01:44:25 DP1 TP0] Decode batch. #running-req: 1, #token: 387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.51, #queue-req: 0, 
[2025-07-28 01:44:25 DP0 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.69, #queue-req: 0, 
[2025-07-28 01:44:25 DP1 TP0] Decode batch. #running-req: 1, #token: 427, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.40, #queue-req: 0, 
[2025-07-28 01:44:25 DP0 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.53, #queue-req: 0, 
[2025-07-28 01:44:25 DP1 TP0] Decode batch. #running-req: 1, #token: 467, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.60, #queue-req: 0, 
[2025-07-28 01:44:25 DP0 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.78, #queue-req: 0, 
[2025-07-28 01:44:25 DP1 TP0] Decode batch. #running-req: 1, #token: 507, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.68, #queue-req: 0, 
[2025-07-28 01:44:25 DP0 TP0] Decode batch. #running-req: 1, #token: 755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.83, #queue-req: 0, 
[2025-07-28 01:44:25 DP1 TP0] Decode batch. #running-req: 1, #token: 547, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.43, #queue-req: 0, 
[2025-07-28 01:44:25 DP1 TP0] Decode batch. #running-req: 1, #token: 587, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.54, #queue-req: 0, 
[2025-07-28 01:44:25 DP0 TP0] Decode batch. #running-req: 1, #token: 795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.04, #queue-req: 0, 
[2025-07-28 01:44:26 DP1 TP0] Decode batch. #running-req: 1, #token: 627, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.51, #queue-req: 0, 
[2025-07-28 01:44:26 DP0 TP0] Decode batch. #running-req: 1, #token: 835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.79, #queue-req: 0, 
[2025-07-28 01:44:26 DP1 TP0] Decode batch. #running-req: 1, #token: 667, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.33, #queue-req: 0, 
[2025-07-28 01:44:26 DP0 TP0] Decode batch. #running-req: 1, #token: 875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.91, #queue-req: 0, 
[2025-07-28 01:44:26 DP1 TP0] Decode batch. #running-req: 1, #token: 707, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.56, #queue-req: 0, 
[2025-07-28 01:44:26 DP0 TP0] Decode batch. #running-req: 1, #token: 915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.82, #queue-req: 0, 
[2025-07-28 01:44:26 DP1 TP0] Decode batch. #running-req: 1, #token: 747, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.38, #queue-req: 0, 
[2025-07-28 01:44:26 DP0 TP0] Decode batch. #running-req: 1, #token: 955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.49, #queue-req: 0, 
[2025-07-28 01:44:26 DP1 TP0] Decode batch. #running-req: 1, #token: 787, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.43, #queue-req: 0, 
[2025-07-28 01:44:26 DP0 TP0] Decode batch. #running-req: 1, #token: 995, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.46, #queue-req: 0, 
[2025-07-28 01:44:26 DP1 TP0] Decode batch. #running-req: 1, #token: 827, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.36, #queue-req: 0, 
[2025-07-28 01:44:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.51, #queue-req: 0, 
[2025-07-28 01:44:26 DP1 TP0] Decode batch. #running-req: 1, #token: 867, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.49, #queue-req: 0, 
[2025-07-28 01:44:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1075, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.19, #queue-req: 0, 
[2025-07-28 01:44:27 DP1 TP0] Decode batch. #running-req: 1, #token: 907, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.26, #queue-req: 0, 
[2025-07-28 01:44:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.36, #queue-req: 0, 
[2025-07-28 01:44:27 DP1 TP0] Decode batch. #running-req: 1, #token: 947, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.09, #queue-req: 0, 
[2025-07-28 01:44:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1155, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.75, #queue-req: 0, 
[2025-07-28 01:44:27 DP1 TP0] Decode batch. #running-req: 1, #token: 987, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.98, #queue-req: 0, 
[2025-07-28 01:44:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.63, #queue-req: 0, 
[2025-07-28 01:44:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1027, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.54, #queue-req: 0, 
[2025-07-28 01:44:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.70, #queue-req: 0, 
[2025-07-28 01:44:27] INFO:     127.0.0.1:56612 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:44:27 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 136, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:44:27 DP0 TP0] Decode batch. #running-req: 2, #token: 1425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 250.48, #queue-req: 0, 
[2025-07-28 01:44:28 DP0 TP0] Decode batch. #running-req: 2, #token: 1505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.61, #queue-req: 0, 
[2025-07-28 01:44:28 DP0 TP0] Decode batch. #running-req: 2, #token: 1585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.48, #queue-req: 0, 
[2025-07-28 01:44:28 DP0 TP0] Decode batch. #running-req: 2, #token: 1665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 470.01, #queue-req: 0, 
[2025-07-28 01:44:28 DP0 TP0] Decode batch. #running-req: 2, #token: 1745, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.23, #queue-req: 0, 
[2025-07-28 01:44:28 DP0 TP0] Decode batch. #running-req: 2, #token: 1825, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.52, #queue-req: 0, 
[2025-07-28 01:44:28 DP0 TP0] Decode batch. #running-req: 2, #token: 1905, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.72, #queue-req: 0, 
[2025-07-28 01:44:29 DP0 TP0] Decode batch. #running-req: 2, #token: 1985, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.22, #queue-req: 0, 
[2025-07-28 01:44:29 DP0 TP0] Decode batch. #running-req: 2, #token: 2065, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.16, #queue-req: 0, 
[2025-07-28 01:44:29 DP0 TP0] Decode batch. #running-req: 2, #token: 2145, token usage: 0.00, cuda graph: True, gen throughput (token/s): 468.97, #queue-req: 0, 
[2025-07-28 01:44:29 DP0 TP0] Decode batch. #running-req: 2, #token: 2225, token usage: 0.00, cuda graph: True, gen throughput (token/s): 468.97, #queue-req: 0, 
[2025-07-28 01:44:29 DP0 TP0] Decode batch. #running-req: 2, #token: 2305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.16, #queue-req: 0, 
[2025-07-28 01:44:29 DP0 TP0] Decode batch. #running-req: 2, #token: 2385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.11, #queue-req: 0, 
[2025-07-28 01:44:30 DP0 TP0] Decode batch. #running-req: 2, #token: 2465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 468.89, #queue-req: 0, 
[2025-07-28 01:44:30 DP0 TP0] Decode batch. #running-req: 2, #token: 2545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.30, #queue-req: 0, 
[2025-07-28 01:44:30 DP0 TP0] Decode batch. #running-req: 2, #token: 2625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.10, #queue-req: 0, 
[2025-07-28 01:44:30 DP0 TP0] Decode batch. #running-req: 2, #token: 2705, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.02, #queue-req: 0, 
[2025-07-28 01:44:30 DP0 TP0] Decode batch. #running-req: 2, #token: 2785, token usage: 0.00, cuda graph: True, gen throughput (token/s): 468.71, #queue-req: 0, 
[2025-07-28 01:44:30 DP0 TP0] Decode batch. #running-req: 2, #token: 2865, token usage: 0.00, cuda graph: True, gen throughput (token/s): 468.85, #queue-req: 0, 
[2025-07-28 01:44:31 DP0 TP0] Decode batch. #running-req: 2, #token: 2945, token usage: 0.00, cuda graph: True, gen throughput (token/s): 468.90, #queue-req: 0, 
[2025-07-28 01:44:31 DP0 TP0] Decode batch. #running-req: 2, #token: 3025, token usage: 0.00, cuda graph: True, gen throughput (token/s): 468.62, #queue-req: 0, 
[2025-07-28 01:44:31 DP0 TP0] Decode batch. #running-req: 2, #token: 3105, token usage: 0.00, cuda graph: True, gen throughput (token/s): 468.38, #queue-req: 0, 
[2025-07-28 01:44:31 DP0 TP0] Decode batch. #running-req: 2, #token: 3185, token usage: 0.00, cuda graph: True, gen throughput (token/s): 468.08, #queue-req: 0, 
[2025-07-28 01:44:31 DP0 TP0] Decode batch. #running-req: 2, #token: 3265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.32, #queue-req: 0, 
[2025-07-28 01:44:31] INFO:     127.0.0.1:56618 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:44:31 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 91, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:44:31 DP1 TP0] Decode batch. #running-req: 1, #token: 189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 9.35, #queue-req: 0, 
[2025-07-28 01:44:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 256.03, #queue-req: 0, 
[2025-07-28 01:44:32 DP1 TP0] Decode batch. #running-req: 1, #token: 229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 228.84, #queue-req: 0, 
[2025-07-28 01:44:32 DP0 TP0] Decode batch. #running-req: 1, #token: 2275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.88, #queue-req: 0, 
[2025-07-28 01:44:32 DP1 TP0] Decode batch. #running-req: 1, #token: 269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.13, #queue-req: 0, 
[2025-07-28 01:44:32 DP0 TP0] Decode batch. #running-req: 1, #token: 2315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.48, #queue-req: 0, 
[2025-07-28 01:44:32 DP1 TP0] Decode batch. #running-req: 1, #token: 309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.36, #queue-req: 0, 
[2025-07-28 01:44:32 DP0 TP0] Decode batch. #running-req: 1, #token: 2355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.48, #queue-req: 0, 
[2025-07-28 01:44:32 DP1 TP0] Decode batch. #running-req: 1, #token: 349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.18, #queue-req: 0, 
[2025-07-28 01:44:32 DP0 TP0] Decode batch. #running-req: 1, #token: 2395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.56, #queue-req: 0, 
[2025-07-28 01:44:32 DP1 TP0] Decode batch. #running-req: 1, #token: 389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.26, #queue-req: 0, 
[2025-07-28 01:44:32 DP0 TP0] Decode batch. #running-req: 1, #token: 2435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.11, #queue-req: 0, 
[2025-07-28 01:44:32 DP1 TP0] Decode batch. #running-req: 1, #token: 429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.53, #queue-req: 0, 
[2025-07-28 01:44:32 DP0 TP0] Decode batch. #running-req: 1, #token: 2475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.58, #queue-req: 0, 
[2025-07-28 01:44:33 DP1 TP0] Decode batch. #running-req: 1, #token: 469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.16, #queue-req: 0, 
[2025-07-28 01:44:33 DP0 TP0] Decode batch. #running-req: 1, #token: 2515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.45, #queue-req: 0, 
[2025-07-28 01:44:33 DP1 TP0] Decode batch. #running-req: 1, #token: 509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.08, #queue-req: 0, 
[2025-07-28 01:44:33 DP0 TP0] Decode batch. #running-req: 1, #token: 2555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.34, #queue-req: 0, 
[2025-07-28 01:44:33 DP1 TP0] Decode batch. #running-req: 1, #token: 549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.14, #queue-req: 0, 
[2025-07-28 01:44:33 DP0 TP0] Decode batch. #running-req: 1, #token: 2595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.03, #queue-req: 0, 
[2025-07-28 01:44:33 DP1 TP0] Decode batch. #running-req: 1, #token: 589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.68, #queue-req: 0, 
[2025-07-28 01:44:33 DP0 TP0] Decode batch. #running-req: 1, #token: 2635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.96, #queue-req: 0, 
[2025-07-28 01:44:33 DP1 TP0] Decode batch. #running-req: 1, #token: 629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.37, #queue-req: 0, 
[2025-07-28 01:44:33 DP0 TP0] Decode batch. #running-req: 1, #token: 2675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.16, #queue-req: 0, 
[2025-07-28 01:44:33 DP1 TP0] Decode batch. #running-req: 1, #token: 669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.93, #queue-req: 0, 
[2025-07-28 01:44:33 DP0 TP0] Decode batch. #running-req: 1, #token: 2715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.01, #queue-req: 0, 
[2025-07-28 01:44:34 DP1 TP0] Decode batch. #running-req: 1, #token: 709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.50, #queue-req: 0, 
[2025-07-28 01:44:34 DP0 TP0] Decode batch. #running-req: 1, #token: 2755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.62, #queue-req: 0, 
[2025-07-28 01:44:34 DP1 TP0] Decode batch. #running-req: 1, #token: 749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.03, #queue-req: 0, 
[2025-07-28 01:44:34 DP0 TP0] Decode batch. #running-req: 1, #token: 2795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.99, #queue-req: 0, 
[2025-07-28 01:44:34 DP1 TP0] Decode batch. #running-req: 1, #token: 789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.63, #queue-req: 0, 
[2025-07-28 01:44:34 DP0 TP0] Decode batch. #running-req: 1, #token: 2835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.78, #queue-req: 0, 
[2025-07-28 01:44:34 DP1 TP0] Decode batch. #running-req: 1, #token: 829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.74, #queue-req: 0, 
[2025-07-28 01:44:34 DP0 TP0] Decode batch. #running-req: 1, #token: 2875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.30, #queue-req: 0, 
[2025-07-28 01:44:34 DP1 TP0] Decode batch. #running-req: 1, #token: 869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.95, #queue-req: 0, 
[2025-07-28 01:44:34 DP0 TP0] Decode batch. #running-req: 1, #token: 2915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.84, #queue-req: 0, 
[2025-07-28 01:44:34 DP1 TP0] Decode batch. #running-req: 1, #token: 909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.10, #queue-req: 0, 
[2025-07-28 01:44:34 DP0 TP0] Decode batch. #running-req: 1, #token: 2955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.01, #queue-req: 0, 
[2025-07-28 01:44:34] INFO:     127.0.0.1:56630 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:44:34 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 136, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:44:35 DP0 TP0] Decode batch. #running-req: 2, #token: 3150, token usage: 0.00, cuda graph: True, gen throughput (token/s): 280.38, #queue-req: 0, 
[2025-07-28 01:44:35 DP0 TP0] Decode batch. #running-req: 2, #token: 3230, token usage: 0.00, cuda graph: True, gen throughput (token/s): 468.07, #queue-req: 0, 
[2025-07-28 01:44:35 DP0 TP0] Decode batch. #running-req: 2, #token: 3310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.83, #queue-req: 0, 
[2025-07-28 01:44:35 DP0 TP0] Decode batch. #running-req: 2, #token: 3390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.32, #queue-req: 0, 
[2025-07-28 01:44:35 DP0 TP0] Decode batch. #running-req: 2, #token: 3470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.53, #queue-req: 0, 
[2025-07-28 01:44:35 DP0 TP0] Decode batch. #running-req: 2, #token: 3550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 466.00, #queue-req: 0, 
[2025-07-28 01:44:36 DP0 TP0] Decode batch. #running-req: 2, #token: 3630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.71, #queue-req: 0, 
[2025-07-28 01:44:36 DP0 TP0] Decode batch. #running-req: 2, #token: 3710, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.16, #queue-req: 0, 
[2025-07-28 01:44:36 DP0 TP0] Decode batch. #running-req: 2, #token: 3790, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.25, #queue-req: 0, 
[2025-07-28 01:44:36 DP0 TP0] Decode batch. #running-req: 2, #token: 3870, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.56, #queue-req: 0, 
[2025-07-28 01:44:36 DP0 TP0] Decode batch. #running-req: 2, #token: 3950, token usage: 0.00, cuda graph: True, gen throughput (token/s): 467.37, #queue-req: 0, 
[2025-07-28 01:44:36 DP0 TP0] Decode batch. #running-req: 2, #token: 4030, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.94, #queue-req: 0, 
[2025-07-28 01:44:37 DP0 TP0] Decode batch. #running-req: 2, #token: 4110, token usage: 0.00, cuda graph: True, gen throughput (token/s): 459.71, #queue-req: 0, 
[2025-07-28 01:44:37 DP0 TP0] Decode batch. #running-req: 2, #token: 4190, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.59, #queue-req: 0, 
[2025-07-28 01:44:37 DP0 TP0] Decode batch. #running-req: 2, #token: 4270, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.13, #queue-req: 0, 
[2025-07-28 01:44:37 DP0 TP0] Decode batch. #running-req: 2, #token: 4350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.92, #queue-req: 0, 
[2025-07-28 01:44:37 DP0 TP0] Decode batch. #running-req: 2, #token: 4430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.60, #queue-req: 0, 
[2025-07-28 01:44:38 DP0 TP0] Decode batch. #running-req: 2, #token: 4510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.64, #queue-req: 0, 
[2025-07-28 01:44:38 DP0 TP0] Decode batch. #running-req: 2, #token: 4590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.73, #queue-req: 0, 
[2025-07-28 01:44:38 DP0 TP0] Decode batch. #running-req: 2, #token: 4670, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.87, #queue-req: 0, 
[2025-07-28 01:44:38 DP0 TP0] Decode batch. #running-req: 2, #token: 4750, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.51, #queue-req: 0, 
[2025-07-28 01:44:38 DP0 TP0] Decode batch. #running-req: 2, #token: 4830, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.91, #queue-req: 0, 
[2025-07-28 01:44:38 DP0 TP0] Decode batch. #running-req: 2, #token: 4910, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.54, #queue-req: 0, 
[2025-07-28 01:44:39 DP0 TP0] Decode batch. #running-req: 2, #token: 4990, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.79, #queue-req: 0, 
[2025-07-28 01:44:39 DP0 TP0] Decode batch. #running-req: 2, #token: 5070, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.56, #queue-req: 0, 
[2025-07-28 01:44:39 DP0 TP0] Decode batch. #running-req: 2, #token: 5150, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.63, #queue-req: 0, 
[2025-07-28 01:44:39 DP0 TP0] Decode batch. #running-req: 2, #token: 5230, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.89, #queue-req: 0, 
[2025-07-28 01:44:39 DP0 TP0] Decode batch. #running-req: 2, #token: 5310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.15, #queue-req: 0, 
[2025-07-28 01:44:39 DP0 TP0] Decode batch. #running-req: 2, #token: 5390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.54, #queue-req: 0, 
[2025-07-28 01:44:40 DP0 TP0] Decode batch. #running-req: 2, #token: 5470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.42, #queue-req: 0, 
[2025-07-28 01:44:40 DP0 TP0] Decode batch. #running-req: 2, #token: 5550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.48, #queue-req: 0, 
[2025-07-28 01:44:40 DP0 TP0] Decode batch. #running-req: 2, #token: 5630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.36, #queue-req: 0, 
[2025-07-28 01:44:40 DP0 TP0] Decode batch. #running-req: 2, #token: 5710, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.48, #queue-req: 0, 
[2025-07-28 01:44:40 DP0 TP0] Decode batch. #running-req: 2, #token: 5790, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.28, #queue-req: 0, 
[2025-07-28 01:44:40 DP0 TP0] Decode batch. #running-req: 2, #token: 5870, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.25, #queue-req: 0, 
[2025-07-28 01:44:41 DP0 TP0] Decode batch. #running-req: 2, #token: 5950, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.74, #queue-req: 0, 
[2025-07-28 01:44:41 DP0 TP0] Decode batch. #running-req: 2, #token: 6030, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.09, #queue-req: 0, 
[2025-07-28 01:44:41 DP0 TP0] Decode batch. #running-req: 2, #token: 6110, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.49, #queue-req: 0, 
[2025-07-28 01:44:41 DP0 TP0] Decode batch. #running-req: 2, #token: 6190, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.01, #queue-req: 0, 
[2025-07-28 01:44:41 DP0 TP0] Decode batch. #running-req: 2, #token: 6270, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.61, #queue-req: 0, 
[2025-07-28 01:44:41 DP0 TP0] Decode batch. #running-req: 2, #token: 6350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.85, #queue-req: 0, 
[2025-07-28 01:44:42 DP0 TP0] Decode batch. #running-req: 2, #token: 6430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.59, #queue-req: 0, 
[2025-07-28 01:44:42 DP0 TP0] Decode batch. #running-req: 2, #token: 6510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.61, #queue-req: 0, 
[2025-07-28 01:44:42 DP0 TP0] Decode batch. #running-req: 2, #token: 6590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.51, #queue-req: 0, 
[2025-07-28 01:44:42 DP0 TP0] Decode batch. #running-req: 2, #token: 6670, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.53, #queue-req: 0, 
[2025-07-28 01:44:42 DP0 TP0] Decode batch. #running-req: 2, #token: 6750, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.40, #queue-req: 0, 
[2025-07-28 01:44:42 DP0 TP0] Decode batch. #running-req: 2, #token: 6830, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.16, #queue-req: 0, 
[2025-07-28 01:44:43 DP0 TP0] Decode batch. #running-req: 2, #token: 6910, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.77, #queue-req: 0, 
[2025-07-28 01:44:43 DP0 TP0] Decode batch. #running-req: 2, #token: 6990, token usage: 0.00, cuda graph: True, gen throughput (token/s): 465.36, #queue-req: 0, 
[2025-07-28 01:44:43 DP0 TP0] Decode batch. #running-req: 2, #token: 7070, token usage: 0.00, cuda graph: True, gen throughput (token/s): 464.72, #queue-req: 0, 
[2025-07-28 01:44:43 DP0 TP0] Decode batch. #running-req: 2, #token: 7150, token usage: 0.00, cuda graph: True, gen throughput (token/s): 441.57, #queue-req: 0, 
[2025-07-28 01:44:43 DP0 TP0] Decode batch. #running-req: 2, #token: 7230, token usage: 0.00, cuda graph: True, gen throughput (token/s): 459.00, #queue-req: 0, 
[2025-07-28 01:44:44 DP0 TP0] Decode batch. #running-req: 2, #token: 7310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 456.77, #queue-req: 0, 
[2025-07-28 01:44:44 DP0 TP0] Decode batch. #running-req: 2, #token: 7390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 449.71, #queue-req: 0, 
[2025-07-28 01:44:44 DP0 TP0] Decode batch. #running-req: 2, #token: 7470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 456.16, #queue-req: 0, 
[2025-07-28 01:44:44 DP0 TP0] Decode batch. #running-req: 2, #token: 7550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 457.02, #queue-req: 0, 
[2025-07-28 01:44:44 DP0 TP0] Decode batch. #running-req: 2, #token: 7630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.20, #queue-req: 0, 
[2025-07-28 01:44:44 DP0 TP0] Decode batch. #running-req: 2, #token: 7710, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.09, #queue-req: 0, 
[2025-07-28 01:44:45 DP0 TP0] Decode batch. #running-req: 2, #token: 7790, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.29, #queue-req: 0, 
[2025-07-28 01:44:45 DP0 TP0] Decode batch. #running-req: 2, #token: 7870, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.49, #queue-req: 0, 
[2025-07-28 01:44:45 DP0 TP0] Decode batch. #running-req: 2, #token: 7950, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.79, #queue-req: 0, 
[2025-07-28 01:44:45 DP0 TP0] Decode batch. #running-req: 2, #token: 8030, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.45, #queue-req: 0, 
[2025-07-28 01:44:45 DP0 TP0] Decode batch. #running-req: 2, #token: 8110, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.43, #queue-req: 0, 
[2025-07-28 01:44:45 DP0 TP0] Decode batch. #running-req: 2, #token: 8190, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.28, #queue-req: 0, 
[2025-07-28 01:44:46 DP0 TP0] Decode batch. #running-req: 2, #token: 8270, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.27, #queue-req: 0, 
[2025-07-28 01:44:46 DP0 TP0] Decode batch. #running-req: 2, #token: 8350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.12, #queue-req: 0, 
[2025-07-28 01:44:46 DP0 TP0] Decode batch. #running-req: 2, #token: 8430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.98, #queue-req: 0, 
[2025-07-28 01:44:46 DP0 TP0] Decode batch. #running-req: 2, #token: 8510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.70, #queue-req: 0, 
[2025-07-28 01:44:46 DP0 TP0] Decode batch. #running-req: 2, #token: 8590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.27, #queue-req: 0, 
[2025-07-28 01:44:46 DP0 TP0] Decode batch. #running-req: 2, #token: 8670, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.73, #queue-req: 0, 
[2025-07-28 01:44:47 DP0 TP0] Decode batch. #running-req: 2, #token: 8750, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.53, #queue-req: 0, 
[2025-07-28 01:44:47 DP0 TP0] Decode batch. #running-req: 2, #token: 8830, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.61, #queue-req: 0, 
[2025-07-28 01:44:47 DP0 TP0] Decode batch. #running-req: 2, #token: 8910, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.95, #queue-req: 0, 
[2025-07-28 01:44:47 DP0 TP0] Decode batch. #running-req: 2, #token: 8990, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.00, #queue-req: 0, 
[2025-07-28 01:44:47 DP0 TP0] Decode batch. #running-req: 2, #token: 9070, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.07, #queue-req: 0, 
[2025-07-28 01:44:48 DP0 TP0] Decode batch. #running-req: 2, #token: 9150, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.18, #queue-req: 0, 
[2025-07-28 01:44:48 DP0 TP0] Decode batch. #running-req: 2, #token: 9230, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.02, #queue-req: 0, 
[2025-07-28 01:44:48 DP0 TP0] Decode batch. #running-req: 2, #token: 9310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.22, #queue-req: 0, 
[2025-07-28 01:44:48 DP0 TP0] Decode batch. #running-req: 2, #token: 9390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.69, #queue-req: 0, 
[2025-07-28 01:44:48 DP0 TP0] Decode batch. #running-req: 2, #token: 9470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.42, #queue-req: 0, 
[2025-07-28 01:44:48 DP0 TP0] Decode batch. #running-req: 2, #token: 9550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 459.64, #queue-req: 0, 
[2025-07-28 01:44:49 DP0 TP0] Decode batch. #running-req: 2, #token: 9630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.31, #queue-req: 0, 
[2025-07-28 01:44:49 DP0 TP0] Decode batch. #running-req: 2, #token: 9710, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.60, #queue-req: 0, 
[2025-07-28 01:44:49 DP0 TP0] Decode batch. #running-req: 2, #token: 9790, token usage: 0.00, cuda graph: True, gen throughput (token/s): 458.74, #queue-req: 0, 
[2025-07-28 01:44:49 DP0 TP0] Decode batch. #running-req: 2, #token: 9870, token usage: 0.00, cuda graph: True, gen throughput (token/s): 454.02, #queue-req: 0, 
[2025-07-28 01:44:49 DP0 TP0] Decode batch. #running-req: 2, #token: 9950, token usage: 0.00, cuda graph: True, gen throughput (token/s): 455.55, #queue-req: 0, 
[2025-07-28 01:44:49 DP0 TP0] Decode batch. #running-req: 2, #token: 10030, token usage: 0.00, cuda graph: True, gen throughput (token/s): 456.96, #queue-req: 0, 
[2025-07-28 01:44:50 DP0 TP0] Decode batch. #running-req: 2, #token: 10110, token usage: 0.00, cuda graph: True, gen throughput (token/s): 457.96, #queue-req: 0, 
[2025-07-28 01:44:50 DP0 TP0] Decode batch. #running-req: 2, #token: 10190, token usage: 0.00, cuda graph: True, gen throughput (token/s): 457.94, #queue-req: 0, 
[2025-07-28 01:44:50 DP0 TP0] Decode batch. #running-req: 2, #token: 10270, token usage: 0.00, cuda graph: True, gen throughput (token/s): 458.95, #queue-req: 0, 
[2025-07-28 01:44:50 DP0 TP0] Decode batch. #running-req: 2, #token: 10350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 458.89, #queue-req: 0, 
[2025-07-28 01:44:50 DP0 TP0] Decode batch. #running-req: 2, #token: 10430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 459.70, #queue-req: 0, 
[2025-07-28 01:44:50 DP0 TP0] Decode batch. #running-req: 2, #token: 10510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 459.58, #queue-req: 0, 
[2025-07-28 01:44:51 DP0 TP0] Decode batch. #running-req: 2, #token: 10590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 459.80, #queue-req: 0, 
[2025-07-28 01:44:51 DP0 TP0] Decode batch. #running-req: 2, #token: 10670, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.27, #queue-req: 0, 
[2025-07-28 01:44:51 DP0 TP0] Decode batch. #running-req: 2, #token: 10750, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.36, #queue-req: 0, 
[2025-07-28 01:44:51 DP0 TP0] Decode batch. #running-req: 2, #token: 10830, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.25, #queue-req: 0, 
[2025-07-28 01:44:51 DP0 TP0] Decode batch. #running-req: 2, #token: 10910, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.72, #queue-req: 0, 
[2025-07-28 01:44:52 DP0 TP0] Decode batch. #running-req: 2, #token: 10990, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.56, #queue-req: 0, 
[2025-07-28 01:44:52 DP0 TP0] Decode batch. #running-req: 2, #token: 11070, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.77, #queue-req: 0, 
[2025-07-28 01:44:52 DP0 TP0] Decode batch. #running-req: 2, #token: 11150, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.02, #queue-req: 0, 
[2025-07-28 01:44:52 DP0 TP0] Decode batch. #running-req: 2, #token: 11230, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.39, #queue-req: 0, 
[2025-07-28 01:44:52 DP0 TP0] Decode batch. #running-req: 2, #token: 11310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.18, #queue-req: 0, 
[2025-07-28 01:44:52 DP0 TP0] Decode batch. #running-req: 2, #token: 11390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.36, #queue-req: 0, 
[2025-07-28 01:44:53 DP0 TP0] Decode batch. #running-req: 2, #token: 11470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.33, #queue-req: 0, 
[2025-07-28 01:44:53 DP0 TP0] Decode batch. #running-req: 2, #token: 11550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 463.27, #queue-req: 0, 
[2025-07-28 01:44:53 DP0 TP0] Decode batch. #running-req: 2, #token: 11630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.28, #queue-req: 0, 
[2025-07-28 01:44:53 DP0 TP0] Decode batch. #running-req: 2, #token: 11710, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.43, #queue-req: 0, 
[2025-07-28 01:44:53 DP0 TP0] Decode batch. #running-req: 2, #token: 11790, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.71, #queue-req: 0, 
[2025-07-28 01:44:53 DP0 TP0] Decode batch. #running-req: 2, #token: 11870, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.68, #queue-req: 0, 
[2025-07-28 01:44:54 DP0 TP0] Decode batch. #running-req: 2, #token: 11950, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.34, #queue-req: 0, 
[2025-07-28 01:44:54 DP0 TP0] Decode batch. #running-req: 2, #token: 12030, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.47, #queue-req: 0, 
[2025-07-28 01:44:54 DP0 TP0] Decode batch. #running-req: 2, #token: 12110, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.77, #queue-req: 0, 
[2025-07-28 01:44:54 DP0 TP0] Decode batch. #running-req: 2, #token: 12190, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.58, #queue-req: 0, 
[2025-07-28 01:44:54 DP0 TP0] Decode batch. #running-req: 2, #token: 12270, token usage: 0.00, cuda graph: True, gen throughput (token/s): 462.21, #queue-req: 0, 
[2025-07-28 01:44:54 DP0 TP0] Decode batch. #running-req: 2, #token: 12350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.84, #queue-req: 0, 
[2025-07-28 01:44:55 DP0 TP0] Decode batch. #running-req: 2, #token: 12430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.47, #queue-req: 0, 
[2025-07-28 01:44:55 DP0 TP0] Decode batch. #running-req: 2, #token: 12510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.73, #queue-req: 0, 
[2025-07-28 01:44:55 DP0 TP0] Decode batch. #running-req: 2, #token: 12590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.77, #queue-req: 0, 
[2025-07-28 01:44:55 DP0 TP0] Decode batch. #running-req: 2, #token: 12670, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.43, #queue-req: 0, 
[2025-07-28 01:44:55 DP0 TP0] Decode batch. #running-req: 2, #token: 12750, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.23, #queue-req: 0, 
[2025-07-28 01:44:56 DP0 TP0] Decode batch. #running-req: 2, #token: 12830, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.42, #queue-req: 0, 
[2025-07-28 01:44:56 DP0 TP0] Decode batch. #running-req: 2, #token: 12910, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.30, #queue-req: 0, 
[2025-07-28 01:44:56 DP0 TP0] Decode batch. #running-req: 2, #token: 12990, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.84, #queue-req: 0, 
[2025-07-28 01:44:56 DP0 TP0] Decode batch. #running-req: 2, #token: 13070, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.98, #queue-req: 0, 
[2025-07-28 01:44:56 DP0 TP0] Decode batch. #running-req: 2, #token: 13150, token usage: 0.00, cuda graph: True, gen throughput (token/s): 461.19, #queue-req: 0, 
[2025-07-28 01:44:56 DP0 TP0] Decode batch. #running-req: 2, #token: 13230, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.72, #queue-req: 0, 
[2025-07-28 01:44:57 DP0 TP0] Decode batch. #running-req: 2, #token: 13310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.09, #queue-req: 0, 
[2025-07-28 01:44:57 DP0 TP0] Decode batch. #running-req: 2, #token: 13390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.55, #queue-req: 0, 
[2025-07-28 01:44:57 DP0 TP0] Decode batch. #running-req: 2, #token: 13470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.13, #queue-req: 0, 
[2025-07-28 01:44:57 DP0 TP0] Decode batch. #running-req: 2, #token: 13550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 460.17, #queue-req: 0, 
[2025-07-28 01:44:57 DP0 TP0] Decode batch. #running-req: 2, #token: 13630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 456.75, #queue-req: 0, 
[2025-07-28 01:44:57 DP0 TP0] Decode batch. #running-req: 2, #token: 13710, token usage: 0.00, cuda graph: True, gen throughput (token/s): 457.44, #queue-req: 0, 
[2025-07-28 01:44:58 DP0 TP0] Decode batch. #running-req: 2, #token: 13790, token usage: 0.00, cuda graph: True, gen throughput (token/s): 454.73, #queue-req: 0, 
[2025-07-28 01:44:58 DP0 TP0] Decode batch. #running-req: 2, #token: 13870, token usage: 0.00, cuda graph: True, gen throughput (token/s): 451.50, #queue-req: 0, 
[2025-07-28 01:44:58 DP0 TP0] Decode batch. #running-req: 2, #token: 13950, token usage: 0.00, cuda graph: True, gen throughput (token/s): 451.12, #queue-req: 0, 
[2025-07-28 01:44:58] INFO:     127.0.0.1:56604 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:44:58 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 74, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:44:58 DP0 TP0] Decode batch. #running-req: 1, #token: 5680, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.15, #queue-req: 0, 
[2025-07-28 01:44:58 DP1 TP0] Decode batch. #running-req: 1, #token: 174, token usage: 0.00, cuda graph: True, gen throughput (token/s): 1.68, #queue-req: 0, 
[2025-07-28 01:44:58 DP0 TP0] Decode batch. #running-req: 1, #token: 5720, token usage: 0.00, cuda graph: True, gen throughput (token/s): 243.93, #queue-req: 0, 
[2025-07-28 01:44:58] INFO:     127.0.0.1:55998 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:44:58 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 142, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:44:58 DP1 TP0] Decode batch. #running-req: 1, #token: 214, token usage: 0.00, cuda graph: True, gen throughput (token/s): 221.41, #queue-req: 0, 
[2025-07-28 01:44:59 DP0 TP0] Decode batch. #running-req: 1, #token: 249, token usage: 0.00, cuda graph: True, gen throughput (token/s): 185.85, #queue-req: 0, 
[2025-07-28 01:44:59 DP1 TP0] Decode batch. #running-req: 1, #token: 254, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.07, #queue-req: 0, 
[2025-07-28 01:44:59 DP0 TP0] Decode batch. #running-req: 1, #token: 289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.29, #queue-req: 0, 
[2025-07-28 01:44:59 DP1 TP0] Decode batch. #running-req: 1, #token: 294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.97, #queue-req: 0, 
[2025-07-28 01:44:59 DP0 TP0] Decode batch. #running-req: 1, #token: 329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.27, #queue-req: 0, 
[2025-07-28 01:44:59 DP1 TP0] Decode batch. #running-req: 1, #token: 334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.98, #queue-req: 0, 
[2025-07-28 01:44:59 DP0 TP0] Decode batch. #running-req: 1, #token: 369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.27, #queue-req: 0, 
[2025-07-28 01:44:59 DP1 TP0] Decode batch. #running-req: 1, #token: 374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.77, #queue-req: 0, 
[2025-07-28 01:44:59 DP0 TP0] Decode batch. #running-req: 1, #token: 409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.25, #queue-req: 0, 
[2025-07-28 01:44:59 DP1 TP0] Decode batch. #running-req: 1, #token: 414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.09, #queue-req: 0, 
[2025-07-28 01:44:59 DP0 TP0] Decode batch. #running-req: 1, #token: 449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.12, #queue-req: 0, 
[2025-07-28 01:44:59 DP1 TP0] Decode batch. #running-req: 1, #token: 454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.98, #queue-req: 0, 
[2025-07-28 01:44:59 DP0 TP0] Decode batch. #running-req: 1, #token: 489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.39, #queue-req: 0, 
[2025-07-28 01:45:00 DP1 TP0] Decode batch. #running-req: 1, #token: 494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.11, #queue-req: 0, 
[2025-07-28 01:45:00 DP0 TP0] Decode batch. #running-req: 1, #token: 529, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.93, #queue-req: 0, 
[2025-07-28 01:45:00 DP1 TP0] Decode batch. #running-req: 1, #token: 534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.11, #queue-req: 0, 
[2025-07-28 01:45:00 DP0 TP0] Decode batch. #running-req: 1, #token: 569, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.25, #queue-req: 0, 
[2025-07-28 01:45:00 DP1 TP0] Decode batch. #running-req: 1, #token: 574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.05, #queue-req: 0, 
[2025-07-28 01:45:00 DP0 TP0] Decode batch. #running-req: 1, #token: 609, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.88, #queue-req: 0, 
[2025-07-28 01:45:00 DP1 TP0] Decode batch. #running-req: 1, #token: 614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.93, #queue-req: 0, 
[2025-07-28 01:45:00 DP0 TP0] Decode batch. #running-req: 1, #token: 649, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.12, #queue-req: 0, 
[2025-07-28 01:45:00 DP1 TP0] Decode batch. #running-req: 1, #token: 654, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.04, #queue-req: 0, 
[2025-07-28 01:45:00 DP0 TP0] Decode batch. #running-req: 1, #token: 689, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.26, #queue-req: 0, 
[2025-07-28 01:45:00 DP1 TP0] Decode batch. #running-req: 1, #token: 694, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.86, #queue-req: 0, 
[2025-07-28 01:45:00 DP0 TP0] Decode batch. #running-req: 1, #token: 729, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.11, #queue-req: 0, 
[2025-07-28 01:45:01 DP1 TP0] Decode batch. #running-req: 1, #token: 734, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.10, #queue-req: 0, 
[2025-07-28 01:45:01 DP0 TP0] Decode batch. #running-req: 1, #token: 769, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.52, #queue-req: 0, 
[2025-07-28 01:45:01 DP1 TP0] Decode batch. #running-req: 1, #token: 774, token usage: 0.00, cuda graph: True, gen throughput (token/s): 241.26, #queue-req: 0, 
[2025-07-28 01:45:01 DP0 TP0] Decode batch. #running-req: 1, #token: 809, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.12, #queue-req: 0, 
[2025-07-28 01:45:01 DP1 TP0] Decode batch. #running-req: 1, #token: 814, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.02, #queue-req: 0, 
[2025-07-28 01:45:01 DP0 TP0] Decode batch. #running-req: 1, #token: 849, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.92, #queue-req: 0, 
[2025-07-28 01:45:01 DP1 TP0] Decode batch. #running-req: 1, #token: 854, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.32, #queue-req: 0, 
[2025-07-28 01:45:01 DP0 TP0] Decode batch. #running-req: 1, #token: 889, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.10, #queue-req: 0, 
[2025-07-28 01:45:01 DP1 TP0] Decode batch. #running-req: 1, #token: 894, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.12, #queue-req: 0, 
[2025-07-28 01:45:01 DP0 TP0] Decode batch. #running-req: 1, #token: 929, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.59, #queue-req: 0, 
[2025-07-28 01:45:01] INFO:     127.0.0.1:36788 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:45:01 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 80, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:45:01 DP1 TP0] Decode batch. #running-req: 1, #token: 176, token usage: 0.00, cuda graph: True, gen throughput (token/s): 187.55, #queue-req: 0, 
[2025-07-28 01:45:01] INFO:     127.0.0.1:36792 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:45:01 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 169, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:45:01 DP0 TP0] Decode batch. #running-req: 1, #token: 258, token usage: 0.00, cuda graph: True, gen throughput (token/s): 184.59, #queue-req: 0, 
[2025-07-28 01:45:02 DP1 TP0] Decode batch. #running-req: 1, #token: 216, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.26, #queue-req: 0, 
[2025-07-28 01:45:02 DP0 TP0] Decode batch. #running-req: 1, #token: 298, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.69, #queue-req: 0, 
[2025-07-28 01:45:02 DP1 TP0] Decode batch. #running-req: 1, #token: 256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.32, #queue-req: 0, 
[2025-07-28 01:45:02 DP0 TP0] Decode batch. #running-req: 1, #token: 338, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.70, #queue-req: 0, 
[2025-07-28 01:45:02 DP1 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.41, #queue-req: 0, 
[2025-07-28 01:45:02 DP0 TP0] Decode batch. #running-req: 1, #token: 378, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.29, #queue-req: 0, 
[2025-07-28 01:45:02 DP1 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.28, #queue-req: 0, 
[2025-07-28 01:45:02 DP0 TP0] Decode batch. #running-req: 1, #token: 418, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.25, #queue-req: 0, 
[2025-07-28 01:45:02 DP1 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.35, #queue-req: 0, 
[2025-07-28 01:45:02 DP0 TP0] Decode batch. #running-req: 1, #token: 458, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.41, #queue-req: 0, 
[2025-07-28 01:45:02 DP1 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.89, #queue-req: 0, 
[2025-07-28 01:45:02 DP0 TP0] Decode batch. #running-req: 1, #token: 498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.47, #queue-req: 0, 
[2025-07-28 01:45:03 DP1 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.40, #queue-req: 0, 
[2025-07-28 01:45:03 DP0 TP0] Decode batch. #running-req: 1, #token: 538, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.68, #queue-req: 0, 
[2025-07-28 01:45:03 DP1 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.50, #queue-req: 0, 
[2025-07-28 01:45:03 DP0 TP0] Decode batch. #running-req: 1, #token: 578, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.47, #queue-req: 0, 
[2025-07-28 01:45:03 DP1 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.28, #queue-req: 0, 
[2025-07-28 01:45:03 DP0 TP0] Decode batch. #running-req: 1, #token: 618, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.83, #queue-req: 0, 
[2025-07-28 01:45:03 DP1 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.44, #queue-req: 0, 
[2025-07-28 01:45:03 DP0 TP0] Decode batch. #running-req: 1, #token: 658, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.87, #queue-req: 0, 
[2025-07-28 01:45:03 DP1 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.92, #queue-req: 0, 
[2025-07-28 01:45:03 DP0 TP0] Decode batch. #running-req: 1, #token: 698, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.70, #queue-req: 0, 
[2025-07-28 01:45:03 DP1 TP0] Decode batch. #running-req: 1, #token: 656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.47, #queue-req: 0, 
[2025-07-28 01:45:03 DP0 TP0] Decode batch. #running-req: 1, #token: 738, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.96, #queue-req: 0, 
[2025-07-28 01:45:03 DP1 TP0] Decode batch. #running-req: 1, #token: 696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.18, #queue-req: 0, 
[2025-07-28 01:45:03] INFO:     127.0.0.1:36796 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:45:04 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 174, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:45:04 DP0 TP0] Decode batch. #running-req: 1, #token: 778, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.86, #queue-req: 0, 
[2025-07-28 01:45:04 DP1 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 189.03, #queue-req: 0, 
[2025-07-28 01:45:04 DP0 TP0] Decode batch. #running-req: 1, #token: 818, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.72, #queue-req: 0, 
[2025-07-28 01:45:04 DP1 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.35, #queue-req: 0, 
[2025-07-28 01:45:04 DP0 TP0] Decode batch. #running-req: 1, #token: 858, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.92, #queue-req: 0, 
[2025-07-28 01:45:04 DP1 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.70, #queue-req: 0, 
[2025-07-28 01:45:04 DP0 TP0] Decode batch. #running-req: 1, #token: 898, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.92, #queue-req: 0, 
[2025-07-28 01:45:04 DP1 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.68, #queue-req: 0, 
[2025-07-28 01:45:04 DP0 TP0] Decode batch. #running-req: 1, #token: 938, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.51, #queue-req: 0, 
[2025-07-28 01:45:04 DP1 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.68, #queue-req: 0, 
[2025-07-28 01:45:04 DP0 TP0] Decode batch. #running-req: 1, #token: 978, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.69, #queue-req: 0, 
[2025-07-28 01:45:04] INFO:     127.0.0.1:36800 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:45:04 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 84, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:45:04 DP1 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.53, #queue-req: 0, 
[2025-07-28 01:45:05 DP0 TP0] Decode batch. #running-req: 1, #token: 196, token usage: 0.00, cuda graph: True, gen throughput (token/s): 189.81, #queue-req: 0, 
[2025-07-28 01:45:05 DP1 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.41, #queue-req: 0, 
[2025-07-28 01:45:05 DP0 TP0] Decode batch. #running-req: 1, #token: 236, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.24, #queue-req: 0, 
[2025-07-28 01:45:05 DP1 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.65, #queue-req: 0, 
[2025-07-28 01:45:05 DP0 TP0] Decode batch. #running-req: 1, #token: 276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.47, #queue-req: 0, 
[2025-07-28 01:45:05 DP1 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.51, #queue-req: 0, 
[2025-07-28 01:45:05 DP0 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.20, #queue-req: 0, 
[2025-07-28 01:45:05 DP1 TP0] Decode batch. #running-req: 1, #token: 656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.53, #queue-req: 0, 
[2025-07-28 01:45:05 DP0 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.98, #queue-req: 0, 
[2025-07-28 01:45:05 DP1 TP0] Decode batch. #running-req: 1, #token: 696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.52, #queue-req: 0, 
[2025-07-28 01:45:05 DP0 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.17, #queue-req: 0, 
[2025-07-28 01:45:05 DP1 TP0] Decode batch. #running-req: 1, #token: 736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.05, #queue-req: 0, 
[2025-07-28 01:45:06 DP0 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.85, #queue-req: 0, 
[2025-07-28 01:45:06 DP1 TP0] Decode batch. #running-req: 1, #token: 776, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.87, #queue-req: 0, 
[2025-07-28 01:45:06 DP0 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.90, #queue-req: 0, 
[2025-07-28 01:45:06 DP1 TP0] Decode batch. #running-req: 1, #token: 816, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.69, #queue-req: 0, 
[2025-07-28 01:45:06 DP0 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.80, #queue-req: 0, 
[2025-07-28 01:45:06 DP1 TP0] Decode batch. #running-req: 1, #token: 856, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.59, #queue-req: 0, 
[2025-07-28 01:45:06 DP0 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.79, #queue-req: 0, 
[2025-07-28 01:45:06 DP1 TP0] Decode batch. #running-req: 1, #token: 896, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.47, #queue-req: 0, 
[2025-07-28 01:45:06 DP0 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.25, #queue-req: 0, 
[2025-07-28 01:45:06] INFO:     127.0.0.1:56692 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:45:06 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 149, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:45:06 DP1 TP0] Decode batch. #running-req: 1, #token: 245, token usage: 0.00, cuda graph: True, gen throughput (token/s): 185.89, #queue-req: 0, 
[2025-07-28 01:45:06 DP0 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.20, #queue-req: 0, 
[2025-07-28 01:45:06] INFO:     127.0.0.1:56704 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:45:06 DP1 TP0] Decode batch. #running-req: 1, #token: 285, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.35, #queue-req: 0, 
[2025-07-28 01:45:07 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 119, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:45:07 DP0 TP0] Decode batch. #running-req: 1, #token: 216, token usage: 0.00, cuda graph: True, gen throughput (token/s): 188.71, #queue-req: 0, 
[2025-07-28 01:45:07 DP1 TP0] Decode batch. #running-req: 1, #token: 325, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.17, #queue-req: 0, 
[2025-07-28 01:45:07 DP0 TP0] Decode batch. #running-req: 1, #token: 256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.19, #queue-req: 0, 
[2025-07-28 01:45:07 DP1 TP0] Decode batch. #running-req: 1, #token: 365, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.63, #queue-req: 0, 
[2025-07-28 01:45:07 DP0 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.34, #queue-req: 0, 
[2025-07-28 01:45:07 DP1 TP0] Decode batch. #running-req: 1, #token: 405, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.56, #queue-req: 0, 
[2025-07-28 01:45:07 DP0 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.30, #queue-req: 0, 
[2025-07-28 01:45:07 DP1 TP0] Decode batch. #running-req: 1, #token: 445, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.46, #queue-req: 0, 
[2025-07-28 01:45:07 DP0 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.44, #queue-req: 0, 
[2025-07-28 01:45:07 DP1 TP0] Decode batch. #running-req: 1, #token: 485, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.34, #queue-req: 0, 
[2025-07-28 01:45:07 DP0 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.29, #queue-req: 0, 
[2025-07-28 01:45:07 DP1 TP0] Decode batch. #running-req: 1, #token: 525, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.79, #queue-req: 0, 
[2025-07-28 01:45:08 DP0 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.32, #queue-req: 0, 
[2025-07-28 01:45:08 DP1 TP0] Decode batch. #running-req: 1, #token: 565, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.40, #queue-req: 0, 
[2025-07-28 01:45:08 DP0 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.49, #queue-req: 0, 
[2025-07-28 01:45:08 DP1 TP0] Decode batch. #running-req: 1, #token: 605, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.20, #queue-req: 0, 
[2025-07-28 01:45:08 DP0 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.14, #queue-req: 0, 
[2025-07-28 01:45:08 DP1 TP0] Decode batch. #running-req: 1, #token: 645, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.35, #queue-req: 0, 
[2025-07-28 01:45:08 DP0 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.43, #queue-req: 0, 
[2025-07-28 01:45:08 DP1 TP0] Decode batch. #running-req: 1, #token: 685, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.64, #queue-req: 0, 
[2025-07-28 01:45:08 DP0 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.27, #queue-req: 0, 
[2025-07-28 01:45:08 DP1 TP0] Decode batch. #running-req: 1, #token: 725, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.40, #queue-req: 0, 
[2025-07-28 01:45:08 DP0 TP0] Decode batch. #running-req: 1, #token: 656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.27, #queue-req: 0, 
[2025-07-28 01:45:08 DP1 TP0] Decode batch. #running-req: 1, #token: 765, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.67, #queue-req: 0, 
[2025-07-28 01:45:09 DP0 TP0] Decode batch. #running-req: 1, #token: 696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.31, #queue-req: 0, 
[2025-07-28 01:45:09 DP1 TP0] Decode batch. #running-req: 1, #token: 805, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.21, #queue-req: 0, 
[2025-07-28 01:45:09 DP0 TP0] Decode batch. #running-req: 1, #token: 736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.09, #queue-req: 0, 
[2025-07-28 01:45:09 DP1 TP0] Decode batch. #running-req: 1, #token: 845, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.42, #queue-req: 0, 
[2025-07-28 01:45:09 DP0 TP0] Decode batch. #running-req: 1, #token: 776, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.21, #queue-req: 0, 
[2025-07-28 01:45:09 DP1 TP0] Decode batch. #running-req: 1, #token: 885, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.03, #queue-req: 0, 
[2025-07-28 01:45:09 DP0 TP0] Decode batch. #running-req: 1, #token: 816, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.87, #queue-req: 0, 
[2025-07-28 01:45:09 DP1 TP0] Decode batch. #running-req: 1, #token: 925, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.56, #queue-req: 0, 
[2025-07-28 01:45:09 DP0 TP0] Decode batch. #running-req: 1, #token: 856, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.30, #queue-req: 0, 
[2025-07-28 01:45:09 DP1 TP0] Decode batch. #running-req: 1, #token: 965, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.34, #queue-req: 0, 
[2025-07-28 01:45:09 DP0 TP0] Decode batch. #running-req: 1, #token: 896, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.11, #queue-req: 0, 
[2025-07-28 01:45:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1005, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.84, #queue-req: 0, 
[2025-07-28 01:45:10 DP0 TP0] Decode batch. #running-req: 1, #token: 936, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.22, #queue-req: 0, 
[2025-07-28 01:45:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1045, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.92, #queue-req: 0, 
[2025-07-28 01:45:10 DP0 TP0] Decode batch. #running-req: 1, #token: 976, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.89, #queue-req: 0, 
[2025-07-28 01:45:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1085, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.43, #queue-req: 0, 
[2025-07-28 01:45:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1016, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.97, #queue-req: 0, 
[2025-07-28 01:45:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1125, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.10, #queue-req: 0, 
[2025-07-28 01:45:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1056, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.66, #queue-req: 0, 
[2025-07-28 01:45:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1165, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.69, #queue-req: 0, 
[2025-07-28 01:45:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1096, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.74, #queue-req: 0, 
[2025-07-28 01:45:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1205, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.86, #queue-req: 0, 
[2025-07-28 01:45:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1136, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.47, #queue-req: 0, 
[2025-07-28 01:45:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1245, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.57, #queue-req: 0, 
[2025-07-28 01:45:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1176, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.65, #queue-req: 0, 
[2025-07-28 01:45:11 DP1 TP0] Decode batch. #running-req: 1, #token: 1285, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.19, #queue-req: 0, 
[2025-07-28 01:45:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1216, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.46, #queue-req: 0, 
[2025-07-28 01:45:11 DP1 TP0] Decode batch. #running-req: 1, #token: 1325, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.84, #queue-req: 0, 
[2025-07-28 01:45:11] INFO:     127.0.0.1:56712 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:45:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.56, #queue-req: 0, 
[2025-07-28 01:45:11 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 125, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:45:11] INFO:     127.0.0.1:56714 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:45:11 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 123, #cached-token: 90, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:45:11 DP1 TP0] Decode batch. #running-req: 1, #token: 237, token usage: 0.00, cuda graph: True, gen throughput (token/s): 184.61, #queue-req: 0, 
[2025-07-28 01:45:11 DP0 TP0] Decode batch. #running-req: 1, #token: 249, token usage: 0.00, cuda graph: True, gen throughput (token/s): 187.53, #queue-req: 0, 
[2025-07-28 01:45:11 DP1 TP0] Decode batch. #running-req: 1, #token: 277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.47, #queue-req: 0, 
[2025-07-28 01:45:11 DP0 TP0] Decode batch. #running-req: 1, #token: 289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.22, #queue-req: 0, 
[2025-07-28 01:45:11 DP1 TP0] Decode batch. #running-req: 1, #token: 317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.29, #queue-req: 0, 
[2025-07-28 01:45:11 DP0 TP0] Decode batch. #running-req: 1, #token: 329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.34, #queue-req: 0, 
[2025-07-28 01:45:11 DP1 TP0] Decode batch. #running-req: 1, #token: 357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.43, #queue-req: 0, 
[2025-07-28 01:45:11 DP0 TP0] Decode batch. #running-req: 1, #token: 369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.13, #queue-req: 0, 
[2025-07-28 01:45:12 DP1 TP0] Decode batch. #running-req: 1, #token: 397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.06, #queue-req: 0, 
[2025-07-28 01:45:12 DP0 TP0] Decode batch. #running-req: 1, #token: 409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.22, #queue-req: 0, 
[2025-07-28 01:45:12 DP1 TP0] Decode batch. #running-req: 1, #token: 437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.29, #queue-req: 0, 
[2025-07-28 01:45:12 DP0 TP0] Decode batch. #running-req: 1, #token: 449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.11, #queue-req: 0, 
[2025-07-28 01:45:12 DP1 TP0] Decode batch. #running-req: 1, #token: 477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.37, #queue-req: 0, 
[2025-07-28 01:45:12 DP0 TP0] Decode batch. #running-req: 1, #token: 489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.20, #queue-req: 0, 
[2025-07-28 01:45:12 DP1 TP0] Decode batch. #running-req: 1, #token: 517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.32, #queue-req: 0, 
[2025-07-28 01:45:12 DP0 TP0] Decode batch. #running-req: 1, #token: 529, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.01, #queue-req: 0, 
[2025-07-28 01:45:12 DP1 TP0] Decode batch. #running-req: 1, #token: 557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.22, #queue-req: 0, 
[2025-07-28 01:45:12 DP0 TP0] Decode batch. #running-req: 1, #token: 569, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.27, #queue-req: 0, 
[2025-07-28 01:45:12 DP1 TP0] Decode batch. #running-req: 1, #token: 597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.38, #queue-req: 0, 
[2025-07-28 01:45:12 DP0 TP0] Decode batch. #running-req: 1, #token: 609, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.85, #queue-req: 0, 
[2025-07-28 01:45:13 DP1 TP0] Decode batch. #running-req: 1, #token: 637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.15, #queue-req: 0, 
[2025-07-28 01:45:13 DP0 TP0] Decode batch. #running-req: 1, #token: 649, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.03, #queue-req: 0, 
[2025-07-28 01:45:13 DP1 TP0] Decode batch. #running-req: 1, #token: 677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.15, #queue-req: 0, 
[2025-07-28 01:45:13 DP0 TP0] Decode batch. #running-req: 1, #token: 689, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.95, #queue-req: 0, 
[2025-07-28 01:45:13 DP1 TP0] Decode batch. #running-req: 1, #token: 717, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.96, #queue-req: 0, 
[2025-07-28 01:45:13 DP0 TP0] Decode batch. #running-req: 1, #token: 729, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.86, #queue-req: 0, 
[2025-07-28 01:45:13 DP1 TP0] Decode batch. #running-req: 1, #token: 757, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.88, #queue-req: 0, 
[2025-07-28 01:45:13 DP0 TP0] Decode batch. #running-req: 1, #token: 769, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.84, #queue-req: 0, 
[2025-07-28 01:45:13 DP1 TP0] Decode batch. #running-req: 1, #token: 797, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.19, #queue-req: 0, 
[2025-07-28 01:45:13 DP0 TP0] Decode batch. #running-req: 1, #token: 809, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.86, #queue-req: 0, 
[2025-07-28 01:45:13 DP1 TP0] Decode batch. #running-req: 1, #token: 837, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.15, #queue-req: 0, 
[2025-07-28 01:45:13 DP0 TP0] Decode batch. #running-req: 1, #token: 849, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.95, #queue-req: 0, 
[2025-07-28 01:45:14 DP1 TP0] Decode batch. #running-req: 1, #token: 877, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.92, #queue-req: 0, 
[2025-07-28 01:45:14 DP0 TP0] Decode batch. #running-req: 1, #token: 889, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.57, #queue-req: 0, 
[2025-07-28 01:45:14 DP1 TP0] Decode batch. #running-req: 1, #token: 917, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.35, #queue-req: 0, 
[2025-07-28 01:45:14 DP0 TP0] Decode batch. #running-req: 1, #token: 929, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.13, #queue-req: 0, 
[2025-07-28 01:45:14 DP1 TP0] Decode batch. #running-req: 1, #token: 957, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.58, #queue-req: 0, 
[2025-07-28 01:45:14 DP0 TP0] Decode batch. #running-req: 1, #token: 969, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.57, #queue-req: 0, 
[2025-07-28 01:45:14 DP1 TP0] Decode batch. #running-req: 1, #token: 997, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.13, #queue-req: 0, 
[2025-07-28 01:45:14] INFO:     127.0.0.1:56732 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:45:14 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 91, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:45:14 DP1 TP0] Decode batch. #running-req: 2, #token: 1208, token usage: 0.00, cuda graph: True, gen throughput (token/s): 321.18, #queue-req: 0, 
[2025-07-28 01:45:14 DP1 TP0] Decode batch. #running-req: 2, #token: 1288, token usage: 0.00, cuda graph: True, gen throughput (token/s): 470.85, #queue-req: 0, 
[2025-07-28 01:45:15] INFO:     127.0.0.1:56728 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:45:15 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 99, #cached-token: 90, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:45:15 DP1 TP0] Decode batch. #running-req: 1, #token: 337, token usage: 0.00, cuda graph: True, gen throughput (token/s): 378.17, #queue-req: 0, 
[2025-07-28 01:45:15 DP0 TP0] Decode batch. #running-req: 1, #token: 194, token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.90, #queue-req: 0, 
[2025-07-28 01:45:15 DP1 TP0] Decode batch. #running-req: 1, #token: 377, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.11, #queue-req: 0, 
[2025-07-28 01:45:15 DP0 TP0] Decode batch. #running-req: 1, #token: 234, token usage: 0.00, cuda graph: True, gen throughput (token/s): 237.16, #queue-req: 0, 
[2025-07-28 01:45:15 DP1 TP0] Decode batch. #running-req: 1, #token: 417, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.23, #queue-req: 0, 
[2025-07-28 01:45:15 DP0 TP0] Decode batch. #running-req: 1, #token: 274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.02, #queue-req: 0, 
[2025-07-28 01:45:15 DP1 TP0] Decode batch. #running-req: 1, #token: 457, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.02, #queue-req: 0, 
[2025-07-28 01:45:15 DP0 TP0] Decode batch. #running-req: 1, #token: 314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 244.19, #queue-req: 0, 
[2025-07-28 01:45:15 DP1 TP0] Decode batch. #running-req: 1, #token: 497, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.16, #queue-req: 0, 
[2025-07-28 01:45:15 DP0 TP0] Decode batch. #running-req: 1, #token: 354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.52, #queue-req: 0, 
[2025-07-28 01:45:15 DP1 TP0] Decode batch. #running-req: 1, #token: 537, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.04, #queue-req: 0, 
[2025-07-28 01:45:15 DP0 TP0] Decode batch. #running-req: 1, #token: 394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.14, #queue-req: 0, 
[2025-07-28 01:45:16 DP1 TP0] Decode batch. #running-req: 1, #token: 577, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.98, #queue-req: 0, 
[2025-07-28 01:45:16 DP0 TP0] Decode batch. #running-req: 1, #token: 434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.11, #queue-req: 0, 
[2025-07-28 01:45:16 DP1 TP0] Decode batch. #running-req: 1, #token: 617, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.84, #queue-req: 0, 
[2025-07-28 01:45:16 DP0 TP0] Decode batch. #running-req: 1, #token: 474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.55, #queue-req: 0, 
[2025-07-28 01:45:16 DP1 TP0] Decode batch. #running-req: 1, #token: 657, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.02, #queue-req: 0, 
[2025-07-28 01:45:16 DP0 TP0] Decode batch. #running-req: 1, #token: 514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.88, #queue-req: 0, 
[2025-07-28 01:45:16 DP1 TP0] Decode batch. #running-req: 1, #token: 697, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.93, #queue-req: 0, 
[2025-07-28 01:45:16 DP0 TP0] Decode batch. #running-req: 1, #token: 554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.14, #queue-req: 0, 
[2025-07-28 01:45:16 DP1 TP0] Decode batch. #running-req: 1, #token: 737, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.93, #queue-req: 0, 
[2025-07-28 01:45:16 DP0 TP0] Decode batch. #running-req: 1, #token: 594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.49, #queue-req: 0, 
[2025-07-28 01:45:16 DP1 TP0] Decode batch. #running-req: 1, #token: 777, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.87, #queue-req: 0, 
[2025-07-28 01:45:16 DP0 TP0] Decode batch. #running-req: 1, #token: 634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.95, #queue-req: 0, 
[2025-07-28 01:45:17 DP1 TP0] Decode batch. #running-req: 1, #token: 817, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.84, #queue-req: 0, 
[2025-07-28 01:45:17 DP0 TP0] Decode batch. #running-req: 1, #token: 674, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.03, #queue-req: 0, 
[2025-07-28 01:45:17 DP1 TP0] Decode batch. #running-req: 1, #token: 857, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.03, #queue-req: 0, 
[2025-07-28 01:45:17 DP0 TP0] Decode batch. #running-req: 1, #token: 714, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.69, #queue-req: 0, 
[2025-07-28 01:45:17 DP1 TP0] Decode batch. #running-req: 1, #token: 897, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.90, #queue-req: 0, 
[2025-07-28 01:45:17 DP0 TP0] Decode batch. #running-req: 1, #token: 754, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.93, #queue-req: 0, 
[2025-07-28 01:45:17 DP1 TP0] Decode batch. #running-req: 1, #token: 937, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.62, #queue-req: 0, 
[2025-07-28 01:45:17 DP0 TP0] Decode batch. #running-req: 1, #token: 794, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.81, #queue-req: 0, 
[2025-07-28 01:45:17 DP1 TP0] Decode batch. #running-req: 1, #token: 977, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.00, #queue-req: 0, 
[2025-07-28 01:45:17 DP0 TP0] Decode batch. #running-req: 1, #token: 834, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.72, #queue-req: 0, 
[2025-07-28 01:45:17 DP1 TP0] Decode batch. #running-req: 1, #token: 1017, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.74, #queue-req: 0, 
[2025-07-28 01:45:17 DP0 TP0] Decode batch. #running-req: 1, #token: 874, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.96, #queue-req: 0, 
[2025-07-28 01:45:17 DP1 TP0] Decode batch. #running-req: 1, #token: 1057, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.46, #queue-req: 0, 
[2025-07-28 01:45:18 DP0 TP0] Decode batch. #running-req: 1, #token: 914, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.84, #queue-req: 0, 
[2025-07-28 01:45:18] INFO:     127.0.0.1:56420 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:45:18 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 85, #cached-token: 90, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:45:18 DP0 TP0] Decode batch. #running-req: 1, #token: 954, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.71, #queue-req: 0, 
[2025-07-28 01:45:18 DP1 TP0] Decode batch. #running-req: 1, #token: 185, token usage: 0.00, cuda graph: True, gen throughput (token/s): 189.23, #queue-req: 0, 
[2025-07-28 01:45:18 DP0 TP0] Decode batch. #running-req: 1, #token: 994, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.18, #queue-req: 0, 
[2025-07-28 01:45:18 DP1 TP0] Decode batch. #running-req: 1, #token: 225, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.58, #queue-req: 0, 
[2025-07-28 01:45:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1034, token usage: 0.00, cuda graph: True, gen throughput (token/s): 248.66, #queue-req: 0, 
[2025-07-28 01:45:18 DP1 TP0] Decode batch. #running-req: 1, #token: 265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.20, #queue-req: 0, 
[2025-07-28 01:45:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1074, token usage: 0.00, cuda graph: True, gen throughput (token/s): 249.10, #queue-req: 0, 
[2025-07-28 01:45:18 DP1 TP0] Decode batch. #running-req: 1, #token: 305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.85, #queue-req: 0, 
[2025-07-28 01:45:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1114, token usage: 0.00, cuda graph: True, gen throughput (token/s): 249.01, #queue-req: 0, 
[2025-07-28 01:45:18 DP1 TP0] Decode batch. #running-req: 1, #token: 345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.93, #queue-req: 0, 
[2025-07-28 01:45:18] INFO:     127.0.0.1:56428 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:45:19 DP1 TP0] Decode batch. #running-req: 1, #token: 385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.96, #queue-req: 0, 
[2025-07-28 01:45:19 DP1 TP0] Decode batch. #running-req: 1, #token: 425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.73, #queue-req: 0, 
[2025-07-28 01:45:19 DP1 TP0] Decode batch. #running-req: 1, #token: 465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.09, #queue-req: 0, 
[2025-07-28 01:45:19 DP1 TP0] Decode batch. #running-req: 1, #token: 505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.93, #queue-req: 0, 
[2025-07-28 01:45:19 DP1 TP0] Decode batch. #running-req: 1, #token: 545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 247.00, #queue-req: 0, 
[2025-07-28 01:45:19 DP1 TP0] Decode batch. #running-req: 1, #token: 585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.99, #queue-req: 0, 
[2025-07-28 01:45:19 DP1 TP0] Decode batch. #running-req: 1, #token: 625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.47, #queue-req: 0, 
[2025-07-28 01:45:20 DP1 TP0] Decode batch. #running-req: 1, #token: 665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.75, #queue-req: 0, 
[2025-07-28 01:45:20 DP1 TP0] Decode batch. #running-req: 1, #token: 705, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.69, #queue-req: 0, 
[2025-07-28 01:45:20 DP1 TP0] Decode batch. #running-req: 1, #token: 745, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.66, #queue-req: 0, 
[2025-07-28 01:45:20 DP1 TP0] Decode batch. #running-req: 1, #token: 785, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.62, #queue-req: 0, 
[2025-07-28 01:45:20 DP1 TP0] Decode batch. #running-req: 1, #token: 825, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.36, #queue-req: 0, 
[2025-07-28 01:45:20] INFO:     127.0.0.1:56436 - "POST /v1/chat/completions HTTP/1.1" 200 OK

[2025-07-28 00:12:51] server_args=ServerArgs(model_path='/data/home/<USER>/repos/anesbench/share/models/gemma-3-27b-it', tokenizer_path='/data/home/<USER>/repos/anesbench/share/models/gemma-3-27b-it', tokenizer_mode='auto', skip_tokenizer_init=False, load_format='auto', model_loader_extra_config='{}', trust_remote_code=False, context_length=32768, is_embedding=False, enable_multimodal=None, revision=None, model_impl='auto', host='127.0.0.1', port=8004, skip_server_warmup=False, warmups=None, nccl_port=None, dtype='auto', quantization=None, quantization_param_path=None, kv_cache_dtype='auto', mem_fraction_static=0.85, max_running_requests=None, max_total_tokens=None, chunked_prefill_size=8192, max_prefill_tokens=16384, schedule_policy='fcfs', schedule_conservativeness=1.0, cpu_offload_gb=0, page_size=1, hybrid_kvcache_ratio=None, swa_full_tokens_ratio=0.8, disable_hybrid_swa_memory=False, device='cuda', tp_size=4, pp_size=1, max_micro_batch_size=None, stream_interval=1, stream_output=False, random_seed=1050673696, constrained_json_whitespace_pattern=None, watchdog_timeout=300, dist_timeout=None, download_dir=None, base_gpu_id=0, gpu_id_step=1, sleep_on_idle=False, log_level='info', log_level_http=None, log_requests=False, log_requests_level=0, crash_dump_folder=None, show_time_cost=True, enable_metrics=True, enable_metrics_for_all_schedulers=False, bucket_time_to_first_token=None, bucket_inter_token_latency=None, bucket_e2e_request_latency=None, collect_tokens_histogram=False, decode_log_interval=40, enable_request_time_stats_logging=False, kv_events_config=None, api_key=None, served_model_name='/data/home/<USER>/repos/anesbench/share/models/gemma-3-27b-it', chat_template=None, completion_template=None, file_storage_path='sglang_storage', enable_cache_report=False, reasoning_parser=None, tool_call_parser=None, dp_size=2, load_balance_method='round_robin', dist_init_addr=None, nnodes=1, node_rank=0, json_model_override_args='{}', preferred_sampling_params=None, enable_lora=None, max_lora_rank=None, lora_target_modules=None, lora_paths=None, max_loras_per_batch=8, lora_backend='triton', attention_backend=None, sampling_backend='flashinfer', grammar_backend='xgrammar', mm_attention_backend=None, speculative_algorithm=None, speculative_draft_model_path=None, speculative_num_steps=None, speculative_eagle_topk=None, speculative_num_draft_tokens=None, speculative_accept_threshold_single=1.0, speculative_accept_threshold_acc=1.0, speculative_token_map=None, ep_size=1, enable_ep_moe=False, enable_deepep_moe=False, enable_flashinfer_moe=False, enable_flashinfer_allreduce_fusion=False, deepep_mode='auto', ep_num_redundant_experts=0, ep_dispatch_algorithm='static', init_expert_location='trivial', enable_eplb=False, eplb_algorithm='auto', eplb_rebalance_num_iterations=1000, eplb_rebalance_layers_per_chunk=None, expert_distribution_recorder_mode=None, expert_distribution_recorder_buffer_size=1000, enable_expert_distribution_metrics=False, deepep_config=None, moe_dense_tp_size=None, enable_hierarchical_cache=False, hicache_ratio=2.0, hicache_size=0, hicache_write_policy='write_through_selective', hicache_io_backend='', hicache_storage_backend=None, enable_double_sparsity=False, ds_channel_config_path=None, ds_heavy_channel_num=32, ds_heavy_token_num=256, ds_heavy_channel_type='qk', ds_sparse_decode_threshold=4096, disable_radix_cache=False, cuda_graph_max_bs=None, cuda_graph_bs=None, disable_cuda_graph=False, disable_cuda_graph_padding=False, enable_profile_cuda_graph=False, enable_nccl_nvls=False, enable_tokenizer_batch_encode=False, disable_outlines_disk_cache=False, disable_custom_all_reduce=False, enable_mscclpp=False, disable_overlap_schedule=False, enable_mixed_chunk=False, enable_dp_attention=False, enable_dp_lm_head=False, enable_two_batch_overlap=False, enable_torch_compile=False, torch_compile_max_bs=32, torchao_config='', enable_nan_detection=False, enable_p2p_check=False, triton_attention_reduce_in_fp32=False, triton_attention_num_kv_splits=8, num_continuous_decode_steps=1, delete_ckpt_after_loading=False, enable_memory_saver=False, allow_auto_truncate=False, enable_custom_logit_processor=False, flashinfer_mla_disable_ragged=False, disable_shared_experts_fusion=False, disable_chunked_prefix_cache=False, disable_fast_image_processor=False, enable_return_hidden_states=False, enable_triton_kernel_moe=False, debug_tensor_dump_output_folder=None, debug_tensor_dump_input_file=None, debug_tensor_dump_inject=False, debug_tensor_dump_prefill_only=False, disaggregation_mode='null', disaggregation_transfer_backend='mooncake', disaggregation_bootstrap_port=8998, disaggregation_decode_tp=None, disaggregation_decode_dp=None, disaggregation_prefill_pp=1, disaggregation_ib_device=None, num_reserved_decode_tokens=512, pdlb_url=None, custom_weight_loader=[], weight_loader_disable_mmap=False, enable_pdmux=False, sm_group_num=3)
[2025-07-28 00:12:51] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:12:51] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:12:51] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:12:52] Inferred chat template from model path: gemma-it
[2025-07-28 00:12:56] Launch DP0 starting at GPU #0.
[2025-07-28 00:12:56] Launch DP1 starting at GPU #4.
[2025-07-28 00:13:04 DP1 TP1] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:13:04 DP1 TP1] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:13:04 DP1 TP1] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:13:04 DP0 TP3] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:13:04 DP0 TP3] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:13:04 DP0 TP3] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:13:04 DP0 TP0] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:13:04 DP0 TP0] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:13:04 DP0 TP0] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:13:04 DP1 TP3] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:13:04 DP1 TP3] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:13:04 DP1 TP3] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:13:04 DP0 TP2] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:13:04 DP0 TP2] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:13:04 DP0 TP2] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:13:04 DP0 TP1] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:13:04 DP0 TP1] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:13:04 DP0 TP1] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:13:04 DP1 TP2] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:13:04 DP1 TP2] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:13:04 DP1 TP2] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:13:04 DP1 TP0] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:13:04 DP1 TP0] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:13:04 DP1 TP0] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:13:05 DP1 TP1] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:13:05 DP1 TP1] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:13:05 DP1 TP1] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:13:05 DP0 TP3] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:13:05 DP0 TP3] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:13:05 DP0 TP3] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:13:05 DP0 TP0] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:13:05 DP0 TP0] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:13:05 DP0 TP0] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:13:05 DP0 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 00:13:05 DP0 TP0] Init torch distributed begin.
[2025-07-28 00:13:05 DP1 TP3] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:13:05 DP1 TP3] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:13:05 DP1 TP3] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:13:05 DP0 TP2] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:13:05 DP0 TP2] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:13:05 DP0 TP2] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:13:05 DP0 TP1] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:13:05 DP0 TP1] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:13:05 DP0 TP1] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:13:05 DP1 TP2] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:13:05 DP1 TP2] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:13:05 DP1 TP2] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:13:05 DP1 TP0] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:13:05 DP1 TP0] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:13:05 DP1 TP0] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:13:05 DP1 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 00:13:05 DP1 TP0] Init torch distributed begin.
[2025-07-28 00:13:06 DP0 TP0] sglang is using nccl==2.26.2
[2025-07-28 00:13:07 DP1 TP0] sglang is using nccl==2.26.2
[2025-07-28 00:13:08 DP0 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 00:13:08 DP1 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 00:13:08 DP0 TP0] Load weight begin. avail mem=78.21 GB
[2025-07-28 00:13:08 DP0 TP3] Multimodal attention backend not set. Use sdpa.
[2025-07-28 00:13:08 DP0 TP3] Using sdpa as multimodal attention backend.
[2025-07-28 00:13:08 DP0 TP0] Multimodal attention backend not set. Use sdpa.
[2025-07-28 00:13:08 DP0 TP0] Using sdpa as multimodal attention backend.
[2025-07-28 00:13:08 DP0 TP1] Multimodal attention backend not set. Use sdpa.
[2025-07-28 00:13:08 DP0 TP1] Using sdpa as multimodal attention backend.
[2025-07-28 00:13:08 DP1 TP0] Load weight begin. avail mem=78.21 GB
[2025-07-28 00:13:08 DP1 TP3] Multimodal attention backend not set. Use sdpa.
[2025-07-28 00:13:08 DP1 TP3] Using sdpa as multimodal attention backend.
[2025-07-28 00:13:08 DP1 TP0] Multimodal attention backend not set. Use sdpa.
[2025-07-28 00:13:08 DP1 TP0] Using sdpa as multimodal attention backend.
[2025-07-28 00:13:08 DP1 TP1] Multimodal attention backend not set. Use sdpa.
[2025-07-28 00:13:08 DP1 TP1] Using sdpa as multimodal attention backend.
[2025-07-28 00:13:08 DP0 TP2] Multimodal attention backend not set. Use sdpa.
[2025-07-28 00:13:08 DP0 TP2] Using sdpa as multimodal attention backend.
[2025-07-28 00:13:08 DP1 TP2] Multimodal attention backend not set. Use sdpa.
[2025-07-28 00:13:08 DP1 TP2] Using sdpa as multimodal attention backend.

Loading safetensors checkpoint shards:   0% Completed | 0/12 [00:00<?, ?it/s]

Loading safetensors checkpoint shards:   0% Completed | 0/12 [00:00<?, ?it/s]

Loading safetensors checkpoint shards:   8% Completed | 1/12 [00:00<00:08,  1.32it/s]

Loading safetensors checkpoint shards:   8% Completed | 1/12 [00:00<00:08,  1.24it/s]

Loading safetensors checkpoint shards:  17% Completed | 2/12 [00:00<00:03,  2.62it/s]

Loading safetensors checkpoint shards:  17% Completed | 2/12 [00:00<00:04,  2.50it/s]

Loading safetensors checkpoint shards:  25% Completed | 3/12 [00:01<00:04,  2.18it/s]

Loading safetensors checkpoint shards:  25% Completed | 3/12 [00:01<00:04,  2.15it/s]

Loading safetensors checkpoint shards:  33% Completed | 4/12 [00:02<00:04,  1.97it/s]

Loading safetensors checkpoint shards:  33% Completed | 4/12 [00:02<00:04,  1.97it/s]

Loading safetensors checkpoint shards:  42% Completed | 5/12 [00:02<00:03,  1.86it/s]

Loading safetensors checkpoint shards:  42% Completed | 5/12 [00:02<00:03,  1.91it/s]

Loading safetensors checkpoint shards:  50% Completed | 6/12 [00:03<00:03,  1.83it/s]

Loading safetensors checkpoint shards:  50% Completed | 6/12 [00:03<00:03,  1.88it/s]

Loading safetensors checkpoint shards:  58% Completed | 7/12 [00:03<00:02,  1.78it/s]

Loading safetensors checkpoint shards:  58% Completed | 7/12 [00:03<00:02,  1.82it/s]

Loading safetensors checkpoint shards:  67% Completed | 8/12 [00:04<00:02,  1.84it/s]

Loading safetensors checkpoint shards:  67% Completed | 8/12 [00:04<00:02,  1.76it/s]

Loading safetensors checkpoint shards:  75% Completed | 9/12 [00:04<00:01,  1.82it/s]

Loading safetensors checkpoint shards:  75% Completed | 9/12 [00:04<00:01,  1.74it/s]

Loading safetensors checkpoint shards:  83% Completed | 10/12 [00:05<00:01,  1.79it/s]

Loading safetensors checkpoint shards:  83% Completed | 10/12 [00:05<00:01,  1.74it/s]

Loading safetensors checkpoint shards:  92% Completed | 11/12 [00:05<00:00,  1.81it/s]

Loading safetensors checkpoint shards:  92% Completed | 11/12 [00:06<00:00,  1.73it/s]

Loading safetensors checkpoint shards: 100% Completed | 12/12 [00:06<00:00,  1.73it/s]

Loading safetensors checkpoint shards: 100% Completed | 12/12 [00:06<00:00,  1.83it/s]

[2025-07-28 00:13:15 DP1 TP0] Load weight end. type=Gemma3ForConditionalGeneration, dtype=torch.bfloat16, avail mem=63.14 GB, mem usage=15.06 GB.

Loading safetensors checkpoint shards: 100% Completed | 12/12 [00:06<00:00,  1.70it/s]

Loading safetensors checkpoint shards: 100% Completed | 12/12 [00:06<00:00,  1.79it/s]

[2025-07-28 00:13:15 DP0 TP0] Load weight end. type=Gemma3ForConditionalGeneration, dtype=torch.bfloat16, avail mem=63.14 GB, mem usage=15.06 GB.
[2025-07-28 00:13:15 DP0 TP0] Use Sliding window memory pool. full_layer_tokens=521550, swa_layer_tokens=417240
[2025-07-28 00:13:15 DP0 TP3] KV Cache is allocated. #tokens: 417240, K size: 20.69 GB, V size: 20.69 GB
[2025-07-28 00:13:15 DP0 TP0] KV Cache is allocated. #tokens: 417240, K size: 20.69 GB, V size: 20.69 GB
[2025-07-28 00:13:15 DP0 TP2] KV Cache is allocated. #tokens: 417240, K size: 20.69 GB, V size: 20.69 GB
[2025-07-28 00:13:15 DP0 TP1] KV Cache is allocated. #tokens: 417240, K size: 20.69 GB, V size: 20.69 GB
[2025-07-28 00:13:15 DP0 TP3] KV Cache is allocated. #tokens: 521550, K size: 4.97 GB, V size: 4.97 GB
[2025-07-28 00:13:15 DP0 TP0] KV Cache is allocated. #tokens: 521550, K size: 4.97 GB, V size: 4.97 GB
[2025-07-28 00:13:15 DP0 TP0] Memory pool end. avail mem=11.22 GB
[2025-07-28 00:13:15 DP0 TP2] KV Cache is allocated. #tokens: 521550, K size: 4.97 GB, V size: 4.97 GB
[2025-07-28 00:13:15 DP0 TP1] KV Cache is allocated. #tokens: 521550, K size: 4.97 GB, V size: 4.97 GB
[2025-07-28 00:13:15 DP1 TP0] Use Sliding window memory pool. full_layer_tokens=521550, swa_layer_tokens=417240
[2025-07-28 00:13:15 DP0 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.63 GB
[2025-07-28 00:13:15 DP1 TP3] KV Cache is allocated. #tokens: 417240, K size: 20.69 GB, V size: 20.69 GB
[2025-07-28 00:13:15 DP1 TP0] KV Cache is allocated. #tokens: 417240, K size: 20.69 GB, V size: 20.69 GB
[2025-07-28 00:13:15 DP1 TP2] KV Cache is allocated. #tokens: 417240, K size: 20.69 GB, V size: 20.69 GB
[2025-07-28 00:13:15 DP1 TP1] KV Cache is allocated. #tokens: 417240, K size: 20.69 GB, V size: 20.69 GB
[2025-07-28 00:13:15 DP1 TP3] KV Cache is allocated. #tokens: 521550, K size: 4.97 GB, V size: 4.97 GB
[2025-07-28 00:13:16 DP1 TP0] KV Cache is allocated. #tokens: 521550, K size: 4.97 GB, V size: 4.97 GB
[2025-07-28 00:13:16 DP1 TP0] Memory pool end. avail mem=11.22 GB
[2025-07-28 00:13:16 DP1 TP2] KV Cache is allocated. #tokens: 521550, K size: 4.97 GB, V size: 4.97 GB
[2025-07-28 00:13:16 DP1 TP1] KV Cache is allocated. #tokens: 521550, K size: 4.97 GB, V size: 4.97 GB
[2025-07-28 00:13:16 DP0 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.59 GB):   0%|          | 0/23 [00:00<?, ?it/s][2025-07-28 00:13:16 DP1 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.63 GB
[2025-07-28 00:13:16 DP1 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.59 GB):   0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.59 GB):   4%|▍         | 1/23 [00:01<00:28,  1.31s/it]
Capturing batches (bs=152 avail_mem=9.75 GB):   4%|▍         | 1/23 [00:01<00:28,  1.31s/it] 
Capturing batches (bs=160 avail_mem=10.59 GB):   4%|▍         | 1/23 [00:01<00:28,  1.30s/it]
Capturing batches (bs=152 avail_mem=9.75 GB):   4%|▍         | 1/23 [00:01<00:28,  1.30s/it] 
Capturing batches (bs=152 avail_mem=9.75 GB):   9%|▊         | 2/23 [00:01<00:18,  1.11it/s]
Capturing batches (bs=144 avail_mem=9.38 GB):   9%|▊         | 2/23 [00:01<00:18,  1.11it/s]
Capturing batches (bs=152 avail_mem=9.75 GB):   9%|▊         | 2/23 [00:01<00:18,  1.15it/s]
Capturing batches (bs=144 avail_mem=9.38 GB):   9%|▊         | 2/23 [00:01<00:18,  1.15it/s]
Capturing batches (bs=144 avail_mem=9.38 GB):  13%|█▎        | 3/23 [00:02<00:14,  1.35it/s]
Capturing batches (bs=136 avail_mem=9.30 GB):  13%|█▎        | 3/23 [00:02<00:14,  1.35it/s]
Capturing batches (bs=144 avail_mem=9.38 GB):  13%|█▎        | 3/23 [00:02<00:14,  1.38it/s]
Capturing batches (bs=136 avail_mem=9.30 GB):  13%|█▎        | 3/23 [00:02<00:14,  1.38it/s]
Capturing batches (bs=136 avail_mem=9.30 GB):  17%|█▋        | 4/23 [00:03<00:12,  1.50it/s]
Capturing batches (bs=128 avail_mem=8.96 GB):  17%|█▋        | 4/23 [00:03<00:12,  1.50it/s]
Capturing batches (bs=136 avail_mem=9.30 GB):  17%|█▋        | 4/23 [00:02<00:12,  1.52it/s]
Capturing batches (bs=128 avail_mem=8.96 GB):  17%|█▋        | 4/23 [00:02<00:12,  1.52it/s]
Capturing batches (bs=128 avail_mem=8.96 GB):  22%|██▏       | 5/23 [00:03<00:11,  1.60it/s]
Capturing batches (bs=120 avail_mem=8.88 GB):  22%|██▏       | 5/23 [00:03<00:11,  1.60it/s]
Capturing batches (bs=128 avail_mem=8.96 GB):  22%|██▏       | 5/23 [00:03<00:11,  1.61it/s]
Capturing batches (bs=120 avail_mem=8.88 GB):  22%|██▏       | 5/23 [00:03<00:11,  1.61it/s]
Capturing batches (bs=120 avail_mem=8.88 GB):  26%|██▌       | 6/23 [00:04<00:10,  1.67it/s]
Capturing batches (bs=112 avail_mem=8.57 GB):  26%|██▌       | 6/23 [00:04<00:10,  1.67it/s]
Capturing batches (bs=120 avail_mem=8.88 GB):  26%|██▌       | 6/23 [00:04<00:10,  1.66it/s]
Capturing batches (bs=112 avail_mem=8.57 GB):  26%|██▌       | 6/23 [00:04<00:10,  1.66it/s]
Capturing batches (bs=112 avail_mem=8.57 GB):  30%|███       | 7/23 [00:04<00:09,  1.72it/s]
Capturing batches (bs=104 avail_mem=8.49 GB):  30%|███       | 7/23 [00:04<00:09,  1.72it/s]
Capturing batches (bs=112 avail_mem=8.57 GB):  30%|███       | 7/23 [00:04<00:09,  1.69it/s]
Capturing batches (bs=104 avail_mem=8.49 GB):  30%|███       | 7/23 [00:04<00:09,  1.69it/s]
Capturing batches (bs=104 avail_mem=8.49 GB):  35%|███▍      | 8/23 [00:05<00:08,  1.74it/s]
Capturing batches (bs=96 avail_mem=8.21 GB):  35%|███▍      | 8/23 [00:05<00:08,  1.74it/s] 
Capturing batches (bs=104 avail_mem=8.49 GB):  35%|███▍      | 8/23 [00:05<00:08,  1.75it/s]
Capturing batches (bs=96 avail_mem=8.21 GB):  35%|███▍      | 8/23 [00:05<00:08,  1.75it/s] 
Capturing batches (bs=96 avail_mem=8.21 GB):  39%|███▉      | 9/23 [00:05<00:08,  1.75it/s]
Capturing batches (bs=88 avail_mem=8.13 GB):  39%|███▉      | 9/23 [00:05<00:08,  1.75it/s]
Capturing batches (bs=96 avail_mem=8.21 GB):  39%|███▉      | 9/23 [00:05<00:07,  1.79it/s]
Capturing batches (bs=88 avail_mem=8.13 GB):  39%|███▉      | 9/23 [00:05<00:07,  1.79it/s]
Capturing batches (bs=88 avail_mem=8.13 GB):  43%|████▎     | 10/23 [00:06<00:07,  1.79it/s]
Capturing batches (bs=80 avail_mem=8.06 GB):  43%|████▎     | 10/23 [00:06<00:07,  1.79it/s]
Capturing batches (bs=88 avail_mem=8.13 GB):  43%|████▎     | 10/23 [00:06<00:07,  1.77it/s]
Capturing batches (bs=80 avail_mem=8.06 GB):  43%|████▎     | 10/23 [00:06<00:07,  1.77it/s]
Capturing batches (bs=80 avail_mem=8.06 GB):  48%|████▊     | 11/23 [00:06<00:06,  1.81it/s]
Capturing batches (bs=72 avail_mem=7.82 GB):  48%|████▊     | 11/23 [00:06<00:06,  1.81it/s]
Capturing batches (bs=80 avail_mem=8.06 GB):  48%|████▊     | 11/23 [00:06<00:06,  1.81it/s]
Capturing batches (bs=72 avail_mem=7.82 GB):  48%|████▊     | 11/23 [00:06<00:06,  1.81it/s]
Capturing batches (bs=72 avail_mem=7.82 GB):  52%|█████▏    | 12/23 [00:07<00:05,  1.84it/s]
Capturing batches (bs=64 avail_mem=7.75 GB):  52%|█████▏    | 12/23 [00:07<00:05,  1.84it/s]
Capturing batches (bs=72 avail_mem=7.82 GB):  52%|█████▏    | 12/23 [00:07<00:06,  1.76it/s]
Capturing batches (bs=64 avail_mem=7.75 GB):  52%|█████▏    | 12/23 [00:07<00:06,  1.76it/s]
Capturing batches (bs=64 avail_mem=7.75 GB):  57%|█████▋    | 13/23 [00:07<00:05,  1.86it/s]
Capturing batches (bs=56 avail_mem=7.68 GB):  57%|█████▋    | 13/23 [00:07<00:05,  1.86it/s]
Capturing batches (bs=64 avail_mem=7.75 GB):  57%|█████▋    | 13/23 [00:07<00:05,  1.81it/s]
Capturing batches (bs=56 avail_mem=7.68 GB):  57%|█████▋    | 13/23 [00:07<00:05,  1.81it/s]
Capturing batches (bs=56 avail_mem=7.68 GB):  61%|██████    | 14/23 [00:08<00:04,  1.80it/s]
Capturing batches (bs=48 avail_mem=7.61 GB):  61%|██████    | 14/23 [00:08<00:04,  1.80it/s]
Capturing batches (bs=56 avail_mem=7.68 GB):  61%|██████    | 14/23 [00:08<00:04,  1.82it/s]
Capturing batches (bs=48 avail_mem=7.61 GB):  61%|██████    | 14/23 [00:08<00:04,  1.82it/s]
Capturing batches (bs=48 avail_mem=7.61 GB):  65%|██████▌   | 15/23 [00:09<00:04,  1.84it/s]
Capturing batches (bs=40 avail_mem=7.44 GB):  65%|██████▌   | 15/23 [00:09<00:04,  1.84it/s]
Capturing batches (bs=48 avail_mem=7.61 GB):  65%|██████▌   | 15/23 [00:09<00:04,  1.86it/s]
Capturing batches (bs=40 avail_mem=7.44 GB):  65%|██████▌   | 15/23 [00:09<00:04,  1.86it/s]
Capturing batches (bs=40 avail_mem=7.44 GB):  70%|██████▉   | 16/23 [00:09<00:03,  1.89it/s]
Capturing batches (bs=32 avail_mem=7.37 GB):  70%|██████▉   | 16/23 [00:09<00:03,  1.89it/s]
Capturing batches (bs=40 avail_mem=7.44 GB):  70%|██████▉   | 16/23 [00:09<00:03,  1.87it/s]
Capturing batches (bs=32 avail_mem=7.37 GB):  70%|██████▉   | 16/23 [00:09<00:03,  1.87it/s]
Capturing batches (bs=32 avail_mem=7.37 GB):  74%|███████▍  | 17/23 [00:10<00:03,  1.90it/s]
Capturing batches (bs=24 avail_mem=7.30 GB):  74%|███████▍  | 17/23 [00:10<00:03,  1.90it/s]
Capturing batches (bs=32 avail_mem=7.37 GB):  74%|███████▍  | 17/23 [00:10<00:03,  1.88it/s]
Capturing batches (bs=24 avail_mem=7.30 GB):  74%|███████▍  | 17/23 [00:10<00:03,  1.88it/s]
Capturing batches (bs=24 avail_mem=7.30 GB):  78%|███████▊  | 18/23 [00:10<00:02,  1.93it/s]
Capturing batches (bs=16 avail_mem=7.23 GB):  78%|███████▊  | 18/23 [00:10<00:02,  1.93it/s]
Capturing batches (bs=24 avail_mem=7.30 GB):  78%|███████▊  | 18/23 [00:10<00:02,  1.76it/s]
Capturing batches (bs=16 avail_mem=7.23 GB):  78%|███████▊  | 18/23 [00:10<00:02,  1.76it/s]
Capturing batches (bs=16 avail_mem=7.23 GB):  83%|████████▎ | 19/23 [00:11<00:02,  1.92it/s]
Capturing batches (bs=8 avail_mem=7.16 GB):  83%|████████▎ | 19/23 [00:11<00:02,  1.92it/s] 
Capturing batches (bs=16 avail_mem=7.23 GB):  83%|████████▎ | 19/23 [00:11<00:02,  1.75it/s]
Capturing batches (bs=8 avail_mem=7.16 GB):  83%|████████▎ | 19/23 [00:11<00:02,  1.75it/s] 
Capturing batches (bs=8 avail_mem=7.16 GB):  87%|████████▋ | 20/23 [00:11<00:01,  1.89it/s]
Capturing batches (bs=4 avail_mem=7.09 GB):  87%|████████▋ | 20/23 [00:11<00:01,  1.89it/s]
Capturing batches (bs=8 avail_mem=7.16 GB):  87%|████████▋ | 20/23 [00:11<00:01,  1.78it/s]
Capturing batches (bs=4 avail_mem=7.09 GB):  87%|████████▋ | 20/23 [00:11<00:01,  1.78it/s]
Capturing batches (bs=4 avail_mem=7.09 GB):  91%|█████████▏| 21/23 [00:12<00:01,  1.89it/s]
Capturing batches (bs=2 avail_mem=7.02 GB):  91%|█████████▏| 21/23 [00:12<00:01,  1.89it/s]
Capturing batches (bs=4 avail_mem=7.09 GB):  91%|█████████▏| 21/23 [00:12<00:01,  1.82it/s]
Capturing batches (bs=2 avail_mem=7.02 GB):  91%|█████████▏| 21/23 [00:12<00:01,  1.82it/s]
Capturing batches (bs=2 avail_mem=7.02 GB):  96%|█████████▌| 22/23 [00:12<00:00,  1.89it/s]
Capturing batches (bs=1 avail_mem=6.95 GB):  96%|█████████▌| 22/23 [00:12<00:00,  1.89it/s][2025-07-28 00:13:29 DP0 TP3] Registering 2852 cuda graph addresses

Capturing batches (bs=1 avail_mem=6.95 GB): 100%|██████████| 23/23 [00:13<00:00,  1.87it/s]
Capturing batches (bs=1 avail_mem=6.95 GB): 100%|██████████| 23/23 [00:13<00:00,  1.74it/s]
[2025-07-28 00:13:29 DP0 TP2] Registering 2852 cuda graph addresses
[2025-07-28 00:13:29 DP0 TP0] Registering 2852 cuda graph addresses
[2025-07-28 00:13:29 DP0 TP1] Registering 2852 cuda graph addresses
[2025-07-28 00:13:29 DP0 TP0] Capture cuda graph end. Time elapsed: 13.45 s. mem usage=3.76 GB. avail mem=6.87 GB.

Capturing batches (bs=2 avail_mem=7.02 GB):  96%|█████████▌| 22/23 [00:13<00:00,  1.72it/s]
Capturing batches (bs=1 avail_mem=6.95 GB):  96%|█████████▌| 22/23 [00:13<00:00,  1.72it/s][2025-07-28 00:13:29 DP1 TP2] Registering 2852 cuda graph addresses
[2025-07-28 00:13:29 DP1 TP1] Registering 2852 cuda graph addresses
[2025-07-28 00:13:29 DP1 TP3] Registering 2852 cuda graph addresses

Capturing batches (bs=1 avail_mem=6.95 GB): 100%|██████████| 23/23 [00:13<00:00,  1.72it/s]
Capturing batches (bs=1 avail_mem=6.95 GB): 100%|██████████| 23/23 [00:13<00:00,  1.69it/s]
[2025-07-28 00:13:29 DP1 TP0] Registering 2852 cuda graph addresses
[2025-07-28 00:13:30 DP1 TP0] Capture cuda graph end. Time elapsed: 13.76 s. mem usage=3.76 GB. avail mem=6.87 GB.
[2025-07-28 00:13:30 DP0 TP0] max_total_num_tokens=521550, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=6.87 GB
[2025-07-28 00:13:31 DP1 TP0] max_total_num_tokens=521550, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=6.87 GB
[2025-07-28 00:13:32] INFO:     Started server process [1725718]
[2025-07-28 00:13:32] INFO:     Waiting for application startup.
[2025-07-28 00:13:32] INFO:     Application startup complete.
[2025-07-28 00:13:32] INFO:     Uvicorn running on http://127.0.0.1:8004 (Press CTRL+C to quit)
[2025-07-28 00:13:33] INFO:     127.0.0.1:47566 - "GET /health HTTP/1.1" 200 OK
[2025-07-28 00:13:33 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 251, #cached-token: 0, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:13:33 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 213, #cached-token: 0, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:13:33] INFO:     127.0.0.1:47594 - "GET /get_model_info HTTP/1.1" 200 OK
[2025-07-28 00:13:34 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 1, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:13:34 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 1, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:13:35] INFO:     127.0.0.1:47608 - "POST /generate HTTP/1.1" 200 OK
[2025-07-28 00:13:35] The server is fired up and ready to roll!
[2025-07-28 00:13:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 292, full token usage: 0.00, #swa token: 292, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 10.00, #queue-req: 0, 
[2025-07-28 00:13:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 254, full token usage: 0.00, #swa token: 254, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 8.64, #queue-req: 0, 
[2025-07-28 00:13:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 332, full token usage: 0.00, #swa token: 332, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.81, #queue-req: 0, 
[2025-07-28 00:13:37 DP0 TP0] Decode batch. #running-req: 1, #full token: 294, full token usage: 0.00, #swa token: 294, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.64, #queue-req: 0, 
[2025-07-28 00:13:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 372, full token usage: 0.00, #swa token: 372, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.48, #queue-req: 0, 
[2025-07-28 00:13:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 334, full token usage: 0.00, #swa token: 334, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.65, #queue-req: 0, 
[2025-07-28 00:13:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 412, full token usage: 0.00, #swa token: 412, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.88, #queue-req: 0, 
[2025-07-28 00:13:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 374, full token usage: 0.00, #swa token: 374, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.75, #queue-req: 0, 
[2025-07-28 00:13:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 452, full token usage: 0.00, #swa token: 452, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:13:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 414, full token usage: 0.00, #swa token: 414, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.61, #queue-req: 0, 
[2025-07-28 00:13:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 492, full token usage: 0.00, #swa token: 492, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.79, #queue-req: 0, 
[2025-07-28 00:13:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 454, full token usage: 0.00, #swa token: 454, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.71, #queue-req: 0, 
[2025-07-28 00:13:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 532, full token usage: 0.00, #swa token: 532, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.83, #queue-req: 0, 
[2025-07-28 00:13:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 494, full token usage: 0.00, #swa token: 494, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.72, #queue-req: 0, 
[2025-07-28 00:13:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 572, full token usage: 0.00, #swa token: 572, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.76, #queue-req: 0, 
[2025-07-28 00:13:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 534, full token usage: 0.00, #swa token: 534, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.67, #queue-req: 0, 
[2025-07-28 00:13:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 612, full token usage: 0.00, #swa token: 612, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.74, #queue-req: 0, 
[2025-07-28 00:13:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 574, full token usage: 0.00, #swa token: 574, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.67, #queue-req: 0, 
[2025-07-28 00:13:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 652, full token usage: 0.00, #swa token: 652, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.73, #queue-req: 0, 
[2025-07-28 00:13:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 614, full token usage: 0.00, #swa token: 614, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.60, #queue-req: 0, 
[2025-07-28 00:13:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 692, full token usage: 0.00, #swa token: 692, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.67, #queue-req: 0, 
[2025-07-28 00:13:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 654, full token usage: 0.00, #swa token: 654, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.52, #queue-req: 0, 
[2025-07-28 00:13:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 732, full token usage: 0.00, #swa token: 732, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.61, #queue-req: 0, 
[2025-07-28 00:13:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 694, full token usage: 0.00, #swa token: 694, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.50, #queue-req: 0, 
[2025-07-28 00:13:47] INFO:     127.0.0.1:47574 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:13:47 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 93, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:13:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 772, full token usage: 0.00, #swa token: 772, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.55, #queue-req: 0, 
[2025-07-28 00:13:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 205, full token usage: 0.00, #swa token: 205, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.68, #queue-req: 0, 
[2025-07-28 00:13:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 812, full token usage: 0.00, #swa token: 812, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.50, #queue-req: 0, 
[2025-07-28 00:13:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 245, full token usage: 0.00, #swa token: 245, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:13:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 852, full token usage: 0.00, #swa token: 852, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.53, #queue-req: 0, 
[2025-07-28 00:13:50 DP0 TP0] Decode batch. #running-req: 1, #full token: 285, full token usage: 0.00, #swa token: 285, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.71, #queue-req: 0, 
[2025-07-28 00:13:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 892, full token usage: 0.00, #swa token: 892, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.52, #queue-req: 0, 
[2025-07-28 00:13:51] INFO:     127.0.0.1:47580 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:13:51 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 212, #cached-token: 98, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:13:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 325, full token usage: 0.00, #swa token: 325, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.79, #queue-req: 0, 
[2025-07-28 00:13:51 DP1 TP0] Decode batch. #running-req: 1, #full token: 341, full token usage: 0.00, #swa token: 341, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.06, #queue-req: 0, 
[2025-07-28 00:13:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 365, full token usage: 0.00, #swa token: 365, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.73, #queue-req: 0, 
[2025-07-28 00:13:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 381, full token usage: 0.00, #swa token: 381, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.90, #queue-req: 0, 
[2025-07-28 00:13:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 405, full token usage: 0.00, #swa token: 405, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.75, #queue-req: 0, 
[2025-07-28 00:13:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 421, full token usage: 0.00, #swa token: 421, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.90, #queue-req: 0, 
[2025-07-28 00:13:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 445, full token usage: 0.00, #swa token: 445, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.78, #queue-req: 0, 
[2025-07-28 00:13:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 461, full token usage: 0.00, #swa token: 461, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.89, #queue-req: 0, 
[2025-07-28 00:13:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 485, full token usage: 0.00, #swa token: 485, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.27, #queue-req: 0, 
[2025-07-28 00:13:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 501, full token usage: 0.00, #swa token: 501, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.90, #queue-req: 0, 
[2025-07-28 00:13:56 DP0 TP0] Decode batch. #running-req: 1, #full token: 525, full token usage: 0.00, #swa token: 525, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.74, #queue-req: 0, 
[2025-07-28 00:13:56 DP1 TP0] Decode batch. #running-req: 1, #full token: 541, full token usage: 0.00, #swa token: 541, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.84, #queue-req: 0, 
[2025-07-28 00:13:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 565, full token usage: 0.00, #swa token: 565, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.70, #queue-req: 0, 
[2025-07-28 00:13:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 581, full token usage: 0.00, #swa token: 581, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.79, #queue-req: 0, 
[2025-07-28 00:13:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 605, full token usage: 0.00, #swa token: 605, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.63, #queue-req: 0, 
[2025-07-28 00:13:58] INFO:     127.0.0.1:34856 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:13:58 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 134, #cached-token: 98, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:13:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 621, full token usage: 0.00, #swa token: 621, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.75, #queue-req: 0, 
[2025-07-28 00:13:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 258, full token usage: 0.00, #swa token: 258, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.34, #queue-req: 0, 
[2025-07-28 00:13:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 661, full token usage: 0.00, #swa token: 661, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.73, #queue-req: 0, 
[2025-07-28 00:14:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 298, full token usage: 0.00, #swa token: 298, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.80, #queue-req: 0, 
[2025-07-28 00:14:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 701, full token usage: 0.00, #swa token: 701, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.68, #queue-req: 0, 
[2025-07-28 00:14:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 338, full token usage: 0.00, #swa token: 338, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.78, #queue-req: 0, 
[2025-07-28 00:14:01 DP1 TP0] Decode batch. #running-req: 1, #full token: 741, full token usage: 0.00, #swa token: 741, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.64, #queue-req: 0, 
[2025-07-28 00:14:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 378, full token usage: 0.00, #swa token: 378, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.77, #queue-req: 0, 
[2025-07-28 00:14:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 781, full token usage: 0.00, #swa token: 781, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.56, #queue-req: 0, 
[2025-07-28 00:14:03 DP0 TP0] Decode batch. #running-req: 1, #full token: 418, full token usage: 0.00, #swa token: 418, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.67, #queue-req: 0, 
[2025-07-28 00:14:03 DP1 TP0] Decode batch. #running-req: 1, #full token: 821, full token usage: 0.00, #swa token: 821, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.54, #queue-req: 0, 
[2025-07-28 00:14:04 DP0 TP0] Decode batch. #running-req: 1, #full token: 458, full token usage: 0.00, #swa token: 458, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.77, #queue-req: 0, 
[2025-07-28 00:14:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 861, full token usage: 0.00, #swa token: 861, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.55, #queue-req: 0, 
[2025-07-28 00:14:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 498, full token usage: 0.00, #swa token: 498, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.78, #queue-req: 0, 
[2025-07-28 00:14:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 901, full token usage: 0.00, #swa token: 901, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.56, #queue-req: 0, 
[2025-07-28 00:14:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 538, full token usage: 0.00, #swa token: 538, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.74, #queue-req: 0, 
[2025-07-28 00:14:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 941, full token usage: 0.00, #swa token: 941, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.57, #queue-req: 0, 
[2025-07-28 00:14:06 DP0 TP0] Decode batch. #running-req: 1, #full token: 578, full token usage: 0.00, #swa token: 578, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.69, #queue-req: 0, 
[2025-07-28 00:14:07 DP1 TP0] Decode batch. #running-req: 1, #full token: 981, full token usage: 0.00, #swa token: 981, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.54, #queue-req: 0, 
[2025-07-28 00:14:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 618, full token usage: 0.00, #swa token: 618, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.64, #queue-req: 0, 
[2025-07-28 00:14:08] INFO:     127.0.0.1:57622 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:14:08 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 136, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:14:08 DP1 TP0] Decode batch. #running-req: 2, #full token: 1180, full token usage: 0.00, #swa token: 1180, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 54.93, #queue-req: 0, 
[2025-07-28 00:14:09 DP1 TP0] Decode batch. #running-req: 2, #full token: 1260, full token usage: 0.00, #swa token: 1260, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 77.05, #queue-req: 0, 
[2025-07-28 00:14:10] INFO:     127.0.0.1:34870 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:14:10 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 193, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:14:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 336, full token usage: 0.00, #swa token: 336, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 75.37, #queue-req: 0, 
[2025-07-28 00:14:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 324, full token usage: 0.00, #swa token: 324, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 10.27, #queue-req: 0, 
[2025-07-28 00:14:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 376, full token usage: 0.00, #swa token: 376, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.90, #queue-req: 0, 
[2025-07-28 00:14:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 364, full token usage: 0.00, #swa token: 364, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:14:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 416, full token usage: 0.00, #swa token: 416, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.89, #queue-req: 0, 
[2025-07-28 00:14:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 404, full token usage: 0.00, #swa token: 404, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:14:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 456, full token usage: 0.00, #swa token: 456, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.91, #queue-req: 0, 
[2025-07-28 00:14:14 DP0 TP0] Decode batch. #running-req: 1, #full token: 444, full token usage: 0.00, #swa token: 444, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.80, #queue-req: 0, 
[2025-07-28 00:14:14 DP1 TP0] Decode batch. #running-req: 1, #full token: 496, full token usage: 0.00, #swa token: 496, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.91, #queue-req: 0, 
[2025-07-28 00:14:15 DP0 TP0] Decode batch. #running-req: 1, #full token: 484, full token usage: 0.00, #swa token: 484, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.79, #queue-req: 0, 
[2025-07-28 00:14:15 DP1 TP0] Decode batch. #running-req: 1, #full token: 536, full token usage: 0.00, #swa token: 536, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:14:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 524, full token usage: 0.00, #swa token: 524, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.76, #queue-req: 0, 
[2025-07-28 00:14:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 576, full token usage: 0.00, #swa token: 576, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.80, #queue-req: 0, 
[2025-07-28 00:14:17 DP0 TP0] Decode batch. #running-req: 1, #full token: 564, full token usage: 0.00, #swa token: 564, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.76, #queue-req: 0, 
[2025-07-28 00:14:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 616, full token usage: 0.00, #swa token: 616, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.75, #queue-req: 0, 
[2025-07-28 00:14:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 604, full token usage: 0.00, #swa token: 604, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.70, #queue-req: 0, 
[2025-07-28 00:14:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 656, full token usage: 0.00, #swa token: 656, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.72, #queue-req: 0, 
[2025-07-28 00:14:19 DP0 TP0] Decode batch. #running-req: 1, #full token: 644, full token usage: 0.00, #swa token: 644, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.67, #queue-req: 0, 
[2025-07-28 00:14:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 696, full token usage: 0.00, #swa token: 696, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.68, #queue-req: 0, 
[2025-07-28 00:14:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 684, full token usage: 0.00, #swa token: 684, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.62, #queue-req: 0, 
[2025-07-28 00:14:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 736, full token usage: 0.00, #swa token: 736, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.63, #queue-req: 0, 
[2025-07-28 00:14:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 724, full token usage: 0.00, #swa token: 724, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.59, #queue-req: 0, 
[2025-07-28 00:14:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 776, full token usage: 0.00, #swa token: 776, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.57, #queue-req: 0, 
[2025-07-28 00:14:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 764, full token usage: 0.00, #swa token: 764, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.52, #queue-req: 0, 
[2025-07-28 00:14:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 816, full token usage: 0.00, #swa token: 816, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.54, #queue-req: 0, 
[2025-07-28 00:14:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 804, full token usage: 0.00, #swa token: 804, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.48, #queue-req: 0, 
[2025-07-28 00:14:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 856, full token usage: 0.00, #swa token: 856, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.53, #queue-req: 0, 
[2025-07-28 00:14:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 844, full token usage: 0.00, #swa token: 844, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.47, #queue-req: 0, 
[2025-07-28 00:14:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 896, full token usage: 0.00, #swa token: 896, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.54, #queue-req: 0, 
[2025-07-28 00:14:24] INFO:     127.0.0.1:39946 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:14:24 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 181, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:14:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 884, full token usage: 0.00, #swa token: 884, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.46, #queue-req: 0, 
[2025-07-28 00:14:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 309, full token usage: 0.00, #swa token: 309, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.44, #queue-req: 0, 
[2025-07-28 00:14:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 924, full token usage: 0.00, #swa token: 924, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.47, #queue-req: 0, 
[2025-07-28 00:14:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 349, full token usage: 0.00, #swa token: 349, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.91, #queue-req: 0, 
[2025-07-28 00:14:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 964, full token usage: 0.00, #swa token: 964, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.47, #queue-req: 0, 
[2025-07-28 00:14:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 389, full token usage: 0.00, #swa token: 389, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.90, #queue-req: 0, 
[2025-07-28 00:14:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 1004, full token usage: 0.00, #swa token: 1004, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.43, #queue-req: 0, 
[2025-07-28 00:14:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 429, full token usage: 0.00, #swa token: 429, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.90, #queue-req: 0, 
[2025-07-28 00:14:29] INFO:     127.0.0.1:39960 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:14:29 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 222, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:14:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 469, full token usage: 0.00, #swa token: 469, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.89, #queue-req: 0, 
[2025-07-28 00:14:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 339, full token usage: 0.00, #swa token: 339, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.94, #queue-req: 0, 
[2025-07-28 00:14:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 509, full token usage: 0.00, #swa token: 509, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.88, #queue-req: 0, 
[2025-07-28 00:14:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 379, full token usage: 0.00, #swa token: 379, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.80, #queue-req: 0, 
[2025-07-28 00:14:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 549, full token usage: 0.00, #swa token: 549, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.83, #queue-req: 0, 
[2025-07-28 00:14:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 419, full token usage: 0.00, #swa token: 419, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.80, #queue-req: 0, 
[2025-07-28 00:14:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 589, full token usage: 0.00, #swa token: 589, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.78, #queue-req: 0, 
[2025-07-28 00:14:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 459, full token usage: 0.00, #swa token: 459, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.79, #queue-req: 0, 
[2025-07-28 00:14:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 629, full token usage: 0.00, #swa token: 629, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.76, #queue-req: 0, 
[2025-07-28 00:14:33 DP0 TP0] Decode batch. #running-req: 1, #full token: 499, full token usage: 0.00, #swa token: 499, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.77, #queue-req: 0, 
[2025-07-28 00:14:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 669, full token usage: 0.00, #swa token: 669, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.72, #queue-req: 0, 
[2025-07-28 00:14:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 539, full token usage: 0.00, #swa token: 539, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.74, #queue-req: 0, 
[2025-07-28 00:14:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 709, full token usage: 0.00, #swa token: 709, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.65, #queue-req: 0, 
[2025-07-28 00:14:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 579, full token usage: 0.00, #swa token: 579, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.66, #queue-req: 0, 
[2025-07-28 00:14:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 749, full token usage: 0.00, #swa token: 749, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.61, #queue-req: 0, 
[2025-07-28 00:14:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 619, full token usage: 0.00, #swa token: 619, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.66, #queue-req: 0, 
[2025-07-28 00:14:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 789, full token usage: 0.00, #swa token: 789, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.56, #queue-req: 0, 
[2025-07-28 00:14:37 DP0 TP0] Decode batch. #running-req: 1, #full token: 659, full token usage: 0.00, #swa token: 659, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.61, #queue-req: 0, 
[2025-07-28 00:14:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 829, full token usage: 0.00, #swa token: 829, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.55, #queue-req: 0, 
[2025-07-28 00:14:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 699, full token usage: 0.00, #swa token: 699, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.56, #queue-req: 0, 
[2025-07-28 00:14:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 869, full token usage: 0.00, #swa token: 869, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.52, #queue-req: 0, 
[2025-07-28 00:14:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 739, full token usage: 0.00, #swa token: 739, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.50, #queue-req: 0, 
[2025-07-28 00:14:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 909, full token usage: 0.00, #swa token: 909, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.55, #queue-req: 0, 
[2025-07-28 00:14:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 779, full token usage: 0.00, #swa token: 779, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.47, #queue-req: 0, 
[2025-07-28 00:14:41] INFO:     127.0.0.1:58982 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:14:41 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 173, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:14:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 819, full token usage: 0.00, #swa token: 819, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.36, #queue-req: 0, 
[2025-07-28 00:14:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 281, full token usage: 0.00, #swa token: 281, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.22, #queue-req: 0, 
[2025-07-28 00:14:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 859, full token usage: 0.00, #swa token: 859, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.43, #queue-req: 0, 
[2025-07-28 00:14:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 321, full token usage: 0.00, #swa token: 321, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:14:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 899, full token usage: 0.00, #swa token: 899, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.44, #queue-req: 0, 
[2025-07-28 00:14:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 361, full token usage: 0.00, #swa token: 361, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:14:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 939, full token usage: 0.00, #swa token: 939, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.39, #queue-req: 0, 
[2025-07-28 00:14:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 401, full token usage: 0.00, #swa token: 401, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:14:44] INFO:     127.0.0.1:58990 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:14:44 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 158, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:14:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 441, full token usage: 0.00, #swa token: 441, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:14:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 289, full token usage: 0.00, #swa token: 289, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.33, #queue-req: 0, 
[2025-07-28 00:14:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 481, full token usage: 0.00, #swa token: 481, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.91, #queue-req: 0, 
[2025-07-28 00:14:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 329, full token usage: 0.00, #swa token: 329, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.82, #queue-req: 0, 
[2025-07-28 00:14:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 521, full token usage: 0.00, #swa token: 521, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.90, #queue-req: 0, 
[2025-07-28 00:14:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 369, full token usage: 0.00, #swa token: 369, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.80, #queue-req: 0, 
[2025-07-28 00:14:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 561, full token usage: 0.00, #swa token: 561, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.84, #queue-req: 0, 
[2025-07-28 00:14:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 409, full token usage: 0.00, #swa token: 409, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.82, #queue-req: 0, 
[2025-07-28 00:14:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 601, full token usage: 0.00, #swa token: 601, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.80, #queue-req: 0, 
[2025-07-28 00:14:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 449, full token usage: 0.00, #swa token: 449, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.82, #queue-req: 0, 
[2025-07-28 00:14:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 641, full token usage: 0.00, #swa token: 641, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.76, #queue-req: 0, 
[2025-07-28 00:14:50 DP0 TP0] Decode batch. #running-req: 1, #full token: 489, full token usage: 0.00, #swa token: 489, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.79, #queue-req: 0, 
[2025-07-28 00:14:51 DP1 TP0] Decode batch. #running-req: 1, #full token: 681, full token usage: 0.00, #swa token: 681, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.71, #queue-req: 0, 
[2025-07-28 00:14:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 529, full token usage: 0.00, #swa token: 529, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.76, #queue-req: 0, 
[2025-07-28 00:14:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 721, full token usage: 0.00, #swa token: 721, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.67, #queue-req: 0, 
[2025-07-28 00:14:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 569, full token usage: 0.00, #swa token: 569, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.71, #queue-req: 0, 
[2025-07-28 00:14:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 761, full token usage: 0.00, #swa token: 761, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.62, #queue-req: 0, 
[2025-07-28 00:14:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 609, full token usage: 0.00, #swa token: 609, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.67, #queue-req: 0, 
[2025-07-28 00:14:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 801, full token usage: 0.00, #swa token: 801, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.58, #queue-req: 0, 
[2025-07-28 00:14:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 649, full token usage: 0.00, #swa token: 649, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.66, #queue-req: 0, 
[2025-07-28 00:14:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 841, full token usage: 0.00, #swa token: 841, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.57, #queue-req: 0, 
[2025-07-28 00:14:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 689, full token usage: 0.00, #swa token: 689, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.57, #queue-req: 0, 
[2025-07-28 00:14:56 DP1 TP0] Decode batch. #running-req: 1, #full token: 881, full token usage: 0.00, #swa token: 881, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.55, #queue-req: 0, 
[2025-07-28 00:14:56 DP0 TP0] Decode batch. #running-req: 1, #full token: 729, full token usage: 0.00, #swa token: 729, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.07, #queue-req: 0, 
[2025-07-28 00:14:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 921, full token usage: 0.00, #swa token: 921, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.57, #queue-req: 0, 
[2025-07-28 00:14:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 769, full token usage: 0.00, #swa token: 769, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.47, #queue-req: 0, 
[2025-07-28 00:14:57] INFO:     127.0.0.1:39246 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:14:58 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 149, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:14:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 809, full token usage: 0.00, #swa token: 809, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.48, #queue-req: 0, 
[2025-07-28 00:14:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 258, full token usage: 0.00, #swa token: 258, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.36, #queue-req: 0, 
[2025-07-28 00:14:59] INFO:     127.0.0.1:48454 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:14:59 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 186, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:14:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 298, full token usage: 0.00, #swa token: 298, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:14:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 299, full token usage: 0.00, #swa token: 299, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.28, #queue-req: 0, 
[2025-07-28 00:15:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 338, full token usage: 0.00, #swa token: 338, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:15:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 339, full token usage: 0.00, #swa token: 339, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.80, #queue-req: 0, 
[2025-07-28 00:15:01 DP1 TP0] Decode batch. #running-req: 1, #full token: 378, full token usage: 0.00, #swa token: 378, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:15:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 379, full token usage: 0.00, #swa token: 379, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.82, #queue-req: 0, 
[2025-07-28 00:15:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 418, full token usage: 0.00, #swa token: 418, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:15:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 419, full token usage: 0.00, #swa token: 419, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.80, #queue-req: 0, 
[2025-07-28 00:15:03 DP1 TP0] Decode batch. #running-req: 1, #full token: 458, full token usage: 0.00, #swa token: 458, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.93, #queue-req: 0, 
[2025-07-28 00:15:03 DP0 TP0] Decode batch. #running-req: 1, #full token: 459, full token usage: 0.00, #swa token: 459, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.80, #queue-req: 0, 
[2025-07-28 00:15:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 498, full token usage: 0.00, #swa token: 498, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:15:04 DP0 TP0] Decode batch. #running-req: 1, #full token: 499, full token usage: 0.00, #swa token: 499, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.78, #queue-req: 0, 
[2025-07-28 00:15:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 538, full token usage: 0.00, #swa token: 538, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:15:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 539, full token usage: 0.00, #swa token: 539, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.75, #queue-req: 0, 
[2025-07-28 00:15:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 578, full token usage: 0.00, #swa token: 578, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.83, #queue-req: 0, 
[2025-07-28 00:15:06 DP0 TP0] Decode batch. #running-req: 1, #full token: 579, full token usage: 0.00, #swa token: 579, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.70, #queue-req: 0, 
[2025-07-28 00:15:07 DP1 TP0] Decode batch. #running-req: 1, #full token: 618, full token usage: 0.00, #swa token: 618, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.78, #queue-req: 0, 
[2025-07-28 00:15:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 619, full token usage: 0.00, #swa token: 619, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.65, #queue-req: 0, 
[2025-07-28 00:15:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 658, full token usage: 0.00, #swa token: 658, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.74, #queue-req: 0, 
[2025-07-28 00:15:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 659, full token usage: 0.00, #swa token: 659, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.62, #queue-req: 0, 
[2025-07-28 00:15:09 DP1 TP0] Decode batch. #running-req: 1, #full token: 698, full token usage: 0.00, #swa token: 698, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.68, #queue-req: 0, 
[2025-07-28 00:15:09 DP0 TP0] Decode batch. #running-req: 1, #full token: 699, full token usage: 0.00, #swa token: 699, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.57, #queue-req: 0, 
[2025-07-28 00:15:10] INFO:     127.0.0.1:57910 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:15:10 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 196, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:15:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 299, full token usage: 0.00, #swa token: 299, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.44, #queue-req: 0, 
[2025-07-28 00:15:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 739, full token usage: 0.00, #swa token: 739, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.52, #queue-req: 0, 
[2025-07-28 00:15:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 339, full token usage: 0.00, #swa token: 339, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.93, #queue-req: 0, 
[2025-07-28 00:15:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 779, full token usage: 0.00, #swa token: 779, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.48, #queue-req: 0, 
[2025-07-28 00:15:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 379, full token usage: 0.00, #swa token: 379, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:15:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 819, full token usage: 0.00, #swa token: 819, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.41, #queue-req: 0, 
[2025-07-28 00:15:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 419, full token usage: 0.00, #swa token: 419, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.93, #queue-req: 0, 
[2025-07-28 00:15:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 859, full token usage: 0.00, #swa token: 859, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.46, #queue-req: 0, 
[2025-07-28 00:15:14 DP1 TP0] Decode batch. #running-req: 1, #full token: 459, full token usage: 0.00, #swa token: 459, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.89, #queue-req: 0, 
[2025-07-28 00:15:14 DP0 TP0] Decode batch. #running-req: 1, #full token: 899, full token usage: 0.00, #swa token: 899, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.39, #queue-req: 0, 
[2025-07-28 00:15:15 DP1 TP0] Decode batch. #running-req: 1, #full token: 499, full token usage: 0.00, #swa token: 499, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.91, #queue-req: 0, 
[2025-07-28 00:15:15 DP0 TP0] Decode batch. #running-req: 1, #full token: 939, full token usage: 0.00, #swa token: 939, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.44, #queue-req: 0, 
[2025-07-28 00:15:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 539, full token usage: 0.00, #swa token: 539, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:15:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 979, full token usage: 0.00, #swa token: 979, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.42, #queue-req: 0, 
[2025-07-28 00:15:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 579, full token usage: 0.00, #swa token: 579, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.82, #queue-req: 0, 
[2025-07-28 00:15:17 DP0 TP0] Decode batch. #running-req: 1, #full token: 1019, full token usage: 0.00, #swa token: 1019, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.44, #queue-req: 0, 
[2025-07-28 00:15:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 619, full token usage: 0.00, #swa token: 619, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.72, #queue-req: 0, 
[2025-07-28 00:15:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 1059, full token usage: 0.00, #swa token: 1059, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.40, #queue-req: 0, 
[2025-07-28 00:15:18] INFO:     127.0.0.1:57916 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:15:18 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:15:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 659, full token usage: 0.00, #swa token: 659, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.73, #queue-req: 0, 
[2025-07-28 00:15:19 DP0 TP0] Decode batch. #running-req: 1, #full token: 280, full token usage: 0.00, #swa token: 280, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.43, #queue-req: 0, 
[2025-07-28 00:15:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 699, full token usage: 0.00, #swa token: 699, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.70, #queue-req: 0, 
[2025-07-28 00:15:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 320, full token usage: 0.00, #swa token: 320, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.80, #queue-req: 0, 
[2025-07-28 00:15:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 739, full token usage: 0.00, #swa token: 739, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.65, #queue-req: 0, 
[2025-07-28 00:15:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 360, full token usage: 0.00, #swa token: 360, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.80, #queue-req: 0, 
[2025-07-28 00:15:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 779, full token usage: 0.00, #swa token: 779, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.60, #queue-req: 0, 
[2025-07-28 00:15:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 400, full token usage: 0.00, #swa token: 400, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.79, #queue-req: 0, 
[2025-07-28 00:15:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 819, full token usage: 0.00, #swa token: 819, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.57, #queue-req: 0, 
[2025-07-28 00:15:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 440, full token usage: 0.00, #swa token: 440, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.58, #queue-req: 0, 
[2025-07-28 00:15:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 859, full token usage: 0.00, #swa token: 859, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.56, #queue-req: 0, 
[2025-07-28 00:15:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 480, full token usage: 0.00, #swa token: 480, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.56, #queue-req: 0, 
[2025-07-28 00:15:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 899, full token usage: 0.00, #swa token: 899, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.55, #queue-req: 0, 
[2025-07-28 00:15:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 520, full token usage: 0.00, #swa token: 520, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.55, #queue-req: 0, 
[2025-07-28 00:15:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 939, full token usage: 0.00, #swa token: 939, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.55, #queue-req: 0, 
[2025-07-28 00:15:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 560, full token usage: 0.00, #swa token: 560, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.54, #queue-req: 0, 
[2025-07-28 00:15:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 979, full token usage: 0.00, #swa token: 979, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.55, #queue-req: 0, 
[2025-07-28 00:15:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 600, full token usage: 0.00, #swa token: 600, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.55, #queue-req: 0, 
[2025-07-28 00:15:27] INFO:     127.0.0.1:60786 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:15:27 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 134, #cached-token: 101, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:15:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 249, full token usage: 0.00, #swa token: 249, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.28, #queue-req: 0, 
[2025-07-28 00:15:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 640, full token usage: 0.00, #swa token: 640, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.61, #queue-req: 0, 
[2025-07-28 00:15:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 289, full token usage: 0.00, #swa token: 289, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.95, #queue-req: 0, 
[2025-07-28 00:15:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 680, full token usage: 0.00, #swa token: 680, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.60, #queue-req: 0, 
[2025-07-28 00:15:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 329, full token usage: 0.00, #swa token: 329, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.93, #queue-req: 0, 
[2025-07-28 00:15:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 720, full token usage: 0.00, #swa token: 720, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.08, #queue-req: 0, 
[2025-07-28 00:15:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 369, full token usage: 0.00, #swa token: 369, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:15:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 760, full token usage: 0.00, #swa token: 760, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.49, #queue-req: 0, 
[2025-07-28 00:15:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 409, full token usage: 0.00, #swa token: 409, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.93, #queue-req: 0, 
[2025-07-28 00:15:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 800, full token usage: 0.00, #swa token: 800, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.46, #queue-req: 0, 
[2025-07-28 00:15:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 449, full token usage: 0.00, #swa token: 449, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:15:33 DP0 TP0] Decode batch. #running-req: 1, #full token: 840, full token usage: 0.00, #swa token: 840, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.47, #queue-req: 0, 
[2025-07-28 00:15:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 489, full token usage: 0.00, #swa token: 489, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.93, #queue-req: 0, 
[2025-07-28 00:15:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 880, full token usage: 0.00, #swa token: 880, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.44, #queue-req: 0, 
[2025-07-28 00:15:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 529, full token usage: 0.00, #swa token: 529, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:15:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 920, full token usage: 0.00, #swa token: 920, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.45, #queue-req: 0, 
[2025-07-28 00:15:35] INFO:     127.0.0.1:39940 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:15:35 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 200, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:15:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 569, full token usage: 0.00, #swa token: 569, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.77, #queue-req: 0, 
[2025-07-28 00:15:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 321, full token usage: 0.00, #swa token: 321, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.35, #queue-req: 0, 
[2025-07-28 00:15:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 609, full token usage: 0.00, #swa token: 609, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.60, #queue-req: 0, 
[2025-07-28 00:15:37 DP0 TP0] Decode batch. #running-req: 1, #full token: 361, full token usage: 0.00, #swa token: 361, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:15:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 649, full token usage: 0.00, #swa token: 649, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.73, #queue-req: 0, 
[2025-07-28 00:15:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 401, full token usage: 0.00, #swa token: 401, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:15:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 689, full token usage: 0.00, #swa token: 689, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.70, #queue-req: 0, 
[2025-07-28 00:15:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 441, full token usage: 0.00, #swa token: 441, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:15:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 729, full token usage: 0.00, #swa token: 729, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.62, #queue-req: 0, 
[2025-07-28 00:15:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 481, full token usage: 0.00, #swa token: 481, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.83, #queue-req: 0, 
[2025-07-28 00:15:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 769, full token usage: 0.00, #swa token: 769, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.61, #queue-req: 0, 
[2025-07-28 00:15:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 521, full token usage: 0.00, #swa token: 521, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.82, #queue-req: 0, 
[2025-07-28 00:15:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 809, full token usage: 0.00, #swa token: 809, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.56, #queue-req: 0, 
[2025-07-28 00:15:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 561, full token usage: 0.00, #swa token: 561, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.77, #queue-req: 0, 
[2025-07-28 00:15:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 849, full token usage: 0.00, #swa token: 849, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.53, #queue-req: 0, 
[2025-07-28 00:15:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 601, full token usage: 0.00, #swa token: 601, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.73, #queue-req: 0, 
[2025-07-28 00:15:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 889, full token usage: 0.00, #swa token: 889, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.55, #queue-req: 0, 
[2025-07-28 00:15:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 641, full token usage: 0.00, #swa token: 641, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.70, #queue-req: 0, 
[2025-07-28 00:15:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 929, full token usage: 0.00, #swa token: 929, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.56, #queue-req: 0, 
[2025-07-28 00:15:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 681, full token usage: 0.00, #swa token: 681, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.62, #queue-req: 0, 
[2025-07-28 00:15:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 969, full token usage: 0.00, #swa token: 969, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.54, #queue-req: 0, 
[2025-07-28 00:15:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 721, full token usage: 0.00, #swa token: 721, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.59, #queue-req: 0, 
[2025-07-28 00:15:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 1009, full token usage: 0.00, #swa token: 1009, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.49, #queue-req: 0, 
[2025-07-28 00:15:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 761, full token usage: 0.00, #swa token: 761, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.42, #queue-req: 0, 
[2025-07-28 00:15:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 1049, full token usage: 0.00, #swa token: 1049, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.52, #queue-req: 0, 
[2025-07-28 00:15:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 801, full token usage: 0.00, #swa token: 801, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.49, #queue-req: 0, 
[2025-07-28 00:15:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 1089, full token usage: 0.00, #swa token: 1089, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.49, #queue-req: 0, 
[2025-07-28 00:15:48] INFO:     127.0.0.1:56654 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:15:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 841, full token usage: 0.00, #swa token: 841, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.46, #queue-req: 0, 
[2025-07-28 00:15:48 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 189, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:15:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 318, full token usage: 0.00, #swa token: 318, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.49, #queue-req: 0, 
[2025-07-28 00:15:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 881, full token usage: 0.00, #swa token: 881, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.48, #queue-req: 0, 
[2025-07-28 00:15:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 358, full token usage: 0.00, #swa token: 358, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.88, #queue-req: 0, 
[2025-07-28 00:15:50 DP0 TP0] Decode batch. #running-req: 1, #full token: 921, full token usage: 0.00, #swa token: 921, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.47, #queue-req: 0, 
[2025-07-28 00:15:51 DP1 TP0] Decode batch. #running-req: 1, #full token: 398, full token usage: 0.00, #swa token: 398, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.93, #queue-req: 0, 
[2025-07-28 00:15:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 961, full token usage: 0.00, #swa token: 961, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.47, #queue-req: 0, 
[2025-07-28 00:15:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 438, full token usage: 0.00, #swa token: 438, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:15:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 1001, full token usage: 0.00, #swa token: 1001, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.45, #queue-req: 0, 
[2025-07-28 00:15:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 478, full token usage: 0.00, #swa token: 478, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.93, #queue-req: 0, 
[2025-07-28 00:15:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 1041, full token usage: 0.00, #swa token: 1041, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.45, #queue-req: 0, 
[2025-07-28 00:15:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 518, full token usage: 0.00, #swa token: 518, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.80, #queue-req: 0, 
[2025-07-28 00:15:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 1081, full token usage: 0.00, #swa token: 1081, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.43, #queue-req: 0, 
[2025-07-28 00:15:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 558, full token usage: 0.00, #swa token: 558, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.82, #queue-req: 0, 
[2025-07-28 00:15:55] INFO:     127.0.0.1:53306 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:15:55 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 175, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:15:56 DP0 TP0] Decode batch. #running-req: 1, #full token: 281, full token usage: 0.00, #swa token: 281, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.23, #queue-req: 0, 
[2025-07-28 00:15:56 DP1 TP0] Decode batch. #running-req: 1, #full token: 598, full token usage: 0.00, #swa token: 598, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.76, #queue-req: 0, 
[2025-07-28 00:15:56 DP0 TP0] Decode batch. #running-req: 1, #full token: 321, full token usage: 0.00, #swa token: 321, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.83, #queue-req: 0, 
[2025-07-28 00:15:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 638, full token usage: 0.00, #swa token: 638, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.73, #queue-req: 0, 
[2025-07-28 00:15:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 361, full token usage: 0.00, #swa token: 361, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.40, #queue-req: 0, 
[2025-07-28 00:15:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 678, full token usage: 0.00, #swa token: 678, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.71, #queue-req: 0, 
[2025-07-28 00:15:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 401, full token usage: 0.00, #swa token: 401, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:15:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 718, full token usage: 0.00, #swa token: 718, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.65, #queue-req: 0, 
[2025-07-28 00:15:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 441, full token usage: 0.00, #swa token: 441, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:16:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 758, full token usage: 0.00, #swa token: 758, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.60, #queue-req: 0, 
[2025-07-28 00:16:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 481, full token usage: 0.00, #swa token: 481, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.84, #queue-req: 0, 
[2025-07-28 00:16:01 DP1 TP0] Decode batch. #running-req: 1, #full token: 798, full token usage: 0.00, #swa token: 798, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.56, #queue-req: 0, 
[2025-07-28 00:16:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 521, full token usage: 0.00, #swa token: 521, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.82, #queue-req: 0, 
[2025-07-28 00:16:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 838, full token usage: 0.00, #swa token: 838, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.53, #queue-req: 0, 
[2025-07-28 00:16:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 561, full token usage: 0.00, #swa token: 561, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.76, #queue-req: 0, 
[2025-07-28 00:16:03 DP1 TP0] Decode batch. #running-req: 1, #full token: 878, full token usage: 0.00, #swa token: 878, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.55, #queue-req: 0, 
[2025-07-28 00:16:03 DP0 TP0] Decode batch. #running-req: 1, #full token: 601, full token usage: 0.00, #swa token: 601, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.73, #queue-req: 0, 
[2025-07-28 00:16:04] INFO:     127.0.0.1:44208 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:16:04 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 177, #cached-token: 101, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:16:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 291, full token usage: 0.00, #swa token: 291, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.28, #queue-req: 0, 
[2025-07-28 00:16:04 DP0 TP0] Decode batch. #running-req: 1, #full token: 641, full token usage: 0.00, #swa token: 641, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.68, #queue-req: 0, 
[2025-07-28 00:16:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 331, full token usage: 0.00, #swa token: 331, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:16:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 681, full token usage: 0.00, #swa token: 681, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.63, #queue-req: 0, 
[2025-07-28 00:16:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 371, full token usage: 0.00, #swa token: 371, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.93, #queue-req: 0, 
[2025-07-28 00:16:06 DP0 TP0] Decode batch. #running-req: 1, #full token: 721, full token usage: 0.00, #swa token: 721, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.59, #queue-req: 0, 
[2025-07-28 00:16:07 DP1 TP0] Decode batch. #running-req: 1, #full token: 411, full token usage: 0.00, #swa token: 411, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:16:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 761, full token usage: 0.00, #swa token: 761, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.55, #queue-req: 0, 
[2025-07-28 00:16:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 451, full token usage: 0.00, #swa token: 451, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:16:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 801, full token usage: 0.00, #swa token: 801, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.51, #queue-req: 0, 
[2025-07-28 00:16:09 DP1 TP0] Decode batch. #running-req: 1, #full token: 491, full token usage: 0.00, #swa token: 491, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:16:09 DP0 TP0] Decode batch. #running-req: 1, #full token: 841, full token usage: 0.00, #swa token: 841, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.50, #queue-req: 0, 
[2025-07-28 00:16:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 531, full token usage: 0.00, #swa token: 531, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.88, #queue-req: 0, 
[2025-07-28 00:16:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 881, full token usage: 0.00, #swa token: 881, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.50, #queue-req: 0, 
[2025-07-28 00:16:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 571, full token usage: 0.00, #swa token: 571, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.81, #queue-req: 0, 
[2025-07-28 00:16:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 921, full token usage: 0.00, #swa token: 921, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.50, #queue-req: 0, 
[2025-07-28 00:16:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 611, full token usage: 0.00, #swa token: 611, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.77, #queue-req: 0, 
[2025-07-28 00:16:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 961, full token usage: 0.00, #swa token: 961, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.51, #queue-req: 0, 
[2025-07-28 00:16:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 651, full token usage: 0.00, #swa token: 651, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.75, #queue-req: 0, 
[2025-07-28 00:16:13] INFO:     127.0.0.1:45424 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:16:13 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 210, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:16:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 320, full token usage: 0.00, #swa token: 320, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.39, #queue-req: 0, 
[2025-07-28 00:16:14 DP1 TP0] Decode batch. #running-req: 1, #full token: 691, full token usage: 0.00, #swa token: 691, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.69, #queue-req: 0, 
[2025-07-28 00:16:14 DP0 TP0] Decode batch. #running-req: 1, #full token: 360, full token usage: 0.00, #swa token: 360, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:16:15 DP1 TP0] Decode batch. #running-req: 1, #full token: 731, full token usage: 0.00, #swa token: 731, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.59, #queue-req: 0, 
[2025-07-28 00:16:15 DP0 TP0] Decode batch. #running-req: 1, #full token: 400, full token usage: 0.00, #swa token: 400, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:16:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 771, full token usage: 0.00, #swa token: 771, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.54, #queue-req: 0, 
[2025-07-28 00:16:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 440, full token usage: 0.00, #swa token: 440, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:16:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 811, full token usage: 0.00, #swa token: 811, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.54, #queue-req: 0, 
[2025-07-28 00:16:17 DP0 TP0] Decode batch. #running-req: 1, #full token: 480, full token usage: 0.00, #swa token: 480, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:16:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 851, full token usage: 0.00, #swa token: 851, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.53, #queue-req: 0, 
[2025-07-28 00:16:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 520, full token usage: 0.00, #swa token: 520, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.81, #queue-req: 0, 
[2025-07-28 00:16:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 891, full token usage: 0.00, #swa token: 891, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.51, #queue-req: 0, 
[2025-07-28 00:16:19 DP0 TP0] Decode batch. #running-req: 1, #full token: 560, full token usage: 0.00, #swa token: 560, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.71, #queue-req: 0, 
[2025-07-28 00:16:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 931, full token usage: 0.00, #swa token: 931, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.52, #queue-req: 0, 
[2025-07-28 00:16:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 600, full token usage: 0.00, #swa token: 600, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.73, #queue-req: 0, 
[2025-07-28 00:16:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 971, full token usage: 0.00, #swa token: 971, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.51, #queue-req: 0, 
[2025-07-28 00:16:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 640, full token usage: 0.00, #swa token: 640, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.70, #queue-req: 0, 
[2025-07-28 00:16:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 1011, full token usage: 0.00, #swa token: 1011, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.51, #queue-req: 0, 
[2025-07-28 00:16:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 680, full token usage: 0.00, #swa token: 680, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.64, #queue-req: 0, 
[2025-07-28 00:16:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 1051, full token usage: 0.00, #swa token: 1051, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.49, #queue-req: 0, 
[2025-07-28 00:16:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 720, full token usage: 0.00, #swa token: 720, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.58, #queue-req: 0, 
[2025-07-28 00:16:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 1091, full token usage: 0.00, #swa token: 1091, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.49, #queue-req: 0, 
[2025-07-28 00:16:24] INFO:     127.0.0.1:37910 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:16:24 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 161, #cached-token: 102, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:16:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 760, full token usage: 0.00, #swa token: 760, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.56, #queue-req: 0, 
[2025-07-28 00:16:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 294, full token usage: 0.00, #swa token: 294, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.39, #queue-req: 0, 
[2025-07-28 00:16:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 800, full token usage: 0.00, #swa token: 800, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.50, #queue-req: 0, 
[2025-07-28 00:16:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 334, full token usage: 0.00, #swa token: 334, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:16:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 840, full token usage: 0.00, #swa token: 840, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.48, #queue-req: 0, 
[2025-07-28 00:16:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 374, full token usage: 0.00, #swa token: 374, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:16:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 880, full token usage: 0.00, #swa token: 880, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.48, #queue-req: 0, 
[2025-07-28 00:16:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 414, full token usage: 0.00, #swa token: 414, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.91, #queue-req: 0, 
[2025-07-28 00:16:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 920, full token usage: 0.00, #swa token: 920, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.49, #queue-req: 0, 
[2025-07-28 00:16:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 454, full token usage: 0.00, #swa token: 454, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.90, #queue-req: 0, 
[2025-07-28 00:16:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 960, full token usage: 0.00, #swa token: 960, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.49, #queue-req: 0, 
[2025-07-28 00:16:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 494, full token usage: 0.00, #swa token: 494, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.84, #queue-req: 0, 
[2025-07-28 00:16:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 1000, full token usage: 0.00, #swa token: 1000, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.48, #queue-req: 0, 
[2025-07-28 00:16:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 534, full token usage: 0.00, #swa token: 534, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:16:31] INFO:     127.0.0.1:33280 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:16:31 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 172, #cached-token: 102, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:16:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 278, full token usage: 0.00, #swa token: 278, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.88, #queue-req: 0, 
[2025-07-28 00:16:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 574, full token usage: 0.00, #swa token: 574, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.83, #queue-req: 0, 
[2025-07-28 00:16:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 318, full token usage: 0.00, #swa token: 318, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:16:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 614, full token usage: 0.00, #swa token: 614, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.80, #queue-req: 0, 
[2025-07-28 00:16:33 DP0 TP0] Decode batch. #running-req: 1, #full token: 358, full token usage: 0.00, #swa token: 358, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:16:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 654, full token usage: 0.00, #swa token: 654, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.75, #queue-req: 0, 
[2025-07-28 00:16:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 398, full token usage: 0.00, #swa token: 398, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.88, #queue-req: 0, 
[2025-07-28 00:16:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 694, full token usage: 0.00, #swa token: 694, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.69, #queue-req: 0, 
[2025-07-28 00:16:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 438, full token usage: 0.00, #swa token: 438, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:16:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 734, full token usage: 0.00, #swa token: 734, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.64, #queue-req: 0, 
[2025-07-28 00:16:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 478, full token usage: 0.00, #swa token: 478, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:16:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 774, full token usage: 0.00, #swa token: 774, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.56, #queue-req: 0, 
[2025-07-28 00:16:37 DP0 TP0] Decode batch. #running-req: 1, #full token: 518, full token usage: 0.00, #swa token: 518, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.84, #queue-req: 0, 
[2025-07-28 00:16:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 814, full token usage: 0.00, #swa token: 814, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.51, #queue-req: 0, 
[2025-07-28 00:16:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 558, full token usage: 0.00, #swa token: 558, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.78, #queue-req: 0, 
[2025-07-28 00:16:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 854, full token usage: 0.00, #swa token: 854, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.49, #queue-req: 0, 
[2025-07-28 00:16:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 598, full token usage: 0.00, #swa token: 598, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.75, #queue-req: 0, 
[2025-07-28 00:16:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 894, full token usage: 0.00, #swa token: 894, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.48, #queue-req: 0, 
[2025-07-28 00:16:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 638, full token usage: 0.00, #swa token: 638, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.70, #queue-req: 0, 
[2025-07-28 00:16:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 934, full token usage: 0.00, #swa token: 934, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.54, #queue-req: 0, 
[2025-07-28 00:16:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 678, full token usage: 0.00, #swa token: 678, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.66, #queue-req: 0, 
[2025-07-28 00:16:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 974, full token usage: 0.00, #swa token: 974, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.55, #queue-req: 0, 
[2025-07-28 00:16:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 718, full token usage: 0.00, #swa token: 718, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.61, #queue-req: 0, 
[2025-07-28 00:16:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 1014, full token usage: 0.00, #swa token: 1014, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.54, #queue-req: 0, 
[2025-07-28 00:16:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 758, full token usage: 0.00, #swa token: 758, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.54, #queue-req: 0, 
[2025-07-28 00:16:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 1054, full token usage: 0.00, #swa token: 1054, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.49, #queue-req: 0, 
[2025-07-28 00:16:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 798, full token usage: 0.00, #swa token: 798, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.50, #queue-req: 0, 
[2025-07-28 00:16:44] INFO:     127.0.0.1:35516 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:16:44 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 102, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:16:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 212, full token usage: 0.00, #swa token: 212, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.32, #queue-req: 0, 
[2025-07-28 00:16:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 838, full token usage: 0.00, #swa token: 838, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.49, #queue-req: 0, 
[2025-07-28 00:16:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 252, full token usage: 0.00, #swa token: 252, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 41.01, #queue-req: 0, 
[2025-07-28 00:16:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 878, full token usage: 0.00, #swa token: 878, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.49, #queue-req: 0, 
[2025-07-28 00:16:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 292, full token usage: 0.00, #swa token: 292, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:16:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 918, full token usage: 0.00, #swa token: 918, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.49, #queue-req: 0, 
[2025-07-28 00:16:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 332, full token usage: 0.00, #swa token: 332, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.93, #queue-req: 0, 
[2025-07-28 00:16:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 958, full token usage: 0.00, #swa token: 958, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.46, #queue-req: 0, 
[2025-07-28 00:16:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 372, full token usage: 0.00, #swa token: 372, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.91, #queue-req: 0, 
[2025-07-28 00:16:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 998, full token usage: 0.00, #swa token: 998, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.44, #queue-req: 0, 
[2025-07-28 00:16:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 412, full token usage: 0.00, #swa token: 412, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.90, #queue-req: 0, 
[2025-07-28 00:16:50 DP0 TP0] Decode batch. #running-req: 1, #full token: 1038, full token usage: 0.00, #swa token: 1038, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.40, #queue-req: 0, 
[2025-07-28 00:16:51 DP1 TP0] Decode batch. #running-req: 1, #full token: 452, full token usage: 0.00, #swa token: 452, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:16:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 1078, full token usage: 0.00, #swa token: 1078, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.41, #queue-req: 0, 
[2025-07-28 00:16:51] INFO:     127.0.0.1:35528 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:16:51 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 120, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:16:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 492, full token usage: 0.00, #swa token: 492, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.88, #queue-req: 0, 
[2025-07-28 00:16:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 245, full token usage: 0.00, #swa token: 245, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.56, #queue-req: 0, 
[2025-07-28 00:16:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 532, full token usage: 0.00, #swa token: 532, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.88, #queue-req: 0, 
[2025-07-28 00:16:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 285, full token usage: 0.00, #swa token: 285, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.82, #queue-req: 0, 
[2025-07-28 00:16:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 572, full token usage: 0.00, #swa token: 572, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.81, #queue-req: 0, 
[2025-07-28 00:16:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 325, full token usage: 0.00, #swa token: 325, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.80, #queue-req: 0, 
[2025-07-28 00:16:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 612, full token usage: 0.00, #swa token: 612, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.75, #queue-req: 0, 
[2025-07-28 00:16:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 365, full token usage: 0.00, #swa token: 365, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.79, #queue-req: 0, 
[2025-07-28 00:16:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 652, full token usage: 0.00, #swa token: 652, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.73, #queue-req: 0, 
[2025-07-28 00:16:56 DP0 TP0] Decode batch. #running-req: 1, #full token: 405, full token usage: 0.00, #swa token: 405, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.81, #queue-req: 0, 
[2025-07-28 00:16:56 DP1 TP0] Decode batch. #running-req: 1, #full token: 692, full token usage: 0.00, #swa token: 692, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.68, #queue-req: 0, 
[2025-07-28 00:16:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 445, full token usage: 0.00, #swa token: 445, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.80, #queue-req: 0, 
[2025-07-28 00:16:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 732, full token usage: 0.00, #swa token: 732, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.66, #queue-req: 0, 
[2025-07-28 00:16:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 485, full token usage: 0.00, #swa token: 485, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.79, #queue-req: 0, 
[2025-07-28 00:16:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 772, full token usage: 0.00, #swa token: 772, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.59, #queue-req: 0, 
[2025-07-28 00:16:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 525, full token usage: 0.00, #swa token: 525, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.33, #queue-req: 0, 
[2025-07-28 00:16:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 812, full token usage: 0.00, #swa token: 812, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.57, #queue-req: 0, 
[2025-07-28 00:17:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 565, full token usage: 0.00, #swa token: 565, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.76, #queue-req: 0, 
[2025-07-28 00:17:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 852, full token usage: 0.00, #swa token: 852, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.57, #queue-req: 0, 
[2025-07-28 00:17:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 605, full token usage: 0.00, #swa token: 605, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.72, #queue-req: 0, 
[2025-07-28 00:17:01] INFO:     127.0.0.1:55078 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:17:01 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 152, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:17:01 DP1 TP0] Decode batch. #running-req: 1, #full token: 265, full token usage: 0.00, #swa token: 265, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.41, #queue-req: 0, 
[2025-07-28 00:17:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 645, full token usage: 0.00, #swa token: 645, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.69, #queue-req: 0, 
[2025-07-28 00:17:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 305, full token usage: 0.00, #swa token: 305, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:17:03 DP0 TP0] Decode batch. #running-req: 1, #full token: 685, full token usage: 0.00, #swa token: 685, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.64, #queue-req: 0, 
[2025-07-28 00:17:03 DP1 TP0] Decode batch. #running-req: 1, #full token: 345, full token usage: 0.00, #swa token: 345, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:17:04 DP0 TP0] Decode batch. #running-req: 1, #full token: 725, full token usage: 0.00, #swa token: 725, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.59, #queue-req: 0, 
[2025-07-28 00:17:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 385, full token usage: 0.00, #swa token: 385, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:17:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 765, full token usage: 0.00, #swa token: 765, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.53, #queue-req: 0, 
[2025-07-28 00:17:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 425, full token usage: 0.00, #swa token: 425, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.93, #queue-req: 0, 
[2025-07-28 00:17:05] INFO:     127.0.0.1:55082 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:17:05 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 153, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:17:06 DP0 TP0] Decode batch. #running-req: 1, #full token: 268, full token usage: 0.00, #swa token: 268, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.38, #queue-req: 0, 
[2025-07-28 00:17:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 465, full token usage: 0.00, #swa token: 465, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:17:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 308, full token usage: 0.00, #swa token: 308, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:17:07 DP1 TP0] Decode batch. #running-req: 1, #full token: 505, full token usage: 0.00, #swa token: 505, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.90, #queue-req: 0, 
[2025-07-28 00:17:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 348, full token usage: 0.00, #swa token: 348, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:17:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 545, full token usage: 0.00, #swa token: 545, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:17:09 DP0 TP0] Decode batch. #running-req: 1, #full token: 388, full token usage: 0.00, #swa token: 388, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:17:09 DP1 TP0] Decode batch. #running-req: 1, #full token: 585, full token usage: 0.00, #swa token: 585, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.82, #queue-req: 0, 
[2025-07-28 00:17:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 428, full token usage: 0.00, #swa token: 428, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:17:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 625, full token usage: 0.00, #swa token: 625, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.77, #queue-req: 0, 
[2025-07-28 00:17:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 468, full token usage: 0.00, #swa token: 468, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:17:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 665, full token usage: 0.00, #swa token: 665, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.72, #queue-req: 0, 
[2025-07-28 00:17:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 508, full token usage: 0.00, #swa token: 508, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:17:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 705, full token usage: 0.00, #swa token: 705, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.69, #queue-req: 0, 
[2025-07-28 00:17:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 548, full token usage: 0.00, #swa token: 548, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.78, #queue-req: 0, 
[2025-07-28 00:17:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 745, full token usage: 0.00, #swa token: 745, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.61, #queue-req: 0, 
[2025-07-28 00:17:14 DP0 TP0] Decode batch. #running-req: 1, #full token: 588, full token usage: 0.00, #swa token: 588, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.74, #queue-req: 0, 
[2025-07-28 00:17:14 DP1 TP0] Decode batch. #running-req: 1, #full token: 785, full token usage: 0.00, #swa token: 785, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.58, #queue-req: 0, 
[2025-07-28 00:17:15 DP0 TP0] Decode batch. #running-req: 1, #full token: 628, full token usage: 0.00, #swa token: 628, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.71, #queue-req: 0, 
[2025-07-28 00:17:15 DP1 TP0] Decode batch. #running-req: 1, #full token: 825, full token usage: 0.00, #swa token: 825, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.56, #queue-req: 0, 
[2025-07-28 00:17:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 668, full token usage: 0.00, #swa token: 668, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.66, #queue-req: 0, 
[2025-07-28 00:17:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 865, full token usage: 0.00, #swa token: 865, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.56, #queue-req: 0, 
[2025-07-28 00:17:17 DP0 TP0] Decode batch. #running-req: 1, #full token: 708, full token usage: 0.00, #swa token: 708, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.60, #queue-req: 0, 
[2025-07-28 00:17:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 905, full token usage: 0.00, #swa token: 905, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.55, #queue-req: 0, 
[2025-07-28 00:17:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 748, full token usage: 0.00, #swa token: 748, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.56, #queue-req: 0, 
[2025-07-28 00:17:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 945, full token usage: 0.00, #swa token: 945, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.56, #queue-req: 0, 
[2025-07-28 00:17:19 DP0 TP0] Decode batch. #running-req: 1, #full token: 788, full token usage: 0.00, #swa token: 788, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.51, #queue-req: 0, 
[2025-07-28 00:17:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 985, full token usage: 0.00, #swa token: 985, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.56, #queue-req: 0, 
[2025-07-28 00:17:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 828, full token usage: 0.00, #swa token: 828, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.50, #queue-req: 0, 
[2025-07-28 00:17:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 1025, full token usage: 0.00, #swa token: 1025, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.54, #queue-req: 0, 
[2025-07-28 00:17:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 868, full token usage: 0.00, #swa token: 868, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.50, #queue-req: 0, 
[2025-07-28 00:17:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 1065, full token usage: 0.00, #swa token: 1065, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.47, #queue-req: 0, 
[2025-07-28 00:17:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 908, full token usage: 0.00, #swa token: 908, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.51, #queue-req: 0, 
[2025-07-28 00:17:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 1105, full token usage: 0.00, #swa token: 1105, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.49, #queue-req: 0, 
[2025-07-28 00:17:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 948, full token usage: 0.00, #swa token: 948, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.51, #queue-req: 0, 
[2025-07-28 00:17:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 1145, full token usage: 0.00, #swa token: 1145, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.49, #queue-req: 0, 
[2025-07-28 00:17:23] INFO:     127.0.0.1:47514 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:17:23 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 167, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:17:24] INFO:     127.0.0.1:40938 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:17:24 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 121, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:17:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 297, full token usage: 0.00, #swa token: 297, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 49.29, #queue-req: 0, 
[2025-07-28 00:17:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 232, full token usage: 0.00, #swa token: 232, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 24.27, #queue-req: 0, 
[2025-07-28 00:17:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 337, full token usage: 0.00, #swa token: 337, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:17:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 272, full token usage: 0.00, #swa token: 272, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.91, #queue-req: 0, 
[2025-07-28 00:17:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 377, full token usage: 0.00, #swa token: 377, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.89, #queue-req: 0, 
[2025-07-28 00:17:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 312, full token usage: 0.00, #swa token: 312, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:17:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 417, full token usage: 0.00, #swa token: 417, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.89, #queue-req: 0, 
[2025-07-28 00:17:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 352, full token usage: 0.00, #swa token: 352, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:17:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 457, full token usage: 0.00, #swa token: 457, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:17:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 392, full token usage: 0.00, #swa token: 392, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:17:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 497, full token usage: 0.00, #swa token: 497, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.89, #queue-req: 0, 
[2025-07-28 00:17:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 432, full token usage: 0.00, #swa token: 432, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:17:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 537, full token usage: 0.00, #swa token: 537, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:17:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 472, full token usage: 0.00, #swa token: 472, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:17:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 577, full token usage: 0.00, #swa token: 577, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.80, #queue-req: 0, 
[2025-07-28 00:17:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 512, full token usage: 0.00, #swa token: 512, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.83, #queue-req: 0, 
[2025-07-28 00:17:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 617, full token usage: 0.00, #swa token: 617, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.75, #queue-req: 0, 
[2025-07-28 00:17:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 552, full token usage: 0.00, #swa token: 552, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.28, #queue-req: 0, 
[2025-07-28 00:17:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 657, full token usage: 0.00, #swa token: 657, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.74, #queue-req: 0, 
[2025-07-28 00:17:33 DP0 TP0] Decode batch. #running-req: 1, #full token: 592, full token usage: 0.00, #swa token: 592, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.75, #queue-req: 0, 
[2025-07-28 00:17:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 697, full token usage: 0.00, #swa token: 697, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.69, #queue-req: 0, 
[2025-07-28 00:17:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 632, full token usage: 0.00, #swa token: 632, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.70, #queue-req: 0, 
[2025-07-28 00:17:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 737, full token usage: 0.00, #swa token: 737, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.64, #queue-req: 0, 
[2025-07-28 00:17:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 672, full token usage: 0.00, #swa token: 672, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.66, #queue-req: 0, 
[2025-07-28 00:17:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 777, full token usage: 0.00, #swa token: 777, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.59, #queue-req: 0, 
[2025-07-28 00:17:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 712, full token usage: 0.00, #swa token: 712, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.60, #queue-req: 0, 
[2025-07-28 00:17:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 817, full token usage: 0.00, #swa token: 817, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.55, #queue-req: 0, 
[2025-07-28 00:17:37 DP0 TP0] Decode batch. #running-req: 1, #full token: 752, full token usage: 0.00, #swa token: 752, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.56, #queue-req: 0, 
[2025-07-28 00:17:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 857, full token usage: 0.00, #swa token: 857, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.55, #queue-req: 0, 
[2025-07-28 00:17:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 792, full token usage: 0.00, #swa token: 792, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.50, #queue-req: 0, 
[2025-07-28 00:17:39] INFO:     127.0.0.1:44054 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:17:39 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 89, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:17:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 832, full token usage: 0.00, #swa token: 832, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.50, #queue-req: 0, 
[2025-07-28 00:17:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 192, full token usage: 0.00, #swa token: 192, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.25, #queue-req: 0, 
[2025-07-28 00:17:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 872, full token usage: 0.00, #swa token: 872, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.50, #queue-req: 0, 
[2025-07-28 00:17:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 232, full token usage: 0.00, #swa token: 232, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 41.03, #queue-req: 0, 
[2025-07-28 00:17:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 912, full token usage: 0.00, #swa token: 912, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.48, #queue-req: 0, 
[2025-07-28 00:17:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 272, full token usage: 0.00, #swa token: 272, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.98, #queue-req: 0, 
[2025-07-28 00:17:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 952, full token usage: 0.00, #swa token: 952, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.48, #queue-req: 0, 
[2025-07-28 00:17:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 312, full token usage: 0.00, #swa token: 312, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.93, #queue-req: 0, 
[2025-07-28 00:17:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 992, full token usage: 0.00, #swa token: 992, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.48, #queue-req: 0, 
[2025-07-28 00:17:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 352, full token usage: 0.00, #swa token: 352, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:17:44] INFO:     127.0.0.1:44056 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:17:44 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 134, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:17:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 392, full token usage: 0.00, #swa token: 392, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.89, #queue-req: 0, 
[2025-07-28 00:17:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 242, full token usage: 0.00, #swa token: 242, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.37, #queue-req: 0, 
[2025-07-28 00:17:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 432, full token usage: 0.00, #swa token: 432, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.91, #queue-req: 0, 
[2025-07-28 00:17:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 282, full token usage: 0.00, #swa token: 282, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.89, #queue-req: 0, 
[2025-07-28 00:17:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 472, full token usage: 0.00, #swa token: 472, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.91, #queue-req: 0, 
[2025-07-28 00:17:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 322, full token usage: 0.00, #swa token: 322, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:17:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 512, full token usage: 0.00, #swa token: 512, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.91, #queue-req: 0, 
[2025-07-28 00:17:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 362, full token usage: 0.00, #swa token: 362, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:17:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 552, full token usage: 0.00, #swa token: 552, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:17:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 402, full token usage: 0.00, #swa token: 402, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:17:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 592, full token usage: 0.00, #swa token: 592, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.80, #queue-req: 0, 
[2025-07-28 00:17:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 442, full token usage: 0.00, #swa token: 442, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:17:49] INFO:     127.0.0.1:42016 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:17:49 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:17:50 DP1 TP0] Decode batch. #running-req: 2, #full token: 789, full token usage: 0.00, #swa token: 789, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 47.86, #queue-req: 0, 
[2025-07-28 00:17:51] INFO:     127.0.0.1:48050 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:17:51 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 93, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:17:51 DP1 TP0] Decode batch. #running-req: 1, #full token: 294, full token usage: 0.00, #swa token: 294, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 63.49, #queue-req: 0, 
[2025-07-28 00:17:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 208, full token usage: 0.00, #swa token: 208, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 18.44, #queue-req: 0, 
[2025-07-28 00:17:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 334, full token usage: 0.00, #swa token: 334, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.93, #queue-req: 0, 
[2025-07-28 00:17:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 248, full token usage: 0.00, #swa token: 248, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:17:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 374, full token usage: 0.00, #swa token: 374, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.93, #queue-req: 0, 
[2025-07-28 00:17:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 288, full token usage: 0.00, #swa token: 288, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.89, #queue-req: 0, 
[2025-07-28 00:17:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 414, full token usage: 0.00, #swa token: 414, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:17:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 328, full token usage: 0.00, #swa token: 328, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:17:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 454, full token usage: 0.00, #swa token: 454, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:17:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 368, full token usage: 0.00, #swa token: 368, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:17:56 DP1 TP0] Decode batch. #running-req: 1, #full token: 494, full token usage: 0.00, #swa token: 494, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:17:56 DP0 TP0] Decode batch. #running-req: 1, #full token: 408, full token usage: 0.00, #swa token: 408, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:17:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 534, full token usage: 0.00, #swa token: 534, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:17:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 448, full token usage: 0.00, #swa token: 448, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.81, #queue-req: 0, 
[2025-07-28 00:17:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 574, full token usage: 0.00, #swa token: 574, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.79, #queue-req: 0, 
[2025-07-28 00:17:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 488, full token usage: 0.00, #swa token: 488, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:17:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 614, full token usage: 0.00, #swa token: 614, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.79, #queue-req: 0, 
[2025-07-28 00:17:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 528, full token usage: 0.00, #swa token: 528, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.82, #queue-req: 0, 
[2025-07-28 00:18:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 654, full token usage: 0.00, #swa token: 654, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.70, #queue-req: 0, 
[2025-07-28 00:18:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 568, full token usage: 0.00, #swa token: 568, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.28, #queue-req: 0, 
[2025-07-28 00:18:01 DP1 TP0] Decode batch. #running-req: 1, #full token: 694, full token usage: 0.00, #swa token: 694, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.70, #queue-req: 0, 
[2025-07-28 00:18:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 608, full token usage: 0.00, #swa token: 608, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.74, #queue-req: 0, 
[2025-07-28 00:18:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 734, full token usage: 0.00, #swa token: 734, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.66, #queue-req: 0, 
[2025-07-28 00:18:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 648, full token usage: 0.00, #swa token: 648, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.70, #queue-req: 0, 
[2025-07-28 00:18:03 DP1 TP0] Decode batch. #running-req: 1, #full token: 774, full token usage: 0.00, #swa token: 774, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.62, #queue-req: 0, 
[2025-07-28 00:18:03 DP0 TP0] Decode batch. #running-req: 1, #full token: 688, full token usage: 0.00, #swa token: 688, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.63, #queue-req: 0, 
[2025-07-28 00:18:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 814, full token usage: 0.00, #swa token: 814, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.55, #queue-req: 0, 
[2025-07-28 00:18:04 DP0 TP0] Decode batch. #running-req: 1, #full token: 728, full token usage: 0.00, #swa token: 728, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.58, #queue-req: 0, 
[2025-07-28 00:18:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 854, full token usage: 0.00, #swa token: 854, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.50, #queue-req: 0, 
[2025-07-28 00:18:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 768, full token usage: 0.00, #swa token: 768, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.53, #queue-req: 0, 
[2025-07-28 00:18:06] INFO:     127.0.0.1:42030 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:18:06 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 167, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:18:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 1060, full token usage: 0.00, #swa token: 1060, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.79, #queue-req: 0, 
[2025-07-28 00:18:07] INFO:     127.0.0.1:42018 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:18:07 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 152, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:18:07 DP1 TP0] Decode batch. #running-req: 1, #full token: 305, full token usage: 0.00, #swa token: 305, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 67.60, #queue-req: 0, 
[2025-07-28 00:18:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 260, full token usage: 0.00, #swa token: 260, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 19.80, #queue-req: 0, 
[2025-07-28 00:18:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 345, full token usage: 0.00, #swa token: 345, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.83, #queue-req: 0, 
[2025-07-28 00:18:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 300, full token usage: 0.00, #swa token: 300, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:18:09 DP1 TP0] Decode batch. #running-req: 1, #full token: 385, full token usage: 0.00, #swa token: 385, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.91, #queue-req: 0, 
[2025-07-28 00:18:09 DP0 TP0] Decode batch. #running-req: 1, #full token: 340, full token usage: 0.00, #swa token: 340, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:18:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 425, full token usage: 0.00, #swa token: 425, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.91, #queue-req: 0, 
[2025-07-28 00:18:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 380, full token usage: 0.00, #swa token: 380, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:18:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 465, full token usage: 0.00, #swa token: 465, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.89, #queue-req: 0, 
[2025-07-28 00:18:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 420, full token usage: 0.00, #swa token: 420, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:18:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 505, full token usage: 0.00, #swa token: 505, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.91, #queue-req: 0, 
[2025-07-28 00:18:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 460, full token usage: 0.00, #swa token: 460, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:18:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 545, full token usage: 0.00, #swa token: 545, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:18:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 500, full token usage: 0.00, #swa token: 500, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:18:14 DP1 TP0] Decode batch. #running-req: 1, #full token: 585, full token usage: 0.00, #swa token: 585, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.78, #queue-req: 0, 
[2025-07-28 00:18:14 DP0 TP0] Decode batch. #running-req: 1, #full token: 540, full token usage: 0.00, #swa token: 540, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.78, #queue-req: 0, 
[2025-07-28 00:18:15 DP1 TP0] Decode batch. #running-req: 1, #full token: 625, full token usage: 0.00, #swa token: 625, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.77, #queue-req: 0, 
[2025-07-28 00:18:15 DP0 TP0] Decode batch. #running-req: 1, #full token: 580, full token usage: 0.00, #swa token: 580, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.73, #queue-req: 0, 
[2025-07-28 00:18:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 665, full token usage: 0.00, #swa token: 665, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.73, #queue-req: 0, 
[2025-07-28 00:18:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 620, full token usage: 0.00, #swa token: 620, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.71, #queue-req: 0, 
[2025-07-28 00:18:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 705, full token usage: 0.00, #swa token: 705, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.67, #queue-req: 0, 
[2025-07-28 00:18:17 DP0 TP0] Decode batch. #running-req: 1, #full token: 660, full token usage: 0.00, #swa token: 660, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.67, #queue-req: 0, 
[2025-07-28 00:18:17] INFO:     127.0.0.1:48344 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:18:17 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 117, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:18:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 700, full token usage: 0.00, #swa token: 700, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.61, #queue-req: 0, 
[2025-07-28 00:18:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 228, full token usage: 0.00, #swa token: 228, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.44, #queue-req: 0, 
[2025-07-28 00:18:19 DP0 TP0] Decode batch. #running-req: 1, #full token: 740, full token usage: 0.00, #swa token: 740, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.56, #queue-req: 0, 
[2025-07-28 00:18:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 268, full token usage: 0.00, #swa token: 268, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.96, #queue-req: 0, 
[2025-07-28 00:18:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 780, full token usage: 0.00, #swa token: 780, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.53, #queue-req: 0, 
[2025-07-28 00:18:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 308, full token usage: 0.00, #swa token: 308, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:18:20] INFO:     127.0.0.1:48356 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:18:20 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 103, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:18:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 348, full token usage: 0.00, #swa token: 348, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.93, #queue-req: 0, 
[2025-07-28 00:18:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 206, full token usage: 0.00, #swa token: 206, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.33, #queue-req: 0, 
[2025-07-28 00:18:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 388, full token usage: 0.00, #swa token: 388, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.91, #queue-req: 0, 
[2025-07-28 00:18:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 246, full token usage: 0.00, #swa token: 246, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:18:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 428, full token usage: 0.00, #swa token: 428, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.93, #queue-req: 0, 
[2025-07-28 00:18:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 286, full token usage: 0.00, #swa token: 286, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.88, #queue-req: 0, 
[2025-07-28 00:18:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 468, full token usage: 0.00, #swa token: 468, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:18:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 326, full token usage: 0.00, #swa token: 326, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:18:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 508, full token usage: 0.00, #swa token: 508, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:18:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 366, full token usage: 0.00, #swa token: 366, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:18:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 548, full token usage: 0.00, #swa token: 548, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.83, #queue-req: 0, 
[2025-07-28 00:18:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 406, full token usage: 0.00, #swa token: 406, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:18:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 588, full token usage: 0.00, #swa token: 588, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.82, #queue-req: 0, 
[2025-07-28 00:18:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 446, full token usage: 0.00, #swa token: 446, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:18:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 628, full token usage: 0.00, #swa token: 628, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.77, #queue-req: 0, 
[2025-07-28 00:18:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 486, full token usage: 0.00, #swa token: 486, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.84, #queue-req: 0, 
[2025-07-28 00:18:28] INFO:     127.0.0.1:53192 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:18:28 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 92, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:18:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 526, full token usage: 0.00, #swa token: 526, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.83, #queue-req: 0, 
[2025-07-28 00:18:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 222, full token usage: 0.00, #swa token: 222, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.71, #queue-req: 0, 
[2025-07-28 00:18:29] INFO:     127.0.0.1:53196 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:18:29 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 122, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:18:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 262, full token usage: 0.00, #swa token: 262, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.96, #queue-req: 0, 
[2025-07-28 00:18:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 230, full token usage: 0.00, #swa token: 230, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.65, #queue-req: 0, 
[2025-07-28 00:18:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 302, full token usage: 0.00, #swa token: 302, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.91, #queue-req: 0, 
[2025-07-28 00:18:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 270, full token usage: 0.00, #swa token: 270, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.88, #queue-req: 0, 
[2025-07-28 00:18:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 342, full token usage: 0.00, #swa token: 342, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.93, #queue-req: 0, 
[2025-07-28 00:18:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 310, full token usage: 0.00, #swa token: 310, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:18:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 382, full token usage: 0.00, #swa token: 382, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.93, #queue-req: 0, 
[2025-07-28 00:18:33 DP0 TP0] Decode batch. #running-req: 1, #full token: 350, full token usage: 0.00, #swa token: 350, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.88, #queue-req: 0, 
[2025-07-28 00:18:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 422, full token usage: 0.00, #swa token: 422, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.95, #queue-req: 0, 
[2025-07-28 00:18:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 390, full token usage: 0.00, #swa token: 390, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.41, #queue-req: 0, 
[2025-07-28 00:18:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 462, full token usage: 0.00, #swa token: 462, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:18:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 430, full token usage: 0.00, #swa token: 430, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.88, #queue-req: 0, 
[2025-07-28 00:18:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 502, full token usage: 0.00, #swa token: 502, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:18:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 470, full token usage: 0.00, #swa token: 470, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:18:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 542, full token usage: 0.00, #swa token: 542, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:18:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 510, full token usage: 0.00, #swa token: 510, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:18:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 582, full token usage: 0.00, #swa token: 582, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.83, #queue-req: 0, 
[2025-07-28 00:18:37 DP0 TP0] Decode batch. #running-req: 1, #full token: 550, full token usage: 0.00, #swa token: 550, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.81, #queue-req: 0, 
[2025-07-28 00:18:38] INFO:     127.0.0.1:49888 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:18:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 622, full token usage: 0.00, #swa token: 622, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.79, #queue-req: 0, 
[2025-07-28 00:18:38 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 162, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:18:39] INFO:     127.0.0.1:49884 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:18:39 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 148, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:18:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 250, full token usage: 0.00, #swa token: 250, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 21.71, #queue-req: 0, 
[2025-07-28 00:18:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 301, full token usage: 0.00, #swa token: 301, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 57.30, #queue-req: 0, 
[2025-07-28 00:18:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 290, full token usage: 0.00, #swa token: 290, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:18:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 341, full token usage: 0.00, #swa token: 341, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.95, #queue-req: 0, 
[2025-07-28 00:18:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 330, full token usage: 0.00, #swa token: 330, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:18:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 381, full token usage: 0.00, #swa token: 381, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:18:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 370, full token usage: 0.00, #swa token: 370, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.88, #queue-req: 0, 
[2025-07-28 00:18:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 421, full token usage: 0.00, #swa token: 421, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:18:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 410, full token usage: 0.00, #swa token: 410, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:18:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 461, full token usage: 0.00, #swa token: 461, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.91, #queue-req: 0, 
[2025-07-28 00:18:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 450, full token usage: 0.00, #swa token: 450, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:18:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 501, full token usage: 0.00, #swa token: 501, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.90, #queue-req: 0, 
[2025-07-28 00:18:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 490, full token usage: 0.00, #swa token: 490, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:18:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 541, full token usage: 0.00, #swa token: 541, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:18:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 530, full token usage: 0.00, #swa token: 530, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.82, #queue-req: 0, 
[2025-07-28 00:18:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 581, full token usage: 0.00, #swa token: 581, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.81, #queue-req: 0, 
[2025-07-28 00:18:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 570, full token usage: 0.00, #swa token: 570, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.77, #queue-req: 0, 
[2025-07-28 00:18:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 621, full token usage: 0.00, #swa token: 621, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.78, #queue-req: 0, 
[2025-07-28 00:18:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 610, full token usage: 0.00, #swa token: 610, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.74, #queue-req: 0, 
[2025-07-28 00:18:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 661, full token usage: 0.00, #swa token: 661, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.75, #queue-req: 0, 
[2025-07-28 00:18:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 650, full token usage: 0.00, #swa token: 650, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.69, #queue-req: 0, 
[2025-07-28 00:18:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 701, full token usage: 0.00, #swa token: 701, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.69, #queue-req: 0, 
[2025-07-28 00:18:50 DP0 TP0] Decode batch. #running-req: 1, #full token: 690, full token usage: 0.00, #swa token: 690, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.65, #queue-req: 0, 
[2025-07-28 00:18:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 741, full token usage: 0.00, #swa token: 741, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.64, #queue-req: 0, 
[2025-07-28 00:18:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 730, full token usage: 0.00, #swa token: 730, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.61, #queue-req: 0, 
[2025-07-28 00:18:51 DP1 TP0] Decode batch. #running-req: 1, #full token: 781, full token usage: 0.00, #swa token: 781, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.60, #queue-req: 0, 
[2025-07-28 00:18:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 770, full token usage: 0.00, #swa token: 770, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.55, #queue-req: 0, 
[2025-07-28 00:18:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 821, full token usage: 0.00, #swa token: 821, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.57, #queue-req: 0, 
[2025-07-28 00:18:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 810, full token usage: 0.00, #swa token: 810, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.52, #queue-req: 0, 
[2025-07-28 00:18:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 861, full token usage: 0.00, #swa token: 861, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.55, #queue-req: 0, 
[2025-07-28 00:18:54] INFO:     127.0.0.1:43598 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:18:54 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 117, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:18:54 DP1 TP0] Decode batch. #running-req: 2, #full token: 1042, full token usage: 0.00, #swa token: 1042, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 55.48, #queue-req: 0, 
[2025-07-28 00:18:55 DP1 TP0] Decode batch. #running-req: 2, #full token: 1122, full token usage: 0.00, #swa token: 1122, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 77.11, #queue-req: 0, 
[2025-07-28 00:18:56 DP1 TP0] Decode batch. #running-req: 2, #full token: 1202, full token usage: 0.00, #swa token: 1202, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 77.10, #queue-req: 0, 
[2025-07-28 00:18:56] INFO:     127.0.0.1:43588 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:18:56 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 133, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:18:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 250, full token usage: 0.00, #swa token: 250, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 10.09, #queue-req: 0, 
[2025-07-28 00:18:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 360, full token usage: 0.00, #swa token: 360, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 43.81, #queue-req: 0, 
[2025-07-28 00:18:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 290, full token usage: 0.00, #swa token: 290, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:18:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 400, full token usage: 0.00, #swa token: 400, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.93, #queue-req: 0, 
[2025-07-28 00:18:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 330, full token usage: 0.00, #swa token: 330, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:18:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 440, full token usage: 0.00, #swa token: 440, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:19:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 370, full token usage: 0.00, #swa token: 370, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:19:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 480, full token usage: 0.00, #swa token: 480, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.93, #queue-req: 0, 
[2025-07-28 00:19:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 410, full token usage: 0.00, #swa token: 410, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.38, #queue-req: 0, 
[2025-07-28 00:19:01 DP1 TP0] Decode batch. #running-req: 1, #full token: 520, full token usage: 0.00, #swa token: 520, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.90, #queue-req: 0, 
[2025-07-28 00:19:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 450, full token usage: 0.00, #swa token: 450, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:19:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 560, full token usage: 0.00, #swa token: 560, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.84, #queue-req: 0, 
[2025-07-28 00:19:03 DP0 TP0] Decode batch. #running-req: 1, #full token: 490, full token usage: 0.00, #swa token: 490, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.77, #queue-req: 0, 
[2025-07-28 00:19:03 DP1 TP0] Decode batch. #running-req: 1, #full token: 600, full token usage: 0.00, #swa token: 600, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.77, #queue-req: 0, 
[2025-07-28 00:19:04 DP0 TP0] Decode batch. #running-req: 1, #full token: 530, full token usage: 0.00, #swa token: 530, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.81, #queue-req: 0, 
[2025-07-28 00:19:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 640, full token usage: 0.00, #swa token: 640, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.77, #queue-req: 0, 
[2025-07-28 00:19:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 570, full token usage: 0.00, #swa token: 570, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.76, #queue-req: 0, 
[2025-07-28 00:19:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 680, full token usage: 0.00, #swa token: 680, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.73, #queue-req: 0, 
[2025-07-28 00:19:06 DP0 TP0] Decode batch. #running-req: 1, #full token: 610, full token usage: 0.00, #swa token: 610, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.71, #queue-req: 0, 
[2025-07-28 00:19:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 720, full token usage: 0.00, #swa token: 720, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.67, #queue-req: 0, 
[2025-07-28 00:19:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 650, full token usage: 0.00, #swa token: 650, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.69, #queue-req: 0, 
[2025-07-28 00:19:07 DP1 TP0] Decode batch. #running-req: 1, #full token: 760, full token usage: 0.00, #swa token: 760, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.62, #queue-req: 0, 
[2025-07-28 00:19:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 690, full token usage: 0.00, #swa token: 690, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.62, #queue-req: 0, 
[2025-07-28 00:19:08] INFO:     127.0.0.1:46254 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:19:08 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 201, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:19:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 311, full token usage: 0.00, #swa token: 311, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.42, #queue-req: 0, 
[2025-07-28 00:19:09 DP0 TP0] Decode batch. #running-req: 1, #full token: 730, full token usage: 0.00, #swa token: 730, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.58, #queue-req: 0, 
[2025-07-28 00:19:09 DP1 TP0] Decode batch. #running-req: 1, #full token: 351, full token usage: 0.00, #swa token: 351, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:19:10] INFO:     127.0.0.1:46264 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:19:10 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 90, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:19:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 193, full token usage: 0.00, #swa token: 193, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.38, #queue-req: 0, 
[2025-07-28 00:19:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 391, full token usage: 0.00, #swa token: 391, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.95, #queue-req: 0, 
[2025-07-28 00:19:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 233, full token usage: 0.00, #swa token: 233, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.90, #queue-req: 0, 
[2025-07-28 00:19:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 431, full token usage: 0.00, #swa token: 431, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.95, #queue-req: 0, 
[2025-07-28 00:19:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 273, full token usage: 0.00, #swa token: 273, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:19:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 471, full token usage: 0.00, #swa token: 471, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:19:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 313, full token usage: 0.00, #swa token: 313, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.78, #queue-req: 0, 
[2025-07-28 00:19:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 511, full token usage: 0.00, #swa token: 511, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.93, #queue-req: 0, 
[2025-07-28 00:19:14 DP0 TP0] Decode batch. #running-req: 1, #full token: 353, full token usage: 0.00, #swa token: 353, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.80, #queue-req: 0, 
[2025-07-28 00:19:14 DP1 TP0] Decode batch. #running-req: 1, #full token: 551, full token usage: 0.00, #swa token: 551, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:19:15 DP0 TP0] Decode batch. #running-req: 1, #full token: 393, full token usage: 0.00, #swa token: 393, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.80, #queue-req: 0, 
[2025-07-28 00:19:15 DP1 TP0] Decode batch. #running-req: 1, #full token: 591, full token usage: 0.00, #swa token: 591, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.83, #queue-req: 0, 
[2025-07-28 00:19:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 433, full token usage: 0.00, #swa token: 433, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.80, #queue-req: 0, 
[2025-07-28 00:19:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 631, full token usage: 0.00, #swa token: 631, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.79, #queue-req: 0, 
[2025-07-28 00:19:17 DP0 TP0] Decode batch. #running-req: 1, #full token: 473, full token usage: 0.00, #swa token: 473, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.79, #queue-req: 0, 
[2025-07-28 00:19:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 671, full token usage: 0.00, #swa token: 671, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.73, #queue-req: 0, 
[2025-07-28 00:19:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 513, full token usage: 0.00, #swa token: 513, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.79, #queue-req: 0, 
[2025-07-28 00:19:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 711, full token usage: 0.00, #swa token: 711, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.68, #queue-req: 0, 
[2025-07-28 00:19:19 DP0 TP0] Decode batch. #running-req: 1, #full token: 553, full token usage: 0.00, #swa token: 553, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.69, #queue-req: 0, 
[2025-07-28 00:19:19] INFO:     127.0.0.1:41324 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:19:19 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 158, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:19:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 264, full token usage: 0.00, #swa token: 264, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.26, #queue-req: 0, 
[2025-07-28 00:19:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 593, full token usage: 0.00, #swa token: 593, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.68, #queue-req: 0, 
[2025-07-28 00:19:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 304, full token usage: 0.00, #swa token: 304, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:19:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 633, full token usage: 0.00, #swa token: 633, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.65, #queue-req: 0, 
[2025-07-28 00:19:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 344, full token usage: 0.00, #swa token: 344, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:19:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 673, full token usage: 0.00, #swa token: 673, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.61, #queue-req: 0, 
[2025-07-28 00:19:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 384, full token usage: 0.00, #swa token: 384, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.93, #queue-req: 0, 
[2025-07-28 00:19:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 713, full token usage: 0.00, #swa token: 713, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.55, #queue-req: 0, 
[2025-07-28 00:19:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 424, full token usage: 0.00, #swa token: 424, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.93, #queue-req: 0, 
[2025-07-28 00:19:23] INFO:     127.0.0.1:41326 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:19:23 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 134, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:19:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 252, full token usage: 0.00, #swa token: 252, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.55, #queue-req: 0, 
[2025-07-28 00:19:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 464, full token usage: 0.00, #swa token: 464, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.93, #queue-req: 0, 
[2025-07-28 00:19:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 292, full token usage: 0.00, #swa token: 292, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.82, #queue-req: 0, 
[2025-07-28 00:19:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 504, full token usage: 0.00, #swa token: 504, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:19:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 332, full token usage: 0.00, #swa token: 332, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.82, #queue-req: 0, 
[2025-07-28 00:19:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 544, full token usage: 0.00, #swa token: 544, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:19:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 372, full token usage: 0.00, #swa token: 372, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.81, #queue-req: 0, 
[2025-07-28 00:19:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 584, full token usage: 0.00, #swa token: 584, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.82, #queue-req: 0, 
[2025-07-28 00:19:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 412, full token usage: 0.00, #swa token: 412, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.80, #queue-req: 0, 
[2025-07-28 00:19:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 624, full token usage: 0.00, #swa token: 624, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.77, #queue-req: 0, 
[2025-07-28 00:19:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 452, full token usage: 0.00, #swa token: 452, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.80, #queue-req: 0, 
[2025-07-28 00:19:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 664, full token usage: 0.00, #swa token: 664, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.74, #queue-req: 0, 
[2025-07-28 00:19:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 492, full token usage: 0.00, #swa token: 492, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.83, #queue-req: 0, 
[2025-07-28 00:19:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 704, full token usage: 0.00, #swa token: 704, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.64, #queue-req: 0, 
[2025-07-28 00:19:31] INFO:     127.0.0.1:49018 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:19:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 532, full token usage: 0.00, #swa token: 532, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.80, #queue-req: 0, 
[2025-07-28 00:19:31 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:19:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 256, full token usage: 0.00, #swa token: 256, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.50, #queue-req: 0, 
[2025-07-28 00:19:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 572, full token usage: 0.00, #swa token: 572, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.76, #queue-req: 0, 
[2025-07-28 00:19:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 296, full token usage: 0.00, #swa token: 296, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:19:33 DP0 TP0] Decode batch. #running-req: 1, #full token: 612, full token usage: 0.00, #swa token: 612, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.73, #queue-req: 0, 
[2025-07-28 00:19:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 336, full token usage: 0.00, #swa token: 336, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:19:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 652, full token usage: 0.00, #swa token: 652, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.68, #queue-req: 0, 
[2025-07-28 00:19:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 376, full token usage: 0.00, #swa token: 376, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:19:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 692, full token usage: 0.00, #swa token: 692, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.14, #queue-req: 0, 
[2025-07-28 00:19:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 416, full token usage: 0.00, #swa token: 416, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.91, #queue-req: 0, 
[2025-07-28 00:19:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 732, full token usage: 0.00, #swa token: 732, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.53, #queue-req: 0, 
[2025-07-28 00:19:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 456, full token usage: 0.00, #swa token: 456, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:19:37 DP0 TP0] Decode batch. #running-req: 1, #full token: 772, full token usage: 0.00, #swa token: 772, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.54, #queue-req: 0, 
[2025-07-28 00:19:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 496, full token usage: 0.00, #swa token: 496, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.90, #queue-req: 0, 
[2025-07-28 00:19:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 812, full token usage: 0.00, #swa token: 812, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.52, #queue-req: 0, 
[2025-07-28 00:19:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 536, full token usage: 0.00, #swa token: 536, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:19:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 852, full token usage: 0.00, #swa token: 852, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.50, #queue-req: 0, 
[2025-07-28 00:19:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 576, full token usage: 0.00, #swa token: 576, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.82, #queue-req: 0, 
[2025-07-28 00:19:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 892, full token usage: 0.00, #swa token: 892, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.49, #queue-req: 0, 
[2025-07-28 00:19:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 616, full token usage: 0.00, #swa token: 616, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.77, #queue-req: 0, 
[2025-07-28 00:19:40] INFO:     127.0.0.1:59640 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:19:40 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:19:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 259, full token usage: 0.00, #swa token: 259, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.45, #queue-req: 0, 
[2025-07-28 00:19:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 656, full token usage: 0.00, #swa token: 656, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.74, #queue-req: 0, 
[2025-07-28 00:19:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 299, full token usage: 0.00, #swa token: 299, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:19:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 696, full token usage: 0.00, #swa token: 696, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.68, #queue-req: 0, 
[2025-07-28 00:19:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 339, full token usage: 0.00, #swa token: 339, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:19:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 736, full token usage: 0.00, #swa token: 736, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.65, #queue-req: 0, 
[2025-07-28 00:19:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 379, full token usage: 0.00, #swa token: 379, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:19:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 776, full token usage: 0.00, #swa token: 776, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.60, #queue-req: 0, 
[2025-07-28 00:19:44] INFO:     127.0.0.1:59650 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:19:44 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 102, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:19:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 419, full token usage: 0.00, #swa token: 419, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:19:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 231, full token usage: 0.00, #swa token: 231, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.64, #queue-req: 0, 
[2025-07-28 00:19:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 459, full token usage: 0.00, #swa token: 459, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:19:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 271, full token usage: 0.00, #swa token: 271, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 41.00, #queue-req: 0, 
[2025-07-28 00:19:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 499, full token usage: 0.00, #swa token: 499, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.84, #queue-req: 0, 
[2025-07-28 00:19:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 311, full token usage: 0.00, #swa token: 311, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:19:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 539, full token usage: 0.00, #swa token: 539, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.79, #queue-req: 0, 
[2025-07-28 00:19:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 351, full token usage: 0.00, #swa token: 351, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.93, #queue-req: 0, 
[2025-07-28 00:19:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 579, full token usage: 0.00, #swa token: 579, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.75, #queue-req: 0, 
[2025-07-28 00:19:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 391, full token usage: 0.00, #swa token: 391, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.95, #queue-req: 0, 
[2025-07-28 00:19:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 619, full token usage: 0.00, #swa token: 619, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.71, #queue-req: 0, 
[2025-07-28 00:19:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 431, full token usage: 0.00, #swa token: 431, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:19:50 DP0 TP0] Decode batch. #running-req: 1, #full token: 659, full token usage: 0.00, #swa token: 659, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.67, #queue-req: 0, 
[2025-07-28 00:19:51 DP1 TP0] Decode batch. #running-req: 1, #full token: 471, full token usage: 0.00, #swa token: 471, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:19:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 699, full token usage: 0.00, #swa token: 699, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.61, #queue-req: 0, 
[2025-07-28 00:19:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 511, full token usage: 0.00, #swa token: 511, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:19:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 739, full token usage: 0.00, #swa token: 739, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.58, #queue-req: 0, 
[2025-07-28 00:19:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 551, full token usage: 0.00, #swa token: 551, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.84, #queue-req: 0, 
[2025-07-28 00:19:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 779, full token usage: 0.00, #swa token: 779, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.52, #queue-req: 0, 
[2025-07-28 00:19:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 591, full token usage: 0.00, #swa token: 591, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.82, #queue-req: 0, 
[2025-07-28 00:19:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 819, full token usage: 0.00, #swa token: 819, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.51, #queue-req: 0, 
[2025-07-28 00:19:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 631, full token usage: 0.00, #swa token: 631, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.78, #queue-req: 0, 
[2025-07-28 00:19:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 859, full token usage: 0.00, #swa token: 859, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.51, #queue-req: 0, 
[2025-07-28 00:19:55] INFO:     127.0.0.1:35194 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:19:55 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 149, #cached-token: 98, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:19:56 DP1 TP0] Decode batch. #running-req: 1, #full token: 671, full token usage: 0.00, #swa token: 671, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.74, #queue-req: 0, 
[2025-07-28 00:19:56 DP0 TP0] Decode batch. #running-req: 1, #full token: 286, full token usage: 0.00, #swa token: 286, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.62, #queue-req: 0, 
[2025-07-28 00:19:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 711, full token usage: 0.00, #swa token: 711, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.67, #queue-req: 0, 
[2025-07-28 00:19:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 326, full token usage: 0.00, #swa token: 326, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.88, #queue-req: 0, 
[2025-07-28 00:19:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 751, full token usage: 0.00, #swa token: 751, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.62, #queue-req: 0, 
[2025-07-28 00:19:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 366, full token usage: 0.00, #swa token: 366, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:19:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 791, full token usage: 0.00, #swa token: 791, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.59, #queue-req: 0, 
[2025-07-28 00:19:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 406, full token usage: 0.00, #swa token: 406, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:20:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 831, full token usage: 0.00, #swa token: 831, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.56, #queue-req: 0, 
[2025-07-28 00:20:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 446, full token usage: 0.00, #swa token: 446, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:20:01 DP1 TP0] Decode batch. #running-req: 1, #full token: 871, full token usage: 0.00, #swa token: 871, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.57, #queue-req: 0, 
[2025-07-28 00:20:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 486, full token usage: 0.00, #swa token: 486, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.84, #queue-req: 0, 
[2025-07-28 00:20:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 911, full token usage: 0.00, #swa token: 911, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.57, #queue-req: 0, 
[2025-07-28 00:20:02] INFO:     127.0.0.1:38950 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:20:02 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 83, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:20:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 526, full token usage: 0.00, #swa token: 526, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.34, #queue-req: 0, 
[2025-07-28 00:20:03 DP1 TP0] Decode batch. #running-req: 1, #full token: 205, full token usage: 0.00, #swa token: 205, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.57, #queue-req: 0, 
[2025-07-28 00:20:03] INFO:     127.0.0.1:60624 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:20:03 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 155, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:20:03 DP0 TP0] Decode batch. #running-req: 1, #full token: 279, full token usage: 0.00, #swa token: 279, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.58, #queue-req: 0, 
[2025-07-28 00:20:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 245, full token usage: 0.00, #swa token: 245, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 41.02, #queue-req: 0, 
[2025-07-28 00:20:04 DP0 TP0] Decode batch. #running-req: 1, #full token: 319, full token usage: 0.00, #swa token: 319, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:20:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 285, full token usage: 0.00, #swa token: 285, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.95, #queue-req: 0, 
[2025-07-28 00:20:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 359, full token usage: 0.00, #swa token: 359, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:20:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 325, full token usage: 0.00, #swa token: 325, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.91, #queue-req: 0, 
[2025-07-28 00:20:06 DP0 TP0] Decode batch. #running-req: 1, #full token: 399, full token usage: 0.00, #swa token: 399, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:20:07 DP1 TP0] Decode batch. #running-req: 1, #full token: 365, full token usage: 0.00, #swa token: 365, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:20:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 439, full token usage: 0.00, #swa token: 439, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:20:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 405, full token usage: 0.00, #swa token: 405, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.91, #queue-req: 0, 
[2025-07-28 00:20:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 479, full token usage: 0.00, #swa token: 479, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.77, #queue-req: 0, 
[2025-07-28 00:20:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 445, full token usage: 0.00, #swa token: 445, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:20:09 DP0 TP0] Decode batch. #running-req: 1, #full token: 519, full token usage: 0.00, #swa token: 519, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.84, #queue-req: 0, 
[2025-07-28 00:20:09 DP1 TP0] Decode batch. #running-req: 1, #full token: 485, full token usage: 0.00, #swa token: 485, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.88, #queue-req: 0, 
[2025-07-28 00:20:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 559, full token usage: 0.00, #swa token: 559, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.78, #queue-req: 0, 
[2025-07-28 00:20:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 525, full token usage: 0.00, #swa token: 525, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.89, #queue-req: 0, 
[2025-07-28 00:20:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 599, full token usage: 0.00, #swa token: 599, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.74, #queue-req: 0, 
[2025-07-28 00:20:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 565, full token usage: 0.00, #swa token: 565, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.83, #queue-req: 0, 
[2025-07-28 00:20:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 639, full token usage: 0.00, #swa token: 639, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.70, #queue-req: 0, 
[2025-07-28 00:20:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 605, full token usage: 0.00, #swa token: 605, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.79, #queue-req: 0, 
[2025-07-28 00:20:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 679, full token usage: 0.00, #swa token: 679, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.65, #queue-req: 0, 
[2025-07-28 00:20:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 645, full token usage: 0.00, #swa token: 645, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.76, #queue-req: 0, 
[2025-07-28 00:20:14 DP0 TP0] Decode batch. #running-req: 1, #full token: 719, full token usage: 0.00, #swa token: 719, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.61, #queue-req: 0, 
[2025-07-28 00:20:14 DP1 TP0] Decode batch. #running-req: 1, #full token: 685, full token usage: 0.00, #swa token: 685, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.72, #queue-req: 0, 
[2025-07-28 00:20:15 DP0 TP0] Decode batch. #running-req: 1, #full token: 759, full token usage: 0.00, #swa token: 759, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.57, #queue-req: 0, 
[2025-07-28 00:20:15 DP1 TP0] Decode batch. #running-req: 1, #full token: 725, full token usage: 0.00, #swa token: 725, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.67, #queue-req: 0, 
[2025-07-28 00:20:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 799, full token usage: 0.00, #swa token: 799, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.51, #queue-req: 0, 
[2025-07-28 00:20:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 765, full token usage: 0.00, #swa token: 765, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.61, #queue-req: 0, 
[2025-07-28 00:20:17 DP0 TP0] Decode batch. #running-req: 1, #full token: 839, full token usage: 0.00, #swa token: 839, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.50, #queue-req: 0, 
[2025-07-28 00:20:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 805, full token usage: 0.00, #swa token: 805, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.57, #queue-req: 0, 
[2025-07-28 00:20:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 879, full token usage: 0.00, #swa token: 879, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.49, #queue-req: 0, 
[2025-07-28 00:20:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 845, full token usage: 0.00, #swa token: 845, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.57, #queue-req: 0, 
[2025-07-28 00:20:19 DP0 TP0] Decode batch. #running-req: 1, #full token: 919, full token usage: 0.00, #swa token: 919, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.50, #queue-req: 0, 
[2025-07-28 00:20:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 885, full token usage: 0.00, #swa token: 885, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.56, #queue-req: 0, 
[2025-07-28 00:20:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 959, full token usage: 0.00, #swa token: 959, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.49, #queue-req: 0, 
[2025-07-28 00:20:20] INFO:     127.0.0.1:36350 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:20:20 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 91, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:20:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 195, full token usage: 0.00, #swa token: 195, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.46, #queue-req: 0, 
[2025-07-28 00:20:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 999, full token usage: 0.00, #swa token: 999, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.48, #queue-req: 0, 
[2025-07-28 00:20:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 235, full token usage: 0.00, #swa token: 235, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 41.04, #queue-req: 0, 
[2025-07-28 00:20:22] INFO:     127.0.0.1:36362 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:20:22 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 179, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:20:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 283, full token usage: 0.00, #swa token: 283, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.32, #queue-req: 0, 
[2025-07-28 00:20:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 275, full token usage: 0.00, #swa token: 275, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.98, #queue-req: 0, 
[2025-07-28 00:20:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 323, full token usage: 0.00, #swa token: 323, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:20:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 315, full token usage: 0.00, #swa token: 315, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.95, #queue-req: 0, 
[2025-07-28 00:20:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 363, full token usage: 0.00, #swa token: 363, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.84, #queue-req: 0, 
[2025-07-28 00:20:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 355, full token usage: 0.00, #swa token: 355, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:20:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 403, full token usage: 0.00, #swa token: 403, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:20:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 395, full token usage: 0.00, #swa token: 395, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:20:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 443, full token usage: 0.00, #swa token: 443, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:20:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 435, full token usage: 0.00, #swa token: 435, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:20:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 483, full token usage: 0.00, #swa token: 483, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.83, #queue-req: 0, 
[2025-07-28 00:20:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 475, full token usage: 0.00, #swa token: 475, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.93, #queue-req: 0, 
[2025-07-28 00:20:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 523, full token usage: 0.00, #swa token: 523, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.82, #queue-req: 0, 
[2025-07-28 00:20:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 515, full token usage: 0.00, #swa token: 515, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.91, #queue-req: 0, 
[2025-07-28 00:20:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 563, full token usage: 0.00, #swa token: 563, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.76, #queue-req: 0, 
[2025-07-28 00:20:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 555, full token usage: 0.00, #swa token: 555, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.84, #queue-req: 0, 
[2025-07-28 00:20:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 603, full token usage: 0.00, #swa token: 603, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.73, #queue-req: 0, 
[2025-07-28 00:20:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 595, full token usage: 0.00, #swa token: 595, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.81, #queue-req: 0, 
[2025-07-28 00:20:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 643, full token usage: 0.00, #swa token: 643, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.69, #queue-req: 0, 
[2025-07-28 00:20:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 635, full token usage: 0.00, #swa token: 635, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.78, #queue-req: 0, 
[2025-07-28 00:20:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 683, full token usage: 0.00, #swa token: 683, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.63, #queue-req: 0, 
[2025-07-28 00:20:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 675, full token usage: 0.00, #swa token: 675, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.73, #queue-req: 0, 
[2025-07-28 00:20:33 DP0 TP0] Decode batch. #running-req: 1, #full token: 723, full token usage: 0.00, #swa token: 723, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.59, #queue-req: 0, 
[2025-07-28 00:20:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 715, full token usage: 0.00, #swa token: 715, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.68, #queue-req: 0, 
[2025-07-28 00:20:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 763, full token usage: 0.00, #swa token: 763, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.52, #queue-req: 0, 
[2025-07-28 00:20:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 755, full token usage: 0.00, #swa token: 755, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.63, #queue-req: 0, 
[2025-07-28 00:20:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 803, full token usage: 0.00, #swa token: 803, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.50, #queue-req: 0, 
[2025-07-28 00:20:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 795, full token usage: 0.00, #swa token: 795, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.59, #queue-req: 0, 
[2025-07-28 00:20:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 843, full token usage: 0.00, #swa token: 843, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 39.94, #queue-req: 0, 
[2025-07-28 00:20:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 835, full token usage: 0.00, #swa token: 835, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.51, #queue-req: 0, 
[2025-07-28 00:20:37 DP0 TP0] Decode batch. #running-req: 1, #full token: 883, full token usage: 0.00, #swa token: 883, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.50, #queue-req: 0, 
[2025-07-28 00:20:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 875, full token usage: 0.00, #swa token: 875, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.57, #queue-req: 0, 
[2025-07-28 00:20:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 923, full token usage: 0.00, #swa token: 923, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.52, #queue-req: 0, 
[2025-07-28 00:20:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 915, full token usage: 0.00, #swa token: 915, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.58, #queue-req: 0, 
[2025-07-28 00:20:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 963, full token usage: 0.00, #swa token: 963, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.50, #queue-req: 0, 
[2025-07-28 00:20:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 955, full token usage: 0.00, #swa token: 955, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.57, #queue-req: 0, 
[2025-07-28 00:20:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 1003, full token usage: 0.00, #swa token: 1003, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.51, #queue-req: 0, 
[2025-07-28 00:20:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 995, full token usage: 0.00, #swa token: 995, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.56, #queue-req: 0, 
[2025-07-28 00:20:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 1043, full token usage: 0.00, #swa token: 1043, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.49, #queue-req: 0, 
[2025-07-28 00:20:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 1035, full token usage: 0.00, #swa token: 1035, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.53, #queue-req: 0, 
[2025-07-28 00:20:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 1083, full token usage: 0.00, #swa token: 1083, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.47, #queue-req: 0, 
[2025-07-28 00:20:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 1075, full token usage: 0.00, #swa token: 1075, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.52, #queue-req: 0, 
[2025-07-28 00:20:43] INFO:     127.0.0.1:45564 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:20:43 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 183, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:20:43 DP1 TP0] Decode batch. #running-req: 2, #full token: 1305, full token usage: 0.00, #swa token: 1305, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.02, #queue-req: 0, 
[2025-07-28 00:20:44 DP1 TP0] Decode batch. #running-req: 2, #full token: 1385, full token usage: 0.00, #swa token: 1385, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 77.12, #queue-req: 0, 
[2025-07-28 00:20:45 DP1 TP0] Decode batch. #running-req: 2, #full token: 1465, full token usage: 0.00, #swa token: 1465, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 77.13, #queue-req: 0, 
[2025-07-28 00:20:45] INFO:     127.0.0.1:57096 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:20:46 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 96, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:20:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 195, full token usage: 0.00, #swa token: 195, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 10.64, #queue-req: 0, 
[2025-07-28 00:20:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 407, full token usage: 0.00, #swa token: 407, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 53.19, #queue-req: 0, 
[2025-07-28 00:20:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 235, full token usage: 0.00, #swa token: 235, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.27, #queue-req: 0, 
[2025-07-28 00:20:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 447, full token usage: 0.00, #swa token: 447, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:20:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 275, full token usage: 0.00, #swa token: 275, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.89, #queue-req: 0, 
[2025-07-28 00:20:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 487, full token usage: 0.00, #swa token: 487, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:20:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 315, full token usage: 0.00, #swa token: 315, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:20:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 527, full token usage: 0.00, #swa token: 527, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.91, #queue-req: 0, 
[2025-07-28 00:20:50 DP0 TP0] Decode batch. #running-req: 1, #full token: 355, full token usage: 0.00, #swa token: 355, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.88, #queue-req: 0, 
[2025-07-28 00:20:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 567, full token usage: 0.00, #swa token: 567, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.84, #queue-req: 0, 
[2025-07-28 00:20:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 395, full token usage: 0.00, #swa token: 395, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:20:51 DP1 TP0] Decode batch. #running-req: 1, #full token: 607, full token usage: 0.00, #swa token: 607, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.80, #queue-req: 0, 
[2025-07-28 00:20:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 435, full token usage: 0.00, #swa token: 435, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:20:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 647, full token usage: 0.00, #swa token: 647, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.77, #queue-req: 0, 
[2025-07-28 00:20:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 475, full token usage: 0.00, #swa token: 475, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:20:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 687, full token usage: 0.00, #swa token: 687, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.70, #queue-req: 0, 
[2025-07-28 00:20:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 515, full token usage: 0.00, #swa token: 515, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.84, #queue-req: 0, 
[2025-07-28 00:20:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 727, full token usage: 0.00, #swa token: 727, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.66, #queue-req: 0, 
[2025-07-28 00:20:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 555, full token usage: 0.00, #swa token: 555, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.78, #queue-req: 0, 
[2025-07-28 00:20:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 767, full token usage: 0.00, #swa token: 767, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.61, #queue-req: 0, 
[2025-07-28 00:20:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 595, full token usage: 0.00, #swa token: 595, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.75, #queue-req: 0, 
[2025-07-28 00:20:56 DP1 TP0] Decode batch. #running-req: 1, #full token: 807, full token usage: 0.00, #swa token: 807, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.57, #queue-req: 0, 
[2025-07-28 00:20:56 DP0 TP0] Decode batch. #running-req: 1, #full token: 635, full token usage: 0.00, #swa token: 635, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.71, #queue-req: 0, 
[2025-07-28 00:20:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 847, full token usage: 0.00, #swa token: 847, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.57, #queue-req: 0, 
[2025-07-28 00:20:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 675, full token usage: 0.00, #swa token: 675, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.65, #queue-req: 0, 
[2025-07-28 00:20:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 887, full token usage: 0.00, #swa token: 887, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.57, #queue-req: 0, 
[2025-07-28 00:20:58] INFO:     127.0.0.1:33762 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:20:58 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 156, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:20:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 715, full token usage: 0.00, #swa token: 715, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.58, #queue-req: 0, 
[2025-07-28 00:20:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 284, full token usage: 0.00, #swa token: 284, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.50, #queue-req: 0, 
[2025-07-28 00:20:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 755, full token usage: 0.00, #swa token: 755, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.56, #queue-req: 0, 
[2025-07-28 00:21:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 324, full token usage: 0.00, #swa token: 324, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.95, #queue-req: 0, 
[2025-07-28 00:21:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 795, full token usage: 0.00, #swa token: 795, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.52, #queue-req: 0, 
[2025-07-28 00:21:01] INFO:     127.0.0.1:33764 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:21:01 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:21:01 DP1 TP0] Decode batch. #running-req: 1, #full token: 364, full token usage: 0.00, #swa token: 364, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:21:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 260, full token usage: 0.00, #swa token: 260, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.48, #queue-req: 0, 
[2025-07-28 00:21:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 404, full token usage: 0.00, #swa token: 404, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.95, #queue-req: 0, 
[2025-07-28 00:21:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 300, full token usage: 0.00, #swa token: 300, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:21:03 DP1 TP0] Decode batch. #running-req: 1, #full token: 444, full token usage: 0.00, #swa token: 444, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.93, #queue-req: 0, 
[2025-07-28 00:21:03 DP0 TP0] Decode batch. #running-req: 1, #full token: 340, full token usage: 0.00, #swa token: 340, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.34, #queue-req: 0, 
[2025-07-28 00:21:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 484, full token usage: 0.00, #swa token: 484, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:21:04 DP0 TP0] Decode batch. #running-req: 1, #full token: 380, full token usage: 0.00, #swa token: 380, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:21:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 524, full token usage: 0.00, #swa token: 524, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.91, #queue-req: 0, 
[2025-07-28 00:21:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 420, full token usage: 0.00, #swa token: 420, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:21:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 564, full token usage: 0.00, #swa token: 564, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:21:06 DP0 TP0] Decode batch. #running-req: 1, #full token: 460, full token usage: 0.00, #swa token: 460, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:21:07 DP1 TP0] Decode batch. #running-req: 1, #full token: 604, full token usage: 0.00, #swa token: 604, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.82, #queue-req: 0, 
[2025-07-28 00:21:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 500, full token usage: 0.00, #swa token: 500, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.82, #queue-req: 0, 
[2025-07-28 00:21:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 644, full token usage: 0.00, #swa token: 644, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.78, #queue-req: 0, 
[2025-07-28 00:21:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 540, full token usage: 0.00, #swa token: 540, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.79, #queue-req: 0, 
[2025-07-28 00:21:09 DP1 TP0] Decode batch. #running-req: 1, #full token: 684, full token usage: 0.00, #swa token: 684, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.74, #queue-req: 0, 
[2025-07-28 00:21:09 DP0 TP0] Decode batch. #running-req: 1, #full token: 580, full token usage: 0.00, #swa token: 580, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.72, #queue-req: 0, 
[2025-07-28 00:21:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 724, full token usage: 0.00, #swa token: 724, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.67, #queue-req: 0, 
[2025-07-28 00:21:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 620, full token usage: 0.00, #swa token: 620, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.70, #queue-req: 0, 
[2025-07-28 00:21:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 764, full token usage: 0.00, #swa token: 764, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.63, #queue-req: 0, 
[2025-07-28 00:21:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 660, full token usage: 0.00, #swa token: 660, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.66, #queue-req: 0, 
[2025-07-28 00:21:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 804, full token usage: 0.00, #swa token: 804, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.57, #queue-req: 0, 
[2025-07-28 00:21:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 700, full token usage: 0.00, #swa token: 700, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.62, #queue-req: 0, 
[2025-07-28 00:21:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 844, full token usage: 0.00, #swa token: 844, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.58, #queue-req: 0, 
[2025-07-28 00:21:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 740, full token usage: 0.00, #swa token: 740, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.56, #queue-req: 0, 
[2025-07-28 00:21:14 DP1 TP0] Decode batch. #running-req: 1, #full token: 884, full token usage: 0.00, #swa token: 884, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.57, #queue-req: 0, 
[2025-07-28 00:21:14 DP0 TP0] Decode batch. #running-req: 1, #full token: 780, full token usage: 0.00, #swa token: 780, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.44, #queue-req: 0, 
[2025-07-28 00:21:15 DP1 TP0] Decode batch. #running-req: 1, #full token: 924, full token usage: 0.00, #swa token: 924, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.54, #queue-req: 0, 
[2025-07-28 00:21:15 DP0 TP0] Decode batch. #running-req: 1, #full token: 820, full token usage: 0.00, #swa token: 820, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.46, #queue-req: 0, 
[2025-07-28 00:21:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 964, full token usage: 0.00, #swa token: 964, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.58, #queue-req: 0, 
[2025-07-28 00:21:16] INFO:     127.0.0.1:54604 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:21:16 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 135, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:21:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 860, full token usage: 0.00, #swa token: 860, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.50, #queue-req: 0, 
[2025-07-28 00:21:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 268, full token usage: 0.00, #swa token: 268, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.76, #queue-req: 0, 
[2025-07-28 00:21:17 DP0 TP0] Decode batch. #running-req: 1, #full token: 900, full token usage: 0.00, #swa token: 900, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.49, #queue-req: 0, 
[2025-07-28 00:21:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 308, full token usage: 0.00, #swa token: 308, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.96, #queue-req: 0, 
[2025-07-28 00:21:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 940, full token usage: 0.00, #swa token: 940, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.50, #queue-req: 0, 
[2025-07-28 00:21:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 348, full token usage: 0.00, #swa token: 348, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.95, #queue-req: 0, 
[2025-07-28 00:21:19 DP0 TP0] Decode batch. #running-req: 1, #full token: 980, full token usage: 0.00, #swa token: 980, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.50, #queue-req: 0, 
[2025-07-28 00:21:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 388, full token usage: 0.00, #swa token: 388, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.95, #queue-req: 0, 
[2025-07-28 00:21:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 1020, full token usage: 0.00, #swa token: 1020, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.46, #queue-req: 0, 
[2025-07-28 00:21:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 428, full token usage: 0.00, #swa token: 428, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.96, #queue-req: 0, 
[2025-07-28 00:21:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 1060, full token usage: 0.00, #swa token: 1060, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.46, #queue-req: 0, 
[2025-07-28 00:21:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 468, full token usage: 0.00, #swa token: 468, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.96, #queue-req: 0, 
[2025-07-28 00:21:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 1100, full token usage: 0.00, #swa token: 1100, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.45, #queue-req: 0, 
[2025-07-28 00:21:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 508, full token usage: 0.00, #swa token: 508, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:21:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 1140, full token usage: 0.00, #swa token: 1140, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.44, #queue-req: 0, 
[2025-07-28 00:21:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 548, full token usage: 0.00, #swa token: 548, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:21:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 1180, full token usage: 0.00, #swa token: 1180, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.43, #queue-req: 0, 
[2025-07-28 00:21:24] INFO:     127.0.0.1:54610 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:21:24 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 131, #cached-token: 103, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:21:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 588, full token usage: 0.00, #swa token: 588, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.82, #queue-req: 0, 
[2025-07-28 00:21:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 272, full token usage: 0.00, #swa token: 272, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.53, #queue-req: 0, 
[2025-07-28 00:21:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 628, full token usage: 0.00, #swa token: 628, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.78, #queue-req: 0, 
[2025-07-28 00:21:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 312, full token usage: 0.00, #swa token: 312, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.86, #queue-req: 0, 
[2025-07-28 00:21:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 668, full token usage: 0.00, #swa token: 668, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.73, #queue-req: 0, 
[2025-07-28 00:21:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 352, full token usage: 0.00, #swa token: 352, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:21:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 708, full token usage: 0.00, #swa token: 708, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.69, #queue-req: 0, 
[2025-07-28 00:21:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 392, full token usage: 0.00, #swa token: 392, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:21:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 748, full token usage: 0.00, #swa token: 748, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.63, #queue-req: 0, 
[2025-07-28 00:21:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 432, full token usage: 0.00, #swa token: 432, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:21:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 788, full token usage: 0.00, #swa token: 788, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.57, #queue-req: 0, 
[2025-07-28 00:21:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 472, full token usage: 0.00, #swa token: 472, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:21:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 828, full token usage: 0.00, #swa token: 828, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.57, #queue-req: 0, 
[2025-07-28 00:21:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 512, full token usage: 0.00, #swa token: 512, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.84, #queue-req: 0, 
[2025-07-28 00:21:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 868, full token usage: 0.00, #swa token: 868, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.55, #queue-req: 0, 
[2025-07-28 00:21:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 552, full token usage: 0.00, #swa token: 552, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.78, #queue-req: 0, 
[2025-07-28 00:21:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 908, full token usage: 0.00, #swa token: 908, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.57, #queue-req: 0, 
[2025-07-28 00:21:33 DP0 TP0] Decode batch. #running-req: 1, #full token: 592, full token usage: 0.00, #swa token: 592, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.73, #queue-req: 0, 
[2025-07-28 00:21:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 948, full token usage: 0.00, #swa token: 948, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.57, #queue-req: 0, 
[2025-07-28 00:21:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 632, full token usage: 0.00, #swa token: 632, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.71, #queue-req: 0, 
[2025-07-28 00:21:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 988, full token usage: 0.00, #swa token: 988, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.56, #queue-req: 0, 
[2025-07-28 00:21:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 672, full token usage: 0.00, #swa token: 672, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.67, #queue-req: 0, 
[2025-07-28 00:21:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 1028, full token usage: 0.00, #swa token: 1028, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.54, #queue-req: 0, 
[2025-07-28 00:21:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 712, full token usage: 0.00, #swa token: 712, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.62, #queue-req: 0, 
[2025-07-28 00:21:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 1068, full token usage: 0.00, #swa token: 1068, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.53, #queue-req: 0, 
[2025-07-28 00:21:37 DP0 TP0] Decode batch. #running-req: 1, #full token: 752, full token usage: 0.00, #swa token: 752, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.11, #queue-req: 0, 
[2025-07-28 00:21:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 1108, full token usage: 0.00, #swa token: 1108, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.52, #queue-req: 0, 
[2025-07-28 00:21:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 792, full token usage: 0.00, #swa token: 792, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.52, #queue-req: 0, 
[2025-07-28 00:21:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 1148, full token usage: 0.00, #swa token: 1148, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.51, #queue-req: 0, 
[2025-07-28 00:21:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 832, full token usage: 0.00, #swa token: 832, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.51, #queue-req: 0, 
[2025-07-28 00:21:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 1188, full token usage: 0.00, #swa token: 1188, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.51, #queue-req: 0, 
[2025-07-28 00:21:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 872, full token usage: 0.00, #swa token: 872, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.51, #queue-req: 0, 
[2025-07-28 00:21:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 1228, full token usage: 0.00, #swa token: 1228, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.49, #queue-req: 0, 
[2025-07-28 00:21:41] INFO:     127.0.0.1:43080 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:21:41 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 152, #cached-token: 104, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:21:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 912, full token usage: 0.00, #swa token: 912, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.51, #queue-req: 0, 
[2025-07-28 00:21:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 289, full token usage: 0.00, #swa token: 289, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.63, #queue-req: 0, 
[2025-07-28 00:21:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 952, full token usage: 0.00, #swa token: 952, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.52, #queue-req: 0, 
[2025-07-28 00:21:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 329, full token usage: 0.00, #swa token: 329, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.95, #queue-req: 0, 
[2025-07-28 00:21:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 992, full token usage: 0.00, #swa token: 992, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.50, #queue-req: 0, 
[2025-07-28 00:21:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 369, full token usage: 0.00, #swa token: 369, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.95, #queue-req: 0, 
[2025-07-28 00:21:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 1032, full token usage: 0.00, #swa token: 1032, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.48, #queue-req: 0, 
[2025-07-28 00:21:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 409, full token usage: 0.00, #swa token: 409, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.96, #queue-req: 0, 
[2025-07-28 00:21:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 1072, full token usage: 0.00, #swa token: 1072, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.47, #queue-req: 0, 
[2025-07-28 00:21:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 449, full token usage: 0.00, #swa token: 449, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.95, #queue-req: 0, 
[2025-07-28 00:21:46] INFO:     127.0.0.1:52862 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:21:46 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 110, #cached-token: 103, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:21:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 222, full token usage: 0.00, #swa token: 222, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.41, #queue-req: 0, 
[2025-07-28 00:21:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 489, full token usage: 0.00, #swa token: 489, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.92, #queue-req: 0, 
[2025-07-28 00:21:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 262, full token usage: 0.00, #swa token: 262, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.83, #queue-req: 0, 
[2025-07-28 00:21:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 529, full token usage: 0.00, #swa token: 529, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:21:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 302, full token usage: 0.00, #swa token: 302, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:21:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 569, full token usage: 0.00, #swa token: 569, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.84, #queue-req: 0, 
[2025-07-28 00:21:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 342, full token usage: 0.00, #swa token: 342, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:21:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 609, full token usage: 0.00, #swa token: 609, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.81, #queue-req: 0, 
[2025-07-28 00:21:50 DP0 TP0] Decode batch. #running-req: 1, #full token: 382, full token usage: 0.00, #swa token: 382, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:21:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 649, full token usage: 0.00, #swa token: 649, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.77, #queue-req: 0, 
[2025-07-28 00:21:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 422, full token usage: 0.00, #swa token: 422, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.85, #queue-req: 0, 
[2025-07-28 00:21:51 DP1 TP0] Decode batch. #running-req: 1, #full token: 689, full token usage: 0.00, #swa token: 689, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.72, #queue-req: 0, 
[2025-07-28 00:21:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 462, full token usage: 0.00, #swa token: 462, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.83, #queue-req: 0, 
[2025-07-28 00:21:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 729, full token usage: 0.00, #swa token: 729, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.67, #queue-req: 0, 
[2025-07-28 00:21:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 502, full token usage: 0.00, #swa token: 502, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.84, #queue-req: 0, 
[2025-07-28 00:21:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 769, full token usage: 0.00, #swa token: 769, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.61, #queue-req: 0, 
[2025-07-28 00:21:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 542, full token usage: 0.00, #swa token: 542, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.79, #queue-req: 0, 
[2025-07-28 00:21:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 809, full token usage: 0.00, #swa token: 809, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.59, #queue-req: 0, 
[2025-07-28 00:21:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 582, full token usage: 0.00, #swa token: 582, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.75, #queue-req: 0, 
[2025-07-28 00:21:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 849, full token usage: 0.00, #swa token: 849, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.56, #queue-req: 0, 
[2025-07-28 00:21:56 DP0 TP0] Decode batch. #running-req: 1, #full token: 622, full token usage: 0.00, #swa token: 622, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.72, #queue-req: 0, 
[2025-07-28 00:21:56 DP1 TP0] Decode batch. #running-req: 1, #full token: 889, full token usage: 0.00, #swa token: 889, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.58, #queue-req: 0, 
[2025-07-28 00:21:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 662, full token usage: 0.00, #swa token: 662, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.68, #queue-req: 0, 
[2025-07-28 00:21:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 929, full token usage: 0.00, #swa token: 929, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.57, #queue-req: 0, 
[2025-07-28 00:21:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 702, full token usage: 0.00, #swa token: 702, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.63, #queue-req: 0, 
[2025-07-28 00:21:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 969, full token usage: 0.00, #swa token: 969, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.57, #queue-req: 0, 
[2025-07-28 00:21:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 742, full token usage: 0.00, #swa token: 742, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.58, #queue-req: 0, 
[2025-07-28 00:21:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 1009, full token usage: 0.00, #swa token: 1009, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.55, #queue-req: 0, 
[2025-07-28 00:22:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 782, full token usage: 0.00, #swa token: 782, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.53, #queue-req: 0, 
[2025-07-28 00:22:00] INFO:     127.0.0.1:43254 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:22:00 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 96, #cached-token: 103, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:22:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 211, full token usage: 0.00, #swa token: 211, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 36.23, #queue-req: 0, 
[2025-07-28 00:22:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 822, full token usage: 0.00, #swa token: 822, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.49, #queue-req: 0, 
[2025-07-28 00:22:01 DP1 TP0] Decode batch. #running-req: 1, #full token: 251, full token usage: 0.00, #swa token: 251, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 41.02, #queue-req: 0, 
[2025-07-28 00:22:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 862, full token usage: 0.00, #swa token: 862, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.50, #queue-req: 0, 
[2025-07-28 00:22:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 291, full token usage: 0.00, #swa token: 291, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.96, #queue-req: 0, 
[2025-07-28 00:22:03 DP0 TP0] Decode batch. #running-req: 1, #full token: 902, full token usage: 0.00, #swa token: 902, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.50, #queue-req: 0, 
[2025-07-28 00:22:03 DP1 TP0] Decode batch. #running-req: 1, #full token: 331, full token usage: 0.00, #swa token: 331, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.94, #queue-req: 0, 
[2025-07-28 00:22:04 DP0 TP0] Decode batch. #running-req: 1, #full token: 942, full token usage: 0.00, #swa token: 942, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.50, #queue-req: 0, 
[2025-07-28 00:22:04] INFO:     127.0.0.1:35874 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:22:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 371, full token usage: 0.00, #swa token: 371, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.93, #queue-req: 0, 
[2025-07-28 00:22:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 411, full token usage: 0.00, #swa token: 411, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.93, #queue-req: 0, 
[2025-07-28 00:22:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 451, full token usage: 0.00, #swa token: 451, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.93, #queue-req: 0, 
[2025-07-28 00:22:07 DP1 TP0] Decode batch. #running-req: 1, #full token: 491, full token usage: 0.00, #swa token: 491, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.88, #queue-req: 0, 
[2025-07-28 00:22:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 531, full token usage: 0.00, #swa token: 531, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.87, #queue-req: 0, 
[2025-07-28 00:22:09 DP1 TP0] Decode batch. #running-req: 1, #full token: 571, full token usage: 0.00, #swa token: 571, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.82, #queue-req: 0, 
[2025-07-28 00:22:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 611, full token usage: 0.00, #swa token: 611, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.79, #queue-req: 0, 
[2025-07-28 00:22:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 651, full token usage: 0.00, #swa token: 651, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.75, #queue-req: 0, 
[2025-07-28 00:22:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 691, full token usage: 0.00, #swa token: 691, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.71, #queue-req: 0, 
[2025-07-28 00:22:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 731, full token usage: 0.00, #swa token: 731, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.66, #queue-req: 0, 
[2025-07-28 00:22:14 DP1 TP0] Decode batch. #running-req: 1, #full token: 771, full token usage: 0.00, #swa token: 771, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.61, #queue-req: 0, 
[2025-07-28 00:22:15 DP1 TP0] Decode batch. #running-req: 1, #full token: 811, full token usage: 0.00, #swa token: 811, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.58, #queue-req: 0, 
[2025-07-28 00:22:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 851, full token usage: 0.00, #swa token: 851, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.57, #queue-req: 0, 
[2025-07-28 00:22:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 891, full token usage: 0.00, #swa token: 891, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.56, #queue-req: 0, 
[2025-07-28 00:22:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 931, full token usage: 0.00, #swa token: 931, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.57, #queue-req: 0, 
[2025-07-28 00:22:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 971, full token usage: 0.00, #swa token: 971, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.55, #queue-req: 0, 
[2025-07-28 00:22:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 1011, full token usage: 0.00, #swa token: 1011, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.49, #queue-req: 0, 
[2025-07-28 00:22:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 1051, full token usage: 0.00, #swa token: 1051, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 40.51, #queue-req: 0, 
[2025-07-28 00:22:22] INFO:     127.0.0.1:53930 - "POST /v1/chat/completions HTTP/1.1" 200 OK

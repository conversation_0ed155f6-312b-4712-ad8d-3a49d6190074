[2025-07-28 00:01:45] server_args=ServerArgs(model_path='/data/home/<USER>/repos/anesbench/share/models/gemma-3-4b-it', tokenizer_path='/data/home/<USER>/repos/anesbench/share/models/gemma-3-4b-it', tokenizer_mode='auto', skip_tokenizer_init=False, load_format='auto', model_loader_extra_config='{}', trust_remote_code=False, context_length=32768, is_embedding=False, enable_multimodal=None, revision=None, model_impl='auto', host='127.0.0.1', port=8002, skip_server_warmup=False, warmups=None, nccl_port=None, dtype='auto', quantization=None, quantization_param_path=None, kv_cache_dtype='auto', mem_fraction_static=0.85, max_running_requests=None, max_total_tokens=None, chunked_prefill_size=8192, max_prefill_tokens=16384, schedule_policy='fcfs', schedule_conservativeness=1.0, cpu_offload_gb=0, page_size=1, hybrid_kvcache_ratio=None, swa_full_tokens_ratio=0.8, disable_hybrid_swa_memory=False, device='cuda', tp_size=4, pp_size=1, max_micro_batch_size=None, stream_interval=1, stream_output=False, random_seed=134632208, constrained_json_whitespace_pattern=None, watchdog_timeout=300, dist_timeout=None, download_dir=None, base_gpu_id=0, gpu_id_step=1, sleep_on_idle=False, log_level='info', log_level_http=None, log_requests=False, log_requests_level=0, crash_dump_folder=None, show_time_cost=True, enable_metrics=True, enable_metrics_for_all_schedulers=False, bucket_time_to_first_token=None, bucket_inter_token_latency=None, bucket_e2e_request_latency=None, collect_tokens_histogram=False, decode_log_interval=40, enable_request_time_stats_logging=False, kv_events_config=None, api_key=None, served_model_name='/data/home/<USER>/repos/anesbench/share/models/gemma-3-4b-it', chat_template=None, completion_template=None, file_storage_path='sglang_storage', enable_cache_report=False, reasoning_parser=None, tool_call_parser=None, dp_size=2, load_balance_method='round_robin', dist_init_addr=None, nnodes=1, node_rank=0, json_model_override_args='{}', preferred_sampling_params=None, enable_lora=None, max_lora_rank=None, lora_target_modules=None, lora_paths=None, max_loras_per_batch=8, lora_backend='triton', attention_backend=None, sampling_backend='flashinfer', grammar_backend='xgrammar', mm_attention_backend=None, speculative_algorithm=None, speculative_draft_model_path=None, speculative_num_steps=None, speculative_eagle_topk=None, speculative_num_draft_tokens=None, speculative_accept_threshold_single=1.0, speculative_accept_threshold_acc=1.0, speculative_token_map=None, ep_size=1, enable_ep_moe=False, enable_deepep_moe=False, enable_flashinfer_moe=False, enable_flashinfer_allreduce_fusion=False, deepep_mode='auto', ep_num_redundant_experts=0, ep_dispatch_algorithm='static', init_expert_location='trivial', enable_eplb=False, eplb_algorithm='auto', eplb_rebalance_num_iterations=1000, eplb_rebalance_layers_per_chunk=None, expert_distribution_recorder_mode=None, expert_distribution_recorder_buffer_size=1000, enable_expert_distribution_metrics=False, deepep_config=None, moe_dense_tp_size=None, enable_hierarchical_cache=False, hicache_ratio=2.0, hicache_size=0, hicache_write_policy='write_through_selective', hicache_io_backend='', hicache_storage_backend=None, enable_double_sparsity=False, ds_channel_config_path=None, ds_heavy_channel_num=32, ds_heavy_token_num=256, ds_heavy_channel_type='qk', ds_sparse_decode_threshold=4096, disable_radix_cache=False, cuda_graph_max_bs=None, cuda_graph_bs=None, disable_cuda_graph=False, disable_cuda_graph_padding=False, enable_profile_cuda_graph=False, enable_nccl_nvls=False, enable_tokenizer_batch_encode=False, disable_outlines_disk_cache=False, disable_custom_all_reduce=False, enable_mscclpp=False, disable_overlap_schedule=False, enable_mixed_chunk=False, enable_dp_attention=False, enable_dp_lm_head=False, enable_two_batch_overlap=False, enable_torch_compile=False, torch_compile_max_bs=32, torchao_config='', enable_nan_detection=False, enable_p2p_check=False, triton_attention_reduce_in_fp32=False, triton_attention_num_kv_splits=8, num_continuous_decode_steps=1, delete_ckpt_after_loading=False, enable_memory_saver=False, allow_auto_truncate=False, enable_custom_logit_processor=False, flashinfer_mla_disable_ragged=False, disable_shared_experts_fusion=False, disable_chunked_prefix_cache=False, disable_fast_image_processor=False, enable_return_hidden_states=False, enable_triton_kernel_moe=False, debug_tensor_dump_output_folder=None, debug_tensor_dump_input_file=None, debug_tensor_dump_inject=False, debug_tensor_dump_prefill_only=False, disaggregation_mode='null', disaggregation_transfer_backend='mooncake', disaggregation_bootstrap_port=8998, disaggregation_decode_tp=None, disaggregation_decode_dp=None, disaggregation_prefill_pp=1, disaggregation_ib_device=None, num_reserved_decode_tokens=512, pdlb_url=None, custom_weight_loader=[], weight_loader_disable_mmap=False, enable_pdmux=False, sm_group_num=3)
[2025-07-28 00:01:45] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:01:45] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:01:45] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:01:46] Inferred chat template from model path: gemma-it
[2025-07-28 00:01:51] Launch DP0 starting at GPU #0.
[2025-07-28 00:01:51] Launch DP1 starting at GPU #4.
[2025-07-28 00:01:57 DP1 TP1] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:01:57 DP1 TP1] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:01:57 DP1 TP1] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:01:58 DP0 TP0] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:01:58 DP0 TP0] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:01:58 DP0 TP0] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:01:58 DP0 TP3] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:01:58 DP0 TP3] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:01:58 DP0 TP3] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:01:58 DP1 TP0] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:01:58 DP1 TP0] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:01:58 DP1 TP0] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:01:58 DP1 TP2] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:01:58 DP1 TP2] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:01:58 DP1 TP2] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:01:58 DP1 TP3] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:01:58 DP1 TP3] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:01:58 DP1 TP3] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:01:58 DP0 TP1] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:01:58 DP0 TP1] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:01:58 DP0 TP1] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:01:58 DP0 TP2] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:01:58 DP0 TP2] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:01:58 DP0 TP2] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:01:59 DP1 TP1] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:01:59 DP1 TP1] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:01:59 DP1 TP1] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:01:59 DP0 TP0] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:01:59 DP0 TP0] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:01:59 DP0 TP0] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:01:59 DP0 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 00:01:59 DP0 TP0] Init torch distributed begin.
[2025-07-28 00:01:59 DP0 TP3] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:01:59 DP0 TP3] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:01:59 DP0 TP3] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:01:59 DP1 TP0] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:01:59 DP1 TP0] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:01:59 DP1 TP0] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:01:59 DP1 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 00:01:59 DP1 TP0] Init torch distributed begin.
[2025-07-28 00:01:59 DP0 TP1] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:01:59 DP0 TP1] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:01:59 DP0 TP1] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:01:59 DP1 TP2] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:01:59 DP1 TP2] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:01:59 DP1 TP2] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:02:00 DP1 TP3] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:02:00 DP1 TP3] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:02:00 DP1 TP3] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:02:00 DP0 TP2] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:02:00 DP0 TP2] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:02:00 DP0 TP2] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:02:01 DP0 TP0] sglang is using nccl==2.26.2
[2025-07-28 00:02:02 DP0 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 00:02:02 DP1 TP0] sglang is using nccl==2.26.2
[2025-07-28 00:02:02 DP0 TP3] Multimodal attention backend not set. Use sdpa.
[2025-07-28 00:02:02 DP0 TP3] Using sdpa as multimodal attention backend.
[2025-07-28 00:02:02 DP0 TP0] Load weight begin. avail mem=78.21 GB
[2025-07-28 00:02:02 DP0 TP0] Multimodal attention backend not set. Use sdpa.
[2025-07-28 00:02:02 DP0 TP0] Using sdpa as multimodal attention backend.
[2025-07-28 00:02:02 DP0 TP1] Multimodal attention backend not set. Use sdpa.
[2025-07-28 00:02:02 DP0 TP1] Using sdpa as multimodal attention backend.
[2025-07-28 00:02:02 DP0 TP2] Multimodal attention backend not set. Use sdpa.
[2025-07-28 00:02:02 DP0 TP2] Using sdpa as multimodal attention backend.

Loading safetensors checkpoint shards:   0% Completed | 0/2 [00:00<?, ?it/s]
[2025-07-28 00:02:03 DP1 TP0] Init torch distributed ends. mem usage=0.63 GB

Loading safetensors checkpoint shards:  50% Completed | 1/2 [00:00<00:00,  1.74it/s]
[2025-07-28 00:02:03 DP1 TP0] Load weight begin. avail mem=78.21 GB
[2025-07-28 00:02:03 DP1 TP0] Multimodal attention backend not set. Use sdpa.
[2025-07-28 00:02:03 DP1 TP0] Using sdpa as multimodal attention backend.
[2025-07-28 00:02:03 DP1 TP3] Multimodal attention backend not set. Use sdpa.
[2025-07-28 00:02:03 DP1 TP3] Using sdpa as multimodal attention backend.
[2025-07-28 00:02:03 DP1 TP1] Multimodal attention backend not set. Use sdpa.
[2025-07-28 00:02:03 DP1 TP1] Using sdpa as multimodal attention backend.
[2025-07-28 00:02:03 DP1 TP2] Multimodal attention backend not set. Use sdpa.
[2025-07-28 00:02:03 DP1 TP2] Using sdpa as multimodal attention backend.

Loading safetensors checkpoint shards:   0% Completed | 0/2 [00:00<?, ?it/s]

Loading safetensors checkpoint shards: 100% Completed | 2/2 [00:01<00:00,  1.43it/s]

Loading safetensors checkpoint shards: 100% Completed | 2/2 [00:01<00:00,  1.47it/s]

[2025-07-28 00:02:03 DP0 TP0] Load weight end. type=Gemma3ForConditionalGeneration, dtype=torch.bfloat16, avail mem=75.23 GB, mem usage=2.97 GB.
[2025-07-28 00:02:04 DP0 TP0] Use Sliding window memory pool. full_layer_tokens=2358273, swa_layer_tokens=1886618

Loading safetensors checkpoint shards:  50% Completed | 1/2 [00:00<00:00,  1.71it/s]
[2025-07-28 00:02:04 DP0 TP2] KV Cache is allocated. #tokens: 1886618, K size: 26.09 GB, V size: 26.09 GB
[2025-07-28 00:02:04 DP0 TP0] KV Cache is allocated. #tokens: 1886618, K size: 26.09 GB, V size: 26.09 GB
[2025-07-28 00:02:04 DP0 TP1] KV Cache is allocated. #tokens: 1886618, K size: 26.09 GB, V size: 26.09 GB
[2025-07-28 00:02:04 DP0 TP3] KV Cache is allocated. #tokens: 1886618, K size: 26.09 GB, V size: 26.09 GB
[2025-07-28 00:02:04 DP0 TP2] KV Cache is allocated. #tokens: 2358273, K size: 5.62 GB, V size: 5.62 GB
[2025-07-28 00:02:04 DP0 TP0] KV Cache is allocated. #tokens: 2358273, K size: 5.62 GB, V size: 5.62 GB
[2025-07-28 00:02:04 DP0 TP1] KV Cache is allocated. #tokens: 2358273, K size: 5.62 GB, V size: 5.62 GB
[2025-07-28 00:02:04 DP0 TP3] KV Cache is allocated. #tokens: 2358273, K size: 5.62 GB, V size: 5.62 GB
[2025-07-28 00:02:04 DP0 TP0] Memory pool end. avail mem=11.19 GB
[2025-07-28 00:02:04 DP0 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.60 GB
[2025-07-28 00:02:04 DP0 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.54 GB):   0%|          | 0/23 [00:00<?, ?it/s]
Loading safetensors checkpoint shards: 100% Completed | 2/2 [00:01<00:00,  1.34it/s]

Loading safetensors checkpoint shards: 100% Completed | 2/2 [00:01<00:00,  1.38it/s]

[2025-07-28 00:02:05 DP1 TP0] Load weight end. type=Gemma3ForConditionalGeneration, dtype=torch.bfloat16, avail mem=75.23 GB, mem usage=2.97 GB.
[2025-07-28 00:02:05 DP1 TP0] Use Sliding window memory pool. full_layer_tokens=2358273, swa_layer_tokens=1886618
[2025-07-28 00:02:05 DP1 TP1] KV Cache is allocated. #tokens: 1886618, K size: 26.09 GB, V size: 26.09 GB
[2025-07-28 00:02:05 DP1 TP0] KV Cache is allocated. #tokens: 1886618, K size: 26.09 GB, V size: 26.09 GB
[2025-07-28 00:02:05 DP1 TP3] KV Cache is allocated. #tokens: 1886618, K size: 26.09 GB, V size: 26.09 GB
[2025-07-28 00:02:05 DP1 TP2] KV Cache is allocated. #tokens: 1886618, K size: 26.09 GB, V size: 26.09 GB
[2025-07-28 00:02:05 DP1 TP1] KV Cache is allocated. #tokens: 2358273, K size: 5.62 GB, V size: 5.62 GB
[2025-07-28 00:02:05 DP1 TP0] KV Cache is allocated. #tokens: 2358273, K size: 5.62 GB, V size: 5.62 GB
[2025-07-28 00:02:05 DP1 TP3] KV Cache is allocated. #tokens: 2358273, K size: 5.62 GB, V size: 5.62 GB
[2025-07-28 00:02:05 DP1 TP0] Memory pool end. avail mem=11.19 GB
[2025-07-28 00:02:05 DP1 TP2] KV Cache is allocated. #tokens: 2358273, K size: 5.62 GB, V size: 5.62 GB
[2025-07-28 00:02:05 DP1 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.60 GB
[2025-07-28 00:02:05 DP1 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.54 GB):   0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.54 GB):   4%|▍         | 1/23 [00:01<00:24,  1.13s/it]
Capturing batches (bs=152 avail_mem=9.73 GB):   4%|▍         | 1/23 [00:01<00:24,  1.13s/it] 
Capturing batches (bs=152 avail_mem=9.73 GB):   9%|▊         | 2/23 [00:01<00:15,  1.33it/s]
Capturing batches (bs=144 avail_mem=9.38 GB):   9%|▊         | 2/23 [00:01<00:15,  1.33it/s]
Capturing batches (bs=160 avail_mem=10.54 GB):   4%|▍         | 1/23 [00:01<00:24,  1.13s/it]
Capturing batches (bs=152 avail_mem=9.73 GB):   4%|▍         | 1/23 [00:01<00:24,  1.13s/it] 
Capturing batches (bs=144 avail_mem=9.38 GB):  13%|█▎        | 3/23 [00:02<00:12,  1.63it/s]
Capturing batches (bs=136 avail_mem=9.33 GB):  13%|█▎        | 3/23 [00:02<00:12,  1.63it/s]
Capturing batches (bs=136 avail_mem=9.33 GB):  17%|█▋        | 4/23 [00:02<00:10,  1.88it/s]
Capturing batches (bs=128 avail_mem=9.02 GB):  17%|█▋        | 4/23 [00:02<00:10,  1.88it/s]
Capturing batches (bs=152 avail_mem=9.73 GB):   9%|▊         | 2/23 [00:01<00:15,  1.38it/s]
Capturing batches (bs=144 avail_mem=9.38 GB):   9%|▊         | 2/23 [00:01<00:15,  1.38it/s]
Capturing batches (bs=144 avail_mem=9.38 GB):  13%|█▎        | 3/23 [00:02<00:12,  1.64it/s]
Capturing batches (bs=136 avail_mem=9.33 GB):  13%|█▎        | 3/23 [00:02<00:12,  1.64it/s]
Capturing batches (bs=128 avail_mem=9.02 GB):  22%|██▏       | 5/23 [00:02<00:09,  1.90it/s]
Capturing batches (bs=120 avail_mem=8.97 GB):  22%|██▏       | 5/23 [00:02<00:09,  1.90it/s]
Capturing batches (bs=136 avail_mem=9.33 GB):  17%|█▋        | 4/23 [00:02<00:10,  1.80it/s]
Capturing batches (bs=128 avail_mem=9.02 GB):  17%|█▋        | 4/23 [00:02<00:10,  1.80it/s]
Capturing batches (bs=120 avail_mem=8.97 GB):  26%|██▌       | 6/23 [00:03<00:08,  1.97it/s]
Capturing batches (bs=112 avail_mem=8.69 GB):  26%|██▌       | 6/23 [00:03<00:08,  1.97it/s]
Capturing batches (bs=128 avail_mem=9.02 GB):  22%|██▏       | 5/23 [00:02<00:09,  1.95it/s]
Capturing batches (bs=120 avail_mem=8.97 GB):  22%|██▏       | 5/23 [00:02<00:09,  1.95it/s]
Capturing batches (bs=112 avail_mem=8.69 GB):  30%|███       | 7/23 [00:03<00:07,  2.01it/s]
Capturing batches (bs=104 avail_mem=8.64 GB):  30%|███       | 7/23 [00:03<00:07,  2.01it/s]
Capturing batches (bs=120 avail_mem=8.97 GB):  26%|██▌       | 6/23 [00:03<00:08,  2.06it/s]
Capturing batches (bs=112 avail_mem=8.69 GB):  26%|██▌       | 6/23 [00:03<00:08,  2.06it/s]
Capturing batches (bs=104 avail_mem=8.64 GB):  35%|███▍      | 8/23 [00:04<00:07,  2.14it/s]
Capturing batches (bs=96 avail_mem=8.39 GB):  35%|███▍      | 8/23 [00:04<00:07,  2.14it/s] 
Capturing batches (bs=96 avail_mem=8.39 GB):  39%|███▉      | 9/23 [00:04<00:06,  2.23it/s]
Capturing batches (bs=88 avail_mem=8.34 GB):  39%|███▉      | 9/23 [00:04<00:06,  2.23it/s]
Capturing batches (bs=112 avail_mem=8.69 GB):  30%|███       | 7/23 [00:03<00:07,  2.09it/s]
Capturing batches (bs=104 avail_mem=8.64 GB):  30%|███       | 7/23 [00:03<00:07,  2.09it/s]
Capturing batches (bs=88 avail_mem=8.34 GB):  43%|████▎     | 10/23 [00:05<00:05,  2.30it/s]
Capturing batches (bs=80 avail_mem=8.29 GB):  43%|████▎     | 10/23 [00:05<00:05,  2.30it/s]
Capturing batches (bs=104 avail_mem=8.64 GB):  35%|███▍      | 8/23 [00:04<00:07,  2.11it/s]
Capturing batches (bs=96 avail_mem=8.39 GB):  35%|███▍      | 8/23 [00:04<00:07,  2.11it/s] 
Capturing batches (bs=80 avail_mem=8.29 GB):  48%|████▊     | 11/23 [00:05<00:05,  2.40it/s]
Capturing batches (bs=72 avail_mem=8.09 GB):  48%|████▊     | 11/23 [00:05<00:05,  2.40it/s]
Capturing batches (bs=96 avail_mem=8.39 GB):  39%|███▉      | 9/23 [00:04<00:06,  2.18it/s]
Capturing batches (bs=88 avail_mem=8.34 GB):  39%|███▉      | 9/23 [00:04<00:06,  2.18it/s]
Capturing batches (bs=72 avail_mem=8.09 GB):  52%|█████▏    | 12/23 [00:05<00:04,  2.45it/s]
Capturing batches (bs=64 avail_mem=8.04 GB):  52%|█████▏    | 12/23 [00:05<00:04,  2.45it/s]
Capturing batches (bs=88 avail_mem=8.34 GB):  43%|████▎     | 10/23 [00:05<00:06,  2.13it/s]
Capturing batches (bs=80 avail_mem=8.29 GB):  43%|████▎     | 10/23 [00:05<00:06,  2.13it/s]
Capturing batches (bs=64 avail_mem=8.04 GB):  57%|█████▋    | 13/23 [00:06<00:03,  2.52it/s]
Capturing batches (bs=56 avail_mem=7.99 GB):  57%|█████▋    | 13/23 [00:06<00:03,  2.52it/s]
Capturing batches (bs=80 avail_mem=8.29 GB):  48%|████▊     | 11/23 [00:05<00:05,  2.19it/s]
Capturing batches (bs=72 avail_mem=8.09 GB):  48%|████▊     | 11/23 [00:05<00:05,  2.19it/s]
Capturing batches (bs=56 avail_mem=7.99 GB):  61%|██████    | 14/23 [00:06<00:03,  2.43it/s]
Capturing batches (bs=48 avail_mem=7.95 GB):  61%|██████    | 14/23 [00:06<00:03,  2.43it/s]
Capturing batches (bs=72 avail_mem=8.09 GB):  52%|█████▏    | 12/23 [00:06<00:05,  2.10it/s]
Capturing batches (bs=64 avail_mem=8.04 GB):  52%|█████▏    | 12/23 [00:06<00:05,  2.10it/s]
Capturing batches (bs=48 avail_mem=7.95 GB):  65%|██████▌   | 15/23 [00:07<00:03,  2.45it/s]
Capturing batches (bs=40 avail_mem=7.80 GB):  65%|██████▌   | 15/23 [00:07<00:03,  2.45it/s]
Capturing batches (bs=40 avail_mem=7.80 GB):  70%|██████▉   | 16/23 [00:07<00:02,  2.51it/s]
Capturing batches (bs=32 avail_mem=7.75 GB):  70%|██████▉   | 16/23 [00:07<00:02,  2.51it/s]
Capturing batches (bs=64 avail_mem=8.04 GB):  57%|█████▋    | 13/23 [00:06<00:04,  2.11it/s]
Capturing batches (bs=56 avail_mem=7.99 GB):  57%|█████▋    | 13/23 [00:06<00:04,  2.11it/s]
Capturing batches (bs=32 avail_mem=7.75 GB):  74%|███████▍  | 17/23 [00:07<00:02,  2.55it/s]
Capturing batches (bs=24 avail_mem=7.71 GB):  74%|███████▍  | 17/23 [00:07<00:02,  2.55it/s]
Capturing batches (bs=56 avail_mem=7.99 GB):  61%|██████    | 14/23 [00:07<00:04,  2.17it/s]
Capturing batches (bs=48 avail_mem=7.95 GB):  61%|██████    | 14/23 [00:07<00:04,  2.17it/s]
Capturing batches (bs=24 avail_mem=7.71 GB):  78%|███████▊  | 18/23 [00:08<00:01,  2.55it/s]
Capturing batches (bs=16 avail_mem=7.66 GB):  78%|███████▊  | 18/23 [00:08<00:01,  2.55it/s]
Capturing batches (bs=48 avail_mem=7.95 GB):  65%|██████▌   | 15/23 [00:07<00:03,  2.26it/s]
Capturing batches (bs=40 avail_mem=7.80 GB):  65%|██████▌   | 15/23 [00:07<00:03,  2.26it/s]
Capturing batches (bs=16 avail_mem=7.66 GB):  83%|████████▎ | 19/23 [00:08<00:01,  2.32it/s]
Capturing batches (bs=8 avail_mem=7.61 GB):  83%|████████▎ | 19/23 [00:08<00:01,  2.32it/s] 
Capturing batches (bs=40 avail_mem=7.80 GB):  70%|██████▉   | 16/23 [00:07<00:03,  2.29it/s]
Capturing batches (bs=32 avail_mem=7.75 GB):  70%|██████▉   | 16/23 [00:07<00:03,  2.29it/s]
Capturing batches (bs=8 avail_mem=7.61 GB):  87%|████████▋ | 20/23 [00:09<00:01,  2.26it/s]
Capturing batches (bs=4 avail_mem=7.56 GB):  87%|████████▋ | 20/23 [00:09<00:01,  2.26it/s]
Capturing batches (bs=32 avail_mem=7.75 GB):  74%|███████▍  | 17/23 [00:08<00:02,  2.30it/s]
Capturing batches (bs=24 avail_mem=7.71 GB):  74%|███████▍  | 17/23 [00:08<00:02,  2.30it/s]
Capturing batches (bs=24 avail_mem=7.71 GB):  78%|███████▊  | 18/23 [00:08<00:02,  2.30it/s]
Capturing batches (bs=16 avail_mem=7.66 GB):  78%|███████▊  | 18/23 [00:08<00:02,  2.30it/s]
Capturing batches (bs=4 avail_mem=7.56 GB):  91%|█████████▏| 21/23 [00:09<00:00,  2.27it/s]
Capturing batches (bs=2 avail_mem=7.52 GB):  91%|█████████▏| 21/23 [00:09<00:00,  2.27it/s]
Capturing batches (bs=2 avail_mem=7.52 GB):  96%|█████████▌| 22/23 [00:10<00:00,  2.22it/s]
Capturing batches (bs=1 avail_mem=7.45 GB):  96%|█████████▌| 22/23 [00:10<00:00,  2.22it/s]
Capturing batches (bs=16 avail_mem=7.66 GB):  83%|████████▎ | 19/23 [00:09<00:01,  2.23it/s]
Capturing batches (bs=8 avail_mem=7.61 GB):  83%|████████▎ | 19/23 [00:09<00:01,  2.23it/s] [2025-07-28 00:02:15 DP0 TP2] Registering 1564 cuda graph addresses

Capturing batches (bs=1 avail_mem=7.45 GB): 100%|██████████| 23/23 [00:10<00:00,  2.29it/s]
Capturing batches (bs=1 avail_mem=7.45 GB): 100%|██████████| 23/23 [00:10<00:00,  2.17it/s]
[2025-07-28 00:02:15 DP0 TP0] Registering 1564 cuda graph addresses
[2025-07-28 00:02:15 DP0 TP3] Registering 1564 cuda graph addresses

Capturing batches (bs=8 avail_mem=7.61 GB):  87%|████████▋ | 20/23 [00:09<00:01,  2.24it/s]
Capturing batches (bs=4 avail_mem=7.56 GB):  87%|████████▋ | 20/23 [00:09<00:01,  2.24it/s][2025-07-28 00:02:15 DP0 TP1] Registering 1564 cuda graph addresses
[2025-07-28 00:02:15 DP0 TP0] Capture cuda graph end. Time elapsed: 10.79 s. mem usage=3.18 GB. avail mem=7.42 GB.

Capturing batches (bs=4 avail_mem=7.56 GB):  91%|█████████▏| 21/23 [00:10<00:00,  2.26it/s]
Capturing batches (bs=2 avail_mem=7.52 GB):  91%|█████████▏| 21/23 [00:10<00:00,  2.26it/s]
Capturing batches (bs=2 avail_mem=7.52 GB):  96%|█████████▌| 22/23 [00:10<00:00,  2.25it/s]
Capturing batches (bs=1 avail_mem=7.47 GB):  96%|█████████▌| 22/23 [00:10<00:00,  2.25it/s][2025-07-28 00:02:16 DP0 TP0] max_total_num_tokens=2358273, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=7.42 GB
[2025-07-28 00:02:16 DP1 TP3] Registering 1564 cuda graph addresses
[2025-07-28 00:02:16 DP1 TP1] Registering 1564 cuda graph addresses

Capturing batches (bs=1 avail_mem=7.47 GB): 100%|██████████| 23/23 [00:11<00:00,  2.18it/s]
Capturing batches (bs=1 avail_mem=7.47 GB): 100%|██████████| 23/23 [00:11<00:00,  2.08it/s]
[2025-07-28 00:02:16 DP1 TP0] Registering 1564 cuda graph addresses
[2025-07-28 00:02:16 DP1 TP2] Registering 1564 cuda graph addresses
[2025-07-28 00:02:16 DP1 TP0] Capture cuda graph end. Time elapsed: 11.23 s. mem usage=3.18 GB. avail mem=7.42 GB.
[2025-07-28 00:02:17 DP1 TP0] max_total_num_tokens=2358273, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=7.42 GB
[2025-07-28 00:02:19] INFO:     Started server process [1705316]
[2025-07-28 00:02:19] INFO:     Waiting for application startup.
[2025-07-28 00:02:19] INFO:     Application startup complete.
[2025-07-28 00:02:19] INFO:     Uvicorn running on http://127.0.0.1:8002 (Press CTRL+C to quit)
[2025-07-28 00:02:19] INFO:     127.0.0.1:56532 - "GET /health HTTP/1.1" 200 OK
[2025-07-28 00:02:19 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 213, #cached-token: 0, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:02:19 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 251, #cached-token: 0, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:02:20] INFO:     127.0.0.1:56560 - "GET /get_model_info HTTP/1.1" 200 OK
[2025-07-28 00:02:20 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 1, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:02:20 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 1, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:02:21] INFO:     127.0.0.1:56570 - "POST /generate HTTP/1.1" 200 OK
[2025-07-28 00:02:21] The server is fired up and ready to roll!
[2025-07-28 00:02:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 292, full token usage: 0.00, #swa token: 292, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 9.42, #queue-req: 0, 
[2025-07-28 00:02:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 254, full token usage: 0.00, #swa token: 254, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 13.28, #queue-req: 0, 
[2025-07-28 00:02:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 332, full token usage: 0.00, #swa token: 332, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 98.17, #queue-req: 0, 
[2025-07-28 00:02:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 294, full token usage: 0.00, #swa token: 294, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 99.63, #queue-req: 0, 
[2025-07-28 00:02:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 372, full token usage: 0.00, #swa token: 372, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.27, #queue-req: 0, 
[2025-07-28 00:02:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 334, full token usage: 0.00, #swa token: 334, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.53, #queue-req: 0, 
[2025-07-28 00:02:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 412, full token usage: 0.00, #swa token: 412, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.45, #queue-req: 0, 
[2025-07-28 00:02:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 374, full token usage: 0.00, #swa token: 374, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.35, #queue-req: 0, 
[2025-07-28 00:02:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 452, full token usage: 0.00, #swa token: 452, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.92, #queue-req: 0, 
[2025-07-28 00:02:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 414, full token usage: 0.00, #swa token: 414, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.31, #queue-req: 0, 
[2025-07-28 00:02:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 492, full token usage: 0.00, #swa token: 492, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.96, #queue-req: 0, 
[2025-07-28 00:02:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 454, full token usage: 0.00, #swa token: 454, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 99.68, #queue-req: 0, 
[2025-07-28 00:02:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 532, full token usage: 0.00, #swa token: 532, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.94, #queue-req: 0, 
[2025-07-28 00:02:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 494, full token usage: 0.00, #swa token: 494, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 99.98, #queue-req: 0, 
[2025-07-28 00:02:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 572, full token usage: 0.00, #swa token: 572, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.79, #queue-req: 0, 
[2025-07-28 00:02:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 534, full token usage: 0.00, #swa token: 534, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.34, #queue-req: 0, 
[2025-07-28 00:02:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 612, full token usage: 0.00, #swa token: 612, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.62, #queue-req: 0, 
[2025-07-28 00:02:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 574, full token usage: 0.00, #swa token: 574, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 99.88, #queue-req: 0, 
[2025-07-28 00:02:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 652, full token usage: 0.00, #swa token: 652, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.76, #queue-req: 0, 
[2025-07-28 00:02:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 614, full token usage: 0.00, #swa token: 614, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.12, #queue-req: 0, 
[2025-07-28 00:02:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 692, full token usage: 0.00, #swa token: 692, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.56, #queue-req: 0, 
[2025-07-28 00:02:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 654, full token usage: 0.00, #swa token: 654, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.16, #queue-req: 0, 
[2025-07-28 00:02:25] INFO:     127.0.0.1:56556 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:02:25 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 93, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:02:25 DP0 TP0] Decode batch. #running-req: 2, #full token: 835, full token usage: 0.00, #swa token: 835, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 105.09, #queue-req: 0, 
[2025-07-28 00:02:26 DP0 TP0] Decode batch. #running-req: 2, #full token: 915, full token usage: 0.00, #swa token: 915, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 188.25, #queue-req: 0, 
[2025-07-28 00:02:26 DP0 TP0] Decode batch. #running-req: 2, #full token: 995, full token usage: 0.00, #swa token: 995, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 188.12, #queue-req: 0, 
[2025-07-28 00:02:26] INFO:     127.0.0.1:56546 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:02:26 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 212, #cached-token: 98, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:02:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 328, full token usage: 0.00, #swa token: 328, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 25.47, #queue-req: 0, 
[2025-07-28 00:02:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 320, full token usage: 0.00, #swa token: 320, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 105.68, #queue-req: 0, 
[2025-07-28 00:02:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 368, full token usage: 0.00, #swa token: 368, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.46, #queue-req: 0, 
[2025-07-28 00:02:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 360, full token usage: 0.00, #swa token: 360, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.78, #queue-req: 0, 
[2025-07-28 00:02:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 408, full token usage: 0.00, #swa token: 408, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.02, #queue-req: 0, 
[2025-07-28 00:02:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 400, full token usage: 0.00, #swa token: 400, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.98, #queue-req: 0, 
[2025-07-28 00:02:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 448, full token usage: 0.00, #swa token: 448, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.59, #queue-req: 0, 
[2025-07-28 00:02:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 440, full token usage: 0.00, #swa token: 440, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.70, #queue-req: 0, 
[2025-07-28 00:02:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 488, full token usage: 0.00, #swa token: 488, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.54, #queue-req: 0, 
[2025-07-28 00:02:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 480, full token usage: 0.00, #swa token: 480, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.08, #queue-req: 0, 
[2025-07-28 00:02:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 528, full token usage: 0.00, #swa token: 528, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.37, #queue-req: 0, 
[2025-07-28 00:02:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 520, full token usage: 0.00, #swa token: 520, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.01, #queue-req: 0, 
[2025-07-28 00:02:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 568, full token usage: 0.00, #swa token: 568, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.58, #queue-req: 0, 
[2025-07-28 00:02:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 560, full token usage: 0.00, #swa token: 560, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.76, #queue-req: 0, 
[2025-07-28 00:02:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 608, full token usage: 0.00, #swa token: 608, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.28, #queue-req: 0, 
[2025-07-28 00:02:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 600, full token usage: 0.00, #swa token: 600, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.76, #queue-req: 0, 
[2025-07-28 00:02:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 648, full token usage: 0.00, #swa token: 648, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.38, #queue-req: 0, 
[2025-07-28 00:02:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 640, full token usage: 0.00, #swa token: 640, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.73, #queue-req: 0, 
[2025-07-28 00:02:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 688, full token usage: 0.00, #swa token: 688, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.23, #queue-req: 0, 
[2025-07-28 00:02:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 680, full token usage: 0.00, #swa token: 680, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.71, #queue-req: 0, 
[2025-07-28 00:02:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 728, full token usage: 0.00, #swa token: 728, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.49, #queue-req: 0, 
[2025-07-28 00:02:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 720, full token usage: 0.00, #swa token: 720, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.60, #queue-req: 0, 
[2025-07-28 00:02:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 768, full token usage: 0.00, #swa token: 768, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.52, #queue-req: 0, 
[2025-07-28 00:02:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 760, full token usage: 0.00, #swa token: 760, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.69, #queue-req: 0, 
[2025-07-28 00:02:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 808, full token usage: 0.00, #swa token: 808, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.53, #queue-req: 0, 
[2025-07-28 00:02:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 800, full token usage: 0.00, #swa token: 800, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.90, #queue-req: 0, 
[2025-07-28 00:02:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 848, full token usage: 0.00, #swa token: 848, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.47, #queue-req: 0, 
[2025-07-28 00:02:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 840, full token usage: 0.00, #swa token: 840, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.76, #queue-req: 0, 
[2025-07-28 00:02:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 888, full token usage: 0.00, #swa token: 888, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.26, #queue-req: 0, 
[2025-07-28 00:02:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 880, full token usage: 0.00, #swa token: 880, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.75, #queue-req: 0, 
[2025-07-28 00:02:32] INFO:     127.0.0.1:41612 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:02:32 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 134, #cached-token: 98, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:02:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 928, full token usage: 0.00, #swa token: 928, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.49, #queue-req: 0, 
[2025-07-28 00:02:33 DP0 TP0] Decode batch. #running-req: 1, #full token: 265, full token usage: 0.00, #swa token: 265, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 81.34, #queue-req: 0, 
[2025-07-28 00:02:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 968, full token usage: 0.00, #swa token: 968, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.32, #queue-req: 0, 
[2025-07-28 00:02:33 DP0 TP0] Decode batch. #running-req: 1, #full token: 305, full token usage: 0.00, #swa token: 305, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.05, #queue-req: 0, 
[2025-07-28 00:02:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 1008, full token usage: 0.00, #swa token: 1008, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.66, #queue-req: 0, 
[2025-07-28 00:02:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 345, full token usage: 0.00, #swa token: 345, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.06, #queue-req: 0, 
[2025-07-28 00:02:34] INFO:     127.0.0.1:41616 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:02:34 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 136, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:02:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 243, full token usage: 0.00, #swa token: 243, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 84.15, #queue-req: 0, 
[2025-07-28 00:02:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 385, full token usage: 0.00, #swa token: 385, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.13, #queue-req: 0, 
[2025-07-28 00:02:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 283, full token usage: 0.00, #swa token: 283, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.64, #queue-req: 0, 
[2025-07-28 00:02:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 425, full token usage: 0.00, #swa token: 425, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.09, #queue-req: 0, 
[2025-07-28 00:02:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 323, full token usage: 0.00, #swa token: 323, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.67, #queue-req: 0, 
[2025-07-28 00:02:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 465, full token usage: 0.00, #swa token: 465, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.96, #queue-req: 0, 
[2025-07-28 00:02:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 363, full token usage: 0.00, #swa token: 363, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.42, #queue-req: 0, 
[2025-07-28 00:02:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 505, full token usage: 0.00, #swa token: 505, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.89, #queue-req: 0, 
[2025-07-28 00:02:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 403, full token usage: 0.00, #swa token: 403, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.58, #queue-req: 0, 
[2025-07-28 00:02:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 545, full token usage: 0.00, #swa token: 545, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.74, #queue-req: 0, 
[2025-07-28 00:02:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 443, full token usage: 0.00, #swa token: 443, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.56, #queue-req: 0, 
[2025-07-28 00:02:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 585, full token usage: 0.00, #swa token: 585, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.58, #queue-req: 0, 
[2025-07-28 00:02:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 483, full token usage: 0.00, #swa token: 483, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.58, #queue-req: 0, 
[2025-07-28 00:02:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 625, full token usage: 0.00, #swa token: 625, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.79, #queue-req: 0, 
[2025-07-28 00:02:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 523, full token usage: 0.00, #swa token: 523, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.60, #queue-req: 0, 
[2025-07-28 00:02:37 DP0 TP0] Decode batch. #running-req: 1, #full token: 665, full token usage: 0.00, #swa token: 665, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.73, #queue-req: 0, 
[2025-07-28 00:02:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 563, full token usage: 0.00, #swa token: 563, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.54, #queue-req: 0, 
[2025-07-28 00:02:37 DP0 TP0] Decode batch. #running-req: 1, #full token: 705, full token usage: 0.00, #swa token: 705, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.80, #queue-req: 0, 
[2025-07-28 00:02:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 603, full token usage: 0.00, #swa token: 603, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.50, #queue-req: 0, 
[2025-07-28 00:02:37 DP0 TP0] Decode batch. #running-req: 1, #full token: 745, full token usage: 0.00, #swa token: 745, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.75, #queue-req: 0, 
[2025-07-28 00:02:38] INFO:     127.0.0.1:50174 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:02:38 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 193, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:02:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 643, full token usage: 0.00, #swa token: 643, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.41, #queue-req: 0, 
[2025-07-28 00:02:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 299, full token usage: 0.00, #swa token: 299, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 85.47, #queue-req: 0, 
[2025-07-28 00:02:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 683, full token usage: 0.00, #swa token: 683, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.40, #queue-req: 0, 
[2025-07-28 00:02:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 339, full token usage: 0.00, #swa token: 339, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.95, #queue-req: 0, 
[2025-07-28 00:02:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 723, full token usage: 0.00, #swa token: 723, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.39, #queue-req: 0, 
[2025-07-28 00:02:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 379, full token usage: 0.00, #swa token: 379, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.00, #queue-req: 0, 
[2025-07-28 00:02:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 763, full token usage: 0.00, #swa token: 763, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.38, #queue-req: 0, 
[2025-07-28 00:02:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 419, full token usage: 0.00, #swa token: 419, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.94, #queue-req: 0, 
[2025-07-28 00:02:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 803, full token usage: 0.00, #swa token: 803, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.31, #queue-req: 0, 
[2025-07-28 00:02:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 459, full token usage: 0.00, #swa token: 459, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.63, #queue-req: 0, 
[2025-07-28 00:02:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 843, full token usage: 0.00, #swa token: 843, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.49, #queue-req: 0, 
[2025-07-28 00:02:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 499, full token usage: 0.00, #swa token: 499, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.35, #queue-req: 0, 
[2025-07-28 00:02:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 883, full token usage: 0.00, #swa token: 883, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.50, #queue-req: 0, 
[2025-07-28 00:02:40] INFO:     127.0.0.1:50182 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:02:40 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 181, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:02:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 539, full token usage: 0.00, #swa token: 539, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.69, #queue-req: 0, 
[2025-07-28 00:02:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 310, full token usage: 0.00, #swa token: 310, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 85.11, #queue-req: 0, 
[2025-07-28 00:02:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 579, full token usage: 0.00, #swa token: 579, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.81, #queue-req: 0, 
[2025-07-28 00:02:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 350, full token usage: 0.00, #swa token: 350, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.73, #queue-req: 0, 
[2025-07-28 00:02:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 619, full token usage: 0.00, #swa token: 619, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 98.01, #queue-req: 0, 
[2025-07-28 00:02:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 390, full token usage: 0.00, #swa token: 390, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.69, #queue-req: 0, 
[2025-07-28 00:02:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 659, full token usage: 0.00, #swa token: 659, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.84, #queue-req: 0, 
[2025-07-28 00:02:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 430, full token usage: 0.00, #swa token: 430, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.63, #queue-req: 0, 
[2025-07-28 00:02:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 699, full token usage: 0.00, #swa token: 699, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.98, #queue-req: 0, 
[2025-07-28 00:02:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 470, full token usage: 0.00, #swa token: 470, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.67, #queue-req: 0, 
[2025-07-28 00:02:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 739, full token usage: 0.00, #swa token: 739, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.94, #queue-req: 0, 
[2025-07-28 00:02:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 510, full token usage: 0.00, #swa token: 510, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.59, #queue-req: 0, 
[2025-07-28 00:02:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 779, full token usage: 0.00, #swa token: 779, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.21, #queue-req: 0, 
[2025-07-28 00:02:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 550, full token usage: 0.00, #swa token: 550, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.47, #queue-req: 0, 
[2025-07-28 00:02:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 819, full token usage: 0.00, #swa token: 819, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.42, #queue-req: 0, 
[2025-07-28 00:02:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 590, full token usage: 0.00, #swa token: 590, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.53, #queue-req: 0, 
[2025-07-28 00:02:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 859, full token usage: 0.00, #swa token: 859, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.67, #queue-req: 0, 
[2025-07-28 00:02:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 630, full token usage: 0.00, #swa token: 630, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.50, #queue-req: 0, 
[2025-07-28 00:02:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 899, full token usage: 0.00, #swa token: 899, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.31, #queue-req: 0, 
[2025-07-28 00:02:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 670, full token usage: 0.00, #swa token: 670, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.64, #queue-req: 0, 
[2025-07-28 00:02:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 939, full token usage: 0.00, #swa token: 939, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.67, #queue-req: 0, 
[2025-07-28 00:02:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 710, full token usage: 0.00, #swa token: 710, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.53, #queue-req: 0, 
[2025-07-28 00:02:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 979, full token usage: 0.00, #swa token: 979, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.68, #queue-req: 0, 
[2025-07-28 00:02:45] INFO:     127.0.0.1:50192 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:02:45 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 222, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:02:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 750, full token usage: 0.00, #swa token: 750, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.39, #queue-req: 0, 
[2025-07-28 00:02:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 331, full token usage: 0.00, #swa token: 331, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 83.68, #queue-req: 0, 
[2025-07-28 00:02:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 790, full token usage: 0.00, #swa token: 790, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.46, #queue-req: 0, 
[2025-07-28 00:02:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 371, full token usage: 0.00, #swa token: 371, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 99.83, #queue-req: 0, 
[2025-07-28 00:02:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 830, full token usage: 0.00, #swa token: 830, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.35, #queue-req: 0, 
[2025-07-28 00:02:46] INFO:     127.0.0.1:50206 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:02:46 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 173, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:02:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 411, full token usage: 0.00, #swa token: 411, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.00, #queue-req: 0, 
[2025-07-28 00:02:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 303, full token usage: 0.00, #swa token: 303, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 83.30, #queue-req: 0, 
[2025-07-28 00:02:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 451, full token usage: 0.00, #swa token: 451, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.14, #queue-req: 0, 
[2025-07-28 00:02:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 343, full token usage: 0.00, #swa token: 343, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.70, #queue-req: 0, 
[2025-07-28 00:02:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 491, full token usage: 0.00, #swa token: 491, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.07, #queue-req: 0, 
[2025-07-28 00:02:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 383, full token usage: 0.00, #swa token: 383, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.70, #queue-req: 0, 
[2025-07-28 00:02:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 531, full token usage: 0.00, #swa token: 531, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.08, #queue-req: 0, 
[2025-07-28 00:02:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 423, full token usage: 0.00, #swa token: 423, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.40, #queue-req: 0, 
[2025-07-28 00:02:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 571, full token usage: 0.00, #swa token: 571, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.39, #queue-req: 0, 
[2025-07-28 00:02:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 463, full token usage: 0.00, #swa token: 463, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.54, #queue-req: 0, 
[2025-07-28 00:02:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 611, full token usage: 0.00, #swa token: 611, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.92, #queue-req: 0, 
[2025-07-28 00:02:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 503, full token usage: 0.00, #swa token: 503, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.79, #queue-req: 0, 
[2025-07-28 00:02:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 651, full token usage: 0.00, #swa token: 651, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.71, #queue-req: 0, 
[2025-07-28 00:02:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 543, full token usage: 0.00, #swa token: 543, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.59, #queue-req: 0, 
[2025-07-28 00:02:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 691, full token usage: 0.00, #swa token: 691, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.87, #queue-req: 0, 
[2025-07-28 00:02:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 583, full token usage: 0.00, #swa token: 583, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.59, #queue-req: 0, 
[2025-07-28 00:02:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 731, full token usage: 0.00, #swa token: 731, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.87, #queue-req: 0, 
[2025-07-28 00:02:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 623, full token usage: 0.00, #swa token: 623, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.51, #queue-req: 0, 
[2025-07-28 00:02:50 DP0 TP0] Decode batch. #running-req: 1, #full token: 771, full token usage: 0.00, #swa token: 771, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.72, #queue-req: 0, 
[2025-07-28 00:02:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 663, full token usage: 0.00, #swa token: 663, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.56, #queue-req: 0, 
[2025-07-28 00:02:50 DP0 TP0] Decode batch. #running-req: 1, #full token: 811, full token usage: 0.00, #swa token: 811, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.98, #queue-req: 0, 
[2025-07-28 00:02:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 703, full token usage: 0.00, #swa token: 703, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.60, #queue-req: 0, 
[2025-07-28 00:02:50 DP0 TP0] Decode batch. #running-req: 1, #full token: 851, full token usage: 0.00, #swa token: 851, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.73, #queue-req: 0, 
[2025-07-28 00:02:50] INFO:     127.0.0.1:50580 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:02:50 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 158, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:02:51] INFO:     127.0.0.1:50564 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:02:51 DP0 TP0] Decode batch. #running-req: 2, #full token: 294, full token usage: 0.00, #swa token: 294, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 155.60, #queue-req: 0, 
[2025-07-28 00:02:51 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 149, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:02:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 334, full token usage: 0.00, #swa token: 334, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.64, #queue-req: 0, 
[2025-07-28 00:02:51 DP1 TP0] Decode batch. #running-req: 1, #full token: 280, full token usage: 0.00, #swa token: 280, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 41.74, #queue-req: 0, 
[2025-07-28 00:02:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 374, full token usage: 0.00, #swa token: 374, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.64, #queue-req: 0, 
[2025-07-28 00:02:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 320, full token usage: 0.00, #swa token: 320, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.71, #queue-req: 0, 
[2025-07-28 00:02:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 414, full token usage: 0.00, #swa token: 414, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.69, #queue-req: 0, 
[2025-07-28 00:02:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 360, full token usage: 0.00, #swa token: 360, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.70, #queue-req: 0, 
[2025-07-28 00:02:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 454, full token usage: 0.00, #swa token: 454, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.60, #queue-req: 0, 
[2025-07-28 00:02:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 400, full token usage: 0.00, #swa token: 400, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.62, #queue-req: 0, 
[2025-07-28 00:02:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 494, full token usage: 0.00, #swa token: 494, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.71, #queue-req: 0, 
[2025-07-28 00:02:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 440, full token usage: 0.00, #swa token: 440, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.57, #queue-req: 0, 
[2025-07-28 00:02:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 534, full token usage: 0.00, #swa token: 534, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.57, #queue-req: 0, 
[2025-07-28 00:02:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 480, full token usage: 0.00, #swa token: 480, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.74, #queue-req: 0, 
[2025-07-28 00:02:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 574, full token usage: 0.00, #swa token: 574, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.51, #queue-req: 0, 
[2025-07-28 00:02:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 520, full token usage: 0.00, #swa token: 520, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.51, #queue-req: 0, 
[2025-07-28 00:02:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 614, full token usage: 0.00, #swa token: 614, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.63, #queue-req: 0, 
[2025-07-28 00:02:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 560, full token usage: 0.00, #swa token: 560, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.48, #queue-req: 0, 
[2025-07-28 00:02:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 654, full token usage: 0.00, #swa token: 654, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.57, #queue-req: 0, 
[2025-07-28 00:02:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 600, full token usage: 0.00, #swa token: 600, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.55, #queue-req: 0, 
[2025-07-28 00:02:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 694, full token usage: 0.00, #swa token: 694, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.55, #queue-req: 0, 
[2025-07-28 00:02:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 640, full token usage: 0.00, #swa token: 640, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.58, #queue-req: 0, 
[2025-07-28 00:02:55] INFO:     127.0.0.1:50592 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:02:55 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 186, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:02:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 680, full token usage: 0.00, #swa token: 680, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.57, #queue-req: 0, 
[2025-07-28 00:02:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 288, full token usage: 0.00, #swa token: 288, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 83.15, #queue-req: 0, 
[2025-07-28 00:02:56] INFO:     127.0.0.1:50598 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:02:56 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 196, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:02:56 DP0 TP0] Decode batch. #running-req: 1, #full token: 328, full token usage: 0.00, #swa token: 328, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.65, #queue-req: 0, 
[2025-07-28 00:02:56 DP1 TP0] Decode batch. #running-req: 1, #full token: 309, full token usage: 0.00, #swa token: 309, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 84.78, #queue-req: 0, 
[2025-07-28 00:02:56 DP0 TP0] Decode batch. #running-req: 1, #full token: 368, full token usage: 0.00, #swa token: 368, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.64, #queue-req: 0, 
[2025-07-28 00:02:56 DP1 TP0] Decode batch. #running-req: 1, #full token: 349, full token usage: 0.00, #swa token: 349, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.69, #queue-req: 0, 
[2025-07-28 00:02:56 DP0 TP0] Decode batch. #running-req: 1, #full token: 408, full token usage: 0.00, #swa token: 408, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.53, #queue-req: 0, 
[2025-07-28 00:02:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 389, full token usage: 0.00, #swa token: 389, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.80, #queue-req: 0, 
[2025-07-28 00:02:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 448, full token usage: 0.00, #swa token: 448, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.54, #queue-req: 0, 
[2025-07-28 00:02:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 429, full token usage: 0.00, #swa token: 429, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.72, #queue-req: 0, 
[2025-07-28 00:02:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 488, full token usage: 0.00, #swa token: 488, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.41, #queue-req: 0, 
[2025-07-28 00:02:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 469, full token usage: 0.00, #swa token: 469, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.72, #queue-req: 0, 
[2025-07-28 00:02:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 528, full token usage: 0.00, #swa token: 528, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.63, #queue-req: 0, 
[2025-07-28 00:02:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 509, full token usage: 0.00, #swa token: 509, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.63, #queue-req: 0, 
[2025-07-28 00:02:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 568, full token usage: 0.00, #swa token: 568, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.89, #queue-req: 0, 
[2025-07-28 00:02:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 549, full token usage: 0.00, #swa token: 549, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.58, #queue-req: 0, 
[2025-07-28 00:02:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 608, full token usage: 0.00, #swa token: 608, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.93, #queue-req: 0, 
[2025-07-28 00:02:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 589, full token usage: 0.00, #swa token: 589, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.59, #queue-req: 0, 
[2025-07-28 00:02:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 648, full token usage: 0.00, #swa token: 648, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.90, #queue-req: 0, 
[2025-07-28 00:02:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 629, full token usage: 0.00, #swa token: 629, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.66, #queue-req: 0, 
[2025-07-28 00:02:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 688, full token usage: 0.00, #swa token: 688, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.67, #queue-req: 0, 
[2025-07-28 00:02:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 669, full token usage: 0.00, #swa token: 669, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.45, #queue-req: 0, 
[2025-07-28 00:03:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 728, full token usage: 0.00, #swa token: 728, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.75, #queue-req: 0, 
[2025-07-28 00:03:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 709, full token usage: 0.00, #swa token: 709, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.32, #queue-req: 0, 
[2025-07-28 00:03:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 768, full token usage: 0.00, #swa token: 768, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.86, #queue-req: 0, 
[2025-07-28 00:03:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 749, full token usage: 0.00, #swa token: 749, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.61, #queue-req: 0, 
[2025-07-28 00:03:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 808, full token usage: 0.00, #swa token: 808, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 99.80, #queue-req: 0, 
[2025-07-28 00:03:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 789, full token usage: 0.00, #swa token: 789, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.59, #queue-req: 0, 
[2025-07-28 00:03:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 848, full token usage: 0.00, #swa token: 848, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.81, #queue-req: 0, 
[2025-07-28 00:03:01 DP1 TP0] Decode batch. #running-req: 1, #full token: 829, full token usage: 0.00, #swa token: 829, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.57, #queue-req: 0, 
[2025-07-28 00:03:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 888, full token usage: 0.00, #swa token: 888, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.90, #queue-req: 0, 
[2025-07-28 00:03:01] INFO:     127.0.0.1:47220 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:03:01 DP1 TP0] Decode batch. #running-req: 1, #full token: 869, full token usage: 0.00, #swa token: 869, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.52, #queue-req: 0, 
[2025-07-28 00:03:01 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:03:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 909, full token usage: 0.00, #swa token: 909, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.64, #queue-req: 0, 
[2025-07-28 00:03:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 282, full token usage: 0.00, #swa token: 282, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 84.56, #queue-req: 0, 
[2025-07-28 00:03:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 949, full token usage: 0.00, #swa token: 949, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.60, #queue-req: 0, 
[2025-07-28 00:03:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 322, full token usage: 0.00, #swa token: 322, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.09, #queue-req: 0, 
[2025-07-28 00:03:02] INFO:     127.0.0.1:47232 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:03:02 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 134, #cached-token: 101, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:03:03 DP0 TP0] Decode batch. #running-req: 1, #full token: 362, full token usage: 0.00, #swa token: 362, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.05, #queue-req: 0, 
[2025-07-28 00:03:03 DP1 TP0] Decode batch. #running-req: 1, #full token: 259, full token usage: 0.00, #swa token: 259, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 84.57, #queue-req: 0, 
[2025-07-28 00:03:03 DP0 TP0] Decode batch. #running-req: 1, #full token: 402, full token usage: 0.00, #swa token: 402, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.14, #queue-req: 0, 
[2025-07-28 00:03:03 DP1 TP0] Decode batch. #running-req: 1, #full token: 299, full token usage: 0.00, #swa token: 299, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.74, #queue-req: 0, 
[2025-07-28 00:03:03 DP0 TP0] Decode batch. #running-req: 1, #full token: 442, full token usage: 0.00, #swa token: 442, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.15, #queue-req: 0, 
[2025-07-28 00:03:03 DP1 TP0] Decode batch. #running-req: 1, #full token: 339, full token usage: 0.00, #swa token: 339, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.74, #queue-req: 0, 
[2025-07-28 00:03:04 DP0 TP0] Decode batch. #running-req: 1, #full token: 482, full token usage: 0.00, #swa token: 482, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.16, #queue-req: 0, 
[2025-07-28 00:03:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 379, full token usage: 0.00, #swa token: 379, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.78, #queue-req: 0, 
[2025-07-28 00:03:04 DP0 TP0] Decode batch. #running-req: 1, #full token: 522, full token usage: 0.00, #swa token: 522, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.56, #queue-req: 0, 
[2025-07-28 00:03:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 419, full token usage: 0.00, #swa token: 419, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.43, #queue-req: 0, 
[2025-07-28 00:03:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 562, full token usage: 0.00, #swa token: 562, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.54, #queue-req: 0, 
[2025-07-28 00:03:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 459, full token usage: 0.00, #swa token: 459, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.52, #queue-req: 0, 
[2025-07-28 00:03:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 602, full token usage: 0.00, #swa token: 602, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.89, #queue-req: 0, 
[2025-07-28 00:03:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 499, full token usage: 0.00, #swa token: 499, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.73, #queue-req: 0, 
[2025-07-28 00:03:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 642, full token usage: 0.00, #swa token: 642, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.92, #queue-req: 0, 
[2025-07-28 00:03:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 539, full token usage: 0.00, #swa token: 539, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.65, #queue-req: 0, 
[2025-07-28 00:03:06 DP0 TP0] Decode batch. #running-req: 1, #full token: 682, full token usage: 0.00, #swa token: 682, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.62, #queue-req: 0, 
[2025-07-28 00:03:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 579, full token usage: 0.00, #swa token: 579, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.56, #queue-req: 0, 
[2025-07-28 00:03:06 DP0 TP0] Decode batch. #running-req: 1, #full token: 722, full token usage: 0.00, #swa token: 722, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.00, #queue-req: 0, 
[2025-07-28 00:03:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 619, full token usage: 0.00, #swa token: 619, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.58, #queue-req: 0, 
[2025-07-28 00:03:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 762, full token usage: 0.00, #swa token: 762, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.70, #queue-req: 0, 
[2025-07-28 00:03:07 DP1 TP0] Decode batch. #running-req: 1, #full token: 659, full token usage: 0.00, #swa token: 659, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.62, #queue-req: 0, 
[2025-07-28 00:03:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 802, full token usage: 0.00, #swa token: 802, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.66, #queue-req: 0, 
[2025-07-28 00:03:07 DP1 TP0] Decode batch. #running-req: 1, #full token: 699, full token usage: 0.00, #swa token: 699, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.52, #queue-req: 0, 
[2025-07-28 00:03:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 842, full token usage: 0.00, #swa token: 842, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.61, #queue-req: 0, 
[2025-07-28 00:03:07 DP1 TP0] Decode batch. #running-req: 1, #full token: 739, full token usage: 0.00, #swa token: 739, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.61, #queue-req: 0, 
[2025-07-28 00:03:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 882, full token usage: 0.00, #swa token: 882, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.55, #queue-req: 0, 
[2025-07-28 00:03:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 779, full token usage: 0.00, #swa token: 779, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.55, #queue-req: 0, 
[2025-07-28 00:03:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 922, full token usage: 0.00, #swa token: 922, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.49, #queue-req: 0, 
[2025-07-28 00:03:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 819, full token usage: 0.00, #swa token: 819, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.47, #queue-req: 0, 
[2025-07-28 00:03:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 962, full token usage: 0.00, #swa token: 962, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.56, #queue-req: 0, 
[2025-07-28 00:03:09 DP1 TP0] Decode batch. #running-req: 1, #full token: 859, full token usage: 0.00, #swa token: 859, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.55, #queue-req: 0, 
[2025-07-28 00:03:09] INFO:     127.0.0.1:47246 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:03:09 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 200, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:03:09 DP1 TP0] Decode batch. #running-req: 1, #full token: 899, full token usage: 0.00, #swa token: 899, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.58, #queue-req: 0, 
[2025-07-28 00:03:09 DP0 TP0] Decode batch. #running-req: 1, #full token: 334, full token usage: 0.00, #swa token: 334, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 84.70, #queue-req: 0, 
[2025-07-28 00:03:09 DP1 TP0] Decode batch. #running-req: 1, #full token: 939, full token usage: 0.00, #swa token: 939, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.55, #queue-req: 0, 
[2025-07-28 00:03:09 DP0 TP0] Decode batch. #running-req: 1, #full token: 374, full token usage: 0.00, #swa token: 374, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.94, #queue-req: 0, 
[2025-07-28 00:03:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 979, full token usage: 0.00, #swa token: 979, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.50, #queue-req: 0, 
[2025-07-28 00:03:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 414, full token usage: 0.00, #swa token: 414, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.91, #queue-req: 0, 
[2025-07-28 00:03:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 1019, full token usage: 0.00, #swa token: 1019, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.56, #queue-req: 0, 
[2025-07-28 00:03:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 454, full token usage: 0.00, #swa token: 454, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.87, #queue-req: 0, 
[2025-07-28 00:03:10] INFO:     127.0.0.1:43716 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:03:10 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 189, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:03:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 494, full token usage: 0.00, #swa token: 494, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.88, #queue-req: 0, 
[2025-07-28 00:03:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 302, full token usage: 0.00, #swa token: 302, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 84.40, #queue-req: 0, 
[2025-07-28 00:03:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 534, full token usage: 0.00, #swa token: 534, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.75, #queue-req: 0, 
[2025-07-28 00:03:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 342, full token usage: 0.00, #swa token: 342, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.76, #queue-req: 0, 
[2025-07-28 00:03:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 574, full token usage: 0.00, #swa token: 574, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.63, #queue-req: 0, 
[2025-07-28 00:03:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 382, full token usage: 0.00, #swa token: 382, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.74, #queue-req: 0, 
[2025-07-28 00:03:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 614, full token usage: 0.00, #swa token: 614, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.66, #queue-req: 0, 
[2025-07-28 00:03:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 422, full token usage: 0.00, #swa token: 422, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.68, #queue-req: 0, 
[2025-07-28 00:03:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 654, full token usage: 0.00, #swa token: 654, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.69, #queue-req: 0, 
[2025-07-28 00:03:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 462, full token usage: 0.00, #swa token: 462, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.76, #queue-req: 0, 
[2025-07-28 00:03:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 694, full token usage: 0.00, #swa token: 694, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.67, #queue-req: 0, 
[2025-07-28 00:03:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 502, full token usage: 0.00, #swa token: 502, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.78, #queue-req: 0, 
[2025-07-28 00:03:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 734, full token usage: 0.00, #swa token: 734, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.67, #queue-req: 0, 
[2025-07-28 00:03:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 542, full token usage: 0.00, #swa token: 542, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.59, #queue-req: 0, 
[2025-07-28 00:03:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 774, full token usage: 0.00, #swa token: 774, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.66, #queue-req: 0, 
[2025-07-28 00:03:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 582, full token usage: 0.00, #swa token: 582, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.60, #queue-req: 0, 
[2025-07-28 00:03:14] INFO:     127.0.0.1:43726 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:03:14 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 175, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:03:14 DP0 TP0] Decode batch. #running-req: 2, #full token: 1006, full token usage: 0.00, #swa token: 1006, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 118.99, #queue-req: 0, 
[2025-07-28 00:03:14 DP0 TP0] Decode batch. #running-req: 2, #full token: 1086, full token usage: 0.00, #swa token: 1086, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 188.03, #queue-req: 0, 
[2025-07-28 00:03:15 DP0 TP0] Decode batch. #running-req: 2, #full token: 1166, full token usage: 0.00, #swa token: 1166, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 188.46, #queue-req: 0, 
[2025-07-28 00:03:15] INFO:     127.0.0.1:43724 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:03:15 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 177, #cached-token: 101, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:03:15 DP0 TP0] Decode batch. #running-req: 1, #full token: 411, full token usage: 0.00, #swa token: 411, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 179.93, #queue-req: 0, 
[2025-07-28 00:03:15 DP1 TP0] Decode batch. #running-req: 1, #full token: 300, full token usage: 0.00, #swa token: 300, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 20.43, #queue-req: 0, 
[2025-07-28 00:03:15 DP0 TP0] Decode batch. #running-req: 1, #full token: 451, full token usage: 0.00, #swa token: 451, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 97.57, #queue-req: 0, 
[2025-07-28 00:03:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 340, full token usage: 0.00, #swa token: 340, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.89, #queue-req: 0, 
[2025-07-28 00:03:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 491, full token usage: 0.00, #swa token: 491, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.09, #queue-req: 0, 
[2025-07-28 00:03:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 380, full token usage: 0.00, #swa token: 380, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.82, #queue-req: 0, 
[2025-07-28 00:03:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 531, full token usage: 0.00, #swa token: 531, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.05, #queue-req: 0, 
[2025-07-28 00:03:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 420, full token usage: 0.00, #swa token: 420, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.83, #queue-req: 0, 
[2025-07-28 00:03:17 DP0 TP0] Decode batch. #running-req: 1, #full token: 571, full token usage: 0.00, #swa token: 571, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.03, #queue-req: 0, 
[2025-07-28 00:03:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 460, full token usage: 0.00, #swa token: 460, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.79, #queue-req: 0, 
[2025-07-28 00:03:17 DP0 TP0] Decode batch. #running-req: 1, #full token: 611, full token usage: 0.00, #swa token: 611, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.98, #queue-req: 0, 
[2025-07-28 00:03:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 500, full token usage: 0.00, #swa token: 500, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.83, #queue-req: 0, 
[2025-07-28 00:03:17 DP0 TP0] Decode batch. #running-req: 1, #full token: 651, full token usage: 0.00, #swa token: 651, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.01, #queue-req: 0, 
[2025-07-28 00:03:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 540, full token usage: 0.00, #swa token: 540, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.69, #queue-req: 0, 
[2025-07-28 00:03:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 691, full token usage: 0.00, #swa token: 691, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.03, #queue-req: 0, 
[2025-07-28 00:03:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 580, full token usage: 0.00, #swa token: 580, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.66, #queue-req: 0, 
[2025-07-28 00:03:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 731, full token usage: 0.00, #swa token: 731, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.90, #queue-req: 0, 
[2025-07-28 00:03:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 620, full token usage: 0.00, #swa token: 620, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.69, #queue-req: 0, 
[2025-07-28 00:03:19 DP0 TP0] Decode batch. #running-req: 1, #full token: 771, full token usage: 0.00, #swa token: 771, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.01, #queue-req: 0, 
[2025-07-28 00:03:19] INFO:     127.0.0.1:39460 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:03:19 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 210, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:03:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 660, full token usage: 0.00, #swa token: 660, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.57, #queue-req: 0, 
[2025-07-28 00:03:19 DP0 TP0] Decode batch. #running-req: 1, #full token: 329, full token usage: 0.00, #swa token: 329, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 85.15, #queue-req: 0, 
[2025-07-28 00:03:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 700, full token usage: 0.00, #swa token: 700, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.53, #queue-req: 0, 
[2025-07-28 00:03:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 369, full token usage: 0.00, #swa token: 369, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.17, #queue-req: 0, 
[2025-07-28 00:03:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 740, full token usage: 0.00, #swa token: 740, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.60, #queue-req: 0, 
[2025-07-28 00:03:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 409, full token usage: 0.00, #swa token: 409, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.10, #queue-req: 0, 
[2025-07-28 00:03:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 780, full token usage: 0.00, #swa token: 780, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.58, #queue-req: 0, 
[2025-07-28 00:03:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 449, full token usage: 0.00, #swa token: 449, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.91, #queue-req: 0, 
[2025-07-28 00:03:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 820, full token usage: 0.00, #swa token: 820, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.18, #queue-req: 0, 
[2025-07-28 00:03:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 489, full token usage: 0.00, #swa token: 489, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.15, #queue-req: 0, 
[2025-07-28 00:03:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 860, full token usage: 0.00, #swa token: 860, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.52, #queue-req: 0, 
[2025-07-28 00:03:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 529, full token usage: 0.00, #swa token: 529, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.13, #queue-req: 0, 
[2025-07-28 00:03:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 900, full token usage: 0.00, #swa token: 900, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.53, #queue-req: 0, 
[2025-07-28 00:03:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 569, full token usage: 0.00, #swa token: 569, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.94, #queue-req: 0, 
[2025-07-28 00:03:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 940, full token usage: 0.00, #swa token: 940, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.57, #queue-req: 0, 
[2025-07-28 00:03:22] INFO:     127.0.0.1:39462 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:03:22 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 161, #cached-token: 102, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:03:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 609, full token usage: 0.00, #swa token: 609, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.05, #queue-req: 0, 
[2025-07-28 00:03:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 297, full token usage: 0.00, #swa token: 297, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 84.68, #queue-req: 0, 
[2025-07-28 00:03:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 649, full token usage: 0.00, #swa token: 649, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.05, #queue-req: 0, 
[2025-07-28 00:03:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 337, full token usage: 0.00, #swa token: 337, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.79, #queue-req: 0, 
[2025-07-28 00:03:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 689, full token usage: 0.00, #swa token: 689, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.96, #queue-req: 0, 
[2025-07-28 00:03:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 377, full token usage: 0.00, #swa token: 377, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.85, #queue-req: 0, 
[2025-07-28 00:03:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 729, full token usage: 0.00, #swa token: 729, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.04, #queue-req: 0, 
[2025-07-28 00:03:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 417, full token usage: 0.00, #swa token: 417, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.77, #queue-req: 0, 
[2025-07-28 00:03:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 769, full token usage: 0.00, #swa token: 769, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.81, #queue-req: 0, 
[2025-07-28 00:03:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 457, full token usage: 0.00, #swa token: 457, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.80, #queue-req: 0, 
[2025-07-28 00:03:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 809, full token usage: 0.00, #swa token: 809, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.96, #queue-req: 0, 
[2025-07-28 00:03:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 497, full token usage: 0.00, #swa token: 497, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.78, #queue-req: 0, 
[2025-07-28 00:03:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 849, full token usage: 0.00, #swa token: 849, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.78, #queue-req: 0, 
[2025-07-28 00:03:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 537, full token usage: 0.00, #swa token: 537, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.68, #queue-req: 0, 
[2025-07-28 00:03:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 889, full token usage: 0.00, #swa token: 889, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.83, #queue-req: 0, 
[2025-07-28 00:03:25] INFO:     127.0.0.1:39476 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:03:25 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 172, #cached-token: 102, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:03:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 577, full token usage: 0.00, #swa token: 577, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.63, #queue-req: 0, 
[2025-07-28 00:03:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 304, full token usage: 0.00, #swa token: 304, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 84.92, #queue-req: 0, 
[2025-07-28 00:03:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 617, full token usage: 0.00, #swa token: 617, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.54, #queue-req: 0, 
[2025-07-28 00:03:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 344, full token usage: 0.00, #swa token: 344, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.13, #queue-req: 0, 
[2025-07-28 00:03:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 657, full token usage: 0.00, #swa token: 657, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.64, #queue-req: 0, 
[2025-07-28 00:03:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 384, full token usage: 0.00, #swa token: 384, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.12, #queue-req: 0, 
[2025-07-28 00:03:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 697, full token usage: 0.00, #swa token: 697, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.59, #queue-req: 0, 
[2025-07-28 00:03:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 424, full token usage: 0.00, #swa token: 424, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.10, #queue-req: 0, 
[2025-07-28 00:03:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 737, full token usage: 0.00, #swa token: 737, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.63, #queue-req: 0, 
[2025-07-28 00:03:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 464, full token usage: 0.00, #swa token: 464, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.17, #queue-req: 0, 
[2025-07-28 00:03:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 777, full token usage: 0.00, #swa token: 777, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.56, #queue-req: 0, 
[2025-07-28 00:03:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 504, full token usage: 0.00, #swa token: 504, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.12, #queue-req: 0, 
[2025-07-28 00:03:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 817, full token usage: 0.00, #swa token: 817, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.67, #queue-req: 0, 
[2025-07-28 00:03:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 544, full token usage: 0.00, #swa token: 544, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.10, #queue-req: 0, 
[2025-07-28 00:03:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 857, full token usage: 0.00, #swa token: 857, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.66, #queue-req: 0, 
[2025-07-28 00:03:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 584, full token usage: 0.00, #swa token: 584, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.98, #queue-req: 0, 
[2025-07-28 00:03:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 897, full token usage: 0.00, #swa token: 897, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.66, #queue-req: 0, 
[2025-07-28 00:03:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 624, full token usage: 0.00, #swa token: 624, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.02, #queue-req: 0, 
[2025-07-28 00:03:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 937, full token usage: 0.00, #swa token: 937, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.68, #queue-req: 0, 
[2025-07-28 00:03:29] INFO:     127.0.0.1:39478 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:03:29 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 102, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:03:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 664, full token usage: 0.00, #swa token: 664, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.00, #queue-req: 0, 
[2025-07-28 00:03:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 227, full token usage: 0.00, #swa token: 227, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 85.22, #queue-req: 0, 
[2025-07-28 00:03:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 704, full token usage: 0.00, #swa token: 704, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.05, #queue-req: 0, 
[2025-07-28 00:03:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 267, full token usage: 0.00, #swa token: 267, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.01, #queue-req: 0, 
[2025-07-28 00:03:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 744, full token usage: 0.00, #swa token: 744, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.84, #queue-req: 0, 
[2025-07-28 00:03:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 307, full token usage: 0.00, #swa token: 307, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.80, #queue-req: 0, 
[2025-07-28 00:03:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 784, full token usage: 0.00, #swa token: 784, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.88, #queue-req: 0, 
[2025-07-28 00:03:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 347, full token usage: 0.00, #swa token: 347, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.78, #queue-req: 0, 
[2025-07-28 00:03:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 824, full token usage: 0.00, #swa token: 824, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.89, #queue-req: 0, 
[2025-07-28 00:03:31] INFO:     127.0.0.1:54864 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:03:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 387, full token usage: 0.00, #swa token: 387, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.83, #queue-req: 0, 
[2025-07-28 00:03:31 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 120, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:03:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 229, full token usage: 0.00, #swa token: 229, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 85.48, #queue-req: 0, 
[2025-07-28 00:03:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 427, full token usage: 0.00, #swa token: 427, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.82, #queue-req: 0, 
[2025-07-28 00:03:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 269, full token usage: 0.00, #swa token: 269, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.34, #queue-req: 0, 
[2025-07-28 00:03:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 467, full token usage: 0.00, #swa token: 467, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.66, #queue-req: 0, 
[2025-07-28 00:03:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 309, full token usage: 0.00, #swa token: 309, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.12, #queue-req: 0, 
[2025-07-28 00:03:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 507, full token usage: 0.00, #swa token: 507, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.78, #queue-req: 0, 
[2025-07-28 00:03:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 349, full token usage: 0.00, #swa token: 349, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.11, #queue-req: 0, 
[2025-07-28 00:03:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 547, full token usage: 0.00, #swa token: 547, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.67, #queue-req: 0, 
[2025-07-28 00:03:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 389, full token usage: 0.00, #swa token: 389, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.13, #queue-req: 0, 
[2025-07-28 00:03:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 587, full token usage: 0.00, #swa token: 587, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.72, #queue-req: 0, 
[2025-07-28 00:03:33 DP0 TP0] Decode batch. #running-req: 1, #full token: 429, full token usage: 0.00, #swa token: 429, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.12, #queue-req: 0, 
[2025-07-28 00:03:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 627, full token usage: 0.00, #swa token: 627, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.37, #queue-req: 0, 
[2025-07-28 00:03:33 DP0 TP0] Decode batch. #running-req: 1, #full token: 469, full token usage: 0.00, #swa token: 469, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.14, #queue-req: 0, 
[2025-07-28 00:03:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 667, full token usage: 0.00, #swa token: 667, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.66, #queue-req: 0, 
[2025-07-28 00:03:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 509, full token usage: 0.00, #swa token: 509, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.06, #queue-req: 0, 
[2025-07-28 00:03:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 707, full token usage: 0.00, #swa token: 707, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.65, #queue-req: 0, 
[2025-07-28 00:03:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 549, full token usage: 0.00, #swa token: 549, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.00, #queue-req: 0, 
[2025-07-28 00:03:34] INFO:     127.0.0.1:54866 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:03:34 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 152, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:03:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 270, full token usage: 0.00, #swa token: 270, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 85.43, #queue-req: 0, 
[2025-07-28 00:03:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 589, full token usage: 0.00, #swa token: 589, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.93, #queue-req: 0, 
[2025-07-28 00:03:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 310, full token usage: 0.00, #swa token: 310, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.80, #queue-req: 0, 
[2025-07-28 00:03:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 629, full token usage: 0.00, #swa token: 629, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.94, #queue-req: 0, 
[2025-07-28 00:03:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 350, full token usage: 0.00, #swa token: 350, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.74, #queue-req: 0, 
[2025-07-28 00:03:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 669, full token usage: 0.00, #swa token: 669, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.90, #queue-req: 0, 
[2025-07-28 00:03:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 390, full token usage: 0.00, #swa token: 390, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.70, #queue-req: 0, 
[2025-07-28 00:03:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 709, full token usage: 0.00, #swa token: 709, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.98, #queue-req: 0, 
[2025-07-28 00:03:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 430, full token usage: 0.00, #swa token: 430, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.76, #queue-req: 0, 
[2025-07-28 00:03:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 749, full token usage: 0.00, #swa token: 749, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.86, #queue-req: 0, 
[2025-07-28 00:03:36] INFO:     127.0.0.1:54874 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:03:36 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 153, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:03:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 470, full token usage: 0.00, #swa token: 470, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.77, #queue-req: 0, 
[2025-07-28 00:03:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 267, full token usage: 0.00, #swa token: 267, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 86.09, #queue-req: 0, 
[2025-07-28 00:03:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 510, full token usage: 0.00, #swa token: 510, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.44, #queue-req: 0, 
[2025-07-28 00:03:37 DP0 TP0] Decode batch. #running-req: 1, #full token: 307, full token usage: 0.00, #swa token: 307, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.14, #queue-req: 0, 
[2025-07-28 00:03:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 550, full token usage: 0.00, #swa token: 550, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.64, #queue-req: 0, 
[2025-07-28 00:03:37 DP0 TP0] Decode batch. #running-req: 1, #full token: 347, full token usage: 0.00, #swa token: 347, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.16, #queue-req: 0, 
[2025-07-28 00:03:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 590, full token usage: 0.00, #swa token: 590, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.60, #queue-req: 0, 
[2025-07-28 00:03:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 387, full token usage: 0.00, #swa token: 387, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.18, #queue-req: 0, 
[2025-07-28 00:03:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 630, full token usage: 0.00, #swa token: 630, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.61, #queue-req: 0, 
[2025-07-28 00:03:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 427, full token usage: 0.00, #swa token: 427, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.23, #queue-req: 0, 
[2025-07-28 00:03:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 670, full token usage: 0.00, #swa token: 670, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.60, #queue-req: 0, 
[2025-07-28 00:03:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 467, full token usage: 0.00, #swa token: 467, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.10, #queue-req: 0, 
[2025-07-28 00:03:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 710, full token usage: 0.00, #swa token: 710, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.56, #queue-req: 0, 
[2025-07-28 00:03:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 507, full token usage: 0.00, #swa token: 507, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.20, #queue-req: 0, 
[2025-07-28 00:03:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 750, full token usage: 0.00, #swa token: 750, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.61, #queue-req: 0, 
[2025-07-28 00:03:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 547, full token usage: 0.00, #swa token: 547, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.03, #queue-req: 0, 
[2025-07-28 00:03:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 790, full token usage: 0.00, #swa token: 790, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.63, #queue-req: 0, 
[2025-07-28 00:03:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 587, full token usage: 0.00, #swa token: 587, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.00, #queue-req: 0, 
[2025-07-28 00:03:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 830, full token usage: 0.00, #swa token: 830, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.52, #queue-req: 0, 
[2025-07-28 00:03:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 627, full token usage: 0.00, #swa token: 627, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.01, #queue-req: 0, 
[2025-07-28 00:03:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 870, full token usage: 0.00, #swa token: 870, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.52, #queue-req: 0, 
[2025-07-28 00:03:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 667, full token usage: 0.00, #swa token: 667, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.98, #queue-req: 0, 
[2025-07-28 00:03:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 910, full token usage: 0.00, #swa token: 910, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.56, #queue-req: 0, 
[2025-07-28 00:03:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 707, full token usage: 0.00, #swa token: 707, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.95, #queue-req: 0, 
[2025-07-28 00:03:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 950, full token usage: 0.00, #swa token: 950, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.66, #queue-req: 0, 
[2025-07-28 00:03:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 747, full token usage: 0.00, #swa token: 747, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.00, #queue-req: 0, 
[2025-07-28 00:03:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 990, full token usage: 0.00, #swa token: 990, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.57, #queue-req: 0, 
[2025-07-28 00:03:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 787, full token usage: 0.00, #swa token: 787, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.01, #queue-req: 0, 
[2025-07-28 00:03:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 1030, full token usage: 0.00, #swa token: 1030, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.61, #queue-req: 0, 
[2025-07-28 00:03:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 827, full token usage: 0.00, #swa token: 827, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.05, #queue-req: 0, 
[2025-07-28 00:03:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 1070, full token usage: 0.00, #swa token: 1070, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.63, #queue-req: 0, 
[2025-07-28 00:03:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 867, full token usage: 0.00, #swa token: 867, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 97.34, #queue-req: 0, 
[2025-07-28 00:03:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 1110, full token usage: 0.00, #swa token: 1110, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.62, #queue-req: 0, 
[2025-07-28 00:03:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 907, full token usage: 0.00, #swa token: 907, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.99, #queue-req: 0, 
[2025-07-28 00:03:43] INFO:     127.0.0.1:44084 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:03:43 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 167, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:03:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 283, full token usage: 0.00, #swa token: 283, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 85.05, #queue-req: 0, 
[2025-07-28 00:03:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 947, full token usage: 0.00, #swa token: 947, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.02, #queue-req: 0, 
[2025-07-28 00:03:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 323, full token usage: 0.00, #swa token: 323, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.77, #queue-req: 0, 
[2025-07-28 00:03:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 987, full token usage: 0.00, #swa token: 987, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.99, #queue-req: 0, 
[2025-07-28 00:03:44] INFO:     127.0.0.1:44086 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:03:44 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 121, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:03:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 363, full token usage: 0.00, #swa token: 363, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.86, #queue-req: 0, 
[2025-07-28 00:03:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 259, full token usage: 0.00, #swa token: 259, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 85.57, #queue-req: 0, 
[2025-07-28 00:03:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 403, full token usage: 0.00, #swa token: 403, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.82, #queue-req: 0, 
[2025-07-28 00:03:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 299, full token usage: 0.00, #swa token: 299, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.15, #queue-req: 0, 
[2025-07-28 00:03:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 443, full token usage: 0.00, #swa token: 443, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.87, #queue-req: 0, 
[2025-07-28 00:03:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 339, full token usage: 0.00, #swa token: 339, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.96, #queue-req: 0, 
[2025-07-28 00:03:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 483, full token usage: 0.00, #swa token: 483, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.76, #queue-req: 0, 
[2025-07-28 00:03:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 379, full token usage: 0.00, #swa token: 379, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.15, #queue-req: 0, 
[2025-07-28 00:03:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 523, full token usage: 0.00, #swa token: 523, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.77, #queue-req: 0, 
[2025-07-28 00:03:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 419, full token usage: 0.00, #swa token: 419, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.18, #queue-req: 0, 
[2025-07-28 00:03:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 563, full token usage: 0.00, #swa token: 563, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.66, #queue-req: 0, 
[2025-07-28 00:03:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 459, full token usage: 0.00, #swa token: 459, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.13, #queue-req: 0, 
[2025-07-28 00:03:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 603, full token usage: 0.00, #swa token: 603, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.71, #queue-req: 0, 
[2025-07-28 00:03:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 499, full token usage: 0.00, #swa token: 499, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.06, #queue-req: 0, 
[2025-07-28 00:03:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 643, full token usage: 0.00, #swa token: 643, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.63, #queue-req: 0, 
[2025-07-28 00:03:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 539, full token usage: 0.00, #swa token: 539, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.93, #queue-req: 0, 
[2025-07-28 00:03:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 683, full token usage: 0.00, #swa token: 683, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.74, #queue-req: 0, 
[2025-07-28 00:03:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 579, full token usage: 0.00, #swa token: 579, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.03, #queue-req: 0, 
[2025-07-28 00:03:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 723, full token usage: 0.00, #swa token: 723, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.60, #queue-req: 0, 
[2025-07-28 00:03:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 619, full token usage: 0.00, #swa token: 619, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.87, #queue-req: 0, 
[2025-07-28 00:03:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 763, full token usage: 0.00, #swa token: 763, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.67, #queue-req: 0, 
[2025-07-28 00:03:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 659, full token usage: 0.00, #swa token: 659, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.00, #queue-req: 0, 
[2025-07-28 00:03:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 803, full token usage: 0.00, #swa token: 803, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.56, #queue-req: 0, 
[2025-07-28 00:03:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 699, full token usage: 0.00, #swa token: 699, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.88, #queue-req: 0, 
[2025-07-28 00:03:48] INFO:     127.0.0.1:34240 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:03:48 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 89, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:03:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 197, full token usage: 0.00, #swa token: 197, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 85.24, #queue-req: 0, 
[2025-07-28 00:03:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 739, full token usage: 0.00, #swa token: 739, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.07, #queue-req: 0, 
[2025-07-28 00:03:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 237, full token usage: 0.00, #swa token: 237, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.18, #queue-req: 0, 
[2025-07-28 00:03:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 779, full token usage: 0.00, #swa token: 779, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.90, #queue-req: 0, 
[2025-07-28 00:03:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 277, full token usage: 0.00, #swa token: 277, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.91, #queue-req: 0, 
[2025-07-28 00:03:50 DP0 TP0] Decode batch. #running-req: 1, #full token: 819, full token usage: 0.00, #swa token: 819, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.84, #queue-req: 0, 
[2025-07-28 00:03:50] INFO:     127.0.0.1:34250 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:03:50 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 134, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:03:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 317, full token usage: 0.00, #swa token: 317, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.79, #queue-req: 0, 
[2025-07-28 00:03:50 DP0 TP0] Decode batch. #running-req: 1, #full token: 264, full token usage: 0.00, #swa token: 264, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 85.61, #queue-req: 0, 
[2025-07-28 00:03:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 357, full token usage: 0.00, #swa token: 357, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.66, #queue-req: 0, 
[2025-07-28 00:03:50 DP0 TP0] Decode batch. #running-req: 1, #full token: 304, full token usage: 0.00, #swa token: 304, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.12, #queue-req: 0, 
[2025-07-28 00:03:51 DP1 TP0] Decode batch. #running-req: 1, #full token: 397, full token usage: 0.00, #swa token: 397, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.78, #queue-req: 0, 
[2025-07-28 00:03:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 344, full token usage: 0.00, #swa token: 344, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.07, #queue-req: 0, 
[2025-07-28 00:03:51 DP1 TP0] Decode batch. #running-req: 1, #full token: 437, full token usage: 0.00, #swa token: 437, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.74, #queue-req: 0, 
[2025-07-28 00:03:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 384, full token usage: 0.00, #swa token: 384, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.16, #queue-req: 0, 
[2025-07-28 00:03:51 DP1 TP0] Decode batch. #running-req: 1, #full token: 477, full token usage: 0.00, #swa token: 477, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.82, #queue-req: 0, 
[2025-07-28 00:03:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 424, full token usage: 0.00, #swa token: 424, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.87, #queue-req: 0, 
[2025-07-28 00:03:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 517, full token usage: 0.00, #swa token: 517, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.74, #queue-req: 0, 
[2025-07-28 00:03:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 464, full token usage: 0.00, #swa token: 464, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.07, #queue-req: 0, 
[2025-07-28 00:03:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 557, full token usage: 0.00, #swa token: 557, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.64, #queue-req: 0, 
[2025-07-28 00:03:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 504, full token usage: 0.00, #swa token: 504, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.14, #queue-req: 0, 
[2025-07-28 00:03:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 597, full token usage: 0.00, #swa token: 597, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.69, #queue-req: 0, 
[2025-07-28 00:03:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 544, full token usage: 0.00, #swa token: 544, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.03, #queue-req: 0, 
[2025-07-28 00:03:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 637, full token usage: 0.00, #swa token: 637, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.38, #queue-req: 0, 
[2025-07-28 00:03:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 584, full token usage: 0.00, #swa token: 584, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.17, #queue-req: 0, 
[2025-07-28 00:03:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 677, full token usage: 0.00, #swa token: 677, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.46, #queue-req: 0, 
[2025-07-28 00:03:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 624, full token usage: 0.00, #swa token: 624, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.00, #queue-req: 0, 
[2025-07-28 00:03:54] INFO:     127.0.0.1:34256 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:03:54 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:03:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 258, full token usage: 0.00, #swa token: 258, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 83.74, #queue-req: 0, 
[2025-07-28 00:03:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 664, full token usage: 0.00, #swa token: 664, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.00, #queue-req: 0, 
[2025-07-28 00:03:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 298, full token usage: 0.00, #swa token: 298, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.83, #queue-req: 0, 
[2025-07-28 00:03:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 704, full token usage: 0.00, #swa token: 704, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.90, #queue-req: 0, 
[2025-07-28 00:03:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 338, full token usage: 0.00, #swa token: 338, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.76, #queue-req: 0, 
[2025-07-28 00:03:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 744, full token usage: 0.00, #swa token: 744, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.91, #queue-req: 0, 
[2025-07-28 00:03:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 378, full token usage: 0.00, #swa token: 378, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.77, #queue-req: 0, 
[2025-07-28 00:03:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 784, full token usage: 0.00, #swa token: 784, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.94, #queue-req: 0, 
[2025-07-28 00:03:55] INFO:     127.0.0.1:34270 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:03:55 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 93, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:03:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 418, full token usage: 0.00, #swa token: 418, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.73, #queue-req: 0, 
[2025-07-28 00:03:56 DP0 TP0] Decode batch. #running-req: 1, #full token: 213, full token usage: 0.00, #swa token: 213, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 83.46, #queue-req: 0, 
[2025-07-28 00:03:56 DP1 TP0] Decode batch. #running-req: 1, #full token: 458, full token usage: 0.00, #swa token: 458, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.79, #queue-req: 0, 
[2025-07-28 00:03:56 DP0 TP0] Decode batch. #running-req: 1, #full token: 253, full token usage: 0.00, #swa token: 253, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.47, #queue-req: 0, 
[2025-07-28 00:03:56 DP1 TP0] Decode batch. #running-req: 1, #full token: 498, full token usage: 0.00, #swa token: 498, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.77, #queue-req: 0, 
[2025-07-28 00:03:56 DP0 TP0] Decode batch. #running-req: 1, #full token: 293, full token usage: 0.00, #swa token: 293, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.15, #queue-req: 0, 
[2025-07-28 00:03:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 538, full token usage: 0.00, #swa token: 538, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.73, #queue-req: 0, 
[2025-07-28 00:03:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 333, full token usage: 0.00, #swa token: 333, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.03, #queue-req: 0, 
[2025-07-28 00:03:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 578, full token usage: 0.00, #swa token: 578, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.66, #queue-req: 0, 
[2025-07-28 00:03:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 373, full token usage: 0.00, #swa token: 373, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.94, #queue-req: 0, 
[2025-07-28 00:03:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 618, full token usage: 0.00, #swa token: 618, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.70, #queue-req: 0, 
[2025-07-28 00:03:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 413, full token usage: 0.00, #swa token: 413, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.12, #queue-req: 0, 
[2025-07-28 00:03:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 658, full token usage: 0.00, #swa token: 658, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.67, #queue-req: 0, 
[2025-07-28 00:03:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 453, full token usage: 0.00, #swa token: 453, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.21, #queue-req: 0, 
[2025-07-28 00:03:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 698, full token usage: 0.00, #swa token: 698, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.57, #queue-req: 0, 
[2025-07-28 00:03:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 493, full token usage: 0.00, #swa token: 493, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.15, #queue-req: 0, 
[2025-07-28 00:03:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 738, full token usage: 0.00, #swa token: 738, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.63, #queue-req: 0, 
[2025-07-28 00:03:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 533, full token usage: 0.00, #swa token: 533, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.08, #queue-req: 0, 
[2025-07-28 00:03:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 778, full token usage: 0.00, #swa token: 778, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.72, #queue-req: 0, 
[2025-07-28 00:03:59] INFO:     127.0.0.1:49232 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:03:59 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 167, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:03:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 573, full token usage: 0.00, #swa token: 573, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.99, #queue-req: 0, 
[2025-07-28 00:04:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 300, full token usage: 0.00, #swa token: 300, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 86.66, #queue-req: 0, 
[2025-07-28 00:04:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 613, full token usage: 0.00, #swa token: 613, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.96, #queue-req: 0, 
[2025-07-28 00:04:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 340, full token usage: 0.00, #swa token: 340, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.86, #queue-req: 0, 
[2025-07-28 00:04:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 653, full token usage: 0.00, #swa token: 653, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.96, #queue-req: 0, 
[2025-07-28 00:04:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 380, full token usage: 0.00, #swa token: 380, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.00, #queue-req: 0, 
[2025-07-28 00:04:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 693, full token usage: 0.00, #swa token: 693, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.89, #queue-req: 0, 
[2025-07-28 00:04:01 DP1 TP0] Decode batch. #running-req: 1, #full token: 420, full token usage: 0.00, #swa token: 420, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.87, #queue-req: 0, 
[2025-07-28 00:04:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 733, full token usage: 0.00, #swa token: 733, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.01, #queue-req: 0, 
[2025-07-28 00:04:01 DP1 TP0] Decode batch. #running-req: 1, #full token: 460, full token usage: 0.00, #swa token: 460, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.84, #queue-req: 0, 
[2025-07-28 00:04:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 773, full token usage: 0.00, #swa token: 773, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.98, #queue-req: 0, 
[2025-07-28 00:04:01] INFO:     127.0.0.1:49234 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:04:01 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 152, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:04:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 500, full token usage: 0.00, #swa token: 500, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.90, #queue-req: 0, 
[2025-07-28 00:04:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 291, full token usage: 0.00, #swa token: 291, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 86.08, #queue-req: 0, 
[2025-07-28 00:04:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 540, full token usage: 0.00, #swa token: 540, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.75, #queue-req: 0, 
[2025-07-28 00:04:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 331, full token usage: 0.00, #swa token: 331, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.15, #queue-req: 0, 
[2025-07-28 00:04:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 580, full token usage: 0.00, #swa token: 580, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.75, #queue-req: 0, 
[2025-07-28 00:04:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 371, full token usage: 0.00, #swa token: 371, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.18, #queue-req: 0, 
[2025-07-28 00:04:03 DP1 TP0] Decode batch. #running-req: 1, #full token: 620, full token usage: 0.00, #swa token: 620, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.77, #queue-req: 0, 
[2025-07-28 00:04:03 DP0 TP0] Decode batch. #running-req: 1, #full token: 411, full token usage: 0.00, #swa token: 411, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.11, #queue-req: 0, 
[2025-07-28 00:04:03 DP1 TP0] Decode batch. #running-req: 1, #full token: 660, full token usage: 0.00, #swa token: 660, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.78, #queue-req: 0, 
[2025-07-28 00:04:03 DP0 TP0] Decode batch. #running-req: 1, #full token: 451, full token usage: 0.00, #swa token: 451, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.15, #queue-req: 0, 
[2025-07-28 00:04:03 DP1 TP0] Decode batch. #running-req: 1, #full token: 700, full token usage: 0.00, #swa token: 700, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.76, #queue-req: 0, 
[2025-07-28 00:04:04 DP0 TP0] Decode batch. #running-req: 1, #full token: 491, full token usage: 0.00, #swa token: 491, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.15, #queue-req: 0, 
[2025-07-28 00:04:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 740, full token usage: 0.00, #swa token: 740, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.68, #queue-req: 0, 
[2025-07-28 00:04:04 DP0 TP0] Decode batch. #running-req: 1, #full token: 531, full token usage: 0.00, #swa token: 531, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.05, #queue-req: 0, 
[2025-07-28 00:04:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 780, full token usage: 0.00, #swa token: 780, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.74, #queue-req: 0, 
[2025-07-28 00:04:04 DP0 TP0] Decode batch. #running-req: 1, #full token: 571, full token usage: 0.00, #swa token: 571, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.00, #queue-req: 0, 
[2025-07-28 00:04:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 820, full token usage: 0.00, #swa token: 820, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.73, #queue-req: 0, 
[2025-07-28 00:04:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 611, full token usage: 0.00, #swa token: 611, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.97, #queue-req: 0, 
[2025-07-28 00:04:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 860, full token usage: 0.00, #swa token: 860, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.62, #queue-req: 0, 
[2025-07-28 00:04:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 651, full token usage: 0.00, #swa token: 651, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.99, #queue-req: 0, 
[2025-07-28 00:04:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 900, full token usage: 0.00, #swa token: 900, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.62, #queue-req: 0, 
[2025-07-28 00:04:06 DP0 TP0] Decode batch. #running-req: 1, #full token: 691, full token usage: 0.00, #swa token: 691, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.97, #queue-req: 0, 
[2025-07-28 00:04:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 940, full token usage: 0.00, #swa token: 940, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.73, #queue-req: 0, 
[2025-07-28 00:04:06] INFO:     127.0.0.1:49236 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:04:06 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 117, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:04:06 DP0 TP0] Decode batch. #running-req: 1, #full token: 731, full token usage: 0.00, #swa token: 731, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.00, #queue-req: 0, 
[2025-07-28 00:04:06] INFO:     127.0.0.1:49250 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:04:06 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 103, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:04:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 253, full token usage: 0.00, #swa token: 253, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 86.51, #queue-req: 0, 
[2025-07-28 00:04:06 DP0 TP0] Decode batch. #running-req: 1, #full token: 215, full token usage: 0.00, #swa token: 215, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 87.03, #queue-req: 0, 
[2025-07-28 00:04:07 DP1 TP0] Decode batch. #running-req: 1, #full token: 293, full token usage: 0.00, #swa token: 293, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.93, #queue-req: 0, 
[2025-07-28 00:04:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 255, full token usage: 0.00, #swa token: 255, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.47, #queue-req: 0, 
[2025-07-28 00:04:07 DP1 TP0] Decode batch. #running-req: 1, #full token: 333, full token usage: 0.00, #swa token: 333, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.83, #queue-req: 0, 
[2025-07-28 00:04:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 295, full token usage: 0.00, #swa token: 295, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.13, #queue-req: 0, 
[2025-07-28 00:04:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 373, full token usage: 0.00, #swa token: 373, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.87, #queue-req: 0, 
[2025-07-28 00:04:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 335, full token usage: 0.00, #swa token: 335, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.16, #queue-req: 0, 
[2025-07-28 00:04:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 413, full token usage: 0.00, #swa token: 413, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.89, #queue-req: 0, 
[2025-07-28 00:04:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 375, full token usage: 0.00, #swa token: 375, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.09, #queue-req: 0, 
[2025-07-28 00:04:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 453, full token usage: 0.00, #swa token: 453, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.80, #queue-req: 0, 
[2025-07-28 00:04:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 415, full token usage: 0.00, #swa token: 415, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.11, #queue-req: 0, 
[2025-07-28 00:04:09 DP1 TP0] Decode batch. #running-req: 1, #full token: 493, full token usage: 0.00, #swa token: 493, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.85, #queue-req: 0, 
[2025-07-28 00:04:09 DP0 TP0] Decode batch. #running-req: 1, #full token: 455, full token usage: 0.00, #swa token: 455, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.08, #queue-req: 0, 
[2025-07-28 00:04:09 DP1 TP0] Decode batch. #running-req: 1, #full token: 533, full token usage: 0.00, #swa token: 533, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.66, #queue-req: 0, 
[2025-07-28 00:04:09 DP0 TP0] Decode batch. #running-req: 1, #full token: 495, full token usage: 0.00, #swa token: 495, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.14, #queue-req: 0, 
[2025-07-28 00:04:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 573, full token usage: 0.00, #swa token: 573, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.44, #queue-req: 0, 
[2025-07-28 00:04:10] INFO:     127.0.0.1:39724 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:04:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 535, full token usage: 0.00, #swa token: 535, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.04, #queue-req: 0, 
[2025-07-28 00:04:10 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 92, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:04:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 222, full token usage: 0.00, #swa token: 222, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 85.53, #queue-req: 0, 
[2025-07-28 00:04:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 575, full token usage: 0.00, #swa token: 575, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.97, #queue-req: 0, 
[2025-07-28 00:04:10] INFO:     127.0.0.1:39728 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:04:10 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 122, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:04:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 262, full token usage: 0.00, #swa token: 262, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.94, #queue-req: 0, 
[2025-07-28 00:04:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 223, full token usage: 0.00, #swa token: 223, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 86.07, #queue-req: 0, 
[2025-07-28 00:04:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 302, full token usage: 0.00, #swa token: 302, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.80, #queue-req: 0, 
[2025-07-28 00:04:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 263, full token usage: 0.00, #swa token: 263, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.37, #queue-req: 0, 
[2025-07-28 00:04:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 342, full token usage: 0.00, #swa token: 342, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.80, #queue-req: 0, 
[2025-07-28 00:04:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 303, full token usage: 0.00, #swa token: 303, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.24, #queue-req: 0, 
[2025-07-28 00:04:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 382, full token usage: 0.00, #swa token: 382, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.84, #queue-req: 0, 
[2025-07-28 00:04:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 343, full token usage: 0.00, #swa token: 343, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.13, #queue-req: 0, 
[2025-07-28 00:04:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 422, full token usage: 0.00, #swa token: 422, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.78, #queue-req: 0, 
[2025-07-28 00:04:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 383, full token usage: 0.00, #swa token: 383, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.13, #queue-req: 0, 
[2025-07-28 00:04:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 462, full token usage: 0.00, #swa token: 462, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.78, #queue-req: 0, 
[2025-07-28 00:04:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 423, full token usage: 0.00, #swa token: 423, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.20, #queue-req: 0, 
[2025-07-28 00:04:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 502, full token usage: 0.00, #swa token: 502, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.77, #queue-req: 0, 
[2025-07-28 00:04:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 463, full token usage: 0.00, #swa token: 463, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.11, #queue-req: 0, 
[2025-07-28 00:04:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 542, full token usage: 0.00, #swa token: 542, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.69, #queue-req: 0, 
[2025-07-28 00:04:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 503, full token usage: 0.00, #swa token: 503, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.24, #queue-req: 0, 
[2025-07-28 00:04:14 DP1 TP0] Decode batch. #running-req: 1, #full token: 582, full token usage: 0.00, #swa token: 582, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.56, #queue-req: 0, 
[2025-07-28 00:04:14 DP0 TP0] Decode batch. #running-req: 1, #full token: 543, full token usage: 0.00, #swa token: 543, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.04, #queue-req: 0, 
[2025-07-28 00:04:14 DP1 TP0] Decode batch. #running-req: 1, #full token: 622, full token usage: 0.00, #swa token: 622, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.69, #queue-req: 0, 
[2025-07-28 00:04:14] INFO:     127.0.0.1:39746 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:04:14 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 162, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:04:14 DP1 TP0] Decode batch. #running-req: 2, #full token: 863, full token usage: 0.00, #swa token: 863, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 157.20, #queue-req: 0, 
[2025-07-28 00:04:15 DP1 TP0] Decode batch. #running-req: 2, #full token: 943, full token usage: 0.00, #swa token: 943, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 188.18, #queue-req: 0, 
[2025-07-28 00:04:15 DP1 TP0] Decode batch. #running-req: 2, #full token: 1023, full token usage: 0.00, #swa token: 1023, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 188.14, #queue-req: 0, 
[2025-07-28 00:04:16 DP1 TP0] Decode batch. #running-req: 2, #full token: 1103, full token usage: 0.00, #swa token: 1103, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 188.14, #queue-req: 0, 
[2025-07-28 00:04:16] INFO:     127.0.0.1:39730 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:04:16 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 148, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:04:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 250, full token usage: 0.00, #swa token: 250, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 16.93, #queue-req: 0, 
[2025-07-28 00:04:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 458, full token usage: 0.00, #swa token: 458, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 128.27, #queue-req: 0, 
[2025-07-28 00:04:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 290, full token usage: 0.00, #swa token: 290, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 97.58, #queue-req: 0, 
[2025-07-28 00:04:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 498, full token usage: 0.00, #swa token: 498, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.76, #queue-req: 0, 
[2025-07-28 00:04:17 DP0 TP0] Decode batch. #running-req: 1, #full token: 330, full token usage: 0.00, #swa token: 330, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 98.61, #queue-req: 0, 
[2025-07-28 00:04:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 538, full token usage: 0.00, #swa token: 538, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.67, #queue-req: 0, 
[2025-07-28 00:04:17 DP0 TP0] Decode batch. #running-req: 1, #full token: 370, full token usage: 0.00, #swa token: 370, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.22, #queue-req: 0, 
[2025-07-28 00:04:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 578, full token usage: 0.00, #swa token: 578, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.65, #queue-req: 0, 
[2025-07-28 00:04:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 410, full token usage: 0.00, #swa token: 410, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.25, #queue-req: 0, 
[2025-07-28 00:04:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 618, full token usage: 0.00, #swa token: 618, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.68, #queue-req: 0, 
[2025-07-28 00:04:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 450, full token usage: 0.00, #swa token: 450, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.20, #queue-req: 0, 
[2025-07-28 00:04:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 658, full token usage: 0.00, #swa token: 658, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.56, #queue-req: 0, 
[2025-07-28 00:04:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 490, full token usage: 0.00, #swa token: 490, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.22, #queue-req: 0, 
[2025-07-28 00:04:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 698, full token usage: 0.00, #swa token: 698, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.61, #queue-req: 0, 
[2025-07-28 00:04:19 DP0 TP0] Decode batch. #running-req: 1, #full token: 530, full token usage: 0.00, #swa token: 530, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.12, #queue-req: 0, 
[2025-07-28 00:04:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 738, full token usage: 0.00, #swa token: 738, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.63, #queue-req: 0, 
[2025-07-28 00:04:19 DP0 TP0] Decode batch. #running-req: 1, #full token: 570, full token usage: 0.00, #swa token: 570, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.11, #queue-req: 0, 
[2025-07-28 00:04:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 778, full token usage: 0.00, #swa token: 778, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.57, #queue-req: 0, 
[2025-07-28 00:04:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 610, full token usage: 0.00, #swa token: 610, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.00, #queue-req: 0, 
[2025-07-28 00:04:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 818, full token usage: 0.00, #swa token: 818, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.35, #queue-req: 0, 
[2025-07-28 00:04:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 650, full token usage: 0.00, #swa token: 650, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.05, #queue-req: 0, 
[2025-07-28 00:04:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 858, full token usage: 0.00, #swa token: 858, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.50, #queue-req: 0, 
[2025-07-28 00:04:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 690, full token usage: 0.00, #swa token: 690, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.06, #queue-req: 0, 
[2025-07-28 00:04:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 898, full token usage: 0.00, #swa token: 898, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.53, #queue-req: 0, 
[2025-07-28 00:04:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 730, full token usage: 0.00, #swa token: 730, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.04, #queue-req: 0, 
[2025-07-28 00:04:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 938, full token usage: 0.00, #swa token: 938, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.45, #queue-req: 0, 
[2025-07-28 00:04:21] INFO:     127.0.0.1:38742 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:04:21 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 117, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:04:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 770, full token usage: 0.00, #swa token: 770, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.03, #queue-req: 0, 
[2025-07-28 00:04:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 246, full token usage: 0.00, #swa token: 246, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 86.01, #queue-req: 0, 
[2025-07-28 00:04:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 810, full token usage: 0.00, #swa token: 810, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.03, #queue-req: 0, 
[2025-07-28 00:04:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 286, full token usage: 0.00, #swa token: 286, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.89, #queue-req: 0, 
[2025-07-28 00:04:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 850, full token usage: 0.00, #swa token: 850, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.96, #queue-req: 0, 
[2025-07-28 00:04:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 326, full token usage: 0.00, #swa token: 326, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.80, #queue-req: 0, 
[2025-07-28 00:04:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 890, full token usage: 0.00, #swa token: 890, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.91, #queue-req: 0, 
[2025-07-28 00:04:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 366, full token usage: 0.00, #swa token: 366, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.74, #queue-req: 0, 
[2025-07-28 00:04:23] INFO:     127.0.0.1:38748 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:04:23 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 133, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:04:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 238, full token usage: 0.00, #swa token: 238, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 85.91, #queue-req: 0, 
[2025-07-28 00:04:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 406, full token usage: 0.00, #swa token: 406, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.82, #queue-req: 0, 
[2025-07-28 00:04:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 278, full token usage: 0.00, #swa token: 278, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.18, #queue-req: 0, 
[2025-07-28 00:04:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 446, full token usage: 0.00, #swa token: 446, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.75, #queue-req: 0, 
[2025-07-28 00:04:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 318, full token usage: 0.00, #swa token: 318, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.22, #queue-req: 0, 
[2025-07-28 00:04:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 486, full token usage: 0.00, #swa token: 486, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.81, #queue-req: 0, 
[2025-07-28 00:04:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 358, full token usage: 0.00, #swa token: 358, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.27, #queue-req: 0, 
[2025-07-28 00:04:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 526, full token usage: 0.00, #swa token: 526, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.79, #queue-req: 0, 
[2025-07-28 00:04:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 398, full token usage: 0.00, #swa token: 398, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.19, #queue-req: 0, 
[2025-07-28 00:04:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 566, full token usage: 0.00, #swa token: 566, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.67, #queue-req: 0, 
[2025-07-28 00:04:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 438, full token usage: 0.00, #swa token: 438, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.21, #queue-req: 0, 
[2025-07-28 00:04:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 606, full token usage: 0.00, #swa token: 606, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.63, #queue-req: 0, 
[2025-07-28 00:04:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 478, full token usage: 0.00, #swa token: 478, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.20, #queue-req: 0, 
[2025-07-28 00:04:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 646, full token usage: 0.00, #swa token: 646, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.71, #queue-req: 0, 
[2025-07-28 00:04:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 518, full token usage: 0.00, #swa token: 518, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.13, #queue-req: 0, 
[2025-07-28 00:04:26] INFO:     127.0.0.1:38754 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:04:26 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 201, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:04:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 310, full token usage: 0.00, #swa token: 310, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 84.93, #queue-req: 0, 
[2025-07-28 00:04:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 558, full token usage: 0.00, #swa token: 558, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.68, #queue-req: 0, 
[2025-07-28 00:04:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 350, full token usage: 0.00, #swa token: 350, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.41, #queue-req: 0, 
[2025-07-28 00:04:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 598, full token usage: 0.00, #swa token: 598, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.03, #queue-req: 0, 
[2025-07-28 00:04:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 390, full token usage: 0.00, #swa token: 390, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.94, #queue-req: 0, 
[2025-07-28 00:04:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 638, full token usage: 0.00, #swa token: 638, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.05, #queue-req: 0, 
[2025-07-28 00:04:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 430, full token usage: 0.00, #swa token: 430, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.86, #queue-req: 0, 
[2025-07-28 00:04:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 678, full token usage: 0.00, #swa token: 678, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.03, #queue-req: 0, 
[2025-07-28 00:04:27] INFO:     127.0.0.1:51598 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:04:27 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 90, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:04:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 470, full token usage: 0.00, #swa token: 470, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.87, #queue-req: 0, 
[2025-07-28 00:04:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 218, full token usage: 0.00, #swa token: 218, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 86.29, #queue-req: 0, 
[2025-07-28 00:04:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 510, full token usage: 0.00, #swa token: 510, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.83, #queue-req: 0, 
[2025-07-28 00:04:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 258, full token usage: 0.00, #swa token: 258, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.49, #queue-req: 0, 
[2025-07-28 00:04:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 550, full token usage: 0.00, #swa token: 550, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.70, #queue-req: 0, 
[2025-07-28 00:04:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 298, full token usage: 0.00, #swa token: 298, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.16, #queue-req: 0, 
[2025-07-28 00:04:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 590, full token usage: 0.00, #swa token: 590, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.63, #queue-req: 0, 
[2025-07-28 00:04:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 338, full token usage: 0.00, #swa token: 338, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.10, #queue-req: 0, 
[2025-07-28 00:04:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 630, full token usage: 0.00, #swa token: 630, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.69, #queue-req: 0, 
[2025-07-28 00:04:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 378, full token usage: 0.00, #swa token: 378, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.17, #queue-req: 0, 
[2025-07-28 00:04:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 670, full token usage: 0.00, #swa token: 670, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.70, #queue-req: 0, 
[2025-07-28 00:04:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 418, full token usage: 0.00, #swa token: 418, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.17, #queue-req: 0, 
[2025-07-28 00:04:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 710, full token usage: 0.00, #swa token: 710, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.58, #queue-req: 0, 
[2025-07-28 00:04:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 458, full token usage: 0.00, #swa token: 458, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.17, #queue-req: 0, 
[2025-07-28 00:04:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 750, full token usage: 0.00, #swa token: 750, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.69, #queue-req: 0, 
[2025-07-28 00:04:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 498, full token usage: 0.00, #swa token: 498, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.00, #queue-req: 0, 
[2025-07-28 00:04:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 790, full token usage: 0.00, #swa token: 790, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.58, #queue-req: 0, 
[2025-07-28 00:04:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 538, full token usage: 0.00, #swa token: 538, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.18, #queue-req: 0, 
[2025-07-28 00:04:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 830, full token usage: 0.00, #swa token: 830, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.62, #queue-req: 0, 
[2025-07-28 00:04:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 578, full token usage: 0.00, #swa token: 578, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.04, #queue-req: 0, 
[2025-07-28 00:04:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 870, full token usage: 0.00, #swa token: 870, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.53, #queue-req: 0, 
[2025-07-28 00:04:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 618, full token usage: 0.00, #swa token: 618, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.02, #queue-req: 0, 
[2025-07-28 00:04:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 910, full token usage: 0.00, #swa token: 910, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.64, #queue-req: 0, 
[2025-07-28 00:04:32] INFO:     127.0.0.1:51610 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:04:32 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 158, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:04:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 658, full token usage: 0.00, #swa token: 658, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.94, #queue-req: 0, 
[2025-07-28 00:04:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 288, full token usage: 0.00, #swa token: 288, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 85.00, #queue-req: 0, 
[2025-07-28 00:04:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 698, full token usage: 0.00, #swa token: 698, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.06, #queue-req: 0, 
[2025-07-28 00:04:32] INFO:     127.0.0.1:51626 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:04:32 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 134, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:04:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 328, full token usage: 0.00, #swa token: 328, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.74, #queue-req: 0, 
[2025-07-28 00:04:33 DP0 TP0] Decode batch. #running-req: 1, #full token: 268, full token usage: 0.00, #swa token: 268, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 85.93, #queue-req: 0, 
[2025-07-28 00:04:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 368, full token usage: 0.00, #swa token: 368, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.79, #queue-req: 0, 
[2025-07-28 00:04:33 DP0 TP0] Decode batch. #running-req: 1, #full token: 308, full token usage: 0.00, #swa token: 308, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.24, #queue-req: 0, 
[2025-07-28 00:04:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 408, full token usage: 0.00, #swa token: 408, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.71, #queue-req: 0, 
[2025-07-28 00:04:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 348, full token usage: 0.00, #swa token: 348, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.25, #queue-req: 0, 
[2025-07-28 00:04:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 448, full token usage: 0.00, #swa token: 448, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.85, #queue-req: 0, 
[2025-07-28 00:04:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 388, full token usage: 0.00, #swa token: 388, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.21, #queue-req: 0, 
[2025-07-28 00:04:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 488, full token usage: 0.00, #swa token: 488, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.77, #queue-req: 0, 
[2025-07-28 00:04:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 428, full token usage: 0.00, #swa token: 428, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.18, #queue-req: 0, 
[2025-07-28 00:04:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 528, full token usage: 0.00, #swa token: 528, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.79, #queue-req: 0, 
[2025-07-28 00:04:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 468, full token usage: 0.00, #swa token: 468, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.21, #queue-req: 0, 
[2025-07-28 00:04:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 568, full token usage: 0.00, #swa token: 568, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.70, #queue-req: 0, 
[2025-07-28 00:04:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 508, full token usage: 0.00, #swa token: 508, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.21, #queue-req: 0, 
[2025-07-28 00:04:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 608, full token usage: 0.00, #swa token: 608, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.62, #queue-req: 0, 
[2025-07-28 00:04:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 548, full token usage: 0.00, #swa token: 548, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.04, #queue-req: 0, 
[2025-07-28 00:04:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 648, full token usage: 0.00, #swa token: 648, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.72, #queue-req: 0, 
[2025-07-28 00:04:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 588, full token usage: 0.00, #swa token: 588, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.87, #queue-req: 0, 
[2025-07-28 00:04:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 688, full token usage: 0.00, #swa token: 688, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.70, #queue-req: 0, 
[2025-07-28 00:04:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 628, full token usage: 0.00, #swa token: 628, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.08, #queue-req: 0, 
[2025-07-28 00:04:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 728, full token usage: 0.00, #swa token: 728, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.68, #queue-req: 0, 
[2025-07-28 00:04:37 DP0 TP0] Decode batch. #running-req: 1, #full token: 668, full token usage: 0.00, #swa token: 668, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.05, #queue-req: 0, 
[2025-07-28 00:04:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 768, full token usage: 0.00, #swa token: 768, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.69, #queue-req: 0, 
[2025-07-28 00:04:37 DP0 TP0] Decode batch. #running-req: 1, #full token: 708, full token usage: 0.00, #swa token: 708, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.97, #queue-req: 0, 
[2025-07-28 00:04:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 808, full token usage: 0.00, #swa token: 808, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.64, #queue-req: 0, 
[2025-07-28 00:04:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 748, full token usage: 0.00, #swa token: 748, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.03, #queue-req: 0, 
[2025-07-28 00:04:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 848, full token usage: 0.00, #swa token: 848, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.71, #queue-req: 0, 
[2025-07-28 00:04:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 788, full token usage: 0.00, #swa token: 788, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.92, #queue-req: 0, 
[2025-07-28 00:04:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 888, full token usage: 0.00, #swa token: 888, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.69, #queue-req: 0, 
[2025-07-28 00:04:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 828, full token usage: 0.00, #swa token: 828, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.03, #queue-req: 0, 
[2025-07-28 00:04:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 928, full token usage: 0.00, #swa token: 928, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.73, #queue-req: 0, 
[2025-07-28 00:04:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 868, full token usage: 0.00, #swa token: 868, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.94, #queue-req: 0, 
[2025-07-28 00:04:39] INFO:     127.0.0.1:57118 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:04:39 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:04:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 261, full token usage: 0.00, #swa token: 261, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 85.30, #queue-req: 0, 
[2025-07-28 00:04:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 908, full token usage: 0.00, #swa token: 908, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.05, #queue-req: 0, 
[2025-07-28 00:04:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 301, full token usage: 0.00, #swa token: 301, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.81, #queue-req: 0, 
[2025-07-28 00:04:39] INFO:     127.0.0.1:57120 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:04:39 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:04:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 248, full token usage: 0.00, #swa token: 248, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 86.04, #queue-req: 0, 
[2025-07-28 00:04:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 341, full token usage: 0.00, #swa token: 341, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.91, #queue-req: 0, 
[2025-07-28 00:04:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 288, full token usage: 0.00, #swa token: 288, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.28, #queue-req: 0, 
[2025-07-28 00:04:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 381, full token usage: 0.00, #swa token: 381, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.92, #queue-req: 0, 
[2025-07-28 00:04:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 328, full token usage: 0.00, #swa token: 328, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.18, #queue-req: 0, 
[2025-07-28 00:04:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 421, full token usage: 0.00, #swa token: 421, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.88, #queue-req: 0, 
[2025-07-28 00:04:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 368, full token usage: 0.00, #swa token: 368, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.20, #queue-req: 0, 
[2025-07-28 00:04:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 461, full token usage: 0.00, #swa token: 461, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.85, #queue-req: 0, 
[2025-07-28 00:04:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 408, full token usage: 0.00, #swa token: 408, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.19, #queue-req: 0, 
[2025-07-28 00:04:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 501, full token usage: 0.00, #swa token: 501, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.87, #queue-req: 0, 
[2025-07-28 00:04:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 448, full token usage: 0.00, #swa token: 448, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.17, #queue-req: 0, 
[2025-07-28 00:04:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 541, full token usage: 0.00, #swa token: 541, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.78, #queue-req: 0, 
[2025-07-28 00:04:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 488, full token usage: 0.00, #swa token: 488, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.20, #queue-req: 0, 
[2025-07-28 00:04:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 581, full token usage: 0.00, #swa token: 581, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.70, #queue-req: 0, 
[2025-07-28 00:04:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 528, full token usage: 0.00, #swa token: 528, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.17, #queue-req: 0, 
[2025-07-28 00:04:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 621, full token usage: 0.00, #swa token: 621, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.72, #queue-req: 0, 
[2025-07-28 00:04:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 568, full token usage: 0.00, #swa token: 568, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.08, #queue-req: 0, 
[2025-07-28 00:04:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 661, full token usage: 0.00, #swa token: 661, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.72, #queue-req: 0, 
[2025-07-28 00:04:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 608, full token usage: 0.00, #swa token: 608, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.98, #queue-req: 0, 
[2025-07-28 00:04:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 701, full token usage: 0.00, #swa token: 701, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.69, #queue-req: 0, 
[2025-07-28 00:04:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 648, full token usage: 0.00, #swa token: 648, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 98.52, #queue-req: 0, 
[2025-07-28 00:04:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 741, full token usage: 0.00, #swa token: 741, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.75, #queue-req: 0, 
[2025-07-28 00:04:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 688, full token usage: 0.00, #swa token: 688, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.05, #queue-req: 0, 
[2025-07-28 00:04:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 781, full token usage: 0.00, #swa token: 781, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.73, #queue-req: 0, 
[2025-07-28 00:04:44] INFO:     127.0.0.1:57124 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:04:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 728, full token usage: 0.00, #swa token: 728, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.01, #queue-req: 0, 
[2025-07-28 00:04:44 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 102, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:04:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 225, full token usage: 0.00, #swa token: 225, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 84.64, #queue-req: 0, 
[2025-07-28 00:04:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 768, full token usage: 0.00, #swa token: 768, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.08, #queue-req: 0, 
[2025-07-28 00:04:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 265, full token usage: 0.00, #swa token: 265, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.15, #queue-req: 0, 
[2025-07-28 00:04:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 808, full token usage: 0.00, #swa token: 808, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.02, #queue-req: 0, 
[2025-07-28 00:04:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 305, full token usage: 0.00, #swa token: 305, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.80, #queue-req: 0, 
[2025-07-28 00:04:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 848, full token usage: 0.00, #swa token: 848, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.02, #queue-req: 0, 
[2025-07-28 00:04:46] INFO:     127.0.0.1:57130 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:04:46 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 149, #cached-token: 98, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:04:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 345, full token usage: 0.00, #swa token: 345, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.86, #queue-req: 0, 
[2025-07-28 00:04:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 270, full token usage: 0.00, #swa token: 270, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 86.55, #queue-req: 0, 
[2025-07-28 00:04:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 385, full token usage: 0.00, #swa token: 385, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.88, #queue-req: 0, 
[2025-07-28 00:04:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 310, full token usage: 0.00, #swa token: 310, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.24, #queue-req: 0, 
[2025-07-28 00:04:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 425, full token usage: 0.00, #swa token: 425, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.81, #queue-req: 0, 
[2025-07-28 00:04:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 350, full token usage: 0.00, #swa token: 350, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.13, #queue-req: 0, 
[2025-07-28 00:04:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 465, full token usage: 0.00, #swa token: 465, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.79, #queue-req: 0, 
[2025-07-28 00:04:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 390, full token usage: 0.00, #swa token: 390, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.15, #queue-req: 0, 
[2025-07-28 00:04:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 505, full token usage: 0.00, #swa token: 505, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.88, #queue-req: 0, 
[2025-07-28 00:04:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 430, full token usage: 0.00, #swa token: 430, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.17, #queue-req: 0, 
[2025-07-28 00:04:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 545, full token usage: 0.00, #swa token: 545, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.68, #queue-req: 0, 
[2025-07-28 00:04:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 470, full token usage: 0.00, #swa token: 470, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.19, #queue-req: 0, 
[2025-07-28 00:04:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 585, full token usage: 0.00, #swa token: 585, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.72, #queue-req: 0, 
[2025-07-28 00:04:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 510, full token usage: 0.00, #swa token: 510, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.18, #queue-req: 0, 
[2025-07-28 00:04:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 625, full token usage: 0.00, #swa token: 625, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.69, #queue-req: 0, 
[2025-07-28 00:04:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 550, full token usage: 0.00, #swa token: 550, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.06, #queue-req: 0, 
[2025-07-28 00:04:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 665, full token usage: 0.00, #swa token: 665, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.67, #queue-req: 0, 
[2025-07-28 00:04:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 590, full token usage: 0.00, #swa token: 590, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.07, #queue-req: 0, 
[2025-07-28 00:04:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 705, full token usage: 0.00, #swa token: 705, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.69, #queue-req: 0, 
[2025-07-28 00:04:50 DP0 TP0] Decode batch. #running-req: 1, #full token: 630, full token usage: 0.00, #swa token: 630, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.05, #queue-req: 0, 
[2025-07-28 00:04:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 745, full token usage: 0.00, #swa token: 745, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.55, #queue-req: 0, 
[2025-07-28 00:04:50 DP0 TP0] Decode batch. #running-req: 1, #full token: 670, full token usage: 0.00, #swa token: 670, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.06, #queue-req: 0, 
[2025-07-28 00:04:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 785, full token usage: 0.00, #swa token: 785, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.70, #queue-req: 0, 
[2025-07-28 00:04:50] INFO:     127.0.0.1:57994 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:04:50 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 83, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:04:50 DP0 TP0] Decode batch. #running-req: 1, #full token: 710, full token usage: 0.00, #swa token: 710, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.00, #queue-req: 0, 
[2025-07-28 00:04:51 DP1 TP0] Decode batch. #running-req: 1, #full token: 210, full token usage: 0.00, #swa token: 210, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 85.61, #queue-req: 0, 
[2025-07-28 00:04:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 750, full token usage: 0.00, #swa token: 750, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.04, #queue-req: 0, 
[2025-07-28 00:04:51 DP1 TP0] Decode batch. #running-req: 1, #full token: 250, full token usage: 0.00, #swa token: 250, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.14, #queue-req: 0, 
[2025-07-28 00:04:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 790, full token usage: 0.00, #swa token: 790, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.97, #queue-req: 0, 
[2025-07-28 00:04:51] INFO:     127.0.0.1:58006 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:04:51 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 155, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:04:51 DP1 TP0] Decode batch. #running-req: 1, #full token: 290, full token usage: 0.00, #swa token: 290, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.90, #queue-req: 0, 
[2025-07-28 00:04:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 264, full token usage: 0.00, #swa token: 264, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 86.02, #queue-req: 0, 
[2025-07-28 00:04:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 330, full token usage: 0.00, #swa token: 330, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.79, #queue-req: 0, 
[2025-07-28 00:04:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 304, full token usage: 0.00, #swa token: 304, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.16, #queue-req: 0, 
[2025-07-28 00:04:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 370, full token usage: 0.00, #swa token: 370, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.88, #queue-req: 0, 
[2025-07-28 00:04:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 344, full token usage: 0.00, #swa token: 344, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.19, #queue-req: 0, 
[2025-07-28 00:04:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 410, full token usage: 0.00, #swa token: 410, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.86, #queue-req: 0, 
[2025-07-28 00:04:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 384, full token usage: 0.00, #swa token: 384, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.21, #queue-req: 0, 
[2025-07-28 00:04:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 450, full token usage: 0.00, #swa token: 450, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.79, #queue-req: 0, 
[2025-07-28 00:04:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 424, full token usage: 0.00, #swa token: 424, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.14, #queue-req: 0, 
[2025-07-28 00:04:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 490, full token usage: 0.00, #swa token: 490, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.82, #queue-req: 0, 
[2025-07-28 00:04:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 464, full token usage: 0.00, #swa token: 464, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.18, #queue-req: 0, 
[2025-07-28 00:04:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 530, full token usage: 0.00, #swa token: 530, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.63, #queue-req: 0, 
[2025-07-28 00:04:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 504, full token usage: 0.00, #swa token: 504, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.13, #queue-req: 0, 
[2025-07-28 00:04:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 570, full token usage: 0.00, #swa token: 570, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.74, #queue-req: 0, 
[2025-07-28 00:04:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 544, full token usage: 0.00, #swa token: 544, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.11, #queue-req: 0, 
[2025-07-28 00:04:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 610, full token usage: 0.00, #swa token: 610, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.62, #queue-req: 0, 
[2025-07-28 00:04:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 584, full token usage: 0.00, #swa token: 584, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.07, #queue-req: 0, 
[2025-07-28 00:04:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 650, full token usage: 0.00, #swa token: 650, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.73, #queue-req: 0, 
[2025-07-28 00:04:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 624, full token usage: 0.00, #swa token: 624, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.02, #queue-req: 0, 
[2025-07-28 00:04:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 690, full token usage: 0.00, #swa token: 690, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.69, #queue-req: 0, 
[2025-07-28 00:04:56 DP0 TP0] Decode batch. #running-req: 1, #full token: 664, full token usage: 0.00, #swa token: 664, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.98, #queue-req: 0, 
[2025-07-28 00:04:56 DP1 TP0] Decode batch. #running-req: 1, #full token: 730, full token usage: 0.00, #swa token: 730, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.69, #queue-req: 0, 
[2025-07-28 00:04:56 DP0 TP0] Decode batch. #running-req: 1, #full token: 704, full token usage: 0.00, #swa token: 704, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.03, #queue-req: 0, 
[2025-07-28 00:04:56] INFO:     127.0.0.1:58020 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:04:56 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 91, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:04:56 DP1 TP0] Decode batch. #running-req: 1, #full token: 217, full token usage: 0.00, #swa token: 217, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 84.84, #queue-req: 0, 
[2025-07-28 00:04:56 DP0 TP0] Decode batch. #running-req: 1, #full token: 744, full token usage: 0.00, #swa token: 744, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.08, #queue-req: 0, 
[2025-07-28 00:04:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 257, full token usage: 0.00, #swa token: 257, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.05, #queue-req: 0, 
[2025-07-28 00:04:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 784, full token usage: 0.00, #swa token: 784, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.03, #queue-req: 0, 
[2025-07-28 00:04:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 297, full token usage: 0.00, #swa token: 297, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.84, #queue-req: 0, 
[2025-07-28 00:04:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 824, full token usage: 0.00, #swa token: 824, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.01, #queue-req: 0, 
[2025-07-28 00:04:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 337, full token usage: 0.00, #swa token: 337, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.91, #queue-req: 0, 
[2025-07-28 00:04:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 864, full token usage: 0.00, #swa token: 864, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.07, #queue-req: 0, 
[2025-07-28 00:04:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 377, full token usage: 0.00, #swa token: 377, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.86, #queue-req: 0, 
[2025-07-28 00:04:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 904, full token usage: 0.00, #swa token: 904, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.02, #queue-req: 0, 
[2025-07-28 00:04:58] INFO:     127.0.0.1:58034 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:04:58 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 179, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:04:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 417, full token usage: 0.00, #swa token: 417, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.82, #queue-req: 0, 
[2025-07-28 00:04:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 289, full token usage: 0.00, #swa token: 289, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 86.15, #queue-req: 0, 
[2025-07-28 00:04:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 457, full token usage: 0.00, #swa token: 457, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.50, #queue-req: 0, 
[2025-07-28 00:04:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 329, full token usage: 0.00, #swa token: 329, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.81, #queue-req: 0, 
[2025-07-28 00:04:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 497, full token usage: 0.00, #swa token: 497, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.89, #queue-req: 0, 
[2025-07-28 00:04:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 369, full token usage: 0.00, #swa token: 369, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.21, #queue-req: 0, 
[2025-07-28 00:05:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 537, full token usage: 0.00, #swa token: 537, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.69, #queue-req: 0, 
[2025-07-28 00:05:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 409, full token usage: 0.00, #swa token: 409, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.13, #queue-req: 0, 
[2025-07-28 00:05:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 577, full token usage: 0.00, #swa token: 577, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.73, #queue-req: 0, 
[2025-07-28 00:05:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 449, full token usage: 0.00, #swa token: 449, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.18, #queue-req: 0, 
[2025-07-28 00:05:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 617, full token usage: 0.00, #swa token: 617, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.72, #queue-req: 0, 
[2025-07-28 00:05:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 489, full token usage: 0.00, #swa token: 489, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.12, #queue-req: 0, 
[2025-07-28 00:05:01 DP1 TP0] Decode batch. #running-req: 1, #full token: 657, full token usage: 0.00, #swa token: 657, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.73, #queue-req: 0, 
[2025-07-28 00:05:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 529, full token usage: 0.00, #swa token: 529, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.13, #queue-req: 0, 
[2025-07-28 00:05:01 DP1 TP0] Decode batch. #running-req: 1, #full token: 697, full token usage: 0.00, #swa token: 697, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.72, #queue-req: 0, 
[2025-07-28 00:05:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 569, full token usage: 0.00, #swa token: 569, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.04, #queue-req: 0, 
[2025-07-28 00:05:01 DP1 TP0] Decode batch. #running-req: 1, #full token: 737, full token usage: 0.00, #swa token: 737, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.76, #queue-req: 0, 
[2025-07-28 00:05:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 609, full token usage: 0.00, #swa token: 609, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.97, #queue-req: 0, 
[2025-07-28 00:05:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 777, full token usage: 0.00, #swa token: 777, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.69, #queue-req: 0, 
[2025-07-28 00:05:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 649, full token usage: 0.00, #swa token: 649, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.00, #queue-req: 0, 
[2025-07-28 00:05:02] INFO:     127.0.0.1:55982 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:05:02 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 183, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:05:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 305, full token usage: 0.00, #swa token: 305, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 85.18, #queue-req: 0, 
[2025-07-28 00:05:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 689, full token usage: 0.00, #swa token: 689, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.99, #queue-req: 0, 
[2025-07-28 00:05:03 DP1 TP0] Decode batch. #running-req: 1, #full token: 345, full token usage: 0.00, #swa token: 345, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.79, #queue-req: 0, 
[2025-07-28 00:05:03 DP0 TP0] Decode batch. #running-req: 1, #full token: 729, full token usage: 0.00, #swa token: 729, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.91, #queue-req: 0, 
[2025-07-28 00:05:03 DP1 TP0] Decode batch. #running-req: 1, #full token: 385, full token usage: 0.00, #swa token: 385, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.76, #queue-req: 0, 
[2025-07-28 00:05:03 DP0 TP0] Decode batch. #running-req: 1, #full token: 769, full token usage: 0.00, #swa token: 769, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.88, #queue-req: 0, 
[2025-07-28 00:05:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 425, full token usage: 0.00, #swa token: 425, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.77, #queue-req: 0, 
[2025-07-28 00:05:04 DP0 TP0] Decode batch. #running-req: 1, #full token: 809, full token usage: 0.00, #swa token: 809, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.01, #queue-req: 0, 
[2025-07-28 00:05:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 465, full token usage: 0.00, #swa token: 465, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.78, #queue-req: 0, 
[2025-07-28 00:05:04 DP0 TP0] Decode batch. #running-req: 1, #full token: 849, full token usage: 0.00, #swa token: 849, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.99, #queue-req: 0, 
[2025-07-28 00:05:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 505, full token usage: 0.00, #swa token: 505, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.79, #queue-req: 0, 
[2025-07-28 00:05:04 DP0 TP0] Decode batch. #running-req: 1, #full token: 889, full token usage: 0.00, #swa token: 889, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.94, #queue-req: 0, 
[2025-07-28 00:05:05] INFO:     127.0.0.1:55992 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:05:05 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 96, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:05:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 545, full token usage: 0.00, #swa token: 545, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.68, #queue-req: 0, 
[2025-07-28 00:05:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 200, full token usage: 0.00, #swa token: 200, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 86.00, #queue-req: 0, 
[2025-07-28 00:05:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 585, full token usage: 0.00, #swa token: 585, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.61, #queue-req: 0, 
[2025-07-28 00:05:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 240, full token usage: 0.00, #swa token: 240, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.56, #queue-req: 0, 
[2025-07-28 00:05:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 625, full token usage: 0.00, #swa token: 625, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.62, #queue-req: 0, 
[2025-07-28 00:05:06 DP0 TP0] Decode batch. #running-req: 1, #full token: 280, full token usage: 0.00, #swa token: 280, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.30, #queue-req: 0, 
[2025-07-28 00:05:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 665, full token usage: 0.00, #swa token: 665, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.64, #queue-req: 0, 
[2025-07-28 00:05:06 DP0 TP0] Decode batch. #running-req: 1, #full token: 320, full token usage: 0.00, #swa token: 320, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.17, #queue-req: 0, 
[2025-07-28 00:05:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 705, full token usage: 0.00, #swa token: 705, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.62, #queue-req: 0, 
[2025-07-28 00:05:06 DP0 TP0] Decode batch. #running-req: 1, #full token: 360, full token usage: 0.00, #swa token: 360, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.13, #queue-req: 0, 
[2025-07-28 00:05:07 DP1 TP0] Decode batch. #running-req: 1, #full token: 745, full token usage: 0.00, #swa token: 745, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.60, #queue-req: 0, 
[2025-07-28 00:05:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 400, full token usage: 0.00, #swa token: 400, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.20, #queue-req: 0, 
[2025-07-28 00:05:07 DP1 TP0] Decode batch. #running-req: 1, #full token: 785, full token usage: 0.00, #swa token: 785, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.60, #queue-req: 0, 
[2025-07-28 00:05:07] INFO:     127.0.0.1:33176 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:05:07 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 156, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:05:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 440, full token usage: 0.00, #swa token: 440, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.19, #queue-req: 0, 
[2025-07-28 00:05:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 289, full token usage: 0.00, #swa token: 289, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 86.02, #queue-req: 0, 
[2025-07-28 00:05:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 480, full token usage: 0.00, #swa token: 480, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.11, #queue-req: 0, 
[2025-07-28 00:05:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 329, full token usage: 0.00, #swa token: 329, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.69, #queue-req: 0, 
[2025-07-28 00:05:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 520, full token usage: 0.00, #swa token: 520, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.12, #queue-req: 0, 
[2025-07-28 00:05:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 369, full token usage: 0.00, #swa token: 369, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.82, #queue-req: 0, 
[2025-07-28 00:05:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 560, full token usage: 0.00, #swa token: 560, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.98, #queue-req: 0, 
[2025-07-28 00:05:09 DP1 TP0] Decode batch. #running-req: 1, #full token: 409, full token usage: 0.00, #swa token: 409, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.78, #queue-req: 0, 
[2025-07-28 00:05:09 DP0 TP0] Decode batch. #running-req: 1, #full token: 600, full token usage: 0.00, #swa token: 600, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.99, #queue-req: 0, 
[2025-07-28 00:05:09 DP1 TP0] Decode batch. #running-req: 1, #full token: 449, full token usage: 0.00, #swa token: 449, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.77, #queue-req: 0, 
[2025-07-28 00:05:09 DP0 TP0] Decode batch. #running-req: 1, #full token: 640, full token usage: 0.00, #swa token: 640, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.01, #queue-req: 0, 
[2025-07-28 00:05:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 489, full token usage: 0.00, #swa token: 489, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.75, #queue-req: 0, 
[2025-07-28 00:05:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 680, full token usage: 0.00, #swa token: 680, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.05, #queue-req: 0, 
[2025-07-28 00:05:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 529, full token usage: 0.00, #swa token: 529, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.69, #queue-req: 0, 
[2025-07-28 00:05:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 720, full token usage: 0.00, #swa token: 720, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.02, #queue-req: 0, 
[2025-07-28 00:05:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 760, full token usage: 0.00, #swa token: 760, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.03, #queue-req: 0, 
[2025-07-28 00:05:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 569, full token usage: 0.00, #swa token: 569, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.59, #queue-req: 0, 
[2025-07-28 00:05:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 800, full token usage: 0.00, #swa token: 800, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.02, #queue-req: 0, 
[2025-07-28 00:05:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 609, full token usage: 0.00, #swa token: 609, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.56, #queue-req: 0, 
[2025-07-28 00:05:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 840, full token usage: 0.00, #swa token: 840, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.99, #queue-req: 0, 
[2025-07-28 00:05:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 649, full token usage: 0.00, #swa token: 649, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.62, #queue-req: 0, 
[2025-07-28 00:05:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 880, full token usage: 0.00, #swa token: 880, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.05, #queue-req: 0, 
[2025-07-28 00:05:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 689, full token usage: 0.00, #swa token: 689, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.63, #queue-req: 0, 
[2025-07-28 00:05:12] INFO:     127.0.0.1:33180 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:05:12 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:05:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 729, full token usage: 0.00, #swa token: 729, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.65, #queue-req: 0, 
[2025-07-28 00:05:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 248, full token usage: 0.00, #swa token: 248, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 86.25, #queue-req: 0, 
[2025-07-28 00:05:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 769, full token usage: 0.00, #swa token: 769, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.62, #queue-req: 0, 
[2025-07-28 00:05:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 288, full token usage: 0.00, #swa token: 288, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.21, #queue-req: 0, 
[2025-07-28 00:05:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 809, full token usage: 0.00, #swa token: 809, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.59, #queue-req: 0, 
[2025-07-28 00:05:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 328, full token usage: 0.00, #swa token: 328, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.22, #queue-req: 0, 
[2025-07-28 00:05:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 849, full token usage: 0.00, #swa token: 849, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.59, #queue-req: 0, 
[2025-07-28 00:05:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 368, full token usage: 0.00, #swa token: 368, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.19, #queue-req: 0, 
[2025-07-28 00:05:14 DP1 TP0] Decode batch. #running-req: 1, #full token: 889, full token usage: 0.00, #swa token: 889, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.60, #queue-req: 0, 
[2025-07-28 00:05:14 DP0 TP0] Decode batch. #running-req: 1, #full token: 408, full token usage: 0.00, #swa token: 408, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.18, #queue-req: 0, 
[2025-07-28 00:05:14 DP1 TP0] Decode batch. #running-req: 1, #full token: 929, full token usage: 0.00, #swa token: 929, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.62, #queue-req: 0, 
[2025-07-28 00:05:14 DP0 TP0] Decode batch. #running-req: 1, #full token: 448, full token usage: 0.00, #swa token: 448, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.19, #queue-req: 0, 
[2025-07-28 00:05:14] INFO:     127.0.0.1:33184 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:05:14 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 135, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:05:14 DP0 TP0] Decode batch. #running-req: 1, #full token: 488, full token usage: 0.00, #swa token: 488, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.20, #queue-req: 0, 
[2025-07-28 00:05:14 DP1 TP0] Decode batch. #running-req: 1, #full token: 240, full token usage: 0.00, #swa token: 240, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 85.93, #queue-req: 0, 
[2025-07-28 00:05:15 DP0 TP0] Decode batch. #running-req: 1, #full token: 528, full token usage: 0.00, #swa token: 528, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.17, #queue-req: 0, 
[2025-07-28 00:05:15 DP1 TP0] Decode batch. #running-req: 1, #full token: 280, full token usage: 0.00, #swa token: 280, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.82, #queue-req: 0, 
[2025-07-28 00:05:15 DP0 TP0] Decode batch. #running-req: 1, #full token: 568, full token usage: 0.00, #swa token: 568, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.03, #queue-req: 0, 
[2025-07-28 00:05:15 DP1 TP0] Decode batch. #running-req: 1, #full token: 320, full token usage: 0.00, #swa token: 320, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.87, #queue-req: 0, 
[2025-07-28 00:05:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 608, full token usage: 0.00, #swa token: 608, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.05, #queue-req: 0, 
[2025-07-28 00:05:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 360, full token usage: 0.00, #swa token: 360, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.80, #queue-req: 0, 
[2025-07-28 00:05:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 648, full token usage: 0.00, #swa token: 648, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.06, #queue-req: 0, 
[2025-07-28 00:05:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 400, full token usage: 0.00, #swa token: 400, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.77, #queue-req: 0, 
[2025-07-28 00:05:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 688, full token usage: 0.00, #swa token: 688, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.02, #queue-req: 0, 
[2025-07-28 00:05:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 440, full token usage: 0.00, #swa token: 440, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.79, #queue-req: 0, 
[2025-07-28 00:05:17 DP0 TP0] Decode batch. #running-req: 1, #full token: 728, full token usage: 0.00, #swa token: 728, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.01, #queue-req: 0, 
[2025-07-28 00:05:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 480, full token usage: 0.00, #swa token: 480, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.79, #queue-req: 0, 
[2025-07-28 00:05:17 DP0 TP0] Decode batch. #running-req: 1, #full token: 768, full token usage: 0.00, #swa token: 768, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.02, #queue-req: 0, 
[2025-07-28 00:05:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 520, full token usage: 0.00, #swa token: 520, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.79, #queue-req: 0, 
[2025-07-28 00:05:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 808, full token usage: 0.00, #swa token: 808, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.10, #queue-req: 0, 
[2025-07-28 00:05:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 560, full token usage: 0.00, #swa token: 560, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.73, #queue-req: 0, 
[2025-07-28 00:05:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 848, full token usage: 0.00, #swa token: 848, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 98.51, #queue-req: 0, 
[2025-07-28 00:05:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 600, full token usage: 0.00, #swa token: 600, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.69, #queue-req: 0, 
[2025-07-28 00:05:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 888, full token usage: 0.00, #swa token: 888, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.03, #queue-req: 0, 
[2025-07-28 00:05:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 640, full token usage: 0.00, #swa token: 640, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.72, #queue-req: 0, 
[2025-07-28 00:05:18] INFO:     127.0.0.1:33194 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:05:18 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 131, #cached-token: 103, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:05:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 680, full token usage: 0.00, #swa token: 680, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.74, #queue-req: 0, 
[2025-07-28 00:05:19 DP0 TP0] Decode batch. #running-req: 1, #full token: 270, full token usage: 0.00, #swa token: 270, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 85.97, #queue-req: 0, 
[2025-07-28 00:05:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 720, full token usage: 0.00, #swa token: 720, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.69, #queue-req: 0, 
[2025-07-28 00:05:19 DP0 TP0] Decode batch. #running-req: 1, #full token: 310, full token usage: 0.00, #swa token: 310, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.23, #queue-req: 0, 
[2025-07-28 00:05:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 760, full token usage: 0.00, #swa token: 760, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.67, #queue-req: 0, 
[2025-07-28 00:05:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 350, full token usage: 0.00, #swa token: 350, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.29, #queue-req: 0, 
[2025-07-28 00:05:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 800, full token usage: 0.00, #swa token: 800, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.69, #queue-req: 0, 
[2025-07-28 00:05:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 390, full token usage: 0.00, #swa token: 390, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.20, #queue-req: 0, 
[2025-07-28 00:05:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 840, full token usage: 0.00, #swa token: 840, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.67, #queue-req: 0, 
[2025-07-28 00:05:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 430, full token usage: 0.00, #swa token: 430, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.26, #queue-req: 0, 
[2025-07-28 00:05:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 880, full token usage: 0.00, #swa token: 880, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.69, #queue-req: 0, 
[2025-07-28 00:05:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 470, full token usage: 0.00, #swa token: 470, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.23, #queue-req: 0, 
[2025-07-28 00:05:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 920, full token usage: 0.00, #swa token: 920, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.67, #queue-req: 0, 
[2025-07-28 00:05:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 510, full token usage: 0.00, #swa token: 510, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.21, #queue-req: 0, 
[2025-07-28 00:05:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 960, full token usage: 0.00, #swa token: 960, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.76, #queue-req: 0, 
[2025-07-28 00:05:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 550, full token usage: 0.00, #swa token: 550, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.92, #queue-req: 0, 
[2025-07-28 00:05:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 1000, full token usage: 0.00, #swa token: 1000, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.75, #queue-req: 0, 
[2025-07-28 00:05:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 590, full token usage: 0.00, #swa token: 590, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.96, #queue-req: 0, 
[2025-07-28 00:05:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 1040, full token usage: 0.00, #swa token: 1040, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.63, #queue-req: 0, 
[2025-07-28 00:05:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 630, full token usage: 0.00, #swa token: 630, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.96, #queue-req: 0, 
[2025-07-28 00:05:23] INFO:     127.0.0.1:55384 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:05:23 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 152, #cached-token: 104, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:05:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 670, full token usage: 0.00, #swa token: 670, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.00, #queue-req: 0, 
[2025-07-28 00:05:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 276, full token usage: 0.00, #swa token: 276, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 85.55, #queue-req: 0, 
[2025-07-28 00:05:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 710, full token usage: 0.00, #swa token: 710, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.02, #queue-req: 0, 
[2025-07-28 00:05:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 316, full token usage: 0.00, #swa token: 316, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.80, #queue-req: 0, 
[2025-07-28 00:05:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 750, full token usage: 0.00, #swa token: 750, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.01, #queue-req: 0, 
[2025-07-28 00:05:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 356, full token usage: 0.00, #swa token: 356, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.82, #queue-req: 0, 
[2025-07-28 00:05:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 790, full token usage: 0.00, #swa token: 790, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.95, #queue-req: 0, 
[2025-07-28 00:05:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 396, full token usage: 0.00, #swa token: 396, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.86, #queue-req: 0, 
[2025-07-28 00:05:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 830, full token usage: 0.00, #swa token: 830, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.81, #queue-req: 0, 
[2025-07-28 00:05:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 436, full token usage: 0.00, #swa token: 436, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.80, #queue-req: 0, 
[2025-07-28 00:05:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 870, full token usage: 0.00, #swa token: 870, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.77, #queue-req: 0, 
[2025-07-28 00:05:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 476, full token usage: 0.00, #swa token: 476, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.79, #queue-req: 0, 
[2025-07-28 00:05:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 910, full token usage: 0.00, #swa token: 910, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.76, #queue-req: 0, 
[2025-07-28 00:05:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 516, full token usage: 0.00, #swa token: 516, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.81, #queue-req: 0, 
[2025-07-28 00:05:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 950, full token usage: 0.00, #swa token: 950, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.94, #queue-req: 0, 
[2025-07-28 00:05:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 556, full token usage: 0.00, #swa token: 556, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.60, #queue-req: 0, 
[2025-07-28 00:05:26] INFO:     127.0.0.1:55398 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:05:26 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 110, #cached-token: 103, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:05:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 596, full token usage: 0.00, #swa token: 596, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.65, #queue-req: 0, 
[2025-07-28 00:05:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 218, full token usage: 0.00, #swa token: 218, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 85.56, #queue-req: 0, 
[2025-07-28 00:05:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 636, full token usage: 0.00, #swa token: 636, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.65, #queue-req: 0, 
[2025-07-28 00:05:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 258, full token usage: 0.00, #swa token: 258, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.45, #queue-req: 0, 
[2025-07-28 00:05:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 676, full token usage: 0.00, #swa token: 676, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.64, #queue-req: 0, 
[2025-07-28 00:05:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 298, full token usage: 0.00, #swa token: 298, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.15, #queue-req: 0, 
[2025-07-28 00:05:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 716, full token usage: 0.00, #swa token: 716, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.66, #queue-req: 0, 
[2025-07-28 00:05:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 338, full token usage: 0.00, #swa token: 338, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.18, #queue-req: 0, 
[2025-07-28 00:05:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 756, full token usage: 0.00, #swa token: 756, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.62, #queue-req: 0, 
[2025-07-28 00:05:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 378, full token usage: 0.00, #swa token: 378, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.18, #queue-req: 0, 
[2025-07-28 00:05:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 796, full token usage: 0.00, #swa token: 796, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.55, #queue-req: 0, 
[2025-07-28 00:05:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 418, full token usage: 0.00, #swa token: 418, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.16, #queue-req: 0, 
[2025-07-28 00:05:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 836, full token usage: 0.00, #swa token: 836, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.61, #queue-req: 0, 
[2025-07-28 00:05:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 458, full token usage: 0.00, #swa token: 458, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.20, #queue-req: 0, 
[2025-07-28 00:05:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 876, full token usage: 0.00, #swa token: 876, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.51, #queue-req: 0, 
[2025-07-28 00:05:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 498, full token usage: 0.00, #swa token: 498, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.24, #queue-req: 0, 
[2025-07-28 00:05:29] INFO:     127.0.0.1:43770 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:05:29 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 96, #cached-token: 103, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:05:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 538, full token usage: 0.00, #swa token: 538, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.11, #queue-req: 0, 
[2025-07-28 00:05:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 221, full token usage: 0.00, #swa token: 221, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 85.66, #queue-req: 0, 
[2025-07-28 00:05:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 578, full token usage: 0.00, #swa token: 578, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.02, #queue-req: 0, 
[2025-07-28 00:05:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 261, full token usage: 0.00, #swa token: 261, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.99, #queue-req: 0, 
[2025-07-28 00:05:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 618, full token usage: 0.00, #swa token: 618, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.12, #queue-req: 0, 
[2025-07-28 00:05:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 301, full token usage: 0.00, #swa token: 301, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.70, #queue-req: 0, 
[2025-07-28 00:05:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 658, full token usage: 0.00, #swa token: 658, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.08, #queue-req: 0, 
[2025-07-28 00:05:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 341, full token usage: 0.00, #swa token: 341, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.83, #queue-req: 0, 
[2025-07-28 00:05:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 698, full token usage: 0.00, #swa token: 698, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.06, #queue-req: 0, 
[2025-07-28 00:05:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 381, full token usage: 0.00, #swa token: 381, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.79, #queue-req: 0, 
[2025-07-28 00:05:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 738, full token usage: 0.00, #swa token: 738, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 101.06, #queue-req: 0, 
[2025-07-28 00:05:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 421, full token usage: 0.00, #swa token: 421, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.67, #queue-req: 0, 
[2025-07-28 00:05:31] INFO:     127.0.0.1:43774 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:05:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 461, full token usage: 0.00, #swa token: 461, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.60, #queue-req: 0, 
[2025-07-28 00:05:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 501, full token usage: 0.00, #swa token: 501, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.75, #queue-req: 0, 
[2025-07-28 00:05:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 541, full token usage: 0.00, #swa token: 541, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.73, #queue-req: 0, 
[2025-07-28 00:05:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 581, full token usage: 0.00, #swa token: 581, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.73, #queue-req: 0, 
[2025-07-28 00:05:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 621, full token usage: 0.00, #swa token: 621, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.64, #queue-req: 0, 
[2025-07-28 00:05:33] INFO:     127.0.0.1:43784 - "POST /v1/chat/completions HTTP/1.1" 200 OK

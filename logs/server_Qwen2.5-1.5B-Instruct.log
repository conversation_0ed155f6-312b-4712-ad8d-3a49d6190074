[2025-07-28 00:32:31] server_args=ServerArgs(model_path='/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-1.5B-Instruct', tokenizer_path='/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-1.5B-Instruct', tokenizer_mode='auto', skip_tokenizer_init=False, load_format='auto', model_loader_extra_config='{}', trust_remote_code=False, context_length=32768, is_embedding=False, enable_multimodal=None, revision=None, model_impl='auto', host='127.0.0.1', port=8002, skip_server_warmup=False, warmups=None, nccl_port=None, dtype='auto', quantization=None, quantization_param_path=None, kv_cache_dtype='auto', mem_fraction_static=0.85, max_running_requests=None, max_total_tokens=None, chunked_prefill_size=8192, max_prefill_tokens=16384, schedule_policy='fcfs', schedule_conservativeness=1.0, cpu_offload_gb=0, page_size=1, hybrid_kvcache_ratio=None, swa_full_tokens_ratio=0.8, disable_hybrid_swa_memory=False, device='cuda', tp_size=4, pp_size=1, max_micro_batch_size=None, stream_interval=1, stream_output=False, random_seed=729403899, constrained_json_whitespace_pattern=None, watchdog_timeout=300, dist_timeout=None, download_dir=None, base_gpu_id=0, gpu_id_step=1, sleep_on_idle=False, log_level='info', log_level_http=None, log_requests=False, log_requests_level=0, crash_dump_folder=None, show_time_cost=True, enable_metrics=True, enable_metrics_for_all_schedulers=False, bucket_time_to_first_token=None, bucket_inter_token_latency=None, bucket_e2e_request_latency=None, collect_tokens_histogram=False, decode_log_interval=40, enable_request_time_stats_logging=False, kv_events_config=None, api_key=None, served_model_name='/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-1.5B-Instruct', chat_template=None, completion_template=None, file_storage_path='sglang_storage', enable_cache_report=False, reasoning_parser=None, tool_call_parser=None, dp_size=2, load_balance_method='round_robin', dist_init_addr=None, nnodes=1, node_rank=0, json_model_override_args='{}', preferred_sampling_params=None, enable_lora=None, max_lora_rank=None, lora_target_modules=None, lora_paths=None, max_loras_per_batch=8, lora_backend='triton', attention_backend=None, sampling_backend='flashinfer', grammar_backend='xgrammar', mm_attention_backend=None, speculative_algorithm=None, speculative_draft_model_path=None, speculative_num_steps=None, speculative_eagle_topk=None, speculative_num_draft_tokens=None, speculative_accept_threshold_single=1.0, speculative_accept_threshold_acc=1.0, speculative_token_map=None, ep_size=1, enable_ep_moe=False, enable_deepep_moe=False, enable_flashinfer_moe=False, enable_flashinfer_allreduce_fusion=False, deepep_mode='auto', ep_num_redundant_experts=0, ep_dispatch_algorithm='static', init_expert_location='trivial', enable_eplb=False, eplb_algorithm='auto', eplb_rebalance_num_iterations=1000, eplb_rebalance_layers_per_chunk=None, expert_distribution_recorder_mode=None, expert_distribution_recorder_buffer_size=1000, enable_expert_distribution_metrics=False, deepep_config=None, moe_dense_tp_size=None, enable_hierarchical_cache=False, hicache_ratio=2.0, hicache_size=0, hicache_write_policy='write_through_selective', hicache_io_backend='', hicache_storage_backend=None, enable_double_sparsity=False, ds_channel_config_path=None, ds_heavy_channel_num=32, ds_heavy_token_num=256, ds_heavy_channel_type='qk', ds_sparse_decode_threshold=4096, disable_radix_cache=False, cuda_graph_max_bs=None, cuda_graph_bs=None, disable_cuda_graph=False, disable_cuda_graph_padding=False, enable_profile_cuda_graph=False, enable_nccl_nvls=False, enable_tokenizer_batch_encode=False, disable_outlines_disk_cache=False, disable_custom_all_reduce=False, enable_mscclpp=False, disable_overlap_schedule=False, enable_mixed_chunk=False, enable_dp_attention=False, enable_dp_lm_head=False, enable_two_batch_overlap=False, enable_torch_compile=False, torch_compile_max_bs=32, torchao_config='', enable_nan_detection=False, enable_p2p_check=False, triton_attention_reduce_in_fp32=False, triton_attention_num_kv_splits=8, num_continuous_decode_steps=1, delete_ckpt_after_loading=False, enable_memory_saver=False, allow_auto_truncate=False, enable_custom_logit_processor=False, flashinfer_mla_disable_ragged=False, disable_shared_experts_fusion=False, disable_chunked_prefix_cache=False, disable_fast_image_processor=False, enable_return_hidden_states=False, enable_triton_kernel_moe=False, debug_tensor_dump_output_folder=None, debug_tensor_dump_input_file=None, debug_tensor_dump_inject=False, debug_tensor_dump_prefill_only=False, disaggregation_mode='null', disaggregation_transfer_backend='mooncake', disaggregation_bootstrap_port=8998, disaggregation_decode_tp=None, disaggregation_decode_dp=None, disaggregation_prefill_pp=1, disaggregation_ib_device=None, num_reserved_decode_tokens=512, pdlb_url=None, custom_weight_loader=[], weight_loader_disable_mmap=False, enable_pdmux=False, sm_group_num=3)
[2025-07-28 00:32:37] Launch DP0 starting at GPU #0.
[2025-07-28 00:32:37] Launch DP1 starting at GPU #4.
[2025-07-28 00:32:45 DP1 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 00:32:45 DP1 TP0] Init torch distributed begin.
[2025-07-28 00:32:45 DP0 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 00:32:45 DP0 TP0] Init torch distributed begin.
[2025-07-28 00:32:46 DP1 TP0] sglang is using nccl==2.26.2
[2025-07-28 00:32:46 DP0 TP0] sglang is using nccl==2.26.2
[2025-07-28 00:32:47 DP1 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 00:32:47 DP0 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 00:32:48 DP1 TP0] Load weight begin. avail mem=78.21 GB

Loading safetensors checkpoint shards:   0% Completed | 0/1 [00:00<?, ?it/s]
[2025-07-28 00:32:48 DP0 TP0] Load weight begin. avail mem=78.21 GB

Loading safetensors checkpoint shards:   0% Completed | 0/1 [00:00<?, ?it/s]

Loading safetensors checkpoint shards: 100% Completed | 1/1 [00:01<00:00,  1.00s/it]

Loading safetensors checkpoint shards: 100% Completed | 1/1 [00:00<00:00,  1.14it/s]

Loading safetensors checkpoint shards: 100% Completed | 1/1 [00:01<00:00,  1.00s/it]


Loading safetensors checkpoint shards: 100% Completed | 1/1 [00:00<00:00,  1.14it/s]

[2025-07-28 00:32:49 DP0 TP0] Load weight end. type=Qwen2ForCausalLM, dtype=torch.bfloat16, avail mem=77.43 GB, mem usage=0.78 GB.
[2025-07-28 00:32:49 DP1 TP0] Load weight end. type=Qwen2ForCausalLM, dtype=torch.bfloat16, avail mem=77.43 GB, mem usage=0.78 GB.
[2025-07-28 00:32:49 DP0 TP2] KV Cache is allocated. #tokens: 4914516, K size: 32.81 GB, V size: 32.81 GB
[2025-07-28 00:32:49 DP1 TP2] KV Cache is allocated. #tokens: 4914516, K size: 32.81 GB, V size: 32.81 GB
[2025-07-28 00:32:49 DP1 TP0] KV Cache is allocated. #tokens: 4914516, K size: 32.81 GB, V size: 32.81 GB
[2025-07-28 00:32:49 DP1 TP0] Memory pool end. avail mem=11.26 GB
[2025-07-28 00:32:49 DP0 TP1] KV Cache is allocated. #tokens: 4914516, K size: 32.81 GB, V size: 32.81 GB
[2025-07-28 00:32:49 DP0 TP3] KV Cache is allocated. #tokens: 4914516, K size: 32.81 GB, V size: 32.81 GB
[2025-07-28 00:32:49 DP0 TP0] KV Cache is allocated. #tokens: 4914516, K size: 32.81 GB, V size: 32.81 GB
[2025-07-28 00:32:49 DP0 TP0] Memory pool end. avail mem=11.26 GB
[2025-07-28 00:32:49 DP1 TP3] KV Cache is allocated. #tokens: 4914516, K size: 32.81 GB, V size: 32.81 GB
[2025-07-28 00:32:49 DP1 TP1] KV Cache is allocated. #tokens: 4914516, K size: 32.81 GB, V size: 32.81 GB
[2025-07-28 00:32:49 DP0 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.65 GB
[2025-07-28 00:32:49 DP1 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.65 GB
[2025-07-28 00:32:49 DP0 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.61 GB):   0%|          | 0/23 [00:00<?, ?it/s][2025-07-28 00:32:50 DP1 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.63 GB):   0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.61 GB):   4%|▍         | 1/23 [00:01<00:24,  1.10s/it]
Capturing batches (bs=152 avail_mem=10.39 GB):   4%|▍         | 1/23 [00:01<00:24,  1.10s/it]
Capturing batches (bs=160 avail_mem=10.63 GB):   4%|▍         | 1/23 [00:01<00:28,  1.28s/it]
Capturing batches (bs=152 avail_mem=10.39 GB):   4%|▍         | 1/23 [00:01<00:28,  1.28s/it]
Capturing batches (bs=152 avail_mem=10.39 GB):   9%|▊         | 2/23 [00:01<00:13,  1.52it/s]
Capturing batches (bs=144 avail_mem=10.27 GB):   9%|▊         | 2/23 [00:01<00:13,  1.52it/s]
Capturing batches (bs=152 avail_mem=10.39 GB):   9%|▊         | 2/23 [00:01<00:15,  1.32it/s]
Capturing batches (bs=144 avail_mem=10.27 GB):   9%|▊         | 2/23 [00:01<00:15,  1.32it/s]
Capturing batches (bs=144 avail_mem=10.27 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.76it/s]
Capturing batches (bs=136 avail_mem=10.19 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.76it/s]
Capturing batches (bs=136 avail_mem=10.19 GB):  17%|█▋        | 4/23 [00:02<00:08,  2.21it/s]
Capturing batches (bs=128 avail_mem=10.08 GB):  17%|█▋        | 4/23 [00:02<00:08,  2.21it/s]
Capturing batches (bs=144 avail_mem=10.27 GB):  13%|█▎        | 3/23 [00:02<00:12,  1.57it/s]
Capturing batches (bs=136 avail_mem=10.19 GB):  13%|█▎        | 3/23 [00:02<00:12,  1.57it/s]
Capturing batches (bs=128 avail_mem=10.08 GB):  22%|██▏       | 5/23 [00:02<00:06,  2.59it/s]
Capturing batches (bs=120 avail_mem=10.00 GB):  22%|██▏       | 5/23 [00:02<00:06,  2.59it/s]
Capturing batches (bs=136 avail_mem=10.19 GB):  17%|█▋        | 4/23 [00:02<00:09,  2.00it/s]
Capturing batches (bs=128 avail_mem=10.08 GB):  17%|█▋        | 4/23 [00:02<00:09,  2.00it/s]
Capturing batches (bs=120 avail_mem=10.00 GB):  26%|██▌       | 6/23 [00:02<00:05,  2.86it/s]
Capturing batches (bs=112 avail_mem=9.91 GB):  26%|██▌       | 6/23 [00:02<00:05,  2.86it/s] 
Capturing batches (bs=128 avail_mem=10.08 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.37it/s]
Capturing batches (bs=120 avail_mem=10.00 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.37it/s]
Capturing batches (bs=112 avail_mem=9.91 GB):  30%|███       | 7/23 [00:03<00:05,  3.04it/s]
Capturing batches (bs=104 avail_mem=9.84 GB):  30%|███       | 7/23 [00:03<00:05,  3.04it/s]
Capturing batches (bs=120 avail_mem=10.00 GB):  26%|██▌       | 6/23 [00:03<00:06,  2.66it/s]
Capturing batches (bs=112 avail_mem=9.91 GB):  26%|██▌       | 6/23 [00:03<00:06,  2.66it/s] 
Capturing batches (bs=104 avail_mem=9.84 GB):  35%|███▍      | 8/23 [00:03<00:04,  3.16it/s]
Capturing batches (bs=96 avail_mem=9.76 GB):  35%|███▍      | 8/23 [00:03<00:04,  3.16it/s] 
Capturing batches (bs=112 avail_mem=9.91 GB):  30%|███       | 7/23 [00:03<00:05,  2.88it/s]
Capturing batches (bs=104 avail_mem=9.84 GB):  30%|███       | 7/23 [00:03<00:05,  2.88it/s]
Capturing batches (bs=96 avail_mem=9.76 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.28it/s]
Capturing batches (bs=88 avail_mem=9.70 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.28it/s]
Capturing batches (bs=104 avail_mem=9.84 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.99it/s]
Capturing batches (bs=96 avail_mem=9.76 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.99it/s] 
Capturing batches (bs=88 avail_mem=9.70 GB):  43%|████▎     | 10/23 [00:03<00:03,  3.37it/s]
Capturing batches (bs=80 avail_mem=9.62 GB):  43%|████▎     | 10/23 [00:03<00:03,  3.37it/s]
Capturing batches (bs=96 avail_mem=9.76 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.07it/s]
Capturing batches (bs=88 avail_mem=9.70 GB):  39%|███▉      | 9/23 [00:03<00:04,  3.07it/s]
Capturing batches (bs=80 avail_mem=9.62 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.49it/s]
Capturing batches (bs=72 avail_mem=9.62 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.49it/s]
Capturing batches (bs=88 avail_mem=9.70 GB):  43%|████▎     | 10/23 [00:04<00:04,  3.16it/s]
Capturing batches (bs=80 avail_mem=9.62 GB):  43%|████▎     | 10/23 [00:04<00:04,  3.16it/s]
Capturing batches (bs=72 avail_mem=9.62 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.52it/s]
Capturing batches (bs=64 avail_mem=9.55 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.52it/s]
Capturing batches (bs=80 avail_mem=9.62 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.22it/s]
Capturing batches (bs=72 avail_mem=9.62 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.22it/s]
Capturing batches (bs=64 avail_mem=9.55 GB):  57%|█████▋    | 13/23 [00:04<00:02,  3.51it/s]
Capturing batches (bs=56 avail_mem=9.51 GB):  57%|█████▋    | 13/23 [00:04<00:02,  3.51it/s]
Capturing batches (bs=72 avail_mem=9.62 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.26it/s]
Capturing batches (bs=64 avail_mem=9.55 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.26it/s]
Capturing batches (bs=56 avail_mem=9.51 GB):  61%|██████    | 14/23 [00:04<00:02,  3.57it/s]
Capturing batches (bs=48 avail_mem=9.45 GB):  61%|██████    | 14/23 [00:04<00:02,  3.57it/s]
Capturing batches (bs=64 avail_mem=9.55 GB):  57%|█████▋    | 13/23 [00:05<00:03,  3.26it/s]
Capturing batches (bs=56 avail_mem=9.51 GB):  57%|█████▋    | 13/23 [00:05<00:03,  3.26it/s]
Capturing batches (bs=48 avail_mem=9.45 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.65it/s]
Capturing batches (bs=40 avail_mem=9.45 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.65it/s]
Capturing batches (bs=56 avail_mem=9.51 GB):  61%|██████    | 14/23 [00:05<00:02,  3.28it/s]
Capturing batches (bs=48 avail_mem=9.45 GB):  61%|██████    | 14/23 [00:05<00:02,  3.28it/s]
Capturing batches (bs=40 avail_mem=9.45 GB):  70%|██████▉   | 16/23 [00:05<00:01,  3.63it/s]
Capturing batches (bs=32 avail_mem=9.40 GB):  70%|██████▉   | 16/23 [00:05<00:01,  3.63it/s]
Capturing batches (bs=48 avail_mem=9.45 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.31it/s]
Capturing batches (bs=40 avail_mem=9.45 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.31it/s]
Capturing batches (bs=32 avail_mem=9.40 GB):  74%|███████▍  | 17/23 [00:05<00:01,  3.54it/s]
Capturing batches (bs=24 avail_mem=9.37 GB):  74%|███████▍  | 17/23 [00:05<00:01,  3.54it/s]
Capturing batches (bs=40 avail_mem=9.45 GB):  70%|██████▉   | 16/23 [00:06<00:02,  3.35it/s]
Capturing batches (bs=32 avail_mem=9.40 GB):  70%|██████▉   | 16/23 [00:06<00:02,  3.35it/s]
Capturing batches (bs=24 avail_mem=9.37 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.55it/s]
Capturing batches (bs=16 avail_mem=9.35 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.55it/s]
Capturing batches (bs=32 avail_mem=9.40 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.32it/s]
Capturing batches (bs=24 avail_mem=9.37 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.32it/s]
Capturing batches (bs=16 avail_mem=9.35 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.55it/s]
Capturing batches (bs=8 avail_mem=9.34 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.55it/s] 
Capturing batches (bs=8 avail_mem=9.34 GB):  87%|████████▋ | 20/23 [00:06<00:00,  3.62it/s]
Capturing batches (bs=4 avail_mem=9.32 GB):  87%|████████▋ | 20/23 [00:06<00:00,  3.62it/s]
Capturing batches (bs=24 avail_mem=9.37 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.36it/s]
Capturing batches (bs=16 avail_mem=9.35 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.36it/s]
Capturing batches (bs=4 avail_mem=9.32 GB):  91%|█████████▏| 21/23 [00:06<00:00,  3.68it/s]
Capturing batches (bs=2 avail_mem=9.31 GB):  91%|█████████▏| 21/23 [00:06<00:00,  3.68it/s]
Capturing batches (bs=16 avail_mem=9.35 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.40it/s]
Capturing batches (bs=8 avail_mem=9.34 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.40it/s] 
Capturing batches (bs=2 avail_mem=9.31 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.72it/s]
Capturing batches (bs=1 avail_mem=9.29 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.72it/s]
Capturing batches (bs=8 avail_mem=9.34 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.44it/s]
Capturing batches (bs=4 avail_mem=9.32 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.44it/s][2025-07-28 00:32:57 DP0 TP1] Registering 1311 cuda graph addresses
[2025-07-28 00:32:57 DP0 TP2] Registering 1311 cuda graph addresses

Capturing batches (bs=1 avail_mem=9.29 GB): 100%|██████████| 23/23 [00:07<00:00,  3.70it/s]
Capturing batches (bs=1 avail_mem=9.29 GB): 100%|██████████| 23/23 [00:07<00:00,  3.10it/s]
[2025-07-28 00:32:57 DP0 TP0] Registering 1311 cuda graph addresses
[2025-07-28 00:32:57 DP0 TP3] Registering 1311 cuda graph addresses
[2025-07-28 00:32:57 DP0 TP0] Capture cuda graph end. Time elapsed: 7.78 s. mem usage=1.36 GB. avail mem=9.28 GB.

Capturing batches (bs=4 avail_mem=9.32 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.34it/s]
Capturing batches (bs=2 avail_mem=9.31 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.34it/s][2025-07-28 00:32:57 DP0 TP0] max_total_num_tokens=4914516, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=9.28 GB

Capturing batches (bs=2 avail_mem=9.31 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.36it/s]
Capturing batches (bs=1 avail_mem=9.29 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.36it/s]
Capturing batches (bs=1 avail_mem=9.29 GB): 100%|██████████| 23/23 [00:08<00:00,  3.23it/s]
Capturing batches (bs=1 avail_mem=9.29 GB): 100%|██████████| 23/23 [00:08<00:00,  2.83it/s]
[2025-07-28 00:32:58 DP1 TP0] Registering 1311 cuda graph addresses
[2025-07-28 00:32:58 DP1 TP2] Registering 1311 cuda graph addresses
[2025-07-28 00:32:58 DP1 TP3] Registering 1311 cuda graph addresses
[2025-07-28 00:32:58 DP1 TP1] Registering 1311 cuda graph addresses
[2025-07-28 00:32:58 DP1 TP0] Capture cuda graph end. Time elapsed: 8.55 s. mem usage=1.36 GB. avail mem=9.28 GB.
[2025-07-28 00:32:58 DP1 TP0] max_total_num_tokens=4914516, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=9.28 GB
[2025-07-28 00:32:59] INFO:     Started server process [1751953]
[2025-07-28 00:32:59] INFO:     Waiting for application startup.
[2025-07-28 00:32:59] INFO:     Application startup complete.
[2025-07-28 00:32:59] INFO:     Uvicorn running on http://127.0.0.1:8002 (Press CTRL+C to quit)
[2025-07-28 00:33:00] INFO:     127.0.0.1:51380 - "GET /get_model_info HTTP/1.1" 200 OK
[2025-07-28 00:33:00 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:33:00] INFO:     127.0.0.1:51398 - "GET /health HTTP/1.1" 200 OK
[2025-07-28 00:33:00 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:33:01 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 215, #cached-token: 0, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:33:01 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 256, #cached-token: 0, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:33:01] INFO:     127.0.0.1:51396 - "POST /generate HTTP/1.1" 200 OK
[2025-07-28 00:33:01] The server is fired up and ready to roll!
[2025-07-28 00:33:01 DP1 TP0] Decode batch. #running-req: 1, #token: 255, token usage: 0.00, cuda graph: True, gen throughput (token/s): 16.18, #queue-req: 0, 
[2025-07-28 00:33:01 DP0 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 12.67, #queue-req: 0, 
[2025-07-28 00:33:01 DP1 TP0] Decode batch. #running-req: 1, #token: 295, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.38, #queue-req: 0, 
[2025-07-28 00:33:01 DP0 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 274.88, #queue-req: 0, 
[2025-07-28 00:33:01 DP1 TP0] Decode batch. #running-req: 1, #token: 335, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.25, #queue-req: 0, 
[2025-07-28 00:33:01 DP0 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.42, #queue-req: 0, 
[2025-07-28 00:33:01 DP1 TP0] Decode batch. #running-req: 1, #token: 375, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.02, #queue-req: 0, 
[2025-07-28 00:33:01 DP0 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 272.07, #queue-req: 0, 
[2025-07-28 00:33:01 DP1 TP0] Decode batch. #running-req: 1, #token: 415, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.16, #queue-req: 0, 
[2025-07-28 00:33:02 DP0 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.48, #queue-req: 0, 
[2025-07-28 00:33:02 DP1 TP0] Decode batch. #running-req: 1, #token: 455, token usage: 0.00, cuda graph: True, gen throughput (token/s): 288.21, #queue-req: 0, 
[2025-07-28 00:33:02 DP0 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.15, #queue-req: 0, 
[2025-07-28 00:33:02 DP1 TP0] Decode batch. #running-req: 1, #token: 495, token usage: 0.00, cuda graph: True, gen throughput (token/s): 281.95, #queue-req: 0, 
[2025-07-28 00:33:02 DP0 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.77, #queue-req: 0, 
[2025-07-28 00:33:02 DP1 TP0] Decode batch. #running-req: 1, #token: 535, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.15, #queue-req: 0, 
[2025-07-28 00:33:02 DP0 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.04, #queue-req: 0, 
[2025-07-28 00:33:02 DP1 TP0] Decode batch. #running-req: 1, #token: 575, token usage: 0.00, cuda graph: True, gen throughput (token/s): 275.63, #queue-req: 0, 
[2025-07-28 00:33:02 DP0 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.82, #queue-req: 0, 
[2025-07-28 00:33:02 DP1 TP0] Decode batch. #running-req: 1, #token: 615, token usage: 0.00, cuda graph: True, gen throughput (token/s): 261.74, #queue-req: 0, 
[2025-07-28 00:33:02 DP0 TP0] Decode batch. #running-req: 1, #token: 656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 273.57, #queue-req: 0, 
[2025-07-28 00:33:02] INFO:     127.0.0.1:51408 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:33:02 DP1 TP0] Decode batch. #running-req: 1, #token: 655, token usage: 0.00, cuda graph: True, gen throughput (token/s): 258.02, #queue-req: 0, 
[2025-07-28 00:33:02 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:33:02 DP1 TP0] Decode batch. #running-req: 1, #token: 695, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.61, #queue-req: 0, 
[2025-07-28 00:33:03 DP0 TP0] Decode batch. #running-req: 1, #token: 196, token usage: 0.00, cuda graph: True, gen throughput (token/s): 99.63, #queue-req: 0, 
[2025-07-28 00:33:03 DP1 TP0] Decode batch. #running-req: 1, #token: 735, token usage: 0.00, cuda graph: True, gen throughput (token/s): 280.20, #queue-req: 0, 
[2025-07-28 00:33:03 DP0 TP0] Decode batch. #running-req: 1, #token: 236, token usage: 0.00, cuda graph: True, gen throughput (token/s): 349.60, #queue-req: 0, 
[2025-07-28 00:33:03 DP1 TP0] Decode batch. #running-req: 1, #token: 775, token usage: 0.00, cuda graph: True, gen throughput (token/s): 234.51, #queue-req: 0, 
[2025-07-28 00:33:03 DP0 TP0] Decode batch. #running-req: 1, #token: 276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.83, #queue-req: 0, 
[2025-07-28 00:33:03 DP1 TP0] Decode batch. #running-req: 1, #token: 815, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.86, #queue-req: 0, 
[2025-07-28 00:33:03 DP0 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.09, #queue-req: 0, 
[2025-07-28 00:33:03 DP1 TP0] Decode batch. #running-req: 1, #token: 855, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.36, #queue-req: 0, 
[2025-07-28 00:33:03 DP0 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 276.35, #queue-req: 0, 
[2025-07-28 00:33:03 DP1 TP0] Decode batch. #running-req: 1, #token: 895, token usage: 0.00, cuda graph: True, gen throughput (token/s): 258.12, #queue-req: 0, 
[2025-07-28 00:33:03 DP0 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 255.97, #queue-req: 0, 
[2025-07-28 00:33:03 DP1 TP0] Decode batch. #running-req: 1, #token: 935, token usage: 0.00, cuda graph: True, gen throughput (token/s): 271.45, #queue-req: 0, 
[2025-07-28 00:33:03 DP0 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 227.97, #queue-req: 0, 
[2025-07-28 00:33:04 DP1 TP0] Decode batch. #running-req: 1, #token: 975, token usage: 0.00, cuda graph: True, gen throughput (token/s): 260.01, #queue-req: 0, 
[2025-07-28 00:33:04 DP0 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 234.70, #queue-req: 0, 
[2025-07-28 00:33:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1015, token usage: 0.00, cuda graph: True, gen throughput (token/s): 264.37, #queue-req: 0, 
[2025-07-28 00:33:04 DP0 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 256.08, #queue-req: 0, 
[2025-07-28 00:33:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1055, token usage: 0.00, cuda graph: True, gen throughput (token/s): 246.07, #queue-req: 0, 
[2025-07-28 00:33:04 DP0 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 252.87, #queue-req: 0, 
[2025-07-28 00:33:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1095, token usage: 0.00, cuda graph: True, gen throughput (token/s): 259.10, #queue-req: 0, 
[2025-07-28 00:33:04 DP0 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 233.79, #queue-req: 0, 
[2025-07-28 00:33:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1135, token usage: 0.00, cuda graph: True, gen throughput (token/s): 262.53, #queue-req: 0, 
[2025-07-28 00:33:04 DP0 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 240.17, #queue-req: 0, 
[2025-07-28 00:33:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1175, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.89, #queue-req: 0, 
[2025-07-28 00:33:04 DP0 TP0] Decode batch. #running-req: 1, #token: 676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 257.86, #queue-req: 0, 
[2025-07-28 00:33:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1215, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.36, #queue-req: 0, 
[2025-07-28 00:33:05 DP0 TP0] Decode batch. #running-req: 1, #token: 716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 258.37, #queue-req: 0, 
[2025-07-28 00:33:05 DP1 TP0] Decode batch. #running-req: 1, #token: 1255, token usage: 0.00, cuda graph: True, gen throughput (token/s): 256.04, #queue-req: 0, 
[2025-07-28 00:33:05 DP0 TP0] Decode batch. #running-req: 1, #token: 756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 264.03, #queue-req: 0, 
[2025-07-28 00:33:05 DP1 TP0] Decode batch. #running-req: 1, #token: 1295, token usage: 0.00, cuda graph: True, gen throughput (token/s): 270.17, #queue-req: 0, 
[2025-07-28 00:33:05 DP0 TP0] Decode batch. #running-req: 1, #token: 796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.79, #queue-req: 0, 
[2025-07-28 00:33:05 DP1 TP0] Decode batch. #running-req: 1, #token: 1335, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.61, #queue-req: 0, 
[2025-07-28 00:33:05 DP0 TP0] Decode batch. #running-req: 1, #token: 836, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.34, #queue-req: 0, 
[2025-07-28 00:33:05 DP1 TP0] Decode batch. #running-req: 1, #token: 1375, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.59, #queue-req: 0, 
[2025-07-28 00:33:05] INFO:     127.0.0.1:47232 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:33:05 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 207, #cached-token: 107, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:33:05 DP1 TP0] Decode batch. #running-req: 2, #token: 1635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 147.39, #queue-req: 0, 
[2025-07-28 00:33:06 DP1 TP0] Decode batch. #running-req: 2, #token: 1715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 539.72, #queue-req: 0, 
[2025-07-28 00:33:06 DP1 TP0] Decode batch. #running-req: 2, #token: 1795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 575.63, #queue-req: 0, 
[2025-07-28 00:33:06 DP1 TP0] Decode batch. #running-req: 2, #token: 1875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 540.25, #queue-req: 0, 
[2025-07-28 00:33:06 DP1 TP0] Decode batch. #running-req: 2, #token: 1955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 552.68, #queue-req: 0, 
[2025-07-28 00:33:06 DP1 TP0] Decode batch. #running-req: 2, #token: 2035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 590.17, #queue-req: 0, 
[2025-07-28 00:33:06 DP1 TP0] Decode batch. #running-req: 2, #token: 2115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 516.84, #queue-req: 0, 
[2025-07-28 00:33:06 DP1 TP0] Decode batch. #running-req: 2, #token: 2195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 551.70, #queue-req: 0, 
[2025-07-28 00:33:07 DP1 TP0] Decode batch. #running-req: 2, #token: 2275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 588.24, #queue-req: 0, 
[2025-07-28 00:33:07 DP1 TP0] Decode batch. #running-req: 2, #token: 2355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 509.09, #queue-req: 0, 
[2025-07-28 00:33:07 DP1 TP0] Decode batch. #running-req: 2, #token: 2435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 505.16, #queue-req: 0, 
[2025-07-28 00:33:07 DP1 TP0] Decode batch. #running-req: 2, #token: 2515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 469.51, #queue-req: 0, 
[2025-07-28 00:33:07 DP1 TP0] Decode batch. #running-req: 2, #token: 2595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 503.12, #queue-req: 0, 
[2025-07-28 00:33:07 DP1 TP0] Decode batch. #running-req: 2, #token: 2675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 532.38, #queue-req: 0, 
[2025-07-28 00:33:08 DP1 TP0] Decode batch. #running-req: 2, #token: 2755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 540.24, #queue-req: 0, 
[2025-07-28 00:33:08 DP1 TP0] Decode batch. #running-req: 2, #token: 2835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 559.26, #queue-req: 0, 
[2025-07-28 00:33:08] INFO:     127.0.0.1:47240 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:33:08 DP1 TP0] Decode batch. #running-req: 1, #token: 2055, token usage: 0.00, cuda graph: True, gen throughput (token/s): 514.87, #queue-req: 0, 
[2025-07-28 00:33:08 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 107, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:33:08 DP0 TP0] Decode batch. #running-req: 1, #token: 241, token usage: 0.00, cuda graph: True, gen throughput (token/s): 13.95, #queue-req: 0, 
[2025-07-28 00:33:08 DP1 TP0] Decode batch. #running-req: 1, #token: 2095, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.95, #queue-req: 0, 
[2025-07-28 00:33:08 DP0 TP0] Decode batch. #running-req: 1, #token: 281, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.26, #queue-req: 0, 
[2025-07-28 00:33:08 DP1 TP0] Decode batch. #running-req: 1, #token: 2135, token usage: 0.00, cuda graph: True, gen throughput (token/s): 259.72, #queue-req: 0, 
[2025-07-28 00:33:08 DP0 TP0] Decode batch. #running-req: 1, #token: 321, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.27, #queue-req: 0, 
[2025-07-28 00:33:08 DP1 TP0] Decode batch. #running-req: 1, #token: 2175, token usage: 0.00, cuda graph: True, gen throughput (token/s): 274.75, #queue-req: 0, 
[2025-07-28 00:33:08 DP0 TP0] Decode batch. #running-req: 1, #token: 361, token usage: 0.00, cuda graph: True, gen throughput (token/s): 276.41, #queue-req: 0, 
[2025-07-28 00:33:08 DP1 TP0] Decode batch. #running-req: 1, #token: 2215, token usage: 0.00, cuda graph: True, gen throughput (token/s): 271.29, #queue-req: 0, 
[2025-07-28 00:33:08 DP0 TP0] Decode batch. #running-req: 1, #token: 401, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.57, #queue-req: 0, 
[2025-07-28 00:33:09 DP1 TP0] Decode batch. #running-req: 1, #token: 2255, token usage: 0.00, cuda graph: True, gen throughput (token/s): 271.83, #queue-req: 0, 
[2025-07-28 00:33:09 DP0 TP0] Decode batch. #running-req: 1, #token: 441, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.48, #queue-req: 0, 
[2025-07-28 00:33:09 DP1 TP0] Decode batch. #running-req: 1, #token: 2295, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.87, #queue-req: 0, 
[2025-07-28 00:33:09 DP0 TP0] Decode batch. #running-req: 1, #token: 481, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.13, #queue-req: 0, 
[2025-07-28 00:33:09 DP1 TP0] Decode batch. #running-req: 1, #token: 2335, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.84, #queue-req: 0, 
[2025-07-28 00:33:09 DP0 TP0] Decode batch. #running-req: 1, #token: 521, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.09, #queue-req: 0, 
[2025-07-28 00:33:09 DP1 TP0] Decode batch. #running-req: 1, #token: 2375, token usage: 0.00, cuda graph: True, gen throughput (token/s): 281.95, #queue-req: 0, 
[2025-07-28 00:33:09 DP0 TP0] Decode batch. #running-req: 1, #token: 561, token usage: 0.00, cuda graph: True, gen throughput (token/s): 273.22, #queue-req: 0, 
[2025-07-28 00:33:09 DP1 TP0] Decode batch. #running-req: 1, #token: 2415, token usage: 0.00, cuda graph: True, gen throughput (token/s): 281.66, #queue-req: 0, 
[2025-07-28 00:33:09 DP0 TP0] Decode batch. #running-req: 1, #token: 601, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.47, #queue-req: 0, 
[2025-07-28 00:33:09 DP1 TP0] Decode batch. #running-req: 1, #token: 2455, token usage: 0.00, cuda graph: True, gen throughput (token/s): 276.28, #queue-req: 0, 
[2025-07-28 00:33:09 DP0 TP0] Decode batch. #running-req: 1, #token: 641, token usage: 0.00, cuda graph: True, gen throughput (token/s): 283.75, #queue-req: 0, 
[2025-07-28 00:33:09 DP1 TP0] Decode batch. #running-req: 1, #token: 2495, token usage: 0.00, cuda graph: True, gen throughput (token/s): 284.91, #queue-req: 0, 
[2025-07-28 00:33:09 DP0 TP0] Decode batch. #running-req: 1, #token: 681, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.57, #queue-req: 0, 
[2025-07-28 00:33:10 DP1 TP0] Decode batch. #running-req: 1, #token: 2535, token usage: 0.00, cuda graph: True, gen throughput (token/s): 276.18, #queue-req: 0, 
[2025-07-28 00:33:10 DP0 TP0] Decode batch. #running-req: 1, #token: 721, token usage: 0.00, cuda graph: True, gen throughput (token/s): 279.12, #queue-req: 0, 
[2025-07-28 00:33:10 DP1 TP0] Decode batch. #running-req: 1, #token: 2575, token usage: 0.00, cuda graph: True, gen throughput (token/s): 278.53, #queue-req: 0, 
[2025-07-28 00:33:10 DP0 TP0] Decode batch. #running-req: 1, #token: 761, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.96, #queue-req: 0, 
[2025-07-28 00:33:10 DP1 TP0] Decode batch. #running-req: 1, #token: 2615, token usage: 0.00, cuda graph: True, gen throughput (token/s): 274.92, #queue-req: 0, 
[2025-07-28 00:33:10 DP0 TP0] Decode batch. #running-req: 1, #token: 801, token usage: 0.00, cuda graph: True, gen throughput (token/s): 262.08, #queue-req: 0, 
[2025-07-28 00:33:10 DP1 TP0] Decode batch. #running-req: 1, #token: 2655, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.96, #queue-req: 0, 
[2025-07-28 00:33:10 DP0 TP0] Decode batch. #running-req: 1, #token: 841, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.93, #queue-req: 0, 
[2025-07-28 00:33:10] INFO:     127.0.0.1:47250 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:33:10 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 133, #cached-token: 106, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:33:10 DP1 TP0] Decode batch. #running-req: 2, #token: 2835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 228.59, #queue-req: 0, 
[2025-07-28 00:33:10 DP1 TP0] Decode batch. #running-req: 2, #token: 2915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 540.76, #queue-req: 0, 
[2025-07-28 00:33:10 DP1 TP0] Decode batch. #running-req: 2, #token: 2995, token usage: 0.00, cuda graph: True, gen throughput (token/s): 524.44, #queue-req: 0, 
[2025-07-28 00:33:11 DP1 TP0] Decode batch. #running-req: 2, #token: 3075, token usage: 0.00, cuda graph: True, gen throughput (token/s): 523.92, #queue-req: 0, 
[2025-07-28 00:33:11 DP1 TP0] Decode batch. #running-req: 2, #token: 3155, token usage: 0.00, cuda graph: True, gen throughput (token/s): 527.09, #queue-req: 0, 
[2025-07-28 00:33:11 DP1 TP0] Decode batch. #running-req: 2, #token: 3235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 532.25, #queue-req: 0, 
[2025-07-28 00:33:11 DP1 TP0] Decode batch. #running-req: 2, #token: 3315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 528.24, #queue-req: 0, 
[2025-07-28 00:33:11 DP1 TP0] Decode batch. #running-req: 2, #token: 3395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 525.49, #queue-req: 0, 
[2025-07-28 00:33:11 DP1 TP0] Decode batch. #running-req: 2, #token: 3475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 531.21, #queue-req: 0, 
[2025-07-28 00:33:12 DP1 TP0] Decode batch. #running-req: 2, #token: 3555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 524.65, #queue-req: 0, 
[2025-07-28 00:33:12 DP1 TP0] Decode batch. #running-req: 2, #token: 3635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 529.67, #queue-req: 0, 
[2025-07-28 00:33:12 DP1 TP0] Decode batch. #running-req: 2, #token: 3715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 529.14, #queue-req: 0, 
[2025-07-28 00:33:12 DP1 TP0] Decode batch. #running-req: 2, #token: 3795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 513.42, #queue-req: 0, 
[2025-07-28 00:33:12 DP1 TP0] Decode batch. #running-req: 2, #token: 3875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 525.97, #queue-req: 0, 
[2025-07-28 00:33:12 DP1 TP0] Decode batch. #running-req: 2, #token: 3955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 522.67, #queue-req: 0, 
[2025-07-28 00:33:12 DP1 TP0] Decode batch. #running-req: 2, #token: 4035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 542.57, #queue-req: 0, 
[2025-07-28 00:33:13 DP1 TP0] Decode batch. #running-req: 2, #token: 4115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 533.96, #queue-req: 0, 
[2025-07-28 00:33:13 DP1 TP0] Decode batch. #running-req: 2, #token: 4195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 549.62, #queue-req: 0, 
[2025-07-28 00:33:13 DP1 TP0] Decode batch. #running-req: 2, #token: 4275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 547.85, #queue-req: 0, 
[2025-07-28 00:33:13 DP1 TP0] Decode batch. #running-req: 2, #token: 4355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 531.44, #queue-req: 0, 
[2025-07-28 00:33:13 DP1 TP0] Decode batch. #running-req: 2, #token: 4435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 567.71, #queue-req: 0, 
[2025-07-28 00:33:13 DP1 TP0] Decode batch. #running-req: 2, #token: 4515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 569.36, #queue-req: 0, 
[2025-07-28 00:33:13 DP1 TP0] Decode batch. #running-req: 2, #token: 4595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 564.24, #queue-req: 0, 
[2025-07-28 00:33:14 DP1 TP0] Decode batch. #running-req: 2, #token: 4675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 574.78, #queue-req: 0, 
[2025-07-28 00:33:14 DP1 TP0] Decode batch. #running-req: 2, #token: 4755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 567.78, #queue-req: 0, 
[2025-07-28 00:33:14 DP1 TP0] Decode batch. #running-req: 2, #token: 4835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 564.12, #queue-req: 0, 
[2025-07-28 00:33:14 DP1 TP0] Decode batch. #running-req: 2, #token: 4915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 505.62, #queue-req: 0, 
[2025-07-28 00:33:14 DP1 TP0] Decode batch. #running-req: 2, #token: 4995, token usage: 0.00, cuda graph: True, gen throughput (token/s): 506.17, #queue-req: 0, 
[2025-07-28 00:33:14 DP1 TP0] Decode batch. #running-req: 2, #token: 5075, token usage: 0.00, cuda graph: True, gen throughput (token/s): 494.73, #queue-req: 0, 
[2025-07-28 00:33:15 DP1 TP0] Decode batch. #running-req: 2, #token: 5155, token usage: 0.00, cuda graph: True, gen throughput (token/s): 520.08, #queue-req: 0, 
[2025-07-28 00:33:15 DP1 TP0] Decode batch. #running-req: 2, #token: 5235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 515.71, #queue-req: 0, 
[2025-07-28 00:33:15 DP1 TP0] Decode batch. #running-req: 2, #token: 5315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 493.99, #queue-req: 0, 
[2025-07-28 00:33:15 DP1 TP0] Decode batch. #running-req: 2, #token: 5395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 519.06, #queue-req: 0, 
[2025-07-28 00:33:15 DP1 TP0] Decode batch. #running-req: 2, #token: 5475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 580.86, #queue-req: 0, 
[2025-07-28 00:33:15 DP1 TP0] Decode batch. #running-req: 2, #token: 5555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 554.90, #queue-req: 0, 
[2025-07-28 00:33:15 DP1 TP0] Decode batch. #running-req: 2, #token: 5635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 589.86, #queue-req: 0, 
[2025-07-28 00:33:16 DP1 TP0] Decode batch. #running-req: 2, #token: 5715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 576.34, #queue-req: 0, 
[2025-07-28 00:33:16 DP1 TP0] Decode batch. #running-req: 2, #token: 5795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 563.32, #queue-req: 0, 
[2025-07-28 00:33:16 DP1 TP0] Decode batch. #running-req: 2, #token: 5875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 596.67, #queue-req: 0, 
[2025-07-28 00:33:16 DP1 TP0] Decode batch. #running-req: 2, #token: 5955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 597.46, #queue-req: 0, 
[2025-07-28 00:33:16 DP1 TP0] Decode batch. #running-req: 2, #token: 6035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 590.51, #queue-req: 0, 
[2025-07-28 00:33:16 DP1 TP0] Decode batch. #running-req: 2, #token: 6115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 594.15, #queue-req: 0, 
[2025-07-28 00:33:16 DP1 TP0] Decode batch. #running-req: 2, #token: 6195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 480.62, #queue-req: 0, 
[2025-07-28 00:33:17 DP1 TP0] Decode batch. #running-req: 2, #token: 6275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 432.42, #queue-req: 0, 
[2025-07-28 00:33:17 DP1 TP0] Decode batch. #running-req: 2, #token: 6355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 414.60, #queue-req: 0, 
[2025-07-28 00:33:17 DP1 TP0] Decode batch. #running-req: 2, #token: 6435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 514.37, #queue-req: 0, 
[2025-07-28 00:33:17 DP1 TP0] Decode batch. #running-req: 2, #token: 6515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 581.96, #queue-req: 0, 
[2025-07-28 00:33:17 DP1 TP0] Decode batch. #running-req: 2, #token: 6595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 535.96, #queue-req: 0, 
[2025-07-28 00:33:17 DP1 TP0] Decode batch. #running-req: 2, #token: 6675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 520.81, #queue-req: 0, 
[2025-07-28 00:33:18 DP1 TP0] Decode batch. #running-req: 2, #token: 6755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 514.33, #queue-req: 0, 
[2025-07-28 00:33:18 DP1 TP0] Decode batch. #running-req: 2, #token: 6835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 533.01, #queue-req: 0, 
[2025-07-28 00:33:18 DP1 TP0] Decode batch. #running-req: 2, #token: 6915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 575.28, #queue-req: 0, 
[2025-07-28 00:33:18 DP1 TP0] Decode batch. #running-req: 2, #token: 6995, token usage: 0.00, cuda graph: True, gen throughput (token/s): 580.48, #queue-req: 0, 
[2025-07-28 00:33:18 DP1 TP0] Decode batch. #running-req: 2, #token: 7075, token usage: 0.00, cuda graph: True, gen throughput (token/s): 543.18, #queue-req: 0, 
[2025-07-28 00:33:18 DP1 TP0] Decode batch. #running-req: 2, #token: 7155, token usage: 0.00, cuda graph: True, gen throughput (token/s): 571.30, #queue-req: 0, 
[2025-07-28 00:33:18 DP1 TP0] Decode batch. #running-req: 2, #token: 7235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 570.30, #queue-req: 0, 
[2025-07-28 00:33:19 DP1 TP0] Decode batch. #running-req: 2, #token: 7315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 554.25, #queue-req: 0, 
[2025-07-28 00:33:19 DP1 TP0] Decode batch. #running-req: 2, #token: 7395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 564.88, #queue-req: 0, 
[2025-07-28 00:33:19 DP1 TP0] Decode batch. #running-req: 2, #token: 7475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 582.98, #queue-req: 0, 
[2025-07-28 00:33:19 DP1 TP0] Decode batch. #running-req: 2, #token: 7555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 568.01, #queue-req: 0, 
[2025-07-28 00:33:19 DP1 TP0] Decode batch. #running-req: 2, #token: 7635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 549.98, #queue-req: 0, 
[2025-07-28 00:33:19 DP1 TP0] Decode batch. #running-req: 2, #token: 7715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 538.41, #queue-req: 0, 
[2025-07-28 00:33:19 DP1 TP0] Decode batch. #running-req: 2, #token: 7795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 521.97, #queue-req: 0, 
[2025-07-28 00:33:20 DP1 TP0] Decode batch. #running-req: 2, #token: 7875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 539.33, #queue-req: 0, 
[2025-07-28 00:33:20 DP1 TP0] Decode batch. #running-req: 2, #token: 7955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 565.80, #queue-req: 0, 
[2025-07-28 00:33:20 DP1 TP0] Decode batch. #running-req: 2, #token: 8035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 586.24, #queue-req: 0, 
[2025-07-28 00:33:20 DP1 TP0] Decode batch. #running-req: 2, #token: 8115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 587.02, #queue-req: 0, 
[2025-07-28 00:33:20 DP1 TP0] Decode batch. #running-req: 2, #token: 8195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 562.82, #queue-req: 0, 
[2025-07-28 00:33:20 DP1 TP0] Decode batch. #running-req: 2, #token: 8275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 585.63, #queue-req: 0, 
[2025-07-28 00:33:20 DP1 TP0] Decode batch. #running-req: 2, #token: 8355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 597.21, #queue-req: 0, 
[2025-07-28 00:33:21 DP1 TP0] Decode batch. #running-req: 2, #token: 8435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 590.73, #queue-req: 0, 
[2025-07-28 00:33:21 DP1 TP0] Decode batch. #running-req: 2, #token: 8515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 555.70, #queue-req: 0, 
[2025-07-28 00:33:21 DP1 TP0] Decode batch. #running-req: 2, #token: 8595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 587.83, #queue-req: 0, 
[2025-07-28 00:33:21 DP1 TP0] Decode batch. #running-req: 2, #token: 8675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 577.82, #queue-req: 0, 
[2025-07-28 00:33:21 DP1 TP0] Decode batch. #running-req: 2, #token: 8755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 584.66, #queue-req: 0, 
[2025-07-28 00:33:21 DP1 TP0] Decode batch. #running-req: 2, #token: 8835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 584.50, #queue-req: 0, 
[2025-07-28 00:33:21 DP1 TP0] Decode batch. #running-req: 2, #token: 8915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 576.54, #queue-req: 0, 
[2025-07-28 00:33:21 DP1 TP0] Decode batch. #running-req: 2, #token: 8995, token usage: 0.00, cuda graph: True, gen throughput (token/s): 597.86, #queue-req: 0, 
[2025-07-28 00:33:22 DP1 TP0] Decode batch. #running-req: 2, #token: 9075, token usage: 0.00, cuda graph: True, gen throughput (token/s): 597.06, #queue-req: 0, 
[2025-07-28 00:33:22 DP1 TP0] Decode batch. #running-req: 2, #token: 9155, token usage: 0.00, cuda graph: True, gen throughput (token/s): 579.26, #queue-req: 0, 
[2025-07-28 00:33:22 DP1 TP0] Decode batch. #running-req: 2, #token: 9235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 567.76, #queue-req: 0, 
[2025-07-28 00:33:22 DP1 TP0] Decode batch. #running-req: 2, #token: 9315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 561.44, #queue-req: 0, 
[2025-07-28 00:33:22 DP1 TP0] Decode batch. #running-req: 2, #token: 9395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 581.79, #queue-req: 0, 
[2025-07-28 00:33:22 DP1 TP0] Decode batch. #running-req: 2, #token: 9475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 580.89, #queue-req: 0, 
[2025-07-28 00:33:22 DP1 TP0] Decode batch. #running-req: 2, #token: 9555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 546.35, #queue-req: 0, 
[2025-07-28 00:33:23 DP1 TP0] Decode batch. #running-req: 2, #token: 9635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 537.34, #queue-req: 0, 
[2025-07-28 00:33:23 DP1 TP0] Decode batch. #running-req: 2, #token: 9715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 552.73, #queue-req: 0, 
[2025-07-28 00:33:23 DP1 TP0] Decode batch. #running-req: 2, #token: 9795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 594.71, #queue-req: 0, 
[2025-07-28 00:33:23 DP1 TP0] Decode batch. #running-req: 2, #token: 9875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 599.09, #queue-req: 0, 
[2025-07-28 00:33:23 DP1 TP0] Decode batch. #running-req: 2, #token: 9955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 597.15, #queue-req: 0, 
[2025-07-28 00:33:23 DP1 TP0] Decode batch. #running-req: 2, #token: 10035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 587.99, #queue-req: 0, 
[2025-07-28 00:33:23 DP1 TP0] Decode batch. #running-req: 2, #token: 10115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 575.65, #queue-req: 0, 
[2025-07-28 00:33:24 DP1 TP0] Decode batch. #running-req: 2, #token: 10195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 602.28, #queue-req: 0, 
[2025-07-28 00:33:24 DP1 TP0] Decode batch. #running-req: 2, #token: 10275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 598.16, #queue-req: 0, 
[2025-07-28 00:33:24 DP1 TP0] Decode batch. #running-req: 2, #token: 10355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 581.80, #queue-req: 0, 
[2025-07-28 00:33:24 DP1 TP0] Decode batch. #running-req: 2, #token: 10435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 590.59, #queue-req: 0, 
[2025-07-28 00:33:24 DP1 TP0] Decode batch. #running-req: 2, #token: 10515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 582.57, #queue-req: 0, 
[2025-07-28 00:33:24 DP1 TP0] Decode batch. #running-req: 2, #token: 10595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 573.41, #queue-req: 0, 
[2025-07-28 00:33:24 DP1 TP0] Decode batch. #running-req: 2, #token: 10675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 566.98, #queue-req: 0, 
[2025-07-28 00:33:25 DP1 TP0] Decode batch. #running-req: 2, #token: 10755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 586.55, #queue-req: 0, 
[2025-07-28 00:33:25 DP1 TP0] Decode batch. #running-req: 2, #token: 10835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 569.46, #queue-req: 0, 
[2025-07-28 00:33:25 DP1 TP0] Decode batch. #running-req: 2, #token: 10915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 586.86, #queue-req: 0, 
[2025-07-28 00:33:25 DP1 TP0] Decode batch. #running-req: 2, #token: 10995, token usage: 0.00, cuda graph: True, gen throughput (token/s): 577.77, #queue-req: 0, 
[2025-07-28 00:33:25 DP1 TP0] Decode batch. #running-req: 2, #token: 11075, token usage: 0.00, cuda graph: True, gen throughput (token/s): 585.50, #queue-req: 0, 
[2025-07-28 00:33:25 DP1 TP0] Decode batch. #running-req: 2, #token: 11155, token usage: 0.00, cuda graph: True, gen throughput (token/s): 587.48, #queue-req: 0, 
[2025-07-28 00:33:25 DP1 TP0] Decode batch. #running-req: 2, #token: 11235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 586.42, #queue-req: 0, 
[2025-07-28 00:33:25 DP1 TP0] Decode batch. #running-req: 2, #token: 11315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 584.79, #queue-req: 0, 
[2025-07-28 00:33:26 DP1 TP0] Decode batch. #running-req: 2, #token: 11395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 579.32, #queue-req: 0, 
[2025-07-28 00:33:26 DP1 TP0] Decode batch. #running-req: 2, #token: 11475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 584.59, #queue-req: 0, 
[2025-07-28 00:33:26 DP1 TP0] Decode batch. #running-req: 2, #token: 11555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 582.99, #queue-req: 0, 
[2025-07-28 00:33:26 DP1 TP0] Decode batch. #running-req: 2, #token: 11635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 581.37, #queue-req: 0, 
[2025-07-28 00:33:26 DP1 TP0] Decode batch. #running-req: 2, #token: 11715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 569.11, #queue-req: 0, 
[2025-07-28 00:33:26 DP1 TP0] Decode batch. #running-req: 2, #token: 11795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 579.21, #queue-req: 0, 
[2025-07-28 00:33:26 DP1 TP0] Decode batch. #running-req: 2, #token: 11875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 587.47, #queue-req: 0, 
[2025-07-28 00:33:27 DP1 TP0] Decode batch. #running-req: 2, #token: 11955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 583.42, #queue-req: 0, 
[2025-07-28 00:33:27 DP1 TP0] Decode batch. #running-req: 2, #token: 12035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 564.83, #queue-req: 0, 
[2025-07-28 00:33:27 DP1 TP0] Decode batch. #running-req: 2, #token: 12115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 563.08, #queue-req: 0, 
[2025-07-28 00:33:27 DP1 TP0] Decode batch. #running-req: 2, #token: 12195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 546.73, #queue-req: 0, 
[2025-07-28 00:33:27 DP1 TP0] Decode batch. #running-req: 2, #token: 12275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 563.91, #queue-req: 0, 
[2025-07-28 00:33:27 DP1 TP0] Decode batch. #running-req: 2, #token: 12355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 559.51, #queue-req: 0, 
[2025-07-28 00:33:27 DP1 TP0] Decode batch. #running-req: 2, #token: 12435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 562.74, #queue-req: 0, 
[2025-07-28 00:33:28 DP1 TP0] Decode batch. #running-req: 2, #token: 12515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 568.68, #queue-req: 0, 
[2025-07-28 00:33:28 DP1 TP0] Decode batch. #running-req: 2, #token: 12595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 580.36, #queue-req: 0, 
[2025-07-28 00:33:28 DP1 TP0] Decode batch. #running-req: 2, #token: 12675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 575.64, #queue-req: 0, 
[2025-07-28 00:33:28 DP1 TP0] Decode batch. #running-req: 2, #token: 12755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 581.74, #queue-req: 0, 
[2025-07-28 00:33:28 DP1 TP0] Decode batch. #running-req: 2, #token: 12835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 580.00, #queue-req: 0, 
[2025-07-28 00:33:28 DP1 TP0] Decode batch. #running-req: 2, #token: 12915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 573.84, #queue-req: 0, 
[2025-07-28 00:33:28 DP1 TP0] Decode batch. #running-req: 2, #token: 12995, token usage: 0.00, cuda graph: True, gen throughput (token/s): 583.36, #queue-req: 0, 
[2025-07-28 00:33:29 DP1 TP0] Decode batch. #running-req: 2, #token: 13075, token usage: 0.00, cuda graph: True, gen throughput (token/s): 571.23, #queue-req: 0, 
[2025-07-28 00:33:29 DP1 TP0] Decode batch. #running-req: 2, #token: 13155, token usage: 0.00, cuda graph: True, gen throughput (token/s): 582.87, #queue-req: 0, 
[2025-07-28 00:33:29 DP1 TP0] Decode batch. #running-req: 2, #token: 13235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 580.03, #queue-req: 0, 
[2025-07-28 00:33:29 DP1 TP0] Decode batch. #running-req: 2, #token: 13315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 588.48, #queue-req: 0, 
[2025-07-28 00:33:29 DP1 TP0] Decode batch. #running-req: 2, #token: 13395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 592.70, #queue-req: 0, 
[2025-07-28 00:33:29 DP1 TP0] Decode batch. #running-req: 2, #token: 13475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 574.02, #queue-req: 0, 
[2025-07-28 00:33:29 DP1 TP0] Decode batch. #running-req: 2, #token: 13555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 576.97, #queue-req: 0, 
[2025-07-28 00:33:30 DP1 TP0] Decode batch. #running-req: 2, #token: 13635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 569.80, #queue-req: 0, 
[2025-07-28 00:33:30 DP1 TP0] Decode batch. #running-req: 2, #token: 13715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 579.52, #queue-req: 0, 
[2025-07-28 00:33:30 DP1 TP0] Decode batch. #running-req: 2, #token: 13795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 587.52, #queue-req: 0, 
[2025-07-28 00:33:30 DP1 TP0] Decode batch. #running-req: 2, #token: 13875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 534.43, #queue-req: 0, 
[2025-07-28 00:33:30 DP1 TP0] Decode batch. #running-req: 2, #token: 13955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 592.42, #queue-req: 0, 
[2025-07-28 00:33:30 DP1 TP0] Decode batch. #running-req: 2, #token: 14035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 579.19, #queue-req: 0, 
[2025-07-28 00:33:30 DP1 TP0] Decode batch. #running-req: 2, #token: 14115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 578.97, #queue-req: 0, 
[2025-07-28 00:33:30 DP1 TP0] Decode batch. #running-req: 2, #token: 14195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 563.58, #queue-req: 0, 
[2025-07-28 00:33:31] INFO:     127.0.0.1:51416 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:33:31 DP1 TP0] Decode batch. #running-req: 1, #token: 5966, token usage: 0.00, cuda graph: True, gen throughput (token/s): 504.60, #queue-req: 0, 
[2025-07-28 00:33:31 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 186, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:33:31 DP1 TP0] Decode batch. #running-req: 1, #token: 6006, token usage: 0.00, cuda graph: True, gen throughput (token/s): 280.71, #queue-req: 0, 
[2025-07-28 00:33:31 DP0 TP0] Decode batch. #running-req: 1, #token: 318, token usage: 0.00, cuda graph: True, gen throughput (token/s): 1.92, #queue-req: 0, 
[2025-07-28 00:33:31 DP1 TP0] Decode batch. #running-req: 1, #token: 6046, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.17, #queue-req: 0, 
[2025-07-28 00:33:31 DP0 TP0] Decode batch. #running-req: 1, #token: 358, token usage: 0.00, cuda graph: True, gen throughput (token/s): 280.57, #queue-req: 0, 
[2025-07-28 00:33:31 DP1 TP0] Decode batch. #running-req: 1, #token: 6086, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.24, #queue-req: 0, 
[2025-07-28 00:33:31 DP0 TP0] Decode batch. #running-req: 1, #token: 398, token usage: 0.00, cuda graph: True, gen throughput (token/s): 278.55, #queue-req: 0, 
[2025-07-28 00:33:31 DP1 TP0] Decode batch. #running-req: 1, #token: 6126, token usage: 0.00, cuda graph: True, gen throughput (token/s): 270.14, #queue-req: 0, 
[2025-07-28 00:33:31 DP0 TP0] Decode batch. #running-req: 1, #token: 438, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.99, #queue-req: 0, 
[2025-07-28 00:33:31 DP1 TP0] Decode batch. #running-req: 1, #token: 6166, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.15, #queue-req: 0, 
[2025-07-28 00:33:31 DP0 TP0] Decode batch. #running-req: 1, #token: 478, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.53, #queue-req: 0, 
[2025-07-28 00:33:31 DP1 TP0] Decode batch. #running-req: 1, #token: 6206, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.56, #queue-req: 0, 
[2025-07-28 00:33:32 DP0 TP0] Decode batch. #running-req: 1, #token: 518, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.21, #queue-req: 0, 
[2025-07-28 00:33:32 DP1 TP0] Decode batch. #running-req: 1, #token: 6246, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.32, #queue-req: 0, 
[2025-07-28 00:33:32 DP0 TP0] Decode batch. #running-req: 1, #token: 558, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.84, #queue-req: 0, 
[2025-07-28 00:33:32 DP1 TP0] Decode batch. #running-req: 1, #token: 6286, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.82, #queue-req: 0, 
[2025-07-28 00:33:32 DP0 TP0] Decode batch. #running-req: 1, #token: 598, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.94, #queue-req: 0, 
[2025-07-28 00:33:32 DP1 TP0] Decode batch. #running-req: 1, #token: 6326, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.46, #queue-req: 0, 
[2025-07-28 00:33:32 DP0 TP0] Decode batch. #running-req: 1, #token: 638, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.58, #queue-req: 0, 
[2025-07-28 00:33:32 DP1 TP0] Decode batch. #running-req: 1, #token: 6366, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.76, #queue-req: 0, 
[2025-07-28 00:33:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.00, #queue-req: 0, 
[2025-07-28 00:33:32] INFO:     127.0.0.1:48084 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:33:32 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 173, #cached-token: 108, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:33:32 DP1 TP0] Decode batch. #running-req: 2, #token: 6591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 254.63, #queue-req: 0, 
[2025-07-28 00:33:32 DP1 TP0] Decode batch. #running-req: 2, #token: 6671, token usage: 0.00, cuda graph: True, gen throughput (token/s): 612.03, #queue-req: 0, 
[2025-07-28 00:33:32 DP1 TP0] Decode batch. #running-req: 2, #token: 6751, token usage: 0.00, cuda graph: True, gen throughput (token/s): 596.33, #queue-req: 0, 
[2025-07-28 00:33:33 DP1 TP0] Decode batch. #running-req: 2, #token: 6831, token usage: 0.00, cuda graph: True, gen throughput (token/s): 580.60, #queue-req: 0, 
[2025-07-28 00:33:33 DP1 TP0] Decode batch. #running-req: 2, #token: 6911, token usage: 0.00, cuda graph: True, gen throughput (token/s): 560.47, #queue-req: 0, 
[2025-07-28 00:33:33 DP1 TP0] Decode batch. #running-req: 2, #token: 6991, token usage: 0.00, cuda graph: True, gen throughput (token/s): 587.13, #queue-req: 0, 
[2025-07-28 00:33:33 DP1 TP0] Decode batch. #running-req: 2, #token: 7071, token usage: 0.00, cuda graph: True, gen throughput (token/s): 566.73, #queue-req: 0, 
[2025-07-28 00:33:33 DP1 TP0] Decode batch. #running-req: 2, #token: 7151, token usage: 0.00, cuda graph: True, gen throughput (token/s): 583.37, #queue-req: 0, 
[2025-07-28 00:33:33 DP1 TP0] Decode batch. #running-req: 2, #token: 7231, token usage: 0.00, cuda graph: True, gen throughput (token/s): 573.92, #queue-req: 0, 
[2025-07-28 00:33:33 DP1 TP0] Decode batch. #running-req: 2, #token: 7311, token usage: 0.00, cuda graph: True, gen throughput (token/s): 580.36, #queue-req: 0, 
[2025-07-28 00:33:34 DP1 TP0] Decode batch. #running-req: 2, #token: 7391, token usage: 0.00, cuda graph: True, gen throughput (token/s): 585.37, #queue-req: 0, 
[2025-07-28 00:33:34 DP1 TP0] Decode batch. #running-req: 2, #token: 7471, token usage: 0.00, cuda graph: True, gen throughput (token/s): 569.19, #queue-req: 0, 
[2025-07-28 00:33:34 DP1 TP0] Decode batch. #running-req: 2, #token: 7551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 588.05, #queue-req: 0, 
[2025-07-28 00:33:34 DP1 TP0] Decode batch. #running-req: 2, #token: 7631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 570.89, #queue-req: 0, 
[2025-07-28 00:33:34 DP1 TP0] Decode batch. #running-req: 2, #token: 7711, token usage: 0.00, cuda graph: True, gen throughput (token/s): 589.74, #queue-req: 0, 
[2025-07-28 00:33:34] INFO:     127.0.0.1:58878 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:33:34 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 226, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:33:34 DP1 TP0] Decode batch. #running-req: 1, #token: 7006, token usage: 0.00, cuda graph: True, gen throughput (token/s): 450.83, #queue-req: 0, 
[2025-07-28 00:33:34 DP1 TP0] Decode batch. #running-req: 1, #token: 7046, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.28, #queue-req: 0, 
[2025-07-28 00:33:34 DP0 TP0] Decode batch. #running-req: 1, #token: 374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 16.98, #queue-req: 0, 
[2025-07-28 00:33:35 DP1 TP0] Decode batch. #running-req: 1, #token: 7086, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.35, #queue-req: 0, 
[2025-07-28 00:33:35 DP0 TP0] Decode batch. #running-req: 1, #token: 414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.09, #queue-req: 0, 
[2025-07-28 00:33:35 DP1 TP0] Decode batch. #running-req: 1, #token: 7126, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.08, #queue-req: 0, 
[2025-07-28 00:33:35 DP0 TP0] Decode batch. #running-req: 1, #token: 454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 272.06, #queue-req: 0, 
[2025-07-28 00:33:35 DP1 TP0] Decode batch. #running-req: 1, #token: 7166, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.86, #queue-req: 0, 
[2025-07-28 00:33:35 DP0 TP0] Decode batch. #running-req: 1, #token: 494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 275.98, #queue-req: 0, 
[2025-07-28 00:33:35 DP1 TP0] Decode batch. #running-req: 1, #token: 7206, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.26, #queue-req: 0, 
[2025-07-28 00:33:35 DP0 TP0] Decode batch. #running-req: 1, #token: 534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 258.82, #queue-req: 0, 
[2025-07-28 00:33:35 DP1 TP0] Decode batch. #running-req: 1, #token: 7246, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.24, #queue-req: 0, 
[2025-07-28 00:33:35 DP0 TP0] Decode batch. #running-req: 1, #token: 574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.31, #queue-req: 0, 
[2025-07-28 00:33:35 DP1 TP0] Decode batch. #running-req: 1, #token: 7286, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.52, #queue-req: 0, 
[2025-07-28 00:33:35 DP0 TP0] Decode batch. #running-req: 1, #token: 614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.76, #queue-req: 0, 
[2025-07-28 00:33:35 DP1 TP0] Decode batch. #running-req: 1, #token: 7326, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.76, #queue-req: 0, 
[2025-07-28 00:33:35 DP0 TP0] Decode batch. #running-req: 1, #token: 654, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.90, #queue-req: 0, 
[2025-07-28 00:33:35 DP1 TP0] Decode batch. #running-req: 1, #token: 7366, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.64, #queue-req: 0, 
[2025-07-28 00:33:36 DP0 TP0] Decode batch. #running-req: 1, #token: 694, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.14, #queue-req: 0, 
[2025-07-28 00:33:36 DP1 TP0] Decode batch. #running-req: 1, #token: 7406, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.40, #queue-req: 0, 
[2025-07-28 00:33:36 DP0 TP0] Decode batch. #running-req: 1, #token: 734, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.75, #queue-req: 0, 
[2025-07-28 00:33:36 DP1 TP0] Decode batch. #running-req: 1, #token: 7446, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.92, #queue-req: 0, 
[2025-07-28 00:33:36 DP1 TP0] Decode batch. #running-req: 1, #token: 7486, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.49, #queue-req: 0, 
[2025-07-28 00:33:36 DP0 TP0] Decode batch. #running-req: 1, #token: 774, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.02, #queue-req: 0, 
[2025-07-28 00:33:36] INFO:     127.0.0.1:58882 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:33:36 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 168, #cached-token: 109, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:33:36 DP1 TP0] Decode batch. #running-req: 2, #token: 7712, token usage: 0.00, cuda graph: True, gen throughput (token/s): 277.12, #queue-req: 0, 
[2025-07-28 00:33:36 DP1 TP0] Decode batch. #running-req: 2, #token: 7792, token usage: 0.00, cuda graph: True, gen throughput (token/s): 623.03, #queue-req: 0, 
[2025-07-28 00:33:36 DP1 TP0] Decode batch. #running-req: 2, #token: 7872, token usage: 0.00, cuda graph: True, gen throughput (token/s): 591.45, #queue-req: 0, 
[2025-07-28 00:33:36 DP1 TP0] Decode batch. #running-req: 2, #token: 7952, token usage: 0.00, cuda graph: True, gen throughput (token/s): 581.81, #queue-req: 0, 
[2025-07-28 00:33:37 DP1 TP0] Decode batch. #running-req: 2, #token: 8032, token usage: 0.00, cuda graph: True, gen throughput (token/s): 586.53, #queue-req: 0, 
[2025-07-28 00:33:37 DP1 TP0] Decode batch. #running-req: 2, #token: 8112, token usage: 0.00, cuda graph: True, gen throughput (token/s): 563.41, #queue-req: 0, 
[2025-07-28 00:33:37 DP1 TP0] Decode batch. #running-req: 2, #token: 8192, token usage: 0.00, cuda graph: True, gen throughput (token/s): 585.36, #queue-req: 0, 
[2025-07-28 00:33:37 DP1 TP0] Decode batch. #running-req: 2, #token: 8272, token usage: 0.00, cuda graph: True, gen throughput (token/s): 587.92, #queue-req: 0, 
[2025-07-28 00:33:37 DP1 TP0] Decode batch. #running-req: 2, #token: 8352, token usage: 0.00, cuda graph: True, gen throughput (token/s): 549.98, #queue-req: 0, 
[2025-07-28 00:33:37] INFO:     127.0.0.1:58898 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:33:37 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 154, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:33:37 DP1 TP0] Decode batch. #running-req: 1, #token: 7886, token usage: 0.00, cuda graph: True, gen throughput (token/s): 378.96, #queue-req: 0, 
[2025-07-28 00:33:37 DP0 TP0] Decode batch. #running-req: 1, #token: 282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 26.34, #queue-req: 0, 
[2025-07-28 00:33:37 DP1 TP0] Decode batch. #running-req: 1, #token: 7926, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.99, #queue-req: 0, 
[2025-07-28 00:33:37 DP0 TP0] Decode batch. #running-req: 1, #token: 322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.87, #queue-req: 0, 
[2025-07-28 00:33:38 DP1 TP0] Decode batch. #running-req: 1, #token: 7966, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.87, #queue-req: 0, 
[2025-07-28 00:33:38 DP0 TP0] Decode batch. #running-req: 1, #token: 362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 282.62, #queue-req: 0, 
[2025-07-28 00:33:38 DP1 TP0] Decode batch. #running-req: 1, #token: 8006, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.89, #queue-req: 0, 
[2025-07-28 00:33:38 DP0 TP0] Decode batch. #running-req: 1, #token: 402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.51, #queue-req: 0, 
[2025-07-28 00:33:38 DP1 TP0] Decode batch. #running-req: 1, #token: 8046, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.69, #queue-req: 0, 
[2025-07-28 00:33:38 DP0 TP0] Decode batch. #running-req: 1, #token: 442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.23, #queue-req: 0, 
[2025-07-28 00:33:38 DP1 TP0] Decode batch. #running-req: 1, #token: 8086, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.61, #queue-req: 0, 
[2025-07-28 00:33:38 DP0 TP0] Decode batch. #running-req: 1, #token: 482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.22, #queue-req: 0, 
[2025-07-28 00:33:38 DP1 TP0] Decode batch. #running-req: 1, #token: 8126, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.17, #queue-req: 0, 
[2025-07-28 00:33:38 DP0 TP0] Decode batch. #running-req: 1, #token: 522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.98, #queue-req: 0, 
[2025-07-28 00:33:38 DP1 TP0] Decode batch. #running-req: 1, #token: 8166, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.12, #queue-req: 0, 
[2025-07-28 00:33:38 DP0 TP0] Decode batch. #running-req: 1, #token: 562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.87, #queue-req: 0, 
[2025-07-28 00:33:38 DP1 TP0] Decode batch. #running-req: 1, #token: 8206, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.55, #queue-req: 0, 
[2025-07-28 00:33:38] INFO:     127.0.0.1:58912 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:33:38 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 141, #cached-token: 106, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:33:39 DP1 TP0] Decode batch. #running-req: 2, #token: 8411, token usage: 0.00, cuda graph: True, gen throughput (token/s): 339.41, #queue-req: 0, 
[2025-07-28 00:33:39 DP1 TP0] Decode batch. #running-req: 2, #token: 8491, token usage: 0.00, cuda graph: True, gen throughput (token/s): 554.40, #queue-req: 0, 
[2025-07-28 00:33:39 DP1 TP0] Decode batch. #running-req: 2, #token: 8571, token usage: 0.00, cuda graph: True, gen throughput (token/s): 571.72, #queue-req: 0, 
[2025-07-28 00:33:39 DP1 TP0] Decode batch. #running-req: 2, #token: 8651, token usage: 0.00, cuda graph: True, gen throughput (token/s): 540.04, #queue-req: 0, 
[2025-07-28 00:33:39 DP1 TP0] Decode batch. #running-req: 2, #token: 8731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 542.09, #queue-req: 0, 
[2025-07-28 00:33:39] INFO:     127.0.0.1:47258 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:33:39 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 192, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:33:39 DP1 TP0] Decode batch. #running-req: 1, #token: 471, token usage: 0.00, cuda graph: True, gen throughput (token/s): 487.32, #queue-req: 0, 
[2025-07-28 00:33:39 DP0 TP0] Decode batch. #running-req: 1, #token: 319, token usage: 0.00, cuda graph: True, gen throughput (token/s): 38.64, #queue-req: 0, 
[2025-07-28 00:33:39 DP1 TP0] Decode batch. #running-req: 1, #token: 511, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.12, #queue-req: 0, 
[2025-07-28 00:33:39 DP0 TP0] Decode batch. #running-req: 1, #token: 359, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.51, #queue-req: 0, 
[2025-07-28 00:33:40 DP1 TP0] Decode batch. #running-req: 1, #token: 551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.84, #queue-req: 0, 
[2025-07-28 00:33:40 DP0 TP0] Decode batch. #running-req: 1, #token: 399, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.34, #queue-req: 0, 
[2025-07-28 00:33:40] INFO:     127.0.0.1:58928 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:33:40 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 190, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:33:40 DP1 TP0] Decode batch. #running-req: 1, #token: 306, token usage: 0.00, cuda graph: True, gen throughput (token/s): 196.51, #queue-req: 0, 
[2025-07-28 00:33:40 DP0 TP0] Decode batch. #running-req: 1, #token: 439, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.89, #queue-req: 0, 
[2025-07-28 00:33:40 DP1 TP0] Decode batch. #running-req: 1, #token: 346, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.37, #queue-req: 0, 
[2025-07-28 00:33:40 DP0 TP0] Decode batch. #running-req: 1, #token: 479, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.49, #queue-req: 0, 
[2025-07-28 00:33:40 DP1 TP0] Decode batch. #running-req: 1, #token: 386, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.64, #queue-req: 0, 
[2025-07-28 00:33:40 DP0 TP0] Decode batch. #running-req: 1, #token: 519, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.58, #queue-req: 0, 
[2025-07-28 00:33:40 DP1 TP0] Decode batch. #running-req: 1, #token: 426, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.66, #queue-req: 0, 
[2025-07-28 00:33:40 DP0 TP0] Decode batch. #running-req: 1, #token: 559, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.87, #queue-req: 0, 
[2025-07-28 00:33:40 DP1 TP0] Decode batch. #running-req: 1, #token: 466, token usage: 0.00, cuda graph: True, gen throughput (token/s): 285.83, #queue-req: 0, 
[2025-07-28 00:33:40 DP0 TP0] Decode batch. #running-req: 1, #token: 599, token usage: 0.00, cuda graph: True, gen throughput (token/s): 262.49, #queue-req: 0, 
[2025-07-28 00:33:40 DP1 TP0] Decode batch. #running-req: 1, #token: 506, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.76, #queue-req: 0, 
[2025-07-28 00:33:40 DP0 TP0] Decode batch. #running-req: 1, #token: 639, token usage: 0.00, cuda graph: True, gen throughput (token/s): 289.20, #queue-req: 0, 
[2025-07-28 00:33:41 DP1 TP0] Decode batch. #running-req: 1, #token: 546, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.87, #queue-req: 0, 
[2025-07-28 00:33:41 DP0 TP0] Decode batch. #running-req: 1, #token: 679, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.14, #queue-req: 0, 
[2025-07-28 00:33:41 DP1 TP0] Decode batch. #running-req: 1, #token: 586, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.21, #queue-req: 0, 
[2025-07-28 00:33:41 DP0 TP0] Decode batch. #running-req: 1, #token: 719, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.26, #queue-req: 0, 
[2025-07-28 00:33:41 DP1 TP0] Decode batch. #running-req: 1, #token: 626, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.93, #queue-req: 0, 
[2025-07-28 00:33:41 DP0 TP0] Decode batch. #running-req: 1, #token: 759, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.68, #queue-req: 0, 
[2025-07-28 00:33:41 DP1 TP0] Decode batch. #running-req: 1, #token: 666, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.97, #queue-req: 0, 
[2025-07-28 00:33:41 DP0 TP0] Decode batch. #running-req: 1, #token: 799, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.09, #queue-req: 0, 
[2025-07-28 00:33:41 DP1 TP0] Decode batch. #running-req: 1, #token: 706, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.64, #queue-req: 0, 
[2025-07-28 00:33:41 DP0 TP0] Decode batch. #running-req: 1, #token: 839, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.34, #queue-req: 0, 
[2025-07-28 00:33:41 DP1 TP0] Decode batch. #running-req: 1, #token: 746, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.75, #queue-req: 0, 
[2025-07-28 00:33:41 DP0 TP0] Decode batch. #running-req: 1, #token: 879, token usage: 0.00, cuda graph: True, gen throughput (token/s): 274.00, #queue-req: 0, 
[2025-07-28 00:33:41 DP1 TP0] Decode batch. #running-req: 1, #token: 786, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.22, #queue-req: 0, 
[2025-07-28 00:33:41 DP0 TP0] Decode batch. #running-req: 1, #token: 919, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.59, #queue-req: 0, 
[2025-07-28 00:33:41 DP1 TP0] Decode batch. #running-req: 1, #token: 826, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.34, #queue-req: 0, 
[2025-07-28 00:33:41] INFO:     127.0.0.1:58936 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:33:41 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 137, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:33:42 DP1 TP0] Decode batch. #running-req: 1, #token: 866, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.49, #queue-req: 0, 
[2025-07-28 00:33:42 DP0 TP0] Decode batch. #running-req: 1, #token: 265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.27, #queue-req: 0, 
[2025-07-28 00:33:42] INFO:     127.0.0.1:58948 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:33:42 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 132, #cached-token: 110, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:33:42 DP0 TP0] Decode batch. #running-req: 1, #token: 305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 329.07, #queue-req: 0, 
[2025-07-28 00:33:42 DP1 TP0] Decode batch. #running-req: 1, #token: 267, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.06, #queue-req: 0, 
[2025-07-28 00:33:42 DP0 TP0] Decode batch. #running-req: 1, #token: 345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 327.83, #queue-req: 0, 
[2025-07-28 00:33:42 DP1 TP0] Decode batch. #running-req: 1, #token: 307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 325.35, #queue-req: 0, 
[2025-07-28 00:33:42 DP0 TP0] Decode batch. #running-req: 1, #token: 385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.75, #queue-req: 0, 
[2025-07-28 00:33:42 DP1 TP0] Decode batch. #running-req: 1, #token: 347, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.75, #queue-req: 0, 
[2025-07-28 00:33:42 DP0 TP0] Decode batch. #running-req: 1, #token: 425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.34, #queue-req: 0, 
[2025-07-28 00:33:42 DP1 TP0] Decode batch. #running-req: 1, #token: 387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.02, #queue-req: 0, 
[2025-07-28 00:33:42 DP0 TP0] Decode batch. #running-req: 1, #token: 465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.89, #queue-req: 0, 
[2025-07-28 00:33:42 DP1 TP0] Decode batch. #running-req: 1, #token: 427, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.30, #queue-req: 0, 
[2025-07-28 00:33:42 DP0 TP0] Decode batch. #running-req: 1, #token: 505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.23, #queue-req: 0, 
[2025-07-28 00:33:42 DP1 TP0] Decode batch. #running-req: 1, #token: 467, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.89, #queue-req: 0, 
[2025-07-28 00:33:42 DP0 TP0] Decode batch. #running-req: 1, #token: 545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.54, #queue-req: 0, 
[2025-07-28 00:33:43 DP1 TP0] Decode batch. #running-req: 1, #token: 507, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.07, #queue-req: 0, 
[2025-07-28 00:33:43 DP0 TP0] Decode batch. #running-req: 1, #token: 585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.26, #queue-req: 0, 
[2025-07-28 00:33:43 DP1 TP0] Decode batch. #running-req: 1, #token: 547, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.65, #queue-req: 0, 
[2025-07-28 00:33:43 DP0 TP0] Decode batch. #running-req: 1, #token: 625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.87, #queue-req: 0, 
[2025-07-28 00:33:43 DP1 TP0] Decode batch. #running-req: 1, #token: 587, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.25, #queue-req: 0, 
[2025-07-28 00:33:43 DP0 TP0] Decode batch. #running-req: 1, #token: 665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.70, #queue-req: 0, 
[2025-07-28 00:33:43 DP1 TP0] Decode batch. #running-req: 1, #token: 627, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.26, #queue-req: 0, 
[2025-07-28 00:33:43 DP0 TP0] Decode batch. #running-req: 1, #token: 705, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.85, #queue-req: 0, 
[2025-07-28 00:33:43 DP1 TP0] Decode batch. #running-req: 1, #token: 667, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.09, #queue-req: 0, 
[2025-07-28 00:33:43 DP0 TP0] Decode batch. #running-req: 1, #token: 745, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.60, #queue-req: 0, 
[2025-07-28 00:33:43 DP1 TP0] Decode batch. #running-req: 1, #token: 707, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.77, #queue-req: 0, 
[2025-07-28 00:33:43 DP0 TP0] Decode batch. #running-req: 1, #token: 785, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.87, #queue-req: 0, 
[2025-07-28 00:33:43 DP1 TP0] Decode batch. #running-req: 1, #token: 747, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.43, #queue-req: 0, 
[2025-07-28 00:33:43 DP0 TP0] Decode batch. #running-req: 1, #token: 825, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.33, #queue-req: 0, 
[2025-07-28 00:33:43 DP1 TP0] Decode batch. #running-req: 1, #token: 787, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.36, #queue-req: 0, 
[2025-07-28 00:33:43] INFO:     127.0.0.1:58958 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:33:43 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 199, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:33:44 DP1 TP0] Decode batch. #running-req: 1, #token: 827, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.84, #queue-req: 0, 
[2025-07-28 00:33:44 DP0 TP0] Decode batch. #running-req: 1, #token: 333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 200.25, #queue-req: 0, 
[2025-07-28 00:33:44 DP1 TP0] Decode batch. #running-req: 1, #token: 867, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.28, #queue-req: 0, 
[2025-07-28 00:33:44 DP0 TP0] Decode batch. #running-req: 1, #token: 373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.60, #queue-req: 0, 
[2025-07-28 00:33:44 DP1 TP0] Decode batch. #running-req: 1, #token: 907, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.42, #queue-req: 0, 
[2025-07-28 00:33:44] INFO:     127.0.0.1:58974 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:33:44 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 194, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:33:44 DP0 TP0] Decode batch. #running-req: 1, #token: 413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.54, #queue-req: 0, 
[2025-07-28 00:33:44 DP0 TP0] Decode batch. #running-req: 1, #token: 453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.13, #queue-req: 0, 
[2025-07-28 00:33:44 DP1 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.71, #queue-req: 0, 
[2025-07-28 00:33:44 DP1 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 332.43, #queue-req: 0, 
[2025-07-28 00:33:44 DP0 TP0] Decode batch. #running-req: 1, #token: 493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.86, #queue-req: 0, 
[2025-07-28 00:33:44 DP1 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 326.00, #queue-req: 0, 
[2025-07-28 00:33:44 DP0 TP0] Decode batch. #running-req: 1, #token: 533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.69, #queue-req: 0, 
[2025-07-28 00:33:44 DP1 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.95, #queue-req: 0, 
[2025-07-28 00:33:44 DP0 TP0] Decode batch. #running-req: 1, #token: 573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.98, #queue-req: 0, 
[2025-07-28 00:33:45 DP1 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.61, #queue-req: 0, 
[2025-07-28 00:33:45 DP0 TP0] Decode batch. #running-req: 1, #token: 613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.72, #queue-req: 0, 
[2025-07-28 00:33:45 DP1 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.07, #queue-req: 0, 
[2025-07-28 00:33:45 DP0 TP0] Decode batch. #running-req: 1, #token: 653, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.83, #queue-req: 0, 
[2025-07-28 00:33:45 DP1 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.71, #queue-req: 0, 
[2025-07-28 00:33:45 DP0 TP0] Decode batch. #running-req: 1, #token: 693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.41, #queue-req: 0, 
[2025-07-28 00:33:45] INFO:     127.0.0.1:56660 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:33:45 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 167, #cached-token: 108, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:33:45 DP0 TP0] Decode batch. #running-req: 2, #token: 926, token usage: 0.00, cuda graph: True, gen throughput (token/s): 348.68, #queue-req: 0, 
[2025-07-28 00:33:45] INFO:     127.0.0.1:56658 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:33:45 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 172, #cached-token: 110, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:33:45 DP0 TP0] Decode batch. #running-req: 1, #token: 341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 395.28, #queue-req: 0, 
[2025-07-28 00:33:45 DP1 TP0] Decode batch. #running-req: 1, #token: 304, token usage: 0.00, cuda graph: True, gen throughput (token/s): 100.92, #queue-req: 0, 
[2025-07-28 00:33:45 DP0 TP0] Decode batch. #running-req: 1, #token: 381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.88, #queue-req: 0, 
[2025-07-28 00:33:45 DP1 TP0] Decode batch. #running-req: 1, #token: 344, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.81, #queue-req: 0, 
[2025-07-28 00:33:45 DP0 TP0] Decode batch. #running-req: 1, #token: 421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.64, #queue-req: 0, 
[2025-07-28 00:33:45 DP1 TP0] Decode batch. #running-req: 1, #token: 384, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.12, #queue-req: 0, 
[2025-07-28 00:33:46 DP0 TP0] Decode batch. #running-req: 1, #token: 461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.49, #queue-req: 0, 
[2025-07-28 00:33:46 DP1 TP0] Decode batch. #running-req: 1, #token: 424, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.33, #queue-req: 0, 
[2025-07-28 00:33:46 DP0 TP0] Decode batch. #running-req: 1, #token: 501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.15, #queue-req: 0, 
[2025-07-28 00:33:46 DP1 TP0] Decode batch. #running-req: 1, #token: 464, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.11, #queue-req: 0, 
[2025-07-28 00:33:46 DP0 TP0] Decode batch. #running-req: 1, #token: 541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.10, #queue-req: 0, 
[2025-07-28 00:33:46 DP1 TP0] Decode batch. #running-req: 1, #token: 504, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.04, #queue-req: 0, 
[2025-07-28 00:33:46 DP0 TP0] Decode batch. #running-req: 1, #token: 581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.48, #queue-req: 0, 
[2025-07-28 00:33:46 DP1 TP0] Decode batch. #running-req: 1, #token: 544, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.44, #queue-req: 0, 
[2025-07-28 00:33:46 DP0 TP0] Decode batch. #running-req: 1, #token: 621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.33, #queue-req: 0, 
[2025-07-28 00:33:46 DP1 TP0] Decode batch. #running-req: 1, #token: 584, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.86, #queue-req: 0, 
[2025-07-28 00:33:46 DP0 TP0] Decode batch. #running-req: 1, #token: 661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.65, #queue-req: 0, 
[2025-07-28 00:33:46 DP1 TP0] Decode batch. #running-req: 1, #token: 624, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.23, #queue-req: 0, 
[2025-07-28 00:33:46 DP0 TP0] Decode batch. #running-req: 1, #token: 701, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.83, #queue-req: 0, 
[2025-07-28 00:33:46 DP1 TP0] Decode batch. #running-req: 1, #token: 664, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.33, #queue-req: 0, 
[2025-07-28 00:33:46 DP0 TP0] Decode batch. #running-req: 1, #token: 741, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.77, #queue-req: 0, 
[2025-07-28 00:33:47 DP1 TP0] Decode batch. #running-req: 1, #token: 704, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.58, #queue-req: 0, 
[2025-07-28 00:33:47 DP0 TP0] Decode batch. #running-req: 1, #token: 781, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.42, #queue-req: 0, 
[2025-07-28 00:33:47 DP1 TP0] Decode batch. #running-req: 1, #token: 744, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.58, #queue-req: 0, 
[2025-07-28 00:33:47 DP0 TP0] Decode batch. #running-req: 1, #token: 821, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.53, #queue-req: 0, 
[2025-07-28 00:33:47 DP1 TP0] Decode batch. #running-req: 1, #token: 784, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.08, #queue-req: 0, 
[2025-07-28 00:33:47 DP0 TP0] Decode batch. #running-req: 1, #token: 861, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.54, #queue-req: 0, 
[2025-07-28 00:33:47 DP1 TP0] Decode batch. #running-req: 1, #token: 824, token usage: 0.00, cuda graph: True, gen throughput (token/s): 293.36, #queue-req: 0, 
[2025-07-28 00:33:47 DP0 TP0] Decode batch. #running-req: 1, #token: 901, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.72, #queue-req: 0, 
[2025-07-28 00:33:47 DP1 TP0] Decode batch. #running-req: 1, #token: 864, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.37, #queue-req: 0, 
[2025-07-28 00:33:47 DP0 TP0] Decode batch. #running-req: 1, #token: 941, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.83, #queue-req: 0, 
[2025-07-28 00:33:47 DP1 TP0] Decode batch. #running-req: 1, #token: 904, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.98, #queue-req: 0, 
[2025-07-28 00:33:47 DP0 TP0] Decode batch. #running-req: 1, #token: 981, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.22, #queue-req: 0, 
[2025-07-28 00:33:47 DP1 TP0] Decode batch. #running-req: 1, #token: 944, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.74, #queue-req: 0, 
[2025-07-28 00:33:47 DP0 TP0] Decode batch. #running-req: 1, #token: 1021, token usage: 0.00, cuda graph: True, gen throughput (token/s): 282.67, #queue-req: 0, 
[2025-07-28 00:33:47 DP1 TP0] Decode batch. #running-req: 1, #token: 984, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.66, #queue-req: 0, 
[2025-07-28 00:33:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1061, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.38, #queue-req: 0, 
[2025-07-28 00:33:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1024, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.14, #queue-req: 0, 
[2025-07-28 00:33:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1101, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.87, #queue-req: 0, 
[2025-07-28 00:33:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1064, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.10, #queue-req: 0, 
[2025-07-28 00:33:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1141, token usage: 0.00, cuda graph: True, gen throughput (token/s): 292.92, #queue-req: 0, 
[2025-07-28 00:33:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1104, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.53, #queue-req: 0, 
[2025-07-28 00:33:48 DP0 TP0] Decode batch. #running-req: 1, #token: 1181, token usage: 0.00, cuda graph: True, gen throughput (token/s): 291.07, #queue-req: 0, 
[2025-07-28 00:33:48 DP1 TP0] Decode batch. #running-req: 1, #token: 1144, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.23, #queue-req: 0, 
[2025-07-28 00:33:48] INFO:     127.0.0.1:56672 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:33:48] INFO:     127.0.0.1:56662 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:33:48 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 219, #cached-token: 108, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:33:48 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 155, #cached-token: 111, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:33:48 DP0 TP0] Decode batch. #running-req: 1, #token: 346, token usage: 0.00, cuda graph: True, gen throughput (token/s): 204.94, #queue-req: 0, 
[2025-07-28 00:33:48 DP1 TP0] Decode batch. #running-req: 1, #token: 289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 190.28, #queue-req: 0, 
[2025-07-28 00:33:48 DP0 TP0] Decode batch. #running-req: 1, #token: 386, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.57, #queue-req: 0, 
[2025-07-28 00:33:48 DP1 TP0] Decode batch. #running-req: 1, #token: 329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.66, #queue-req: 0, 
[2025-07-28 00:33:48 DP0 TP0] Decode batch. #running-req: 1, #token: 426, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.31, #queue-req: 0, 
[2025-07-28 00:33:48 DP1 TP0] Decode batch. #running-req: 1, #token: 369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.05, #queue-req: 0, 
[2025-07-28 00:33:49 DP0 TP0] Decode batch. #running-req: 1, #token: 466, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.39, #queue-req: 0, 
[2025-07-28 00:33:49 DP1 TP0] Decode batch. #running-req: 1, #token: 409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.18, #queue-req: 0, 
[2025-07-28 00:33:49 DP0 TP0] Decode batch. #running-req: 1, #token: 506, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.01, #queue-req: 0, 
[2025-07-28 00:33:49 DP1 TP0] Decode batch. #running-req: 1, #token: 449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.54, #queue-req: 0, 
[2025-07-28 00:33:49 DP1 TP0] Decode batch. #running-req: 1, #token: 489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.76, #queue-req: 0, 
[2025-07-28 00:33:49 DP0 TP0] Decode batch. #running-req: 1, #token: 546, token usage: 0.00, cuda graph: True, gen throughput (token/s): 283.03, #queue-req: 0, 
[2025-07-28 00:33:49 DP1 TP0] Decode batch. #running-req: 1, #token: 529, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.46, #queue-req: 0, 
[2025-07-28 00:33:49 DP0 TP0] Decode batch. #running-req: 1, #token: 586, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.37, #queue-req: 0, 
[2025-07-28 00:33:49 DP1 TP0] Decode batch. #running-req: 1, #token: 569, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.01, #queue-req: 0, 
[2025-07-28 00:33:49 DP0 TP0] Decode batch. #running-req: 1, #token: 626, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.29, #queue-req: 0, 
[2025-07-28 00:33:49 DP1 TP0] Decode batch. #running-req: 1, #token: 609, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.16, #queue-req: 0, 
[2025-07-28 00:33:49 DP0 TP0] Decode batch. #running-req: 1, #token: 666, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.64, #queue-req: 0, 
[2025-07-28 00:33:49 DP1 TP0] Decode batch. #running-req: 1, #token: 649, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.19, #queue-req: 0, 
[2025-07-28 00:33:49 DP0 TP0] Decode batch. #running-req: 1, #token: 706, token usage: 0.00, cuda graph: True, gen throughput (token/s): 295.65, #queue-req: 0, 
[2025-07-28 00:33:49 DP1 TP0] Decode batch. #running-req: 1, #token: 689, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.66, #queue-req: 0, 
[2025-07-28 00:33:49 DP0 TP0] Decode batch. #running-req: 1, #token: 746, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.15, #queue-req: 0, 
[2025-07-28 00:33:50 DP1 TP0] Decode batch. #running-req: 1, #token: 729, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.32, #queue-req: 0, 
[2025-07-28 00:33:50 DP0 TP0] Decode batch. #running-req: 1, #token: 786, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.83, #queue-req: 0, 
[2025-07-28 00:33:50 DP1 TP0] Decode batch. #running-req: 1, #token: 769, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.92, #queue-req: 0, 
[2025-07-28 00:33:50 DP0 TP0] Decode batch. #running-req: 1, #token: 826, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.22, #queue-req: 0, 
[2025-07-28 00:33:50 DP1 TP0] Decode batch. #running-req: 1, #token: 809, token usage: 0.00, cuda graph: True, gen throughput (token/s): 290.71, #queue-req: 0, 
[2025-07-28 00:33:50 DP0 TP0] Decode batch. #running-req: 1, #token: 866, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.70, #queue-req: 0, 
[2025-07-28 00:33:50 DP1 TP0] Decode batch. #running-req: 1, #token: 849, token usage: 0.00, cuda graph: True, gen throughput (token/s): 278.28, #queue-req: 0, 
[2025-07-28 00:33:50 DP0 TP0] Decode batch. #running-req: 1, #token: 906, token usage: 0.00, cuda graph: True, gen throughput (token/s): 282.27, #queue-req: 0, 
[2025-07-28 00:33:50] INFO:     127.0.0.1:56692 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:33:50 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 175, #cached-token: 110, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:33:50 DP0 TP0] Decode batch. #running-req: 2, #token: 1148, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.70, #queue-req: 0, 
[2025-07-28 00:33:50] INFO:     127.0.0.1:56678 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:33:50 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 94, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:33:50 DP0 TP0] Decode batch. #running-req: 1, #token: 350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 407.35, #queue-req: 0, 
[2025-07-28 00:33:50 DP1 TP0] Decode batch. #running-req: 1, #token: 225, token usage: 0.00, cuda graph: True, gen throughput (token/s): 88.36, #queue-req: 0, 
[2025-07-28 00:33:51 DP0 TP0] Decode batch. #running-req: 1, #token: 390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.45, #queue-req: 0, 
[2025-07-28 00:33:51 DP1 TP0] Decode batch. #running-req: 1, #token: 265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.68, #queue-req: 0, 
[2025-07-28 00:33:51 DP0 TP0] Decode batch. #running-req: 1, #token: 430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.53, #queue-req: 0, 
[2025-07-28 00:33:51 DP1 TP0] Decode batch. #running-req: 1, #token: 305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.53, #queue-req: 0, 
[2025-07-28 00:33:51 DP0 TP0] Decode batch. #running-req: 1, #token: 470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.41, #queue-req: 0, 
[2025-07-28 00:33:51 DP1 TP0] Decode batch. #running-req: 1, #token: 345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.59, #queue-req: 0, 
[2025-07-28 00:33:51 DP0 TP0] Decode batch. #running-req: 1, #token: 510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.07, #queue-req: 0, 
[2025-07-28 00:33:51 DP1 TP0] Decode batch. #running-req: 1, #token: 385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 287.77, #queue-req: 0, 
[2025-07-28 00:33:51 DP0 TP0] Decode batch. #running-req: 1, #token: 550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 262.75, #queue-req: 0, 
[2025-07-28 00:33:51 DP1 TP0] Decode batch. #running-req: 1, #token: 425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 262.92, #queue-req: 0, 
[2025-07-28 00:33:51 DP0 TP0] Decode batch. #running-req: 1, #token: 590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 216.97, #queue-req: 0, 
[2025-07-28 00:33:51 DP1 TP0] Decode batch. #running-req: 1, #token: 465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 227.40, #queue-req: 0, 
[2025-07-28 00:33:51 DP0 TP0] Decode batch. #running-req: 1, #token: 630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 253.88, #queue-req: 0, 
[2025-07-28 00:33:52 DP1 TP0] Decode batch. #running-req: 1, #token: 505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 221.35, #queue-req: 0, 
[2025-07-28 00:33:52 DP0 TP0] Decode batch. #running-req: 1, #token: 670, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.20, #queue-req: 0, 
[2025-07-28 00:33:52] INFO:     127.0.0.1:56720 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:33:52 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 115, #cached-token: 106, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:33:52 DP0 TP0] Decode batch. #running-req: 2, #token: 837, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.80, #queue-req: 0, 
[2025-07-28 00:33:52] INFO:     127.0.0.1:56706 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:33:52 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 150, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:33:52 DP0 TP0] Decode batch. #running-req: 1, #token: 273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 457.06, #queue-req: 0, 
[2025-07-28 00:33:52 DP1 TP0] Decode batch. #running-req: 1, #token: 263, token usage: 0.00, cuda graph: True, gen throughput (token/s): 97.97, #queue-req: 0, 
[2025-07-28 00:33:52 DP0 TP0] Decode batch. #running-req: 1, #token: 313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.84, #queue-req: 0, 
[2025-07-28 00:33:52 DP1 TP0] Decode batch. #running-req: 1, #token: 303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.23, #queue-req: 0, 
[2025-07-28 00:33:52 DP0 TP0] Decode batch. #running-req: 1, #token: 353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.75, #queue-req: 0, 
[2025-07-28 00:33:52 DP1 TP0] Decode batch. #running-req: 1, #token: 343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.39, #queue-req: 0, 
[2025-07-28 00:33:52 DP0 TP0] Decode batch. #running-req: 1, #token: 393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.19, #queue-req: 0, 
[2025-07-28 00:33:52 DP1 TP0] Decode batch. #running-req: 1, #token: 383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.35, #queue-req: 0, 
[2025-07-28 00:33:52 DP0 TP0] Decode batch. #running-req: 1, #token: 433, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.39, #queue-req: 0, 
[2025-07-28 00:33:52 DP1 TP0] Decode batch. #running-req: 1, #token: 423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.42, #queue-req: 0, 
[2025-07-28 00:33:53 DP0 TP0] Decode batch. #running-req: 1, #token: 473, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.10, #queue-req: 0, 
[2025-07-28 00:33:53 DP1 TP0] Decode batch. #running-req: 1, #token: 463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.13, #queue-req: 0, 
[2025-07-28 00:33:53 DP0 TP0] Decode batch. #running-req: 1, #token: 513, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.91, #queue-req: 0, 
[2025-07-28 00:33:53 DP1 TP0] Decode batch. #running-req: 1, #token: 503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.64, #queue-req: 0, 
[2025-07-28 00:33:53 DP0 TP0] Decode batch. #running-req: 1, #token: 553, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.01, #queue-req: 0, 
[2025-07-28 00:33:53 DP1 TP0] Decode batch. #running-req: 1, #token: 543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.89, #queue-req: 0, 
[2025-07-28 00:33:53 DP0 TP0] Decode batch. #running-req: 1, #token: 593, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.78, #queue-req: 0, 
[2025-07-28 00:33:53 DP1 TP0] Decode batch. #running-req: 1, #token: 583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.41, #queue-req: 0, 
[2025-07-28 00:33:53 DP0 TP0] Decode batch. #running-req: 1, #token: 633, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.02, #queue-req: 0, 
[2025-07-28 00:33:53 DP1 TP0] Decode batch. #running-req: 1, #token: 623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.00, #queue-req: 0, 
[2025-07-28 00:33:53 DP0 TP0] Decode batch. #running-req: 1, #token: 673, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.24, #queue-req: 0, 
[2025-07-28 00:33:53] INFO:     127.0.0.1:56736 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:33:53 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 148, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:33:53 DP1 TP0] Decode batch. #running-req: 1, #token: 663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.71, #queue-req: 0, 
[2025-07-28 00:33:53 DP1 TP0] Decode batch. #running-req: 1, #token: 703, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.61, #queue-req: 0, 
[2025-07-28 00:33:53 DP0 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 205.30, #queue-req: 0, 
[2025-07-28 00:33:53 DP1 TP0] Decode batch. #running-req: 1, #token: 743, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.02, #queue-req: 0, 
[2025-07-28 00:33:54 DP0 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.48, #queue-req: 0, 
[2025-07-28 00:33:54 DP1 TP0] Decode batch. #running-req: 1, #token: 783, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.06, #queue-req: 0, 
[2025-07-28 00:33:54 DP0 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.46, #queue-req: 0, 
[2025-07-28 00:33:54] INFO:     127.0.0.1:47962 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:33:54 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 160, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:33:54 DP0 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.85, #queue-req: 0, 
[2025-07-28 00:33:54 DP1 TP0] Decode batch. #running-req: 1, #token: 275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 197.93, #queue-req: 0, 
[2025-07-28 00:33:54 DP0 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.84, #queue-req: 0, 
[2025-07-28 00:33:54 DP1 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 324.11, #queue-req: 0, 
[2025-07-28 00:33:54 DP0 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.61, #queue-req: 0, 
[2025-07-28 00:33:54 DP1 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.80, #queue-req: 0, 
[2025-07-28 00:33:54 DP0 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.18, #queue-req: 0, 
[2025-07-28 00:33:54 DP1 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.24, #queue-req: 0, 
[2025-07-28 00:33:54 DP0 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.09, #queue-req: 0, 
[2025-07-28 00:33:54 DP1 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.54, #queue-req: 0, 
[2025-07-28 00:33:54 DP0 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.30, #queue-req: 0, 
[2025-07-28 00:33:54 DP1 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.64, #queue-req: 0, 
[2025-07-28 00:33:55 DP0 TP0] Decode batch. #running-req: 1, #token: 656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.66, #queue-req: 0, 
[2025-07-28 00:33:55 DP1 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.69, #queue-req: 0, 
[2025-07-28 00:33:55 DP0 TP0] Decode batch. #running-req: 1, #token: 696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.51, #queue-req: 0, 
[2025-07-28 00:33:55 DP1 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.95, #queue-req: 0, 
[2025-07-28 00:33:55 DP0 TP0] Decode batch. #running-req: 1, #token: 736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.58, #queue-req: 0, 
[2025-07-28 00:33:55 DP1 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.83, #queue-req: 0, 
[2025-07-28 00:33:55 DP0 TP0] Decode batch. #running-req: 1, #token: 776, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.48, #queue-req: 0, 
[2025-07-28 00:33:55 DP1 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.91, #queue-req: 0, 
[2025-07-28 00:33:55 DP0 TP0] Decode batch. #running-req: 1, #token: 816, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.56, #queue-req: 0, 
[2025-07-28 00:33:55 DP1 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.71, #queue-req: 0, 
[2025-07-28 00:33:55 DP0 TP0] Decode batch. #running-req: 1, #token: 856, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.61, #queue-req: 0, 
[2025-07-28 00:33:55 DP1 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 283.57, #queue-req: 0, 
[2025-07-28 00:33:55 DP0 TP0] Decode batch. #running-req: 1, #token: 896, token usage: 0.00, cuda graph: True, gen throughput (token/s): 281.00, #queue-req: 0, 
[2025-07-28 00:33:55 DP1 TP0] Decode batch. #running-req: 1, #token: 755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 272.95, #queue-req: 0, 
[2025-07-28 00:33:56 DP0 TP0] Decode batch. #running-req: 1, #token: 936, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.73, #queue-req: 0, 
[2025-07-28 00:33:56 DP1 TP0] Decode batch. #running-req: 1, #token: 795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.85, #queue-req: 0, 
[2025-07-28 00:33:56 DP0 TP0] Decode batch. #running-req: 1, #token: 976, token usage: 0.00, cuda graph: True, gen throughput (token/s): 274.24, #queue-req: 0, 
[2025-07-28 00:33:56 DP1 TP0] Decode batch. #running-req: 1, #token: 835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 286.80, #queue-req: 0, 
[2025-07-28 00:33:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1016, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.35, #queue-req: 0, 
[2025-07-28 00:33:56 DP1 TP0] Decode batch. #running-req: 1, #token: 875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.44, #queue-req: 0, 
[2025-07-28 00:33:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1056, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.22, #queue-req: 0, 
[2025-07-28 00:33:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.77, #queue-req: 0, 
[2025-07-28 00:33:56] INFO:     127.0.0.1:47968 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:33:56 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 114, #cached-token: 109, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:33:56 DP0 TP0] Decode batch. #running-req: 2, #token: 1232, token usage: 0.00, cuda graph: True, gen throughput (token/s): 325.20, #queue-req: 0, 
[2025-07-28 00:33:56] INFO:     127.0.0.1:47966 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:33:56 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 80, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:33:56 DP0 TP0] Decode batch. #running-req: 1, #token: 284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 508.79, #queue-req: 0, 
[2025-07-28 00:33:56 DP0 TP0] Decode batch. #running-req: 1, #token: 324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.47, #queue-req: 0, 
[2025-07-28 00:33:56 DP1 TP0] Decode batch. #running-req: 1, #token: 226, token usage: 0.00, cuda graph: True, gen throughput (token/s): 89.38, #queue-req: 0, 
[2025-07-28 00:33:56 DP0 TP0] Decode batch. #running-req: 1, #token: 364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.18, #queue-req: 0, 
[2025-07-28 00:33:57 DP1 TP0] Decode batch. #running-req: 1, #token: 266, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.33, #queue-req: 0, 
[2025-07-28 00:33:57 DP0 TP0] Decode batch. #running-req: 1, #token: 404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.13, #queue-req: 0, 
[2025-07-28 00:33:57 DP1 TP0] Decode batch. #running-req: 1, #token: 306, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.77, #queue-req: 0, 
[2025-07-28 00:33:57 DP0 TP0] Decode batch. #running-req: 1, #token: 444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.97, #queue-req: 0, 
[2025-07-28 00:33:57 DP1 TP0] Decode batch. #running-req: 1, #token: 346, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.37, #queue-req: 0, 
[2025-07-28 00:33:57 DP0 TP0] Decode batch. #running-req: 1, #token: 484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.45, #queue-req: 0, 
[2025-07-28 00:33:57 DP1 TP0] Decode batch. #running-req: 1, #token: 386, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.37, #queue-req: 0, 
[2025-07-28 00:33:57 DP0 TP0] Decode batch. #running-req: 1, #token: 524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.62, #queue-req: 0, 
[2025-07-28 00:33:57 DP1 TP0] Decode batch. #running-req: 1, #token: 426, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.27, #queue-req: 0, 
[2025-07-28 00:33:57 DP0 TP0] Decode batch. #running-req: 1, #token: 564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 294.39, #queue-req: 0, 
[2025-07-28 00:33:57 DP1 TP0] Decode batch. #running-req: 1, #token: 466, token usage: 0.00, cuda graph: True, gen throughput (token/s): 298.64, #queue-req: 0, 
[2025-07-28 00:33:57 DP0 TP0] Decode batch. #running-req: 1, #token: 604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.18, #queue-req: 0, 
[2025-07-28 00:33:57 DP1 TP0] Decode batch. #running-req: 1, #token: 506, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.06, #queue-req: 0, 
[2025-07-28 00:33:57 DP0 TP0] Decode batch. #running-req: 1, #token: 644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.08, #queue-req: 0, 
[2025-07-28 00:33:57 DP1 TP0] Decode batch. #running-req: 1, #token: 546, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.12, #queue-req: 0, 
[2025-07-28 00:33:58 DP1 TP0] Decode batch. #running-req: 1, #token: 586, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.88, #queue-req: 0, 
[2025-07-28 00:33:58 DP0 TP0] Decode batch. #running-req: 1, #token: 684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.59, #queue-req: 0, 
[2025-07-28 00:33:58 DP1 TP0] Decode batch. #running-req: 1, #token: 626, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.79, #queue-req: 0, 
[2025-07-28 00:33:58 DP0 TP0] Decode batch. #running-req: 1, #token: 724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 264.37, #queue-req: 0, 
[2025-07-28 00:33:58] INFO:     127.0.0.1:47976 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:33:58 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 136, #cached-token: 106, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:33:58 DP0 TP0] Decode batch. #running-req: 2, #token: 917, token usage: 0.00, cuda graph: True, gen throughput (token/s): 276.49, #queue-req: 0, 
[2025-07-28 00:33:58 DP0 TP0] Decode batch. #running-req: 2, #token: 997, token usage: 0.00, cuda graph: True, gen throughput (token/s): 597.11, #queue-req: 0, 
[2025-07-28 00:33:58 DP0 TP0] Decode batch. #running-req: 2, #token: 1077, token usage: 0.00, cuda graph: True, gen throughput (token/s): 588.08, #queue-req: 0, 
[2025-07-28 00:33:58 DP0 TP0] Decode batch. #running-req: 2, #token: 1157, token usage: 0.00, cuda graph: True, gen throughput (token/s): 579.90, #queue-req: 0, 
[2025-07-28 00:33:58 DP0 TP0] Decode batch. #running-req: 2, #token: 1237, token usage: 0.00, cuda graph: True, gen throughput (token/s): 583.53, #queue-req: 0, 
[2025-07-28 00:33:59 DP0 TP0] Decode batch. #running-req: 2, #token: 1317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 587.01, #queue-req: 0, 
[2025-07-28 00:33:59 DP0 TP0] Decode batch. #running-req: 2, #token: 1397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 585.76, #queue-req: 0, 
[2025-07-28 00:33:59] INFO:     127.0.0.1:47984 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:33:59 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 137, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:33:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1044, token usage: 0.00, cuda graph: True, gen throughput (token/s): 501.74, #queue-req: 0, 
[2025-07-28 00:33:59 DP1 TP0] Decode batch. #running-req: 1, #token: 257, token usage: 0.00, cuda graph: True, gen throughput (token/s): 31.54, #queue-req: 0, 
[2025-07-28 00:33:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1084, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.09, #queue-req: 0, 
[2025-07-28 00:33:59 DP1 TP0] Decode batch. #running-req: 1, #token: 297, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.26, #queue-req: 0, 
[2025-07-28 00:33:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1124, token usage: 0.00, cuda graph: True, gen throughput (token/s): 296.53, #queue-req: 0, 
[2025-07-28 00:33:59 DP1 TP0] Decode batch. #running-req: 1, #token: 337, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.16, #queue-req: 0, 
[2025-07-28 00:33:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1164, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.80, #queue-req: 0, 
[2025-07-28 00:33:59 DP1 TP0] Decode batch. #running-req: 1, #token: 377, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.50, #queue-req: 0, 
[2025-07-28 00:33:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1204, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.53, #queue-req: 0, 
[2025-07-28 00:34:00 DP1 TP0] Decode batch. #running-req: 1, #token: 417, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.60, #queue-req: 0, 
[2025-07-28 00:34:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.04, #queue-req: 0, 
[2025-07-28 00:34:00 DP1 TP0] Decode batch. #running-req: 1, #token: 457, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.51, #queue-req: 0, 
[2025-07-28 00:34:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.13, #queue-req: 0, 
[2025-07-28 00:34:00 DP1 TP0] Decode batch. #running-req: 1, #token: 497, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.52, #queue-req: 0, 
[2025-07-28 00:34:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.26, #queue-req: 0, 
[2025-07-28 00:34:00 DP1 TP0] Decode batch. #running-req: 1, #token: 537, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.21, #queue-req: 0, 
[2025-07-28 00:34:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.11, #queue-req: 0, 
[2025-07-28 00:34:00 DP1 TP0] Decode batch. #running-req: 1, #token: 577, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.13, #queue-req: 0, 
[2025-07-28 00:34:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.83, #queue-req: 0, 
[2025-07-28 00:34:00 DP1 TP0] Decode batch. #running-req: 1, #token: 617, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.34, #queue-req: 0, 
[2025-07-28 00:34:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.17, #queue-req: 0, 
[2025-07-28 00:34:00 DP1 TP0] Decode batch. #running-req: 1, #token: 657, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.33, #queue-req: 0, 
[2025-07-28 00:34:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.42, #queue-req: 0, 
[2025-07-28 00:34:00 DP1 TP0] Decode batch. #running-req: 1, #token: 697, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.37, #queue-req: 0, 
[2025-07-28 00:34:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.26, #queue-req: 0, 
[2025-07-28 00:34:01 DP1 TP0] Decode batch. #running-req: 1, #token: 737, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.99, #queue-req: 0, 
[2025-07-28 00:34:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.83, #queue-req: 0, 
[2025-07-28 00:34:01 DP1 TP0] Decode batch. #running-req: 1, #token: 777, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.05, #queue-req: 0, 
[2025-07-28 00:34:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.51, #queue-req: 0, 
[2025-07-28 00:34:01 DP1 TP0] Decode batch. #running-req: 1, #token: 817, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.70, #queue-req: 0, 
[2025-07-28 00:34:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.83, #queue-req: 0, 
[2025-07-28 00:34:01] INFO:     127.0.0.1:47986 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:01 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 85, #cached-token: 106, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:34:01 DP0 TP0] Decode batch. #running-req: 2, #token: 1791, token usage: 0.00, cuda graph: True, gen throughput (token/s): 338.81, #queue-req: 0, 
[2025-07-28 00:34:01 DP0 TP0] Decode batch. #running-req: 2, #token: 1871, token usage: 0.00, cuda graph: True, gen throughput (token/s): 602.52, #queue-req: 0, 
[2025-07-28 00:34:01 DP0 TP0] Decode batch. #running-req: 2, #token: 1951, token usage: 0.00, cuda graph: True, gen throughput (token/s): 582.51, #queue-req: 0, 
[2025-07-28 00:34:01 DP0 TP0] Decode batch. #running-req: 2, #token: 2031, token usage: 0.00, cuda graph: True, gen throughput (token/s): 595.01, #queue-req: 0, 
[2025-07-28 00:34:02 DP0 TP0] Decode batch. #running-req: 2, #token: 2111, token usage: 0.00, cuda graph: True, gen throughput (token/s): 588.71, #queue-req: 0, 
[2025-07-28 00:34:02 DP0 TP0] Decode batch. #running-req: 2, #token: 2191, token usage: 0.00, cuda graph: True, gen throughput (token/s): 594.74, #queue-req: 0, 
[2025-07-28 00:34:02 DP0 TP0] Decode batch. #running-req: 2, #token: 2271, token usage: 0.00, cuda graph: True, gen throughput (token/s): 591.03, #queue-req: 0, 
[2025-07-28 00:34:02 DP0 TP0] Decode batch. #running-req: 2, #token: 2351, token usage: 0.00, cuda graph: True, gen throughput (token/s): 578.00, #queue-req: 0, 
[2025-07-28 00:34:02 DP0 TP0] Decode batch. #running-req: 2, #token: 2431, token usage: 0.00, cuda graph: True, gen throughput (token/s): 588.14, #queue-req: 0, 
[2025-07-28 00:34:02] INFO:     127.0.0.1:47988 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:02 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 157, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:34:02 DP0 TP0] Decode batch. #running-req: 1, #token: 2044, token usage: 0.00, cuda graph: True, gen throughput (token/s): 403.82, #queue-req: 0, 
[2025-07-28 00:34:02 DP1 TP0] Decode batch. #running-req: 1, #token: 267, token usage: 0.00, cuda graph: True, gen throughput (token/s): 27.04, #queue-req: 0, 
[2025-07-28 00:34:02 DP0 TP0] Decode batch. #running-req: 1, #token: 2084, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.43, #queue-req: 0, 
[2025-07-28 00:34:02 DP1 TP0] Decode batch. #running-req: 1, #token: 307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.00, #queue-req: 0, 
[2025-07-28 00:34:03 DP0 TP0] Decode batch. #running-req: 1, #token: 2124, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.11, #queue-req: 0, 
[2025-07-28 00:34:03 DP1 TP0] Decode batch. #running-req: 1, #token: 347, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.82, #queue-req: 0, 
[2025-07-28 00:34:03 DP0 TP0] Decode batch. #running-req: 1, #token: 2164, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.66, #queue-req: 0, 
[2025-07-28 00:34:03 DP1 TP0] Decode batch. #running-req: 1, #token: 387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.93, #queue-req: 0, 
[2025-07-28 00:34:03 DP0 TP0] Decode batch. #running-req: 1, #token: 2204, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.23, #queue-req: 0, 
[2025-07-28 00:34:03 DP1 TP0] Decode batch. #running-req: 1, #token: 427, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.78, #queue-req: 0, 
[2025-07-28 00:34:03 DP0 TP0] Decode batch. #running-req: 1, #token: 2244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.34, #queue-req: 0, 
[2025-07-28 00:34:03 DP1 TP0] Decode batch. #running-req: 1, #token: 467, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.74, #queue-req: 0, 
[2025-07-28 00:34:03 DP0 TP0] Decode batch. #running-req: 1, #token: 2284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.16, #queue-req: 0, 
[2025-07-28 00:34:03 DP1 TP0] Decode batch. #running-req: 1, #token: 507, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.82, #queue-req: 0, 
[2025-07-28 00:34:03 DP0 TP0] Decode batch. #running-req: 1, #token: 2324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.62, #queue-req: 0, 
[2025-07-28 00:34:03 DP1 TP0] Decode batch. #running-req: 1, #token: 547, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.79, #queue-req: 0, 
[2025-07-28 00:34:03 DP0 TP0] Decode batch. #running-req: 1, #token: 2364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.65, #queue-req: 0, 
[2025-07-28 00:34:03 DP1 TP0] Decode batch. #running-req: 1, #token: 587, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.05, #queue-req: 0, 
[2025-07-28 00:34:03 DP0 TP0] Decode batch. #running-req: 1, #token: 2404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.04, #queue-req: 0, 
[2025-07-28 00:34:03 DP1 TP0] Decode batch. #running-req: 1, #token: 627, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.67, #queue-req: 0, 
[2025-07-28 00:34:04 DP0 TP0] Decode batch. #running-req: 1, #token: 2444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.87, #queue-req: 0, 
[2025-07-28 00:34:04 DP1 TP0] Decode batch. #running-req: 1, #token: 667, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.15, #queue-req: 0, 
[2025-07-28 00:34:04 DP0 TP0] Decode batch. #running-req: 1, #token: 2484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.84, #queue-req: 0, 
[2025-07-28 00:34:04 DP1 TP0] Decode batch. #running-req: 1, #token: 707, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.05, #queue-req: 0, 
[2025-07-28 00:34:04 DP0 TP0] Decode batch. #running-req: 1, #token: 2524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.38, #queue-req: 0, 
[2025-07-28 00:34:04 DP1 TP0] Decode batch. #running-req: 1, #token: 747, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.06, #queue-req: 0, 
[2025-07-28 00:34:04 DP0 TP0] Decode batch. #running-req: 1, #token: 2564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.88, #queue-req: 0, 
[2025-07-28 00:34:04 DP1 TP0] Decode batch. #running-req: 1, #token: 787, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.15, #queue-req: 0, 
[2025-07-28 00:34:04 DP0 TP0] Decode batch. #running-req: 1, #token: 2604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.96, #queue-req: 0, 
[2025-07-28 00:34:04 DP1 TP0] Decode batch. #running-req: 1, #token: 827, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.94, #queue-req: 0, 
[2025-07-28 00:34:04 DP0 TP0] Decode batch. #running-req: 1, #token: 2644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.91, #queue-req: 0, 
[2025-07-28 00:34:04 DP1 TP0] Decode batch. #running-req: 1, #token: 867, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.91, #queue-req: 0, 
[2025-07-28 00:34:04] INFO:     127.0.0.1:51868 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:04 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 150, #cached-token: 109, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:34:04 DP0 TP0] Decode batch. #running-req: 2, #token: 2861, token usage: 0.00, cuda graph: True, gen throughput (token/s): 354.07, #queue-req: 0, 
[2025-07-28 00:34:05 DP0 TP0] Decode batch. #running-req: 2, #token: 2941, token usage: 0.00, cuda graph: True, gen throughput (token/s): 632.59, #queue-req: 0, 
[2025-07-28 00:34:05 DP0 TP0] Decode batch. #running-req: 2, #token: 3021, token usage: 0.00, cuda graph: True, gen throughput (token/s): 640.88, #queue-req: 0, 
[2025-07-28 00:34:05 DP0 TP0] Decode batch. #running-req: 2, #token: 3101, token usage: 0.00, cuda graph: True, gen throughput (token/s): 629.47, #queue-req: 0, 
[2025-07-28 00:34:05 DP0 TP0] Decode batch. #running-req: 2, #token: 3181, token usage: 0.00, cuda graph: True, gen throughput (token/s): 633.38, #queue-req: 0, 
[2025-07-28 00:34:05 DP0 TP0] Decode batch. #running-req: 2, #token: 3261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 639.06, #queue-req: 0, 
[2025-07-28 00:34:05 DP0 TP0] Decode batch. #running-req: 2, #token: 3341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 633.37, #queue-req: 0, 
[2025-07-28 00:34:05 DP0 TP0] Decode batch. #running-req: 2, #token: 3421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 639.20, #queue-req: 0, 
[2025-07-28 00:34:05 DP0 TP0] Decode batch. #running-req: 2, #token: 3501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 634.67, #queue-req: 0, 
[2025-07-28 00:34:06 DP0 TP0] Decode batch. #running-req: 2, #token: 3581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 640.39, #queue-req: 0, 
[2025-07-28 00:34:06 DP0 TP0] Decode batch. #running-req: 2, #token: 3661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 639.15, #queue-req: 0, 
[2025-07-28 00:34:06] INFO:     127.0.0.1:51872 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:06 DP0 TP0] Decode batch. #running-req: 2, #token: 3124, token usage: 0.00, cuda graph: True, gen throughput (token/s): 629.39, #queue-req: 0, 
[2025-07-28 00:34:06 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 111, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:34:06 DP0 TP0] Decode batch. #running-req: 1, #token: 3164, token usage: 0.00, cuda graph: True, gen throughput (token/s): 333.47, #queue-req: 0, 
[2025-07-28 00:34:06 DP1 TP0] Decode batch. #running-req: 1, #token: 253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 22.95, #queue-req: 0, 
[2025-07-28 00:34:06 DP0 TP0] Decode batch. #running-req: 1, #token: 3204, token usage: 0.00, cuda graph: True, gen throughput (token/s): 332.08, #queue-req: 0, 
[2025-07-28 00:34:06 DP1 TP0] Decode batch. #running-req: 1, #token: 293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.27, #queue-req: 0, 
[2025-07-28 00:34:06 DP0 TP0] Decode batch. #running-req: 1, #token: 3244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 328.71, #queue-req: 0, 
[2025-07-28 00:34:06 DP1 TP0] Decode batch. #running-req: 1, #token: 333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.89, #queue-req: 0, 
[2025-07-28 00:34:06 DP0 TP0] Decode batch. #running-req: 1, #token: 3284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 329.25, #queue-req: 0, 
[2025-07-28 00:34:06 DP1 TP0] Decode batch. #running-req: 1, #token: 373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.76, #queue-req: 0, 
[2025-07-28 00:34:06 DP0 TP0] Decode batch. #running-req: 1, #token: 3324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 329.64, #queue-req: 0, 
[2025-07-28 00:34:06 DP1 TP0] Decode batch. #running-req: 1, #token: 413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.79, #queue-req: 0, 
[2025-07-28 00:34:06 DP0 TP0] Decode batch. #running-req: 1, #token: 3364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.03, #queue-req: 0, 
[2025-07-28 00:34:07 DP1 TP0] Decode batch. #running-req: 1, #token: 453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.71, #queue-req: 0, 
[2025-07-28 00:34:07 DP0 TP0] Decode batch. #running-req: 1, #token: 3404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.29, #queue-req: 0, 
[2025-07-28 00:34:07 DP1 TP0] Decode batch. #running-req: 1, #token: 493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.27, #queue-req: 0, 
[2025-07-28 00:34:07 DP0 TP0] Decode batch. #running-req: 1, #token: 3444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.10, #queue-req: 0, 
[2025-07-28 00:34:07 DP1 TP0] Decode batch. #running-req: 1, #token: 533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.28, #queue-req: 0, 
[2025-07-28 00:34:07 DP0 TP0] Decode batch. #running-req: 1, #token: 3484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.24, #queue-req: 0, 
[2025-07-28 00:34:07 DP1 TP0] Decode batch. #running-req: 1, #token: 573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.32, #queue-req: 0, 
[2025-07-28 00:34:07 DP0 TP0] Decode batch. #running-req: 1, #token: 3524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.14, #queue-req: 0, 
[2025-07-28 00:34:07 DP1 TP0] Decode batch. #running-req: 1, #token: 613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.54, #queue-req: 0, 
[2025-07-28 00:34:07 DP0 TP0] Decode batch. #running-req: 1, #token: 3564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.81, #queue-req: 0, 
[2025-07-28 00:34:07] INFO:     127.0.0.1:51880 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:07 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 95, #cached-token: 106, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:34:07 DP0 TP0] Decode batch. #running-req: 2, #token: 3721, token usage: 0.00, cuda graph: True, gen throughput (token/s): 342.66, #queue-req: 0, 
[2025-07-28 00:34:07 DP0 TP0] Decode batch. #running-req: 2, #token: 3801, token usage: 0.00, cuda graph: True, gen throughput (token/s): 637.92, #queue-req: 0, 
[2025-07-28 00:34:08 DP0 TP0] Decode batch. #running-req: 2, #token: 3881, token usage: 0.00, cuda graph: True, gen throughput (token/s): 629.29, #queue-req: 0, 
[2025-07-28 00:34:08 DP0 TP0] Decode batch. #running-req: 2, #token: 3961, token usage: 0.00, cuda graph: True, gen throughput (token/s): 630.67, #queue-req: 0, 
[2025-07-28 00:34:08 DP0 TP0] Decode batch. #running-req: 2, #token: 4041, token usage: 0.00, cuda graph: True, gen throughput (token/s): 578.40, #queue-req: 0, 
[2025-07-28 00:34:08 DP0 TP0] Decode batch. #running-req: 2, #token: 4121, token usage: 0.00, cuda graph: True, gen throughput (token/s): 595.85, #queue-req: 0, 
[2025-07-28 00:34:08] INFO:     127.0.0.1:51888 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:08 DP0 TP0] Decode batch. #running-req: 1, #token: 3844, token usage: 0.00, cuda graph: True, gen throughput (token/s): 568.23, #queue-req: 0, 
[2025-07-28 00:34:08 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 89, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:34:08 DP0 TP0] Decode batch. #running-req: 1, #token: 3884, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.05, #queue-req: 0, 
[2025-07-28 00:34:08 DP1 TP0] Decode batch. #running-req: 1, #token: 218, token usage: 0.00, cuda graph: True, gen throughput (token/s): 35.44, #queue-req: 0, 
[2025-07-28 00:34:08 DP0 TP0] Decode batch. #running-req: 1, #token: 3924, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.67, #queue-req: 0, 
[2025-07-28 00:34:08 DP1 TP0] Decode batch. #running-req: 1, #token: 258, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.64, #queue-req: 0, 
[2025-07-28 00:34:08 DP0 TP0] Decode batch. #running-req: 1, #token: 3964, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.79, #queue-req: 0, 
[2025-07-28 00:34:09 DP1 TP0] Decode batch. #running-req: 1, #token: 298, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.47, #queue-req: 0, 
[2025-07-28 00:34:09 DP0 TP0] Decode batch. #running-req: 1, #token: 4004, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.39, #queue-req: 0, 
[2025-07-28 00:34:09 DP1 TP0] Decode batch. #running-req: 1, #token: 338, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.50, #queue-req: 0, 
[2025-07-28 00:34:09 DP0 TP0] Decode batch. #running-req: 1, #token: 4044, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.40, #queue-req: 0, 
[2025-07-28 00:34:09 DP1 TP0] Decode batch. #running-req: 1, #token: 378, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.13, #queue-req: 0, 
[2025-07-28 00:34:09 DP0 TP0] Decode batch. #running-req: 1, #token: 4084, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.18, #queue-req: 0, 
[2025-07-28 00:34:09 DP1 TP0] Decode batch. #running-req: 1, #token: 418, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.63, #queue-req: 0, 
[2025-07-28 00:34:09 DP0 TP0] Decode batch. #running-req: 1, #token: 4124, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.76, #queue-req: 0, 
[2025-07-28 00:34:09 DP1 TP0] Decode batch. #running-req: 1, #token: 458, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.71, #queue-req: 0, 
[2025-07-28 00:34:09 DP0 TP0] Decode batch. #running-req: 1, #token: 4164, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.59, #queue-req: 0, 
[2025-07-28 00:34:09 DP1 TP0] Decode batch. #running-req: 1, #token: 498, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.36, #queue-req: 0, 
[2025-07-28 00:34:09 DP0 TP0] Decode batch. #running-req: 1, #token: 4204, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.15, #queue-req: 0, 
[2025-07-28 00:34:09 DP1 TP0] Decode batch. #running-req: 1, #token: 538, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.74, #queue-req: 0, 
[2025-07-28 00:34:09 DP0 TP0] Decode batch. #running-req: 1, #token: 4244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.30, #queue-req: 0, 
[2025-07-28 00:34:09 DP1 TP0] Decode batch. #running-req: 1, #token: 578, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.89, #queue-req: 0, 
[2025-07-28 00:34:10 DP0 TP0] Decode batch. #running-req: 1, #token: 4284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.23, #queue-req: 0, 
[2025-07-28 00:34:10 DP1 TP0] Decode batch. #running-req: 1, #token: 618, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.86, #queue-req: 0, 
[2025-07-28 00:34:10 DP0 TP0] Decode batch. #running-req: 1, #token: 4324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.90, #queue-req: 0, 
[2025-07-28 00:34:10 DP1 TP0] Decode batch. #running-req: 1, #token: 658, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.21, #queue-req: 0, 
[2025-07-28 00:34:10 DP0 TP0] Decode batch. #running-req: 1, #token: 4364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.14, #queue-req: 0, 
[2025-07-28 00:34:10 DP1 TP0] Decode batch. #running-req: 1, #token: 698, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.03, #queue-req: 0, 
[2025-07-28 00:34:10 DP0 TP0] Decode batch. #running-req: 1, #token: 4404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.78, #queue-req: 0, 
[2025-07-28 00:34:10 DP1 TP0] Decode batch. #running-req: 1, #token: 738, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.67, #queue-req: 0, 
[2025-07-28 00:34:10 DP0 TP0] Decode batch. #running-req: 1, #token: 4444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.61, #queue-req: 0, 
[2025-07-28 00:34:10 DP1 TP0] Decode batch. #running-req: 1, #token: 778, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.97, #queue-req: 0, 
[2025-07-28 00:34:10 DP0 TP0] Decode batch. #running-req: 1, #token: 4484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.10, #queue-req: 0, 
[2025-07-28 00:34:10 DP1 TP0] Decode batch. #running-req: 1, #token: 818, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.97, #queue-req: 0, 
[2025-07-28 00:34:10 DP0 TP0] Decode batch. #running-req: 1, #token: 4524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.71, #queue-req: 0, 
[2025-07-28 00:34:10 DP1 TP0] Decode batch. #running-req: 1, #token: 858, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.35, #queue-req: 0, 
[2025-07-28 00:34:10 DP0 TP0] Decode batch. #running-req: 1, #token: 4564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.57, #queue-req: 0, 
[2025-07-28 00:34:10 DP1 TP0] Decode batch. #running-req: 1, #token: 898, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.16, #queue-req: 0, 
[2025-07-28 00:34:11] INFO:     127.0.0.1:51890 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:11 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 115, #cached-token: 106, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:34:11 DP0 TP0] Decode batch. #running-req: 2, #token: 4725, token usage: 0.00, cuda graph: True, gen throughput (token/s): 242.14, #queue-req: 0, 
[2025-07-28 00:34:11 DP0 TP0] Decode batch. #running-req: 2, #token: 4805, token usage: 0.00, cuda graph: True, gen throughput (token/s): 629.72, #queue-req: 0, 
[2025-07-28 00:34:11 DP0 TP0] Decode batch. #running-req: 2, #token: 4885, token usage: 0.00, cuda graph: True, gen throughput (token/s): 624.41, #queue-req: 0, 
[2025-07-28 00:34:11 DP0 TP0] Decode batch. #running-req: 2, #token: 4965, token usage: 0.00, cuda graph: True, gen throughput (token/s): 609.48, #queue-req: 0, 
[2025-07-28 00:34:11 DP0 TP0] Decode batch. #running-req: 2, #token: 5045, token usage: 0.00, cuda graph: True, gen throughput (token/s): 587.98, #queue-req: 0, 
[2025-07-28 00:34:11 DP0 TP0] Decode batch. #running-req: 2, #token: 5125, token usage: 0.00, cuda graph: True, gen throughput (token/s): 584.95, #queue-req: 0, 
[2025-07-28 00:34:11 DP0 TP0] Decode batch. #running-req: 2, #token: 5205, token usage: 0.00, cuda graph: True, gen throughput (token/s): 584.73, #queue-req: 0, 
[2025-07-28 00:34:12 DP0 TP0] Decode batch. #running-req: 2, #token: 5285, token usage: 0.00, cuda graph: True, gen throughput (token/s): 588.10, #queue-req: 0, 
[2025-07-28 00:34:12 DP0 TP0] Decode batch. #running-req: 2, #token: 5365, token usage: 0.00, cuda graph: True, gen throughput (token/s): 588.11, #queue-req: 0, 
[2025-07-28 00:34:12 DP0 TP0] Decode batch. #running-req: 2, #token: 5445, token usage: 0.00, cuda graph: True, gen throughput (token/s): 589.52, #queue-req: 0, 
[2025-07-28 00:34:12 DP0 TP0] Decode batch. #running-req: 2, #token: 5525, token usage: 0.00, cuda graph: True, gen throughput (token/s): 589.16, #queue-req: 0, 
[2025-07-28 00:34:12 DP0 TP0] Decode batch. #running-req: 2, #token: 5605, token usage: 0.00, cuda graph: True, gen throughput (token/s): 588.01, #queue-req: 0, 
[2025-07-28 00:34:12 DP0 TP0] Decode batch. #running-req: 2, #token: 5685, token usage: 0.00, cuda graph: True, gen throughput (token/s): 589.73, #queue-req: 0, 
[2025-07-28 00:34:12] INFO:     127.0.0.1:51894 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:12 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 164, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:34:12 DP0 TP0] Decode batch. #running-req: 1, #token: 5124, token usage: 0.00, cuda graph: True, gen throughput (token/s): 425.14, #queue-req: 0, 
[2025-07-28 00:34:12 DP1 TP0] Decode batch. #running-req: 1, #token: 292, token usage: 0.00, cuda graph: True, gen throughput (token/s): 20.08, #queue-req: 0, 
[2025-07-28 00:34:12 DP0 TP0] Decode batch. #running-req: 1, #token: 5164, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.71, #queue-req: 0, 
[2025-07-28 00:34:13 DP1 TP0] Decode batch. #running-req: 1, #token: 332, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.25, #queue-req: 0, 
[2025-07-28 00:34:13 DP0 TP0] Decode batch. #running-req: 1, #token: 5204, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.05, #queue-req: 0, 
[2025-07-28 00:34:13 DP1 TP0] Decode batch. #running-req: 1, #token: 372, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.44, #queue-req: 0, 
[2025-07-28 00:34:13 DP0 TP0] Decode batch. #running-req: 1, #token: 5244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.71, #queue-req: 0, 
[2025-07-28 00:34:13 DP1 TP0] Decode batch. #running-req: 1, #token: 412, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.32, #queue-req: 0, 
[2025-07-28 00:34:13 DP0 TP0] Decode batch. #running-req: 1, #token: 5284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.01, #queue-req: 0, 
[2025-07-28 00:34:13 DP1 TP0] Decode batch. #running-req: 1, #token: 452, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.20, #queue-req: 0, 
[2025-07-28 00:34:13 DP0 TP0] Decode batch. #running-req: 1, #token: 5324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.77, #queue-req: 0, 
[2025-07-28 00:34:13 DP1 TP0] Decode batch. #running-req: 1, #token: 492, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.05, #queue-req: 0, 
[2025-07-28 00:34:13 DP0 TP0] Decode batch. #running-req: 1, #token: 5364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.32, #queue-req: 0, 
[2025-07-28 00:34:13 DP1 TP0] Decode batch. #running-req: 1, #token: 532, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.77, #queue-req: 0, 
[2025-07-28 00:34:13 DP0 TP0] Decode batch. #running-req: 1, #token: 5404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.08, #queue-req: 0, 
[2025-07-28 00:34:13 DP1 TP0] Decode batch. #running-req: 1, #token: 572, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.93, #queue-req: 0, 
[2025-07-28 00:34:13 DP0 TP0] Decode batch. #running-req: 1, #token: 5444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.72, #queue-req: 0, 
[2025-07-28 00:34:13 DP1 TP0] Decode batch. #running-req: 1, #token: 612, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.97, #queue-req: 0, 
[2025-07-28 00:34:14 DP0 TP0] Decode batch. #running-req: 1, #token: 5484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.22, #queue-req: 0, 
[2025-07-28 00:34:14 DP1 TP0] Decode batch. #running-req: 1, #token: 652, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.74, #queue-req: 0, 
[2025-07-28 00:34:14 DP0 TP0] Decode batch. #running-req: 1, #token: 5524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.07, #queue-req: 0, 
[2025-07-28 00:34:14 DP1 TP0] Decode batch. #running-req: 1, #token: 692, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.32, #queue-req: 0, 
[2025-07-28 00:34:14 DP0 TP0] Decode batch. #running-req: 1, #token: 5564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.96, #queue-req: 0, 
[2025-07-28 00:34:14 DP1 TP0] Decode batch. #running-req: 1, #token: 732, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.35, #queue-req: 0, 
[2025-07-28 00:34:14 DP0 TP0] Decode batch. #running-req: 1, #token: 5604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.80, #queue-req: 0, 
[2025-07-28 00:34:14 DP1 TP0] Decode batch. #running-req: 1, #token: 772, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.23, #queue-req: 0, 
[2025-07-28 00:34:14 DP0 TP0] Decode batch. #running-req: 1, #token: 5644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.38, #queue-req: 0, 
[2025-07-28 00:34:14 DP1 TP0] Decode batch. #running-req: 1, #token: 812, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.70, #queue-req: 0, 
[2025-07-28 00:34:14 DP0 TP0] Decode batch. #running-req: 1, #token: 5684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.45, #queue-req: 0, 
[2025-07-28 00:34:14 DP1 TP0] Decode batch. #running-req: 1, #token: 852, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.15, #queue-req: 0, 
[2025-07-28 00:34:14 DP0 TP0] Decode batch. #running-req: 1, #token: 5724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.45, #queue-req: 0, 
[2025-07-28 00:34:14] INFO:     127.0.0.1:45538 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:14 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 143, #cached-token: 106, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:34:14 DP0 TP0] Decode batch. #running-req: 2, #token: 5943, token usage: 0.00, cuda graph: True, gen throughput (token/s): 390.32, #queue-req: 0, 
[2025-07-28 00:34:15 DP0 TP0] Decode batch. #running-req: 2, #token: 6023, token usage: 0.00, cuda graph: True, gen throughput (token/s): 578.30, #queue-req: 0, 
[2025-07-28 00:34:15 DP0 TP0] Decode batch. #running-req: 2, #token: 6103, token usage: 0.00, cuda graph: True, gen throughput (token/s): 579.50, #queue-req: 0, 
[2025-07-28 00:34:15 DP0 TP0] Decode batch. #running-req: 2, #token: 6183, token usage: 0.00, cuda graph: True, gen throughput (token/s): 571.50, #queue-req: 0, 
[2025-07-28 00:34:15 DP0 TP0] Decode batch. #running-req: 2, #token: 6263, token usage: 0.00, cuda graph: True, gen throughput (token/s): 583.12, #queue-req: 0, 
[2025-07-28 00:34:15 DP0 TP0] Decode batch. #running-req: 2, #token: 6343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 579.32, #queue-req: 0, 
[2025-07-28 00:34:15 DP0 TP0] Decode batch. #running-req: 2, #token: 6423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 576.45, #queue-req: 0, 
[2025-07-28 00:34:15 DP0 TP0] Decode batch. #running-req: 2, #token: 6503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 579.15, #queue-req: 0, 
[2025-07-28 00:34:16 DP0 TP0] Decode batch. #running-req: 2, #token: 6583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 584.25, #queue-req: 0, 
[2025-07-28 00:34:16 DP0 TP0] Decode batch. #running-req: 2, #token: 6663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 581.59, #queue-req: 0, 
[2025-07-28 00:34:16 DP0 TP0] Decode batch. #running-req: 2, #token: 6743, token usage: 0.00, cuda graph: True, gen throughput (token/s): 590.94, #queue-req: 0, 
[2025-07-28 00:34:16 DP0 TP0] Decode batch. #running-req: 2, #token: 6823, token usage: 0.00, cuda graph: True, gen throughput (token/s): 591.29, #queue-req: 0, 
[2025-07-28 00:34:16 DP0 TP0] Decode batch. #running-req: 2, #token: 6903, token usage: 0.00, cuda graph: True, gen throughput (token/s): 582.72, #queue-req: 0, 
[2025-07-28 00:34:16 DP0 TP0] Decode batch. #running-req: 2, #token: 6983, token usage: 0.00, cuda graph: True, gen throughput (token/s): 594.67, #queue-req: 0, 
[2025-07-28 00:34:16 DP0 TP0] Decode batch. #running-req: 2, #token: 7063, token usage: 0.00, cuda graph: True, gen throughput (token/s): 595.41, #queue-req: 0, 
[2025-07-28 00:34:17 DP0 TP0] Decode batch. #running-req: 2, #token: 7143, token usage: 0.00, cuda graph: True, gen throughput (token/s): 589.84, #queue-req: 0, 
[2025-07-28 00:34:17] INFO:     127.0.0.1:45552 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:17 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 117, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:34:17 DP0 TP0] Decode batch. #running-req: 1, #token: 6404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 360.04, #queue-req: 0, 
[2025-07-28 00:34:17 DP1 TP0] Decode batch. #running-req: 1, #token: 247, token usage: 0.00, cuda graph: True, gen throughput (token/s): 16.06, #queue-req: 0, 
[2025-07-28 00:34:17 DP0 TP0] Decode batch. #running-req: 1, #token: 6444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.73, #queue-req: 0, 
[2025-07-28 00:34:17 DP1 TP0] Decode batch. #running-req: 1, #token: 287, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.25, #queue-req: 0, 
[2025-07-28 00:34:17 DP0 TP0] Decode batch. #running-req: 1, #token: 6484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.29, #queue-req: 0, 
[2025-07-28 00:34:17 DP1 TP0] Decode batch. #running-req: 1, #token: 327, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.35, #queue-req: 0, 
[2025-07-28 00:34:17 DP0 TP0] Decode batch. #running-req: 1, #token: 6524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.42, #queue-req: 0, 
[2025-07-28 00:34:17 DP1 TP0] Decode batch. #running-req: 1, #token: 367, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.32, #queue-req: 0, 
[2025-07-28 00:34:17 DP0 TP0] Decode batch. #running-req: 1, #token: 6564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.29, #queue-req: 0, 
[2025-07-28 00:34:17 DP1 TP0] Decode batch. #running-req: 1, #token: 407, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.65, #queue-req: 0, 
[2025-07-28 00:34:17 DP0 TP0] Decode batch. #running-req: 1, #token: 6604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.27, #queue-req: 0, 
[2025-07-28 00:34:17 DP1 TP0] Decode batch. #running-req: 1, #token: 447, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.47, #queue-req: 0, 
[2025-07-28 00:34:17 DP0 TP0] Decode batch. #running-req: 1, #token: 6644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.95, #queue-req: 0, 
[2025-07-28 00:34:18 DP1 TP0] Decode batch. #running-req: 1, #token: 487, token usage: 0.00, cuda graph: True, gen throughput (token/s): 323.94, #queue-req: 0, 
[2025-07-28 00:34:18 DP0 TP0] Decode batch. #running-req: 1, #token: 6684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.00, #queue-req: 0, 
[2025-07-28 00:34:18 DP1 TP0] Decode batch. #running-req: 1, #token: 527, token usage: 0.00, cuda graph: True, gen throughput (token/s): 324.24, #queue-req: 0, 
[2025-07-28 00:34:18 DP0 TP0] Decode batch. #running-req: 1, #token: 6724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.53, #queue-req: 0, 
[2025-07-28 00:34:18 DP1 TP0] Decode batch. #running-req: 1, #token: 567, token usage: 0.00, cuda graph: True, gen throughput (token/s): 323.47, #queue-req: 0, 
[2025-07-28 00:34:18 DP0 TP0] Decode batch. #running-req: 1, #token: 6764, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.59, #queue-req: 0, 
[2025-07-28 00:34:18 DP1 TP0] Decode batch. #running-req: 1, #token: 607, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.78, #queue-req: 0, 
[2025-07-28 00:34:18 DP0 TP0] Decode batch. #running-req: 1, #token: 6804, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.50, #queue-req: 0, 
[2025-07-28 00:34:18 DP1 TP0] Decode batch. #running-req: 1, #token: 647, token usage: 0.00, cuda graph: True, gen throughput (token/s): 322.69, #queue-req: 0, 
[2025-07-28 00:34:18 DP0 TP0] Decode batch. #running-req: 1, #token: 6844, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.26, #queue-req: 0, 
[2025-07-28 00:34:18 DP1 TP0] Decode batch. #running-req: 1, #token: 687, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.30, #queue-req: 0, 
[2025-07-28 00:34:18] INFO:     127.0.0.1:45558 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:18 DP0 TP0] Decode batch. #running-req: 1, #token: 6884, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.45, #queue-req: 0, 
[2025-07-28 00:34:18 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 125, #cached-token: 108, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:34:18 DP0 TP0] Decode batch. #running-req: 2, #token: 7086, token usage: 0.00, cuda graph: True, gen throughput (token/s): 419.76, #queue-req: 0, 
[2025-07-28 00:34:19 DP0 TP0] Decode batch. #running-req: 2, #token: 7166, token usage: 0.00, cuda graph: True, gen throughput (token/s): 617.23, #queue-req: 0, 
[2025-07-28 00:34:19 DP0 TP0] Decode batch. #running-req: 2, #token: 7246, token usage: 0.00, cuda graph: True, gen throughput (token/s): 600.36, #queue-req: 0, 
[2025-07-28 00:34:19 DP0 TP0] Decode batch. #running-req: 2, #token: 7326, token usage: 0.00, cuda graph: True, gen throughput (token/s): 582.13, #queue-req: 0, 
[2025-07-28 00:34:19 DP0 TP0] Decode batch. #running-req: 2, #token: 7406, token usage: 0.00, cuda graph: True, gen throughput (token/s): 603.09, #queue-req: 0, 
[2025-07-28 00:34:19 DP0 TP0] Decode batch. #running-req: 2, #token: 7486, token usage: 0.00, cuda graph: True, gen throughput (token/s): 582.65, #queue-req: 0, 
[2025-07-28 00:34:19 DP0 TP0] Decode batch. #running-req: 2, #token: 7566, token usage: 0.00, cuda graph: True, gen throughput (token/s): 584.23, #queue-req: 0, 
[2025-07-28 00:34:19 DP0 TP0] Decode batch. #running-req: 2, #token: 7646, token usage: 0.00, cuda graph: True, gen throughput (token/s): 570.50, #queue-req: 0, 
[2025-07-28 00:34:19] INFO:     127.0.0.1:45572 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:19 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 196, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:34:19 DP0 TP0] Decode batch. #running-req: 1, #token: 7244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 352.41, #queue-req: 0, 
[2025-07-28 00:34:19 DP1 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 29.45, #queue-req: 0, 
[2025-07-28 00:34:20 DP0 TP0] Decode batch. #running-req: 1, #token: 7284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.63, #queue-req: 0, 
[2025-07-28 00:34:20 DP1 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.72, #queue-req: 0, 
[2025-07-28 00:34:20 DP0 TP0] Decode batch. #running-req: 1, #token: 7324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.82, #queue-req: 0, 
[2025-07-28 00:34:20 DP1 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.66, #queue-req: 0, 
[2025-07-28 00:34:20 DP0 TP0] Decode batch. #running-req: 1, #token: 7364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.99, #queue-req: 0, 
[2025-07-28 00:34:20 DP1 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.78, #queue-req: 0, 
[2025-07-28 00:34:20 DP0 TP0] Decode batch. #running-req: 1, #token: 7404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.58, #queue-req: 0, 
[2025-07-28 00:34:20 DP1 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.52, #queue-req: 0, 
[2025-07-28 00:34:20 DP0 TP0] Decode batch. #running-req: 1, #token: 7444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.27, #queue-req: 0, 
[2025-07-28 00:34:20 DP1 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.01, #queue-req: 0, 
[2025-07-28 00:34:20 DP0 TP0] Decode batch. #running-req: 1, #token: 7484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.08, #queue-req: 0, 
[2025-07-28 00:34:20 DP1 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.25, #queue-req: 0, 
[2025-07-28 00:34:20 DP0 TP0] Decode batch. #running-req: 1, #token: 7524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.67, #queue-req: 0, 
[2025-07-28 00:34:20 DP1 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.74, #queue-req: 0, 
[2025-07-28 00:34:21 DP0 TP0] Decode batch. #running-req: 1, #token: 7564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.43, #queue-req: 0, 
[2025-07-28 00:34:21 DP1 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.52, #queue-req: 0, 
[2025-07-28 00:34:21 DP0 TP0] Decode batch. #running-req: 1, #token: 7604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.78, #queue-req: 0, 
[2025-07-28 00:34:21 DP1 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.26, #queue-req: 0, 
[2025-07-28 00:34:21 DP0 TP0] Decode batch. #running-req: 1, #token: 7644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.35, #queue-req: 0, 
[2025-07-28 00:34:21 DP1 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.10, #queue-req: 0, 
[2025-07-28 00:34:21 DP0 TP0] Decode batch. #running-req: 1, #token: 7684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.97, #queue-req: 0, 
[2025-07-28 00:34:21 DP1 TP0] Decode batch. #running-req: 1, #token: 755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.61, #queue-req: 0, 
[2025-07-28 00:34:21] INFO:     127.0.0.1:45580 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:21 DP0 TP0] Decode batch. #running-req: 1, #token: 7724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.81, #queue-req: 0, 
[2025-07-28 00:34:21 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 106, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:34:21 DP0 TP0] Decode batch. #running-req: 2, #token: 7887, token usage: 0.00, cuda graph: True, gen throughput (token/s): 453.43, #queue-req: 0, 
[2025-07-28 00:34:21 DP0 TP0] Decode batch. #running-req: 2, #token: 7967, token usage: 0.00, cuda graph: True, gen throughput (token/s): 659.58, #queue-req: 0, 
[2025-07-28 00:34:21 DP0 TP0] Decode batch. #running-req: 2, #token: 8047, token usage: 0.00, cuda graph: True, gen throughput (token/s): 641.43, #queue-req: 0, 
[2025-07-28 00:34:22 DP0 TP0] Decode batch. #running-req: 2, #token: 8127, token usage: 0.00, cuda graph: True, gen throughput (token/s): 600.37, #queue-req: 0, 
[2025-07-28 00:34:22 DP0 TP0] Decode batch. #running-req: 2, #token: 8207, token usage: 0.00, cuda graph: True, gen throughput (token/s): 598.74, #queue-req: 0, 
[2025-07-28 00:34:22 DP0 TP0] Decode batch. #running-req: 2, #token: 8287, token usage: 0.00, cuda graph: True, gen throughput (token/s): 574.22, #queue-req: 0, 
[2025-07-28 00:34:22 DP0 TP0] Decode batch. #running-req: 2, #token: 8367, token usage: 0.00, cuda graph: True, gen throughput (token/s): 590.54, #queue-req: 0, 
[2025-07-28 00:34:22 DP0 TP0] Decode batch. #running-req: 2, #token: 8447, token usage: 0.00, cuda graph: True, gen throughput (token/s): 589.13, #queue-req: 0, 
[2025-07-28 00:34:22 DP0 TP0] Decode batch. #running-req: 2, #token: 8527, token usage: 0.00, cuda graph: True, gen throughput (token/s): 584.21, #queue-req: 0, 
[2025-07-28 00:34:22 DP0 TP0] Decode batch. #running-req: 2, #token: 8607, token usage: 0.00, cuda graph: True, gen throughput (token/s): 601.48, #queue-req: 0, 
[2025-07-28 00:34:23 DP0 TP0] Decode batch. #running-req: 2, #token: 8687, token usage: 0.00, cuda graph: True, gen throughput (token/s): 598.18, #queue-req: 0, 
[2025-07-28 00:34:23 DP0 TP0] Decode batch. #running-req: 2, #token: 8767, token usage: 0.00, cuda graph: True, gen throughput (token/s): 595.32, #queue-req: 0, 
[2025-07-28 00:34:23 DP0 TP0] Decode batch. #running-req: 2, #token: 8847, token usage: 0.00, cuda graph: True, gen throughput (token/s): 593.00, #queue-req: 0, 
[2025-07-28 00:34:23] INFO:     127.0.0.1:45582 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:23 DP0 TP0] Decode batch. #running-req: 1, #token: 8284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 581.55, #queue-req: 0, 
[2025-07-28 00:34:23 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 154, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:34:23 DP1 TP0] Decode batch. #running-req: 1, #token: 270, token usage: 0.00, cuda graph: True, gen throughput (token/s): 18.83, #queue-req: 0, 
[2025-07-28 00:34:23 DP0 TP0] Decode batch. #running-req: 1, #token: 8324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.60, #queue-req: 0, 
[2025-07-28 00:34:23 DP1 TP0] Decode batch. #running-req: 1, #token: 310, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.90, #queue-req: 0, 
[2025-07-28 00:34:23 DP0 TP0] Decode batch. #running-req: 1, #token: 8364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.96, #queue-req: 0, 
[2025-07-28 00:34:23 DP1 TP0] Decode batch. #running-req: 1, #token: 350, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.86, #queue-req: 0, 
[2025-07-28 00:34:23 DP0 TP0] Decode batch. #running-req: 1, #token: 8404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.42, #queue-req: 0, 
[2025-07-28 00:34:23] INFO:     127.0.0.1:47970 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:23 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 128, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:34:23 DP1 TP0] Decode batch. #running-req: 1, #token: 390, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.56, #queue-req: 0, 
[2025-07-28 00:34:24 DP0 TP0] Decode batch. #running-req: 1, #token: 265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 207.97, #queue-req: 0, 
[2025-07-28 00:34:24 DP1 TP0] Decode batch. #running-req: 1, #token: 430, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.94, #queue-req: 0, 
[2025-07-28 00:34:24 DP0 TP0] Decode batch. #running-req: 1, #token: 305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 332.70, #queue-req: 0, 
[2025-07-28 00:34:24 DP1 TP0] Decode batch. #running-req: 1, #token: 470, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.07, #queue-req: 0, 
[2025-07-28 00:34:24 DP0 TP0] Decode batch. #running-req: 1, #token: 345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 322.91, #queue-req: 0, 
[2025-07-28 00:34:24 DP1 TP0] Decode batch. #running-req: 1, #token: 510, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.48, #queue-req: 0, 
[2025-07-28 00:34:24 DP1 TP0] Decode batch. #running-req: 1, #token: 550, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.14, #queue-req: 0, 
[2025-07-28 00:34:24 DP0 TP0] Decode batch. #running-req: 1, #token: 385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 183.91, #queue-req: 0, 
[2025-07-28 00:34:24 DP1 TP0] Decode batch. #running-req: 1, #token: 590, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.33, #queue-req: 0, 
[2025-07-28 00:34:24 DP0 TP0] Decode batch. #running-req: 1, #token: 425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 169.18, #queue-req: 0, 
[2025-07-28 00:34:24 DP1 TP0] Decode batch. #running-req: 1, #token: 630, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.94, #queue-req: 0, 
[2025-07-28 00:34:24 DP1 TP0] Decode batch. #running-req: 1, #token: 670, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.90, #queue-req: 0, 
[2025-07-28 00:34:24] INFO:     127.0.0.1:36416 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:24 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:34:24 DP0 TP0] Decode batch. #running-req: 1, #token: 465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 152.07, #queue-req: 0, 
[2025-07-28 00:34:25 DP1 TP0] Decode batch. #running-req: 1, #token: 288, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.86, #queue-req: 0, 
[2025-07-28 00:34:25 DP0 TP0] Decode batch. #running-req: 1, #token: 505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 274.80, #queue-req: 0, 
[2025-07-28 00:34:25 DP1 TP0] Decode batch. #running-req: 1, #token: 328, token usage: 0.00, cuda graph: True, gen throughput (token/s): 320.62, #queue-req: 0, 
[2025-07-28 00:34:25 DP0 TP0] Decode batch. #running-req: 1, #token: 545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.87, #queue-req: 0, 
[2025-07-28 00:34:25 DP1 TP0] Decode batch. #running-req: 1, #token: 368, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.38, #queue-req: 0, 
[2025-07-28 00:34:25 DP0 TP0] Decode batch. #running-req: 1, #token: 585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.56, #queue-req: 0, 
[2025-07-28 00:34:25 DP1 TP0] Decode batch. #running-req: 1, #token: 408, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.92, #queue-req: 0, 
[2025-07-28 00:34:25 DP0 TP0] Decode batch. #running-req: 1, #token: 625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.59, #queue-req: 0, 
[2025-07-28 00:34:25 DP1 TP0] Decode batch. #running-req: 1, #token: 448, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.25, #queue-req: 0, 
[2025-07-28 00:34:25 DP0 TP0] Decode batch. #running-req: 1, #token: 665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.15, #queue-req: 0, 
[2025-07-28 00:34:25] INFO:     127.0.0.1:36432 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:25 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 109, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:34:25 DP1 TP0] Decode batch. #running-req: 1, #token: 488, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.32, #queue-req: 0, 
[2025-07-28 00:34:25 DP0 TP0] Decode batch. #running-req: 1, #token: 278, token usage: 0.00, cuda graph: True, gen throughput (token/s): 211.04, #queue-req: 0, 
[2025-07-28 00:34:25 DP1 TP0] Decode batch. #running-req: 1, #token: 528, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.39, #queue-req: 0, 
[2025-07-28 00:34:25 DP0 TP0] Decode batch. #running-req: 1, #token: 318, token usage: 0.00, cuda graph: True, gen throughput (token/s): 328.49, #queue-req: 0, 
[2025-07-28 00:34:25 DP1 TP0] Decode batch. #running-req: 1, #token: 568, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.17, #queue-req: 0, 
[2025-07-28 00:34:26 DP0 TP0] Decode batch. #running-req: 1, #token: 358, token usage: 0.00, cuda graph: True, gen throughput (token/s): 331.16, #queue-req: 0, 
[2025-07-28 00:34:26 DP1 TP0] Decode batch. #running-req: 1, #token: 608, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.98, #queue-req: 0, 
[2025-07-28 00:34:26 DP0 TP0] Decode batch. #running-req: 1, #token: 398, token usage: 0.00, cuda graph: True, gen throughput (token/s): 334.72, #queue-req: 0, 
[2025-07-28 00:34:26 DP1 TP0] Decode batch. #running-req: 1, #token: 648, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.21, #queue-req: 0, 
[2025-07-28 00:34:26 DP0 TP0] Decode batch. #running-req: 1, #token: 438, token usage: 0.00, cuda graph: True, gen throughput (token/s): 324.40, #queue-req: 0, 
[2025-07-28 00:34:26 DP1 TP0] Decode batch. #running-req: 1, #token: 688, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.18, #queue-req: 0, 
[2025-07-28 00:34:26 DP0 TP0] Decode batch. #running-req: 1, #token: 478, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.08, #queue-req: 0, 
[2025-07-28 00:34:26 DP1 TP0] Decode batch. #running-req: 1, #token: 728, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.74, #queue-req: 0, 
[2025-07-28 00:34:26 DP0 TP0] Decode batch. #running-req: 1, #token: 518, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.01, #queue-req: 0, 
[2025-07-28 00:34:26 DP1 TP0] Decode batch. #running-req: 1, #token: 768, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.49, #queue-req: 0, 
[2025-07-28 00:34:26 DP0 TP0] Decode batch. #running-req: 1, #token: 558, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.06, #queue-req: 0, 
[2025-07-28 00:34:26 DP1 TP0] Decode batch. #running-req: 1, #token: 808, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.69, #queue-req: 0, 
[2025-07-28 00:34:26 DP0 TP0] Decode batch. #running-req: 1, #token: 598, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.86, #queue-req: 0, 
[2025-07-28 00:34:26] INFO:     127.0.0.1:36444 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:26 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 95, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:34:26 DP0 TP0] Decode batch. #running-req: 1, #token: 638, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.12, #queue-req: 0, 
[2025-07-28 00:34:26 DP1 TP0] Decode batch. #running-req: 1, #token: 206, token usage: 0.00, cuda graph: True, gen throughput (token/s): 205.94, #queue-req: 0, 
[2025-07-28 00:34:27 DP0 TP0] Decode batch. #running-req: 1, #token: 678, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.61, #queue-req: 0, 
[2025-07-28 00:34:27 DP1 TP0] Decode batch. #running-req: 1, #token: 246, token usage: 0.00, cuda graph: True, gen throughput (token/s): 326.76, #queue-req: 0, 
[2025-07-28 00:34:27 DP0 TP0] Decode batch. #running-req: 1, #token: 718, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.83, #queue-req: 0, 
[2025-07-28 00:34:27 DP1 TP0] Decode batch. #running-req: 1, #token: 286, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.73, #queue-req: 0, 
[2025-07-28 00:34:27 DP0 TP0] Decode batch. #running-req: 1, #token: 758, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.13, #queue-req: 0, 
[2025-07-28 00:34:27 DP1 TP0] Decode batch. #running-req: 1, #token: 326, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.88, #queue-req: 0, 
[2025-07-28 00:34:27 DP0 TP0] Decode batch. #running-req: 1, #token: 798, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.23, #queue-req: 0, 
[2025-07-28 00:34:27 DP1 TP0] Decode batch. #running-req: 1, #token: 366, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.90, #queue-req: 0, 
[2025-07-28 00:34:27 DP0 TP0] Decode batch. #running-req: 1, #token: 838, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.75, #queue-req: 0, 
[2025-07-28 00:34:27 DP1 TP0] Decode batch. #running-req: 1, #token: 406, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.78, #queue-req: 0, 
[2025-07-28 00:34:27 DP0 TP0] Decode batch. #running-req: 1, #token: 878, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.33, #queue-req: 0, 
[2025-07-28 00:34:27 DP1 TP0] Decode batch. #running-req: 1, #token: 446, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.13, #queue-req: 0, 
[2025-07-28 00:34:27 DP0 TP0] Decode batch. #running-req: 1, #token: 918, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.18, #queue-req: 0, 
[2025-07-28 00:34:27 DP1 TP0] Decode batch. #running-req: 1, #token: 486, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.90, #queue-req: 0, 
[2025-07-28 00:34:27] INFO:     127.0.0.1:36454 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:27 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 107, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:34:27 DP1 TP0] Decode batch. #running-req: 1, #token: 526, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.17, #queue-req: 0, 
[2025-07-28 00:34:28 DP0 TP0] Decode batch. #running-req: 1, #token: 267, token usage: 0.00, cuda graph: True, gen throughput (token/s): 210.42, #queue-req: 0, 
[2025-07-28 00:34:28 DP1 TP0] Decode batch. #running-req: 1, #token: 566, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.47, #queue-req: 0, 
[2025-07-28 00:34:28 DP0 TP0] Decode batch. #running-req: 1, #token: 307, token usage: 0.00, cuda graph: True, gen throughput (token/s): 326.29, #queue-req: 0, 
[2025-07-28 00:34:28 DP1 TP0] Decode batch. #running-req: 1, #token: 606, token usage: 0.00, cuda graph: True, gen throughput (token/s): 297.82, #queue-req: 0, 
[2025-07-28 00:34:28 DP0 TP0] Decode batch. #running-req: 1, #token: 347, token usage: 0.00, cuda graph: True, gen throughput (token/s): 324.74, #queue-req: 0, 
[2025-07-28 00:34:28 DP1 TP0] Decode batch. #running-req: 1, #token: 646, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.50, #queue-req: 0, 
[2025-07-28 00:34:28 DP0 TP0] Decode batch. #running-req: 1, #token: 387, token usage: 0.00, cuda graph: True, gen throughput (token/s): 299.03, #queue-req: 0, 
[2025-07-28 00:34:28 DP1 TP0] Decode batch. #running-req: 1, #token: 686, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.18, #queue-req: 0, 
[2025-07-28 00:34:28 DP0 TP0] Decode batch. #running-req: 1, #token: 427, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.04, #queue-req: 0, 
[2025-07-28 00:34:28 DP1 TP0] Decode batch. #running-req: 1, #token: 726, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.72, #queue-req: 0, 
[2025-07-28 00:34:28 DP0 TP0] Decode batch. #running-req: 1, #token: 467, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.87, #queue-req: 0, 
[2025-07-28 00:34:28] INFO:     127.0.0.1:36468 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:28 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 78, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:34:28 DP0 TP0] Decode batch. #running-req: 1, #token: 507, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.55, #queue-req: 0, 
[2025-07-28 00:34:28 DP1 TP0] Decode batch. #running-req: 1, #token: 196, token usage: 0.00, cuda graph: True, gen throughput (token/s): 208.29, #queue-req: 0, 
[2025-07-28 00:34:28] INFO:     127.0.0.1:36470 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:28 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 146, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:34:28 DP1 TP0] Decode batch. #running-req: 1, #token: 236, token usage: 0.00, cuda graph: True, gen throughput (token/s): 334.59, #queue-req: 0, 
[2025-07-28 00:34:28 DP0 TP0] Decode batch. #running-req: 1, #token: 283, token usage: 0.00, cuda graph: True, gen throughput (token/s): 214.64, #queue-req: 0, 
[2025-07-28 00:34:29 DP1 TP0] Decode batch. #running-req: 1, #token: 276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.28, #queue-req: 0, 
[2025-07-28 00:34:29 DP0 TP0] Decode batch. #running-req: 1, #token: 323, token usage: 0.00, cuda graph: True, gen throughput (token/s): 336.80, #queue-req: 0, 
[2025-07-28 00:34:29 DP1 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.90, #queue-req: 0, 
[2025-07-28 00:34:29 DP0 TP0] Decode batch. #running-req: 1, #token: 363, token usage: 0.00, cuda graph: True, gen throughput (token/s): 334.78, #queue-req: 0, 
[2025-07-28 00:34:29 DP1 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.11, #queue-req: 0, 
[2025-07-28 00:34:29 DP0 TP0] Decode batch. #running-req: 1, #token: 403, token usage: 0.00, cuda graph: True, gen throughput (token/s): 336.12, #queue-req: 0, 
[2025-07-28 00:34:29 DP1 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.34, #queue-req: 0, 
[2025-07-28 00:34:29 DP0 TP0] Decode batch. #running-req: 1, #token: 443, token usage: 0.00, cuda graph: True, gen throughput (token/s): 335.06, #queue-req: 0, 
[2025-07-28 00:34:29 DP0 TP0] Decode batch. #running-req: 1, #token: 483, token usage: 0.00, cuda graph: True, gen throughput (token/s): 335.81, #queue-req: 0, 
[2025-07-28 00:34:29 DP1 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.00, #queue-req: 0, 
[2025-07-28 00:34:29 DP0 TP0] Decode batch. #running-req: 1, #token: 523, token usage: 0.00, cuda graph: True, gen throughput (token/s): 327.23, #queue-req: 0, 
[2025-07-28 00:34:29 DP1 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.53, #queue-req: 0, 
[2025-07-28 00:34:29 DP0 TP0] Decode batch. #running-req: 1, #token: 563, token usage: 0.00, cuda graph: True, gen throughput (token/s): 330.72, #queue-req: 0, 
[2025-07-28 00:34:29 DP1 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 300.04, #queue-req: 0, 
[2025-07-28 00:34:29] INFO:     127.0.0.1:36496 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:29 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 108, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:34:30 DP1 TP0] Decode batch. #running-req: 2, #token: 669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 372.09, #queue-req: 0, 
[2025-07-28 00:34:30 DP1 TP0] Decode batch. #running-req: 2, #token: 749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 631.75, #queue-req: 0, 
[2025-07-28 00:34:30 DP1 TP0] Decode batch. #running-req: 2, #token: 829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 585.14, #queue-req: 0, 
[2025-07-28 00:34:30 DP1 TP0] Decode batch. #running-req: 2, #token: 909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 607.98, #queue-req: 0, 
[2025-07-28 00:34:30] INFO:     127.0.0.1:36480 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:30 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 173, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:34:30 DP1 TP0] Decode batch. #running-req: 1, #token: 381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 570.25, #queue-req: 0, 
[2025-07-28 00:34:30 DP1 TP0] Decode batch. #running-req: 1, #token: 421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.29, #queue-req: 0, 
[2025-07-28 00:34:30 DP0 TP0] Decode batch. #running-req: 1, #token: 303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 45.18, #queue-req: 0, 
[2025-07-28 00:34:30 DP1 TP0] Decode batch. #running-req: 1, #token: 461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.36, #queue-req: 0, 
[2025-07-28 00:34:30 DP0 TP0] Decode batch. #running-req: 1, #token: 343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 275.29, #queue-req: 0, 
[2025-07-28 00:34:30 DP1 TP0] Decode batch. #running-req: 1, #token: 501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.70, #queue-req: 0, 
[2025-07-28 00:34:31 DP0 TP0] Decode batch. #running-req: 1, #token: 383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 278.90, #queue-req: 0, 
[2025-07-28 00:34:31 DP1 TP0] Decode batch. #running-req: 1, #token: 541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.46, #queue-req: 0, 
[2025-07-28 00:34:31 DP0 TP0] Decode batch. #running-req: 1, #token: 423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 302.10, #queue-req: 0, 
[2025-07-28 00:34:31 DP1 TP0] Decode batch. #running-req: 1, #token: 581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.91, #queue-req: 0, 
[2025-07-28 00:34:31 DP0 TP0] Decode batch. #running-req: 1, #token: 463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.78, #queue-req: 0, 
[2025-07-28 00:34:31 DP1 TP0] Decode batch. #running-req: 1, #token: 621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.89, #queue-req: 0, 
[2025-07-28 00:34:31 DP0 TP0] Decode batch. #running-req: 1, #token: 503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.80, #queue-req: 0, 
[2025-07-28 00:34:31 DP1 TP0] Decode batch. #running-req: 1, #token: 661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.36, #queue-req: 0, 
[2025-07-28 00:34:31 DP0 TP0] Decode batch. #running-req: 1, #token: 543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.83, #queue-req: 0, 
[2025-07-28 00:34:31] INFO:     127.0.0.1:36512 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:31 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 178, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:34:31 DP0 TP0] Decode batch. #running-req: 1, #token: 583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.15, #queue-req: 0, 
[2025-07-28 00:34:31 DP1 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 203.90, #queue-req: 0, 
[2025-07-28 00:34:31 DP0 TP0] Decode batch. #running-req: 1, #token: 623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.78, #queue-req: 0, 
[2025-07-28 00:34:31 DP1 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 332.19, #queue-req: 0, 
[2025-07-28 00:34:31 DP0 TP0] Decode batch. #running-req: 1, #token: 663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.57, #queue-req: 0, 
[2025-07-28 00:34:31 DP1 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 328.73, #queue-req: 0, 
[2025-07-28 00:34:32 DP0 TP0] Decode batch. #running-req: 1, #token: 703, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.43, #queue-req: 0, 
[2025-07-28 00:34:32 DP1 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.99, #queue-req: 0, 
[2025-07-28 00:34:32] INFO:     127.0.0.1:36520 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:32 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 88, #cached-token: 106, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:34:32 DP1 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.44, #queue-req: 0, 
[2025-07-28 00:34:32 DP0 TP0] Decode batch. #running-req: 1, #token: 197, token usage: 0.00, cuda graph: True, gen throughput (token/s): 204.38, #queue-req: 0, 
[2025-07-28 00:34:32 DP1 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.99, #queue-req: 0, 
[2025-07-28 00:34:32 DP0 TP0] Decode batch. #running-req: 1, #token: 237, token usage: 0.00, cuda graph: True, gen throughput (token/s): 331.89, #queue-req: 0, 
[2025-07-28 00:34:32 DP1 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.56, #queue-req: 0, 
[2025-07-28 00:34:32 DP0 TP0] Decode batch. #running-req: 1, #token: 277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 333.33, #queue-req: 0, 
[2025-07-28 00:34:32 DP1 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 305.32, #queue-req: 0, 
[2025-07-28 00:34:32 DP0 TP0] Decode batch. #running-req: 1, #token: 317, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.46, #queue-req: 0, 
[2025-07-28 00:34:32] INFO:     127.0.0.1:36530 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:32 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 153, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:34:32 DP0 TP0] Decode batch. #running-req: 1, #token: 357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.20, #queue-req: 0, 
[2025-07-28 00:34:32 DP1 TP0] Decode batch. #running-req: 1, #token: 281, token usage: 0.00, cuda graph: True, gen throughput (token/s): 202.80, #queue-req: 0, 
[2025-07-28 00:34:32 DP0 TP0] Decode batch. #running-req: 1, #token: 397, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.84, #queue-req: 0, 
[2025-07-28 00:34:32 DP1 TP0] Decode batch. #running-req: 1, #token: 321, token usage: 0.00, cuda graph: True, gen throughput (token/s): 330.22, #queue-req: 0, 
[2025-07-28 00:34:32 DP0 TP0] Decode batch. #running-req: 1, #token: 437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.58, #queue-req: 0, 
[2025-07-28 00:34:33 DP1 TP0] Decode batch. #running-req: 1, #token: 361, token usage: 0.00, cuda graph: True, gen throughput (token/s): 323.13, #queue-req: 0, 
[2025-07-28 00:34:33 DP0 TP0] Decode batch. #running-req: 1, #token: 477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.38, #queue-req: 0, 
[2025-07-28 00:34:33 DP1 TP0] Decode batch. #running-req: 1, #token: 401, token usage: 0.00, cuda graph: True, gen throughput (token/s): 320.07, #queue-req: 0, 
[2025-07-28 00:34:33 DP0 TP0] Decode batch. #running-req: 1, #token: 517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.92, #queue-req: 0, 
[2025-07-28 00:34:33 DP1 TP0] Decode batch. #running-req: 1, #token: 441, token usage: 0.00, cuda graph: True, gen throughput (token/s): 325.46, #queue-req: 0, 
[2025-07-28 00:34:33 DP0 TP0] Decode batch. #running-req: 1, #token: 557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.21, #queue-req: 0, 
[2025-07-28 00:34:33 DP1 TP0] Decode batch. #running-req: 1, #token: 481, token usage: 0.00, cuda graph: True, gen throughput (token/s): 318.03, #queue-req: 0, 
[2025-07-28 00:34:33 DP0 TP0] Decode batch. #running-req: 1, #token: 597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.54, #queue-req: 0, 
[2025-07-28 00:34:33 DP1 TP0] Decode batch. #running-req: 1, #token: 521, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.83, #queue-req: 0, 
[2025-07-28 00:34:33 DP0 TP0] Decode batch. #running-req: 1, #token: 637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.00, #queue-req: 0, 
[2025-07-28 00:34:33 DP1 TP0] Decode batch. #running-req: 1, #token: 561, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.12, #queue-req: 0, 
[2025-07-28 00:34:33 DP0 TP0] Decode batch. #running-req: 1, #token: 677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.53, #queue-req: 0, 
[2025-07-28 00:34:33 DP1 TP0] Decode batch. #running-req: 1, #token: 601, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.81, #queue-req: 0, 
[2025-07-28 00:34:33] INFO:     127.0.0.1:36540 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:33 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 123, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:34:33 DP1 TP0] Decode batch. #running-req: 1, #token: 641, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.14, #queue-req: 0, 
[2025-07-28 00:34:33 DP0 TP0] Decode batch. #running-req: 1, #token: 256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 209.50, #queue-req: 0, 
[2025-07-28 00:34:33] INFO:     127.0.0.1:39188 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:33 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 129, #cached-token: 108, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:34:34 DP0 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 303.29, #queue-req: 0, 
[2025-07-28 00:34:34 DP1 TP0] Decode batch. #running-req: 1, #token: 253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 204.79, #queue-req: 0, 
[2025-07-28 00:34:34 DP0 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.65, #queue-req: 0, 
[2025-07-28 00:34:34 DP1 TP0] Decode batch. #running-req: 1, #token: 293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 332.94, #queue-req: 0, 
[2025-07-28 00:34:34 DP0 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.42, #queue-req: 0, 
[2025-07-28 00:34:34 DP1 TP0] Decode batch. #running-req: 1, #token: 333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 320.30, #queue-req: 0, 
[2025-07-28 00:34:34 DP0 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.14, #queue-req: 0, 
[2025-07-28 00:34:34 DP1 TP0] Decode batch. #running-req: 1, #token: 373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.78, #queue-req: 0, 
[2025-07-28 00:34:34 DP0 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.61, #queue-req: 0, 
[2025-07-28 00:34:34 DP1 TP0] Decode batch. #running-req: 1, #token: 413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.31, #queue-req: 0, 
[2025-07-28 00:34:34 DP0 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 304.21, #queue-req: 0, 
[2025-07-28 00:34:34 DP1 TP0] Decode batch. #running-req: 1, #token: 453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.20, #queue-req: 0, 
[2025-07-28 00:34:34 DP0 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.12, #queue-req: 0, 
[2025-07-28 00:34:34 DP1 TP0] Decode batch. #running-req: 1, #token: 493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.91, #queue-req: 0, 
[2025-07-28 00:34:34 DP0 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.56, #queue-req: 0, 
[2025-07-28 00:34:34 DP1 TP0] Decode batch. #running-req: 1, #token: 533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.52, #queue-req: 0, 
[2025-07-28 00:34:35 DP0 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.86, #queue-req: 0, 
[2025-07-28 00:34:35 DP1 TP0] Decode batch. #running-req: 1, #token: 573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.58, #queue-req: 0, 
[2025-07-28 00:34:35 DP0 TP0] Decode batch. #running-req: 1, #token: 656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 301.07, #queue-req: 0, 
[2025-07-28 00:34:35 DP1 TP0] Decode batch. #running-req: 1, #token: 613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.23, #queue-req: 0, 
[2025-07-28 00:34:35 DP1 TP0] Decode batch. #running-req: 1, #token: 653, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.67, #queue-req: 0, 
[2025-07-28 00:34:35 DP0 TP0] Decode batch. #running-req: 1, #token: 696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.45, #queue-req: 0, 
[2025-07-28 00:34:35 DP1 TP0] Decode batch. #running-req: 1, #token: 693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 320.46, #queue-req: 0, 
[2025-07-28 00:34:35 DP0 TP0] Decode batch. #running-req: 1, #token: 736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.01, #queue-req: 0, 
[2025-07-28 00:34:35 DP1 TP0] Decode batch. #running-req: 1, #token: 733, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.85, #queue-req: 0, 
[2025-07-28 00:34:35 DP0 TP0] Decode batch. #running-req: 1, #token: 776, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.66, #queue-req: 0, 
[2025-07-28 00:34:35 DP1 TP0] Decode batch. #running-req: 1, #token: 773, token usage: 0.00, cuda graph: True, gen throughput (token/s): 322.96, #queue-req: 0, 
[2025-07-28 00:34:35 DP0 TP0] Decode batch. #running-req: 1, #token: 816, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.73, #queue-req: 0, 
[2025-07-28 00:34:35 DP1 TP0] Decode batch. #running-req: 1, #token: 813, token usage: 0.00, cuda graph: True, gen throughput (token/s): 321.58, #queue-req: 0, 
[2025-07-28 00:34:35 DP0 TP0] Decode batch. #running-req: 1, #token: 856, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.26, #queue-req: 0, 
[2025-07-28 00:34:35] INFO:     127.0.0.1:39214 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:35 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 112, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:34:36 DP0 TP0] Decode batch. #running-req: 2, #token: 1060, token usage: 0.00, cuda graph: True, gen throughput (token/s): 396.60, #queue-req: 0, 
[2025-07-28 00:34:36 DP0 TP0] Decode batch. #running-req: 2, #token: 1140, token usage: 0.00, cuda graph: True, gen throughput (token/s): 632.25, #queue-req: 0, 
[2025-07-28 00:34:36 DP0 TP0] Decode batch. #running-req: 2, #token: 1220, token usage: 0.00, cuda graph: True, gen throughput (token/s): 639.83, #queue-req: 0, 
[2025-07-28 00:34:36 DP0 TP0] Decode batch. #running-req: 2, #token: 1300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 615.04, #queue-req: 0, 
[2025-07-28 00:34:36 DP0 TP0] Decode batch. #running-req: 2, #token: 1380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 595.23, #queue-req: 0, 
[2025-07-28 00:34:36 DP0 TP0] Decode batch. #running-req: 2, #token: 1460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 595.55, #queue-req: 0, 
[2025-07-28 00:34:36] INFO:     127.0.0.1:39200 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:36 DP0 TP0] Decode batch. #running-req: 1, #token: 512, token usage: 0.00, cuda graph: True, gen throughput (token/s): 551.36, #queue-req: 0, 
[2025-07-28 00:34:36 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 113, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:34:36 DP0 TP0] Decode batch. #running-req: 1, #token: 552, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.28, #queue-req: 0, 
[2025-07-28 00:34:37 DP1 TP0] Decode batch. #running-req: 1, #token: 288, token usage: 0.00, cuda graph: True, gen throughput (token/s): 34.32, #queue-req: 0, 
[2025-07-28 00:34:37 DP0 TP0] Decode batch. #running-req: 1, #token: 592, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.19, #queue-req: 0, 
[2025-07-28 00:34:37 DP1 TP0] Decode batch. #running-req: 1, #token: 328, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.75, #queue-req: 0, 
[2025-07-28 00:34:37 DP0 TP0] Decode batch. #running-req: 1, #token: 632, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.13, #queue-req: 0, 
[2025-07-28 00:34:37 DP1 TP0] Decode batch. #running-req: 1, #token: 368, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.60, #queue-req: 0, 
[2025-07-28 00:34:37 DP0 TP0] Decode batch. #running-req: 1, #token: 672, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.33, #queue-req: 0, 
[2025-07-28 00:34:37 DP1 TP0] Decode batch. #running-req: 1, #token: 408, token usage: 0.00, cuda graph: True, gen throughput (token/s): 309.78, #queue-req: 0, 
[2025-07-28 00:34:37 DP0 TP0] Decode batch. #running-req: 1, #token: 712, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.55, #queue-req: 0, 
[2025-07-28 00:34:37 DP1 TP0] Decode batch. #running-req: 1, #token: 448, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.58, #queue-req: 0, 
[2025-07-28 00:34:37 DP0 TP0] Decode batch. #running-req: 1, #token: 752, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.17, #queue-req: 0, 
[2025-07-28 00:34:37] INFO:     127.0.0.1:39216 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:37 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 103, #cached-token: 112, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:34:37 DP1 TP0] Decode batch. #running-req: 1, #token: 488, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.94, #queue-req: 0, 
[2025-07-28 00:34:37 DP1 TP0] Decode batch. #running-req: 1, #token: 528, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.90, #queue-req: 0, 
[2025-07-28 00:34:37 DP0 TP0] Decode batch. #running-req: 1, #token: 253, token usage: 0.00, cuda graph: True, gen throughput (token/s): 212.80, #queue-req: 0, 
[2025-07-28 00:34:37 DP0 TP0] Decode batch. #running-req: 1, #token: 293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 334.90, #queue-req: 0, 
[2025-07-28 00:34:37 DP1 TP0] Decode batch. #running-req: 1, #token: 568, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.33, #queue-req: 0, 
[2025-07-28 00:34:38 DP0 TP0] Decode batch. #running-req: 1, #token: 333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 335.73, #queue-req: 0, 
[2025-07-28 00:34:38 DP1 TP0] Decode batch. #running-req: 1, #token: 608, token usage: 0.00, cuda graph: True, gen throughput (token/s): 312.58, #queue-req: 0, 
[2025-07-28 00:34:38 DP0 TP0] Decode batch. #running-req: 1, #token: 373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 337.05, #queue-req: 0, 
[2025-07-28 00:34:38 DP1 TP0] Decode batch. #running-req: 1, #token: 648, token usage: 0.00, cuda graph: True, gen throughput (token/s): 311.28, #queue-req: 0, 
[2025-07-28 00:34:38] INFO:     127.0.0.1:39218 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:38 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 89, #cached-token: 112, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:34:38 DP0 TP0] Decode batch. #running-req: 1, #token: 413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 335.45, #queue-req: 0, 
[2025-07-28 00:34:38 DP1 TP0] Decode batch. #running-req: 1, #token: 228, token usage: 0.00, cuda graph: True, gen throughput (token/s): 215.79, #queue-req: 0, 
[2025-07-28 00:34:38 DP0 TP0] Decode batch. #running-req: 1, #token: 453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 306.36, #queue-req: 0, 
[2025-07-28 00:34:38 DP1 TP0] Decode batch. #running-req: 1, #token: 268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 332.31, #queue-req: 0, 
[2025-07-28 00:34:38 DP0 TP0] Decode batch. #running-req: 1, #token: 493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 314.64, #queue-req: 0, 
[2025-07-28 00:34:38 DP1 TP0] Decode batch. #running-req: 1, #token: 308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.96, #queue-req: 0, 
[2025-07-28 00:34:38 DP0 TP0] Decode batch. #running-req: 1, #token: 533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 307.28, #queue-req: 0, 
[2025-07-28 00:34:38 DP1 TP0] Decode batch. #running-req: 1, #token: 348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.85, #queue-req: 0, 
[2025-07-28 00:34:38 DP0 TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.89, #queue-req: 0, 
[2025-07-28 00:34:38] INFO:     127.0.0.1:39224 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:34:38 DP1 TP0] Decode batch. #running-req: 1, #token: 388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 321.98, #queue-req: 0, 
[2025-07-28 00:34:38 DP1 TP0] Decode batch. #running-req: 1, #token: 428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 321.61, #queue-req: 0, 
[2025-07-28 00:34:39 DP1 TP0] Decode batch. #running-req: 1, #token: 468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.44, #queue-req: 0, 
[2025-07-28 00:34:39 DP1 TP0] Decode batch. #running-req: 1, #token: 508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 321.60, #queue-req: 0, 
[2025-07-28 00:34:39 DP1 TP0] Decode batch. #running-req: 1, #token: 548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 319.16, #queue-req: 0, 
[2025-07-28 00:34:39 DP1 TP0] Decode batch. #running-req: 1, #token: 588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 317.18, #queue-req: 0, 
[2025-07-28 00:34:39 DP1 TP0] Decode batch. #running-req: 1, #token: 628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.04, #queue-req: 0, 
[2025-07-28 00:34:39 DP1 TP0] Decode batch. #running-req: 1, #token: 668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 308.64, #queue-req: 0, 
[2025-07-28 00:34:39 DP1 TP0] Decode batch. #running-req: 1, #token: 708, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.85, #queue-req: 0, 
[2025-07-28 00:34:40 DP1 TP0] Decode batch. #running-req: 1, #token: 748, token usage: 0.00, cuda graph: True, gen throughput (token/s): 313.00, #queue-req: 0, 
[2025-07-28 00:34:40 DP1 TP0] Decode batch. #running-req: 1, #token: 788, token usage: 0.00, cuda graph: True, gen throughput (token/s): 310.33, #queue-req: 0, 
[2025-07-28 00:34:40 DP1 TP0] Decode batch. #running-req: 1, #token: 828, token usage: 0.00, cuda graph: True, gen throughput (token/s): 316.11, #queue-req: 0, 
[2025-07-28 00:34:40 DP1 TP0] Decode batch. #running-req: 1, #token: 868, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.01, #queue-req: 0, 
[2025-07-28 00:34:40 DP1 TP0] Decode batch. #running-req: 1, #token: 908, token usage: 0.00, cuda graph: True, gen throughput (token/s): 315.38, #queue-req: 0, 
[2025-07-28 00:34:40] INFO:     127.0.0.1:39226 - "POST /v1/chat/completions HTTP/1.1" 200 OK

[2025-07-28 01:13:08] server_args=ServerArgs(model_path='/data/home/<USER>/repos/anesbench/share/models/Qwen3-14B', tokenizer_path='/data/home/<USER>/repos/anesbench/share/models/Qwen3-14B', tokenizer_mode='auto', skip_tokenizer_init=False, load_format='auto', model_loader_extra_config='{}', trust_remote_code=False, context_length=32768, is_embedding=False, enable_multimodal=None, revision=None, model_impl='auto', host='127.0.0.1', port=8007, skip_server_warmup=False, warmups=None, nccl_port=None, dtype='auto', quantization=None, quantization_param_path=None, kv_cache_dtype='auto', mem_fraction_static=0.85, max_running_requests=None, max_total_tokens=None, chunked_prefill_size=8192, max_prefill_tokens=16384, schedule_policy='fcfs', schedule_conservativeness=1.0, cpu_offload_gb=0, page_size=1, hybrid_kvcache_ratio=None, swa_full_tokens_ratio=0.8, disable_hybrid_swa_memory=False, device='cuda', tp_size=4, pp_size=1, max_micro_batch_size=None, stream_interval=1, stream_output=False, random_seed=798070624, constrained_json_whitespace_pattern=None, watchdog_timeout=300, dist_timeout=None, download_dir=None, base_gpu_id=0, gpu_id_step=1, sleep_on_idle=False, log_level='info', log_level_http=None, log_requests=False, log_requests_level=0, crash_dump_folder=None, show_time_cost=True, enable_metrics=True, enable_metrics_for_all_schedulers=False, bucket_time_to_first_token=None, bucket_inter_token_latency=None, bucket_e2e_request_latency=None, collect_tokens_histogram=False, decode_log_interval=40, enable_request_time_stats_logging=False, kv_events_config=None, api_key=None, served_model_name='/data/home/<USER>/repos/anesbench/share/models/Qwen3-14B', chat_template=None, completion_template=None, file_storage_path='sglang_storage', enable_cache_report=False, reasoning_parser=None, tool_call_parser=None, dp_size=2, load_balance_method='round_robin', dist_init_addr=None, nnodes=1, node_rank=0, json_model_override_args='{}', preferred_sampling_params=None, enable_lora=None, max_lora_rank=None, lora_target_modules=None, lora_paths=None, max_loras_per_batch=8, lora_backend='triton', attention_backend=None, sampling_backend='flashinfer', grammar_backend='xgrammar', mm_attention_backend=None, speculative_algorithm=None, speculative_draft_model_path=None, speculative_num_steps=None, speculative_eagle_topk=None, speculative_num_draft_tokens=None, speculative_accept_threshold_single=1.0, speculative_accept_threshold_acc=1.0, speculative_token_map=None, ep_size=1, enable_ep_moe=False, enable_deepep_moe=False, enable_flashinfer_moe=False, enable_flashinfer_allreduce_fusion=False, deepep_mode='auto', ep_num_redundant_experts=0, ep_dispatch_algorithm='static', init_expert_location='trivial', enable_eplb=False, eplb_algorithm='auto', eplb_rebalance_num_iterations=1000, eplb_rebalance_layers_per_chunk=None, expert_distribution_recorder_mode=None, expert_distribution_recorder_buffer_size=1000, enable_expert_distribution_metrics=False, deepep_config=None, moe_dense_tp_size=None, enable_hierarchical_cache=False, hicache_ratio=2.0, hicache_size=0, hicache_write_policy='write_through_selective', hicache_io_backend='', hicache_storage_backend=None, enable_double_sparsity=False, ds_channel_config_path=None, ds_heavy_channel_num=32, ds_heavy_token_num=256, ds_heavy_channel_type='qk', ds_sparse_decode_threshold=4096, disable_radix_cache=False, cuda_graph_max_bs=None, cuda_graph_bs=None, disable_cuda_graph=False, disable_cuda_graph_padding=False, enable_profile_cuda_graph=False, enable_nccl_nvls=False, enable_tokenizer_batch_encode=False, disable_outlines_disk_cache=False, disable_custom_all_reduce=False, enable_mscclpp=False, disable_overlap_schedule=False, enable_mixed_chunk=False, enable_dp_attention=False, enable_dp_lm_head=False, enable_two_batch_overlap=False, enable_torch_compile=False, torch_compile_max_bs=32, torchao_config='', enable_nan_detection=False, enable_p2p_check=False, triton_attention_reduce_in_fp32=False, triton_attention_num_kv_splits=8, num_continuous_decode_steps=1, delete_ckpt_after_loading=False, enable_memory_saver=False, allow_auto_truncate=False, enable_custom_logit_processor=False, flashinfer_mla_disable_ragged=False, disable_shared_experts_fusion=False, disable_chunked_prefix_cache=False, disable_fast_image_processor=False, enable_return_hidden_states=False, enable_triton_kernel_moe=False, debug_tensor_dump_output_folder=None, debug_tensor_dump_input_file=None, debug_tensor_dump_inject=False, debug_tensor_dump_prefill_only=False, disaggregation_mode='null', disaggregation_transfer_backend='mooncake', disaggregation_bootstrap_port=8998, disaggregation_decode_tp=None, disaggregation_decode_dp=None, disaggregation_prefill_pp=1, disaggregation_ib_device=None, num_reserved_decode_tokens=512, pdlb_url=None, custom_weight_loader=[], weight_loader_disable_mmap=False, enable_pdmux=False, sm_group_num=3)
[2025-07-28 01:13:14] Launch DP0 starting at GPU #0.
[2025-07-28 01:13:14] Launch DP1 starting at GPU #4.
[2025-07-28 01:13:22 DP1 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 01:13:22 DP1 TP0] Init torch distributed begin.
[2025-07-28 01:13:22 DP0 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 01:13:22 DP0 TP0] Init torch distributed begin.
[2025-07-28 01:13:23 DP1 TP0] sglang is using nccl==2.26.2
[2025-07-28 01:13:24 DP0 TP0] sglang is using nccl==2.26.2
[2025-07-28 01:13:24 DP1 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 01:13:24 DP0 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 01:13:25 DP1 TP0] Load weight begin. avail mem=78.21 GB

Loading safetensors checkpoint shards:   0% Completed | 0/8 [00:00<?, ?it/s]
[2025-07-28 01:13:25 DP0 TP0] Load weight begin. avail mem=78.21 GB

Loading safetensors checkpoint shards:   0% Completed | 0/8 [00:00<?, ?it/s]

Loading safetensors checkpoint shards:  12% Completed | 1/8 [00:01<00:10,  1.53s/it]

Loading safetensors checkpoint shards:  12% Completed | 1/8 [00:01<00:09,  1.35s/it]

Loading safetensors checkpoint shards:  25% Completed | 2/8 [00:02<00:08,  1.48s/it]

Loading safetensors checkpoint shards:  25% Completed | 2/8 [00:02<00:08,  1.41s/it]

Loading safetensors checkpoint shards:  38% Completed | 3/8 [00:04<00:07,  1.55s/it]

Loading safetensors checkpoint shards:  38% Completed | 3/8 [00:04<00:07,  1.51s/it]

Loading safetensors checkpoint shards:  50% Completed | 4/8 [00:05<00:04,  1.10s/it]

Loading safetensors checkpoint shards:  50% Completed | 4/8 [00:04<00:04,  1.10s/it]

Loading safetensors checkpoint shards:  62% Completed | 5/8 [00:06<00:03,  1.29s/it]

Loading safetensors checkpoint shards:  62% Completed | 5/8 [00:06<00:03,  1.27s/it]

Loading safetensors checkpoint shards:  75% Completed | 6/8 [00:08<00:02,  1.42s/it]

Loading safetensors checkpoint shards:  75% Completed | 6/8 [00:08<00:02,  1.40s/it]

Loading safetensors checkpoint shards:  88% Completed | 7/8 [00:09<00:01,  1.47s/it]

Loading safetensors checkpoint shards:  88% Completed | 7/8 [00:09<00:01,  1.46s/it]

Loading safetensors checkpoint shards: 100% Completed | 8/8 [00:11<00:00,  1.52s/it]

Loading safetensors checkpoint shards: 100% Completed | 8/8 [00:11<00:00,  1.51s/it]

Loading safetensors checkpoint shards: 100% Completed | 8/8 [00:11<00:00,  1.44s/it]


Loading safetensors checkpoint shards: 100% Completed | 8/8 [00:11<00:00,  1.42s/it]

[2025-07-28 01:13:36 DP1 TP0] Load weight end. type=Qwen3ForCausalLM, dtype=torch.bfloat16, avail mem=71.13 GB, mem usage=7.07 GB.
[2025-07-28 01:13:37 DP0 TP0] Load weight end. type=Qwen3ForCausalLM, dtype=torch.bfloat16, avail mem=71.13 GB, mem usage=7.07 GB.
[2025-07-28 01:13:37 DP0 TP3] KV Cache is allocated. #tokens: 1555063, K size: 29.66 GB, V size: 29.66 GB
[2025-07-28 01:13:37 DP0 TP2] KV Cache is allocated. #tokens: 1555063, K size: 29.66 GB, V size: 29.66 GB
[2025-07-28 01:13:37 DP0 TP1] KV Cache is allocated. #tokens: 1555063, K size: 29.66 GB, V size: 29.66 GB
[2025-07-28 01:13:37 DP1 TP3] KV Cache is allocated. #tokens: 1555063, K size: 29.66 GB, V size: 29.66 GB
[2025-07-28 01:13:37 DP0 TP0] KV Cache is allocated. #tokens: 1555063, K size: 29.66 GB, V size: 29.66 GB
[2025-07-28 01:13:37 DP0 TP0] Memory pool end. avail mem=11.25 GB
[2025-07-28 01:13:37 DP1 TP2] KV Cache is allocated. #tokens: 1555063, K size: 29.66 GB, V size: 29.66 GB
[2025-07-28 01:13:37 DP1 TP0] KV Cache is allocated. #tokens: 1555063, K size: 29.66 GB, V size: 29.66 GB
[2025-07-28 01:13:37 DP1 TP0] Memory pool end. avail mem=11.25 GB
[2025-07-28 01:13:37 DP1 TP1] KV Cache is allocated. #tokens: 1555063, K size: 29.66 GB, V size: 29.66 GB
[2025-07-28 01:13:37 DP0 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.62 GB
[2025-07-28 01:13:37 DP1 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.62 GB
[2025-07-28 01:13:37 DP0 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]
[2025-07-28 01:13:37 DP1 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.60 GB):   0%|          | 0/23 [00:00<?, ?it/s]
  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.60 GB):   0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.60 GB):   4%|▍         | 1/23 [00:01<00:24,  1.11s/it]
Capturing batches (bs=152 avail_mem=10.33 GB):   4%|▍         | 1/23 [00:01<00:24,  1.11s/it]
Capturing batches (bs=160 avail_mem=10.60 GB):   4%|▍         | 1/23 [00:01<00:24,  1.13s/it]
Capturing batches (bs=152 avail_mem=10.33 GB):   4%|▍         | 1/23 [00:01<00:24,  1.13s/it]
Capturing batches (bs=152 avail_mem=10.33 GB):   9%|▊         | 2/23 [00:01<00:14,  1.49it/s]
Capturing batches (bs=144 avail_mem=10.21 GB):   9%|▊         | 2/23 [00:01<00:14,  1.49it/s]
Capturing batches (bs=152 avail_mem=10.33 GB):   9%|▊         | 2/23 [00:01<00:14,  1.43it/s]
Capturing batches (bs=144 avail_mem=10.21 GB):   9%|▊         | 2/23 [00:01<00:14,  1.43it/s]
Capturing batches (bs=144 avail_mem=10.21 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.69it/s]
Capturing batches (bs=136 avail_mem=10.11 GB):  13%|█▎        | 3/23 [00:01<00:11,  1.69it/s]
Capturing batches (bs=144 avail_mem=10.21 GB):  13%|█▎        | 3/23 [00:02<00:12,  1.62it/s]
Capturing batches (bs=136 avail_mem=10.11 GB):  13%|█▎        | 3/23 [00:02<00:12,  1.62it/s]
Capturing batches (bs=136 avail_mem=10.11 GB):  17%|█▋        | 4/23 [00:02<00:09,  2.10it/s]
Capturing batches (bs=128 avail_mem=10.01 GB):  17%|█▋        | 4/23 [00:02<00:09,  2.10it/s]
Capturing batches (bs=136 avail_mem=10.11 GB):  17%|█▋        | 4/23 [00:02<00:09,  2.01it/s]
Capturing batches (bs=128 avail_mem=10.01 GB):  17%|█▋        | 4/23 [00:02<00:09,  2.01it/s]
Capturing batches (bs=128 avail_mem=10.01 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.41it/s]
Capturing batches (bs=120 avail_mem=9.92 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.41it/s] 
Capturing batches (bs=128 avail_mem=10.01 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.31it/s]
Capturing batches (bs=120 avail_mem=9.92 GB):  22%|██▏       | 5/23 [00:02<00:07,  2.31it/s] 
Capturing batches (bs=120 avail_mem=9.92 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.67it/s]
Capturing batches (bs=112 avail_mem=9.81 GB):  26%|██▌       | 6/23 [00:02<00:06,  2.67it/s]
Capturing batches (bs=120 avail_mem=9.92 GB):  26%|██▌       | 6/23 [00:03<00:06,  2.52it/s]
Capturing batches (bs=112 avail_mem=9.81 GB):  26%|██▌       | 6/23 [00:03<00:06,  2.52it/s]
Capturing batches (bs=112 avail_mem=9.81 GB):  30%|███       | 7/23 [00:03<00:05,  2.87it/s]
Capturing batches (bs=104 avail_mem=9.73 GB):  30%|███       | 7/23 [00:03<00:05,  2.87it/s]
Capturing batches (bs=112 avail_mem=9.81 GB):  30%|███       | 7/23 [00:03<00:06,  2.64it/s]
Capturing batches (bs=104 avail_mem=9.73 GB):  30%|███       | 7/23 [00:03<00:06,  2.64it/s]
Capturing batches (bs=104 avail_mem=9.73 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.99it/s]
Capturing batches (bs=96 avail_mem=9.63 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.99it/s] 
Capturing batches (bs=104 avail_mem=9.73 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.65it/s]
Capturing batches (bs=96 avail_mem=9.63 GB):  35%|███▍      | 8/23 [00:03<00:05,  2.65it/s] 
Capturing batches (bs=96 avail_mem=9.63 GB):  39%|███▉      | 9/23 [00:03<00:04,  2.98it/s]
Capturing batches (bs=88 avail_mem=9.54 GB):  39%|███▉      | 9/23 [00:03<00:04,  2.98it/s]
Capturing batches (bs=88 avail_mem=9.54 GB):  43%|████▎     | 10/23 [00:04<00:04,  3.01it/s]
Capturing batches (bs=80 avail_mem=9.47 GB):  43%|████▎     | 10/23 [00:04<00:04,  3.01it/s]
Capturing batches (bs=96 avail_mem=9.63 GB):  39%|███▉      | 9/23 [00:04<00:05,  2.55it/s]
Capturing batches (bs=88 avail_mem=9.56 GB):  39%|███▉      | 9/23 [00:04<00:05,  2.55it/s]
Capturing batches (bs=80 avail_mem=9.47 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.06it/s]
Capturing batches (bs=72 avail_mem=9.45 GB):  48%|████▊     | 11/23 [00:04<00:03,  3.06it/s]
Capturing batches (bs=88 avail_mem=9.56 GB):  43%|████▎     | 10/23 [00:04<00:04,  2.62it/s]
Capturing batches (bs=80 avail_mem=9.47 GB):  43%|████▎     | 10/23 [00:04<00:04,  2.62it/s]
Capturing batches (bs=72 avail_mem=9.45 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.14it/s]
Capturing batches (bs=64 avail_mem=9.37 GB):  52%|█████▏    | 12/23 [00:04<00:03,  3.14it/s]
Capturing batches (bs=80 avail_mem=9.47 GB):  48%|████▊     | 11/23 [00:04<00:04,  2.78it/s]
Capturing batches (bs=72 avail_mem=9.45 GB):  48%|████▊     | 11/23 [00:04<00:04,  2.78it/s]
Capturing batches (bs=64 avail_mem=9.37 GB):  57%|█████▋    | 13/23 [00:05<00:03,  3.20it/s]
Capturing batches (bs=56 avail_mem=9.33 GB):  57%|█████▋    | 13/23 [00:05<00:03,  3.20it/s]
Capturing batches (bs=72 avail_mem=9.45 GB):  52%|█████▏    | 12/23 [00:05<00:03,  2.92it/s]
Capturing batches (bs=64 avail_mem=9.37 GB):  52%|█████▏    | 12/23 [00:05<00:03,  2.92it/s]
Capturing batches (bs=56 avail_mem=9.33 GB):  61%|██████    | 14/23 [00:05<00:02,  3.24it/s]
Capturing batches (bs=48 avail_mem=9.27 GB):  61%|██████    | 14/23 [00:05<00:02,  3.24it/s]
Capturing batches (bs=64 avail_mem=9.37 GB):  57%|█████▋    | 13/23 [00:05<00:03,  3.04it/s]
Capturing batches (bs=56 avail_mem=9.33 GB):  57%|█████▋    | 13/23 [00:05<00:03,  3.04it/s]
Capturing batches (bs=48 avail_mem=9.27 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.23it/s]
Capturing batches (bs=40 avail_mem=9.26 GB):  65%|██████▌   | 15/23 [00:05<00:02,  3.23it/s]
Capturing batches (bs=56 avail_mem=9.33 GB):  61%|██████    | 14/23 [00:05<00:02,  3.10it/s]
Capturing batches (bs=48 avail_mem=9.27 GB):  61%|██████    | 14/23 [00:05<00:02,  3.10it/s]
Capturing batches (bs=40 avail_mem=9.26 GB):  70%|██████▉   | 16/23 [00:05<00:02,  3.22it/s]
Capturing batches (bs=32 avail_mem=9.21 GB):  70%|██████▉   | 16/23 [00:05<00:02,  3.22it/s]
Capturing batches (bs=48 avail_mem=9.27 GB):  65%|██████▌   | 15/23 [00:06<00:02,  3.18it/s]
Capturing batches (bs=40 avail_mem=9.26 GB):  65%|██████▌   | 15/23 [00:06<00:02,  3.18it/s]
Capturing batches (bs=32 avail_mem=9.21 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.28it/s]
Capturing batches (bs=24 avail_mem=9.20 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.28it/s]
Capturing batches (bs=40 avail_mem=9.26 GB):  70%|██████▉   | 16/23 [00:06<00:02,  3.23it/s]
Capturing batches (bs=32 avail_mem=9.21 GB):  70%|██████▉   | 16/23 [00:06<00:02,  3.23it/s]
Capturing batches (bs=24 avail_mem=9.20 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.26it/s]
Capturing batches (bs=16 avail_mem=9.16 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.26it/s]
Capturing batches (bs=32 avail_mem=9.21 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.27it/s]
Capturing batches (bs=24 avail_mem=9.20 GB):  74%|███████▍  | 17/23 [00:06<00:01,  3.27it/s]
Capturing batches (bs=16 avail_mem=9.16 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.29it/s]
Capturing batches (bs=8 avail_mem=9.15 GB):  83%|████████▎ | 19/23 [00:06<00:01,  3.29it/s] 
Capturing batches (bs=24 avail_mem=9.20 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.29it/s]
Capturing batches (bs=16 avail_mem=9.16 GB):  78%|███████▊  | 18/23 [00:06<00:01,  3.29it/s]
Capturing batches (bs=8 avail_mem=9.15 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.27it/s]
Capturing batches (bs=4 avail_mem=9.13 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.27it/s]
Capturing batches (bs=16 avail_mem=9.16 GB):  83%|████████▎ | 19/23 [00:07<00:01,  3.25it/s]
Capturing batches (bs=8 avail_mem=9.15 GB):  83%|████████▎ | 19/23 [00:07<00:01,  3.25it/s] 
Capturing batches (bs=4 avail_mem=9.13 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.33it/s]
Capturing batches (bs=2 avail_mem=9.12 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.33it/s]
Capturing batches (bs=8 avail_mem=9.15 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.02it/s]
Capturing batches (bs=4 avail_mem=9.13 GB):  87%|████████▋ | 20/23 [00:07<00:00,  3.02it/s]
Capturing batches (bs=2 avail_mem=9.12 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.37it/s]
Capturing batches (bs=1 avail_mem=9.09 GB):  96%|█████████▌| 22/23 [00:07<00:00,  3.37it/s]
Capturing batches (bs=4 avail_mem=9.13 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.08it/s]
Capturing batches (bs=2 avail_mem=9.12 GB):  91%|█████████▏| 21/23 [00:07<00:00,  3.08it/s]
Capturing batches (bs=1 avail_mem=9.09 GB): 100%|██████████| 23/23 [00:08<00:00,  3.38it/s]
Capturing batches (bs=1 avail_mem=9.09 GB): 100%|██████████| 23/23 [00:08<00:00,  2.85it/s]
[2025-07-28 01:13:45 DP0 TP2] Registering 1863 cuda graph addresses
[2025-07-28 01:13:45 DP0 TP0] Registering 1863 cuda graph addresses
[2025-07-28 01:13:45 DP0 TP1] Registering 1863 cuda graph addresses
[2025-07-28 01:13:45 DP0 TP3] Registering 1863 cuda graph addresses
[2025-07-28 01:13:45 DP0 TP0] Capture cuda graph end. Time elapsed: 8.30 s. mem usage=1.54 GB. avail mem=9.08 GB.

Capturing batches (bs=2 avail_mem=9.12 GB):  96%|█████████▌| 22/23 [00:08<00:00,  3.13it/s]
Capturing batches (bs=1 avail_mem=9.09 GB):  96%|█████████▌| 22/23 [00:08<00:00,  3.13it/s][2025-07-28 01:13:46 DP0 TP0] max_total_num_tokens=1555063, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=9.08 GB

Capturing batches (bs=1 avail_mem=9.09 GB): 100%|██████████| 23/23 [00:08<00:00,  3.17it/s]
Capturing batches (bs=1 avail_mem=9.09 GB): 100%|██████████| 23/23 [00:08<00:00,  2.69it/s]
[2025-07-28 01:13:46 DP1 TP3] Registering 1863 cuda graph addresses
[2025-07-28 01:13:46 DP1 TP1] Registering 1863 cuda graph addresses
[2025-07-28 01:13:46 DP1 TP0] Registering 1863 cuda graph addresses
[2025-07-28 01:13:46 DP1 TP2] Registering 1863 cuda graph addresses
[2025-07-28 01:13:46 DP1 TP0] Capture cuda graph end. Time elapsed: 8.81 s. mem usage=1.54 GB. avail mem=9.08 GB.
[2025-07-28 01:13:46 DP1 TP0] max_total_num_tokens=1555063, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=9.08 GB
[2025-07-28 01:13:47] INFO:     Started server process [1827795]
[2025-07-28 01:13:47] INFO:     Waiting for application startup.
[2025-07-28 01:13:47] INFO:     Application startup complete.
[2025-07-28 01:13:47] INFO:     Uvicorn running on http://127.0.0.1:8007 (Press CTRL+C to quit)
[2025-07-28 01:13:48] INFO:     127.0.0.1:53140 - "GET /get_model_info HTTP/1.1" 200 OK
[2025-07-28 01:13:48 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:13:48 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:13:49] INFO:     127.0.0.1:53170 - "GET /health HTTP/1.1" 200 OK
[2025-07-28 01:13:49 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 235, #cached-token: 0, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:13:49 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 194, #cached-token: 0, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:13:49] INFO:     127.0.0.1:53156 - "POST /generate HTTP/1.1" 200 OK
[2025-07-28 01:13:49] The server is fired up and ready to roll!
[2025-07-28 01:13:49 DP0 TP0] Decode batch. #running-req: 1, #token: 275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 13.16, #queue-req: 0, 
[2025-07-28 01:13:49 DP1 TP0] Decode batch. #running-req: 1, #token: 234, token usage: 0.00, cuda graph: True, gen throughput (token/s): 15.13, #queue-req: 0, 
[2025-07-28 01:13:50 DP0 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.24, #queue-req: 0, 
[2025-07-28 01:13:50 DP1 TP0] Decode batch. #running-req: 1, #token: 274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:13:50 DP0 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:13:50 DP1 TP0] Decode batch. #running-req: 1, #token: 314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.56, #queue-req: 0, 
[2025-07-28 01:13:50 DP0 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:13:50 DP1 TP0] Decode batch. #running-req: 1, #token: 354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.47, #queue-req: 0, 
[2025-07-28 01:13:50 DP0 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.52, #queue-req: 0, 
[2025-07-28 01:13:50 DP1 TP0] Decode batch. #running-req: 1, #token: 394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:13:51 DP0 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.07, #queue-req: 0, 
[2025-07-28 01:13:51 DP1 TP0] Decode batch. #running-req: 1, #token: 434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:13:51 DP0 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.67, #queue-req: 0, 
[2025-07-28 01:13:51 DP1 TP0] Decode batch. #running-req: 1, #token: 474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.91, #queue-req: 0, 
[2025-07-28 01:13:51 DP0 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.01, #queue-req: 0, 
[2025-07-28 01:13:51 DP1 TP0] Decode batch. #running-req: 1, #token: 514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.87, #queue-req: 0, 
[2025-07-28 01:13:52 DP0 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.85, #queue-req: 0, 
[2025-07-28 01:13:52 DP1 TP0] Decode batch. #running-req: 1, #token: 554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.96, #queue-req: 0, 
[2025-07-28 01:13:52 DP0 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.08, #queue-req: 0, 
[2025-07-28 01:13:52 DP1 TP0] Decode batch. #running-req: 1, #token: 594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.96, #queue-req: 0, 
[2025-07-28 01:13:52 DP0 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.77, #queue-req: 0, 
[2025-07-28 01:13:52 DP1 TP0] Decode batch. #running-req: 1, #token: 634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.09, #queue-req: 0, 
[2025-07-28 01:13:52 DP0 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.74, #queue-req: 0, 
[2025-07-28 01:13:52 DP1 TP0] Decode batch. #running-req: 1, #token: 674, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.22, #queue-req: 0, 
[2025-07-28 01:13:53] INFO:     127.0.0.1:53180 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:13:53 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:13:53 DP1 TP0] Decode batch. #running-req: 1, #token: 714, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.26, #queue-req: 0, 
[2025-07-28 01:13:53 DP0 TP0] Decode batch. #running-req: 1, #token: 174, token usage: 0.00, cuda graph: True, gen throughput (token/s): 76.53, #queue-req: 0, 
[2025-07-28 01:13:53 DP1 TP0] Decode batch. #running-req: 1, #token: 754, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.16, #queue-req: 0, 
[2025-07-28 01:13:53 DP0 TP0] Decode batch. #running-req: 1, #token: 214, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.93, #queue-req: 0, 
[2025-07-28 01:13:53] INFO:     127.0.0.1:53178 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:13:53 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 207, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:13:54 DP0 TP0] Decode batch. #running-req: 1, #token: 254, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.22, #queue-req: 0, 
[2025-07-28 01:13:54 DP1 TP0] Decode batch. #running-req: 1, #token: 303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 77.79, #queue-req: 0, 
[2025-07-28 01:13:54 DP0 TP0] Decode batch. #running-req: 1, #token: 294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.91, #queue-req: 0, 
[2025-07-28 01:13:54 DP1 TP0] Decode batch. #running-req: 1, #token: 343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:13:54 DP0 TP0] Decode batch. #running-req: 1, #token: 334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:13:54 DP1 TP0] Decode batch. #running-req: 1, #token: 383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:13:54 DP0 TP0] Decode batch. #running-req: 1, #token: 374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:13:54 DP1 TP0] Decode batch. #running-req: 1, #token: 423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.31, #queue-req: 0, 
[2025-07-28 01:13:55 DP0 TP0] Decode batch. #running-req: 1, #token: 414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.24, #queue-req: 0, 
[2025-07-28 01:13:55 DP1 TP0] Decode batch. #running-req: 1, #token: 463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.23, #queue-req: 0, 
[2025-07-28 01:13:55 DP0 TP0] Decode batch. #running-req: 1, #token: 454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.19, #queue-req: 0, 
[2025-07-28 01:13:55 DP1 TP0] Decode batch. #running-req: 1, #token: 503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:13:55 DP0 TP0] Decode batch. #running-req: 1, #token: 494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.96, #queue-req: 0, 
[2025-07-28 01:13:55 DP1 TP0] Decode batch. #running-req: 1, #token: 543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.27, #queue-req: 0, 
[2025-07-28 01:13:56 DP0 TP0] Decode batch. #running-req: 1, #token: 534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.22, #queue-req: 0, 
[2025-07-28 01:13:56 DP1 TP0] Decode batch. #running-req: 1, #token: 583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.13, #queue-req: 0, 
[2025-07-28 01:13:56 DP0 TP0] Decode batch. #running-req: 1, #token: 574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.19, #queue-req: 0, 
[2025-07-28 01:13:56 DP1 TP0] Decode batch. #running-req: 1, #token: 623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:13:56 DP0 TP0] Decode batch. #running-req: 1, #token: 614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.13, #queue-req: 0, 
[2025-07-28 01:13:56 DP1 TP0] Decode batch. #running-req: 1, #token: 663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:13:56] INFO:     127.0.0.1:56900 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:13:56 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 86, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:13:56 DP0 TP0] Decode batch. #running-req: 1, #token: 244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 119.07, #queue-req: 0, 
[2025-07-28 01:13:56 DP1 TP0] Decode batch. #running-req: 1, #token: 703, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.20, #queue-req: 0, 
[2025-07-28 01:13:57 DP0 TP0] Decode batch. #running-req: 1, #token: 284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:13:57 DP1 TP0] Decode batch. #running-req: 1, #token: 743, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.14, #queue-req: 0, 
[2025-07-28 01:13:57 DP0 TP0] Decode batch. #running-req: 1, #token: 324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.11, #queue-req: 0, 
[2025-07-28 01:13:57 DP1 TP0] Decode batch. #running-req: 1, #token: 783, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.72, #queue-req: 0, 
[2025-07-28 01:13:57 DP0 TP0] Decode batch. #running-req: 1, #token: 364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.92, #queue-req: 0, 
[2025-07-28 01:13:57 DP1 TP0] Decode batch. #running-req: 1, #token: 823, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:13:58 DP0 TP0] Decode batch. #running-req: 1, #token: 404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.88, #queue-req: 0, 
[2025-07-28 01:13:58 DP1 TP0] Decode batch. #running-req: 1, #token: 863, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.00, #queue-req: 0, 
[2025-07-28 01:13:58 DP0 TP0] Decode batch. #running-req: 1, #token: 444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.04, #queue-req: 0, 
[2025-07-28 01:13:58 DP1 TP0] Decode batch. #running-req: 1, #token: 903, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.99, #queue-req: 0, 
[2025-07-28 01:13:58 DP0 TP0] Decode batch. #running-req: 1, #token: 484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.94, #queue-req: 0, 
[2025-07-28 01:13:58 DP1 TP0] Decode batch. #running-req: 1, #token: 943, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.13, #queue-req: 0, 
[2025-07-28 01:13:58 DP0 TP0] Decode batch. #running-req: 1, #token: 524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.21, #queue-req: 0, 
[2025-07-28 01:13:58 DP1 TP0] Decode batch. #running-req: 1, #token: 983, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.46, #queue-req: 0, 
[2025-07-28 01:13:59 DP0 TP0] Decode batch. #running-req: 1, #token: 564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.12, #queue-req: 0, 
[2025-07-28 01:13:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1023, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:13:59 DP0 TP0] Decode batch. #running-req: 1, #token: 604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.11, #queue-req: 0, 
[2025-07-28 01:13:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1063, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.99, #queue-req: 0, 
[2025-07-28 01:13:59 DP0 TP0] Decode batch. #running-req: 1, #token: 644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.17, #queue-req: 0, 
[2025-07-28 01:13:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1103, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.06, #queue-req: 0, 
[2025-07-28 01:14:00 DP0 TP0] Decode batch. #running-req: 1, #token: 684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.11, #queue-req: 0, 
[2025-07-28 01:14:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1143, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.94, #queue-req: 0, 
[2025-07-28 01:14:00 DP0 TP0] Decode batch. #running-req: 1, #token: 724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.13, #queue-req: 0, 
[2025-07-28 01:14:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1183, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.07, #queue-req: 0, 
[2025-07-28 01:14:00] INFO:     127.0.0.1:56908 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:14:00 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 133, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:14:00 DP1 TP0] Decode batch. #running-req: 2, #token: 1362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.46, #queue-req: 0, 
[2025-07-28 01:14:01 DP1 TP0] Decode batch. #running-req: 2, #token: 1442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.49, #queue-req: 0, 
[2025-07-28 01:14:01 DP1 TP0] Decode batch. #running-req: 2, #token: 1522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.61, #queue-req: 0, 
[2025-07-28 01:14:01 DP1 TP0] Decode batch. #running-req: 2, #token: 1602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.46, #queue-req: 0, 
[2025-07-28 01:14:01 DP1 TP0] Decode batch. #running-req: 2, #token: 1682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.60, #queue-req: 0, 
[2025-07-28 01:14:02 DP1 TP0] Decode batch. #running-req: 2, #token: 1762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.48, #queue-req: 0, 
[2025-07-28 01:14:02 DP1 TP0] Decode batch. #running-req: 2, #token: 1842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.18, #queue-req: 0, 
[2025-07-28 01:14:02 DP1 TP0] Decode batch. #running-req: 2, #token: 1922, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.25, #queue-req: 0, 
[2025-07-28 01:14:03 DP1 TP0] Decode batch. #running-req: 2, #token: 2002, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.33, #queue-req: 0, 
[2025-07-28 01:14:03 DP1 TP0] Decode batch. #running-req: 2, #token: 2082, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.29, #queue-req: 0, 
[2025-07-28 01:14:03 DP1 TP0] Decode batch. #running-req: 2, #token: 2162, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.31, #queue-req: 0, 
[2025-07-28 01:14:03 DP1 TP0] Decode batch. #running-req: 2, #token: 2242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.09, #queue-req: 0, 
[2025-07-28 01:14:04 DP1 TP0] Decode batch. #running-req: 2, #token: 2322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.19, #queue-req: 0, 
[2025-07-28 01:14:04 DP1 TP0] Decode batch. #running-req: 2, #token: 2402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.86, #queue-req: 0, 
[2025-07-28 01:14:04 DP1 TP0] Decode batch. #running-req: 2, #token: 2482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.40, #queue-req: 0, 
[2025-07-28 01:14:05 DP1 TP0] Decode batch. #running-req: 2, #token: 2562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.97, #queue-req: 0, 
[2025-07-28 01:14:05 DP1 TP0] Decode batch. #running-req: 2, #token: 2642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.30, #queue-req: 0, 
[2025-07-28 01:14:05 DP1 TP0] Decode batch. #running-req: 2, #token: 2722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.90, #queue-req: 0, 
[2025-07-28 01:14:06 DP1 TP0] Decode batch. #running-req: 2, #token: 2802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.82, #queue-req: 0, 
[2025-07-28 01:14:06 DP1 TP0] Decode batch. #running-req: 2, #token: 2882, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.14, #queue-req: 0, 
[2025-07-28 01:14:06 DP1 TP0] Decode batch. #running-req: 2, #token: 2962, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.80, #queue-req: 0, 
[2025-07-28 01:14:06 DP1 TP0] Decode batch. #running-req: 2, #token: 3042, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.52, #queue-req: 0, 
[2025-07-28 01:14:07 DP1 TP0] Decode batch. #running-req: 2, #token: 3122, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.43, #queue-req: 0, 
[2025-07-28 01:14:07 DP1 TP0] Decode batch. #running-req: 2, #token: 3202, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.16, #queue-req: 0, 
[2025-07-28 01:14:07] INFO:     127.0.0.1:56920 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:14:07 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 186, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:14:07 DP1 TP0] Decode batch. #running-req: 1, #token: 2183, token usage: 0.00, cuda graph: True, gen throughput (token/s): 249.44, #queue-req: 0, 
[2025-07-28 01:14:07 DP0 TP0] Decode batch. #running-req: 1, #token: 278, token usage: 0.00, cuda graph: True, gen throughput (token/s): 5.28, #queue-req: 0, 
[2025-07-28 01:14:08 DP1 TP0] Decode batch. #running-req: 1, #token: 2223, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.56, #queue-req: 0, 
[2025-07-28 01:14:08 DP0 TP0] Decode batch. #running-req: 1, #token: 318, token usage: 0.00, cuda graph: True, gen throughput (token/s): 130.87, #queue-req: 0, 
[2025-07-28 01:14:08 DP1 TP0] Decode batch. #running-req: 1, #token: 2263, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.46, #queue-req: 0, 
[2025-07-28 01:14:08 DP0 TP0] Decode batch. #running-req: 1, #token: 358, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:14:08 DP1 TP0] Decode batch. #running-req: 1, #token: 2303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.59, #queue-req: 0, 
[2025-07-28 01:14:08 DP0 TP0] Decode batch. #running-req: 1, #token: 398, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:14:09 DP1 TP0] Decode batch. #running-req: 1, #token: 2343, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.39, #queue-req: 0, 
[2025-07-28 01:14:09 DP0 TP0] Decode batch. #running-req: 1, #token: 438, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:14:09 DP1 TP0] Decode batch. #running-req: 1, #token: 2383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.57, #queue-req: 0, 
[2025-07-28 01:14:09 DP0 TP0] Decode batch. #running-req: 1, #token: 478, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.28, #queue-req: 0, 
[2025-07-28 01:14:09 DP1 TP0] Decode batch. #running-req: 1, #token: 2423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.40, #queue-req: 0, 
[2025-07-28 01:14:09 DP0 TP0] Decode batch. #running-req: 1, #token: 518, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.23, #queue-req: 0, 
[2025-07-28 01:14:09 DP1 TP0] Decode batch. #running-req: 1, #token: 2463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.40, #queue-req: 0, 
[2025-07-28 01:14:09 DP0 TP0] Decode batch. #running-req: 1, #token: 558, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.14, #queue-req: 0, 
[2025-07-28 01:14:10 DP1 TP0] Decode batch. #running-req: 1, #token: 2503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.34, #queue-req: 0, 
[2025-07-28 01:14:10 DP0 TP0] Decode batch. #running-req: 1, #token: 598, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.07, #queue-req: 0, 
[2025-07-28 01:14:10 DP1 TP0] Decode batch. #running-req: 1, #token: 2543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.27, #queue-req: 0, 
[2025-07-28 01:14:10 DP0 TP0] Decode batch. #running-req: 1, #token: 638, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.17, #queue-req: 0, 
[2025-07-28 01:14:10 DP1 TP0] Decode batch. #running-req: 1, #token: 2583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.40, #queue-req: 0, 
[2025-07-28 01:14:10 DP0 TP0] Decode batch. #running-req: 1, #token: 678, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.09, #queue-req: 0, 
[2025-07-28 01:14:11 DP1 TP0] Decode batch. #running-req: 1, #token: 2623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.32, #queue-req: 0, 
[2025-07-28 01:14:11 DP0 TP0] Decode batch. #running-req: 1, #token: 718, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.76, #queue-req: 0, 
[2025-07-28 01:14:11 DP1 TP0] Decode batch. #running-req: 1, #token: 2663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.99, #queue-req: 0, 
[2025-07-28 01:14:11 DP0 TP0] Decode batch. #running-req: 1, #token: 758, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.11, #queue-req: 0, 
[2025-07-28 01:14:11 DP1 TP0] Decode batch. #running-req: 1, #token: 2703, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.26, #queue-req: 0, 
[2025-07-28 01:14:11 DP0 TP0] Decode batch. #running-req: 1, #token: 798, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.04, #queue-req: 0, 
[2025-07-28 01:14:11 DP1 TP0] Decode batch. #running-req: 1, #token: 2743, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.14, #queue-req: 0, 
[2025-07-28 01:14:12 DP0 TP0] Decode batch. #running-req: 1, #token: 838, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.08, #queue-req: 0, 
[2025-07-28 01:14:12 DP1 TP0] Decode batch. #running-req: 1, #token: 2783, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.34, #queue-req: 0, 
[2025-07-28 01:14:12 DP0 TP0] Decode batch. #running-req: 1, #token: 878, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.09, #queue-req: 0, 
[2025-07-28 01:14:12 DP1 TP0] Decode batch. #running-req: 1, #token: 2823, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.20, #queue-req: 0, 
[2025-07-28 01:14:12 DP0 TP0] Decode batch. #running-req: 1, #token: 918, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.10, #queue-req: 0, 
[2025-07-28 01:14:12 DP1 TP0] Decode batch. #running-req: 1, #token: 2863, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.21, #queue-req: 0, 
[2025-07-28 01:14:12 DP0 TP0] Decode batch. #running-req: 1, #token: 958, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.69, #queue-req: 0, 
[2025-07-28 01:14:13 DP1 TP0] Decode batch. #running-req: 1, #token: 2903, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.20, #queue-req: 0, 
[2025-07-28 01:14:13 DP0 TP0] Decode batch. #running-req: 1, #token: 998, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.20, #queue-req: 0, 
[2025-07-28 01:14:13 DP1 TP0] Decode batch. #running-req: 1, #token: 2943, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.16, #queue-req: 0, 
[2025-07-28 01:14:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1038, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.93, #queue-req: 0, 
[2025-07-28 01:14:13 DP1 TP0] Decode batch. #running-req: 1, #token: 2983, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.21, #queue-req: 0, 
[2025-07-28 01:14:13] INFO:     127.0.0.1:39476 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:14:13 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 173, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:14:13 DP1 TP0] Decode batch. #running-req: 2, #token: 3223, token usage: 0.00, cuda graph: True, gen throughput (token/s): 193.86, #queue-req: 0, 
[2025-07-28 01:14:14 DP1 TP0] Decode batch. #running-req: 2, #token: 3303, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.95, #queue-req: 0, 
[2025-07-28 01:14:14 DP1 TP0] Decode batch. #running-req: 2, #token: 3383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.13, #queue-req: 0, 
[2025-07-28 01:14:14] INFO:     127.0.0.1:56902 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:14:14 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 226, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:14:14 DP1 TP0] Decode batch. #running-req: 1, #token: 407, token usage: 0.00, cuda graph: True, gen throughput (token/s): 236.36, #queue-req: 0, 
[2025-07-28 01:14:14 DP0 TP0] Decode batch. #running-req: 1, #token: 315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 27.59, #queue-req: 0, 
[2025-07-28 01:14:15 DP1 TP0] Decode batch. #running-req: 1, #token: 447, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.13, #queue-req: 0, 
[2025-07-28 01:14:15 DP0 TP0] Decode batch. #running-req: 1, #token: 355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.79, #queue-req: 0, 
[2025-07-28 01:14:15 DP1 TP0] Decode batch. #running-req: 1, #token: 487, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.45, #queue-req: 0, 
[2025-07-28 01:14:15 DP0 TP0] Decode batch. #running-req: 1, #token: 395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:14:15 DP1 TP0] Decode batch. #running-req: 1, #token: 527, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.39, #queue-req: 0, 
[2025-07-28 01:14:15 DP0 TP0] Decode batch. #running-req: 1, #token: 435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.12, #queue-req: 0, 
[2025-07-28 01:14:16 DP1 TP0] Decode batch. #running-req: 1, #token: 567, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:14:16 DP0 TP0] Decode batch. #running-req: 1, #token: 475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:14:16 DP1 TP0] Decode batch. #running-req: 1, #token: 607, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:14:16 DP0 TP0] Decode batch. #running-req: 1, #token: 515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:14:16 DP1 TP0] Decode batch. #running-req: 1, #token: 647, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.27, #queue-req: 0, 
[2025-07-28 01:14:16 DP0 TP0] Decode batch. #running-req: 1, #token: 555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.15, #queue-req: 0, 
[2025-07-28 01:14:16 DP1 TP0] Decode batch. #running-req: 1, #token: 687, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:14:16 DP0 TP0] Decode batch. #running-req: 1, #token: 595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.19, #queue-req: 0, 
[2025-07-28 01:14:17 DP1 TP0] Decode batch. #running-req: 1, #token: 727, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:14:17 DP0 TP0] Decode batch. #running-req: 1, #token: 635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.21, #queue-req: 0, 
[2025-07-28 01:14:17 DP1 TP0] Decode batch. #running-req: 1, #token: 767, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:14:17 DP0 TP0] Decode batch. #running-req: 1, #token: 675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.25, #queue-req: 0, 
[2025-07-28 01:14:17 DP1 TP0] Decode batch. #running-req: 1, #token: 807, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:14:17 DP0 TP0] Decode batch. #running-req: 1, #token: 715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.06, #queue-req: 0, 
[2025-07-28 01:14:18 DP1 TP0] Decode batch. #running-req: 1, #token: 847, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:14:18 DP0 TP0] Decode batch. #running-req: 1, #token: 755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.20, #queue-req: 0, 
[2025-07-28 01:14:18 DP1 TP0] Decode batch. #running-req: 1, #token: 887, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.28, #queue-req: 0, 
[2025-07-28 01:14:18 DP0 TP0] Decode batch. #running-req: 1, #token: 795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.21, #queue-req: 0, 
[2025-07-28 01:14:18 DP1 TP0] Decode batch. #running-req: 1, #token: 927, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:14:18 DP0 TP0] Decode batch. #running-req: 1, #token: 835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.18, #queue-req: 0, 
[2025-07-28 01:14:18 DP1 TP0] Decode batch. #running-req: 1, #token: 967, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:14:18 DP0 TP0] Decode batch. #running-req: 1, #token: 875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.20, #queue-req: 0, 
[2025-07-28 01:14:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1007, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:14:19 DP0 TP0] Decode batch. #running-req: 1, #token: 915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.18, #queue-req: 0, 
[2025-07-28 01:14:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1047, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.15, #queue-req: 0, 
[2025-07-28 01:14:19 DP0 TP0] Decode batch. #running-req: 1, #token: 955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.15, #queue-req: 0, 
[2025-07-28 01:14:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1087, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.02, #queue-req: 0, 
[2025-07-28 01:14:19 DP0 TP0] Decode batch. #running-req: 1, #token: 995, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.21, #queue-req: 0, 
[2025-07-28 01:14:20 DP1 TP0] Decode batch. #running-req: 1, #token: 1127, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.03, #queue-req: 0, 
[2025-07-28 01:14:20 DP0 TP0] Decode batch. #running-req: 1, #token: 1035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.13, #queue-req: 0, 
[2025-07-28 01:14:20] INFO:     127.0.0.1:35628 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:14:20 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 168, #cached-token: 88, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:14:20 DP1 TP0] Decode batch. #running-req: 2, #token: 1352, token usage: 0.00, cuda graph: True, gen throughput (token/s): 168.04, #queue-req: 0, 
[2025-07-28 01:14:20 DP1 TP0] Decode batch. #running-req: 2, #token: 1432, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.41, #queue-req: 0, 
[2025-07-28 01:14:20 DP1 TP0] Decode batch. #running-req: 2, #token: 1512, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.25, #queue-req: 0, 
[2025-07-28 01:14:21 DP1 TP0] Decode batch. #running-req: 2, #token: 1592, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.43, #queue-req: 0, 
[2025-07-28 01:14:21 DP1 TP0] Decode batch. #running-req: 2, #token: 1672, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.15, #queue-req: 0, 
[2025-07-28 01:14:21 DP1 TP0] Decode batch. #running-req: 2, #token: 1752, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.85, #queue-req: 0, 
[2025-07-28 01:14:22 DP1 TP0] Decode batch. #running-req: 2, #token: 1832, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.93, #queue-req: 0, 
[2025-07-28 01:14:22 DP1 TP0] Decode batch. #running-req: 2, #token: 1912, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.94, #queue-req: 0, 
[2025-07-28 01:14:22 DP1 TP0] Decode batch. #running-req: 2, #token: 1992, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.94, #queue-req: 0, 
[2025-07-28 01:14:23 DP1 TP0] Decode batch. #running-req: 2, #token: 2072, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.45, #queue-req: 0, 
[2025-07-28 01:14:23 DP1 TP0] Decode batch. #running-req: 2, #token: 2152, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.31, #queue-req: 0, 
[2025-07-28 01:14:23 DP1 TP0] Decode batch. #running-req: 2, #token: 2232, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.24, #queue-req: 0, 
[2025-07-28 01:14:23 DP1 TP0] Decode batch. #running-req: 2, #token: 2312, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.32, #queue-req: 0, 
[2025-07-28 01:14:24 DP1 TP0] Decode batch. #running-req: 2, #token: 2392, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.21, #queue-req: 0, 
[2025-07-28 01:14:24 DP1 TP0] Decode batch. #running-req: 2, #token: 2472, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.26, #queue-req: 0, 
[2025-07-28 01:14:24 DP1 TP0] Decode batch. #running-req: 2, #token: 2552, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.11, #queue-req: 0, 
[2025-07-28 01:14:24] INFO:     127.0.0.1:35618 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:14:24 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 154, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:14:25 DP1 TP0] Decode batch. #running-req: 1, #token: 912, token usage: 0.00, cuda graph: True, gen throughput (token/s): 179.18, #queue-req: 0, 
[2025-07-28 01:14:25 DP0 TP0] Decode batch. #running-req: 1, #token: 264, token usage: 0.00, cuda graph: True, gen throughput (token/s): 7.83, #queue-req: 0, 
[2025-07-28 01:14:25 DP1 TP0] Decode batch. #running-req: 1, #token: 952, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:14:25 DP0 TP0] Decode batch. #running-req: 1, #token: 304, token usage: 0.00, cuda graph: True, gen throughput (token/s): 135.98, #queue-req: 0, 
[2025-07-28 01:14:25 DP1 TP0] Decode batch. #running-req: 1, #token: 992, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:14:25 DP0 TP0] Decode batch. #running-req: 1, #token: 344, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.28, #queue-req: 0, 
[2025-07-28 01:14:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1032, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.30, #queue-req: 0, 
[2025-07-28 01:14:26 DP0 TP0] Decode batch. #running-req: 1, #token: 384, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:14:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1072, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.05, #queue-req: 0, 
[2025-07-28 01:14:26 DP0 TP0] Decode batch. #running-req: 1, #token: 424, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:14:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1112, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.00, #queue-req: 0, 
[2025-07-28 01:14:26 DP0 TP0] Decode batch. #running-req: 1, #token: 464, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.30, #queue-req: 0, 
[2025-07-28 01:14:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1152, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.93, #queue-req: 0, 
[2025-07-28 01:14:26 DP0 TP0] Decode batch. #running-req: 1, #token: 504, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.20, #queue-req: 0, 
[2025-07-28 01:14:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1192, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.92, #queue-req: 0, 
[2025-07-28 01:14:27 DP0 TP0] Decode batch. #running-req: 1, #token: 544, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.13, #queue-req: 0, 
[2025-07-28 01:14:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1232, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.97, #queue-req: 0, 
[2025-07-28 01:14:27 DP0 TP0] Decode batch. #running-req: 1, #token: 584, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.02, #queue-req: 0, 
[2025-07-28 01:14:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1272, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.95, #queue-req: 0, 
[2025-07-28 01:14:27 DP0 TP0] Decode batch. #running-req: 1, #token: 624, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.16, #queue-req: 0, 
[2025-07-28 01:14:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1312, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.92, #queue-req: 0, 
[2025-07-28 01:14:28 DP0 TP0] Decode batch. #running-req: 1, #token: 664, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.10, #queue-req: 0, 
[2025-07-28 01:14:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1352, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.92, #queue-req: 0, 
[2025-07-28 01:14:28 DP0 TP0] Decode batch. #running-req: 1, #token: 704, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.05, #queue-req: 0, 
[2025-07-28 01:14:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1392, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.89, #queue-req: 0, 
[2025-07-28 01:14:28 DP0 TP0] Decode batch. #running-req: 1, #token: 744, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.09, #queue-req: 0, 
[2025-07-28 01:14:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1432, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.66, #queue-req: 0, 
[2025-07-28 01:14:28 DP0 TP0] Decode batch. #running-req: 1, #token: 784, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.92, #queue-req: 0, 
[2025-07-28 01:14:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1472, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.80, #queue-req: 0, 
[2025-07-28 01:14:29 DP0 TP0] Decode batch. #running-req: 1, #token: 824, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.70, #queue-req: 0, 
[2025-07-28 01:14:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1512, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.84, #queue-req: 0, 
[2025-07-28 01:14:29 DP0 TP0] Decode batch. #running-req: 1, #token: 864, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.15, #queue-req: 0, 
[2025-07-28 01:14:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.41, #queue-req: 0, 
[2025-07-28 01:14:29] INFO:     127.0.0.1:35638 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:14:29 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 141, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:14:29 DP0 TP0] Decode batch. #running-req: 1, #token: 904, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.95, #queue-req: 0, 
[2025-07-28 01:14:29] INFO:     127.0.0.1:48470 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:14:29 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 192, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:14:30 DP1 TP0] Decode batch. #running-req: 1, #token: 266, token usage: 0.00, cuda graph: True, gen throughput (token/s): 118.87, #queue-req: 0, 
[2025-07-28 01:14:30 DP0 TP0] Decode batch. #running-req: 1, #token: 295, token usage: 0.00, cuda graph: True, gen throughput (token/s): 120.07, #queue-req: 0, 
[2025-07-28 01:14:30 DP1 TP0] Decode batch. #running-req: 1, #token: 306, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.08, #queue-req: 0, 
[2025-07-28 01:14:30 DP0 TP0] Decode batch. #running-req: 1, #token: 335, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.94, #queue-req: 0, 
[2025-07-28 01:14:30 DP1 TP0] Decode batch. #running-req: 1, #token: 346, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.46, #queue-req: 0, 
[2025-07-28 01:14:30 DP0 TP0] Decode batch. #running-req: 1, #token: 375, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.51, #queue-req: 0, 
[2025-07-28 01:14:30 DP1 TP0] Decode batch. #running-req: 1, #token: 386, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.24, #queue-req: 0, 
[2025-07-28 01:14:30 DP0 TP0] Decode batch. #running-req: 1, #token: 415, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.42, #queue-req: 0, 
[2025-07-28 01:14:31 DP1 TP0] Decode batch. #running-req: 1, #token: 426, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.42, #queue-req: 0, 
[2025-07-28 01:14:31 DP0 TP0] Decode batch. #running-req: 1, #token: 455, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.46, #queue-req: 0, 
[2025-07-28 01:14:31 DP1 TP0] Decode batch. #running-req: 1, #token: 466, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.59, #queue-req: 0, 
[2025-07-28 01:14:31 DP0 TP0] Decode batch. #running-req: 1, #token: 495, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.45, #queue-req: 0, 
[2025-07-28 01:14:31 DP1 TP0] Decode batch. #running-req: 1, #token: 506, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.48, #queue-req: 0, 
[2025-07-28 01:14:31 DP0 TP0] Decode batch. #running-req: 1, #token: 535, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.39, #queue-req: 0, 
[2025-07-28 01:14:32 DP1 TP0] Decode batch. #running-req: 1, #token: 546, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.26, #queue-req: 0, 
[2025-07-28 01:14:32 DP0 TP0] Decode batch. #running-req: 1, #token: 575, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.37, #queue-req: 0, 
[2025-07-28 01:14:32 DP1 TP0] Decode batch. #running-req: 1, #token: 586, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:14:32 DP0 TP0] Decode batch. #running-req: 1, #token: 615, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.38, #queue-req: 0, 
[2025-07-28 01:14:32 DP1 TP0] Decode batch. #running-req: 1, #token: 626, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.26, #queue-req: 0, 
[2025-07-28 01:14:32 DP0 TP0] Decode batch. #running-req: 1, #token: 655, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.39, #queue-req: 0, 
[2025-07-28 01:14:32 DP1 TP0] Decode batch. #running-req: 1, #token: 666, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.43, #queue-req: 0, 
[2025-07-28 01:14:32 DP0 TP0] Decode batch. #running-req: 1, #token: 695, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.39, #queue-req: 0, 
[2025-07-28 01:14:33 DP1 TP0] Decode batch. #running-req: 1, #token: 706, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:14:33 DP0 TP0] Decode batch. #running-req: 1, #token: 735, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:14:33 DP1 TP0] Decode batch. #running-req: 1, #token: 746, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:14:33 DP0 TP0] Decode batch. #running-req: 1, #token: 775, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:14:33 DP1 TP0] Decode batch. #running-req: 1, #token: 786, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:14:33 DP0 TP0] Decode batch. #running-req: 1, #token: 815, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:14:34 DP1 TP0] Decode batch. #running-req: 1, #token: 826, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.19, #queue-req: 0, 
[2025-07-28 01:14:34 DP0 TP0] Decode batch. #running-req: 1, #token: 855, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.99, #queue-req: 0, 
[2025-07-28 01:14:34 DP1 TP0] Decode batch. #running-req: 1, #token: 866, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:14:34 DP0 TP0] Decode batch. #running-req: 1, #token: 895, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.30, #queue-req: 0, 
[2025-07-28 01:14:34 DP1 TP0] Decode batch. #running-req: 1, #token: 906, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:14:34 DP0 TP0] Decode batch. #running-req: 1, #token: 935, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.26, #queue-req: 0, 
[2025-07-28 01:14:34 DP1 TP0] Decode batch. #running-req: 1, #token: 946, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:14:34 DP0 TP0] Decode batch. #running-req: 1, #token: 975, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:14:35 DP1 TP0] Decode batch. #running-req: 1, #token: 986, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.30, #queue-req: 0, 
[2025-07-28 01:14:35 DP0 TP0] Decode batch. #running-req: 1, #token: 1015, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:14:35] INFO:     127.0.0.1:48484 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:14:35 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 190, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:14:35 DP1 TP0] Decode batch. #running-req: 1, #token: 293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 119.65, #queue-req: 0, 
[2025-07-28 01:14:35] INFO:     127.0.0.1:48492 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:14:35 DP0 TP0] Decode batch. #running-req: 1, #token: 0, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.15, #queue-req: 0, 
[2025-07-28 01:14:35 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 137, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:14:35 DP1 TP0] Decode batch. #running-req: 1, #token: 333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.55, #queue-req: 0, 
[2025-07-28 01:14:35 DP0 TP0] Decode batch. #running-req: 1, #token: 265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 119.66, #queue-req: 0, 
[2025-07-28 01:14:36 DP1 TP0] Decode batch. #running-req: 1, #token: 373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.51, #queue-req: 0, 
[2025-07-28 01:14:36 DP0 TP0] Decode batch. #running-req: 1, #token: 305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.21, #queue-req: 0, 
[2025-07-28 01:14:36 DP1 TP0] Decode batch. #running-req: 1, #token: 413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.51, #queue-req: 0, 
[2025-07-28 01:14:36 DP0 TP0] Decode batch. #running-req: 1, #token: 345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:14:36 DP1 TP0] Decode batch. #running-req: 1, #token: 453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.53, #queue-req: 0, 
[2025-07-28 01:14:36 DP0 TP0] Decode batch. #running-req: 1, #token: 385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:14:36 DP1 TP0] Decode batch. #running-req: 1, #token: 493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.58, #queue-req: 0, 
[2025-07-28 01:14:37 DP0 TP0] Decode batch. #running-req: 1, #token: 425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:14:37 DP1 TP0] Decode batch. #running-req: 1, #token: 533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.47, #queue-req: 0, 
[2025-07-28 01:14:37 DP0 TP0] Decode batch. #running-req: 1, #token: 465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.40, #queue-req: 0, 
[2025-07-28 01:14:37 DP1 TP0] Decode batch. #running-req: 1, #token: 573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.43, #queue-req: 0, 
[2025-07-28 01:14:37 DP0 TP0] Decode batch. #running-req: 1, #token: 505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:14:37 DP1 TP0] Decode batch. #running-req: 1, #token: 613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.46, #queue-req: 0, 
[2025-07-28 01:14:37 DP0 TP0] Decode batch. #running-req: 1, #token: 545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.24, #queue-req: 0, 
[2025-07-28 01:14:38 DP1 TP0] Decode batch. #running-req: 1, #token: 653, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.37, #queue-req: 0, 
[2025-07-28 01:14:38 DP0 TP0] Decode batch. #running-req: 1, #token: 585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.26, #queue-req: 0, 
[2025-07-28 01:14:38 DP1 TP0] Decode batch. #running-req: 1, #token: 693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.39, #queue-req: 0, 
[2025-07-28 01:14:38 DP0 TP0] Decode batch. #running-req: 1, #token: 625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.08, #queue-req: 0, 
[2025-07-28 01:14:38 DP1 TP0] Decode batch. #running-req: 1, #token: 733, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:14:38 DP0 TP0] Decode batch. #running-req: 1, #token: 665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.24, #queue-req: 0, 
[2025-07-28 01:14:39 DP1 TP0] Decode batch. #running-req: 1, #token: 773, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.40, #queue-req: 0, 
[2025-07-28 01:14:39 DP0 TP0] Decode batch. #running-req: 1, #token: 705, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.27, #queue-req: 0, 
[2025-07-28 01:14:39 DP1 TP0] Decode batch. #running-req: 1, #token: 813, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.39, #queue-req: 0, 
[2025-07-28 01:14:39 DP0 TP0] Decode batch. #running-req: 1, #token: 745, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.21, #queue-req: 0, 
[2025-07-28 01:14:39 DP1 TP0] Decode batch. #running-req: 1, #token: 853, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.38, #queue-req: 0, 
[2025-07-28 01:14:39 DP0 TP0] Decode batch. #running-req: 1, #token: 785, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.27, #queue-req: 0, 
[2025-07-28 01:14:39 DP1 TP0] Decode batch. #running-req: 1, #token: 893, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:14:39 DP0 TP0] Decode batch. #running-req: 1, #token: 825, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.16, #queue-req: 0, 
[2025-07-28 01:14:40 DP1 TP0] Decode batch. #running-req: 1, #token: 933, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.37, #queue-req: 0, 
[2025-07-28 01:14:40 DP0 TP0] Decode batch. #running-req: 1, #token: 865, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.18, #queue-req: 0, 
[2025-07-28 01:14:40 DP1 TP0] Decode batch. #running-req: 1, #token: 973, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.39, #queue-req: 0, 
[2025-07-28 01:14:40 DP0 TP0] Decode batch. #running-req: 1, #token: 905, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.23, #queue-req: 0, 
[2025-07-28 01:14:40 DP1 TP0] Decode batch. #running-req: 1, #token: 1013, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.40, #queue-req: 0, 
[2025-07-28 01:14:40 DP0 TP0] Decode batch. #running-req: 1, #token: 945, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.23, #queue-req: 0, 
[2025-07-28 01:14:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1053, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.12, #queue-req: 0, 
[2025-07-28 01:14:41 DP0 TP0] Decode batch. #running-req: 1, #token: 985, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.10, #queue-req: 0, 
[2025-07-28 01:14:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1093, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.98, #queue-req: 0, 
[2025-07-28 01:14:41 DP0 TP0] Decode batch. #running-req: 1, #token: 1025, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.23, #queue-req: 0, 
[2025-07-28 01:14:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1133, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.92, #queue-req: 0, 
[2025-07-28 01:14:41 DP0 TP0] Decode batch. #running-req: 1, #token: 1065, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.92, #queue-req: 0, 
[2025-07-28 01:14:41 DP1 TP0] Decode batch. #running-req: 1, #token: 1173, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.01, #queue-req: 0, 
[2025-07-28 01:14:41] INFO:     127.0.0.1:38094 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:14:41 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 132, #cached-token: 89, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:14:41 DP0 TP0] Decode batch. #running-req: 1, #token: 1105, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.87, #queue-req: 0, 
[2025-07-28 01:14:42] INFO:     127.0.0.1:38100 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:14:42 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 199, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:14:42 DP1 TP0] Decode batch. #running-req: 1, #token: 259, token usage: 0.00, cuda graph: True, gen throughput (token/s): 118.72, #queue-req: 0, 
[2025-07-28 01:14:42 DP0 TP0] Decode batch. #running-req: 1, #token: 293, token usage: 0.00, cuda graph: True, gen throughput (token/s): 119.48, #queue-req: 0, 
[2025-07-28 01:14:42 DP1 TP0] Decode batch. #running-req: 1, #token: 299, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:14:42 DP0 TP0] Decode batch. #running-req: 1, #token: 333, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:14:42 DP1 TP0] Decode batch. #running-req: 1, #token: 339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.48, #queue-req: 0, 
[2025-07-28 01:14:42 DP0 TP0] Decode batch. #running-req: 1, #token: 373, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.25, #queue-req: 0, 
[2025-07-28 01:14:43 DP1 TP0] Decode batch. #running-req: 1, #token: 379, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.28, #queue-req: 0, 
[2025-07-28 01:14:43 DP0 TP0] Decode batch. #running-req: 1, #token: 413, token usage: 0.00, cuda graph: True, gen throughput (token/s): 133.61, #queue-req: 0, 
[2025-07-28 01:14:43 DP1 TP0] Decode batch. #running-req: 1, #token: 419, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.47, #queue-req: 0, 
[2025-07-28 01:14:43 DP0 TP0] Decode batch. #running-req: 1, #token: 453, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.81, #queue-req: 0, 
[2025-07-28 01:14:43 DP1 TP0] Decode batch. #running-req: 1, #token: 459, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:14:43 DP0 TP0] Decode batch. #running-req: 1, #token: 493, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.62, #queue-req: 0, 
[2025-07-28 01:14:43 DP1 TP0] Decode batch. #running-req: 1, #token: 499, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.43, #queue-req: 0, 
[2025-07-28 01:14:44 DP0 TP0] Decode batch. #running-req: 1, #token: 533, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.22, #queue-req: 0, 
[2025-07-28 01:14:44 DP1 TP0] Decode batch. #running-req: 1, #token: 539, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.17, #queue-req: 0, 
[2025-07-28 01:14:44 DP0 TP0] Decode batch. #running-req: 1, #token: 573, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.03, #queue-req: 0, 
[2025-07-28 01:14:44 DP1 TP0] Decode batch. #running-req: 1, #token: 579, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:14:44 DP0 TP0] Decode batch. #running-req: 1, #token: 613, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.22, #queue-req: 0, 
[2025-07-28 01:14:44 DP1 TP0] Decode batch. #running-req: 1, #token: 619, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.46, #queue-req: 0, 
[2025-07-28 01:14:44 DP0 TP0] Decode batch. #running-req: 1, #token: 653, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.16, #queue-req: 0, 
[2025-07-28 01:14:45 DP1 TP0] Decode batch. #running-req: 1, #token: 659, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.42, #queue-req: 0, 
[2025-07-28 01:14:45 DP0 TP0] Decode batch. #running-req: 1, #token: 693, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:14:45 DP1 TP0] Decode batch. #running-req: 1, #token: 699, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.45, #queue-req: 0, 
[2025-07-28 01:14:45 DP0 TP0] Decode batch. #running-req: 1, #token: 733, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.27, #queue-req: 0, 
[2025-07-28 01:14:45 DP1 TP0] Decode batch. #running-req: 1, #token: 739, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.52, #queue-req: 0, 
[2025-07-28 01:14:45 DP0 TP0] Decode batch. #running-req: 1, #token: 773, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.02, #queue-req: 0, 
[2025-07-28 01:14:45 DP1 TP0] Decode batch. #running-req: 1, #token: 779, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.40, #queue-req: 0, 
[2025-07-28 01:14:46 DP0 TP0] Decode batch. #running-req: 1, #token: 813, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.25, #queue-req: 0, 
[2025-07-28 01:14:46 DP1 TP0] Decode batch. #running-req: 1, #token: 819, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:14:46] INFO:     127.0.0.1:38102 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:14:46 DP0 TP0] Decode batch. #running-req: 1, #token: 853, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.28, #queue-req: 0, 
[2025-07-28 01:14:46 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 194, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:14:46 DP1 TP0] Decode batch. #running-req: 1, #token: 312, token usage: 0.00, cuda graph: True, gen throughput (token/s): 118.59, #queue-req: 0, 
[2025-07-28 01:14:46 DP0 TP0] Decode batch. #running-req: 1, #token: 893, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.27, #queue-req: 0, 
[2025-07-28 01:14:46] INFO:     127.0.0.1:38110 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:14:46 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 167, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:14:46 DP1 TP0] Decode batch. #running-req: 1, #token: 352, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.54, #queue-req: 0, 
[2025-07-28 01:14:46 DP0 TP0] Decode batch. #running-req: 1, #token: 260, token usage: 0.00, cuda graph: True, gen throughput (token/s): 119.62, #queue-req: 0, 
[2025-07-28 01:14:47 DP1 TP0] Decode batch. #running-req: 1, #token: 392, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.56, #queue-req: 0, 
[2025-07-28 01:14:47 DP0 TP0] Decode batch. #running-req: 1, #token: 300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.42, #queue-req: 0, 
[2025-07-28 01:14:47 DP1 TP0] Decode batch. #running-req: 1, #token: 432, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.55, #queue-req: 0, 
[2025-07-28 01:14:47 DP0 TP0] Decode batch. #running-req: 1, #token: 340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.40, #queue-req: 0, 
[2025-07-28 01:14:47 DP1 TP0] Decode batch. #running-req: 1, #token: 472, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.53, #queue-req: 0, 
[2025-07-28 01:14:47 DP0 TP0] Decode batch. #running-req: 1, #token: 380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.38, #queue-req: 0, 
[2025-07-28 01:14:48 DP1 TP0] Decode batch. #running-req: 1, #token: 512, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.26, #queue-req: 0, 
[2025-07-28 01:14:48 DP0 TP0] Decode batch. #running-req: 1, #token: 420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.07, #queue-req: 0, 
[2025-07-28 01:14:48 DP1 TP0] Decode batch. #running-req: 1, #token: 552, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.25, #queue-req: 0, 
[2025-07-28 01:14:48 DP0 TP0] Decode batch. #running-req: 1, #token: 460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.40, #queue-req: 0, 
[2025-07-28 01:14:48 DP1 TP0] Decode batch. #running-req: 1, #token: 592, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.26, #queue-req: 0, 
[2025-07-28 01:14:48 DP0 TP0] Decode batch. #running-req: 1, #token: 500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.37, #queue-req: 0, 
[2025-07-28 01:14:48 DP1 TP0] Decode batch. #running-req: 1, #token: 632, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.28, #queue-req: 0, 
[2025-07-28 01:14:48 DP0 TP0] Decode batch. #running-req: 1, #token: 540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:14:49 DP1 TP0] Decode batch. #running-req: 1, #token: 672, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.26, #queue-req: 0, 
[2025-07-28 01:14:49 DP0 TP0] Decode batch. #running-req: 1, #token: 580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.25, #queue-req: 0, 
[2025-07-28 01:14:49 DP1 TP0] Decode batch. #running-req: 1, #token: 712, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:14:49 DP0 TP0] Decode batch. #running-req: 1, #token: 620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.18, #queue-req: 0, 
[2025-07-28 01:14:49 DP1 TP0] Decode batch. #running-req: 1, #token: 752, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.38, #queue-req: 0, 
[2025-07-28 01:14:49 DP0 TP0] Decode batch. #running-req: 1, #token: 660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:14:50 DP1 TP0] Decode batch. #running-req: 1, #token: 792, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.37, #queue-req: 0, 
[2025-07-28 01:14:50 DP0 TP0] Decode batch. #running-req: 1, #token: 700, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.21, #queue-req: 0, 
[2025-07-28 01:14:50 DP1 TP0] Decode batch. #running-req: 1, #token: 832, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:14:50 DP0 TP0] Decode batch. #running-req: 1, #token: 740, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.26, #queue-req: 0, 
[2025-07-28 01:14:50 DP1 TP0] Decode batch. #running-req: 1, #token: 872, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:14:50 DP0 TP0] Decode batch. #running-req: 1, #token: 780, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.23, #queue-req: 0, 
[2025-07-28 01:14:50 DP1 TP0] Decode batch. #running-req: 1, #token: 912, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:14:50 DP0 TP0] Decode batch. #running-req: 1, #token: 820, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.25, #queue-req: 0, 
[2025-07-28 01:14:51 DP1 TP0] Decode batch. #running-req: 1, #token: 952, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.37, #queue-req: 0, 
[2025-07-28 01:14:51 DP0 TP0] Decode batch. #running-req: 1, #token: 860, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.26, #queue-req: 0, 
[2025-07-28 01:14:51 DP1 TP0] Decode batch. #running-req: 1, #token: 992, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:14:51 DP0 TP0] Decode batch. #running-req: 1, #token: 900, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:14:51 DP1 TP0] Decode batch. #running-req: 1, #token: 1032, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.21, #queue-req: 0, 
[2025-07-28 01:14:51 DP0 TP0] Decode batch. #running-req: 1, #token: 940, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:14:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1072, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.01, #queue-req: 0, 
[2025-07-28 01:14:52 DP0 TP0] Decode batch. #running-req: 1, #token: 980, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.26, #queue-req: 0, 
[2025-07-28 01:14:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1112, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.00, #queue-req: 0, 
[2025-07-28 01:14:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1020, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.24, #queue-req: 0, 
[2025-07-28 01:14:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1152, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.99, #queue-req: 0, 
[2025-07-28 01:14:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1060, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.93, #queue-req: 0, 
[2025-07-28 01:14:52 DP1 TP0] Decode batch. #running-req: 1, #token: 1192, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.00, #queue-req: 0, 
[2025-07-28 01:14:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1100, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.93, #queue-req: 0, 
[2025-07-28 01:14:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1232, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.95, #queue-req: 0, 
[2025-07-28 01:14:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1140, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.90, #queue-req: 0, 
[2025-07-28 01:14:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1272, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.89, #queue-req: 0, 
[2025-07-28 01:14:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1180, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.93, #queue-req: 0, 
[2025-07-28 01:14:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1312, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.95, #queue-req: 0, 
[2025-07-28 01:14:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1220, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.85, #queue-req: 0, 
[2025-07-28 01:14:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1352, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.67, #queue-req: 0, 
[2025-07-28 01:14:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1260, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.89, #queue-req: 0, 
[2025-07-28 01:14:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1392, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.98, #queue-req: 0, 
[2025-07-28 01:14:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.89, #queue-req: 0, 
[2025-07-28 01:14:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1432, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.95, #queue-req: 0, 
[2025-07-28 01:14:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.82, #queue-req: 0, 
[2025-07-28 01:14:54 DP1 TP0] Decode batch. #running-req: 1, #token: 1472, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.86, #queue-req: 0, 
[2025-07-28 01:14:54 DP0 TP0] Decode batch. #running-req: 1, #token: 1380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.88, #queue-req: 0, 
[2025-07-28 01:14:55 DP1 TP0] Decode batch. #running-req: 1, #token: 1512, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.57, #queue-req: 0, 
[2025-07-28 01:14:55 DP0 TP0] Decode batch. #running-req: 1, #token: 1420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.87, #queue-req: 0, 
[2025-07-28 01:14:55] INFO:     127.0.0.1:37954 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:14:55 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 172, #cached-token: 89, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:14:55 DP1 TP0] Decode batch. #running-req: 2, #token: 1731, token usage: 0.00, cuda graph: True, gen throughput (token/s): 141.18, #queue-req: 0, 
[2025-07-28 01:14:55 DP1 TP0] Decode batch. #running-req: 2, #token: 1811, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.67, #queue-req: 0, 
[2025-07-28 01:14:56 DP1 TP0] Decode batch. #running-req: 2, #token: 1891, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.31, #queue-req: 0, 
[2025-07-28 01:14:56] INFO:     127.0.0.1:37948 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:14:56 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 219, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:14:56 DP1 TP0] Decode batch. #running-req: 1, #token: 388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 218.60, #queue-req: 0, 
[2025-07-28 01:14:56 DP0 TP0] Decode batch. #running-req: 1, #token: 325, token usage: 0.00, cuda graph: True, gen throughput (token/s): 32.26, #queue-req: 0, 
[2025-07-28 01:14:56 DP1 TP0] Decode batch. #running-req: 1, #token: 428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.56, #queue-req: 0, 
[2025-07-28 01:14:56 DP0 TP0] Decode batch. #running-req: 1, #token: 365, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.74, #queue-req: 0, 
[2025-07-28 01:14:56 DP1 TP0] Decode batch. #running-req: 1, #token: 468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.54, #queue-req: 0, 
[2025-07-28 01:14:57 DP0 TP0] Decode batch. #running-req: 1, #token: 405, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.42, #queue-req: 0, 
[2025-07-28 01:14:57 DP1 TP0] Decode batch. #running-req: 1, #token: 508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.52, #queue-req: 0, 
[2025-07-28 01:14:57 DP0 TP0] Decode batch. #running-req: 1, #token: 445, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:14:57 DP1 TP0] Decode batch. #running-req: 1, #token: 548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.45, #queue-req: 0, 
[2025-07-28 01:14:57 DP0 TP0] Decode batch. #running-req: 1, #token: 485, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.46, #queue-req: 0, 
[2025-07-28 01:14:57 DP1 TP0] Decode batch. #running-req: 1, #token: 588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:14:57 DP0 TP0] Decode batch. #running-req: 1, #token: 525, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:14:58 DP1 TP0] Decode batch. #running-req: 1, #token: 628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:14:58 DP0 TP0] Decode batch. #running-req: 1, #token: 565, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:14:58 DP1 TP0] Decode batch. #running-req: 1, #token: 668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.39, #queue-req: 0, 
[2025-07-28 01:14:58 DP0 TP0] Decode batch. #running-req: 1, #token: 605, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:14:58 DP1 TP0] Decode batch. #running-req: 1, #token: 708, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.39, #queue-req: 0, 
[2025-07-28 01:14:58 DP0 TP0] Decode batch. #running-req: 1, #token: 645, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:14:58 DP1 TP0] Decode batch. #running-req: 1, #token: 748, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:14:59 DP0 TP0] Decode batch. #running-req: 1, #token: 685, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:14:59 DP1 TP0] Decode batch. #running-req: 1, #token: 788, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:14:59 DP0 TP0] Decode batch. #running-req: 1, #token: 725, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:14:59 DP1 TP0] Decode batch. #running-req: 1, #token: 828, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:14:59 DP0 TP0] Decode batch. #running-req: 1, #token: 765, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:14:59 DP1 TP0] Decode batch. #running-req: 1, #token: 868, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:14:59 DP0 TP0] Decode batch. #running-req: 1, #token: 805, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.25, #queue-req: 0, 
[2025-07-28 01:15:00 DP1 TP0] Decode batch. #running-req: 1, #token: 908, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:15:00 DP0 TP0] Decode batch. #running-req: 1, #token: 845, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.28, #queue-req: 0, 
[2025-07-28 01:15:00 DP1 TP0] Decode batch. #running-req: 1, #token: 948, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.42, #queue-req: 0, 
[2025-07-28 01:15:00 DP0 TP0] Decode batch. #running-req: 1, #token: 885, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.21, #queue-req: 0, 
[2025-07-28 01:15:00 DP1 TP0] Decode batch. #running-req: 1, #token: 988, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:15:00 DP0 TP0] Decode batch. #running-req: 1, #token: 925, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.24, #queue-req: 0, 
[2025-07-28 01:15:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1028, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.39, #queue-req: 0, 
[2025-07-28 01:15:01 DP0 TP0] Decode batch. #running-req: 1, #token: 965, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.28, #queue-req: 0, 
[2025-07-28 01:15:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1068, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.07, #queue-req: 0, 
[2025-07-28 01:15:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1005, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.27, #queue-req: 0, 
[2025-07-28 01:15:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1108, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.98, #queue-req: 0, 
[2025-07-28 01:15:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1045, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.14, #queue-req: 0, 
[2025-07-28 01:15:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1148, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.00, #queue-req: 0, 
[2025-07-28 01:15:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1085, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.91, #queue-req: 0, 
[2025-07-28 01:15:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1188, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.93, #queue-req: 0, 
[2025-07-28 01:15:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1125, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.98, #queue-req: 0, 
[2025-07-28 01:15:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1228, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.88, #queue-req: 0, 
[2025-07-28 01:15:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1165, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.88, #queue-req: 0, 
[2025-07-28 01:15:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.15, #queue-req: 0, 
[2025-07-28 01:15:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1205, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.78, #queue-req: 0, 
[2025-07-28 01:15:03 DP1 TP0] Decode batch. #running-req: 1, #token: 1308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.29, #queue-req: 0, 
[2025-07-28 01:15:03 DP0 TP0] Decode batch. #running-req: 1, #token: 1245, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.80, #queue-req: 0, 
[2025-07-28 01:15:03] INFO:     127.0.0.1:57356 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:15:03 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 155, #cached-token: 90, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:15:03 DP1 TP0] Decode batch. #running-req: 1, #token: 269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 118.49, #queue-req: 0, 
[2025-07-28 01:15:03 DP0 TP0] Decode batch. #running-req: 1, #token: 1285, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.79, #queue-req: 0, 
[2025-07-28 01:15:03 DP1 TP0] Decode batch. #running-req: 1, #token: 309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.11, #queue-req: 0, 
[2025-07-28 01:15:03 DP0 TP0] Decode batch. #running-req: 1, #token: 1325, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.69, #queue-req: 0, 
[2025-07-28 01:15:03 DP1 TP0] Decode batch. #running-req: 1, #token: 349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.56, #queue-req: 0, 
[2025-07-28 01:15:03 DP0 TP0] Decode batch. #running-req: 1, #token: 1365, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.95, #queue-req: 0, 
[2025-07-28 01:15:04 DP1 TP0] Decode batch. #running-req: 1, #token: 389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.63, #queue-req: 0, 
[2025-07-28 01:15:04 DP0 TP0] Decode batch. #running-req: 1, #token: 1405, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.90, #queue-req: 0, 
[2025-07-28 01:15:04 DP1 TP0] Decode batch. #running-req: 1, #token: 429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.53, #queue-req: 0, 
[2025-07-28 01:15:04 DP0 TP0] Decode batch. #running-req: 1, #token: 1445, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.94, #queue-req: 0, 
[2025-07-28 01:15:04 DP1 TP0] Decode batch. #running-req: 1, #token: 469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.59, #queue-req: 0, 
[2025-07-28 01:15:04 DP0 TP0] Decode batch. #running-req: 1, #token: 1485, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.91, #queue-req: 0, 
[2025-07-28 01:15:05 DP1 TP0] Decode batch. #running-req: 1, #token: 509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.47, #queue-req: 0, 
[2025-07-28 01:15:05 DP0 TP0] Decode batch. #running-req: 1, #token: 1525, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.84, #queue-req: 0, 
[2025-07-28 01:15:05 DP1 TP0] Decode batch. #running-req: 1, #token: 549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.48, #queue-req: 0, 
[2025-07-28 01:15:05 DP0 TP0] Decode batch. #running-req: 1, #token: 1565, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.79, #queue-req: 0, 
[2025-07-28 01:15:05 DP1 TP0] Decode batch. #running-req: 1, #token: 589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.47, #queue-req: 0, 
[2025-07-28 01:15:05 DP0 TP0] Decode batch. #running-req: 1, #token: 1605, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.53, #queue-req: 0, 
[2025-07-28 01:15:05 DP1 TP0] Decode batch. #running-req: 1, #token: 629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:15:05 DP0 TP0] Decode batch. #running-req: 1, #token: 1645, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.73, #queue-req: 0, 
[2025-07-28 01:15:06 DP1 TP0] Decode batch. #running-req: 1, #token: 669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:15:06 DP0 TP0] Decode batch. #running-req: 1, #token: 1685, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.78, #queue-req: 0, 
[2025-07-28 01:15:06] INFO:     127.0.0.1:57364 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:15:06 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 175, #cached-token: 89, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:15:06 DP1 TP0] Decode batch. #running-req: 1, #token: 709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.39, #queue-req: 0, 
[2025-07-28 01:15:06 DP0 TP0] Decode batch. #running-req: 1, #token: 286, token usage: 0.00, cuda graph: True, gen throughput (token/s): 119.59, #queue-req: 0, 
[2025-07-28 01:15:06 DP1 TP0] Decode batch. #running-req: 1, #token: 749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.27, #queue-req: 0, 
[2025-07-28 01:15:06 DP0 TP0] Decode batch. #running-req: 1, #token: 326, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.50, #queue-req: 0, 
[2025-07-28 01:15:07 DP1 TP0] Decode batch. #running-req: 1, #token: 789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.43, #queue-req: 0, 
[2025-07-28 01:15:07 DP0 TP0] Decode batch. #running-req: 1, #token: 366, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.50, #queue-req: 0, 
[2025-07-28 01:15:07 DP1 TP0] Decode batch. #running-req: 1, #token: 829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:15:07 DP0 TP0] Decode batch. #running-req: 1, #token: 406, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.46, #queue-req: 0, 
[2025-07-28 01:15:07 DP1 TP0] Decode batch. #running-req: 1, #token: 869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:15:07 DP0 TP0] Decode batch. #running-req: 1, #token: 446, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:15:07 DP1 TP0] Decode batch. #running-req: 1, #token: 909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:15:08 DP0 TP0] Decode batch. #running-req: 1, #token: 486, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.13, #queue-req: 0, 
[2025-07-28 01:15:08 DP1 TP0] Decode batch. #running-req: 1, #token: 949, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:15:08 DP0 TP0] Decode batch. #running-req: 1, #token: 526, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.39, #queue-req: 0, 
[2025-07-28 01:15:08 DP1 TP0] Decode batch. #running-req: 1, #token: 989, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:15:08 DP0 TP0] Decode batch. #running-req: 1, #token: 566, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:15:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1029, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:15:08 DP0 TP0] Decode batch. #running-req: 1, #token: 606, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.31, #queue-req: 0, 
[2025-07-28 01:15:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1069, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.05, #queue-req: 0, 
[2025-07-28 01:15:09 DP0 TP0] Decode batch. #running-req: 1, #token: 646, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.87, #queue-req: 0, 
[2025-07-28 01:15:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1109, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.99, #queue-req: 0, 
[2025-07-28 01:15:09 DP0 TP0] Decode batch. #running-req: 1, #token: 686, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:15:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1149, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.77, #queue-req: 0, 
[2025-07-28 01:15:09 DP0 TP0] Decode batch. #running-req: 1, #token: 726, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.13, #queue-req: 0, 
[2025-07-28 01:15:09 DP1 TP0] Decode batch. #running-req: 1, #token: 1189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.87, #queue-req: 0, 
[2025-07-28 01:15:10 DP0 TP0] Decode batch. #running-req: 1, #token: 766, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:15:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.41, #queue-req: 0, 
[2025-07-28 01:15:10 DP0 TP0] Decode batch. #running-req: 1, #token: 806, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:15:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.81, #queue-req: 0, 
[2025-07-28 01:15:10 DP0 TP0] Decode batch. #running-req: 1, #token: 846, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.30, #queue-req: 0, 
[2025-07-28 01:15:10 DP1 TP0] Decode batch. #running-req: 1, #token: 1309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.14, #queue-req: 0, 
[2025-07-28 01:15:10 DP0 TP0] Decode batch. #running-req: 1, #token: 886, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:15:11 DP1 TP0] Decode batch. #running-req: 1, #token: 1349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.30, #queue-req: 0, 
[2025-07-28 01:15:11 DP0 TP0] Decode batch. #running-req: 1, #token: 926, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:15:11 DP1 TP0] Decode batch. #running-req: 1, #token: 1389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.61, #queue-req: 0, 
[2025-07-28 01:15:11] INFO:     127.0.0.1:48778 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:15:11 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 94, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:15:11 DP0 TP0] Decode batch. #running-req: 1, #token: 966, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:15:11 DP1 TP0] Decode batch. #running-req: 1, #token: 208, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.29, #queue-req: 0, 
[2025-07-28 01:15:11 DP0 TP0] Decode batch. #running-req: 1, #token: 1006, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:15:12 DP1 TP0] Decode batch. #running-req: 1, #token: 248, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.90, #queue-req: 0, 
[2025-07-28 01:15:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1046, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.13, #queue-req: 0, 
[2025-07-28 01:15:12 DP1 TP0] Decode batch. #running-req: 1, #token: 288, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.50, #queue-req: 0, 
[2025-07-28 01:15:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1086, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.04, #queue-req: 0, 
[2025-07-28 01:15:12 DP1 TP0] Decode batch. #running-req: 1, #token: 328, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.52, #queue-req: 0, 
[2025-07-28 01:15:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1126, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.96, #queue-req: 0, 
[2025-07-28 01:15:12 DP1 TP0] Decode batch. #running-req: 1, #token: 368, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.45, #queue-req: 0, 
[2025-07-28 01:15:12 DP0 TP0] Decode batch. #running-req: 1, #token: 1166, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.73, #queue-req: 0, 
[2025-07-28 01:15:13 DP1 TP0] Decode batch. #running-req: 1, #token: 408, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.58, #queue-req: 0, 
[2025-07-28 01:15:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1206, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.95, #queue-req: 0, 
[2025-07-28 01:15:13 DP1 TP0] Decode batch. #running-req: 1, #token: 448, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.56, #queue-req: 0, 
[2025-07-28 01:15:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1246, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.95, #queue-req: 0, 
[2025-07-28 01:15:13 DP1 TP0] Decode batch. #running-req: 1, #token: 488, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.55, #queue-req: 0, 
[2025-07-28 01:15:13 DP0 TP0] Decode batch. #running-req: 1, #token: 1286, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.95, #queue-req: 0, 
[2025-07-28 01:15:14 DP1 TP0] Decode batch. #running-req: 1, #token: 528, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.54, #queue-req: 0, 
[2025-07-28 01:15:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1326, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.95, #queue-req: 0, 
[2025-07-28 01:15:14 DP1 TP0] Decode batch. #running-req: 1, #token: 568, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.42, #queue-req: 0, 
[2025-07-28 01:15:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1366, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.93, #queue-req: 0, 
[2025-07-28 01:15:14 DP1 TP0] Decode batch. #running-req: 1, #token: 608, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:15:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1406, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.94, #queue-req: 0, 
[2025-07-28 01:15:14 DP1 TP0] Decode batch. #running-req: 1, #token: 648, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.46, #queue-req: 0, 
[2025-07-28 01:15:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1446, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.89, #queue-req: 0, 
[2025-07-28 01:15:15 DP1 TP0] Decode batch. #running-req: 1, #token: 688, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.47, #queue-req: 0, 
[2025-07-28 01:15:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1486, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.92, #queue-req: 0, 
[2025-07-28 01:15:15 DP1 TP0] Decode batch. #running-req: 1, #token: 728, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.46, #queue-req: 0, 
[2025-07-28 01:15:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1526, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.86, #queue-req: 0, 
[2025-07-28 01:15:15 DP1 TP0] Decode batch. #running-req: 1, #token: 768, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.42, #queue-req: 0, 
[2025-07-28 01:15:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1566, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.83, #queue-req: 0, 
[2025-07-28 01:15:16 DP1 TP0] Decode batch. #running-req: 1, #token: 808, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.39, #queue-req: 0, 
[2025-07-28 01:15:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1606, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.82, #queue-req: 0, 
[2025-07-28 01:15:16 DP1 TP0] Decode batch. #running-req: 1, #token: 848, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:15:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1646, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.79, #queue-req: 0, 
[2025-07-28 01:15:16 DP1 TP0] Decode batch. #running-req: 1, #token: 888, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.39, #queue-req: 0, 
[2025-07-28 01:15:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1686, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.75, #queue-req: 0, 
[2025-07-28 01:15:16 DP1 TP0] Decode batch. #running-req: 1, #token: 928, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.43, #queue-req: 0, 
[2025-07-28 01:15:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1726, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.79, #queue-req: 0, 
[2025-07-28 01:15:17 DP1 TP0] Decode batch. #running-req: 1, #token: 968, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.45, #queue-req: 0, 
[2025-07-28 01:15:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1766, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.86, #queue-req: 0, 
[2025-07-28 01:15:17 DP1 TP0] Decode batch. #running-req: 1, #token: 1008, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.47, #queue-req: 0, 
[2025-07-28 01:15:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1806, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.77, #queue-req: 0, 
[2025-07-28 01:15:17 DP1 TP0] Decode batch. #running-req: 1, #token: 1048, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.26, #queue-req: 0, 
[2025-07-28 01:15:17] INFO:     127.0.0.1:48782 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:15:17 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 115, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:15:17 DP0 TP0] Decode batch. #running-req: 1, #token: 204, token usage: 0.00, cuda graph: True, gen throughput (token/s): 119.79, #queue-req: 0, 
[2025-07-28 01:15:18 DP1 TP0] Decode batch. #running-req: 1, #token: 1088, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.13, #queue-req: 0, 
[2025-07-28 01:15:18 DP0 TP0] Decode batch. #running-req: 1, #token: 244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.57, #queue-req: 0, 
[2025-07-28 01:15:18] INFO:     127.0.0.1:48798 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:15:18 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 150, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:15:18 DP1 TP0] Decode batch. #running-req: 1, #token: 249, token usage: 0.00, cuda graph: True, gen throughput (token/s): 119.78, #queue-req: 0, 
[2025-07-28 01:15:18 DP0 TP0] Decode batch. #running-req: 1, #token: 284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.16, #queue-req: 0, 
[2025-07-28 01:15:18 DP1 TP0] Decode batch. #running-req: 1, #token: 289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.54, #queue-req: 0, 
[2025-07-28 01:15:18 DP0 TP0] Decode batch. #running-req: 1, #token: 324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.51, #queue-req: 0, 
[2025-07-28 01:15:18 DP1 TP0] Decode batch. #running-req: 1, #token: 329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.58, #queue-req: 0, 
[2025-07-28 01:15:19 DP0 TP0] Decode batch. #running-req: 1, #token: 364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.48, #queue-req: 0, 
[2025-07-28 01:15:19 DP1 TP0] Decode batch. #running-req: 1, #token: 369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.50, #queue-req: 0, 
[2025-07-28 01:15:19 DP0 TP0] Decode batch. #running-req: 1, #token: 404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.46, #queue-req: 0, 
[2025-07-28 01:15:19 DP1 TP0] Decode batch. #running-req: 1, #token: 409, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.51, #queue-req: 0, 
[2025-07-28 01:15:19 DP0 TP0] Decode batch. #running-req: 1, #token: 444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.49, #queue-req: 0, 
[2025-07-28 01:15:19 DP1 TP0] Decode batch. #running-req: 1, #token: 449, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.50, #queue-req: 0, 
[2025-07-28 01:15:19 DP0 TP0] Decode batch. #running-req: 1, #token: 484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.46, #queue-req: 0, 
[2025-07-28 01:15:20 DP1 TP0] Decode batch. #running-req: 1, #token: 489, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.55, #queue-req: 0, 
[2025-07-28 01:15:20 DP0 TP0] Decode batch. #running-req: 1, #token: 524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:15:20 DP1 TP0] Decode batch. #running-req: 1, #token: 529, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.50, #queue-req: 0, 
[2025-07-28 01:15:20 DP0 TP0] Decode batch. #running-req: 1, #token: 564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:15:20 DP1 TP0] Decode batch. #running-req: 1, #token: 569, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:15:20 DP0 TP0] Decode batch. #running-req: 1, #token: 604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:15:20 DP1 TP0] Decode batch. #running-req: 1, #token: 609, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:15:21 DP0 TP0] Decode batch. #running-req: 1, #token: 644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.28, #queue-req: 0, 
[2025-07-28 01:15:21 DP1 TP0] Decode batch. #running-req: 1, #token: 649, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:15:21 DP0 TP0] Decode batch. #running-req: 1, #token: 684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.28, #queue-req: 0, 
[2025-07-28 01:15:21 DP1 TP0] Decode batch. #running-req: 1, #token: 689, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.26, #queue-req: 0, 
[2025-07-28 01:15:21 DP0 TP0] Decode batch. #running-req: 1, #token: 724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.25, #queue-req: 0, 
[2025-07-28 01:15:21 DP1 TP0] Decode batch. #running-req: 1, #token: 729, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.39, #queue-req: 0, 
[2025-07-28 01:15:21 DP0 TP0] Decode batch. #running-req: 1, #token: 764, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:15:22 DP1 TP0] Decode batch. #running-req: 1, #token: 769, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:15:22 DP0 TP0] Decode batch. #running-req: 1, #token: 804, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.28, #queue-req: 0, 
[2025-07-28 01:15:22 DP1 TP0] Decode batch. #running-req: 1, #token: 809, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:15:22 DP0 TP0] Decode batch. #running-req: 1, #token: 844, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.24, #queue-req: 0, 
[2025-07-28 01:15:22 DP1 TP0] Decode batch. #running-req: 1, #token: 849, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:15:22 DP0 TP0] Decode batch. #running-req: 1, #token: 884, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.15, #queue-req: 0, 
[2025-07-28 01:15:22 DP1 TP0] Decode batch. #running-req: 1, #token: 889, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:15:23 DP0 TP0] Decode batch. #running-req: 1, #token: 924, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.23, #queue-req: 0, 
[2025-07-28 01:15:23 DP1 TP0] Decode batch. #running-req: 1, #token: 929, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.38, #queue-req: 0, 
[2025-07-28 01:15:23 DP0 TP0] Decode batch. #running-req: 1, #token: 964, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:15:23 DP1 TP0] Decode batch. #running-req: 1, #token: 969, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:15:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1004, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:15:23 DP1 TP0] Decode batch. #running-req: 1, #token: 1009, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:15:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1044, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.08, #queue-req: 0, 
[2025-07-28 01:15:24] INFO:     127.0.0.1:54386 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:15:24 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 148, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:15:24 DP1 TP0] Decode batch. #running-req: 1, #token: 1049, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.15, #queue-req: 0, 
[2025-07-28 01:15:24 DP0 TP0] Decode batch. #running-req: 1, #token: 262, token usage: 0.00, cuda graph: True, gen throughput (token/s): 119.80, #queue-req: 0, 
[2025-07-28 01:15:24 DP1 TP0] Decode batch. #running-req: 1, #token: 1089, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.01, #queue-req: 0, 
[2025-07-28 01:15:24 DP0 TP0] Decode batch. #running-req: 1, #token: 302, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.52, #queue-req: 0, 
[2025-07-28 01:15:24 DP1 TP0] Decode batch. #running-req: 1, #token: 1129, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.05, #queue-req: 0, 
[2025-07-28 01:15:24 DP0 TP0] Decode batch. #running-req: 1, #token: 342, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.48, #queue-req: 0, 
[2025-07-28 01:15:24 DP1 TP0] Decode batch. #running-req: 1, #token: 1169, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.03, #queue-req: 0, 
[2025-07-28 01:15:25 DP0 TP0] Decode batch. #running-req: 1, #token: 382, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.47, #queue-req: 0, 
[2025-07-28 01:15:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1209, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.98, #queue-req: 0, 
[2025-07-28 01:15:25 DP0 TP0] Decode batch. #running-req: 1, #token: 422, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:15:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1249, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.98, #queue-req: 0, 
[2025-07-28 01:15:25 DP0 TP0] Decode batch. #running-req: 1, #token: 462, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.48, #queue-req: 0, 
[2025-07-28 01:15:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1289, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.01, #queue-req: 0, 
[2025-07-28 01:15:25 DP0 TP0] Decode batch. #running-req: 1, #token: 502, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:15:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1329, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.01, #queue-req: 0, 
[2025-07-28 01:15:26 DP0 TP0] Decode batch. #running-req: 1, #token: 542, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:15:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1369, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.96, #queue-req: 0, 
[2025-07-28 01:15:26 DP0 TP0] Decode batch. #running-req: 1, #token: 582, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:15:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.60, #queue-req: 0, 
[2025-07-28 01:15:26] INFO:     127.0.0.1:54396 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:15:26 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 160, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:15:26 DP0 TP0] Decode batch. #running-req: 1, #token: 622, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:15:27 DP1 TP0] Decode batch. #running-req: 1, #token: 287, token usage: 0.00, cuda graph: True, gen throughput (token/s): 120.02, #queue-req: 0, 
[2025-07-28 01:15:27 DP0 TP0] Decode batch. #running-req: 1, #token: 662, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:15:27 DP1 TP0] Decode batch. #running-req: 1, #token: 327, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.56, #queue-req: 0, 
[2025-07-28 01:15:27 DP0 TP0] Decode batch. #running-req: 1, #token: 702, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.26, #queue-req: 0, 
[2025-07-28 01:15:27 DP1 TP0] Decode batch. #running-req: 1, #token: 367, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.55, #queue-req: 0, 
[2025-07-28 01:15:27 DP0 TP0] Decode batch. #running-req: 1, #token: 742, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.30, #queue-req: 0, 
[2025-07-28 01:15:27 DP1 TP0] Decode batch. #running-req: 1, #token: 407, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.45, #queue-req: 0, 
[2025-07-28 01:15:27 DP0 TP0] Decode batch. #running-req: 1, #token: 782, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.30, #queue-req: 0, 
[2025-07-28 01:15:28 DP1 TP0] Decode batch. #running-req: 1, #token: 447, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.40, #queue-req: 0, 
[2025-07-28 01:15:28 DP0 TP0] Decode batch. #running-req: 1, #token: 822, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:15:28 DP1 TP0] Decode batch. #running-req: 1, #token: 487, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.56, #queue-req: 0, 
[2025-07-28 01:15:28 DP0 TP0] Decode batch. #running-req: 1, #token: 862, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.26, #queue-req: 0, 
[2025-07-28 01:15:28 DP1 TP0] Decode batch. #running-req: 1, #token: 527, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.42, #queue-req: 0, 
[2025-07-28 01:15:28 DP0 TP0] Decode batch. #running-req: 1, #token: 902, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.20, #queue-req: 0, 
[2025-07-28 01:15:29 DP1 TP0] Decode batch. #running-req: 1, #token: 567, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:15:29 DP0 TP0] Decode batch. #running-req: 1, #token: 942, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:15:29 DP1 TP0] Decode batch. #running-req: 1, #token: 607, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:15:29 DP0 TP0] Decode batch. #running-req: 1, #token: 982, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:15:29 DP1 TP0] Decode batch. #running-req: 1, #token: 647, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.15, #queue-req: 0, 
[2025-07-28 01:15:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1022, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.30, #queue-req: 0, 
[2025-07-28 01:15:29 DP1 TP0] Decode batch. #running-req: 1, #token: 687, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.43, #queue-req: 0, 
[2025-07-28 01:15:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1062, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.97, #queue-req: 0, 
[2025-07-28 01:15:30 DP1 TP0] Decode batch. #running-req: 1, #token: 727, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.38, #queue-req: 0, 
[2025-07-28 01:15:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1102, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.98, #queue-req: 0, 
[2025-07-28 01:15:30 DP1 TP0] Decode batch. #running-req: 1, #token: 767, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:15:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1142, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.95, #queue-req: 0, 
[2025-07-28 01:15:30 DP1 TP0] Decode batch. #running-req: 1, #token: 807, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:15:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1182, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.96, #queue-req: 0, 
[2025-07-28 01:15:31 DP1 TP0] Decode batch. #running-req: 1, #token: 847, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.30, #queue-req: 0, 
[2025-07-28 01:15:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1222, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.87, #queue-req: 0, 
[2025-07-28 01:15:31 DP1 TP0] Decode batch. #running-req: 1, #token: 887, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.37, #queue-req: 0, 
[2025-07-28 01:15:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1262, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.92, #queue-req: 0, 
[2025-07-28 01:15:31 DP1 TP0] Decode batch. #running-req: 1, #token: 927, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:15:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1302, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.85, #queue-req: 0, 
[2025-07-28 01:15:31 DP1 TP0] Decode batch. #running-req: 1, #token: 967, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:15:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1342, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.90, #queue-req: 0, 
[2025-07-28 01:15:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1007, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:15:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1382, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.93, #queue-req: 0, 
[2025-07-28 01:15:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1047, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.07, #queue-req: 0, 
[2025-07-28 01:15:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1422, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.86, #queue-req: 0, 
[2025-07-28 01:15:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1087, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.10, #queue-req: 0, 
[2025-07-28 01:15:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1462, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.82, #queue-req: 0, 
[2025-07-28 01:15:33 DP1 TP0] Decode batch. #running-req: 1, #token: 1127, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.97, #queue-req: 0, 
[2025-07-28 01:15:33 DP0 TP0] Decode batch. #running-req: 1, #token: 1502, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.88, #queue-req: 0, 
[2025-07-28 01:15:33 DP1 TP0] Decode batch. #running-req: 1, #token: 1167, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.97, #queue-req: 0, 
[2025-07-28 01:15:33 DP0 TP0] Decode batch. #running-req: 1, #token: 1542, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.85, #queue-req: 0, 
[2025-07-28 01:15:33 DP1 TP0] Decode batch. #running-req: 1, #token: 1207, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.93, #queue-req: 0, 
[2025-07-28 01:15:33 DP0 TP0] Decode batch. #running-req: 1, #token: 1582, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.73, #queue-req: 0, 
[2025-07-28 01:15:33] INFO:     127.0.0.1:37210 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:15:33 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 114, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:15:33 DP1 TP0] Decode batch. #running-req: 1, #token: 1247, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.86, #queue-req: 0, 
[2025-07-28 01:15:34 DP0 TP0] Decode batch. #running-req: 1, #token: 236, token usage: 0.00, cuda graph: True, gen throughput (token/s): 120.43, #queue-req: 0, 
[2025-07-28 01:15:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1287, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.08, #queue-req: 0, 
[2025-07-28 01:15:34 DP0 TP0] Decode batch. #running-req: 1, #token: 276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.46, #queue-req: 0, 
[2025-07-28 01:15:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1327, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.95, #queue-req: 0, 
[2025-07-28 01:15:34 DP0 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.49, #queue-req: 0, 
[2025-07-28 01:15:34 DP1 TP0] Decode batch. #running-req: 1, #token: 1367, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.97, #queue-req: 0, 
[2025-07-28 01:15:34 DP0 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.48, #queue-req: 0, 
[2025-07-28 01:15:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1407, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.93, #queue-req: 0, 
[2025-07-28 01:15:35 DP0 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:15:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1447, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.80, #queue-req: 0, 
[2025-07-28 01:15:35 DP0 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.46, #queue-req: 0, 
[2025-07-28 01:15:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1487, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.01, #queue-req: 0, 
[2025-07-28 01:15:35 DP0 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.38, #queue-req: 0, 
[2025-07-28 01:15:35 DP1 TP0] Decode batch. #running-req: 1, #token: 1527, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.83, #queue-req: 0, 
[2025-07-28 01:15:36 DP0 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.58, #queue-req: 0, 
[2025-07-28 01:15:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1567, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.79, #queue-req: 0, 
[2025-07-28 01:15:36 DP0 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.23, #queue-req: 0, 
[2025-07-28 01:15:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1607, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.79, #queue-req: 0, 
[2025-07-28 01:15:36 DP0 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:15:36 DP1 TP0] Decode batch. #running-req: 1, #token: 1647, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.70, #queue-req: 0, 
[2025-07-28 01:15:36 DP0 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.30, #queue-req: 0, 
[2025-07-28 01:15:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1687, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.87, #queue-req: 0, 
[2025-07-28 01:15:37 DP0 TP0] Decode batch. #running-req: 1, #token: 676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.27, #queue-req: 0, 
[2025-07-28 01:15:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1727, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.79, #queue-req: 0, 
[2025-07-28 01:15:37 DP0 TP0] Decode batch. #running-req: 1, #token: 716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.26, #queue-req: 0, 
[2025-07-28 01:15:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1767, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.76, #queue-req: 0, 
[2025-07-28 01:15:37 DP0 TP0] Decode batch. #running-req: 1, #token: 756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:15:37 DP1 TP0] Decode batch. #running-req: 1, #token: 1807, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.82, #queue-req: 0, 
[2025-07-28 01:15:38 DP0 TP0] Decode batch. #running-req: 1, #token: 796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.28, #queue-req: 0, 
[2025-07-28 01:15:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1847, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.67, #queue-req: 0, 
[2025-07-28 01:15:38 DP0 TP0] Decode batch. #running-req: 1, #token: 836, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.17, #queue-req: 0, 
[2025-07-28 01:15:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1887, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.85, #queue-req: 0, 
[2025-07-28 01:15:38 DP0 TP0] Decode batch. #running-req: 1, #token: 876, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.26, #queue-req: 0, 
[2025-07-28 01:15:38 DP1 TP0] Decode batch. #running-req: 1, #token: 1927, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.76, #queue-req: 0, 
[2025-07-28 01:15:38 DP0 TP0] Decode batch. #running-req: 1, #token: 916, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.30, #queue-req: 0, 
[2025-07-28 01:15:39 DP1 TP0] Decode batch. #running-req: 1, #token: 1967, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.84, #queue-req: 0, 
[2025-07-28 01:15:39 DP0 TP0] Decode batch. #running-req: 1, #token: 956, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.26, #queue-req: 0, 
[2025-07-28 01:15:39 DP1 TP0] Decode batch. #running-req: 1, #token: 2007, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.83, #queue-req: 0, 
[2025-07-28 01:15:39 DP0 TP0] Decode batch. #running-req: 1, #token: 996, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.30, #queue-req: 0, 
[2025-07-28 01:15:39 DP1 TP0] Decode batch. #running-req: 1, #token: 2047, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.70, #queue-req: 0, 
[2025-07-28 01:15:39 DP0 TP0] Decode batch. #running-req: 1, #token: 1036, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.14, #queue-req: 0, 
[2025-07-28 01:15:39 DP1 TP0] Decode batch. #running-req: 1, #token: 2087, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.64, #queue-req: 0, 
[2025-07-28 01:15:40 DP0 TP0] Decode batch. #running-req: 1, #token: 1076, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.93, #queue-req: 0, 
[2025-07-28 01:15:40 DP1 TP0] Decode batch. #running-req: 1, #token: 2127, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.54, #queue-req: 0, 
[2025-07-28 01:15:40 DP0 TP0] Decode batch. #running-req: 1, #token: 1116, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.95, #queue-req: 0, 
[2025-07-28 01:15:40 DP1 TP0] Decode batch. #running-req: 1, #token: 2167, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.57, #queue-req: 0, 
[2025-07-28 01:15:40 DP0 TP0] Decode batch. #running-req: 1, #token: 1156, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.95, #queue-req: 0, 
[2025-07-28 01:15:40 DP1 TP0] Decode batch. #running-req: 1, #token: 2207, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.52, #queue-req: 0, 
[2025-07-28 01:15:40 DP0 TP0] Decode batch. #running-req: 1, #token: 1196, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.86, #queue-req: 0, 
[2025-07-28 01:15:41 DP1 TP0] Decode batch. #running-req: 1, #token: 2247, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.39, #queue-req: 0, 
[2025-07-28 01:15:41 DP0 TP0] Decode batch. #running-req: 1, #token: 1236, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.88, #queue-req: 0, 
[2025-07-28 01:15:41 DP1 TP0] Decode batch. #running-req: 1, #token: 2287, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.51, #queue-req: 0, 
[2025-07-28 01:15:41 DP0 TP0] Decode batch. #running-req: 1, #token: 1276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.92, #queue-req: 0, 
[2025-07-28 01:15:41 DP1 TP0] Decode batch. #running-req: 1, #token: 2327, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.46, #queue-req: 0, 
[2025-07-28 01:15:41] INFO:     127.0.0.1:36984 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:15:41 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 80, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:15:42 DP1 TP0] Decode batch. #running-req: 2, #token: 2476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 205.73, #queue-req: 0, 
[2025-07-28 01:15:42 DP1 TP0] Decode batch. #running-req: 2, #token: 2556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.73, #queue-req: 0, 
[2025-07-28 01:15:42] INFO:     127.0.0.1:37226 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:15:42 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 136, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:15:42 DP0 TP0] Decode batch. #running-req: 1, #token: 226, token usage: 0.00, cuda graph: True, gen throughput (token/s): 42.78, #queue-req: 0, 
[2025-07-28 01:15:42 DP1 TP0] Decode batch. #running-req: 1, #token: 274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 145.99, #queue-req: 0, 
[2025-07-28 01:15:42 DP0 TP0] Decode batch. #running-req: 1, #token: 266, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.86, #queue-req: 0, 
[2025-07-28 01:15:42 DP1 TP0] Decode batch. #running-req: 1, #token: 314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.56, #queue-req: 0, 
[2025-07-28 01:15:43 DP0 TP0] Decode batch. #running-req: 1, #token: 306, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.43, #queue-req: 0, 
[2025-07-28 01:15:43 DP1 TP0] Decode batch. #running-req: 1, #token: 354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.52, #queue-req: 0, 
[2025-07-28 01:15:43 DP0 TP0] Decode batch. #running-req: 1, #token: 346, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.49, #queue-req: 0, 
[2025-07-28 01:15:43 DP1 TP0] Decode batch. #running-req: 1, #token: 394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.38, #queue-req: 0, 
[2025-07-28 01:15:43 DP0 TP0] Decode batch. #running-req: 1, #token: 386, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.51, #queue-req: 0, 
[2025-07-28 01:15:43 DP1 TP0] Decode batch. #running-req: 1, #token: 434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.43, #queue-req: 0, 
[2025-07-28 01:15:43 DP0 TP0] Decode batch. #running-req: 1, #token: 426, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:15:44 DP1 TP0] Decode batch. #running-req: 1, #token: 474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.49, #queue-req: 0, 
[2025-07-28 01:15:44 DP0 TP0] Decode batch. #running-req: 1, #token: 466, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:15:44 DP1 TP0] Decode batch. #running-req: 1, #token: 514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.51, #queue-req: 0, 
[2025-07-28 01:15:44 DP0 TP0] Decode batch. #running-req: 1, #token: 506, token usage: 0.00, cuda graph: True, gen throughput (token/s): 134.11, #queue-req: 0, 
[2025-07-28 01:15:44 DP1 TP0] Decode batch. #running-req: 1, #token: 554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:15:44 DP0 TP0] Decode batch. #running-req: 1, #token: 546, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.37, #queue-req: 0, 
[2025-07-28 01:15:44 DP1 TP0] Decode batch. #running-req: 1, #token: 594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.43, #queue-req: 0, 
[2025-07-28 01:15:45 DP0 TP0] Decode batch. #running-req: 1, #token: 586, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:15:45 DP1 TP0] Decode batch. #running-req: 1, #token: 634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.38, #queue-req: 0, 
[2025-07-28 01:15:45 DP0 TP0] Decode batch. #running-req: 1, #token: 626, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:15:45 DP1 TP0] Decode batch. #running-req: 1, #token: 674, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:15:45 DP0 TP0] Decode batch. #running-req: 1, #token: 666, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:15:45 DP1 TP0] Decode batch. #running-req: 1, #token: 714, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.43, #queue-req: 0, 
[2025-07-28 01:15:45 DP0 TP0] Decode batch. #running-req: 1, #token: 706, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:15:46 DP1 TP0] Decode batch. #running-req: 1, #token: 754, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.42, #queue-req: 0, 
[2025-07-28 01:15:46 DP0 TP0] Decode batch. #running-req: 1, #token: 746, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:15:46 DP1 TP0] Decode batch. #running-req: 1, #token: 794, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.46, #queue-req: 0, 
[2025-07-28 01:15:46 DP0 TP0] Decode batch. #running-req: 1, #token: 786, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:15:46 DP1 TP0] Decode batch. #running-req: 1, #token: 834, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.19, #queue-req: 0, 
[2025-07-28 01:15:46 DP0 TP0] Decode batch. #running-req: 1, #token: 826, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.09, #queue-req: 0, 
[2025-07-28 01:15:46 DP1 TP0] Decode batch. #running-req: 1, #token: 874, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.40, #queue-req: 0, 
[2025-07-28 01:15:47 DP0 TP0] Decode batch. #running-req: 1, #token: 866, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.24, #queue-req: 0, 
[2025-07-28 01:15:47 DP1 TP0] Decode batch. #running-req: 1, #token: 914, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.42, #queue-req: 0, 
[2025-07-28 01:15:47 DP0 TP0] Decode batch. #running-req: 1, #token: 906, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.20, #queue-req: 0, 
[2025-07-28 01:15:47 DP1 TP0] Decode batch. #running-req: 1, #token: 954, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:15:47] INFO:     127.0.0.1:43628 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:15:47 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 137, #cached-token: 88, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:15:47 DP1 TP0] Decode batch. #running-req: 2, #token: 1154, token usage: 0.00, cuda graph: True, gen throughput (token/s): 181.22, #queue-req: 0, 
[2025-07-28 01:15:48 DP1 TP0] Decode batch. #running-req: 2, #token: 1234, token usage: 0.00, cuda graph: True, gen throughput (token/s): 270.02, #queue-req: 0, 
[2025-07-28 01:15:48 DP1 TP0] Decode batch. #running-req: 2, #token: 1314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.79, #queue-req: 0, 
[2025-07-28 01:15:48 DP1 TP0] Decode batch. #running-req: 2, #token: 1394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.86, #queue-req: 0, 
[2025-07-28 01:15:49 DP1 TP0] Decode batch. #running-req: 2, #token: 1474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.85, #queue-req: 0, 
[2025-07-28 01:15:49 DP1 TP0] Decode batch. #running-req: 2, #token: 1554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.66, #queue-req: 0, 
[2025-07-28 01:15:49 DP1 TP0] Decode batch. #running-req: 2, #token: 1634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.77, #queue-req: 0, 
[2025-07-28 01:15:49 DP1 TP0] Decode batch. #running-req: 2, #token: 1714, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.41, #queue-req: 0, 
[2025-07-28 01:15:50 DP1 TP0] Decode batch. #running-req: 2, #token: 1794, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.80, #queue-req: 0, 
[2025-07-28 01:15:50 DP1 TP0] Decode batch. #running-req: 2, #token: 1874, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.45, #queue-req: 0, 
[2025-07-28 01:15:50 DP1 TP0] Decode batch. #running-req: 2, #token: 1954, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.56, #queue-req: 0, 
[2025-07-28 01:15:51 DP1 TP0] Decode batch. #running-req: 2, #token: 2034, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.54, #queue-req: 0, 
[2025-07-28 01:15:51 DP1 TP0] Decode batch. #running-req: 2, #token: 2114, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.29, #queue-req: 0, 
[2025-07-28 01:15:51 DP1 TP0] Decode batch. #running-req: 2, #token: 2194, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.40, #queue-req: 0, 
[2025-07-28 01:15:51 DP1 TP0] Decode batch. #running-req: 2, #token: 2274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.33, #queue-req: 0, 
[2025-07-28 01:15:52] INFO:     127.0.0.1:37000 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:15:52 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 85, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:15:52 DP0 TP0] Decode batch. #running-req: 1, #token: 172, token usage: 0.00, cuda graph: True, gen throughput (token/s): 8.25, #queue-req: 0, 
[2025-07-28 01:15:52 DP1 TP0] Decode batch. #running-req: 1, #token: 845, token usage: 0.00, cuda graph: True, gen throughput (token/s): 199.01, #queue-req: 0, 
[2025-07-28 01:15:52 DP0 TP0] Decode batch. #running-req: 1, #token: 212, token usage: 0.00, cuda graph: True, gen throughput (token/s): 133.01, #queue-req: 0, 
[2025-07-28 01:15:52 DP1 TP0] Decode batch. #running-req: 1, #token: 885, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.38, #queue-req: 0, 
[2025-07-28 01:15:52 DP0 TP0] Decode batch. #running-req: 1, #token: 252, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.56, #queue-req: 0, 
[2025-07-28 01:15:52 DP1 TP0] Decode batch. #running-req: 1, #token: 925, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:15:53 DP0 TP0] Decode batch. #running-req: 1, #token: 292, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.53, #queue-req: 0, 
[2025-07-28 01:15:53 DP1 TP0] Decode batch. #running-req: 1, #token: 965, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.56, #queue-req: 0, 
[2025-07-28 01:15:53 DP0 TP0] Decode batch. #running-req: 1, #token: 332, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.54, #queue-req: 0, 
[2025-07-28 01:15:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1005, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:15:53 DP0 TP0] Decode batch. #running-req: 1, #token: 372, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.56, #queue-req: 0, 
[2025-07-28 01:15:53 DP1 TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.90, #queue-req: 0, 
[2025-07-28 01:15:53] INFO:     127.0.0.1:43640 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:15:53 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 157, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:15:53 DP0 TP0] Decode batch. #running-req: 1, #token: 412, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.55, #queue-req: 0, 
[2025-07-28 01:15:54 DP1 TP0] Decode batch. #running-req: 1, #token: 282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 119.32, #queue-req: 0, 
[2025-07-28 01:15:54 DP0 TP0] Decode batch. #running-req: 1, #token: 452, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.54, #queue-req: 0, 
[2025-07-28 01:15:54 DP1 TP0] Decode batch. #running-req: 1, #token: 322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.40, #queue-req: 0, 
[2025-07-28 01:15:54 DP0 TP0] Decode batch. #running-req: 1, #token: 492, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.56, #queue-req: 0, 
[2025-07-28 01:15:54 DP1 TP0] Decode batch. #running-req: 1, #token: 362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.57, #queue-req: 0, 
[2025-07-28 01:15:54 DP0 TP0] Decode batch. #running-req: 1, #token: 532, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.46, #queue-req: 0, 
[2025-07-28 01:15:54 DP1 TP0] Decode batch. #running-req: 1, #token: 402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.50, #queue-req: 0, 
[2025-07-28 01:15:55 DP0 TP0] Decode batch. #running-req: 1, #token: 572, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.39, #queue-req: 0, 
[2025-07-28 01:15:55 DP1 TP0] Decode batch. #running-req: 1, #token: 442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.48, #queue-req: 0, 
[2025-07-28 01:15:55 DP0 TP0] Decode batch. #running-req: 1, #token: 612, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:15:55 DP1 TP0] Decode batch. #running-req: 1, #token: 482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.59, #queue-req: 0, 
[2025-07-28 01:15:55 DP0 TP0] Decode batch. #running-req: 1, #token: 652, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.39, #queue-req: 0, 
[2025-07-28 01:15:55 DP1 TP0] Decode batch. #running-req: 1, #token: 522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.43, #queue-req: 0, 
[2025-07-28 01:15:55 DP0 TP0] Decode batch. #running-req: 1, #token: 692, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.39, #queue-req: 0, 
[2025-07-28 01:15:56 DP1 TP0] Decode batch. #running-req: 1, #token: 562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:15:56 DP0 TP0] Decode batch. #running-req: 1, #token: 732, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:15:56 DP1 TP0] Decode batch. #running-req: 1, #token: 602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.37, #queue-req: 0, 
[2025-07-28 01:15:56 DP0 TP0] Decode batch. #running-req: 1, #token: 772, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.37, #queue-req: 0, 
[2025-07-28 01:15:56 DP1 TP0] Decode batch. #running-req: 1, #token: 642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.26, #queue-req: 0, 
[2025-07-28 01:15:56 DP0 TP0] Decode batch. #running-req: 1, #token: 812, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:15:56 DP1 TP0] Decode batch. #running-req: 1, #token: 682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:15:57 DP0 TP0] Decode batch. #running-req: 1, #token: 852, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:15:57 DP1 TP0] Decode batch. #running-req: 1, #token: 722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:15:57 DP0 TP0] Decode batch. #running-req: 1, #token: 892, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:15:57 DP1 TP0] Decode batch. #running-req: 1, #token: 762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:15:57 DP0 TP0] Decode batch. #running-req: 1, #token: 932, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.37, #queue-req: 0, 
[2025-07-28 01:15:57 DP1 TP0] Decode batch. #running-req: 1, #token: 802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:15:57 DP0 TP0] Decode batch. #running-req: 1, #token: 972, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.37, #queue-req: 0, 
[2025-07-28 01:15:58 DP1 TP0] Decode batch. #running-req: 1, #token: 842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.23, #queue-req: 0, 
[2025-07-28 01:15:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1012, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:15:58 DP1 TP0] Decode batch. #running-req: 1, #token: 882, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:15:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1052, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.68, #queue-req: 0, 
[2025-07-28 01:15:58 DP1 TP0] Decode batch. #running-req: 1, #token: 922, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:15:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1092, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.40, #queue-req: 0, 
[2025-07-28 01:15:58 DP1 TP0] Decode batch. #running-req: 1, #token: 962, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.38, #queue-req: 0, 
[2025-07-28 01:15:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1132, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.42, #queue-req: 0, 
[2025-07-28 01:15:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1002, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:15:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1172, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.95, #queue-req: 0, 
[2025-07-28 01:15:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1042, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.11, #queue-req: 0, 
[2025-07-28 01:15:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1212, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.95, #queue-req: 0, 
[2025-07-28 01:15:59 DP1 TP0] Decode batch. #running-req: 1, #token: 1082, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.13, #queue-req: 0, 
[2025-07-28 01:15:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1252, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.71, #queue-req: 0, 
[2025-07-28 01:16:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1122, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.05, #queue-req: 0, 
[2025-07-28 01:16:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1292, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.57, #queue-req: 0, 
[2025-07-28 01:16:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1162, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.98, #queue-req: 0, 
[2025-07-28 01:16:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1332, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.98, #queue-req: 0, 
[2025-07-28 01:16:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1202, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.99, #queue-req: 0, 
[2025-07-28 01:16:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1372, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.97, #queue-req: 0, 
[2025-07-28 01:16:00] INFO:     127.0.0.1:43656 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:16:00 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 150, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:16:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1242, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.90, #queue-req: 0, 
[2025-07-28 01:16:01 DP0 TP0] Decode batch. #running-req: 1, #token: 273, token usage: 0.00, cuda graph: True, gen throughput (token/s): 120.31, #queue-req: 0, 
[2025-07-28 01:16:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1282, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.05, #queue-req: 0, 
[2025-07-28 01:16:01 DP0 TP0] Decode batch. #running-req: 1, #token: 313, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:16:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1322, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.99, #queue-req: 0, 
[2025-07-28 01:16:01 DP0 TP0] Decode batch. #running-req: 1, #token: 353, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.49, #queue-req: 0, 
[2025-07-28 01:16:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1362, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.99, #queue-req: 0, 
[2025-07-28 01:16:02 DP0 TP0] Decode batch. #running-req: 1, #token: 393, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.50, #queue-req: 0, 
[2025-07-28 01:16:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1402, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.91, #queue-req: 0, 
[2025-07-28 01:16:02 DP0 TP0] Decode batch. #running-req: 1, #token: 433, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.42, #queue-req: 0, 
[2025-07-28 01:16:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1442, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.83, #queue-req: 0, 
[2025-07-28 01:16:02 DP0 TP0] Decode batch. #running-req: 1, #token: 473, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:16:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1482, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.99, #queue-req: 0, 
[2025-07-28 01:16:02 DP0 TP0] Decode batch. #running-req: 1, #token: 513, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.43, #queue-req: 0, 
[2025-07-28 01:16:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1522, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.84, #queue-req: 0, 
[2025-07-28 01:16:03 DP0 TP0] Decode batch. #running-req: 1, #token: 553, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:16:03 DP1 TP0] Decode batch. #running-req: 1, #token: 1562, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.85, #queue-req: 0, 
[2025-07-28 01:16:03 DP0 TP0] Decode batch. #running-req: 1, #token: 593, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:16:03 DP1 TP0] Decode batch. #running-req: 1, #token: 1602, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.83, #queue-req: 0, 
[2025-07-28 01:16:03 DP0 TP0] Decode batch. #running-req: 1, #token: 633, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:16:03 DP1 TP0] Decode batch. #running-req: 1, #token: 1642, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.65, #queue-req: 0, 
[2025-07-28 01:16:04 DP0 TP0] Decode batch. #running-req: 1, #token: 673, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:16:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1682, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.90, #queue-req: 0, 
[2025-07-28 01:16:04 DP0 TP0] Decode batch. #running-req: 1, #token: 713, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.30, #queue-req: 0, 
[2025-07-28 01:16:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1722, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.81, #queue-req: 0, 
[2025-07-28 01:16:04 DP0 TP0] Decode batch. #running-req: 1, #token: 753, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.24, #queue-req: 0, 
[2025-07-28 01:16:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1762, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.79, #queue-req: 0, 
[2025-07-28 01:16:04 DP0 TP0] Decode batch. #running-req: 1, #token: 793, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.27, #queue-req: 0, 
[2025-07-28 01:16:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1802, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.75, #queue-req: 0, 
[2025-07-28 01:16:05 DP0 TP0] Decode batch. #running-req: 1, #token: 833, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.27, #queue-req: 0, 
[2025-07-28 01:16:05 DP1 TP0] Decode batch. #running-req: 1, #token: 1842, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.69, #queue-req: 0, 
[2025-07-28 01:16:05 DP0 TP0] Decode batch. #running-req: 1, #token: 873, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.21, #queue-req: 0, 
[2025-07-28 01:16:05 DP1 TP0] Decode batch. #running-req: 1, #token: 1882, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.82, #queue-req: 0, 
[2025-07-28 01:16:05 DP0 TP0] Decode batch. #running-req: 1, #token: 913, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.20, #queue-req: 0, 
[2025-07-28 01:16:05] INFO:     127.0.0.1:45006 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:16:05 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 111, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:16:05 DP1 TP0] Decode batch. #running-req: 2, #token: 2037, token usage: 0.00, cuda graph: True, gen throughput (token/s): 132.52, #queue-req: 0, 
[2025-07-28 01:16:06 DP1 TP0] Decode batch. #running-req: 2, #token: 2117, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.28, #queue-req: 0, 
[2025-07-28 01:16:06 DP1 TP0] Decode batch. #running-req: 2, #token: 2197, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.40, #queue-req: 0, 
[2025-07-28 01:16:06 DP1 TP0] Decode batch. #running-req: 2, #token: 2277, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.24, #queue-req: 0, 
[2025-07-28 01:16:07 DP1 TP0] Decode batch. #running-req: 2, #token: 2357, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.98, #queue-req: 0, 
[2025-07-28 01:16:07 DP1 TP0] Decode batch. #running-req: 2, #token: 2437, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.72, #queue-req: 0, 
[2025-07-28 01:16:07 DP1 TP0] Decode batch. #running-req: 2, #token: 2517, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.87, #queue-req: 0, 
[2025-07-28 01:16:07 DP1 TP0] Decode batch. #running-req: 2, #token: 2597, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.51, #queue-req: 0, 
[2025-07-28 01:16:08 DP1 TP0] Decode batch. #running-req: 2, #token: 2677, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.29, #queue-req: 0, 
[2025-07-28 01:16:08 DP1 TP0] Decode batch. #running-req: 2, #token: 2757, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.56, #queue-req: 0, 
[2025-07-28 01:16:08] INFO:     127.0.0.1:44992 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:16:08 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 95, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:16:08 DP1 TP0] Decode batch. #running-req: 1, #token: 600, token usage: 0.00, cuda graph: True, gen throughput (token/s): 221.07, #queue-req: 0, 
[2025-07-28 01:16:09 DP0 TP0] Decode batch. #running-req: 1, #token: 216, token usage: 0.00, cuda graph: True, gen throughput (token/s): 12.03, #queue-req: 0, 
[2025-07-28 01:16:09 DP1 TP0] Decode batch. #running-req: 1, #token: 640, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.38, #queue-req: 0, 
[2025-07-28 01:16:09 DP0 TP0] Decode batch. #running-req: 1, #token: 256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.56, #queue-req: 0, 
[2025-07-28 01:16:09 DP1 TP0] Decode batch. #running-req: 1, #token: 680, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.30, #queue-req: 0, 
[2025-07-28 01:16:09 DP0 TP0] Decode batch. #running-req: 1, #token: 296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.50, #queue-req: 0, 
[2025-07-28 01:16:09 DP1 TP0] Decode batch. #running-req: 1, #token: 720, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:16:09 DP0 TP0] Decode batch. #running-req: 1, #token: 336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:16:10 DP1 TP0] Decode batch. #running-req: 1, #token: 760, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.22, #queue-req: 0, 
[2025-07-28 01:16:10 DP0 TP0] Decode batch. #running-req: 1, #token: 376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.89, #queue-req: 0, 
[2025-07-28 01:16:10 DP1 TP0] Decode batch. #running-req: 1, #token: 800, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.40, #queue-req: 0, 
[2025-07-28 01:16:10 DP0 TP0] Decode batch. #running-req: 1, #token: 416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.75, #queue-req: 0, 
[2025-07-28 01:16:10 DP1 TP0] Decode batch. #running-req: 1, #token: 840, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.11, #queue-req: 0, 
[2025-07-28 01:16:10 DP0 TP0] Decode batch. #running-req: 1, #token: 456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.51, #queue-req: 0, 
[2025-07-28 01:16:10 DP1 TP0] Decode batch. #running-req: 1, #token: 880, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.38, #queue-req: 0, 
[2025-07-28 01:16:11 DP0 TP0] Decode batch. #running-req: 1, #token: 496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.38, #queue-req: 0, 
[2025-07-28 01:16:11 DP1 TP0] Decode batch. #running-req: 1, #token: 920, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:16:11 DP0 TP0] Decode batch. #running-req: 1, #token: 536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.27, #queue-req: 0, 
[2025-07-28 01:16:11 DP1 TP0] Decode batch. #running-req: 1, #token: 960, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:16:11 DP0 TP0] Decode batch. #running-req: 1, #token: 576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.25, #queue-req: 0, 
[2025-07-28 01:16:11 DP1 TP0] Decode batch. #running-req: 1, #token: 1000, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.38, #queue-req: 0, 
[2025-07-28 01:16:11 DP0 TP0] Decode batch. #running-req: 1, #token: 616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.09, #queue-req: 0, 
[2025-07-28 01:16:12 DP1 TP0] Decode batch. #running-req: 1, #token: 1040, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.25, #queue-req: 0, 
[2025-07-28 01:16:12 DP0 TP0] Decode batch. #running-req: 1, #token: 656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.17, #queue-req: 0, 
[2025-07-28 01:16:12 DP1 TP0] Decode batch. #running-req: 1, #token: 1080, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.05, #queue-req: 0, 
[2025-07-28 01:16:12 DP0 TP0] Decode batch. #running-req: 1, #token: 696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:16:12 DP1 TP0] Decode batch. #running-req: 1, #token: 1120, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.11, #queue-req: 0, 
[2025-07-28 01:16:12 DP0 TP0] Decode batch. #running-req: 1, #token: 736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.23, #queue-req: 0, 
[2025-07-28 01:16:12 DP1 TP0] Decode batch. #running-req: 1, #token: 1160, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.10, #queue-req: 0, 
[2025-07-28 01:16:13 DP0 TP0] Decode batch. #running-req: 1, #token: 776, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.22, #queue-req: 0, 
[2025-07-28 01:16:13 DP1 TP0] Decode batch. #running-req: 1, #token: 1200, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.04, #queue-req: 0, 
[2025-07-28 01:16:13 DP0 TP0] Decode batch. #running-req: 1, #token: 816, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.14, #queue-req: 0, 
[2025-07-28 01:16:13 DP1 TP0] Decode batch. #running-req: 1, #token: 1240, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.05, #queue-req: 0, 
[2025-07-28 01:16:13 DP0 TP0] Decode batch. #running-req: 1, #token: 856, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.21, #queue-req: 0, 
[2025-07-28 01:16:13] INFO:     127.0.0.1:34736 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:16:13 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 89, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:16:13 DP1 TP0] Decode batch. #running-req: 1, #token: 179, token usage: 0.00, cuda graph: True, gen throughput (token/s): 119.20, #queue-req: 0, 
[2025-07-28 01:16:13 DP0 TP0] Decode batch. #running-req: 1, #token: 896, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.06, #queue-req: 0, 
[2025-07-28 01:16:14 DP1 TP0] Decode batch. #running-req: 1, #token: 219, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.70, #queue-req: 0, 
[2025-07-28 01:16:14 DP0 TP0] Decode batch. #running-req: 1, #token: 936, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.30, #queue-req: 0, 
[2025-07-28 01:16:14 DP1 TP0] Decode batch. #running-req: 1, #token: 259, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.66, #queue-req: 0, 
[2025-07-28 01:16:14 DP0 TP0] Decode batch. #running-req: 1, #token: 976, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:16:14 DP1 TP0] Decode batch. #running-req: 1, #token: 299, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.55, #queue-req: 0, 
[2025-07-28 01:16:14 DP0 TP0] Decode batch. #running-req: 1, #token: 1016, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.19, #queue-req: 0, 
[2025-07-28 01:16:14 DP1 TP0] Decode batch. #running-req: 1, #token: 339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.60, #queue-req: 0, 
[2025-07-28 01:16:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1056, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.98, #queue-req: 0, 
[2025-07-28 01:16:15 DP1 TP0] Decode batch. #running-req: 1, #token: 379, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.37, #queue-req: 0, 
[2025-07-28 01:16:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1096, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.71, #queue-req: 0, 
[2025-07-28 01:16:15 DP1 TP0] Decode batch. #running-req: 1, #token: 419, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.46, #queue-req: 0, 
[2025-07-28 01:16:15 DP0 TP0] Decode batch. #running-req: 1, #token: 1136, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.82, #queue-req: 0, 
[2025-07-28 01:16:15 DP1 TP0] Decode batch. #running-req: 1, #token: 459, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.51, #queue-req: 0, 
[2025-07-28 01:16:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1176, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.88, #queue-req: 0, 
[2025-07-28 01:16:16 DP1 TP0] Decode batch. #running-req: 1, #token: 499, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.54, #queue-req: 0, 
[2025-07-28 01:16:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1216, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.93, #queue-req: 0, 
[2025-07-28 01:16:16 DP1 TP0] Decode batch. #running-req: 1, #token: 539, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.20, #queue-req: 0, 
[2025-07-28 01:16:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.01, #queue-req: 0, 
[2025-07-28 01:16:16 DP1 TP0] Decode batch. #running-req: 1, #token: 579, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.18, #queue-req: 0, 
[2025-07-28 01:16:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1296, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.32, #queue-req: 0, 
[2025-07-28 01:16:16 DP1 TP0] Decode batch. #running-req: 1, #token: 619, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:16:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.39, #queue-req: 0, 
[2025-07-28 01:16:17 DP1 TP0] Decode batch. #running-req: 1, #token: 659, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.45, #queue-req: 0, 
[2025-07-28 01:16:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1376, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.58, #queue-req: 0, 
[2025-07-28 01:16:17 DP1 TP0] Decode batch. #running-req: 1, #token: 699, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.46, #queue-req: 0, 
[2025-07-28 01:16:17 DP0 TP0] Decode batch. #running-req: 1, #token: 1416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.28, #queue-req: 0, 
[2025-07-28 01:16:17 DP1 TP0] Decode batch. #running-req: 1, #token: 739, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.48, #queue-req: 0, 
[2025-07-28 01:16:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1456, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.32, #queue-req: 0, 
[2025-07-28 01:16:18 DP1 TP0] Decode batch. #running-req: 1, #token: 779, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.12, #queue-req: 0, 
[2025-07-28 01:16:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.66, #queue-req: 0, 
[2025-07-28 01:16:18 DP1 TP0] Decode batch. #running-req: 1, #token: 819, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:16:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1536, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.79, #queue-req: 0, 
[2025-07-28 01:16:18 DP1 TP0] Decode batch. #running-req: 1, #token: 859, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.40, #queue-req: 0, 
[2025-07-28 01:16:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.67, #queue-req: 0, 
[2025-07-28 01:16:18 DP1 TP0] Decode batch. #running-req: 1, #token: 899, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.42, #queue-req: 0, 
[2025-07-28 01:16:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1616, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.67, #queue-req: 0, 
[2025-07-28 01:16:19 DP1 TP0] Decode batch. #running-req: 1, #token: 939, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.50, #queue-req: 0, 
[2025-07-28 01:16:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.41, #queue-req: 0, 
[2025-07-28 01:16:19 DP1 TP0] Decode batch. #running-req: 1, #token: 979, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.20, #queue-req: 0, 
[2025-07-28 01:16:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1696, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.67, #queue-req: 0, 
[2025-07-28 01:16:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1019, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.42, #queue-req: 0, 
[2025-07-28 01:16:19] INFO:     127.0.0.1:34750 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:16:19 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 115, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:16:20] INFO:     127.0.0.1:57986 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:16:20 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 164, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:16:20 DP0 TP0] Decode batch. #running-req: 1, #token: 229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 118.86, #queue-req: 0, 
[2025-07-28 01:16:20 DP1 TP0] Decode batch. #running-req: 1, #token: 259, token usage: 0.00, cuda graph: True, gen throughput (token/s): 119.49, #queue-req: 0, 
[2025-07-28 01:16:20 DP0 TP0] Decode batch. #running-req: 1, #token: 269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:16:20 DP1 TP0] Decode batch. #running-req: 1, #token: 299, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.62, #queue-req: 0, 
[2025-07-28 01:16:20 DP0 TP0] Decode batch. #running-req: 1, #token: 309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.43, #queue-req: 0, 
[2025-07-28 01:16:20 DP1 TP0] Decode batch. #running-req: 1, #token: 339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.50, #queue-req: 0, 
[2025-07-28 01:16:20 DP0 TP0] Decode batch. #running-req: 1, #token: 349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.25, #queue-req: 0, 
[2025-07-28 01:16:21 DP1 TP0] Decode batch. #running-req: 1, #token: 379, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.58, #queue-req: 0, 
[2025-07-28 01:16:21 DP0 TP0] Decode batch. #running-req: 1, #token: 389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:16:21 DP1 TP0] Decode batch. #running-req: 1, #token: 419, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.53, #queue-req: 0, 
[2025-07-28 01:16:21 DP0 TP0] Decode batch. #running-req: 1, #token: 429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.45, #queue-req: 0, 
[2025-07-28 01:16:21 DP1 TP0] Decode batch. #running-req: 1, #token: 459, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.67, #queue-req: 0, 
[2025-07-28 01:16:21 DP0 TP0] Decode batch. #running-req: 1, #token: 469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.14, #queue-req: 0, 
[2025-07-28 01:16:21 DP1 TP0] Decode batch. #running-req: 1, #token: 499, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.52, #queue-req: 0, 
[2025-07-28 01:16:22 DP0 TP0] Decode batch. #running-req: 1, #token: 509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.38, #queue-req: 0, 
[2025-07-28 01:16:22 DP1 TP0] Decode batch. #running-req: 1, #token: 539, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.54, #queue-req: 0, 
[2025-07-28 01:16:22 DP0 TP0] Decode batch. #running-req: 1, #token: 549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.11, #queue-req: 0, 
[2025-07-28 01:16:22 DP1 TP0] Decode batch. #running-req: 1, #token: 579, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.16, #queue-req: 0, 
[2025-07-28 01:16:22 DP0 TP0] Decode batch. #running-req: 1, #token: 589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.18, #queue-req: 0, 
[2025-07-28 01:16:22 DP1 TP0] Decode batch. #running-req: 1, #token: 619, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.25, #queue-req: 0, 
[2025-07-28 01:16:22 DP0 TP0] Decode batch. #running-req: 1, #token: 629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.64, #queue-req: 0, 
[2025-07-28 01:16:23 DP1 TP0] Decode batch. #running-req: 1, #token: 659, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:16:23 DP0 TP0] Decode batch. #running-req: 1, #token: 669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.23, #queue-req: 0, 
[2025-07-28 01:16:23 DP1 TP0] Decode batch. #running-req: 1, #token: 699, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.26, #queue-req: 0, 
[2025-07-28 01:16:23 DP0 TP0] Decode batch. #running-req: 1, #token: 709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.25, #queue-req: 0, 
[2025-07-28 01:16:23 DP1 TP0] Decode batch. #running-req: 1, #token: 739, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.43, #queue-req: 0, 
[2025-07-28 01:16:23 DP0 TP0] Decode batch. #running-req: 1, #token: 749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.17, #queue-req: 0, 
[2025-07-28 01:16:23 DP1 TP0] Decode batch. #running-req: 1, #token: 779, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.45, #queue-req: 0, 
[2025-07-28 01:16:24 DP0 TP0] Decode batch. #running-req: 1, #token: 789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.23, #queue-req: 0, 
[2025-07-28 01:16:24 DP1 TP0] Decode batch. #running-req: 1, #token: 819, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:16:24 DP0 TP0] Decode batch. #running-req: 1, #token: 829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.21, #queue-req: 0, 
[2025-07-28 01:16:24 DP1 TP0] Decode batch. #running-req: 1, #token: 859, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.39, #queue-req: 0, 
[2025-07-28 01:16:24 DP0 TP0] Decode batch. #running-req: 1, #token: 869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.16, #queue-req: 0, 
[2025-07-28 01:16:24 DP1 TP0] Decode batch. #running-req: 1, #token: 899, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:16:24 DP0 TP0] Decode batch. #running-req: 1, #token: 909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.17, #queue-req: 0, 
[2025-07-28 01:16:25 DP1 TP0] Decode batch. #running-req: 1, #token: 939, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.45, #queue-req: 0, 
[2025-07-28 01:16:25 DP0 TP0] Decode batch. #running-req: 1, #token: 949, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.21, #queue-req: 0, 
[2025-07-28 01:16:25 DP1 TP0] Decode batch. #running-req: 1, #token: 979, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.42, #queue-req: 0, 
[2025-07-28 01:16:25 DP0 TP0] Decode batch. #running-req: 1, #token: 989, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.18, #queue-req: 0, 
[2025-07-28 01:16:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1019, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.43, #queue-req: 0, 
[2025-07-28 01:16:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1029, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.17, #queue-req: 0, 
[2025-07-28 01:16:25 DP1 TP0] Decode batch. #running-req: 1, #token: 1059, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.14, #queue-req: 0, 
[2025-07-28 01:16:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1069, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.88, #queue-req: 0, 
[2025-07-28 01:16:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1099, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.12, #queue-req: 0, 
[2025-07-28 01:16:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1109, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.89, #queue-req: 0, 
[2025-07-28 01:16:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1139, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.04, #queue-req: 0, 
[2025-07-28 01:16:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1149, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.90, #queue-req: 0, 
[2025-07-28 01:16:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1179, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.09, #queue-req: 0, 
[2025-07-28 01:16:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.85, #queue-req: 0, 
[2025-07-28 01:16:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1219, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.07, #queue-req: 0, 
[2025-07-28 01:16:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.85, #queue-req: 0, 
[2025-07-28 01:16:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1259, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.08, #queue-req: 0, 
[2025-07-28 01:16:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.83, #queue-req: 0, 
[2025-07-28 01:16:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1299, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.08, #queue-req: 0, 
[2025-07-28 01:16:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.84, #queue-req: 0, 
[2025-07-28 01:16:27 DP1 TP0] Decode batch. #running-req: 1, #token: 1339, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.03, #queue-req: 0, 
[2025-07-28 01:16:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.87, #queue-req: 0, 
[2025-07-28 01:16:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1379, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.08, #queue-req: 0, 
[2025-07-28 01:16:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.90, #queue-req: 0, 
[2025-07-28 01:16:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1419, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.07, #queue-req: 0, 
[2025-07-28 01:16:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.83, #queue-req: 0, 
[2025-07-28 01:16:28 DP1 TP0] Decode batch. #running-req: 1, #token: 1459, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.03, #queue-req: 0, 
[2025-07-28 01:16:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.72, #queue-req: 0, 
[2025-07-28 01:16:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1499, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.97, #queue-req: 0, 
[2025-07-28 01:16:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.77, #queue-req: 0, 
[2025-07-28 01:16:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1539, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.05, #queue-req: 0, 
[2025-07-28 01:16:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.77, #queue-req: 0, 
[2025-07-28 01:16:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1579, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.97, #queue-req: 0, 
[2025-07-28 01:16:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.63, #queue-req: 0, 
[2025-07-28 01:16:29 DP1 TP0] Decode batch. #running-req: 1, #token: 1619, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.88, #queue-req: 0, 
[2025-07-28 01:16:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.71, #queue-req: 0, 
[2025-07-28 01:16:30 DP1 TP0] Decode batch. #running-req: 1, #token: 1659, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.91, #queue-req: 0, 
[2025-07-28 01:16:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.67, #queue-req: 0, 
[2025-07-28 01:16:30 DP1 TP0] Decode batch. #running-req: 1, #token: 1699, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.92, #queue-req: 0, 
[2025-07-28 01:16:30 DP0 TP0] Decode batch. #running-req: 1, #token: 1709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.71, #queue-req: 0, 
[2025-07-28 01:16:30 DP1 TP0] Decode batch. #running-req: 1, #token: 1739, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.91, #queue-req: 0, 
[2025-07-28 01:16:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.68, #queue-req: 0, 
[2025-07-28 01:16:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1779, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.92, #queue-req: 0, 
[2025-07-28 01:16:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.68, #queue-req: 0, 
[2025-07-28 01:16:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1819, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.92, #queue-req: 0, 
[2025-07-28 01:16:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.65, #queue-req: 0, 
[2025-07-28 01:16:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1859, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.97, #queue-req: 0, 
[2025-07-28 01:16:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.61, #queue-req: 0, 
[2025-07-28 01:16:31 DP1 TP0] Decode batch. #running-req: 1, #token: 1899, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.88, #queue-req: 0, 
[2025-07-28 01:16:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.70, #queue-req: 0, 
[2025-07-28 01:16:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1939, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.88, #queue-req: 0, 
[2025-07-28 01:16:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1949, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.71, #queue-req: 0, 
[2025-07-28 01:16:32 DP1 TP0] Decode batch. #running-req: 1, #token: 1979, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.92, #queue-req: 0, 
[2025-07-28 01:16:32 DP0 TP0] Decode batch. #running-req: 1, #token: 1989, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.74, #queue-req: 0, 
[2025-07-28 01:16:32 DP1 TP0] Decode batch. #running-req: 1, #token: 2019, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.81, #queue-req: 0, 
[2025-07-28 01:16:33 DP0 TP0] Decode batch. #running-req: 1, #token: 2029, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.69, #queue-req: 0, 
[2025-07-28 01:16:33 DP1 TP0] Decode batch. #running-req: 1, #token: 2059, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.87, #queue-req: 0, 
[2025-07-28 01:16:33 DP0 TP0] Decode batch. #running-req: 1, #token: 2069, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.51, #queue-req: 0, 
[2025-07-28 01:16:33 DP1 TP0] Decode batch. #running-req: 1, #token: 2099, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.63, #queue-req: 0, 
[2025-07-28 01:16:33 DP0 TP0] Decode batch. #running-req: 1, #token: 2109, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.42, #queue-req: 0, 
[2025-07-28 01:16:33 DP1 TP0] Decode batch. #running-req: 1, #token: 2139, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.61, #queue-req: 0, 
[2025-07-28 01:16:33 DP0 TP0] Decode batch. #running-req: 1, #token: 2149, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.34, #queue-req: 0, 
[2025-07-28 01:16:33 DP1 TP0] Decode batch. #running-req: 1, #token: 2179, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.63, #queue-req: 0, 
[2025-07-28 01:16:34 DP0 TP0] Decode batch. #running-req: 1, #token: 2189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.38, #queue-req: 0, 
[2025-07-28 01:16:34 DP1 TP0] Decode batch. #running-req: 1, #token: 2219, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.59, #queue-req: 0, 
[2025-07-28 01:16:34] INFO:     127.0.0.1:58018 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:16:34 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 143, #cached-token: 85, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:16:34 DP0 TP0] Decode batch. #running-req: 2, #token: 2395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 187.33, #queue-req: 0, 
[2025-07-28 01:16:34 DP0 TP0] Decode batch. #running-req: 2, #token: 2475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.18, #queue-req: 0, 
[2025-07-28 01:16:35 DP0 TP0] Decode batch. #running-req: 2, #token: 2555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.06, #queue-req: 0, 
[2025-07-28 01:16:35 DP0 TP0] Decode batch. #running-req: 2, #token: 2635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.42, #queue-req: 0, 
[2025-07-28 01:16:35 DP0 TP0] Decode batch. #running-req: 2, #token: 2715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.39, #queue-req: 0, 
[2025-07-28 01:16:36 DP0 TP0] Decode batch. #running-req: 2, #token: 2795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.44, #queue-req: 0, 
[2025-07-28 01:16:36 DP0 TP0] Decode batch. #running-req: 2, #token: 2875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.41, #queue-req: 0, 
[2025-07-28 01:16:36 DP0 TP0] Decode batch. #running-req: 2, #token: 2955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.35, #queue-req: 0, 
[2025-07-28 01:16:36 DP0 TP0] Decode batch. #running-req: 2, #token: 3035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.27, #queue-req: 0, 
[2025-07-28 01:16:37 DP0 TP0] Decode batch. #running-req: 2, #token: 3115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.28, #queue-req: 0, 
[2025-07-28 01:16:37 DP0 TP0] Decode batch. #running-req: 2, #token: 3195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.26, #queue-req: 0, 
[2025-07-28 01:16:37 DP0 TP0] Decode batch. #running-req: 2, #token: 3275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.26, #queue-req: 0, 
[2025-07-28 01:16:38 DP0 TP0] Decode batch. #running-req: 2, #token: 3355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.18, #queue-req: 0, 
[2025-07-28 01:16:38 DP0 TP0] Decode batch. #running-req: 2, #token: 3435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.20, #queue-req: 0, 
[2025-07-28 01:16:38 DP0 TP0] Decode batch. #running-req: 2, #token: 3515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.14, #queue-req: 0, 
[2025-07-28 01:16:39 DP0 TP0] Decode batch. #running-req: 2, #token: 3595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.18, #queue-req: 0, 
[2025-07-28 01:16:39 DP0 TP0] Decode batch. #running-req: 2, #token: 3675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.02, #queue-req: 0, 
[2025-07-28 01:16:39 DP0 TP0] Decode batch. #running-req: 2, #token: 3755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.98, #queue-req: 0, 
[2025-07-28 01:16:39 DP0 TP0] Decode batch. #running-req: 2, #token: 3835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.97, #queue-req: 0, 
[2025-07-28 01:16:40 DP0 TP0] Decode batch. #running-req: 2, #token: 3915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.74, #queue-req: 0, 
[2025-07-28 01:16:40 DP0 TP0] Decode batch. #running-req: 2, #token: 3995, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.08, #queue-req: 0, 
[2025-07-28 01:16:40 DP0 TP0] Decode batch. #running-req: 2, #token: 4075, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.04, #queue-req: 0, 
[2025-07-28 01:16:41 DP0 TP0] Decode batch. #running-req: 2, #token: 4155, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.71, #queue-req: 0, 
[2025-07-28 01:16:41 DP0 TP0] Decode batch. #running-req: 2, #token: 4235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.80, #queue-req: 0, 
[2025-07-28 01:16:41 DP0 TP0] Decode batch. #running-req: 2, #token: 4315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.86, #queue-req: 0, 
[2025-07-28 01:16:42 DP0 TP0] Decode batch. #running-req: 2, #token: 4395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.81, #queue-req: 0, 
[2025-07-28 01:16:42 DP0 TP0] Decode batch. #running-req: 2, #token: 4475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.68, #queue-req: 0, 
[2025-07-28 01:16:42 DP0 TP0] Decode batch. #running-req: 2, #token: 4555, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.60, #queue-req: 0, 
[2025-07-28 01:16:42 DP0 TP0] Decode batch. #running-req: 2, #token: 4635, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.49, #queue-req: 0, 
[2025-07-28 01:16:43 DP0 TP0] Decode batch. #running-req: 2, #token: 4715, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.40, #queue-req: 0, 
[2025-07-28 01:16:43 DP0 TP0] Decode batch. #running-req: 2, #token: 4795, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.32, #queue-req: 0, 
[2025-07-28 01:16:43 DP0 TP0] Decode batch. #running-req: 2, #token: 4875, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.24, #queue-req: 0, 
[2025-07-28 01:16:44 DP0 TP0] Decode batch. #running-req: 2, #token: 4955, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.35, #queue-req: 0, 
[2025-07-28 01:16:44 DP0 TP0] Decode batch. #running-req: 2, #token: 5035, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.29, #queue-req: 0, 
[2025-07-28 01:16:44 DP0 TP0] Decode batch. #running-req: 2, #token: 5115, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.22, #queue-req: 0, 
[2025-07-28 01:16:44 DP0 TP0] Decode batch. #running-req: 2, #token: 5195, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.90, #queue-req: 0, 
[2025-07-28 01:16:45 DP0 TP0] Decode batch. #running-req: 2, #token: 5275, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.93, #queue-req: 0, 
[2025-07-28 01:16:45 DP0 TP0] Decode batch. #running-req: 2, #token: 5355, token usage: 0.00, cuda graph: True, gen throughput (token/s): 257.59, #queue-req: 0, 
[2025-07-28 01:16:45 DP0 TP0] Decode batch. #running-req: 2, #token: 5435, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.20, #queue-req: 0, 
[2025-07-28 01:16:46 DP0 TP0] Decode batch. #running-req: 2, #token: 5515, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.74, #queue-req: 0, 
[2025-07-28 01:16:46 DP0 TP0] Decode batch. #running-req: 2, #token: 5595, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.59, #queue-req: 0, 
[2025-07-28 01:16:46 DP0 TP0] Decode batch. #running-req: 2, #token: 5675, token usage: 0.00, cuda graph: True, gen throughput (token/s): 265.31, #queue-req: 0, 
[2025-07-28 01:16:47 DP0 TP0] Decode batch. #running-req: 2, #token: 5755, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.19, #queue-req: 0, 
[2025-07-28 01:16:47 DP0 TP0] Decode batch. #running-req: 2, #token: 5835, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.72, #queue-req: 0, 
[2025-07-28 01:16:47 DP0 TP0] Decode batch. #running-req: 2, #token: 5915, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.77, #queue-req: 0, 
[2025-07-28 01:16:48 DP0 TP0] Decode batch. #running-req: 2, #token: 5995, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.23, #queue-req: 0, 
[2025-07-28 01:16:48 DP0 TP0] Decode batch. #running-req: 2, #token: 6075, token usage: 0.00, cuda graph: True, gen throughput (token/s): 264.82, #queue-req: 0, 
[2025-07-28 01:16:48 DP0 TP0] Decode batch. #running-req: 2, #token: 6155, token usage: 0.00, cuda graph: True, gen throughput (token/s): 264.83, #queue-req: 0, 
[2025-07-28 01:16:48 DP0 TP0] Decode batch. #running-req: 2, #token: 6235, token usage: 0.00, cuda graph: True, gen throughput (token/s): 264.25, #queue-req: 0, 
[2025-07-28 01:16:49 DP0 TP0] Decode batch. #running-req: 2, #token: 6315, token usage: 0.00, cuda graph: True, gen throughput (token/s): 264.30, #queue-req: 0, 
[2025-07-28 01:16:49 DP0 TP0] Decode batch. #running-req: 2, #token: 6395, token usage: 0.00, cuda graph: True, gen throughput (token/s): 264.61, #queue-req: 0, 
[2025-07-28 01:16:49 DP0 TP0] Decode batch. #running-req: 2, #token: 6475, token usage: 0.00, cuda graph: True, gen throughput (token/s): 264.40, #queue-req: 0, 
[2025-07-28 01:16:50] INFO:     127.0.0.1:37742 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:16:50 DP0 TP0] Decode batch. #running-req: 1, #token: 4309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 245.55, #queue-req: 0, 
[2025-07-28 01:16:50 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 117, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:16:50] INFO:     127.0.0.1:58002 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:16:50 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 125, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:16:50 DP1 TP0] Decode batch. #running-req: 1, #token: 234, token usage: 0.00, cuda graph: True, gen throughput (token/s): 2.47, #queue-req: 0, 
[2025-07-28 01:16:50 DP0 TP0] Decode batch. #running-req: 1, #token: 236, token usage: 0.00, cuda graph: True, gen throughput (token/s): 118.66, #queue-req: 0, 
[2025-07-28 01:16:50 DP1 TP0] Decode batch. #running-req: 1, #token: 274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:16:50 DP0 TP0] Decode batch. #running-req: 1, #token: 276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.50, #queue-req: 0, 
[2025-07-28 01:16:51 DP1 TP0] Decode batch. #running-req: 1, #token: 314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.58, #queue-req: 0, 
[2025-07-28 01:16:51 DP0 TP0] Decode batch. #running-req: 1, #token: 316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:16:51 DP1 TP0] Decode batch. #running-req: 1, #token: 354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.62, #queue-req: 0, 
[2025-07-28 01:16:51 DP0 TP0] Decode batch. #running-req: 1, #token: 356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.46, #queue-req: 0, 
[2025-07-28 01:16:51 DP1 TP0] Decode batch. #running-req: 1, #token: 394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.62, #queue-req: 0, 
[2025-07-28 01:16:51 DP0 TP0] Decode batch. #running-req: 1, #token: 396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.46, #queue-req: 0, 
[2025-07-28 01:16:51 DP1 TP0] Decode batch. #running-req: 1, #token: 434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.56, #queue-req: 0, 
[2025-07-28 01:16:51 DP0 TP0] Decode batch. #running-req: 1, #token: 436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.37, #queue-req: 0, 
[2025-07-28 01:16:52 DP1 TP0] Decode batch. #running-req: 1, #token: 474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:16:52 DP0 TP0] Decode batch. #running-req: 1, #token: 476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.26, #queue-req: 0, 
[2025-07-28 01:16:52 DP1 TP0] Decode batch. #running-req: 1, #token: 514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.64, #queue-req: 0, 
[2025-07-28 01:16:52 DP0 TP0] Decode batch. #running-req: 1, #token: 516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.43, #queue-req: 0, 
[2025-07-28 01:16:52 DP1 TP0] Decode batch. #running-req: 1, #token: 554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.50, #queue-req: 0, 
[2025-07-28 01:16:52 DP0 TP0] Decode batch. #running-req: 1, #token: 556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:16:53 DP1 TP0] Decode batch. #running-req: 1, #token: 594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.45, #queue-req: 0, 
[2025-07-28 01:16:53 DP0 TP0] Decode batch. #running-req: 1, #token: 596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.21, #queue-req: 0, 
[2025-07-28 01:16:53 DP1 TP0] Decode batch. #running-req: 1, #token: 634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:16:53 DP0 TP0] Decode batch. #running-req: 1, #token: 636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:16:53 DP1 TP0] Decode batch. #running-req: 1, #token: 674, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.46, #queue-req: 0, 
[2025-07-28 01:16:53 DP0 TP0] Decode batch. #running-req: 1, #token: 676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:16:53 DP1 TP0] Decode batch. #running-req: 1, #token: 714, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.45, #queue-req: 0, 
[2025-07-28 01:16:53 DP0 TP0] Decode batch. #running-req: 1, #token: 716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:16:54 DP1 TP0] Decode batch. #running-req: 1, #token: 754, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.45, #queue-req: 0, 
[2025-07-28 01:16:54 DP0 TP0] Decode batch. #running-req: 1, #token: 756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:16:54 DP1 TP0] Decode batch. #running-req: 1, #token: 794, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:16:54 DP0 TP0] Decode batch. #running-req: 1, #token: 796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:16:54 DP1 TP0] Decode batch. #running-req: 1, #token: 834, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.39, #queue-req: 0, 
[2025-07-28 01:16:54 DP0 TP0] Decode batch. #running-req: 1, #token: 836, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:16:55 DP1 TP0] Decode batch. #running-req: 1, #token: 874, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.38, #queue-req: 0, 
[2025-07-28 01:16:55 DP0 TP0] Decode batch. #running-req: 1, #token: 876, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:16:55 DP1 TP0] Decode batch. #running-req: 1, #token: 914, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:16:55 DP0 TP0] Decode batch. #running-req: 1, #token: 916, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:16:55 DP1 TP0] Decode batch. #running-req: 1, #token: 954, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.45, #queue-req: 0, 
[2025-07-28 01:16:55 DP0 TP0] Decode batch. #running-req: 1, #token: 956, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:16:55 DP1 TP0] Decode batch. #running-req: 1, #token: 994, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:16:55 DP0 TP0] Decode batch. #running-req: 1, #token: 996, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.30, #queue-req: 0, 
[2025-07-28 01:16:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1034, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:16:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1036, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.20, #queue-req: 0, 
[2025-07-28 01:16:56 DP1 TP0] Decode batch. #running-req: 1, #token: 1074, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.98, #queue-req: 0, 
[2025-07-28 01:16:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1076, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.96, #queue-req: 0, 
[2025-07-28 01:16:56] INFO:     127.0.0.1:51428 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:16:56 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 196, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:16:56 DP0 TP0] Decode batch. #running-req: 1, #token: 1116, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.95, #queue-req: 0, 
[2025-07-28 01:16:56 DP1 TP0] Decode batch. #running-req: 1, #token: 309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 119.35, #queue-req: 0, 
[2025-07-28 01:16:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1156, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.97, #queue-req: 0, 
[2025-07-28 01:16:57 DP1 TP0] Decode batch. #running-req: 1, #token: 349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:16:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1196, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.99, #queue-req: 0, 
[2025-07-28 01:16:57 DP1 TP0] Decode batch. #running-req: 1, #token: 389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.45, #queue-req: 0, 
[2025-07-28 01:16:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1236, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.95, #queue-req: 0, 
[2025-07-28 01:16:57 DP1 TP0] Decode batch. #running-req: 1, #token: 429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.54, #queue-req: 0, 
[2025-07-28 01:16:57 DP0 TP0] Decode batch. #running-req: 1, #token: 1276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.95, #queue-req: 0, 
[2025-07-28 01:16:57 DP1 TP0] Decode batch. #running-req: 1, #token: 469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.50, #queue-req: 0, 
[2025-07-28 01:16:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.97, #queue-req: 0, 
[2025-07-28 01:16:58 DP1 TP0] Decode batch. #running-req: 1, #token: 509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.48, #queue-req: 0, 
[2025-07-28 01:16:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.93, #queue-req: 0, 
[2025-07-28 01:16:58 DP1 TP0] Decode batch. #running-req: 1, #token: 549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:16:58 DP0 TP0] Decode batch. #running-req: 1, #token: 1396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.91, #queue-req: 0, 
[2025-07-28 01:16:58 DP1 TP0] Decode batch. #running-req: 1, #token: 589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.39, #queue-req: 0, 
[2025-07-28 01:16:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.86, #queue-req: 0, 
[2025-07-28 01:16:59 DP1 TP0] Decode batch. #running-req: 1, #token: 629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.38, #queue-req: 0, 
[2025-07-28 01:16:59 DP1 TP0] Decode batch. #running-req: 1, #token: 669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:16:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.87, #queue-req: 0, 
[2025-07-28 01:16:59 DP1 TP0] Decode batch. #running-req: 1, #token: 709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:16:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.86, #queue-req: 0, 
[2025-07-28 01:16:59 DP1 TP0] Decode batch. #running-req: 1, #token: 749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:16:59 DP0 TP0] Decode batch. #running-req: 1, #token: 1556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.79, #queue-req: 0, 
[2025-07-28 01:17:00 DP1 TP0] Decode batch. #running-req: 1, #token: 789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.38, #queue-req: 0, 
[2025-07-28 01:17:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.77, #queue-req: 0, 
[2025-07-28 01:17:00 DP1 TP0] Decode batch. #running-req: 1, #token: 829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:17:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.75, #queue-req: 0, 
[2025-07-28 01:17:00 DP1 TP0] Decode batch. #running-req: 1, #token: 869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:17:00 DP0 TP0] Decode batch. #running-req: 1, #token: 1676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.73, #queue-req: 0, 
[2025-07-28 01:17:01 DP1 TP0] Decode batch. #running-req: 1, #token: 909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:17:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.87, #queue-req: 0, 
[2025-07-28 01:17:01 DP1 TP0] Decode batch. #running-req: 1, #token: 949, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.37, #queue-req: 0, 
[2025-07-28 01:17:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.80, #queue-req: 0, 
[2025-07-28 01:17:01 DP1 TP0] Decode batch. #running-req: 1, #token: 989, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.38, #queue-req: 0, 
[2025-07-28 01:17:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.81, #queue-req: 0, 
[2025-07-28 01:17:01 DP1 TP0] Decode batch. #running-req: 1, #token: 1029, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:17:01 DP0 TP0] Decode batch. #running-req: 1, #token: 1836, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.78, #queue-req: 0, 
[2025-07-28 01:17:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1069, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.99, #queue-req: 0, 
[2025-07-28 01:17:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1876, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.83, #queue-req: 0, 
[2025-07-28 01:17:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1109, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.00, #queue-req: 0, 
[2025-07-28 01:17:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1916, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.74, #queue-req: 0, 
[2025-07-28 01:17:02 DP1 TP0] Decode batch. #running-req: 1, #token: 1149, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.97, #queue-req: 0, 
[2025-07-28 01:17:02 DP0 TP0] Decode batch. #running-req: 1, #token: 1956, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.84, #queue-req: 0, 
[2025-07-28 01:17:03 DP1 TP0] Decode batch. #running-req: 1, #token: 1189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.94, #queue-req: 0, 
[2025-07-28 01:17:03 DP0 TP0] Decode batch. #running-req: 1, #token: 1996, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.82, #queue-req: 0, 
[2025-07-28 01:17:03 DP1 TP0] Decode batch. #running-req: 1, #token: 1229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.00, #queue-req: 0, 
[2025-07-28 01:17:03 DP0 TP0] Decode batch. #running-req: 1, #token: 2036, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.76, #queue-req: 0, 
[2025-07-28 01:17:03 DP1 TP0] Decode batch. #running-req: 1, #token: 1269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.96, #queue-req: 0, 
[2025-07-28 01:17:03 DP0 TP0] Decode batch. #running-req: 1, #token: 2076, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.58, #queue-req: 0, 
[2025-07-28 01:17:03 DP1 TP0] Decode batch. #running-req: 1, #token: 1309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.95, #queue-req: 0, 
[2025-07-28 01:17:03 DP0 TP0] Decode batch. #running-req: 1, #token: 2116, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.45, #queue-req: 0, 
[2025-07-28 01:17:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.99, #queue-req: 0, 
[2025-07-28 01:17:04 DP0 TP0] Decode batch. #running-req: 1, #token: 2156, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.50, #queue-req: 0, 
[2025-07-28 01:17:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.92, #queue-req: 0, 
[2025-07-28 01:17:04 DP0 TP0] Decode batch. #running-req: 1, #token: 2196, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.51, #queue-req: 0, 
[2025-07-28 01:17:04 DP1 TP0] Decode batch. #running-req: 1, #token: 1429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.92, #queue-req: 0, 
[2025-07-28 01:17:04 DP0 TP0] Decode batch. #running-req: 1, #token: 2236, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.48, #queue-req: 0, 
[2025-07-28 01:17:05 DP1 TP0] Decode batch. #running-req: 1, #token: 1469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.88, #queue-req: 0, 
[2025-07-28 01:17:05 DP0 TP0] Decode batch. #running-req: 1, #token: 2276, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.43, #queue-req: 0, 
[2025-07-28 01:17:05 DP1 TP0] Decode batch. #running-req: 1, #token: 1509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.90, #queue-req: 0, 
[2025-07-28 01:17:05 DP0 TP0] Decode batch. #running-req: 1, #token: 2316, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.47, #queue-req: 0, 
[2025-07-28 01:17:05 DP1 TP0] Decode batch. #running-req: 1, #token: 1549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.88, #queue-req: 0, 
[2025-07-28 01:17:05 DP0 TP0] Decode batch. #running-req: 1, #token: 2356, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.41, #queue-req: 0, 
[2025-07-28 01:17:05 DP1 TP0] Decode batch. #running-req: 1, #token: 1589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.76, #queue-req: 0, 
[2025-07-28 01:17:06 DP0 TP0] Decode batch. #running-req: 1, #token: 2396, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.45, #queue-req: 0, 
[2025-07-28 01:17:06 DP1 TP0] Decode batch. #running-req: 1, #token: 1629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.79, #queue-req: 0, 
[2025-07-28 01:17:06 DP0 TP0] Decode batch. #running-req: 1, #token: 2436, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.45, #queue-req: 0, 
[2025-07-28 01:17:06 DP1 TP0] Decode batch. #running-req: 1, #token: 1669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.78, #queue-req: 0, 
[2025-07-28 01:17:06 DP0 TP0] Decode batch. #running-req: 1, #token: 2476, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.43, #queue-req: 0, 
[2025-07-28 01:17:06 DP1 TP0] Decode batch. #running-req: 1, #token: 1709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.82, #queue-req: 0, 
[2025-07-28 01:17:06 DP0 TP0] Decode batch. #running-req: 1, #token: 2516, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.41, #queue-req: 0, 
[2025-07-28 01:17:07 DP1 TP0] Decode batch. #running-req: 1, #token: 1749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.79, #queue-req: 0, 
[2025-07-28 01:17:07 DP0 TP0] Decode batch. #running-req: 1, #token: 2556, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.39, #queue-req: 0, 
[2025-07-28 01:17:07 DP1 TP0] Decode batch. #running-req: 1, #token: 1789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.80, #queue-req: 0, 
[2025-07-28 01:17:07 DP0 TP0] Decode batch. #running-req: 1, #token: 2596, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.32, #queue-req: 0, 
[2025-07-28 01:17:07 DP1 TP0] Decode batch. #running-req: 1, #token: 1829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.80, #queue-req: 0, 
[2025-07-28 01:17:07 DP0 TP0] Decode batch. #running-req: 1, #token: 2636, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.31, #queue-req: 0, 
[2025-07-28 01:17:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.79, #queue-req: 0, 
[2025-07-28 01:17:08 DP0 TP0] Decode batch. #running-req: 1, #token: 2676, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.31, #queue-req: 0, 
[2025-07-28 01:17:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.77, #queue-req: 0, 
[2025-07-28 01:17:08 DP0 TP0] Decode batch. #running-req: 1, #token: 2716, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.35, #queue-req: 0, 
[2025-07-28 01:17:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1949, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.83, #queue-req: 0, 
[2025-07-28 01:17:08 DP0 TP0] Decode batch. #running-req: 1, #token: 2756, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.36, #queue-req: 0, 
[2025-07-28 01:17:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1989, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.82, #queue-req: 0, 
[2025-07-28 01:17:08 DP0 TP0] Decode batch. #running-req: 1, #token: 2796, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.34, #queue-req: 0, 
[2025-07-28 01:17:09 DP1 TP0] Decode batch. #running-req: 1, #token: 2029, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.73, #queue-req: 0, 
[2025-07-28 01:17:09 DP0 TP0] Decode batch. #running-req: 1, #token: 2836, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.23, #queue-req: 0, 
[2025-07-28 01:17:09 DP1 TP0] Decode batch. #running-req: 1, #token: 2069, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.67, #queue-req: 0, 
[2025-07-28 01:17:09 DP0 TP0] Decode batch. #running-req: 1, #token: 2876, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.33, #queue-req: 0, 
[2025-07-28 01:17:09 DP1 TP0] Decode batch. #running-req: 1, #token: 2109, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.54, #queue-req: 0, 
[2025-07-28 01:17:09 DP0 TP0] Decode batch. #running-req: 1, #token: 2916, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.35, #queue-req: 0, 
[2025-07-28 01:17:10 DP1 TP0] Decode batch. #running-req: 1, #token: 2149, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.52, #queue-req: 0, 
[2025-07-28 01:17:10 DP0 TP0] Decode batch. #running-req: 1, #token: 2956, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.31, #queue-req: 0, 
[2025-07-28 01:17:10 DP1 TP0] Decode batch. #running-req: 1, #token: 2189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.52, #queue-req: 0, 
[2025-07-28 01:17:10 DP0 TP0] Decode batch. #running-req: 1, #token: 2996, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.36, #queue-req: 0, 
[2025-07-28 01:17:10 DP1 TP0] Decode batch. #running-req: 1, #token: 2229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.52, #queue-req: 0, 
[2025-07-28 01:17:10 DP0 TP0] Decode batch. #running-req: 1, #token: 3036, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.36, #queue-req: 0, 
[2025-07-28 01:17:10 DP1 TP0] Decode batch. #running-req: 1, #token: 2269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.46, #queue-req: 0, 
[2025-07-28 01:17:10 DP0 TP0] Decode batch. #running-req: 1, #token: 3076, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.24, #queue-req: 0, 
[2025-07-28 01:17:11 DP1 TP0] Decode batch. #running-req: 1, #token: 2309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.41, #queue-req: 0, 
[2025-07-28 01:17:11 DP0 TP0] Decode batch. #running-req: 1, #token: 3116, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.20, #queue-req: 0, 
[2025-07-28 01:17:11 DP1 TP0] Decode batch. #running-req: 1, #token: 2349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.50, #queue-req: 0, 
[2025-07-28 01:17:11 DP0 TP0] Decode batch. #running-req: 1, #token: 3156, token usage: 0.00, cuda graph: True, gen throughput (token/s): 134.39, #queue-req: 0, 
[2025-07-28 01:17:11 DP1 TP0] Decode batch. #running-req: 1, #token: 2389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.43, #queue-req: 0, 
[2025-07-28 01:17:11 DP0 TP0] Decode batch. #running-req: 1, #token: 3196, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.72, #queue-req: 0, 
[2025-07-28 01:17:12 DP1 TP0] Decode batch. #running-req: 1, #token: 2429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.42, #queue-req: 0, 
[2025-07-28 01:17:12 DP0 TP0] Decode batch. #running-req: 1, #token: 3236, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.15, #queue-req: 0, 
[2025-07-28 01:17:12] INFO:     127.0.0.1:51432 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:17:12 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:17:12 DP1 TP0] Decode batch. #running-req: 1, #token: 2469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.46, #queue-req: 0, 
[2025-07-28 01:17:12 DP0 TP0] Decode batch. #running-req: 1, #token: 205, token usage: 0.00, cuda graph: True, gen throughput (token/s): 118.49, #queue-req: 0, 
[2025-07-28 01:17:12 DP1 TP0] Decode batch. #running-req: 1, #token: 2509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.37, #queue-req: 0, 
[2025-07-28 01:17:12 DP0 TP0] Decode batch. #running-req: 1, #token: 245, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.58, #queue-req: 0, 
[2025-07-28 01:17:12 DP1 TP0] Decode batch. #running-req: 1, #token: 2549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.47, #queue-req: 0, 
[2025-07-28 01:17:13 DP0 TP0] Decode batch. #running-req: 1, #token: 285, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.50, #queue-req: 0, 
[2025-07-28 01:17:13 DP1 TP0] Decode batch. #running-req: 1, #token: 2589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.38, #queue-req: 0, 
[2025-07-28 01:17:13 DP0 TP0] Decode batch. #running-req: 1, #token: 325, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.48, #queue-req: 0, 
[2025-07-28 01:17:13 DP1 TP0] Decode batch. #running-req: 1, #token: 2629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.30, #queue-req: 0, 
[2025-07-28 01:17:13 DP0 TP0] Decode batch. #running-req: 1, #token: 365, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.46, #queue-req: 0, 
[2025-07-28 01:17:13 DP1 TP0] Decode batch. #running-req: 1, #token: 2669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.38, #queue-req: 0, 
[2025-07-28 01:17:13 DP0 TP0] Decode batch. #running-req: 1, #token: 405, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.43, #queue-req: 0, 
[2025-07-28 01:17:14 DP1 TP0] Decode batch. #running-req: 1, #token: 2709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.41, #queue-req: 0, 
[2025-07-28 01:17:14 DP0 TP0] Decode batch. #running-req: 1, #token: 445, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:17:14 DP1 TP0] Decode batch. #running-req: 1, #token: 2749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.10, #queue-req: 0, 
[2025-07-28 01:17:14 DP0 TP0] Decode batch. #running-req: 1, #token: 485, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.42, #queue-req: 0, 
[2025-07-28 01:17:14 DP1 TP0] Decode batch. #running-req: 1, #token: 2789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.37, #queue-req: 0, 
[2025-07-28 01:17:14 DP0 TP0] Decode batch. #running-req: 1, #token: 525, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.37, #queue-req: 0, 
[2025-07-28 01:17:14 DP1 TP0] Decode batch. #running-req: 1, #token: 2829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.28, #queue-req: 0, 
[2025-07-28 01:17:15 DP0 TP0] Decode batch. #running-req: 1, #token: 565, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:17:15 DP1 TP0] Decode batch. #running-req: 1, #token: 2869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.36, #queue-req: 0, 
[2025-07-28 01:17:15 DP0 TP0] Decode batch. #running-req: 1, #token: 605, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.25, #queue-req: 0, 
[2025-07-28 01:17:15 DP1 TP0] Decode batch. #running-req: 1, #token: 2909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.37, #queue-req: 0, 
[2025-07-28 01:17:15 DP0 TP0] Decode batch. #running-req: 1, #token: 645, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.11, #queue-req: 0, 
[2025-07-28 01:17:15 DP1 TP0] Decode batch. #running-req: 1, #token: 2949, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.35, #queue-req: 0, 
[2025-07-28 01:17:15 DP0 TP0] Decode batch. #running-req: 1, #token: 685, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:17:16 DP1 TP0] Decode batch. #running-req: 1, #token: 2989, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.37, #queue-req: 0, 
[2025-07-28 01:17:16 DP0 TP0] Decode batch. #running-req: 1, #token: 725, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.28, #queue-req: 0, 
[2025-07-28 01:17:16 DP1 TP0] Decode batch. #running-req: 1, #token: 3029, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.40, #queue-req: 0, 
[2025-07-28 01:17:16 DP0 TP0] Decode batch. #running-req: 1, #token: 765, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.26, #queue-req: 0, 
[2025-07-28 01:17:16 DP1 TP0] Decode batch. #running-req: 1, #token: 3069, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.34, #queue-req: 0, 
[2025-07-28 01:17:16 DP0 TP0] Decode batch. #running-req: 1, #token: 805, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.22, #queue-req: 0, 
[2025-07-28 01:17:16 DP1 TP0] Decode batch. #running-req: 1, #token: 3109, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.20, #queue-req: 0, 
[2025-07-28 01:17:17 DP0 TP0] Decode batch. #running-req: 1, #token: 845, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.23, #queue-req: 0, 
[2025-07-28 01:17:17 DP1 TP0] Decode batch. #running-req: 1, #token: 3149, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.12, #queue-req: 0, 
[2025-07-28 01:17:17 DP0 TP0] Decode batch. #running-req: 1, #token: 885, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.23, #queue-req: 0, 
[2025-07-28 01:17:17 DP1 TP0] Decode batch. #running-req: 1, #token: 3189, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.20, #queue-req: 0, 
[2025-07-28 01:17:17 DP0 TP0] Decode batch. #running-req: 1, #token: 925, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:17:17 DP1 TP0] Decode batch. #running-req: 1, #token: 3229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.23, #queue-req: 0, 
[2025-07-28 01:17:17 DP0 TP0] Decode batch. #running-req: 1, #token: 965, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.25, #queue-req: 0, 
[2025-07-28 01:17:18 DP1 TP0] Decode batch. #running-req: 1, #token: 3269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.13, #queue-req: 0, 
[2025-07-28 01:17:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1005, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.24, #queue-req: 0, 
[2025-07-28 01:17:18 DP1 TP0] Decode batch. #running-req: 1, #token: 3309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.16, #queue-req: 0, 
[2025-07-28 01:17:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1045, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.14, #queue-req: 0, 
[2025-07-28 01:17:18 DP1 TP0] Decode batch. #running-req: 1, #token: 3349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.15, #queue-req: 0, 
[2025-07-28 01:17:18 DP0 TP0] Decode batch. #running-req: 1, #token: 1085, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.91, #queue-req: 0, 
[2025-07-28 01:17:18 DP1 TP0] Decode batch. #running-req: 1, #token: 3389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.18, #queue-req: 0, 
[2025-07-28 01:17:19 DP0 TP0] Decode batch. #running-req: 1, #token: 1125, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.97, #queue-req: 0, 
[2025-07-28 01:17:19] INFO:     127.0.0.1:60234 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:17:19 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 154, #cached-token: 88, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:17:19 DP1 TP0] Decode batch. #running-req: 2, #token: 3604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 177.66, #queue-req: 0, 
[2025-07-28 01:17:19 DP1 TP0] Decode batch. #running-req: 2, #token: 3684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.09, #queue-req: 0, 
[2025-07-28 01:17:19 DP1 TP0] Decode batch. #running-req: 2, #token: 3764, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.92, #queue-req: 0, 
[2025-07-28 01:17:20 DP1 TP0] Decode batch. #running-req: 2, #token: 3844, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.09, #queue-req: 0, 
[2025-07-28 01:17:20 DP1 TP0] Decode batch. #running-req: 2, #token: 3924, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.01, #queue-req: 0, 
[2025-07-28 01:17:20 DP1 TP0] Decode batch. #running-req: 2, #token: 4004, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.52, #queue-req: 0, 
[2025-07-28 01:17:21 DP1 TP0] Decode batch. #running-req: 2, #token: 4084, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.79, #queue-req: 0, 
[2025-07-28 01:17:21 DP1 TP0] Decode batch. #running-req: 2, #token: 4164, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.78, #queue-req: 0, 
[2025-07-28 01:17:21 DP1 TP0] Decode batch. #running-req: 2, #token: 4244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 268.06, #queue-req: 0, 
[2025-07-28 01:17:22 DP1 TP0] Decode batch. #running-req: 2, #token: 4324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.88, #queue-req: 0, 
[2025-07-28 01:17:22 DP1 TP0] Decode batch. #running-req: 2, #token: 4404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.75, #queue-req: 0, 
[2025-07-28 01:17:22 DP1 TP0] Decode batch. #running-req: 2, #token: 4484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.60, #queue-req: 0, 
[2025-07-28 01:17:22 DP1 TP0] Decode batch. #running-req: 2, #token: 4564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.37, #queue-req: 0, 
[2025-07-28 01:17:23 DP1 TP0] Decode batch. #running-req: 2, #token: 4644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.60, #queue-req: 0, 
[2025-07-28 01:17:23 DP1 TP0] Decode batch. #running-req: 2, #token: 4724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.38, #queue-req: 0, 
[2025-07-28 01:17:23 DP1 TP0] Decode batch. #running-req: 2, #token: 4804, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.31, #queue-req: 0, 
[2025-07-28 01:17:24 DP1 TP0] Decode batch. #running-req: 2, #token: 4884, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.23, #queue-req: 0, 
[2025-07-28 01:17:24 DP1 TP0] Decode batch. #running-req: 2, #token: 4964, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.90, #queue-req: 0, 
[2025-07-28 01:17:24 DP1 TP0] Decode batch. #running-req: 2, #token: 5044, token usage: 0.00, cuda graph: True, gen throughput (token/s): 267.03, #queue-req: 0, 
[2025-07-28 01:17:24 DP1 TP0] Decode batch. #running-req: 2, #token: 5124, token usage: 0.00, cuda graph: True, gen throughput (token/s): 266.43, #queue-req: 0, 
[2025-07-28 01:17:25] INFO:     127.0.0.1:40408 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:17:25 DP1 TP0] Decode batch. #running-req: 1, #token: 4229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 257.13, #queue-req: 0, 
[2025-07-28 01:17:25 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 128, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:17:25 DP1 TP0] Decode batch. #running-req: 1, #token: 4269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.74, #queue-req: 0, 
[2025-07-28 01:17:25 DP0 TP0] Decode batch. #running-req: 1, #token: 244, token usage: 0.00, cuda graph: True, gen throughput (token/s): 6.10, #queue-req: 0, 
[2025-07-28 01:17:25 DP0 TP0] Decode batch. #running-req: 1, #token: 284, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:17:25 DP1 TP0] Decode batch. #running-req: 1, #token: 4309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.64, #queue-req: 0, 
[2025-07-28 01:17:26 DP0 TP0] Decode batch. #running-req: 1, #token: 324, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.52, #queue-req: 0, 
[2025-07-28 01:17:26 DP1 TP0] Decode batch. #running-req: 1, #token: 4349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.63, #queue-req: 0, 
[2025-07-28 01:17:26 DP0 TP0] Decode batch. #running-req: 1, #token: 364, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.50, #queue-req: 0, 
[2025-07-28 01:17:26 DP1 TP0] Decode batch. #running-req: 1, #token: 4389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.64, #queue-req: 0, 
[2025-07-28 01:17:26 DP0 TP0] Decode batch. #running-req: 1, #token: 404, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.48, #queue-req: 0, 
[2025-07-28 01:17:26 DP1 TP0] Decode batch. #running-req: 1, #token: 4429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.64, #queue-req: 0, 
[2025-07-28 01:17:27 DP0 TP0] Decode batch. #running-req: 1, #token: 444, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.51, #queue-req: 0, 
[2025-07-28 01:17:27 DP1 TP0] Decode batch. #running-req: 1, #token: 4469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.59, #queue-req: 0, 
[2025-07-28 01:17:27 DP0 TP0] Decode batch. #running-req: 1, #token: 484, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.45, #queue-req: 0, 
[2025-07-28 01:17:27 DP1 TP0] Decode batch. #running-req: 1, #token: 4509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.59, #queue-req: 0, 
[2025-07-28 01:17:27 DP0 TP0] Decode batch. #running-req: 1, #token: 524, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:17:27 DP1 TP0] Decode batch. #running-req: 1, #token: 4549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.58, #queue-req: 0, 
[2025-07-28 01:17:27 DP0 TP0] Decode batch. #running-req: 1, #token: 564, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:17:27 DP1 TP0] Decode batch. #running-req: 1, #token: 4589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 137.55, #queue-req: 0, 
[2025-07-28 01:17:28] INFO:     127.0.0.1:37684 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:17:28 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:17:28 DP0 TP0] Decode batch. #running-req: 1, #token: 604, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:17:28 DP1 TP0] Decode batch. #running-req: 1, #token: 246, token usage: 0.00, cuda graph: True, gen throughput (token/s): 117.65, #queue-req: 0, 
[2025-07-28 01:17:28 DP0 TP0] Decode batch. #running-req: 1, #token: 644, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.28, #queue-req: 0, 
[2025-07-28 01:17:28 DP1 TP0] Decode batch. #running-req: 1, #token: 286, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.58, #queue-req: 0, 
[2025-07-28 01:17:28 DP0 TP0] Decode batch. #running-req: 1, #token: 684, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:17:28 DP1 TP0] Decode batch. #running-req: 1, #token: 326, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.57, #queue-req: 0, 
[2025-07-28 01:17:29 DP0 TP0] Decode batch. #running-req: 1, #token: 724, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:17:29 DP1 TP0] Decode batch. #running-req: 1, #token: 366, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.56, #queue-req: 0, 
[2025-07-28 01:17:29 DP0 TP0] Decode batch. #running-req: 1, #token: 764, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:17:29 DP1 TP0] Decode batch. #running-req: 1, #token: 406, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.59, #queue-req: 0, 
[2025-07-28 01:17:29 DP0 TP0] Decode batch. #running-req: 1, #token: 804, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.28, #queue-req: 0, 
[2025-07-28 01:17:29 DP1 TP0] Decode batch. #running-req: 1, #token: 446, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.53, #queue-req: 0, 
[2025-07-28 01:17:29 DP0 TP0] Decode batch. #running-req: 1, #token: 844, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.24, #queue-req: 0, 
[2025-07-28 01:17:29 DP1 TP0] Decode batch. #running-req: 1, #token: 486, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.53, #queue-req: 0, 
[2025-07-28 01:17:30 DP0 TP0] Decode batch. #running-req: 1, #token: 884, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.28, #queue-req: 0, 
[2025-07-28 01:17:30 DP1 TP0] Decode batch. #running-req: 1, #token: 526, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.51, #queue-req: 0, 
[2025-07-28 01:17:30 DP0 TP0] Decode batch. #running-req: 1, #token: 924, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:17:30 DP1 TP0] Decode batch. #running-req: 1, #token: 566, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.42, #queue-req: 0, 
[2025-07-28 01:17:30 DP0 TP0] Decode batch. #running-req: 1, #token: 964, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:17:30 DP1 TP0] Decode batch. #running-req: 1, #token: 606, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.40, #queue-req: 0, 
[2025-07-28 01:17:31 DP0 TP0] Decode batch. #running-req: 1, #token: 1004, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.30, #queue-req: 0, 
[2025-07-28 01:17:31 DP1 TP0] Decode batch. #running-req: 1, #token: 646, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.43, #queue-req: 0, 
[2025-07-28 01:17:31] INFO:     127.0.0.1:35476 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:17:31 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 88, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:17:31 DP0 TP0] Decode batch. #running-req: 1, #token: 231, token usage: 0.00, cuda graph: True, gen throughput (token/s): 122.26, #queue-req: 0, 
[2025-07-28 01:17:31 DP1 TP0] Decode batch. #running-req: 1, #token: 686, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.42, #queue-req: 0, 
[2025-07-28 01:17:31 DP0 TP0] Decode batch. #running-req: 1, #token: 271, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.51, #queue-req: 0, 
[2025-07-28 01:17:31 DP1 TP0] Decode batch. #running-req: 1, #token: 726, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.42, #queue-req: 0, 
[2025-07-28 01:17:31 DP0 TP0] Decode batch. #running-req: 1, #token: 311, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.52, #queue-req: 0, 
[2025-07-28 01:17:31 DP1 TP0] Decode batch. #running-req: 1, #token: 766, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.40, #queue-req: 0, 
[2025-07-28 01:17:32 DP0 TP0] Decode batch. #running-req: 1, #token: 351, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.48, #queue-req: 0, 
[2025-07-28 01:17:32 DP1 TP0] Decode batch. #running-req: 1, #token: 806, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.45, #queue-req: 0, 
[2025-07-28 01:17:32 DP0 TP0] Decode batch. #running-req: 1, #token: 391, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.46, #queue-req: 0, 
[2025-07-28 01:17:32 DP1 TP0] Decode batch. #running-req: 1, #token: 846, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.38, #queue-req: 0, 
[2025-07-28 01:17:32 DP0 TP0] Decode batch. #running-req: 1, #token: 431, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.45, #queue-req: 0, 
[2025-07-28 01:17:32 DP1 TP0] Decode batch. #running-req: 1, #token: 886, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.30, #queue-req: 0, 
[2025-07-28 01:17:33 DP0 TP0] Decode batch. #running-req: 1, #token: 471, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.39, #queue-req: 0, 
[2025-07-28 01:17:33 DP1 TP0] Decode batch. #running-req: 1, #token: 926, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.42, #queue-req: 0, 
[2025-07-28 01:17:33 DP0 TP0] Decode batch. #running-req: 1, #token: 511, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.38, #queue-req: 0, 
[2025-07-28 01:17:33 DP1 TP0] Decode batch. #running-req: 1, #token: 966, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:17:33] INFO:     127.0.0.1:35482 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:17:33 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 95, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:17:33 DP0 TP0] Decode batch. #running-req: 1, #token: 551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:17:33 DP1 TP0] Decode batch. #running-req: 1, #token: 214, token usage: 0.00, cuda graph: True, gen throughput (token/s): 120.28, #queue-req: 0, 
[2025-07-28 01:17:33 DP0 TP0] Decode batch. #running-req: 1, #token: 591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.19, #queue-req: 0, 
[2025-07-28 01:17:34 DP1 TP0] Decode batch. #running-req: 1, #token: 254, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.54, #queue-req: 0, 
[2025-07-28 01:17:34 DP0 TP0] Decode batch. #running-req: 1, #token: 631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:17:34 DP1 TP0] Decode batch. #running-req: 1, #token: 294, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.60, #queue-req: 0, 
[2025-07-28 01:17:34 DP0 TP0] Decode batch. #running-req: 1, #token: 671, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.27, #queue-req: 0, 
[2025-07-28 01:17:34 DP1 TP0] Decode batch. #running-req: 1, #token: 334, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.58, #queue-req: 0, 
[2025-07-28 01:17:34 DP0 TP0] Decode batch. #running-req: 1, #token: 711, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.18, #queue-req: 0, 
[2025-07-28 01:17:34 DP1 TP0] Decode batch. #running-req: 1, #token: 374, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.53, #queue-req: 0, 
[2025-07-28 01:17:35 DP0 TP0] Decode batch. #running-req: 1, #token: 751, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:17:35 DP1 TP0] Decode batch. #running-req: 1, #token: 414, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.57, #queue-req: 0, 
[2025-07-28 01:17:35 DP0 TP0] Decode batch. #running-req: 1, #token: 791, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.27, #queue-req: 0, 
[2025-07-28 01:17:35 DP1 TP0] Decode batch. #running-req: 1, #token: 454, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:17:35 DP0 TP0] Decode batch. #running-req: 1, #token: 831, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.24, #queue-req: 0, 
[2025-07-28 01:17:35 DP1 TP0] Decode batch. #running-req: 1, #token: 494, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.50, #queue-req: 0, 
[2025-07-28 01:17:35 DP0 TP0] Decode batch. #running-req: 1, #token: 871, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.18, #queue-req: 0, 
[2025-07-28 01:17:36 DP1 TP0] Decode batch. #running-req: 1, #token: 534, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.47, #queue-req: 0, 
[2025-07-28 01:17:36 DP0 TP0] Decode batch. #running-req: 1, #token: 911, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.24, #queue-req: 0, 
[2025-07-28 01:17:36 DP1 TP0] Decode batch. #running-req: 1, #token: 574, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.40, #queue-req: 0, 
[2025-07-28 01:17:36 DP0 TP0] Decode batch. #running-req: 1, #token: 951, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.23, #queue-req: 0, 
[2025-07-28 01:17:36 DP1 TP0] Decode batch. #running-req: 1, #token: 614, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.40, #queue-req: 0, 
[2025-07-28 01:17:36 DP0 TP0] Decode batch. #running-req: 1, #token: 991, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.25, #queue-req: 0, 
[2025-07-28 01:17:36 DP1 TP0] Decode batch. #running-req: 1, #token: 654, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:17:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1031, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.20, #queue-req: 0, 
[2025-07-28 01:17:37 DP1 TP0] Decode batch. #running-req: 1, #token: 694, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.27, #queue-req: 0, 
[2025-07-28 01:17:37 DP0 TP0] Decode batch. #running-req: 1, #token: 1071, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.97, #queue-req: 0, 
[2025-07-28 01:17:37 DP1 TP0] Decode batch. #running-req: 1, #token: 734, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.39, #queue-req: 0, 
[2025-07-28 01:17:37] INFO:     127.0.0.1:51170 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:17:37 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 86, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:17:37 DP0 TP0] Decode batch. #running-req: 2, #token: 1256, token usage: 0.00, cuda graph: True, gen throughput (token/s): 136.50, #queue-req: 0, 
[2025-07-28 01:17:38 DP0 TP0] Decode batch. #running-req: 2, #token: 1336, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.82, #queue-req: 0, 
[2025-07-28 01:17:38 DP0 TP0] Decode batch. #running-req: 2, #token: 1416, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.79, #queue-req: 0, 
[2025-07-28 01:17:38 DP0 TP0] Decode batch. #running-req: 2, #token: 1496, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.65, #queue-req: 0, 
[2025-07-28 01:17:38 DP0 TP0] Decode batch. #running-req: 2, #token: 1576, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.63, #queue-req: 0, 
[2025-07-28 01:17:39 DP0 TP0] Decode batch. #running-req: 2, #token: 1656, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.49, #queue-req: 0, 
[2025-07-28 01:17:39 DP0 TP0] Decode batch. #running-req: 2, #token: 1736, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.52, #queue-req: 0, 
[2025-07-28 01:17:39 DP0 TP0] Decode batch. #running-req: 2, #token: 1816, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.46, #queue-req: 0, 
[2025-07-28 01:17:39] INFO:     127.0.0.1:35492 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:17:39 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 78, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:17:40 DP0 TP0] Decode batch. #running-req: 1, #token: 551, token usage: 0.00, cuda graph: True, gen throughput (token/s): 172.61, #queue-req: 0, 
[2025-07-28 01:17:40 DP1 TP0] Decode batch. #running-req: 1, #token: 181, token usage: 0.00, cuda graph: True, gen throughput (token/s): 15.32, #queue-req: 0, 
[2025-07-28 01:17:40 DP0 TP0] Decode batch. #running-req: 1, #token: 591, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.27, #queue-req: 0, 
[2025-07-28 01:17:40 DP1 TP0] Decode batch. #running-req: 1, #token: 221, token usage: 0.00, cuda graph: True, gen throughput (token/s): 134.65, #queue-req: 0, 
[2025-07-28 01:17:40 DP0 TP0] Decode batch. #running-req: 1, #token: 631, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.24, #queue-req: 0, 
[2025-07-28 01:17:40 DP1 TP0] Decode batch. #running-req: 1, #token: 261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.56, #queue-req: 0, 
[2025-07-28 01:17:40 DP0 TP0] Decode batch. #running-req: 1, #token: 671, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.28, #queue-req: 0, 
[2025-07-28 01:17:40 DP1 TP0] Decode batch. #running-req: 1, #token: 301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.55, #queue-req: 0, 
[2025-07-28 01:17:41 DP0 TP0] Decode batch. #running-req: 1, #token: 711, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.24, #queue-req: 0, 
[2025-07-28 01:17:41 DP1 TP0] Decode batch. #running-req: 1, #token: 341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.56, #queue-req: 0, 
[2025-07-28 01:17:41 DP0 TP0] Decode batch. #running-req: 1, #token: 751, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.27, #queue-req: 0, 
[2025-07-28 01:17:41 DP1 TP0] Decode batch. #running-req: 1, #token: 381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.46, #queue-req: 0, 
[2025-07-28 01:17:41 DP0 TP0] Decode batch. #running-req: 1, #token: 791, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.21, #queue-req: 0, 
[2025-07-28 01:17:41 DP1 TP0] Decode batch. #running-req: 1, #token: 421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.55, #queue-req: 0, 
[2025-07-28 01:17:42 DP0 TP0] Decode batch. #running-req: 1, #token: 831, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.13, #queue-req: 0, 
[2025-07-28 01:17:42 DP1 TP0] Decode batch. #running-req: 1, #token: 461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.48, #queue-req: 0, 
[2025-07-28 01:17:42 DP0 TP0] Decode batch. #running-req: 1, #token: 871, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.23, #queue-req: 0, 
[2025-07-28 01:17:42 DP1 TP0] Decode batch. #running-req: 1, #token: 501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.55, #queue-req: 0, 
[2025-07-28 01:17:42 DP0 TP0] Decode batch. #running-req: 1, #token: 911, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.26, #queue-req: 0, 
[2025-07-28 01:17:42 DP1 TP0] Decode batch. #running-req: 1, #token: 541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:17:42 DP0 TP0] Decode batch. #running-req: 1, #token: 951, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.26, #queue-req: 0, 
[2025-07-28 01:17:42 DP1 TP0] Decode batch. #running-req: 1, #token: 581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:17:43 DP0 TP0] Decode batch. #running-req: 1, #token: 991, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.21, #queue-req: 0, 
[2025-07-28 01:17:43 DP1 TP0] Decode batch. #running-req: 1, #token: 621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:17:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1031, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.21, #queue-req: 0, 
[2025-07-28 01:17:43 DP1 TP0] Decode batch. #running-req: 1, #token: 661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:17:43 DP0 TP0] Decode batch. #running-req: 1, #token: 1071, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.83, #queue-req: 0, 
[2025-07-28 01:17:43 DP1 TP0] Decode batch. #running-req: 1, #token: 701, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:17:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1111, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.94, #queue-req: 0, 
[2025-07-28 01:17:44 DP1 TP0] Decode batch. #running-req: 1, #token: 741, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:17:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1151, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.94, #queue-req: 0, 
[2025-07-28 01:17:44 DP1 TP0] Decode batch. #running-req: 1, #token: 781, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:17:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1191, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.84, #queue-req: 0, 
[2025-07-28 01:17:44 DP1 TP0] Decode batch. #running-req: 1, #token: 821, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.37, #queue-req: 0, 
[2025-07-28 01:17:44 DP0 TP0] Decode batch. #running-req: 1, #token: 1231, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.83, #queue-req: 0, 
[2025-07-28 01:17:44 DP1 TP0] Decode batch. #running-req: 1, #token: 861, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:17:45 DP1 TP0] Decode batch. #running-req: 1, #token: 901, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.28, #queue-req: 0, 
[2025-07-28 01:17:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1271, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.89, #queue-req: 0, 
[2025-07-28 01:17:45 DP1 TP0] Decode batch. #running-req: 1, #token: 941, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.27, #queue-req: 0, 
[2025-07-28 01:17:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1311, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.76, #queue-req: 0, 
[2025-07-28 01:17:45 DP1 TP0] Decode batch. #running-req: 1, #token: 981, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.38, #queue-req: 0, 
[2025-07-28 01:17:45 DP0 TP0] Decode batch. #running-req: 1, #token: 1351, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.84, #queue-req: 0, 
[2025-07-28 01:17:45] INFO:     127.0.0.1:51172 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:17:45 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 146, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:17:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1021, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:17:46 DP0 TP0] Decode batch. #running-req: 1, #token: 261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 119.78, #queue-req: 0, 
[2025-07-28 01:17:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1061, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.03, #queue-req: 0, 
[2025-07-28 01:17:46 DP0 TP0] Decode batch. #running-req: 1, #token: 301, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.54, #queue-req: 0, 
[2025-07-28 01:17:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1101, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.04, #queue-req: 0, 
[2025-07-28 01:17:46 DP0 TP0] Decode batch. #running-req: 1, #token: 341, token usage: 0.00, cuda graph: True, gen throughput (token/s): 134.81, #queue-req: 0, 
[2025-07-28 01:17:46 DP1 TP0] Decode batch. #running-req: 1, #token: 1141, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.05, #queue-req: 0, 
[2025-07-28 01:17:47 DP0 TP0] Decode batch. #running-req: 1, #token: 381, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.80, #queue-req: 0, 
[2025-07-28 01:17:47 DP1 TP0] Decode batch. #running-req: 1, #token: 1181, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.03, #queue-req: 0, 
[2025-07-28 01:17:47 DP0 TP0] Decode batch. #running-req: 1, #token: 421, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.47, #queue-req: 0, 
[2025-07-28 01:17:47 DP1 TP0] Decode batch. #running-req: 1, #token: 1221, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.04, #queue-req: 0, 
[2025-07-28 01:17:47 DP0 TP0] Decode batch. #running-req: 1, #token: 461, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.51, #queue-req: 0, 
[2025-07-28 01:17:47 DP1 TP0] Decode batch. #running-req: 1, #token: 1261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.01, #queue-req: 0, 
[2025-07-28 01:17:47 DP0 TP0] Decode batch. #running-req: 1, #token: 501, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:17:48] INFO:     127.0.0.1:51176 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:17:48 DP1 TP0] Decode batch. #running-req: 1, #token: 0, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.07, #queue-req: 0, 
[2025-07-28 01:17:48 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 84, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:17:48 DP0 TP0] Decode batch. #running-req: 1, #token: 541, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.39, #queue-req: 0, 
[2025-07-28 01:17:48 DP1 TP0] Decode batch. #running-req: 1, #token: 212, token usage: 0.00, cuda graph: True, gen throughput (token/s): 120.54, #queue-req: 0, 
[2025-07-28 01:17:48 DP0 TP0] Decode batch. #running-req: 1, #token: 581, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:17:48 DP1 TP0] Decode batch. #running-req: 1, #token: 252, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.59, #queue-req: 0, 
[2025-07-28 01:17:48 DP0 TP0] Decode batch. #running-req: 1, #token: 621, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:17:49 DP1 TP0] Decode batch. #running-req: 1, #token: 292, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.57, #queue-req: 0, 
[2025-07-28 01:17:49 DP0 TP0] Decode batch. #running-req: 1, #token: 661, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:17:49 DP1 TP0] Decode batch. #running-req: 1, #token: 332, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.56, #queue-req: 0, 
[2025-07-28 01:17:49 DP0 TP0] Decode batch. #running-req: 1, #token: 701, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:17:49 DP1 TP0] Decode batch. #running-req: 1, #token: 372, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.54, #queue-req: 0, 
[2025-07-28 01:17:49 DP0 TP0] Decode batch. #running-req: 1, #token: 741, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:17:49 DP1 TP0] Decode batch. #running-req: 1, #token: 412, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.53, #queue-req: 0, 
[2025-07-28 01:17:49 DP0 TP0] Decode batch. #running-req: 1, #token: 781, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:17:50 DP1 TP0] Decode batch. #running-req: 1, #token: 452, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.51, #queue-req: 0, 
[2025-07-28 01:17:50 DP0 TP0] Decode batch. #running-req: 1, #token: 821, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:17:50 DP1 TP0] Decode batch. #running-req: 1, #token: 492, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.48, #queue-req: 0, 
[2025-07-28 01:17:50 DP0 TP0] Decode batch. #running-req: 1, #token: 861, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.22, #queue-req: 0, 
[2025-07-28 01:17:50 DP1 TP0] Decode batch. #running-req: 1, #token: 532, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:17:50 DP0 TP0] Decode batch. #running-req: 1, #token: 901, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.28, #queue-req: 0, 
[2025-07-28 01:17:51 DP1 TP0] Decode batch. #running-req: 1, #token: 572, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:17:51 DP0 TP0] Decode batch. #running-req: 1, #token: 941, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.25, #queue-req: 0, 
[2025-07-28 01:17:51 DP1 TP0] Decode batch. #running-req: 1, #token: 612, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.37, #queue-req: 0, 
[2025-07-28 01:17:51 DP0 TP0] Decode batch. #running-req: 1, #token: 981, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.27, #queue-req: 0, 
[2025-07-28 01:17:51 DP1 TP0] Decode batch. #running-req: 1, #token: 652, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:17:51 DP0 TP0] Decode batch. #running-req: 1, #token: 1021, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.28, #queue-req: 0, 
[2025-07-28 01:17:51 DP1 TP0] Decode batch. #running-req: 1, #token: 692, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.39, #queue-req: 0, 
[2025-07-28 01:17:51 DP0 TP0] Decode batch. #running-req: 1, #token: 1061, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.04, #queue-req: 0, 
[2025-07-28 01:17:52 DP1 TP0] Decode batch. #running-req: 1, #token: 732, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:17:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1101, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.94, #queue-req: 0, 
[2025-07-28 01:17:52 DP1 TP0] Decode batch. #running-req: 1, #token: 772, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.28, #queue-req: 0, 
[2025-07-28 01:17:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1141, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.96, #queue-req: 0, 
[2025-07-28 01:17:52 DP1 TP0] Decode batch. #running-req: 1, #token: 812, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:17:52 DP0 TP0] Decode batch. #running-req: 1, #token: 1181, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.92, #queue-req: 0, 
[2025-07-28 01:17:53 DP1 TP0] Decode batch. #running-req: 1, #token: 852, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.30, #queue-req: 0, 
[2025-07-28 01:17:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1221, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.89, #queue-req: 0, 
[2025-07-28 01:17:53 DP1 TP0] Decode batch. #running-req: 1, #token: 892, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:17:53 DP0 TP0] Decode batch. #running-req: 1, #token: 1261, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.89, #queue-req: 0, 
[2025-07-28 01:17:53] INFO:     127.0.0.1:58604 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:17:53 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 173, #cached-token: 87, token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 01:17:53 DP0 TP0] Decode batch. #running-req: 2, #token: 1477, token usage: 0.00, cuda graph: True, gen throughput (token/s): 129.86, #queue-req: 0, 
[2025-07-28 01:17:53 DP0 TP0] Decode batch. #running-req: 2, #token: 1557, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.97, #queue-req: 0, 
[2025-07-28 01:17:54 DP0 TP0] Decode batch. #running-req: 2, #token: 1637, token usage: 0.00, cuda graph: True, gen throughput (token/s): 269.80, #queue-req: 0, 
[2025-07-28 01:17:54] INFO:     127.0.0.1:58598 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:17:54 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 178, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:17:54 DP1 TP0] Decode batch. #running-req: 1, #token: 268, token usage: 0.00, cuda graph: True, gen throughput (token/s): 33.18, #queue-req: 0, 
[2025-07-28 01:17:54 DP0 TP0] Decode batch. #running-req: 1, #token: 383, token usage: 0.00, cuda graph: True, gen throughput (token/s): 218.59, #queue-req: 0, 
[2025-07-28 01:17:54 DP1 TP0] Decode batch. #running-req: 1, #token: 308, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.40, #queue-req: 0, 
[2025-07-28 01:17:54 DP0 TP0] Decode batch. #running-req: 1, #token: 423, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.56, #queue-req: 0, 
[2025-07-28 01:17:55 DP1 TP0] Decode batch. #running-req: 1, #token: 348, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.46, #queue-req: 0, 
[2025-07-28 01:17:55 DP0 TP0] Decode batch. #running-req: 1, #token: 463, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.53, #queue-req: 0, 
[2025-07-28 01:17:55 DP1 TP0] Decode batch. #running-req: 1, #token: 388, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.52, #queue-req: 0, 
[2025-07-28 01:17:55 DP0 TP0] Decode batch. #running-req: 1, #token: 503, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.49, #queue-req: 0, 
[2025-07-28 01:17:55 DP1 TP0] Decode batch. #running-req: 1, #token: 428, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.47, #queue-req: 0, 
[2025-07-28 01:17:55 DP0 TP0] Decode batch. #running-req: 1, #token: 543, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.40, #queue-req: 0, 
[2025-07-28 01:17:55 DP1 TP0] Decode batch. #running-req: 1, #token: 468, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.47, #queue-req: 0, 
[2025-07-28 01:17:56 DP0 TP0] Decode batch. #running-req: 1, #token: 583, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:17:56 DP1 TP0] Decode batch. #running-req: 1, #token: 508, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.48, #queue-req: 0, 
[2025-07-28 01:17:56 DP0 TP0] Decode batch. #running-req: 1, #token: 623, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:17:56 DP1 TP0] Decode batch. #running-req: 1, #token: 548, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.37, #queue-req: 0, 
[2025-07-28 01:17:56 DP0 TP0] Decode batch. #running-req: 1, #token: 663, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.39, #queue-req: 0, 
[2025-07-28 01:17:56 DP1 TP0] Decode batch. #running-req: 1, #token: 588, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.37, #queue-req: 0, 
[2025-07-28 01:17:56 DP0 TP0] Decode batch. #running-req: 1, #token: 703, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.49, #queue-req: 0, 
[2025-07-28 01:17:57 DP1 TP0] Decode batch. #running-req: 1, #token: 628, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.37, #queue-req: 0, 
[2025-07-28 01:17:57 DP0 TP0] Decode batch. #running-req: 1, #token: 743, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.39, #queue-req: 0, 
[2025-07-28 01:17:57 DP1 TP0] Decode batch. #running-req: 1, #token: 668, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:17:57 DP0 TP0] Decode batch. #running-req: 1, #token: 783, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:17:57 DP1 TP0] Decode batch. #running-req: 1, #token: 708, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.19, #queue-req: 0, 
[2025-07-28 01:17:57 DP0 TP0] Decode batch. #running-req: 1, #token: 823, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.02, #queue-req: 0, 
[2025-07-28 01:17:57 DP1 TP0] Decode batch. #running-req: 1, #token: 748, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:17:58 DP0 TP0] Decode batch. #running-req: 1, #token: 863, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:17:58 DP1 TP0] Decode batch. #running-req: 1, #token: 788, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.02, #queue-req: 0, 
[2025-07-28 01:17:58 DP0 TP0] Decode batch. #running-req: 1, #token: 903, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:17:58] INFO:     127.0.0.1:33400 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:17:58 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 88, #cached-token: 85, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:17:58 DP1 TP0] Decode batch. #running-req: 1, #token: 828, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:17:58 DP0 TP0] Decode batch. #running-req: 1, #token: 194, token usage: 0.00, cuda graph: True, gen throughput (token/s): 120.19, #queue-req: 0, 
[2025-07-28 01:17:58 DP1 TP0] Decode batch. #running-req: 1, #token: 868, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:17:58 DP0 TP0] Decode batch. #running-req: 1, #token: 234, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.60, #queue-req: 0, 
[2025-07-28 01:17:59 DP1 TP0] Decode batch. #running-req: 1, #token: 908, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:17:59 DP0 TP0] Decode batch. #running-req: 1, #token: 274, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.50, #queue-req: 0, 
[2025-07-28 01:17:59 DP1 TP0] Decode batch. #running-req: 1, #token: 948, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.27, #queue-req: 0, 
[2025-07-28 01:17:59 DP0 TP0] Decode batch. #running-req: 1, #token: 314, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.50, #queue-req: 0, 
[2025-07-28 01:17:59 DP1 TP0] Decode batch. #running-req: 1, #token: 988, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.94, #queue-req: 0, 
[2025-07-28 01:17:59 DP0 TP0] Decode batch. #running-req: 1, #token: 354, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:18:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1028, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.28, #queue-req: 0, 
[2025-07-28 01:18:00 DP0 TP0] Decode batch. #running-req: 1, #token: 394, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:18:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1068, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.97, #queue-req: 0, 
[2025-07-28 01:18:00 DP0 TP0] Decode batch. #running-req: 1, #token: 434, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:18:00 DP1 TP0] Decode batch. #running-req: 1, #token: 1108, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.98, #queue-req: 0, 
[2025-07-28 01:18:00 DP0 TP0] Decode batch. #running-req: 1, #token: 474, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:18:00] INFO:     127.0.0.1:33416 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:18:00 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 153, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:18:00 DP1 TP0] Decode batch. #running-req: 1, #token: 266, token usage: 0.00, cuda graph: True, gen throughput (token/s): 118.16, #queue-req: 0, 
[2025-07-28 01:18:00 DP0 TP0] Decode batch. #running-req: 1, #token: 514, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:18:01 DP1 TP0] Decode batch. #running-req: 1, #token: 306, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.58, #queue-req: 0, 
[2025-07-28 01:18:01 DP0 TP0] Decode batch. #running-req: 1, #token: 554, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:18:01 DP1 TP0] Decode batch. #running-req: 1, #token: 346, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.59, #queue-req: 0, 
[2025-07-28 01:18:01 DP0 TP0] Decode batch. #running-req: 1, #token: 594, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:18:01 DP1 TP0] Decode batch. #running-req: 1, #token: 386, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.55, #queue-req: 0, 
[2025-07-28 01:18:01 DP0 TP0] Decode batch. #running-req: 1, #token: 634, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.30, #queue-req: 0, 
[2025-07-28 01:18:02 DP1 TP0] Decode batch. #running-req: 1, #token: 426, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.54, #queue-req: 0, 
[2025-07-28 01:18:02 DP0 TP0] Decode batch. #running-req: 1, #token: 674, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:18:02 DP1 TP0] Decode batch. #running-req: 1, #token: 466, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.50, #queue-req: 0, 
[2025-07-28 01:18:02 DP0 TP0] Decode batch. #running-req: 1, #token: 714, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:18:02 DP1 TP0] Decode batch. #running-req: 1, #token: 506, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.52, #queue-req: 0, 
[2025-07-28 01:18:02 DP0 TP0] Decode batch. #running-req: 1, #token: 754, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:18:02 DP1 TP0] Decode batch. #running-req: 1, #token: 546, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.43, #queue-req: 0, 
[2025-07-28 01:18:02 DP0 TP0] Decode batch. #running-req: 1, #token: 794, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.25, #queue-req: 0, 
[2025-07-28 01:18:03 DP1 TP0] Decode batch. #running-req: 1, #token: 586, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.38, #queue-req: 0, 
[2025-07-28 01:18:03 DP0 TP0] Decode batch. #running-req: 1, #token: 834, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.23, #queue-req: 0, 
[2025-07-28 01:18:03 DP1 TP0] Decode batch. #running-req: 1, #token: 626, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:18:03 DP0 TP0] Decode batch. #running-req: 1, #token: 874, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.28, #queue-req: 0, 
[2025-07-28 01:18:03 DP1 TP0] Decode batch. #running-req: 1, #token: 666, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.45, #queue-req: 0, 
[2025-07-28 01:18:03 DP0 TP0] Decode batch. #running-req: 1, #token: 914, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.26, #queue-req: 0, 
[2025-07-28 01:18:04 DP1 TP0] Decode batch. #running-req: 1, #token: 706, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.43, #queue-req: 0, 
[2025-07-28 01:18:04 DP0 TP0] Decode batch. #running-req: 1, #token: 954, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.28, #queue-req: 0, 
[2025-07-28 01:18:04] INFO:     127.0.0.1:33432 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:18:04 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 123, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:18:04 DP1 TP0] Decode batch. #running-req: 1, #token: 746, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.42, #queue-req: 0, 
[2025-07-28 01:18:04 DP0 TP0] Decode batch. #running-req: 1, #token: 232, token usage: 0.00, cuda graph: True, gen throughput (token/s): 119.05, #queue-req: 0, 
[2025-07-28 01:18:04 DP1 TP0] Decode batch. #running-req: 1, #token: 786, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.40, #queue-req: 0, 
[2025-07-28 01:18:04 DP0 TP0] Decode batch. #running-req: 1, #token: 272, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.55, #queue-req: 0, 
[2025-07-28 01:18:04 DP1 TP0] Decode batch. #running-req: 1, #token: 826, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:18:05 DP0 TP0] Decode batch. #running-req: 1, #token: 312, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.51, #queue-req: 0, 
[2025-07-28 01:18:05 DP1 TP0] Decode batch. #running-req: 1, #token: 866, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:18:05 DP0 TP0] Decode batch. #running-req: 1, #token: 352, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.57, #queue-req: 0, 
[2025-07-28 01:18:05 DP1 TP0] Decode batch. #running-req: 1, #token: 906, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:18:05 DP0 TP0] Decode batch. #running-req: 1, #token: 392, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.51, #queue-req: 0, 
[2025-07-28 01:18:05 DP1 TP0] Decode batch. #running-req: 1, #token: 946, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:18:05 DP0 TP0] Decode batch. #running-req: 1, #token: 432, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.48, #queue-req: 0, 
[2025-07-28 01:18:06 DP1 TP0] Decode batch. #running-req: 1, #token: 986, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.39, #queue-req: 0, 
[2025-07-28 01:18:06 DP0 TP0] Decode batch. #running-req: 1, #token: 472, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.50, #queue-req: 0, 
[2025-07-28 01:18:06 DP1 TP0] Decode batch. #running-req: 1, #token: 1026, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.39, #queue-req: 0, 
[2025-07-28 01:18:06 DP0 TP0] Decode batch. #running-req: 1, #token: 512, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:18:06 DP1 TP0] Decode batch. #running-req: 1, #token: 1066, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.13, #queue-req: 0, 
[2025-07-28 01:18:06 DP0 TP0] Decode batch. #running-req: 1, #token: 552, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:18:06 DP1 TP0] Decode batch. #running-req: 1, #token: 1106, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.98, #queue-req: 0, 
[2025-07-28 01:18:07 DP0 TP0] Decode batch. #running-req: 1, #token: 592, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:18:07 DP1 TP0] Decode batch. #running-req: 1, #token: 1146, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.96, #queue-req: 0, 
[2025-07-28 01:18:07 DP0 TP0] Decode batch. #running-req: 1, #token: 632, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.25, #queue-req: 0, 
[2025-07-28 01:18:07 DP1 TP0] Decode batch. #running-req: 1, #token: 1186, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.16, #queue-req: 0, 
[2025-07-28 01:18:07 DP0 TP0] Decode batch. #running-req: 1, #token: 672, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:18:07 DP1 TP0] Decode batch. #running-req: 1, #token: 1226, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.08, #queue-req: 0, 
[2025-07-28 01:18:07 DP0 TP0] Decode batch. #running-req: 1, #token: 712, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.38, #queue-req: 0, 
[2025-07-28 01:18:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1266, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.00, #queue-req: 0, 
[2025-07-28 01:18:08 DP0 TP0] Decode batch. #running-req: 1, #token: 752, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:18:08 DP1 TP0] Decode batch. #running-req: 1, #token: 1306, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.04, #queue-req: 0, 
[2025-07-28 01:18:08 DP0 TP0] Decode batch. #running-req: 1, #token: 792, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:18:08] INFO:     127.0.0.1:33444 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:18:08 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 129, #cached-token: 87, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:18:08 DP1 TP0] Decode batch. #running-req: 1, #token: 229, token usage: 0.00, cuda graph: True, gen throughput (token/s): 120.25, #queue-req: 0, 
[2025-07-28 01:18:08 DP0 TP0] Decode batch. #running-req: 1, #token: 832, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.23, #queue-req: 0, 
[2025-07-28 01:18:09 DP1 TP0] Decode batch. #running-req: 1, #token: 269, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.60, #queue-req: 0, 
[2025-07-28 01:18:09 DP0 TP0] Decode batch. #running-req: 1, #token: 872, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.27, #queue-req: 0, 
[2025-07-28 01:18:09 DP1 TP0] Decode batch. #running-req: 1, #token: 309, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.55, #queue-req: 0, 
[2025-07-28 01:18:09 DP0 TP0] Decode batch. #running-req: 1, #token: 912, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:18:09 DP1 TP0] Decode batch. #running-req: 1, #token: 349, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.59, #queue-req: 0, 
[2025-07-28 01:18:09 DP0 TP0] Decode batch. #running-req: 1, #token: 952, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:18:09 DP1 TP0] Decode batch. #running-req: 1, #token: 389, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.56, #queue-req: 0, 
[2025-07-28 01:18:09 DP0 TP0] Decode batch. #running-req: 1, #token: 992, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.27, #queue-req: 0, 
[2025-07-28 01:18:10 DP1 TP0] Decode batch. #running-req: 1, #token: 429, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.56, #queue-req: 0, 
[2025-07-28 01:18:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1032, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.21, #queue-req: 0, 
[2025-07-28 01:18:10 DP1 TP0] Decode batch. #running-req: 1, #token: 469, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:18:10 DP0 TP0] Decode batch. #running-req: 1, #token: 1072, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.99, #queue-req: 0, 
[2025-07-28 01:18:10] INFO:     127.0.0.1:44028 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:18:10 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 91, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:18:10 DP1 TP0] Decode batch. #running-req: 1, #token: 509, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.58, #queue-req: 0, 
[2025-07-28 01:18:10 DP0 TP0] Decode batch. #running-req: 1, #token: 232, token usage: 0.00, cuda graph: True, gen throughput (token/s): 120.40, #queue-req: 0, 
[2025-07-28 01:18:11 DP1 TP0] Decode batch. #running-req: 1, #token: 549, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.45, #queue-req: 0, 
[2025-07-28 01:18:11 DP0 TP0] Decode batch. #running-req: 1, #token: 272, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.47, #queue-req: 0, 
[2025-07-28 01:18:11 DP1 TP0] Decode batch. #running-req: 1, #token: 589, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:18:11 DP0 TP0] Decode batch. #running-req: 1, #token: 312, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.46, #queue-req: 0, 
[2025-07-28 01:18:11 DP1 TP0] Decode batch. #running-req: 1, #token: 629, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.10, #queue-req: 0, 
[2025-07-28 01:18:11 DP0 TP0] Decode batch. #running-req: 1, #token: 352, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:18:11 DP1 TP0] Decode batch. #running-req: 1, #token: 669, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.37, #queue-req: 0, 
[2025-07-28 01:18:11 DP0 TP0] Decode batch. #running-req: 1, #token: 392, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.48, #queue-req: 0, 
[2025-07-28 01:18:12 DP1 TP0] Decode batch. #running-req: 1, #token: 709, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.40, #queue-req: 0, 
[2025-07-28 01:18:12 DP0 TP0] Decode batch. #running-req: 1, #token: 432, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:18:12 DP1 TP0] Decode batch. #running-req: 1, #token: 749, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:18:12 DP0 TP0] Decode batch. #running-req: 1, #token: 472, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:18:12 DP1 TP0] Decode batch. #running-req: 1, #token: 789, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:18:12 DP0 TP0] Decode batch. #running-req: 1, #token: 512, token usage: 0.00, cuda graph: True, gen throughput (token/s): 134.49, #queue-req: 0, 
[2025-07-28 01:18:13 DP1 TP0] Decode batch. #running-req: 1, #token: 829, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:18:13 DP0 TP0] Decode batch. #running-req: 1, #token: 552, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.02, #queue-req: 0, 
[2025-07-28 01:18:13 DP1 TP0] Decode batch. #running-req: 1, #token: 869, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.40, #queue-req: 0, 
[2025-07-28 01:18:13 DP0 TP0] Decode batch. #running-req: 1, #token: 592, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:18:13 DP1 TP0] Decode batch. #running-req: 1, #token: 909, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.40, #queue-req: 0, 
[2025-07-28 01:18:13 DP0 TP0] Decode batch. #running-req: 1, #token: 632, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:18:13 DP1 TP0] Decode batch. #running-req: 1, #token: 949, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.38, #queue-req: 0, 
[2025-07-28 01:18:13 DP0 TP0] Decode batch. #running-req: 1, #token: 672, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:18:13] INFO:     127.0.0.1:44042 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:18:13 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 92, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:18:14 DP1 TP0] Decode batch. #running-req: 1, #token: 265, token usage: 0.00, cuda graph: True, gen throughput (token/s): 120.05, #queue-req: 0, 
[2025-07-28 01:18:14 DP0 TP0] Decode batch. #running-req: 1, #token: 712, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:18:14 DP1 TP0] Decode batch. #running-req: 1, #token: 305, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.63, #queue-req: 0, 
[2025-07-28 01:18:14 DP0 TP0] Decode batch. #running-req: 1, #token: 752, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.28, #queue-req: 0, 
[2025-07-28 01:18:14 DP1 TP0] Decode batch. #running-req: 1, #token: 345, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.62, #queue-req: 0, 
[2025-07-28 01:18:14 DP0 TP0] Decode batch. #running-req: 1, #token: 792, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:18:15 DP1 TP0] Decode batch. #running-req: 1, #token: 385, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.57, #queue-req: 0, 
[2025-07-28 01:18:15 DP0 TP0] Decode batch. #running-req: 1, #token: 832, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.26, #queue-req: 0, 
[2025-07-28 01:18:15 DP1 TP0] Decode batch. #running-req: 1, #token: 425, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.67, #queue-req: 0, 
[2025-07-28 01:18:15 DP0 TP0] Decode batch. #running-req: 1, #token: 872, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.26, #queue-req: 0, 
[2025-07-28 01:18:15 DP1 TP0] Decode batch. #running-req: 1, #token: 465, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.54, #queue-req: 0, 
[2025-07-28 01:18:15 DP0 TP0] Decode batch. #running-req: 1, #token: 912, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.27, #queue-req: 0, 
[2025-07-28 01:18:15 DP1 TP0] Decode batch. #running-req: 1, #token: 505, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.56, #queue-req: 0, 
[2025-07-28 01:18:15 DP0 TP0] Decode batch. #running-req: 1, #token: 952, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:18:16 DP1 TP0] Decode batch. #running-req: 1, #token: 545, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.47, #queue-req: 0, 
[2025-07-28 01:18:16 DP0 TP0] Decode batch. #running-req: 1, #token: 992, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.28, #queue-req: 0, 
[2025-07-28 01:18:16 DP1 TP0] Decode batch. #running-req: 1, #token: 585, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.45, #queue-req: 0, 
[2025-07-28 01:18:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1032, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.23, #queue-req: 0, 
[2025-07-28 01:18:16 DP1 TP0] Decode batch. #running-req: 1, #token: 625, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.48, #queue-req: 0, 
[2025-07-28 01:18:16 DP0 TP0] Decode batch. #running-req: 1, #token: 1072, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.92, #queue-req: 0, 
[2025-07-28 01:18:17] INFO:     127.0.0.1:44058 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:18:17 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 103, #cached-token: 91, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:18:17 DP1 TP0] Decode batch. #running-req: 1, #token: 665, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.47, #queue-req: 0, 
[2025-07-28 01:18:17 DP0 TP0] Decode batch. #running-req: 1, #token: 210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 119.72, #queue-req: 0, 
[2025-07-28 01:18:17 DP1 TP0] Decode batch. #running-req: 1, #token: 705, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.48, #queue-req: 0, 
[2025-07-28 01:18:17 DP0 TP0] Decode batch. #running-req: 1, #token: 250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.58, #queue-req: 0, 
[2025-07-28 01:18:17 DP1 TP0] Decode batch. #running-req: 1, #token: 745, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.42, #queue-req: 0, 
[2025-07-28 01:18:17 DP0 TP0] Decode batch. #running-req: 1, #token: 290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.57, #queue-req: 0, 
[2025-07-28 01:18:17 DP1 TP0] Decode batch. #running-req: 1, #token: 785, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:18:18 DP0 TP0] Decode batch. #running-req: 1, #token: 330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.54, #queue-req: 0, 
[2025-07-28 01:18:18 DP1 TP0] Decode batch. #running-req: 1, #token: 825, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:18:18 DP0 TP0] Decode batch. #running-req: 1, #token: 370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.53, #queue-req: 0, 
[2025-07-28 01:18:18 DP1 TP0] Decode batch. #running-req: 1, #token: 865, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.41, #queue-req: 0, 
[2025-07-28 01:18:18 DP0 TP0] Decode batch. #running-req: 1, #token: 410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.38, #queue-req: 0, 
[2025-07-28 01:18:18 DP1 TP0] Decode batch. #running-req: 1, #token: 905, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.30, #queue-req: 0, 
[2025-07-28 01:18:18 DP0 TP0] Decode batch. #running-req: 1, #token: 450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.47, #queue-req: 0, 
[2025-07-28 01:18:19 DP1 TP0] Decode batch. #running-req: 1, #token: 945, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.47, #queue-req: 0, 
[2025-07-28 01:18:19 DP0 TP0] Decode batch. #running-req: 1, #token: 490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.54, #queue-req: 0, 
[2025-07-28 01:18:19 DP1 TP0] Decode batch. #running-req: 1, #token: 985, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.46, #queue-req: 0, 
[2025-07-28 01:18:19 DP0 TP0] Decode batch. #running-req: 1, #token: 530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.44, #queue-req: 0, 
[2025-07-28 01:18:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1025, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.48, #queue-req: 0, 
[2025-07-28 01:18:19 DP0 TP0] Decode batch. #running-req: 1, #token: 570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.35, #queue-req: 0, 
[2025-07-28 01:18:19 DP1 TP0] Decode batch. #running-req: 1, #token: 1065, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.13, #queue-req: 0, 
[2025-07-28 01:18:20 DP0 TP0] Decode batch. #running-req: 1, #token: 610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.38, #queue-req: 0, 
[2025-07-28 01:18:20 DP1 TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.78, #queue-req: 0, 
[2025-07-28 01:18:20] INFO:     127.0.0.1:39998 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:18:20 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 89, #cached-token: 91, token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 01:18:20 DP0 TP0] Decode batch. #running-req: 1, #token: 650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:18:20 DP1 TP0] Decode batch. #running-req: 1, #token: 220, token usage: 0.00, cuda graph: True, gen throughput (token/s): 120.63, #queue-req: 0, 
[2025-07-28 01:18:20 DP0 TP0] Decode batch. #running-req: 1, #token: 690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.34, #queue-req: 0, 
[2025-07-28 01:18:20 DP1 TP0] Decode batch. #running-req: 1, #token: 260, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.61, #queue-req: 0, 
[2025-07-28 01:18:20 DP0 TP0] Decode batch. #running-req: 1, #token: 730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:18:21 DP1 TP0] Decode batch. #running-req: 1, #token: 300, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.63, #queue-req: 0, 
[2025-07-28 01:18:21 DP0 TP0] Decode batch. #running-req: 1, #token: 770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.30, #queue-req: 0, 
[2025-07-28 01:18:21 DP1 TP0] Decode batch. #running-req: 1, #token: 340, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.58, #queue-req: 0, 
[2025-07-28 01:18:21 DP0 TP0] Decode batch. #running-req: 1, #token: 810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.28, #queue-req: 0, 
[2025-07-28 01:18:21 DP1 TP0] Decode batch. #running-req: 1, #token: 380, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.50, #queue-req: 0, 
[2025-07-28 01:18:21 DP0 TP0] Decode batch. #running-req: 1, #token: 850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.26, #queue-req: 0, 
[2025-07-28 01:18:22 DP1 TP0] Decode batch. #running-req: 1, #token: 420, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.68, #queue-req: 0, 
[2025-07-28 01:18:22 DP0 TP0] Decode batch. #running-req: 1, #token: 890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.21, #queue-req: 0, 
[2025-07-28 01:18:22 DP1 TP0] Decode batch. #running-req: 1, #token: 460, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.60, #queue-req: 0, 
[2025-07-28 01:18:22 DP0 TP0] Decode batch. #running-req: 1, #token: 930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.27, #queue-req: 0, 
[2025-07-28 01:18:22 DP1 TP0] Decode batch. #running-req: 1, #token: 500, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.56, #queue-req: 0, 
[2025-07-28 01:18:22 DP0 TP0] Decode batch. #running-req: 1, #token: 970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.32, #queue-req: 0, 
[2025-07-28 01:18:22 DP1 TP0] Decode batch. #running-req: 1, #token: 540, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.54, #queue-req: 0, 
[2025-07-28 01:18:22 DP0 TP0] Decode batch. #running-req: 1, #token: 1010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:18:23 DP1 TP0] Decode batch. #running-req: 1, #token: 580, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.38, #queue-req: 0, 
[2025-07-28 01:18:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.03, #queue-req: 0, 
[2025-07-28 01:18:23 DP1 TP0] Decode batch. #running-req: 1, #token: 620, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.46, #queue-req: 0, 
[2025-07-28 01:18:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.00, #queue-req: 0, 
[2025-07-28 01:18:23 DP1 TP0] Decode batch. #running-req: 1, #token: 660, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.46, #queue-req: 0, 
[2025-07-28 01:18:23 DP0 TP0] Decode batch. #running-req: 1, #token: 1130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.91, #queue-req: 0, 
[2025-07-28 01:18:24 DP1 TP0] Decode batch. #running-req: 1, #token: 700, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:18:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.03, #queue-req: 0, 
[2025-07-28 01:18:24 DP1 TP0] Decode batch. #running-req: 1, #token: 740, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.29, #queue-req: 0, 
[2025-07-28 01:18:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.89, #queue-req: 0, 
[2025-07-28 01:18:24 DP1 TP0] Decode batch. #running-req: 1, #token: 780, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.25, #queue-req: 0, 
[2025-07-28 01:18:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.93, #queue-req: 0, 
[2025-07-28 01:18:24 DP1 TP0] Decode batch. #running-req: 1, #token: 820, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.98, #queue-req: 0, 
[2025-07-28 01:18:24 DP0 TP0] Decode batch. #running-req: 1, #token: 1290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.97, #queue-req: 0, 
[2025-07-28 01:18:25 DP1 TP0] Decode batch. #running-req: 1, #token: 860, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.80, #queue-req: 0, 
[2025-07-28 01:18:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.89, #queue-req: 0, 
[2025-07-28 01:18:25 DP1 TP0] Decode batch. #running-req: 1, #token: 900, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.96, #queue-req: 0, 
[2025-07-28 01:18:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.92, #queue-req: 0, 
[2025-07-28 01:18:25 DP1 TP0] Decode batch. #running-req: 1, #token: 940, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.33, #queue-req: 0, 
[2025-07-28 01:18:25 DP0 TP0] Decode batch. #running-req: 1, #token: 1410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.91, #queue-req: 0, 
[2025-07-28 01:18:26 DP1 TP0] Decode batch. #running-req: 1, #token: 980, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.36, #queue-req: 0, 
[2025-07-28 01:18:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.89, #queue-req: 0, 
[2025-07-28 01:18:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1020, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.31, #queue-req: 0, 
[2025-07-28 01:18:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.69, #queue-req: 0, 
[2025-07-28 01:18:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1060, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.10, #queue-req: 0, 
[2025-07-28 01:18:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.89, #queue-req: 0, 
[2025-07-28 01:18:26 DP1 TP0] Decode batch. #running-req: 1, #token: 1100, token usage: 0.00, cuda graph: True, gen throughput (token/s): 139.10, #queue-req: 0, 
[2025-07-28 01:18:26 DP0 TP0] Decode batch. #running-req: 1, #token: 1570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.79, #queue-req: 0, 
[2025-07-28 01:18:26] INFO:     127.0.0.1:40024 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 01:18:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.79, #queue-req: 0, 
[2025-07-28 01:18:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.80, #queue-req: 0, 
[2025-07-28 01:18:27 DP0 TP0] Decode batch. #running-req: 1, #token: 1690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.81, #queue-req: 0, 
[2025-07-28 01:18:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.83, #queue-req: 0, 
[2025-07-28 01:18:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.77, #queue-req: 0, 
[2025-07-28 01:18:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.79, #queue-req: 0, 
[2025-07-28 01:18:28 DP0 TP0] Decode batch. #running-req: 1, #token: 1850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.80, #queue-req: 0, 
[2025-07-28 01:18:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.71, #queue-req: 0, 
[2025-07-28 01:18:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.75, #queue-req: 0, 
[2025-07-28 01:18:29 DP0 TP0] Decode batch. #running-req: 1, #token: 1970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.80, #queue-req: 0, 
[2025-07-28 01:18:30 DP0 TP0] Decode batch. #running-req: 1, #token: 2010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.82, #queue-req: 0, 
[2025-07-28 01:18:30 DP0 TP0] Decode batch. #running-req: 1, #token: 2050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.67, #queue-req: 0, 
[2025-07-28 01:18:30 DP0 TP0] Decode batch. #running-req: 1, #token: 2090, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.39, #queue-req: 0, 
[2025-07-28 01:18:30 DP0 TP0] Decode batch. #running-req: 1, #token: 2130, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.54, #queue-req: 0, 
[2025-07-28 01:18:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2170, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.55, #queue-req: 0, 
[2025-07-28 01:18:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2210, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.47, #queue-req: 0, 
[2025-07-28 01:18:31 DP0 TP0] Decode batch. #running-req: 1, #token: 2250, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.06, #queue-req: 0, 
[2025-07-28 01:18:32 DP0 TP0] Decode batch. #running-req: 1, #token: 2290, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.46, #queue-req: 0, 
[2025-07-28 01:18:32 DP0 TP0] Decode batch. #running-req: 1, #token: 2330, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.42, #queue-req: 0, 
[2025-07-28 01:18:32 DP0 TP0] Decode batch. #running-req: 1, #token: 2370, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.41, #queue-req: 0, 
[2025-07-28 01:18:32 DP0 TP0] Decode batch. #running-req: 1, #token: 2410, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.45, #queue-req: 0, 
[2025-07-28 01:18:33 DP0 TP0] Decode batch. #running-req: 1, #token: 2450, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.43, #queue-req: 0, 
[2025-07-28 01:18:33 DP0 TP0] Decode batch. #running-req: 1, #token: 2490, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.43, #queue-req: 0, 
[2025-07-28 01:18:33 DP0 TP0] Decode batch. #running-req: 1, #token: 2530, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.45, #queue-req: 0, 
[2025-07-28 01:18:34 DP0 TP0] Decode batch. #running-req: 1, #token: 2570, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.40, #queue-req: 0, 
[2025-07-28 01:18:34 DP0 TP0] Decode batch. #running-req: 1, #token: 2610, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.33, #queue-req: 0, 
[2025-07-28 01:18:34 DP0 TP0] Decode batch. #running-req: 1, #token: 2650, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.31, #queue-req: 0, 
[2025-07-28 01:18:35 DP0 TP0] Decode batch. #running-req: 1, #token: 2690, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.30, #queue-req: 0, 
[2025-07-28 01:18:35 DP0 TP0] Decode batch. #running-req: 1, #token: 2730, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.36, #queue-req: 0, 
[2025-07-28 01:18:35 DP0 TP0] Decode batch. #running-req: 1, #token: 2770, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.23, #queue-req: 0, 
[2025-07-28 01:18:35 DP0 TP0] Decode batch. #running-req: 1, #token: 2810, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.30, #queue-req: 0, 
[2025-07-28 01:18:36 DP0 TP0] Decode batch. #running-req: 1, #token: 2850, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.30, #queue-req: 0, 
[2025-07-28 01:18:36 DP0 TP0] Decode batch. #running-req: 1, #token: 2890, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.31, #queue-req: 0, 
[2025-07-28 01:18:36 DP0 TP0] Decode batch. #running-req: 1, #token: 2930, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.31, #queue-req: 0, 
[2025-07-28 01:18:37 DP0 TP0] Decode batch. #running-req: 1, #token: 2970, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.30, #queue-req: 0, 
[2025-07-28 01:18:37 DP0 TP0] Decode batch. #running-req: 1, #token: 3010, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.39, #queue-req: 0, 
[2025-07-28 01:18:37 DP0 TP0] Decode batch. #running-req: 1, #token: 3050, token usage: 0.00, cuda graph: True, gen throughput (token/s): 138.35, #queue-req: 0, 
[2025-07-28 01:18:37] INFO:     127.0.0.1:40010 - "POST /v1/chat/completions HTTP/1.1" 200 OK

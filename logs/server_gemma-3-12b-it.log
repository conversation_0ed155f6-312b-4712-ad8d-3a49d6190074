[2025-07-28 00:05:41] server_args=ServerArgs(model_path='/data/home/<USER>/repos/anesbench/share/models/gemma-3-12b-it', tokenizer_path='/data/home/<USER>/repos/anesbench/share/models/gemma-3-12b-it', tokenizer_mode='auto', skip_tokenizer_init=False, load_format='auto', model_loader_extra_config='{}', trust_remote_code=False, context_length=32768, is_embedding=False, enable_multimodal=None, revision=None, model_impl='auto', host='127.0.0.1', port=8003, skip_server_warmup=False, warmups=None, nccl_port=None, dtype='auto', quantization=None, quantization_param_path=None, kv_cache_dtype='auto', mem_fraction_static=0.85, max_running_requests=None, max_total_tokens=None, chunked_prefill_size=8192, max_prefill_tokens=16384, schedule_policy='fcfs', schedule_conservativeness=1.0, cpu_offload_gb=0, page_size=1, hybrid_kvcache_ratio=None, swa_full_tokens_ratio=0.8, disable_hybrid_swa_memory=False, device='cuda', tp_size=4, pp_size=1, max_micro_batch_size=None, stream_interval=1, stream_output=False, random_seed=637618081, constrained_json_whitespace_pattern=None, watchdog_timeout=300, dist_timeout=None, download_dir=None, base_gpu_id=0, gpu_id_step=1, sleep_on_idle=False, log_level='info', log_level_http=None, log_requests=False, log_requests_level=0, crash_dump_folder=None, show_time_cost=True, enable_metrics=True, enable_metrics_for_all_schedulers=False, bucket_time_to_first_token=None, bucket_inter_token_latency=None, bucket_e2e_request_latency=None, collect_tokens_histogram=False, decode_log_interval=40, enable_request_time_stats_logging=False, kv_events_config=None, api_key=None, served_model_name='/data/home/<USER>/repos/anesbench/share/models/gemma-3-12b-it', chat_template=None, completion_template=None, file_storage_path='sglang_storage', enable_cache_report=False, reasoning_parser=None, tool_call_parser=None, dp_size=2, load_balance_method='round_robin', dist_init_addr=None, nnodes=1, node_rank=0, json_model_override_args='{}', preferred_sampling_params=None, enable_lora=None, max_lora_rank=None, lora_target_modules=None, lora_paths=None, max_loras_per_batch=8, lora_backend='triton', attention_backend=None, sampling_backend='flashinfer', grammar_backend='xgrammar', mm_attention_backend=None, speculative_algorithm=None, speculative_draft_model_path=None, speculative_num_steps=None, speculative_eagle_topk=None, speculative_num_draft_tokens=None, speculative_accept_threshold_single=1.0, speculative_accept_threshold_acc=1.0, speculative_token_map=None, ep_size=1, enable_ep_moe=False, enable_deepep_moe=False, enable_flashinfer_moe=False, enable_flashinfer_allreduce_fusion=False, deepep_mode='auto', ep_num_redundant_experts=0, ep_dispatch_algorithm='static', init_expert_location='trivial', enable_eplb=False, eplb_algorithm='auto', eplb_rebalance_num_iterations=1000, eplb_rebalance_layers_per_chunk=None, expert_distribution_recorder_mode=None, expert_distribution_recorder_buffer_size=1000, enable_expert_distribution_metrics=False, deepep_config=None, moe_dense_tp_size=None, enable_hierarchical_cache=False, hicache_ratio=2.0, hicache_size=0, hicache_write_policy='write_through_selective', hicache_io_backend='', hicache_storage_backend=None, enable_double_sparsity=False, ds_channel_config_path=None, ds_heavy_channel_num=32, ds_heavy_token_num=256, ds_heavy_channel_type='qk', ds_sparse_decode_threshold=4096, disable_radix_cache=False, cuda_graph_max_bs=None, cuda_graph_bs=None, disable_cuda_graph=False, disable_cuda_graph_padding=False, enable_profile_cuda_graph=False, enable_nccl_nvls=False, enable_tokenizer_batch_encode=False, disable_outlines_disk_cache=False, disable_custom_all_reduce=False, enable_mscclpp=False, disable_overlap_schedule=False, enable_mixed_chunk=False, enable_dp_attention=False, enable_dp_lm_head=False, enable_two_batch_overlap=False, enable_torch_compile=False, torch_compile_max_bs=32, torchao_config='', enable_nan_detection=False, enable_p2p_check=False, triton_attention_reduce_in_fp32=False, triton_attention_num_kv_splits=8, num_continuous_decode_steps=1, delete_ckpt_after_loading=False, enable_memory_saver=False, allow_auto_truncate=False, enable_custom_logit_processor=False, flashinfer_mla_disable_ragged=False, disable_shared_experts_fusion=False, disable_chunked_prefix_cache=False, disable_fast_image_processor=False, enable_return_hidden_states=False, enable_triton_kernel_moe=False, debug_tensor_dump_output_folder=None, debug_tensor_dump_input_file=None, debug_tensor_dump_inject=False, debug_tensor_dump_prefill_only=False, disaggregation_mode='null', disaggregation_transfer_backend='mooncake', disaggregation_bootstrap_port=8998, disaggregation_decode_tp=None, disaggregation_decode_dp=None, disaggregation_prefill_pp=1, disaggregation_ib_device=None, num_reserved_decode_tokens=512, pdlb_url=None, custom_weight_loader=[], weight_loader_disable_mmap=False, enable_pdmux=False, sm_group_num=3)
[2025-07-28 00:05:41] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:05:41] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:05:41] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:05:42] Inferred chat template from model path: gemma-it
[2025-07-28 00:05:47] Launch DP0 starting at GPU #0.
[2025-07-28 00:05:47] Launch DP1 starting at GPU #4.
[2025-07-28 00:05:54 DP0 TP0] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:05:54 DP0 TP0] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:05:54 DP0 TP0] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:05:54 DP1 TP1] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:05:54 DP1 TP1] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:05:54 DP1 TP1] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:05:54 DP1 TP0] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:05:54 DP1 TP0] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:05:54 DP1 TP0] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:05:54 DP1 TP2] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:05:54 DP1 TP2] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:05:54 DP1 TP2] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:05:54 DP0 TP3] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:05:54 DP0 TP1] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:05:54 DP0 TP3] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:05:54 DP0 TP1] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:05:54 DP0 TP3] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:05:54 DP0 TP1] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:05:54 DP1 TP3] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:05:54 DP1 TP3] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:05:54 DP1 TP3] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:05:54 DP0 TP2] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:05:54 DP0 TP2] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:05:54 DP0 TP2] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:05:55 DP0 TP0] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:05:55 DP0 TP0] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:05:55 DP0 TP0] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:05:55 DP0 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 00:05:55 DP0 TP0] Init torch distributed begin.
[2025-07-28 00:05:55 DP1 TP1] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:05:55 DP1 TP1] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:05:55 DP1 TP1] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:05:55 DP1 TP0] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:05:55 DP1 TP0] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:05:55 DP1 TP0] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:05:55 DP1 TP0] Attention backend not explicitly specified. Use flashinfer backend by default.
[2025-07-28 00:05:55 DP1 TP0] Init torch distributed begin.
[2025-07-28 00:05:56 DP1 TP2] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:05:56 DP1 TP2] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:05:56 DP1 TP2] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:05:56 DP1 TP3] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:05:56 DP1 TP3] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:05:56 DP1 TP3] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:05:56 DP0 TP3] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:05:56 DP0 TP3] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:05:56 DP0 TP3] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:05:56 DP0 TP1] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:05:56 DP0 TP1] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:05:56 DP0 TP1] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:05:56 DP0 TP2] Multimodal is disabled for gemma3. To enable it, set --enable-multimodal.
[2025-07-28 00:05:56 DP0 TP2] For Gemma 3, we downcast float32 to bfloat16 instead of float16 by default. Please specify `dtype` if you want to use float16.
[2025-07-28 00:05:56 DP0 TP2] Downcasting torch.float32 to torch.bfloat16.
[2025-07-28 00:05:57 DP0 TP0] sglang is using nccl==2.26.2
[2025-07-28 00:05:57 DP1 TP0] sglang is using nccl==2.26.2
[2025-07-28 00:05:58 DP0 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 00:05:58 DP1 TP0] Init torch distributed ends. mem usage=0.63 GB
[2025-07-28 00:05:58 DP0 TP3] Multimodal attention backend not set. Use sdpa.
[2025-07-28 00:05:58 DP0 TP1] Multimodal attention backend not set. Use sdpa.
[2025-07-28 00:05:58 DP0 TP3] Using sdpa as multimodal attention backend.
[2025-07-28 00:05:58 DP0 TP1] Using sdpa as multimodal attention backend.
[2025-07-28 00:05:58 DP0 TP0] Load weight begin. avail mem=78.21 GB
[2025-07-28 00:05:58 DP1 TP0] Load weight begin. avail mem=78.21 GB
[2025-07-28 00:05:58 DP0 TP2] Multimodal attention backend not set. Use sdpa.
[2025-07-28 00:05:58 DP0 TP2] Using sdpa as multimodal attention backend.
[2025-07-28 00:05:58 DP0 TP0] Multimodal attention backend not set. Use sdpa.
[2025-07-28 00:05:58 DP0 TP0] Using sdpa as multimodal attention backend.
[2025-07-28 00:05:58 DP1 TP0] Multimodal attention backend not set. Use sdpa.
[2025-07-28 00:05:58 DP1 TP0] Using sdpa as multimodal attention backend.
[2025-07-28 00:05:58 DP1 TP1] Multimodal attention backend not set. Use sdpa.
[2025-07-28 00:05:58 DP1 TP1] Using sdpa as multimodal attention backend.
[2025-07-28 00:05:58 DP1 TP3] Multimodal attention backend not set. Use sdpa.
[2025-07-28 00:05:58 DP1 TP2] Multimodal attention backend not set. Use sdpa.
[2025-07-28 00:05:58 DP1 TP3] Using sdpa as multimodal attention backend.
[2025-07-28 00:05:58 DP1 TP2] Using sdpa as multimodal attention backend.

Loading safetensors checkpoint shards:   0% Completed | 0/5 [00:00<?, ?it/s]

Loading safetensors checkpoint shards:   0% Completed | 0/5 [00:00<?, ?it/s]

Loading safetensors checkpoint shards:  20% Completed | 1/5 [00:01<00:06,  1.61s/it]

Loading safetensors checkpoint shards:  20% Completed | 1/5 [00:01<00:06,  1.61s/it]

Loading safetensors checkpoint shards:  40% Completed | 2/5 [00:03<00:05,  1.92s/it]

Loading safetensors checkpoint shards:  40% Completed | 2/5 [00:03<00:05,  1.92s/it]

Loading safetensors checkpoint shards:  60% Completed | 3/5 [00:05<00:03,  1.86s/it]

Loading safetensors checkpoint shards:  60% Completed | 3/5 [00:05<00:03,  1.86s/it]

Loading safetensors checkpoint shards:  80% Completed | 4/5 [00:07<00:01,  1.85s/it]

Loading safetensors checkpoint shards:  80% Completed | 4/5 [00:07<00:01,  1.84s/it]

Loading safetensors checkpoint shards: 100% Completed | 5/5 [00:09<00:00,  1.84s/it]

Loading safetensors checkpoint shards: 100% Completed | 5/5 [00:09<00:00,  1.84s/it]

Loading safetensors checkpoint shards: 100% Completed | 5/5 [00:09<00:00,  1.84s/it]

Loading safetensors checkpoint shards: 100% Completed | 5/5 [00:09<00:00,  1.84s/it]


[2025-07-28 00:06:08 DP1 TP0] Load weight end. type=Gemma3ForConditionalGeneration, dtype=torch.bfloat16, avail mem=70.77 GB, mem usage=7.43 GB.
[2025-07-28 00:06:08 DP0 TP0] Load weight end. type=Gemma3ForConditionalGeneration, dtype=torch.bfloat16, avail mem=70.77 GB, mem usage=7.43 GB.
[2025-07-28 00:06:08 DP0 TP0] Use Sliding window memory pool. full_layer_tokens=772820, swa_layer_tokens=618256
[2025-07-28 00:06:08 DP1 TP0] Use Sliding window memory pool. full_layer_tokens=772820, swa_layer_tokens=618256
[2025-07-28 00:06:08 DP0 TP3] KV Cache is allocated. #tokens: 618256, K size: 23.58 GB, V size: 23.58 GB
[2025-07-28 00:06:08 DP0 TP2] KV Cache is allocated. #tokens: 618256, K size: 23.58 GB, V size: 23.58 GB
[2025-07-28 00:06:08 DP1 TP1] KV Cache is allocated. #tokens: 618256, K size: 23.58 GB, V size: 23.58 GB
[2025-07-28 00:06:08 DP0 TP1] KV Cache is allocated. #tokens: 618256, K size: 23.58 GB, V size: 23.58 GB
[2025-07-28 00:06:08 DP1 TP0] KV Cache is allocated. #tokens: 618256, K size: 23.58 GB, V size: 23.58 GB
[2025-07-28 00:06:08 DP0 TP0] KV Cache is allocated. #tokens: 618256, K size: 23.58 GB, V size: 23.58 GB
[2025-07-28 00:06:08 DP1 TP2] KV Cache is allocated. #tokens: 618256, K size: 23.58 GB, V size: 23.58 GB
[2025-07-28 00:06:08 DP1 TP3] KV Cache is allocated. #tokens: 618256, K size: 23.58 GB, V size: 23.58 GB
[2025-07-28 00:06:08 DP0 TP3] KV Cache is allocated. #tokens: 772820, K size: 5.90 GB, V size: 5.90 GB
[2025-07-28 00:06:08 DP0 TP2] KV Cache is allocated. #tokens: 772820, K size: 5.90 GB, V size: 5.90 GB
[2025-07-28 00:06:08 DP1 TP1] KV Cache is allocated. #tokens: 772820, K size: 5.90 GB, V size: 5.90 GB
[2025-07-28 00:06:08 DP0 TP1] KV Cache is allocated. #tokens: 772820, K size: 5.90 GB, V size: 5.90 GB
[2025-07-28 00:06:08 DP1 TP0] KV Cache is allocated. #tokens: 772820, K size: 5.90 GB, V size: 5.90 GB
[2025-07-28 00:06:08 DP0 TP0] KV Cache is allocated. #tokens: 772820, K size: 5.90 GB, V size: 5.90 GB
[2025-07-28 00:06:08 DP1 TP2] KV Cache is allocated. #tokens: 772820, K size: 5.90 GB, V size: 5.90 GB
[2025-07-28 00:06:08 DP1 TP0] Memory pool end. avail mem=11.25 GB
[2025-07-28 00:06:08 DP0 TP0] Memory pool end. avail mem=11.25 GB
[2025-07-28 00:06:08 DP1 TP3] KV Cache is allocated. #tokens: 772820, K size: 5.90 GB, V size: 5.90 GB
[2025-07-28 00:06:08 DP0 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.66 GB
[2025-07-28 00:06:08 DP1 TP0] Capture cuda graph begin. This can take up to several minutes. avail mem=10.66 GB
[2025-07-28 00:06:08 DP0 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.62 GB):   0%|          | 0/23 [00:00<?, ?it/s][2025-07-28 00:06:08 DP1 TP0] Capture cuda graph bs [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160]

  0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.62 GB):   0%|          | 0/23 [00:00<?, ?it/s]
Capturing batches (bs=160 avail_mem=10.62 GB):   4%|▍         | 1/23 [00:01<00:27,  1.25s/it]
Capturing batches (bs=152 avail_mem=9.80 GB):   4%|▍         | 1/23 [00:01<00:27,  1.25s/it] 
Capturing batches (bs=160 avail_mem=10.62 GB):   4%|▍         | 1/23 [00:01<00:27,  1.24s/it]
Capturing batches (bs=152 avail_mem=9.80 GB):   4%|▍         | 1/23 [00:01<00:27,  1.24s/it] 
Capturing batches (bs=152 avail_mem=9.80 GB):   9%|▊         | 2/23 [00:01<00:16,  1.24it/s]
Capturing batches (bs=144 avail_mem=9.44 GB):   9%|▊         | 2/23 [00:01<00:16,  1.24it/s]
Capturing batches (bs=152 avail_mem=9.80 GB):   9%|▊         | 2/23 [00:01<00:16,  1.24it/s]
Capturing batches (bs=144 avail_mem=9.44 GB):   9%|▊         | 2/23 [00:01<00:16,  1.24it/s]
Capturing batches (bs=144 avail_mem=9.44 GB):  13%|█▎        | 3/23 [00:02<00:13,  1.52it/s]
Capturing batches (bs=136 avail_mem=9.38 GB):  13%|█▎        | 3/23 [00:02<00:13,  1.52it/s]
Capturing batches (bs=144 avail_mem=9.44 GB):  13%|█▎        | 3/23 [00:02<00:13,  1.49it/s]
Capturing batches (bs=136 avail_mem=9.38 GB):  13%|█▎        | 3/23 [00:02<00:13,  1.49it/s]
Capturing batches (bs=136 avail_mem=9.38 GB):  17%|█▋        | 4/23 [00:02<00:11,  1.70it/s]
Capturing batches (bs=128 avail_mem=9.06 GB):  17%|█▋        | 4/23 [00:02<00:11,  1.70it/s]
Capturing batches (bs=136 avail_mem=9.38 GB):  17%|█▋        | 4/23 [00:02<00:11,  1.64it/s]
Capturing batches (bs=128 avail_mem=9.06 GB):  17%|█▋        | 4/23 [00:02<00:11,  1.64it/s]
Capturing batches (bs=128 avail_mem=9.06 GB):  22%|██▏       | 5/23 [00:03<00:09,  1.81it/s]
Capturing batches (bs=120 avail_mem=9.00 GB):  22%|██▏       | 5/23 [00:03<00:09,  1.81it/s]
Capturing batches (bs=128 avail_mem=9.06 GB):  22%|██▏       | 5/23 [00:03<00:10,  1.75it/s]
Capturing batches (bs=120 avail_mem=9.00 GB):  22%|██▏       | 5/23 [00:03<00:10,  1.75it/s]
Capturing batches (bs=120 avail_mem=9.00 GB):  26%|██▌       | 6/23 [00:03<00:08,  1.90it/s]
Capturing batches (bs=112 avail_mem=8.70 GB):  26%|██▌       | 6/23 [00:03<00:08,  1.90it/s]
Capturing batches (bs=120 avail_mem=9.00 GB):  26%|██▌       | 6/23 [00:03<00:09,  1.83it/s]
Capturing batches (bs=112 avail_mem=8.70 GB):  26%|██▌       | 6/23 [00:03<00:09,  1.83it/s]
Capturing batches (bs=112 avail_mem=8.70 GB):  30%|███       | 7/23 [00:04<00:08,  1.96it/s]
Capturing batches (bs=104 avail_mem=8.64 GB):  30%|███       | 7/23 [00:04<00:08,  1.96it/s]
Capturing batches (bs=112 avail_mem=8.70 GB):  30%|███       | 7/23 [00:04<00:08,  1.91it/s]
Capturing batches (bs=104 avail_mem=8.64 GB):  30%|███       | 7/23 [00:04<00:08,  1.91it/s]
Capturing batches (bs=104 avail_mem=8.64 GB):  35%|███▍      | 8/23 [00:04<00:07,  1.98it/s]
Capturing batches (bs=96 avail_mem=8.38 GB):  35%|███▍      | 8/23 [00:04<00:07,  1.98it/s] 
Capturing batches (bs=104 avail_mem=8.64 GB):  35%|███▍      | 8/23 [00:04<00:07,  1.96it/s]
Capturing batches (bs=96 avail_mem=8.38 GB):  35%|███▍      | 8/23 [00:04<00:07,  1.96it/s] 
Capturing batches (bs=96 avail_mem=8.38 GB):  39%|███▉      | 9/23 [00:05<00:06,  2.03it/s]
Capturing batches (bs=88 avail_mem=8.31 GB):  39%|███▉      | 9/23 [00:05<00:06,  2.03it/s]
Capturing batches (bs=96 avail_mem=8.38 GB):  39%|███▉      | 9/23 [00:05<00:07,  1.88it/s]
Capturing batches (bs=88 avail_mem=8.31 GB):  39%|███▉      | 9/23 [00:05<00:07,  1.88it/s]
Capturing batches (bs=88 avail_mem=8.31 GB):  43%|████▎     | 10/23 [00:05<00:06,  2.07it/s]
Capturing batches (bs=80 avail_mem=8.25 GB):  43%|████▎     | 10/23 [00:05<00:06,  2.07it/s]
Capturing batches (bs=88 avail_mem=8.31 GB):  43%|████▎     | 10/23 [00:05<00:07,  1.83it/s]
Capturing batches (bs=80 avail_mem=8.25 GB):  43%|████▎     | 10/23 [00:05<00:07,  1.83it/s]
Capturing batches (bs=80 avail_mem=8.25 GB):  48%|████▊     | 11/23 [00:06<00:05,  2.08it/s]
Capturing batches (bs=72 avail_mem=8.03 GB):  48%|████▊     | 11/23 [00:06<00:05,  2.08it/s]
Capturing batches (bs=80 avail_mem=8.25 GB):  48%|████▊     | 11/23 [00:06<00:06,  1.86it/s]
Capturing batches (bs=72 avail_mem=8.03 GB):  48%|████▊     | 11/23 [00:06<00:06,  1.86it/s]
Capturing batches (bs=72 avail_mem=8.03 GB):  52%|█████▏    | 12/23 [00:06<00:05,  2.08it/s]
Capturing batches (bs=64 avail_mem=7.96 GB):  52%|█████▏    | 12/23 [00:06<00:05,  2.08it/s]
Capturing batches (bs=72 avail_mem=8.03 GB):  52%|█████▏    | 12/23 [00:06<00:05,  1.84it/s]
Capturing batches (bs=64 avail_mem=7.96 GB):  52%|█████▏    | 12/23 [00:06<00:05,  1.84it/s]
Capturing batches (bs=64 avail_mem=7.96 GB):  57%|█████▋    | 13/23 [00:07<00:04,  2.01it/s]
Capturing batches (bs=56 avail_mem=7.91 GB):  57%|█████▋    | 13/23 [00:07<00:04,  2.01it/s]
Capturing batches (bs=64 avail_mem=7.96 GB):  57%|█████▋    | 13/23 [00:07<00:05,  1.86it/s]
Capturing batches (bs=56 avail_mem=7.91 GB):  57%|█████▋    | 13/23 [00:07<00:05,  1.86it/s]
Capturing batches (bs=56 avail_mem=7.91 GB):  61%|██████    | 14/23 [00:07<00:04,  2.05it/s]
Capturing batches (bs=48 avail_mem=7.85 GB):  61%|██████    | 14/23 [00:07<00:04,  2.05it/s]
Capturing batches (bs=56 avail_mem=7.91 GB):  61%|██████    | 14/23 [00:08<00:04,  1.89it/s]
Capturing batches (bs=48 avail_mem=7.85 GB):  61%|██████    | 14/23 [00:08<00:04,  1.89it/s]
Capturing batches (bs=48 avail_mem=7.85 GB):  65%|██████▌   | 15/23 [00:08<00:03,  2.04it/s]
Capturing batches (bs=40 avail_mem=7.69 GB):  65%|██████▌   | 15/23 [00:08<00:03,  2.04it/s]
Capturing batches (bs=40 avail_mem=7.69 GB):  70%|██████▉   | 16/23 [00:08<00:03,  2.06it/s]
Capturing batches (bs=32 avail_mem=7.64 GB):  70%|██████▉   | 16/23 [00:08<00:03,  2.06it/s]
Capturing batches (bs=48 avail_mem=7.85 GB):  65%|██████▌   | 15/23 [00:08<00:04,  1.88it/s]
Capturing batches (bs=40 avail_mem=7.69 GB):  65%|██████▌   | 15/23 [00:08<00:04,  1.88it/s]
Capturing batches (bs=32 avail_mem=7.64 GB):  74%|███████▍  | 17/23 [00:08<00:02,  2.10it/s]
Capturing batches (bs=24 avail_mem=7.58 GB):  74%|███████▍  | 17/23 [00:08<00:02,  2.10it/s]
Capturing batches (bs=40 avail_mem=7.69 GB):  70%|██████▉   | 16/23 [00:08<00:03,  1.97it/s]
Capturing batches (bs=32 avail_mem=7.64 GB):  70%|██████▉   | 16/23 [00:08<00:03,  1.97it/s]
Capturing batches (bs=32 avail_mem=7.64 GB):  74%|███████▍  | 17/23 [00:09<00:02,  2.03it/s]
Capturing batches (bs=24 avail_mem=7.58 GB):  78%|███████▊  | 18/23 [00:09<00:02,  2.10it/s]
Capturing batches (bs=24 avail_mem=7.58 GB):  74%|███████▍  | 17/23 [00:09<00:02,  2.03it/s]
Capturing batches (bs=16 avail_mem=7.52 GB):  78%|███████▊  | 18/23 [00:09<00:02,  2.10it/s]
Capturing batches (bs=16 avail_mem=7.52 GB):  83%|████████▎ | 19/23 [00:09<00:01,  2.08it/s]
Capturing batches (bs=8 avail_mem=7.46 GB):  83%|████████▎ | 19/23 [00:09<00:01,  2.08it/s] 
Capturing batches (bs=24 avail_mem=7.58 GB):  78%|███████▊  | 18/23 [00:09<00:02,  2.03it/s]
Capturing batches (bs=16 avail_mem=7.52 GB):  78%|███████▊  | 18/23 [00:09<00:02,  2.03it/s]
Capturing batches (bs=16 avail_mem=7.52 GB):  83%|████████▎ | 19/23 [00:10<00:01,  2.03it/s]
Capturing batches (bs=8 avail_mem=7.45 GB):  83%|████████▎ | 19/23 [00:10<00:01,  2.03it/s] 
Capturing batches (bs=8 avail_mem=7.46 GB):  87%|████████▋ | 20/23 [00:10<00:01,  2.05it/s]
Capturing batches (bs=4 avail_mem=7.40 GB):  87%|████████▋ | 20/23 [00:10<00:01,  2.05it/s]
Capturing batches (bs=4 avail_mem=7.40 GB):  91%|█████████▏| 21/23 [00:10<00:00,  2.08it/s]
Capturing batches (bs=2 avail_mem=7.35 GB):  91%|█████████▏| 21/23 [00:10<00:00,  2.08it/s]
Capturing batches (bs=8 avail_mem=7.45 GB):  87%|████████▋ | 20/23 [00:10<00:01,  1.97it/s]
Capturing batches (bs=4 avail_mem=7.40 GB):  87%|████████▋ | 20/23 [00:10<00:01,  1.97it/s]
Capturing batches (bs=2 avail_mem=7.35 GB):  96%|█████████▌| 22/23 [00:11<00:00,  2.12it/s]
Capturing batches (bs=1 avail_mem=7.29 GB):  96%|█████████▌| 22/23 [00:11<00:00,  2.12it/s]
Capturing batches (bs=4 avail_mem=7.40 GB):  91%|█████████▏| 21/23 [00:11<00:00,  2.05it/s]
Capturing batches (bs=2 avail_mem=7.35 GB):  91%|█████████▏| 21/23 [00:11<00:00,  2.05it/s]
Capturing batches (bs=1 avail_mem=7.29 GB): 100%|██████████| 23/23 [00:11<00:00,  2.11it/s]
Capturing batches (bs=1 avail_mem=7.29 GB): 100%|██████████| 23/23 [00:11<00:00,  1.95it/s]
[2025-07-28 00:06:20 DP1 TP0] Registering 2208 cuda graph addresses
[2025-07-28 00:06:20 DP1 TP1] Registering 2208 cuda graph addresses

Capturing batches (bs=2 avail_mem=7.35 GB):  96%|█████████▌| 22/23 [00:11<00:00,  2.11it/s]
Capturing batches (bs=1 avail_mem=7.29 GB):  96%|█████████▌| 22/23 [00:11<00:00,  2.11it/s][2025-07-28 00:06:20 DP1 TP3] Registering 2208 cuda graph addresses
[2025-07-28 00:06:20 DP1 TP2] Registering 2208 cuda graph addresses
[2025-07-28 00:06:20 DP1 TP0] Capture cuda graph end. Time elapsed: 12.04 s. mem usage=3.43 GB. avail mem=7.23 GB.
[2025-07-28 00:06:21 DP0 TP2] Registering 2208 cuda graph addresses
[2025-07-28 00:06:21 DP0 TP3] Registering 2208 cuda graph addresses

Capturing batches (bs=1 avail_mem=7.29 GB): 100%|██████████| 23/23 [00:12<00:00,  2.12it/s]
Capturing batches (bs=1 avail_mem=7.29 GB): 100%|██████████| 23/23 [00:12<00:00,  1.87it/s]
[2025-07-28 00:06:21 DP0 TP0] Registering 2208 cuda graph addresses
[2025-07-28 00:06:21 DP0 TP1] Registering 2208 cuda graph addresses
[2025-07-28 00:06:21 DP0 TP0] Capture cuda graph end. Time elapsed: 12.55 s. mem usage=3.43 GB. avail mem=7.23 GB.
[2025-07-28 00:06:22 DP1 TP0] max_total_num_tokens=772820, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=7.23 GB
[2025-07-28 00:06:22 DP0 TP0] max_total_num_tokens=772820, chunked_prefill_size=8192, max_prefill_tokens=16384, max_running_requests=4096, context_len=32768, available_gpu_mem=7.23 GB
[2025-07-28 00:06:24] INFO:     Started server process [1715077]
[2025-07-28 00:06:24] INFO:     Waiting for application startup.
[2025-07-28 00:06:24] INFO:     Application startup complete.
[2025-07-28 00:06:24] INFO:     Uvicorn running on http://127.0.0.1:8003 (Press CTRL+C to quit)
[2025-07-28 00:06:25] INFO:     127.0.0.1:59056 - "GET /get_model_info HTTP/1.1" 200 OK
[2025-07-28 00:06:25 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 7, #cached-token: 0, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:06:25 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 7, #cached-token: 0, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:06:25] INFO:     127.0.0.1:59064 - "GET /health HTTP/1.1" 200 OK
[2025-07-28 00:06:26 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 250, #cached-token: 1, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:06:26 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 212, #cached-token: 1, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:06:26] INFO:     127.0.0.1:59062 - "POST /generate HTTP/1.1" 200 OK
[2025-07-28 00:06:26] The server is fired up and ready to roll!
[2025-07-28 00:06:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 291, full token usage: 0.00, #swa token: 291, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 9.23, #queue-req: 0, 
[2025-07-28 00:06:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 253, full token usage: 0.00, #swa token: 253, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 9.95, #queue-req: 0, 
[2025-07-28 00:06:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 331, full token usage: 0.00, #swa token: 331, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.93, #queue-req: 0, 
[2025-07-28 00:06:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 293, full token usage: 0.00, #swa token: 293, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.57, #queue-req: 0, 
[2025-07-28 00:06:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 371, full token usage: 0.00, #swa token: 371, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.94, #queue-req: 0, 
[2025-07-28 00:06:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 333, full token usage: 0.00, #swa token: 333, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.63, #queue-req: 0, 
[2025-07-28 00:06:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 411, full token usage: 0.00, #swa token: 411, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.80, #queue-req: 0, 
[2025-07-28 00:06:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 373, full token usage: 0.00, #swa token: 373, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.34, #queue-req: 0, 
[2025-07-28 00:06:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 451, full token usage: 0.00, #swa token: 451, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.55, #queue-req: 0, 
[2025-07-28 00:06:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 413, full token usage: 0.00, #swa token: 413, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.58, #queue-req: 0, 
[2025-07-28 00:06:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 491, full token usage: 0.00, #swa token: 491, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.96, #queue-req: 0, 
[2025-07-28 00:06:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 453, full token usage: 0.00, #swa token: 453, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.66, #queue-req: 0, 
[2025-07-28 00:06:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 531, full token usage: 0.00, #swa token: 531, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.57, #queue-req: 0, 
[2025-07-28 00:06:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 493, full token usage: 0.00, #swa token: 493, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.59, #queue-req: 0, 
[2025-07-28 00:06:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 571, full token usage: 0.00, #swa token: 571, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.49, #queue-req: 0, 
[2025-07-28 00:06:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 533, full token usage: 0.00, #swa token: 533, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.41, #queue-req: 0, 
[2025-07-28 00:06:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 611, full token usage: 0.00, #swa token: 611, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.76, #queue-req: 0, 
[2025-07-28 00:06:32] INFO:     127.0.0.1:59076 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:06:32 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 93, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:06:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 192, full token usage: 0.00, #swa token: 192, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 50.85, #queue-req: 0, 
[2025-07-28 00:06:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 651, full token usage: 0.00, #swa token: 651, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:06:33 DP0 TP0] Decode batch. #running-req: 1, #full token: 232, full token usage: 0.00, #swa token: 232, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.10, #queue-req: 0, 
[2025-07-28 00:06:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 691, full token usage: 0.00, #swa token: 691, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.93, #queue-req: 0, 
[2025-07-28 00:06:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 272, full token usage: 0.00, #swa token: 272, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.79, #queue-req: 0, 
[2025-07-28 00:06:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 731, full token usage: 0.00, #swa token: 731, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.93, #queue-req: 0, 
[2025-07-28 00:06:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 312, full token usage: 0.00, #swa token: 312, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:06:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 771, full token usage: 0.00, #swa token: 771, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.83, #queue-req: 0, 
[2025-07-28 00:06:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 352, full token usage: 0.00, #swa token: 352, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.75, #queue-req: 0, 
[2025-07-28 00:06:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 811, full token usage: 0.00, #swa token: 811, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.96, #queue-req: 0, 
[2025-07-28 00:06:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 392, full token usage: 0.00, #swa token: 392, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.71, #queue-req: 0, 
[2025-07-28 00:06:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 851, full token usage: 0.00, #swa token: 851, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.89, #queue-req: 0, 
[2025-07-28 00:06:36] INFO:     127.0.0.1:59086 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:06:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 432, full token usage: 0.00, #swa token: 432, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.72, #queue-req: 0, 
[2025-07-28 00:06:36 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 212, #cached-token: 98, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:06:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 339, full token usage: 0.00, #swa token: 339, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 51.49, #queue-req: 0, 
[2025-07-28 00:06:37 DP0 TP0] Decode batch. #running-req: 1, #full token: 472, full token usage: 0.00, #swa token: 472, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.71, #queue-req: 0, 
[2025-07-28 00:06:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 379, full token usage: 0.00, #swa token: 379, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.98, #queue-req: 0, 
[2025-07-28 00:06:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 512, full token usage: 0.00, #swa token: 512, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.68, #queue-req: 0, 
[2025-07-28 00:06:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 419, full token usage: 0.00, #swa token: 419, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.05, #queue-req: 0, 
[2025-07-28 00:06:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 552, full token usage: 0.00, #swa token: 552, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.63, #queue-req: 0, 
[2025-07-28 00:06:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 459, full token usage: 0.00, #swa token: 459, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.07, #queue-req: 0, 
[2025-07-28 00:06:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 592, full token usage: 0.00, #swa token: 592, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.62, #queue-req: 0, 
[2025-07-28 00:06:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 499, full token usage: 0.00, #swa token: 499, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.99, #queue-req: 0, 
[2025-07-28 00:06:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 632, full token usage: 0.00, #swa token: 632, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.58, #queue-req: 0, 
[2025-07-28 00:06:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 539, full token usage: 0.00, #swa token: 539, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.97, #queue-req: 0, 
[2025-07-28 00:06:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 672, full token usage: 0.00, #swa token: 672, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.57, #queue-req: 0, 
[2025-07-28 00:06:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 579, full token usage: 0.00, #swa token: 579, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.66, #queue-req: 0, 
[2025-07-28 00:06:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 712, full token usage: 0.00, #swa token: 712, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.53, #queue-req: 0, 
[2025-07-28 00:06:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 619, full token usage: 0.00, #swa token: 619, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.92, #queue-req: 0, 
[2025-07-28 00:06:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 752, full token usage: 0.00, #swa token: 752, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.56, #queue-req: 0, 
[2025-07-28 00:06:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 659, full token usage: 0.00, #swa token: 659, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.93, #queue-req: 0, 
[2025-07-28 00:06:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 792, full token usage: 0.00, #swa token: 792, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.63, #queue-req: 0, 
[2025-07-28 00:06:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 699, full token usage: 0.00, #swa token: 699, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.85, #queue-req: 0, 
[2025-07-28 00:06:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 832, full token usage: 0.00, #swa token: 832, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.60, #queue-req: 0, 
[2025-07-28 00:06:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 739, full token usage: 0.00, #swa token: 739, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.88, #queue-req: 0, 
[2025-07-28 00:06:44] INFO:     127.0.0.1:43498 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:06:44 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 134, #cached-token: 98, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:06:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 239, full token usage: 0.00, #swa token: 239, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 51.96, #queue-req: 0, 
[2025-07-28 00:06:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 779, full token usage: 0.00, #swa token: 779, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.92, #queue-req: 0, 
[2025-07-28 00:06:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 279, full token usage: 0.00, #swa token: 279, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:06:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 819, full token usage: 0.00, #swa token: 819, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.87, #queue-req: 0, 
[2025-07-28 00:06:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 319, full token usage: 0.00, #swa token: 319, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.73, #queue-req: 0, 
[2025-07-28 00:06:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 859, full token usage: 0.00, #swa token: 859, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.71, #queue-req: 0, 
[2025-07-28 00:06:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 359, full token usage: 0.00, #swa token: 359, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:06:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 899, full token usage: 0.00, #swa token: 899, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.94, #queue-req: 0, 
[2025-07-28 00:06:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 399, full token usage: 0.00, #swa token: 399, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 58.79, #queue-req: 0, 
[2025-07-28 00:06:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 939, full token usage: 0.00, #swa token: 939, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.92, #queue-req: 0, 
[2025-07-28 00:06:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 439, full token usage: 0.00, #swa token: 439, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.74, #queue-req: 0, 
[2025-07-28 00:06:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 979, full token usage: 0.00, #swa token: 979, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.95, #queue-req: 0, 
[2025-07-28 00:06:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 479, full token usage: 0.00, #swa token: 479, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.71, #queue-req: 0, 
[2025-07-28 00:06:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 1019, full token usage: 0.00, #swa token: 1019, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.93, #queue-req: 0, 
[2025-07-28 00:06:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 519, full token usage: 0.00, #swa token: 519, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:06:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 1059, full token usage: 0.00, #swa token: 1059, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.89, #queue-req: 0, 
[2025-07-28 00:06:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 559, full token usage: 0.00, #swa token: 559, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.64, #queue-req: 0, 
[2025-07-28 00:06:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 1099, full token usage: 0.00, #swa token: 1099, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.91, #queue-req: 0, 
[2025-07-28 00:06:50 DP0 TP0] Decode batch. #running-req: 1, #full token: 599, full token usage: 0.00, #swa token: 599, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.59, #queue-req: 0, 
[2025-07-28 00:06:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 1139, full token usage: 0.00, #swa token: 1139, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.72, #queue-req: 0, 
[2025-07-28 00:06:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 639, full token usage: 0.00, #swa token: 639, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.63, #queue-req: 0, 
[2025-07-28 00:06:51 DP1 TP0] Decode batch. #running-req: 1, #full token: 1179, full token usage: 0.00, #swa token: 1179, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.84, #queue-req: 0, 
[2025-07-28 00:06:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 679, full token usage: 0.00, #swa token: 679, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.58, #queue-req: 0, 
[2025-07-28 00:06:51] INFO:     127.0.0.1:43508 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:06:51 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 136, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:06:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 245, full token usage: 0.00, #swa token: 245, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 51.47, #queue-req: 0, 
[2025-07-28 00:06:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 719, full token usage: 0.00, #swa token: 719, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.62, #queue-req: 0, 
[2025-07-28 00:06:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 285, full token usage: 0.00, #swa token: 285, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.04, #queue-req: 0, 
[2025-07-28 00:06:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 759, full token usage: 0.00, #swa token: 759, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.61, #queue-req: 0, 
[2025-07-28 00:06:53] INFO:     127.0.0.1:37076 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:06:53 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 193, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:06:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 325, full token usage: 0.00, #swa token: 325, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.77, #queue-req: 0, 
[2025-07-28 00:06:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 311, full token usage: 0.00, #swa token: 311, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.11, #queue-req: 0, 
[2025-07-28 00:06:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 365, full token usage: 0.00, #swa token: 365, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.01, #queue-req: 0, 
[2025-07-28 00:06:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 351, full token usage: 0.00, #swa token: 351, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.77, #queue-req: 0, 
[2025-07-28 00:06:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 405, full token usage: 0.00, #swa token: 405, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.99, #queue-req: 0, 
[2025-07-28 00:06:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 391, full token usage: 0.00, #swa token: 391, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.75, #queue-req: 0, 
[2025-07-28 00:06:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 445, full token usage: 0.00, #swa token: 445, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.01, #queue-req: 0, 
[2025-07-28 00:06:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 431, full token usage: 0.00, #swa token: 431, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.72, #queue-req: 0, 
[2025-07-28 00:06:56 DP1 TP0] Decode batch. #running-req: 1, #full token: 485, full token usage: 0.00, #swa token: 485, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.02, #queue-req: 0, 
[2025-07-28 00:06:56 DP0 TP0] Decode batch. #running-req: 1, #full token: 471, full token usage: 0.00, #swa token: 471, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.72, #queue-req: 0, 
[2025-07-28 00:06:56 DP1 TP0] Decode batch. #running-req: 1, #full token: 525, full token usage: 0.00, #swa token: 525, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.06, #queue-req: 0, 
[2025-07-28 00:06:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 511, full token usage: 0.00, #swa token: 511, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.70, #queue-req: 0, 
[2025-07-28 00:06:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 565, full token usage: 0.00, #swa token: 565, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.98, #queue-req: 0, 
[2025-07-28 00:06:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 551, full token usage: 0.00, #swa token: 551, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.59, #queue-req: 0, 
[2025-07-28 00:06:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 605, full token usage: 0.00, #swa token: 605, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.95, #queue-req: 0, 
[2025-07-28 00:06:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 591, full token usage: 0.00, #swa token: 591, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.66, #queue-req: 0, 
[2025-07-28 00:06:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 645, full token usage: 0.00, #swa token: 645, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.99, #queue-req: 0, 
[2025-07-28 00:06:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 631, full token usage: 0.00, #swa token: 631, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.59, #queue-req: 0, 
[2025-07-28 00:06:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 685, full token usage: 0.00, #swa token: 685, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.01, #queue-req: 0, 
[2025-07-28 00:06:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 671, full token usage: 0.00, #swa token: 671, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.60, #queue-req: 0, 
[2025-07-28 00:07:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 725, full token usage: 0.00, #swa token: 725, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.90, #queue-req: 0, 
[2025-07-28 00:07:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 711, full token usage: 0.00, #swa token: 711, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.62, #queue-req: 0, 
[2025-07-28 00:07:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 765, full token usage: 0.00, #swa token: 765, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.93, #queue-req: 0, 
[2025-07-28 00:07:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 751, full token usage: 0.00, #swa token: 751, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.65, #queue-req: 0, 
[2025-07-28 00:07:01 DP1 TP0] Decode batch. #running-req: 1, #full token: 805, full token usage: 0.00, #swa token: 805, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.01, #queue-req: 0, 
[2025-07-28 00:07:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 791, full token usage: 0.00, #swa token: 791, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.72, #queue-req: 0, 
[2025-07-28 00:07:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 845, full token usage: 0.00, #swa token: 845, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.96, #queue-req: 0, 
[2025-07-28 00:07:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 831, full token usage: 0.00, #swa token: 831, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.74, #queue-req: 0, 
[2025-07-28 00:07:02] INFO:     127.0.0.1:37084 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:07:02 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 181, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:07:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 286, full token usage: 0.00, #swa token: 286, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.35, #queue-req: 0, 
[2025-07-28 00:07:03 DP0 TP0] Decode batch. #running-req: 1, #full token: 871, full token usage: 0.00, #swa token: 871, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:07:03 DP1 TP0] Decode batch. #running-req: 1, #full token: 326, full token usage: 0.00, #swa token: 326, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.90, #queue-req: 0, 
[2025-07-28 00:07:03 DP0 TP0] Decode batch. #running-req: 1, #full token: 911, full token usage: 0.00, #swa token: 911, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.75, #queue-req: 0, 
[2025-07-28 00:07:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 366, full token usage: 0.00, #swa token: 366, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.96, #queue-req: 0, 
[2025-07-28 00:07:04 DP0 TP0] Decode batch. #running-req: 1, #full token: 951, full token usage: 0.00, #swa token: 951, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:07:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 406, full token usage: 0.00, #swa token: 406, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.66, #queue-req: 0, 
[2025-07-28 00:07:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 991, full token usage: 0.00, #swa token: 991, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:07:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 446, full token usage: 0.00, #swa token: 446, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.62, #queue-req: 0, 
[2025-07-28 00:07:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 1031, full token usage: 0.00, #swa token: 1031, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.60, #queue-req: 0, 
[2025-07-28 00:07:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 486, full token usage: 0.00, #swa token: 486, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.93, #queue-req: 0, 
[2025-07-28 00:07:06 DP0 TP0] Decode batch. #running-req: 1, #full token: 1071, full token usage: 0.00, #swa token: 1071, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.63, #queue-req: 0, 
[2025-07-28 00:07:06] INFO:     127.0.0.1:39268 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:07:06 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 222, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:07:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 526, full token usage: 0.00, #swa token: 526, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.90, #queue-req: 0, 
[2025-07-28 00:07:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 345, full token usage: 0.00, #swa token: 345, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.37, #queue-req: 0, 
[2025-07-28 00:07:07 DP1 TP0] Decode batch. #running-req: 1, #full token: 566, full token usage: 0.00, #swa token: 566, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.84, #queue-req: 0, 
[2025-07-28 00:07:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 385, full token usage: 0.00, #swa token: 385, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.85, #queue-req: 0, 
[2025-07-28 00:07:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 606, full token usage: 0.00, #swa token: 606, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:07:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 425, full token usage: 0.00, #swa token: 425, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.83, #queue-req: 0, 
[2025-07-28 00:07:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 646, full token usage: 0.00, #swa token: 646, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.85, #queue-req: 0, 
[2025-07-28 00:07:09 DP0 TP0] Decode batch. #running-req: 1, #full token: 465, full token usage: 0.00, #swa token: 465, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:07:09 DP1 TP0] Decode batch. #running-req: 1, #full token: 686, full token usage: 0.00, #swa token: 686, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.84, #queue-req: 0, 
[2025-07-28 00:07:09 DP0 TP0] Decode batch. #running-req: 1, #full token: 505, full token usage: 0.00, #swa token: 505, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.83, #queue-req: 0, 
[2025-07-28 00:07:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 726, full token usage: 0.00, #swa token: 726, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.79, #queue-req: 0, 
[2025-07-28 00:07:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 545, full token usage: 0.00, #swa token: 545, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.75, #queue-req: 0, 
[2025-07-28 00:07:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 766, full token usage: 0.00, #swa token: 766, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.74, #queue-req: 0, 
[2025-07-28 00:07:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 585, full token usage: 0.00, #swa token: 585, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.73, #queue-req: 0, 
[2025-07-28 00:07:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 806, full token usage: 0.00, #swa token: 806, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.74, #queue-req: 0, 
[2025-07-28 00:07:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 625, full token usage: 0.00, #swa token: 625, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.71, #queue-req: 0, 
[2025-07-28 00:07:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 846, full token usage: 0.00, #swa token: 846, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.70, #queue-req: 0, 
[2025-07-28 00:07:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 665, full token usage: 0.00, #swa token: 665, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.70, #queue-req: 0, 
[2025-07-28 00:07:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 886, full token usage: 0.00, #swa token: 886, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:07:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 705, full token usage: 0.00, #swa token: 705, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.71, #queue-req: 0, 
[2025-07-28 00:07:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 926, full token usage: 0.00, #swa token: 926, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:07:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 745, full token usage: 0.00, #swa token: 745, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.61, #queue-req: 0, 
[2025-07-28 00:07:14 DP1 TP0] Decode batch. #running-req: 1, #full token: 966, full token usage: 0.00, #swa token: 966, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.58, #queue-req: 0, 
[2025-07-28 00:07:14 DP0 TP0] Decode batch. #running-req: 1, #full token: 785, full token usage: 0.00, #swa token: 785, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.66, #queue-req: 0, 
[2025-07-28 00:07:15 DP1 TP0] Decode batch. #running-req: 1, #full token: 1006, full token usage: 0.00, #swa token: 1006, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.75, #queue-req: 0, 
[2025-07-28 00:07:15 DP0 TP0] Decode batch. #running-req: 1, #full token: 825, full token usage: 0.00, #swa token: 825, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:07:15 DP1 TP0] Decode batch. #running-req: 1, #full token: 1046, full token usage: 0.00, #swa token: 1046, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.60, #queue-req: 0, 
[2025-07-28 00:07:15] INFO:     127.0.0.1:41002 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:07:15 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 173, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:07:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 865, full token usage: 0.00, #swa token: 865, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.61, #queue-req: 0, 
[2025-07-28 00:07:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 296, full token usage: 0.00, #swa token: 296, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.21, #queue-req: 0, 
[2025-07-28 00:07:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 905, full token usage: 0.00, #swa token: 905, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.65, #queue-req: 0, 
[2025-07-28 00:07:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 336, full token usage: 0.00, #swa token: 336, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.00, #queue-req: 0, 
[2025-07-28 00:07:17 DP0 TP0] Decode batch. #running-req: 1, #full token: 945, full token usage: 0.00, #swa token: 945, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:07:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 376, full token usage: 0.00, #swa token: 376, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.98, #queue-req: 0, 
[2025-07-28 00:07:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 985, full token usage: 0.00, #swa token: 985, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.70, #queue-req: 0, 
[2025-07-28 00:07:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 416, full token usage: 0.00, #swa token: 416, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.01, #queue-req: 0, 
[2025-07-28 00:07:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 1025, full token usage: 0.00, #swa token: 1025, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.71, #queue-req: 0, 
[2025-07-28 00:07:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 456, full token usage: 0.00, #swa token: 456, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.83, #queue-req: 0, 
[2025-07-28 00:07:19 DP0 TP0] Decode batch. #running-req: 1, #full token: 1065, full token usage: 0.00, #swa token: 1065, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.70, #queue-req: 0, 
[2025-07-28 00:07:19] INFO:     127.0.0.1:41010 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:07:19 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 158, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:07:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 496, full token usage: 0.00, #swa token: 496, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.84, #queue-req: 0, 
[2025-07-28 00:07:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 288, full token usage: 0.00, #swa token: 288, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.46, #queue-req: 0, 
[2025-07-28 00:07:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 536, full token usage: 0.00, #swa token: 536, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.93, #queue-req: 0, 
[2025-07-28 00:07:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 328, full token usage: 0.00, #swa token: 328, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 58.90, #queue-req: 0, 
[2025-07-28 00:07:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 576, full token usage: 0.00, #swa token: 576, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.91, #queue-req: 0, 
[2025-07-28 00:07:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 368, full token usage: 0.00, #swa token: 368, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.84, #queue-req: 0, 
[2025-07-28 00:07:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 616, full token usage: 0.00, #swa token: 616, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.94, #queue-req: 0, 
[2025-07-28 00:07:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 408, full token usage: 0.00, #swa token: 408, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:07:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 656, full token usage: 0.00, #swa token: 656, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.86, #queue-req: 0, 
[2025-07-28 00:07:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 448, full token usage: 0.00, #swa token: 448, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:07:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 696, full token usage: 0.00, #swa token: 696, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.80, #queue-req: 0, 
[2025-07-28 00:07:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 488, full token usage: 0.00, #swa token: 488, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:07:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 736, full token usage: 0.00, #swa token: 736, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.90, #queue-req: 0, 
[2025-07-28 00:07:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 528, full token usage: 0.00, #swa token: 528, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.78, #queue-req: 0, 
[2025-07-28 00:07:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 776, full token usage: 0.00, #swa token: 776, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.95, #queue-req: 0, 
[2025-07-28 00:07:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 568, full token usage: 0.00, #swa token: 568, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.70, #queue-req: 0, 
[2025-07-28 00:07:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 816, full token usage: 0.00, #swa token: 816, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.93, #queue-req: 0, 
[2025-07-28 00:07:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 608, full token usage: 0.00, #swa token: 608, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.71, #queue-req: 0, 
[2025-07-28 00:07:25] INFO:     127.0.0.1:46382 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:07:25 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 149, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:07:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 259, full token usage: 0.00, #swa token: 259, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.59, #queue-req: 0, 
[2025-07-28 00:07:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 648, full token usage: 0.00, #swa token: 648, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:07:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 299, full token usage: 0.00, #swa token: 299, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.13, #queue-req: 0, 
[2025-07-28 00:07:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 688, full token usage: 0.00, #swa token: 688, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.65, #queue-req: 0, 
[2025-07-28 00:07:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 339, full token usage: 0.00, #swa token: 339, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.00, #queue-req: 0, 
[2025-07-28 00:07:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 728, full token usage: 0.00, #swa token: 728, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:07:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 379, full token usage: 0.00, #swa token: 379, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.70, #queue-req: 0, 
[2025-07-28 00:07:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 768, full token usage: 0.00, #swa token: 768, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.68, #queue-req: 0, 
[2025-07-28 00:07:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 419, full token usage: 0.00, #swa token: 419, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:07:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 808, full token usage: 0.00, #swa token: 808, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:07:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 459, full token usage: 0.00, #swa token: 459, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:07:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 848, full token usage: 0.00, #swa token: 848, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.64, #queue-req: 0, 
[2025-07-28 00:07:29] INFO:     127.0.0.1:46392 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:07:29 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 186, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:07:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 499, full token usage: 0.00, #swa token: 499, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.11, #queue-req: 0, 
[2025-07-28 00:07:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 322, full token usage: 0.00, #swa token: 322, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 51.81, #queue-req: 0, 
[2025-07-28 00:07:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 539, full token usage: 0.00, #swa token: 539, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.00, #queue-req: 0, 
[2025-07-28 00:07:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 362, full token usage: 0.00, #swa token: 362, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.86, #queue-req: 0, 
[2025-07-28 00:07:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 579, full token usage: 0.00, #swa token: 579, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.91, #queue-req: 0, 
[2025-07-28 00:07:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 402, full token usage: 0.00, #swa token: 402, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.83, #queue-req: 0, 
[2025-07-28 00:07:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 619, full token usage: 0.00, #swa token: 619, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.00, #queue-req: 0, 
[2025-07-28 00:07:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 442, full token usage: 0.00, #swa token: 442, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.83, #queue-req: 0, 
[2025-07-28 00:07:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 659, full token usage: 0.00, #swa token: 659, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.95, #queue-req: 0, 
[2025-07-28 00:07:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 482, full token usage: 0.00, #swa token: 482, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:07:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 699, full token usage: 0.00, #swa token: 699, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.91, #queue-req: 0, 
[2025-07-28 00:07:33 DP0 TP0] Decode batch. #running-req: 1, #full token: 522, full token usage: 0.00, #swa token: 522, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.83, #queue-req: 0, 
[2025-07-28 00:07:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 739, full token usage: 0.00, #swa token: 739, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.01, #queue-req: 0, 
[2025-07-28 00:07:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 562, full token usage: 0.00, #swa token: 562, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.70, #queue-req: 0, 
[2025-07-28 00:07:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 779, full token usage: 0.00, #swa token: 779, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.96, #queue-req: 0, 
[2025-07-28 00:07:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 602, full token usage: 0.00, #swa token: 602, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:07:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 819, full token usage: 0.00, #swa token: 819, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.04, #queue-req: 0, 
[2025-07-28 00:07:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 642, full token usage: 0.00, #swa token: 642, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.66, #queue-req: 0, 
[2025-07-28 00:07:35] INFO:     127.0.0.1:38168 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:07:35 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 196, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:07:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 300, full token usage: 0.00, #swa token: 300, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.50, #queue-req: 0, 
[2025-07-28 00:07:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 682, full token usage: 0.00, #swa token: 682, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.62, #queue-req: 0, 
[2025-07-28 00:07:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 340, full token usage: 0.00, #swa token: 340, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.16, #queue-req: 0, 
[2025-07-28 00:07:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 722, full token usage: 0.00, #swa token: 722, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:07:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 380, full token usage: 0.00, #swa token: 380, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.14, #queue-req: 0, 
[2025-07-28 00:07:37 DP0 TP0] Decode batch. #running-req: 1, #full token: 762, full token usage: 0.00, #swa token: 762, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.65, #queue-req: 0, 
[2025-07-28 00:07:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 420, full token usage: 0.00, #swa token: 420, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.15, #queue-req: 0, 
[2025-07-28 00:07:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 802, full token usage: 0.00, #swa token: 802, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.61, #queue-req: 0, 
[2025-07-28 00:07:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 460, full token usage: 0.00, #swa token: 460, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.14, #queue-req: 0, 
[2025-07-28 00:07:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 842, full token usage: 0.00, #swa token: 842, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.65, #queue-req: 0, 
[2025-07-28 00:07:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 500, full token usage: 0.00, #swa token: 500, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.17, #queue-req: 0, 
[2025-07-28 00:07:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 882, full token usage: 0.00, #swa token: 882, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.64, #queue-req: 0, 
[2025-07-28 00:07:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 540, full token usage: 0.00, #swa token: 540, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.99, #queue-req: 0, 
[2025-07-28 00:07:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 922, full token usage: 0.00, #swa token: 922, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.62, #queue-req: 0, 
[2025-07-28 00:07:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 580, full token usage: 0.00, #swa token: 580, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.59, #queue-req: 0, 
[2025-07-28 00:07:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 962, full token usage: 0.00, #swa token: 962, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.62, #queue-req: 0, 
[2025-07-28 00:07:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 620, full token usage: 0.00, #swa token: 620, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.64, #queue-req: 0, 
[2025-07-28 00:07:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 1002, full token usage: 0.00, #swa token: 1002, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.66, #queue-req: 0, 
[2025-07-28 00:07:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 660, full token usage: 0.00, #swa token: 660, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.72, #queue-req: 0, 
[2025-07-28 00:07:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 1042, full token usage: 0.00, #swa token: 1042, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.63, #queue-req: 0, 
[2025-07-28 00:07:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 700, full token usage: 0.00, #swa token: 700, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.04, #queue-req: 0, 
[2025-07-28 00:07:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 1082, full token usage: 0.00, #swa token: 1082, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.70, #queue-req: 0, 
[2025-07-28 00:07:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 740, full token usage: 0.00, #swa token: 740, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.04, #queue-req: 0, 
[2025-07-28 00:07:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 1122, full token usage: 0.00, #swa token: 1122, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.70, #queue-req: 0, 
[2025-07-28 00:07:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 780, full token usage: 0.00, #swa token: 780, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.04, #queue-req: 0, 
[2025-07-28 00:07:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 1162, full token usage: 0.00, #swa token: 1162, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.68, #queue-req: 0, 
[2025-07-28 00:07:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 820, full token usage: 0.00, #swa token: 820, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.03, #queue-req: 0, 
[2025-07-28 00:07:44] INFO:     127.0.0.1:38170 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:07:44 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:07:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 261, full token usage: 0.00, #swa token: 261, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.40, #queue-req: 0, 
[2025-07-28 00:07:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 860, full token usage: 0.00, #swa token: 860, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.01, #queue-req: 0, 
[2025-07-28 00:07:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 301, full token usage: 0.00, #swa token: 301, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:07:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 900, full token usage: 0.00, #swa token: 900, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.02, #queue-req: 0, 
[2025-07-28 00:07:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 341, full token usage: 0.00, #swa token: 341, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.84, #queue-req: 0, 
[2025-07-28 00:07:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 940, full token usage: 0.00, #swa token: 940, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.04, #queue-req: 0, 
[2025-07-28 00:07:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 381, full token usage: 0.00, #swa token: 381, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.89, #queue-req: 0, 
[2025-07-28 00:07:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 980, full token usage: 0.00, #swa token: 980, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.05, #queue-req: 0, 
[2025-07-28 00:07:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 421, full token usage: 0.00, #swa token: 421, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 58.80, #queue-req: 0, 
[2025-07-28 00:07:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 1020, full token usage: 0.00, #swa token: 1020, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.02, #queue-req: 0, 
[2025-07-28 00:07:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 461, full token usage: 0.00, #swa token: 461, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.83, #queue-req: 0, 
[2025-07-28 00:07:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 1060, full token usage: 0.00, #swa token: 1060, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.00, #queue-req: 0, 
[2025-07-28 00:07:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 501, full token usage: 0.00, #swa token: 501, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.89, #queue-req: 0, 
[2025-07-28 00:07:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 1100, full token usage: 0.00, #swa token: 1100, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.93, #queue-req: 0, 
[2025-07-28 00:07:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 541, full token usage: 0.00, #swa token: 541, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.74, #queue-req: 0, 
[2025-07-28 00:07:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 1140, full token usage: 0.00, #swa token: 1140, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.96, #queue-req: 0, 
[2025-07-28 00:07:50 DP0 TP0] Decode batch. #running-req: 1, #full token: 581, full token usage: 0.00, #swa token: 581, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.71, #queue-req: 0, 
[2025-07-28 00:07:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 1180, full token usage: 0.00, #swa token: 1180, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.95, #queue-req: 0, 
[2025-07-28 00:07:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 621, full token usage: 0.00, #swa token: 621, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:07:51 DP1 TP0] Decode batch. #running-req: 1, #full token: 1220, full token usage: 0.00, #swa token: 1220, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.91, #queue-req: 0, 
[2025-07-28 00:07:51] INFO:     127.0.0.1:58258 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:07:51 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 134, #cached-token: 101, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:07:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 661, full token usage: 0.00, #swa token: 661, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.64, #queue-req: 0, 
[2025-07-28 00:07:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 260, full token usage: 0.00, #swa token: 260, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.76, #queue-req: 0, 
[2025-07-28 00:07:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 701, full token usage: 0.00, #swa token: 701, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.68, #queue-req: 0, 
[2025-07-28 00:07:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 300, full token usage: 0.00, #swa token: 300, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.19, #queue-req: 0, 
[2025-07-28 00:07:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 741, full token usage: 0.00, #swa token: 741, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.68, #queue-req: 0, 
[2025-07-28 00:07:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 340, full token usage: 0.00, #swa token: 340, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.07, #queue-req: 0, 
[2025-07-28 00:07:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 781, full token usage: 0.00, #swa token: 781, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:07:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 380, full token usage: 0.00, #swa token: 380, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.74, #queue-req: 0, 
[2025-07-28 00:07:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 821, full token usage: 0.00, #swa token: 821, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:07:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 420, full token usage: 0.00, #swa token: 420, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.68, #queue-req: 0, 
[2025-07-28 00:07:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 861, full token usage: 0.00, #swa token: 861, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:07:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 460, full token usage: 0.00, #swa token: 460, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:07:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 901, full token usage: 0.00, #swa token: 901, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:07:56 DP1 TP0] Decode batch. #running-req: 1, #full token: 500, full token usage: 0.00, #swa token: 500, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:07:56 DP0 TP0] Decode batch. #running-req: 1, #full token: 941, full token usage: 0.00, #swa token: 941, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:07:56 DP1 TP0] Decode batch. #running-req: 1, #full token: 540, full token usage: 0.00, #swa token: 540, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:07:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 981, full token usage: 0.00, #swa token: 981, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.70, #queue-req: 0, 
[2025-07-28 00:07:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 580, full token usage: 0.00, #swa token: 580, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.68, #queue-req: 0, 
[2025-07-28 00:07:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 1021, full token usage: 0.00, #swa token: 1021, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:07:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 620, full token usage: 0.00, #swa token: 620, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:07:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 1061, full token usage: 0.00, #swa token: 1061, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:07:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 660, full token usage: 0.00, #swa token: 660, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.70, #queue-req: 0, 
[2025-07-28 00:07:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 1101, full token usage: 0.00, #swa token: 1101, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:07:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 700, full token usage: 0.00, #swa token: 700, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:07:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 1141, full token usage: 0.00, #swa token: 1141, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.68, #queue-req: 0, 
[2025-07-28 00:08:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 740, full token usage: 0.00, #swa token: 740, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:08:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 1181, full token usage: 0.00, #swa token: 1181, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.66, #queue-req: 0, 
[2025-07-28 00:08:00] INFO:     127.0.0.1:33232 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:08:00 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 200, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:08:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 780, full token usage: 0.00, #swa token: 780, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.66, #queue-req: 0, 
[2025-07-28 00:08:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 333, full token usage: 0.00, #swa token: 333, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.48, #queue-req: 0, 
[2025-07-28 00:08:01 DP1 TP0] Decode batch. #running-req: 1, #full token: 820, full token usage: 0.00, #swa token: 820, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.88, #queue-req: 0, 
[2025-07-28 00:08:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 373, full token usage: 0.00, #swa token: 373, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.85, #queue-req: 0, 
[2025-07-28 00:08:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 860, full token usage: 0.00, #swa token: 860, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.90, #queue-req: 0, 
[2025-07-28 00:08:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 413, full token usage: 0.00, #swa token: 413, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.84, #queue-req: 0, 
[2025-07-28 00:08:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 900, full token usage: 0.00, #swa token: 900, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.85, #queue-req: 0, 
[2025-07-28 00:08:03 DP0 TP0] Decode batch. #running-req: 1, #full token: 453, full token usage: 0.00, #swa token: 453, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.87, #queue-req: 0, 
[2025-07-28 00:08:03 DP1 TP0] Decode batch. #running-req: 1, #full token: 940, full token usage: 0.00, #swa token: 940, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.88, #queue-req: 0, 
[2025-07-28 00:08:03 DP0 TP0] Decode batch. #running-req: 1, #full token: 493, full token usage: 0.00, #swa token: 493, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.88, #queue-req: 0, 
[2025-07-28 00:08:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 980, full token usage: 0.00, #swa token: 980, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.88, #queue-req: 0, 
[2025-07-28 00:08:04 DP0 TP0] Decode batch. #running-req: 1, #full token: 533, full token usage: 0.00, #swa token: 533, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.86, #queue-req: 0, 
[2025-07-28 00:08:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 1020, full token usage: 0.00, #swa token: 1020, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.88, #queue-req: 0, 
[2025-07-28 00:08:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 573, full token usage: 0.00, #swa token: 573, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.79, #queue-req: 0, 
[2025-07-28 00:08:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 1060, full token usage: 0.00, #swa token: 1060, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:08:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 613, full token usage: 0.00, #swa token: 613, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.73, #queue-req: 0, 
[2025-07-28 00:08:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 1100, full token usage: 0.00, #swa token: 1100, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.94, #queue-req: 0, 
[2025-07-28 00:08:06 DP0 TP0] Decode batch. #running-req: 1, #full token: 653, full token usage: 0.00, #swa token: 653, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.78, #queue-req: 0, 
[2025-07-28 00:08:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 1140, full token usage: 0.00, #swa token: 1140, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.95, #queue-req: 0, 
[2025-07-28 00:08:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 693, full token usage: 0.00, #swa token: 693, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.75, #queue-req: 0, 
[2025-07-28 00:08:07 DP1 TP0] Decode batch. #running-req: 1, #full token: 1180, full token usage: 0.00, #swa token: 1180, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.90, #queue-req: 0, 
[2025-07-28 00:08:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 733, full token usage: 0.00, #swa token: 733, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.73, #queue-req: 0, 
[2025-07-28 00:08:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 1220, full token usage: 0.00, #swa token: 1220, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.90, #queue-req: 0, 
[2025-07-28 00:08:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 773, full token usage: 0.00, #swa token: 773, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.77, #queue-req: 0, 
[2025-07-28 00:08:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 1260, full token usage: 0.00, #swa token: 1260, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.91, #queue-req: 0, 
[2025-07-28 00:08:09 DP0 TP0] Decode batch. #running-req: 1, #full token: 813, full token usage: 0.00, #swa token: 813, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.76, #queue-req: 0, 
[2025-07-28 00:08:09 DP1 TP0] Decode batch. #running-req: 1, #full token: 1300, full token usage: 0.00, #swa token: 1300, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.91, #queue-req: 0, 
[2025-07-28 00:08:09] INFO:     127.0.0.1:33234 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:08:09 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 189, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:08:09 DP0 TP0] Decode batch. #running-req: 1, #full token: 853, full token usage: 0.00, #swa token: 853, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.77, #queue-req: 0, 
[2025-07-28 00:08:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 312, full token usage: 0.00, #swa token: 312, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.47, #queue-req: 0, 
[2025-07-28 00:08:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 893, full token usage: 0.00, #swa token: 893, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.74, #queue-req: 0, 
[2025-07-28 00:08:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 352, full token usage: 0.00, #swa token: 352, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.01, #queue-req: 0, 
[2025-07-28 00:08:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 933, full token usage: 0.00, #swa token: 933, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.78, #queue-req: 0, 
[2025-07-28 00:08:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 392, full token usage: 0.00, #swa token: 392, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.86, #queue-req: 0, 
[2025-07-28 00:08:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 973, full token usage: 0.00, #swa token: 973, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.80, #queue-req: 0, 
[2025-07-28 00:08:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 432, full token usage: 0.00, #swa token: 432, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.80, #queue-req: 0, 
[2025-07-28 00:08:12] INFO:     127.0.0.1:59078 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:08:12 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 175, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:08:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 276, full token usage: 0.00, #swa token: 276, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.58, #queue-req: 0, 
[2025-07-28 00:08:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 472, full token usage: 0.00, #swa token: 472, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.83, #queue-req: 0, 
[2025-07-28 00:08:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 316, full token usage: 0.00, #swa token: 316, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.93, #queue-req: 0, 
[2025-07-28 00:08:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 512, full token usage: 0.00, #swa token: 512, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.04, #queue-req: 0, 
[2025-07-28 00:08:14 DP0 TP0] Decode batch. #running-req: 1, #full token: 356, full token usage: 0.00, #swa token: 356, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.88, #queue-req: 0, 
[2025-07-28 00:08:14 DP1 TP0] Decode batch. #running-req: 1, #full token: 552, full token usage: 0.00, #swa token: 552, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.97, #queue-req: 0, 
[2025-07-28 00:08:14 DP0 TP0] Decode batch. #running-req: 1, #full token: 396, full token usage: 0.00, #swa token: 396, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.84, #queue-req: 0, 
[2025-07-28 00:08:14 DP1 TP0] Decode batch. #running-req: 1, #full token: 592, full token usage: 0.00, #swa token: 592, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.91, #queue-req: 0, 
[2025-07-28 00:08:15 DP0 TP0] Decode batch. #running-req: 1, #full token: 436, full token usage: 0.00, #swa token: 436, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.83, #queue-req: 0, 
[2025-07-28 00:08:15 DP1 TP0] Decode batch. #running-req: 1, #full token: 632, full token usage: 0.00, #swa token: 632, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.94, #queue-req: 0, 
[2025-07-28 00:08:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 476, full token usage: 0.00, #swa token: 476, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.84, #queue-req: 0, 
[2025-07-28 00:08:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 672, full token usage: 0.00, #swa token: 672, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.87, #queue-req: 0, 
[2025-07-28 00:08:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 516, full token usage: 0.00, #swa token: 516, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:08:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 712, full token usage: 0.00, #swa token: 712, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.91, #queue-req: 0, 
[2025-07-28 00:08:17 DP0 TP0] Decode batch. #running-req: 1, #full token: 556, full token usage: 0.00, #swa token: 556, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.73, #queue-req: 0, 
[2025-07-28 00:08:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 752, full token usage: 0.00, #swa token: 752, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.90, #queue-req: 0, 
[2025-07-28 00:08:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 596, full token usage: 0.00, #swa token: 596, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.73, #queue-req: 0, 
[2025-07-28 00:08:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 792, full token usage: 0.00, #swa token: 792, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.74, #queue-req: 0, 
[2025-07-28 00:08:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 636, full token usage: 0.00, #swa token: 636, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.73, #queue-req: 0, 
[2025-07-28 00:08:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 832, full token usage: 0.00, #swa token: 832, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.73, #queue-req: 0, 
[2025-07-28 00:08:19] INFO:     127.0.0.1:40120 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:08:19 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 177, #cached-token: 101, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:08:19 DP0 TP0] Decode batch. #running-req: 1, #full token: 676, full token usage: 0.00, #swa token: 676, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:08:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 296, full token usage: 0.00, #swa token: 296, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.42, #queue-req: 0, 
[2025-07-28 00:08:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 716, full token usage: 0.00, #swa token: 716, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.68, #queue-req: 0, 
[2025-07-28 00:08:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 336, full token usage: 0.00, #swa token: 336, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.06, #queue-req: 0, 
[2025-07-28 00:08:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 756, full token usage: 0.00, #swa token: 756, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.65, #queue-req: 0, 
[2025-07-28 00:08:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 376, full token usage: 0.00, #swa token: 376, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.07, #queue-req: 0, 
[2025-07-28 00:08:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 796, full token usage: 0.00, #swa token: 796, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.60, #queue-req: 0, 
[2025-07-28 00:08:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 416, full token usage: 0.00, #swa token: 416, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.10, #queue-req: 0, 
[2025-07-28 00:08:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 836, full token usage: 0.00, #swa token: 836, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 58.69, #queue-req: 0, 
[2025-07-28 00:08:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 456, full token usage: 0.00, #swa token: 456, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.12, #queue-req: 0, 
[2025-07-28 00:08:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 876, full token usage: 0.00, #swa token: 876, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.66, #queue-req: 0, 
[2025-07-28 00:08:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 496, full token usage: 0.00, #swa token: 496, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.08, #queue-req: 0, 
[2025-07-28 00:08:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 916, full token usage: 0.00, #swa token: 916, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:08:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 536, full token usage: 0.00, #swa token: 536, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.99, #queue-req: 0, 
[2025-07-28 00:08:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 956, full token usage: 0.00, #swa token: 956, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:08:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 576, full token usage: 0.00, #swa token: 576, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.97, #queue-req: 0, 
[2025-07-28 00:08:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 996, full token usage: 0.00, #swa token: 996, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:08:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 616, full token usage: 0.00, #swa token: 616, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.75, #queue-req: 0, 
[2025-07-28 00:08:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 1036, full token usage: 0.00, #swa token: 1036, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.65, #queue-req: 0, 
[2025-07-28 00:08:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 656, full token usage: 0.00, #swa token: 656, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.66, #queue-req: 0, 
[2025-07-28 00:08:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 1076, full token usage: 0.00, #swa token: 1076, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.70, #queue-req: 0, 
[2025-07-28 00:08:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 696, full token usage: 0.00, #swa token: 696, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:08:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 1116, full token usage: 0.00, #swa token: 1116, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.70, #queue-req: 0, 
[2025-07-28 00:08:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 736, full token usage: 0.00, #swa token: 736, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.68, #queue-req: 0, 
[2025-07-28 00:08:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 1156, full token usage: 0.00, #swa token: 1156, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:08:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 776, full token usage: 0.00, #swa token: 776, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.65, #queue-req: 0, 
[2025-07-28 00:08:27] INFO:     127.0.0.1:53160 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:08:27 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 210, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:08:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 321, full token usage: 0.00, #swa token: 321, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.04, #queue-req: 0, 
[2025-07-28 00:08:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 816, full token usage: 0.00, #swa token: 816, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.78, #queue-req: 0, 
[2025-07-28 00:08:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 361, full token usage: 0.00, #swa token: 361, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.80, #queue-req: 0, 
[2025-07-28 00:08:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 856, full token usage: 0.00, #swa token: 856, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.90, #queue-req: 0, 
[2025-07-28 00:08:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 401, full token usage: 0.00, #swa token: 401, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.83, #queue-req: 0, 
[2025-07-28 00:08:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 896, full token usage: 0.00, #swa token: 896, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.87, #queue-req: 0, 
[2025-07-28 00:08:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 441, full token usage: 0.00, #swa token: 441, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.85, #queue-req: 0, 
[2025-07-28 00:08:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 936, full token usage: 0.00, #swa token: 936, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.85, #queue-req: 0, 
[2025-07-28 00:08:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 481, full token usage: 0.00, #swa token: 481, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.83, #queue-req: 0, 
[2025-07-28 00:08:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 976, full token usage: 0.00, #swa token: 976, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.84, #queue-req: 0, 
[2025-07-28 00:08:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 521, full token usage: 0.00, #swa token: 521, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:08:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 1016, full token usage: 0.00, #swa token: 1016, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.80, #queue-req: 0, 
[2025-07-28 00:08:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 561, full token usage: 0.00, #swa token: 561, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.71, #queue-req: 0, 
[2025-07-28 00:08:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 1056, full token usage: 0.00, #swa token: 1056, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.73, #queue-req: 0, 
[2025-07-28 00:08:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 601, full token usage: 0.00, #swa token: 601, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.74, #queue-req: 0, 
[2025-07-28 00:08:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 1096, full token usage: 0.00, #swa token: 1096, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.74, #queue-req: 0, 
[2025-07-28 00:08:33 DP0 TP0] Decode batch. #running-req: 1, #full token: 641, full token usage: 0.00, #swa token: 641, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.73, #queue-req: 0, 
[2025-07-28 00:08:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 1136, full token usage: 0.00, #swa token: 1136, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.73, #queue-req: 0, 
[2025-07-28 00:08:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 681, full token usage: 0.00, #swa token: 681, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.66, #queue-req: 0, 
[2025-07-28 00:08:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 1176, full token usage: 0.00, #swa token: 1176, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:08:34] INFO:     127.0.0.1:53172 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:08:34 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 161, #cached-token: 102, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:08:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 721, full token usage: 0.00, #swa token: 721, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.63, #queue-req: 0, 
[2025-07-28 00:08:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 298, full token usage: 0.00, #swa token: 298, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.55, #queue-req: 0, 
[2025-07-28 00:08:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 761, full token usage: 0.00, #swa token: 761, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.65, #queue-req: 0, 
[2025-07-28 00:08:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 338, full token usage: 0.00, #swa token: 338, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.06, #queue-req: 0, 
[2025-07-28 00:08:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 801, full token usage: 0.00, #swa token: 801, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:08:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 378, full token usage: 0.00, #swa token: 378, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:08:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 841, full token usage: 0.00, #swa token: 841, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:08:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 418, full token usage: 0.00, #swa token: 418, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.71, #queue-req: 0, 
[2025-07-28 00:08:37 DP0 TP0] Decode batch. #running-req: 1, #full token: 881, full token usage: 0.00, #swa token: 881, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.65, #queue-req: 0, 
[2025-07-28 00:08:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 458, full token usage: 0.00, #swa token: 458, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.65, #queue-req: 0, 
[2025-07-28 00:08:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 921, full token usage: 0.00, #swa token: 921, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.65, #queue-req: 0, 
[2025-07-28 00:08:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 498, full token usage: 0.00, #swa token: 498, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:08:38] INFO:     127.0.0.1:35586 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:08:38 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 172, #cached-token: 102, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:08:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 296, full token usage: 0.00, #swa token: 296, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.56, #queue-req: 0, 
[2025-07-28 00:08:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 538, full token usage: 0.00, #swa token: 538, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.90, #queue-req: 0, 
[2025-07-28 00:08:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 336, full token usage: 0.00, #swa token: 336, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.83, #queue-req: 0, 
[2025-07-28 00:08:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 578, full token usage: 0.00, #swa token: 578, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.95, #queue-req: 0, 
[2025-07-28 00:08:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 376, full token usage: 0.00, #swa token: 376, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:08:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 618, full token usage: 0.00, #swa token: 618, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.95, #queue-req: 0, 
[2025-07-28 00:08:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 416, full token usage: 0.00, #swa token: 416, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:08:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 658, full token usage: 0.00, #swa token: 658, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.94, #queue-req: 0, 
[2025-07-28 00:08:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 456, full token usage: 0.00, #swa token: 456, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:08:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 698, full token usage: 0.00, #swa token: 698, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.91, #queue-req: 0, 
[2025-07-28 00:08:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 496, full token usage: 0.00, #swa token: 496, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.80, #queue-req: 0, 
[2025-07-28 00:08:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 738, full token usage: 0.00, #swa token: 738, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.91, #queue-req: 0, 
[2025-07-28 00:08:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 536, full token usage: 0.00, #swa token: 536, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.75, #queue-req: 0, 
[2025-07-28 00:08:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 778, full token usage: 0.00, #swa token: 778, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:08:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 576, full token usage: 0.00, #swa token: 576, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.71, #queue-req: 0, 
[2025-07-28 00:08:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 818, full token usage: 0.00, #swa token: 818, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.70, #queue-req: 0, 
[2025-07-28 00:08:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 616, full token usage: 0.00, #swa token: 616, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.68, #queue-req: 0, 
[2025-07-28 00:08:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 858, full token usage: 0.00, #swa token: 858, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:08:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 656, full token usage: 0.00, #swa token: 656, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.68, #queue-req: 0, 
[2025-07-28 00:08:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 898, full token usage: 0.00, #swa token: 898, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.68, #queue-req: 0, 
[2025-07-28 00:08:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 696, full token usage: 0.00, #swa token: 696, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.66, #queue-req: 0, 
[2025-07-28 00:08:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 938, full token usage: 0.00, #swa token: 938, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.65, #queue-req: 0, 
[2025-07-28 00:08:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 736, full token usage: 0.00, #swa token: 736, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.66, #queue-req: 0, 
[2025-07-28 00:08:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 978, full token usage: 0.00, #swa token: 978, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:08:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 776, full token usage: 0.00, #swa token: 776, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.62, #queue-req: 0, 
[2025-07-28 00:08:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 1018, full token usage: 0.00, #swa token: 1018, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.61, #queue-req: 0, 
[2025-07-28 00:08:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 816, full token usage: 0.00, #swa token: 816, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.62, #queue-req: 0, 
[2025-07-28 00:08:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 1058, full token usage: 0.00, #swa token: 1058, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.62, #queue-req: 0, 
[2025-07-28 00:08:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 856, full token usage: 0.00, #swa token: 856, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.59, #queue-req: 0, 
[2025-07-28 00:08:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 1098, full token usage: 0.00, #swa token: 1098, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.60, #queue-req: 0, 
[2025-07-28 00:08:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 896, full token usage: 0.00, #swa token: 896, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 58.66, #queue-req: 0, 
[2025-07-28 00:08:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 1138, full token usage: 0.00, #swa token: 1138, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.80, #queue-req: 0, 
[2025-07-28 00:08:49] INFO:     127.0.0.1:50396 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:08:49 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 102, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:08:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 936, full token usage: 0.00, #swa token: 936, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.66, #queue-req: 0, 
[2025-07-28 00:08:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 228, full token usage: 0.00, #swa token: 228, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 53.01, #queue-req: 0, 
[2025-07-28 00:08:50 DP0 TP0] Decode batch. #running-req: 1, #full token: 976, full token usage: 0.00, #swa token: 976, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.68, #queue-req: 0, 
[2025-07-28 00:08:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 268, full token usage: 0.00, #swa token: 268, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.04, #queue-req: 0, 
[2025-07-28 00:08:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 1016, full token usage: 0.00, #swa token: 1016, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.60, #queue-req: 0, 
[2025-07-28 00:08:51] INFO:     127.0.0.1:50398 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:08:51 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 120, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:08:51 DP1 TP0] Decode batch. #running-req: 1, #full token: 308, full token usage: 0.00, #swa token: 308, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.66, #queue-req: 0, 
[2025-07-28 00:08:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 252, full token usage: 0.00, #swa token: 252, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.76, #queue-req: 0, 
[2025-07-28 00:08:51 DP1 TP0] Decode batch. #running-req: 1, #full token: 348, full token usage: 0.00, #swa token: 348, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.10, #queue-req: 0, 
[2025-07-28 00:08:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 292, full token usage: 0.00, #swa token: 292, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:08:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 388, full token usage: 0.00, #swa token: 388, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.10, #queue-req: 0, 
[2025-07-28 00:08:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 332, full token usage: 0.00, #swa token: 332, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:08:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 428, full token usage: 0.00, #swa token: 428, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.09, #queue-req: 0, 
[2025-07-28 00:08:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 372, full token usage: 0.00, #swa token: 372, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.78, #queue-req: 0, 
[2025-07-28 00:08:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 468, full token usage: 0.00, #swa token: 468, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.91, #queue-req: 0, 
[2025-07-28 00:08:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 412, full token usage: 0.00, #swa token: 412, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.74, #queue-req: 0, 
[2025-07-28 00:08:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 508, full token usage: 0.00, #swa token: 508, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.75, #queue-req: 0, 
[2025-07-28 00:08:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 452, full token usage: 0.00, #swa token: 452, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:08:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 548, full token usage: 0.00, #swa token: 548, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.80, #queue-req: 0, 
[2025-07-28 00:08:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 492, full token usage: 0.00, #swa token: 492, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.79, #queue-req: 0, 
[2025-07-28 00:08:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 588, full token usage: 0.00, #swa token: 588, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.80, #queue-req: 0, 
[2025-07-28 00:08:56 DP0 TP0] Decode batch. #running-req: 1, #full token: 532, full token usage: 0.00, #swa token: 532, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.75, #queue-req: 0, 
[2025-07-28 00:08:56 DP1 TP0] Decode batch. #running-req: 1, #full token: 628, full token usage: 0.00, #swa token: 628, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.74, #queue-req: 0, 
[2025-07-28 00:08:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 572, full token usage: 0.00, #swa token: 572, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.68, #queue-req: 0, 
[2025-07-28 00:08:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 668, full token usage: 0.00, #swa token: 668, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.68, #queue-req: 0, 
[2025-07-28 00:08:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 612, full token usage: 0.00, #swa token: 612, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.65, #queue-req: 0, 
[2025-07-28 00:08:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 708, full token usage: 0.00, #swa token: 708, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.66, #queue-req: 0, 
[2025-07-28 00:08:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 652, full token usage: 0.00, #swa token: 652, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.68, #queue-req: 0, 
[2025-07-28 00:08:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 748, full token usage: 0.00, #swa token: 748, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:08:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 692, full token usage: 0.00, #swa token: 692, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.64, #queue-req: 0, 
[2025-07-28 00:08:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 788, full token usage: 0.00, #swa token: 788, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.65, #queue-req: 0, 
[2025-07-28 00:08:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 732, full token usage: 0.00, #swa token: 732, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.63, #queue-req: 0, 
[2025-07-28 00:08:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 828, full token usage: 0.00, #swa token: 828, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.64, #queue-req: 0, 
[2025-07-28 00:09:00] INFO:     127.0.0.1:33340 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:09:00 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 152, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:09:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 772, full token usage: 0.00, #swa token: 772, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.68, #queue-req: 0, 
[2025-07-28 00:09:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 271, full token usage: 0.00, #swa token: 271, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.64, #queue-req: 0, 
[2025-07-28 00:09:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 812, full token usage: 0.00, #swa token: 812, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.64, #queue-req: 0, 
[2025-07-28 00:09:01 DP1 TP0] Decode batch. #running-req: 1, #full token: 311, full token usage: 0.00, #swa token: 311, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.01, #queue-req: 0, 
[2025-07-28 00:09:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 852, full token usage: 0.00, #swa token: 852, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.66, #queue-req: 0, 
[2025-07-28 00:09:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 351, full token usage: 0.00, #swa token: 351, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:09:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 892, full token usage: 0.00, #swa token: 892, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.65, #queue-req: 0, 
[2025-07-28 00:09:02] INFO:     127.0.0.1:33354 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:09:02 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 153, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:09:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 391, full token usage: 0.00, #swa token: 391, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.73, #queue-req: 0, 
[2025-07-28 00:09:03 DP0 TP0] Decode batch. #running-req: 1, #full token: 291, full token usage: 0.00, #swa token: 291, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.42, #queue-req: 0, 
[2025-07-28 00:09:03 DP1 TP0] Decode batch. #running-req: 1, #full token: 431, full token usage: 0.00, #swa token: 431, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.08, #queue-req: 0, 
[2025-07-28 00:09:04 DP0 TP0] Decode batch. #running-req: 1, #full token: 331, full token usage: 0.00, #swa token: 331, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.83, #queue-req: 0, 
[2025-07-28 00:09:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 471, full token usage: 0.00, #swa token: 471, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.02, #queue-req: 0, 
[2025-07-28 00:09:04 DP0 TP0] Decode batch. #running-req: 1, #full token: 371, full token usage: 0.00, #swa token: 371, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:09:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 511, full token usage: 0.00, #swa token: 511, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.85, #queue-req: 0, 
[2025-07-28 00:09:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 411, full token usage: 0.00, #swa token: 411, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:09:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 551, full token usage: 0.00, #swa token: 551, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.79, #queue-req: 0, 
[2025-07-28 00:09:06 DP0 TP0] Decode batch. #running-req: 1, #full token: 451, full token usage: 0.00, #swa token: 451, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:09:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 591, full token usage: 0.00, #swa token: 591, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:09:06 DP0 TP0] Decode batch. #running-req: 1, #full token: 491, full token usage: 0.00, #swa token: 491, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.80, #queue-req: 0, 
[2025-07-28 00:09:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 631, full token usage: 0.00, #swa token: 631, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.80, #queue-req: 0, 
[2025-07-28 00:09:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 531, full token usage: 0.00, #swa token: 531, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.78, #queue-req: 0, 
[2025-07-28 00:09:07 DP1 TP0] Decode batch. #running-req: 1, #full token: 671, full token usage: 0.00, #swa token: 671, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.79, #queue-req: 0, 
[2025-07-28 00:09:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 571, full token usage: 0.00, #swa token: 571, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.71, #queue-req: 0, 
[2025-07-28 00:09:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 711, full token usage: 0.00, #swa token: 711, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.73, #queue-req: 0, 
[2025-07-28 00:09:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 611, full token usage: 0.00, #swa token: 611, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.71, #queue-req: 0, 
[2025-07-28 00:09:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 751, full token usage: 0.00, #swa token: 751, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.70, #queue-req: 0, 
[2025-07-28 00:09:09 DP0 TP0] Decode batch. #running-req: 1, #full token: 651, full token usage: 0.00, #swa token: 651, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:09:09 DP1 TP0] Decode batch. #running-req: 1, #full token: 791, full token usage: 0.00, #swa token: 791, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.66, #queue-req: 0, 
[2025-07-28 00:09:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 691, full token usage: 0.00, #swa token: 691, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:09:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 831, full token usage: 0.00, #swa token: 831, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:09:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 731, full token usage: 0.00, #swa token: 731, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.68, #queue-req: 0, 
[2025-07-28 00:09:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 871, full token usage: 0.00, #swa token: 871, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.68, #queue-req: 0, 
[2025-07-28 00:09:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 771, full token usage: 0.00, #swa token: 771, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.65, #queue-req: 0, 
[2025-07-28 00:09:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 911, full token usage: 0.00, #swa token: 911, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.66, #queue-req: 0, 
[2025-07-28 00:09:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 811, full token usage: 0.00, #swa token: 811, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.64, #queue-req: 0, 
[2025-07-28 00:09:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 951, full token usage: 0.00, #swa token: 951, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.64, #queue-req: 0, 
[2025-07-28 00:09:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 851, full token usage: 0.00, #swa token: 851, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.66, #queue-req: 0, 
[2025-07-28 00:09:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 991, full token usage: 0.00, #swa token: 991, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.65, #queue-req: 0, 
[2025-07-28 00:09:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 891, full token usage: 0.00, #swa token: 891, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:09:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 1031, full token usage: 0.00, #swa token: 1031, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.66, #queue-req: 0, 
[2025-07-28 00:09:14 DP0 TP0] Decode batch. #running-req: 1, #full token: 931, full token usage: 0.00, #swa token: 931, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:09:14 DP1 TP0] Decode batch. #running-req: 1, #full token: 1071, full token usage: 0.00, #swa token: 1071, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.70, #queue-req: 0, 
[2025-07-28 00:09:14 DP0 TP0] Decode batch. #running-req: 1, #full token: 971, full token usage: 0.00, #swa token: 971, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:09:14 DP1 TP0] Decode batch. #running-req: 1, #full token: 1111, full token usage: 0.00, #swa token: 1111, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:09:15 DP0 TP0] Decode batch. #running-req: 1, #full token: 1011, full token usage: 0.00, #swa token: 1011, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.68, #queue-req: 0, 
[2025-07-28 00:09:15 DP1 TP0] Decode batch. #running-req: 1, #full token: 1151, full token usage: 0.00, #swa token: 1151, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:09:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 1051, full token usage: 0.00, #swa token: 1051, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.66, #queue-req: 0, 
[2025-07-28 00:09:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 1191, full token usage: 0.00, #swa token: 1191, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:09:16] INFO:     127.0.0.1:34030 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:09:16 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 167, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:09:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 1091, full token usage: 0.00, #swa token: 1091, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:09:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 294, full token usage: 0.00, #swa token: 294, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.73, #queue-req: 0, 
[2025-07-28 00:09:17 DP0 TP0] Decode batch. #running-req: 1, #full token: 1131, full token usage: 0.00, #swa token: 1131, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.63, #queue-req: 0, 
[2025-07-28 00:09:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 334, full token usage: 0.00, #swa token: 334, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.04, #queue-req: 0, 
[2025-07-28 00:09:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 1171, full token usage: 0.00, #swa token: 1171, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:09:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 374, full token usage: 0.00, #swa token: 374, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.68, #queue-req: 0, 
[2025-07-28 00:09:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 1, full token usage: 0.00, #swa token: 1, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.55, #queue-req: 0, 
[2025-07-28 00:09:18] INFO:     127.0.0.1:56808 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:09:18 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 121, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:09:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 414, full token usage: 0.00, #swa token: 414, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.72, #queue-req: 0, 
[2025-07-28 00:09:19 DP0 TP0] Decode batch. #running-req: 1, #full token: 261, full token usage: 0.00, #swa token: 261, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.92, #queue-req: 0, 
[2025-07-28 00:09:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 454, full token usage: 0.00, #swa token: 454, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.04, #queue-req: 0, 
[2025-07-28 00:09:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 301, full token usage: 0.00, #swa token: 301, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.83, #queue-req: 0, 
[2025-07-28 00:09:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 494, full token usage: 0.00, #swa token: 494, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.00, #queue-req: 0, 
[2025-07-28 00:09:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 341, full token usage: 0.00, #swa token: 341, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.79, #queue-req: 0, 
[2025-07-28 00:09:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 534, full token usage: 0.00, #swa token: 534, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.00, #queue-req: 0, 
[2025-07-28 00:09:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 381, full token usage: 0.00, #swa token: 381, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:09:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 574, full token usage: 0.00, #swa token: 574, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.95, #queue-req: 0, 
[2025-07-28 00:09:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 421, full token usage: 0.00, #swa token: 421, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.80, #queue-req: 0, 
[2025-07-28 00:09:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 614, full token usage: 0.00, #swa token: 614, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.94, #queue-req: 0, 
[2025-07-28 00:09:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 461, full token usage: 0.00, #swa token: 461, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.60, #queue-req: 0, 
[2025-07-28 00:09:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 654, full token usage: 0.00, #swa token: 654, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.92, #queue-req: 0, 
[2025-07-28 00:09:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 501, full token usage: 0.00, #swa token: 501, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.07, #queue-req: 0, 
[2025-07-28 00:09:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 694, full token usage: 0.00, #swa token: 694, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.95, #queue-req: 0, 
[2025-07-28 00:09:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 541, full token usage: 0.00, #swa token: 541, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.70, #queue-req: 0, 
[2025-07-28 00:09:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 734, full token usage: 0.00, #swa token: 734, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.96, #queue-req: 0, 
[2025-07-28 00:09:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 581, full token usage: 0.00, #swa token: 581, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.72, #queue-req: 0, 
[2025-07-28 00:09:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 774, full token usage: 0.00, #swa token: 774, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.87, #queue-req: 0, 
[2025-07-28 00:09:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 621, full token usage: 0.00, #swa token: 621, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.65, #queue-req: 0, 
[2025-07-28 00:09:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 814, full token usage: 0.00, #swa token: 814, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.66, #queue-req: 0, 
[2025-07-28 00:09:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 661, full token usage: 0.00, #swa token: 661, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.70, #queue-req: 0, 
[2025-07-28 00:09:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 854, full token usage: 0.00, #swa token: 854, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.68, #queue-req: 0, 
[2025-07-28 00:09:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 701, full token usage: 0.00, #swa token: 701, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:09:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 894, full token usage: 0.00, #swa token: 894, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.68, #queue-req: 0, 
[2025-07-28 00:09:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 741, full token usage: 0.00, #swa token: 741, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.56, #queue-req: 0, 
[2025-07-28 00:09:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 934, full token usage: 0.00, #swa token: 934, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.57, #queue-req: 0, 
[2025-07-28 00:09:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 781, full token usage: 0.00, #swa token: 781, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:09:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 974, full token usage: 0.00, #swa token: 974, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:09:28] INFO:     127.0.0.1:46096 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:09:28 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 89, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:09:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 821, full token usage: 0.00, #swa token: 821, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.65, #queue-req: 0, 
[2025-07-28 00:09:28] INFO:     127.0.0.1:46102 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:09:29 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 134, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:09:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 197, full token usage: 0.00, #swa token: 197, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.63, #queue-req: 0, 
[2025-07-28 00:09:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 237, full token usage: 0.00, #swa token: 237, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.45, #queue-req: 0, 
[2025-07-28 00:09:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 268, full token usage: 0.00, #swa token: 268, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.69, #queue-req: 0, 
[2025-07-28 00:09:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 277, full token usage: 0.00, #swa token: 277, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.13, #queue-req: 0, 
[2025-07-28 00:09:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 308, full token usage: 0.00, #swa token: 308, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.85, #queue-req: 0, 
[2025-07-28 00:09:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 317, full token usage: 0.00, #swa token: 317, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.07, #queue-req: 0, 
[2025-07-28 00:09:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 348, full token usage: 0.00, #swa token: 348, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.86, #queue-req: 0, 
[2025-07-28 00:09:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 357, full token usage: 0.00, #swa token: 357, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.04, #queue-req: 0, 
[2025-07-28 00:09:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 388, full token usage: 0.00, #swa token: 388, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.83, #queue-req: 0, 
[2025-07-28 00:09:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 397, full token usage: 0.00, #swa token: 397, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.88, #queue-req: 0, 
[2025-07-28 00:09:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 428, full token usage: 0.00, #swa token: 428, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:09:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 437, full token usage: 0.00, #swa token: 437, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.80, #queue-req: 0, 
[2025-07-28 00:09:33 DP0 TP0] Decode batch. #running-req: 1, #full token: 468, full token usage: 0.00, #swa token: 468, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:09:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 477, full token usage: 0.00, #swa token: 477, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.84, #queue-req: 0, 
[2025-07-28 00:09:33 DP0 TP0] Decode batch. #running-req: 1, #full token: 508, full token usage: 0.00, #swa token: 508, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:09:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 517, full token usage: 0.00, #swa token: 517, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.74, #queue-req: 0, 
[2025-07-28 00:09:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 548, full token usage: 0.00, #swa token: 548, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.73, #queue-req: 0, 
[2025-07-28 00:09:34] INFO:     127.0.0.1:48808 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:09:34 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:09:35 DP1 TP0] Decode batch. #running-req: 2, #full token: 737, full token usage: 0.00, #swa token: 737, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 96.55, #queue-req: 0, 
[2025-07-28 00:09:35 DP1 TP0] Decode batch. #running-req: 2, #full token: 817, full token usage: 0.00, #swa token: 817, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 116.05, #queue-req: 0, 
[2025-07-28 00:09:36 DP1 TP0] Decode batch. #running-req: 2, #full token: 897, full token usage: 0.00, #swa token: 897, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 116.14, #queue-req: 0, 
[2025-07-28 00:09:36] INFO:     127.0.0.1:48806 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:09:36 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 93, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:09:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 397, full token usage: 0.00, #swa token: 397, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 92.78, #queue-req: 0, 
[2025-07-28 00:09:37 DP0 TP0] Decode batch. #running-req: 1, #full token: 229, full token usage: 0.00, #swa token: 229, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 12.13, #queue-req: 0, 
[2025-07-28 00:09:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 437, full token usage: 0.00, #swa token: 437, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.14, #queue-req: 0, 
[2025-07-28 00:09:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 269, full token usage: 0.00, #swa token: 269, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.98, #queue-req: 0, 
[2025-07-28 00:09:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 477, full token usage: 0.00, #swa token: 477, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.16, #queue-req: 0, 
[2025-07-28 00:09:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 309, full token usage: 0.00, #swa token: 309, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.86, #queue-req: 0, 
[2025-07-28 00:09:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 517, full token usage: 0.00, #swa token: 517, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.15, #queue-req: 0, 
[2025-07-28 00:09:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 349, full token usage: 0.00, #swa token: 349, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.86, #queue-req: 0, 
[2025-07-28 00:09:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 557, full token usage: 0.00, #swa token: 557, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.05, #queue-req: 0, 
[2025-07-28 00:09:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 389, full token usage: 0.00, #swa token: 389, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.85, #queue-req: 0, 
[2025-07-28 00:09:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 597, full token usage: 0.00, #swa token: 597, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.05, #queue-req: 0, 
[2025-07-28 00:09:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 429, full token usage: 0.00, #swa token: 429, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.83, #queue-req: 0, 
[2025-07-28 00:09:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 637, full token usage: 0.00, #swa token: 637, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.07, #queue-req: 0, 
[2025-07-28 00:09:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 469, full token usage: 0.00, #swa token: 469, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.83, #queue-req: 0, 
[2025-07-28 00:09:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 677, full token usage: 0.00, #swa token: 677, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.97, #queue-req: 0, 
[2025-07-28 00:09:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 509, full token usage: 0.00, #swa token: 509, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:09:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 717, full token usage: 0.00, #swa token: 717, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.04, #queue-req: 0, 
[2025-07-28 00:09:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 549, full token usage: 0.00, #swa token: 549, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.76, #queue-req: 0, 
[2025-07-28 00:09:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 757, full token usage: 0.00, #swa token: 757, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.02, #queue-req: 0, 
[2025-07-28 00:09:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 589, full token usage: 0.00, #swa token: 589, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:09:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 797, full token usage: 0.00, #swa token: 797, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.98, #queue-req: 0, 
[2025-07-28 00:09:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 629, full token usage: 0.00, #swa token: 629, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.74, #queue-req: 0, 
[2025-07-28 00:09:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 837, full token usage: 0.00, #swa token: 837, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.03, #queue-req: 0, 
[2025-07-28 00:09:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 669, full token usage: 0.00, #swa token: 669, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:09:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 877, full token usage: 0.00, #swa token: 877, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.04, #queue-req: 0, 
[2025-07-28 00:09:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 709, full token usage: 0.00, #swa token: 709, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.66, #queue-req: 0, 
[2025-07-28 00:09:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 917, full token usage: 0.00, #swa token: 917, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.06, #queue-req: 0, 
[2025-07-28 00:09:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 749, full token usage: 0.00, #swa token: 749, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.66, #queue-req: 0, 
[2025-07-28 00:09:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 957, full token usage: 0.00, #swa token: 957, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.03, #queue-req: 0, 
[2025-07-28 00:09:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 789, full token usage: 0.00, #swa token: 789, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.66, #queue-req: 0, 
[2025-07-28 00:09:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 997, full token usage: 0.00, #swa token: 997, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.05, #queue-req: 0, 
[2025-07-28 00:09:47] INFO:     127.0.0.1:41970 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:09:47 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 167, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:09:47 DP1 TP0] Decode batch. #running-req: 2, #full token: 1226, full token usage: 0.00, #swa token: 1226, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 79.88, #queue-req: 0, 
[2025-07-28 00:09:48 DP1 TP0] Decode batch. #running-req: 2, #full token: 1306, full token usage: 0.00, #swa token: 1306, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 116.08, #queue-req: 0, 
[2025-07-28 00:09:49 DP1 TP0] Decode batch. #running-req: 2, #full token: 1386, full token usage: 0.00, #swa token: 1386, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 116.02, #queue-req: 0, 
[2025-07-28 00:09:50 DP1 TP0] Decode batch. #running-req: 2, #full token: 1466, full token usage: 0.00, #swa token: 1466, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 116.02, #queue-req: 0, 
[2025-07-28 00:09:50 DP1 TP0] Decode batch. #running-req: 2, #full token: 1546, full token usage: 0.00, #swa token: 1546, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 116.05, #queue-req: 0, 
[2025-07-28 00:09:51 DP1 TP0] Decode batch. #running-req: 2, #full token: 1626, full token usage: 0.00, #swa token: 1626, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 116.02, #queue-req: 0, 
[2025-07-28 00:09:52 DP1 TP0] Decode batch. #running-req: 2, #full token: 1706, full token usage: 0.00, #swa token: 1706, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 115.95, #queue-req: 0, 
[2025-07-28 00:09:52 DP1 TP0] Decode batch. #running-req: 2, #full token: 1786, full token usage: 0.00, #swa token: 1786, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 116.02, #queue-req: 0, 
[2025-07-28 00:09:53] INFO:     127.0.0.1:41958 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:09:53 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 152, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:09:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 606, full token usage: 0.00, #swa token: 606, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 106.43, #queue-req: 0, 
[2025-07-28 00:09:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 266, full token usage: 0.00, #swa token: 266, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 5.98, #queue-req: 0, 
[2025-07-28 00:09:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 646, full token usage: 0.00, #swa token: 646, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.06, #queue-req: 0, 
[2025-07-28 00:09:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 306, full token usage: 0.00, #swa token: 306, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.83, #queue-req: 0, 
[2025-07-28 00:09:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 686, full token usage: 0.00, #swa token: 686, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.02, #queue-req: 0, 
[2025-07-28 00:09:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 346, full token usage: 0.00, #swa token: 346, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:09:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 726, full token usage: 0.00, #swa token: 726, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.02, #queue-req: 0, 
[2025-07-28 00:09:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 386, full token usage: 0.00, #swa token: 386, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.79, #queue-req: 0, 
[2025-07-28 00:09:56 DP1 TP0] Decode batch. #running-req: 1, #full token: 766, full token usage: 0.00, #swa token: 766, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.04, #queue-req: 0, 
[2025-07-28 00:09:56 DP0 TP0] Decode batch. #running-req: 1, #full token: 426, full token usage: 0.00, #swa token: 426, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:09:56 DP1 TP0] Decode batch. #running-req: 1, #full token: 806, full token usage: 0.00, #swa token: 806, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.03, #queue-req: 0, 
[2025-07-28 00:09:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 466, full token usage: 0.00, #swa token: 466, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.79, #queue-req: 0, 
[2025-07-28 00:09:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 846, full token usage: 0.00, #swa token: 846, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.04, #queue-req: 0, 
[2025-07-28 00:09:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 506, full token usage: 0.00, #swa token: 506, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.78, #queue-req: 0, 
[2025-07-28 00:09:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 886, full token usage: 0.00, #swa token: 886, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.03, #queue-req: 0, 
[2025-07-28 00:09:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 546, full token usage: 0.00, #swa token: 546, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.73, #queue-req: 0, 
[2025-07-28 00:09:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 926, full token usage: 0.00, #swa token: 926, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.05, #queue-req: 0, 
[2025-07-28 00:09:58] INFO:     127.0.0.1:60990 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:09:58 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 117, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:09:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 586, full token usage: 0.00, #swa token: 586, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.73, #queue-req: 0, 
[2025-07-28 00:09:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 244, full token usage: 0.00, #swa token: 244, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 53.13, #queue-req: 0, 
[2025-07-28 00:09:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 626, full token usage: 0.00, #swa token: 626, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:10:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 284, full token usage: 0.00, #swa token: 284, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.17, #queue-req: 0, 
[2025-07-28 00:10:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 666, full token usage: 0.00, #swa token: 666, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.58, #queue-req: 0, 
[2025-07-28 00:10:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 324, full token usage: 0.00, #swa token: 324, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.15, #queue-req: 0, 
[2025-07-28 00:10:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 706, full token usage: 0.00, #swa token: 706, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.65, #queue-req: 0, 
[2025-07-28 00:10:01 DP1 TP0] Decode batch. #running-req: 1, #full token: 364, full token usage: 0.00, #swa token: 364, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.15, #queue-req: 0, 
[2025-07-28 00:10:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 746, full token usage: 0.00, #swa token: 746, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.65, #queue-req: 0, 
[2025-07-28 00:10:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 404, full token usage: 0.00, #swa token: 404, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.13, #queue-req: 0, 
[2025-07-28 00:10:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 786, full token usage: 0.00, #swa token: 786, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:10:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 444, full token usage: 0.00, #swa token: 444, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.15, #queue-req: 0, 
[2025-07-28 00:10:03 DP0 TP0] Decode batch. #running-req: 1, #full token: 826, full token usage: 0.00, #swa token: 826, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:10:03] INFO:     127.0.0.1:58504 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:10:03 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 103, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:10:03 DP1 TP0] Decode batch. #running-req: 1, #full token: 484, full token usage: 0.00, #swa token: 484, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.14, #queue-req: 0, 
[2025-07-28 00:10:03 DP0 TP0] Decode batch. #running-req: 1, #full token: 218, full token usage: 0.00, #swa token: 218, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.69, #queue-req: 0, 
[2025-07-28 00:10:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 524, full token usage: 0.00, #swa token: 524, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.07, #queue-req: 0, 
[2025-07-28 00:10:04 DP0 TP0] Decode batch. #running-req: 1, #full token: 258, full token usage: 0.00, #swa token: 258, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.09, #queue-req: 0, 
[2025-07-28 00:10:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 564, full token usage: 0.00, #swa token: 564, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.91, #queue-req: 0, 
[2025-07-28 00:10:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 298, full token usage: 0.00, #swa token: 298, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.85, #queue-req: 0, 
[2025-07-28 00:10:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 604, full token usage: 0.00, #swa token: 604, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.92, #queue-req: 0, 
[2025-07-28 00:10:05] INFO:     127.0.0.1:58516 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:10:05 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 92, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:10:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 338, full token usage: 0.00, #swa token: 338, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.80, #queue-req: 0, 
[2025-07-28 00:10:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 220, full token usage: 0.00, #swa token: 220, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.90, #queue-req: 0, 
[2025-07-28 00:10:06 DP0 TP0] Decode batch. #running-req: 1, #full token: 378, full token usage: 0.00, #swa token: 378, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.80, #queue-req: 0, 
[2025-07-28 00:10:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 260, full token usage: 0.00, #swa token: 260, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.76, #queue-req: 0, 
[2025-07-28 00:10:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 418, full token usage: 0.00, #swa token: 418, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.80, #queue-req: 0, 
[2025-07-28 00:10:07 DP1 TP0] Decode batch. #running-req: 1, #full token: 300, full token usage: 0.00, #swa token: 300, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:10:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 458, full token usage: 0.00, #swa token: 458, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:10:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 340, full token usage: 0.00, #swa token: 340, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.83, #queue-req: 0, 
[2025-07-28 00:10:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 498, full token usage: 0.00, #swa token: 498, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.83, #queue-req: 0, 
[2025-07-28 00:10:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 380, full token usage: 0.00, #swa token: 380, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.78, #queue-req: 0, 
[2025-07-28 00:10:09 DP0 TP0] Decode batch. #running-req: 1, #full token: 538, full token usage: 0.00, #swa token: 538, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.74, #queue-req: 0, 
[2025-07-28 00:10:09] INFO:     127.0.0.1:33718 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:10:09 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 122, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:10:09 DP1 TP0] Decode batch. #running-req: 1, #full token: 420, full token usage: 0.00, #swa token: 420, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.76, #queue-req: 0, 
[2025-07-28 00:10:09 DP0 TP0] Decode batch. #running-req: 1, #full token: 236, full token usage: 0.00, #swa token: 236, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.72, #queue-req: 0, 
[2025-07-28 00:10:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 460, full token usage: 0.00, #swa token: 460, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.02, #queue-req: 0, 
[2025-07-28 00:10:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 276, full token usage: 0.00, #swa token: 276, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.93, #queue-req: 0, 
[2025-07-28 00:10:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 500, full token usage: 0.00, #swa token: 500, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.02, #queue-req: 0, 
[2025-07-28 00:10:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 316, full token usage: 0.00, #swa token: 316, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.84, #queue-req: 0, 
[2025-07-28 00:10:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 540, full token usage: 0.00, #swa token: 540, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.93, #queue-req: 0, 
[2025-07-28 00:10:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 356, full token usage: 0.00, #swa token: 356, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.85, #queue-req: 0, 
[2025-07-28 00:10:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 580, full token usage: 0.00, #swa token: 580, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.89, #queue-req: 0, 
[2025-07-28 00:10:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 396, full token usage: 0.00, #swa token: 396, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.84, #queue-req: 0, 
[2025-07-28 00:10:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 620, full token usage: 0.00, #swa token: 620, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.91, #queue-req: 0, 
[2025-07-28 00:10:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 436, full token usage: 0.00, #swa token: 436, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.85, #queue-req: 0, 
[2025-07-28 00:10:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 660, full token usage: 0.00, #swa token: 660, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.89, #queue-req: 0, 
[2025-07-28 00:10:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 476, full token usage: 0.00, #swa token: 476, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:10:14] INFO:     127.0.0.1:33728 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:10:14 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 162, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:10:14 DP1 TP0] Decode batch. #running-req: 1, #full token: 267, full token usage: 0.00, #swa token: 267, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.61, #queue-req: 0, 
[2025-07-28 00:10:14 DP0 TP0] Decode batch. #running-req: 1, #full token: 516, full token usage: 0.00, #swa token: 516, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:10:15 DP1 TP0] Decode batch. #running-req: 1, #full token: 307, full token usage: 0.00, #swa token: 307, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.02, #queue-req: 0, 
[2025-07-28 00:10:15 DP0 TP0] Decode batch. #running-req: 1, #full token: 556, full token usage: 0.00, #swa token: 556, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.75, #queue-req: 0, 
[2025-07-28 00:10:15 DP1 TP0] Decode batch. #running-req: 1, #full token: 347, full token usage: 0.00, #swa token: 347, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.02, #queue-req: 0, 
[2025-07-28 00:10:15 DP0 TP0] Decode batch. #running-req: 1, #full token: 596, full token usage: 0.00, #swa token: 596, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.74, #queue-req: 0, 
[2025-07-28 00:10:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 387, full token usage: 0.00, #swa token: 387, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.98, #queue-req: 0, 
[2025-07-28 00:10:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 636, full token usage: 0.00, #swa token: 636, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.68, #queue-req: 0, 
[2025-07-28 00:10:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 427, full token usage: 0.00, #swa token: 427, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.91, #queue-req: 0, 
[2025-07-28 00:10:17 DP0 TP0] Decode batch. #running-req: 1, #full token: 676, full token usage: 0.00, #swa token: 676, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.70, #queue-req: 0, 
[2025-07-28 00:10:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 467, full token usage: 0.00, #swa token: 467, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.85, #queue-req: 0, 
[2025-07-28 00:10:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 716, full token usage: 0.00, #swa token: 716, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.70, #queue-req: 0, 
[2025-07-28 00:10:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 507, full token usage: 0.00, #swa token: 507, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.71, #queue-req: 0, 
[2025-07-28 00:10:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 756, full token usage: 0.00, #swa token: 756, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.70, #queue-req: 0, 
[2025-07-28 00:10:19] INFO:     127.0.0.1:33732 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:10:19 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 148, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:10:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 547, full token usage: 0.00, #swa token: 547, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:10:19 DP0 TP0] Decode batch. #running-req: 1, #full token: 264, full token usage: 0.00, #swa token: 264, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.34, #queue-req: 0, 
[2025-07-28 00:10:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 587, full token usage: 0.00, #swa token: 587, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.92, #queue-req: 0, 
[2025-07-28 00:10:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 304, full token usage: 0.00, #swa token: 304, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:10:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 627, full token usage: 0.00, #swa token: 627, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.90, #queue-req: 0, 
[2025-07-28 00:10:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 344, full token usage: 0.00, #swa token: 344, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.84, #queue-req: 0, 
[2025-07-28 00:10:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 667, full token usage: 0.00, #swa token: 667, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.90, #queue-req: 0, 
[2025-07-28 00:10:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 384, full token usage: 0.00, #swa token: 384, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.84, #queue-req: 0, 
[2025-07-28 00:10:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 707, full token usage: 0.00, #swa token: 707, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.89, #queue-req: 0, 
[2025-07-28 00:10:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 424, full token usage: 0.00, #swa token: 424, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.83, #queue-req: 0, 
[2025-07-28 00:10:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 747, full token usage: 0.00, #swa token: 747, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.90, #queue-req: 0, 
[2025-07-28 00:10:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 464, full token usage: 0.00, #swa token: 464, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.77, #queue-req: 0, 
[2025-07-28 00:10:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 787, full token usage: 0.00, #swa token: 787, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.86, #queue-req: 0, 
[2025-07-28 00:10:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 504, full token usage: 0.00, #swa token: 504, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.79, #queue-req: 0, 
[2025-07-28 00:10:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 827, full token usage: 0.00, #swa token: 827, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.79, #queue-req: 0, 
[2025-07-28 00:10:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 544, full token usage: 0.00, #swa token: 544, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 58.82, #queue-req: 0, 
[2025-07-28 00:10:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 867, full token usage: 0.00, #swa token: 867, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.80, #queue-req: 0, 
[2025-07-28 00:10:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 584, full token usage: 0.00, #swa token: 584, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.70, #queue-req: 0, 
[2025-07-28 00:10:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 907, full token usage: 0.00, #swa token: 907, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.89, #queue-req: 0, 
[2025-07-28 00:10:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 624, full token usage: 0.00, #swa token: 624, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.75, #queue-req: 0, 
[2025-07-28 00:10:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 947, full token usage: 0.00, #swa token: 947, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.90, #queue-req: 0, 
[2025-07-28 00:10:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 664, full token usage: 0.00, #swa token: 664, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.71, #queue-req: 0, 
[2025-07-28 00:10:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 987, full token usage: 0.00, #swa token: 987, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.86, #queue-req: 0, 
[2025-07-28 00:10:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 704, full token usage: 0.00, #swa token: 704, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:10:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 1027, full token usage: 0.00, #swa token: 1027, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:10:27] INFO:     127.0.0.1:55378 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:10:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 744, full token usage: 0.00, #swa token: 744, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.70, #queue-req: 0, 
[2025-07-28 00:10:27 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 117, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:10:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 236, full token usage: 0.00, #swa token: 236, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.84, #queue-req: 0, 
[2025-07-28 00:10:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 784, full token usage: 0.00, #swa token: 784, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.69, #queue-req: 0, 
[2025-07-28 00:10:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 276, full token usage: 0.00, #swa token: 276, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.99, #queue-req: 0, 
[2025-07-28 00:10:28] INFO:     127.0.0.1:55392 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:10:28 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 133, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:10:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 243, full token usage: 0.00, #swa token: 243, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.66, #queue-req: 0, 
[2025-07-28 00:10:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 316, full token usage: 0.00, #swa token: 316, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.98, #queue-req: 0, 
[2025-07-28 00:10:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 283, full token usage: 0.00, #swa token: 283, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.88, #queue-req: 0, 
[2025-07-28 00:10:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 356, full token usage: 0.00, #swa token: 356, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.05, #queue-req: 0, 
[2025-07-28 00:10:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 323, full token usage: 0.00, #swa token: 323, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.86, #queue-req: 0, 
[2025-07-28 00:10:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 396, full token usage: 0.00, #swa token: 396, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.03, #queue-req: 0, 
[2025-07-28 00:10:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 363, full token usage: 0.00, #swa token: 363, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:10:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 436, full token usage: 0.00, #swa token: 436, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.04, #queue-req: 0, 
[2025-07-28 00:10:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 403, full token usage: 0.00, #swa token: 403, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:10:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 476, full token usage: 0.00, #swa token: 476, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.98, #queue-req: 0, 
[2025-07-28 00:10:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 443, full token usage: 0.00, #swa token: 443, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:10:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 516, full token usage: 0.00, #swa token: 516, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.83, #queue-req: 0, 
[2025-07-28 00:10:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 483, full token usage: 0.00, #swa token: 483, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.76, #queue-req: 0, 
[2025-07-28 00:10:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 556, full token usage: 0.00, #swa token: 556, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.76, #queue-req: 0, 
[2025-07-28 00:10:33 DP0 TP0] Decode batch. #running-req: 1, #full token: 523, full token usage: 0.00, #swa token: 523, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:10:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 596, full token usage: 0.00, #swa token: 596, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.77, #queue-req: 0, 
[2025-07-28 00:10:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 563, full token usage: 0.00, #swa token: 563, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.74, #queue-req: 0, 
[2025-07-28 00:10:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 636, full token usage: 0.00, #swa token: 636, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.74, #queue-req: 0, 
[2025-07-28 00:10:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 603, full token usage: 0.00, #swa token: 603, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.72, #queue-req: 0, 
[2025-07-28 00:10:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 676, full token usage: 0.00, #swa token: 676, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.70, #queue-req: 0, 
[2025-07-28 00:10:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 643, full token usage: 0.00, #swa token: 643, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.68, #queue-req: 0, 
[2025-07-28 00:10:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 716, full token usage: 0.00, #swa token: 716, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.67, #queue-req: 0, 
[2025-07-28 00:10:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 683, full token usage: 0.00, #swa token: 683, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.66, #queue-req: 0, 
[2025-07-28 00:10:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 756, full token usage: 0.00, #swa token: 756, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.65, #queue-req: 0, 
[2025-07-28 00:10:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 723, full token usage: 0.00, #swa token: 723, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.65, #queue-req: 0, 
[2025-07-28 00:10:37] INFO:     127.0.0.1:49824 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:10:37 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 201, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:10:37 DP1 TP0] Decode batch. #running-req: 2, #full token: 1004, full token usage: 0.00, #swa token: 1004, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 57.88, #queue-req: 0, 
[2025-07-28 00:10:37] INFO:     127.0.0.1:49808 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:10:37 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 90, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:10:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 347, full token usage: 0.00, #swa token: 347, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 98.35, #queue-req: 0, 
[2025-07-28 00:10:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 217, full token usage: 0.00, #swa token: 217, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 27.65, #queue-req: 0, 
[2025-07-28 00:10:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 387, full token usage: 0.00, #swa token: 387, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.17, #queue-req: 0, 
[2025-07-28 00:10:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 257, full token usage: 0.00, #swa token: 257, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.07, #queue-req: 0, 
[2025-07-28 00:10:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 427, full token usage: 0.00, #swa token: 427, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.12, #queue-req: 0, 
[2025-07-28 00:10:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 297, full token usage: 0.00, #swa token: 297, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:10:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 467, full token usage: 0.00, #swa token: 467, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.14, #queue-req: 0, 
[2025-07-28 00:10:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 337, full token usage: 0.00, #swa token: 337, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:10:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 507, full token usage: 0.00, #swa token: 507, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.16, #queue-req: 0, 
[2025-07-28 00:10:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 377, full token usage: 0.00, #swa token: 377, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:10:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 547, full token usage: 0.00, #swa token: 547, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.10, #queue-req: 0, 
[2025-07-28 00:10:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 417, full token usage: 0.00, #swa token: 417, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.77, #queue-req: 0, 
[2025-07-28 00:10:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 587, full token usage: 0.00, #swa token: 587, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.09, #queue-req: 0, 
[2025-07-28 00:10:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 457, full token usage: 0.00, #swa token: 457, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:10:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 627, full token usage: 0.00, #swa token: 627, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.03, #queue-req: 0, 
[2025-07-28 00:10:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 497, full token usage: 0.00, #swa token: 497, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.79, #queue-req: 0, 
[2025-07-28 00:10:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 667, full token usage: 0.00, #swa token: 667, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.06, #queue-req: 0, 
[2025-07-28 00:10:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 537, full token usage: 0.00, #swa token: 537, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.74, #queue-req: 0, 
[2025-07-28 00:10:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 707, full token usage: 0.00, #swa token: 707, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.05, #queue-req: 0, 
[2025-07-28 00:10:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 577, full token usage: 0.00, #swa token: 577, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.72, #queue-req: 0, 
[2025-07-28 00:10:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 747, full token usage: 0.00, #swa token: 747, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.02, #queue-req: 0, 
[2025-07-28 00:10:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 617, full token usage: 0.00, #swa token: 617, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.70, #queue-req: 0, 
[2025-07-28 00:10:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 787, full token usage: 0.00, #swa token: 787, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.04, #queue-req: 0, 
[2025-07-28 00:10:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 657, full token usage: 0.00, #swa token: 657, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.65, #queue-req: 0, 
[2025-07-28 00:10:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 827, full token usage: 0.00, #swa token: 827, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.03, #queue-req: 0, 
[2025-07-28 00:10:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 697, full token usage: 0.00, #swa token: 697, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.66, #queue-req: 0, 
[2025-07-28 00:10:46] INFO:     127.0.0.1:39136 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:10:46 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 158, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:10:46 DP1 TP0] Decode batch. #running-req: 2, #full token: 1034, full token usage: 0.00, #swa token: 1034, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 62.26, #queue-req: 0, 
[2025-07-28 00:10:47] INFO:     127.0.0.1:39122 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:10:47 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 134, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:10:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 306, full token usage: 0.00, #swa token: 306, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 96.96, #queue-req: 0, 
[2025-07-28 00:10:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 267, full token usage: 0.00, #swa token: 267, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 28.05, #queue-req: 0, 
[2025-07-28 00:10:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 346, full token usage: 0.00, #swa token: 346, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.13, #queue-req: 0, 
[2025-07-28 00:10:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 307, full token usage: 0.00, #swa token: 307, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.85, #queue-req: 0, 
[2025-07-28 00:10:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 386, full token usage: 0.00, #swa token: 386, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.15, #queue-req: 0, 
[2025-07-28 00:10:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 347, full token usage: 0.00, #swa token: 347, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:10:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 426, full token usage: 0.00, #swa token: 426, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.11, #queue-req: 0, 
[2025-07-28 00:10:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 387, full token usage: 0.00, #swa token: 387, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.80, #queue-req: 0, 
[2025-07-28 00:10:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 466, full token usage: 0.00, #swa token: 466, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.15, #queue-req: 0, 
[2025-07-28 00:10:50 DP0 TP0] Decode batch. #running-req: 1, #full token: 427, full token usage: 0.00, #swa token: 427, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:10:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 506, full token usage: 0.00, #swa token: 506, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.14, #queue-req: 0, 
[2025-07-28 00:10:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 467, full token usage: 0.00, #swa token: 467, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:10:51 DP1 TP0] Decode batch. #running-req: 1, #full token: 546, full token usage: 0.00, #swa token: 546, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.07, #queue-req: 0, 
[2025-07-28 00:10:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 507, full token usage: 0.00, #swa token: 507, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 58.88, #queue-req: 0, 
[2025-07-28 00:10:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 586, full token usage: 0.00, #swa token: 586, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.08, #queue-req: 0, 
[2025-07-28 00:10:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 547, full token usage: 0.00, #swa token: 547, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:10:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 626, full token usage: 0.00, #swa token: 626, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.06, #queue-req: 0, 
[2025-07-28 00:10:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 587, full token usage: 0.00, #swa token: 587, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.86, #queue-req: 0, 
[2025-07-28 00:10:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 666, full token usage: 0.00, #swa token: 666, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.04, #queue-req: 0, 
[2025-07-28 00:10:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 627, full token usage: 0.00, #swa token: 627, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.84, #queue-req: 0, 
[2025-07-28 00:10:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 706, full token usage: 0.00, #swa token: 706, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.01, #queue-req: 0, 
[2025-07-28 00:10:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 667, full token usage: 0.00, #swa token: 667, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.78, #queue-req: 0, 
[2025-07-28 00:10:54] INFO:     127.0.0.1:49576 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:10:54 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 144, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:10:54 DP1 TP0] Decode batch. #running-req: 2, #full token: 897, full token usage: 0.00, #swa token: 897, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.77, #queue-req: 0, 
[2025-07-28 00:10:55 DP1 TP0] Decode batch. #running-req: 2, #full token: 977, full token usage: 0.00, #swa token: 977, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 115.95, #queue-req: 0, 
[2025-07-28 00:10:56 DP1 TP0] Decode batch. #running-req: 2, #full token: 1057, full token usage: 0.00, #swa token: 1057, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 115.96, #queue-req: 0, 
[2025-07-28 00:10:56 DP1 TP0] Decode batch. #running-req: 2, #full token: 1137, full token usage: 0.00, #swa token: 1137, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 115.86, #queue-req: 0, 
[2025-07-28 00:10:57 DP1 TP0] Decode batch. #running-req: 2, #full token: 1217, full token usage: 0.00, #swa token: 1217, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 115.88, #queue-req: 0, 
[2025-07-28 00:10:58 DP1 TP0] Decode batch. #running-req: 2, #full token: 1297, full token usage: 0.00, #swa token: 1297, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 115.85, #queue-req: 0, 
[2025-07-28 00:10:58] INFO:     127.0.0.1:49568 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:10:58 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 140, #cached-token: 100, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:10:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 490, full token usage: 0.00, #swa token: 490, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 82.94, #queue-req: 0, 
[2025-07-28 00:10:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 275, full token usage: 0.00, #swa token: 275, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 8.49, #queue-req: 0, 
[2025-07-28 00:10:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 530, full token usage: 0.00, #swa token: 530, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.06, #queue-req: 0, 
[2025-07-28 00:10:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 315, full token usage: 0.00, #swa token: 315, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.96, #queue-req: 0, 
[2025-07-28 00:11:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 570, full token usage: 0.00, #swa token: 570, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.02, #queue-req: 0, 
[2025-07-28 00:11:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 355, full token usage: 0.00, #swa token: 355, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.93, #queue-req: 0, 
[2025-07-28 00:11:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 610, full token usage: 0.00, #swa token: 610, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.01, #queue-req: 0, 
[2025-07-28 00:11:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 395, full token usage: 0.00, #swa token: 395, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.93, #queue-req: 0, 
[2025-07-28 00:11:01 DP1 TP0] Decode batch. #running-req: 1, #full token: 650, full token usage: 0.00, #swa token: 650, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.00, #queue-req: 0, 
[2025-07-28 00:11:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 435, full token usage: 0.00, #swa token: 435, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.93, #queue-req: 0, 
[2025-07-28 00:11:02] INFO:     127.0.0.1:41682 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:11:02 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 102, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:11:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 218, full token usage: 0.00, #swa token: 218, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 53.17, #queue-req: 0, 
[2025-07-28 00:11:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 475, full token usage: 0.00, #swa token: 475, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.94, #queue-req: 0, 
[2025-07-28 00:11:03 DP1 TP0] Decode batch. #running-req: 1, #full token: 258, full token usage: 0.00, #swa token: 258, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.36, #queue-req: 0, 
[2025-07-28 00:11:03 DP0 TP0] Decode batch. #running-req: 1, #full token: 515, full token usage: 0.00, #swa token: 515, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.93, #queue-req: 0, 
[2025-07-28 00:11:03 DP1 TP0] Decode batch. #running-req: 1, #full token: 298, full token usage: 0.00, #swa token: 298, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.14, #queue-req: 0, 
[2025-07-28 00:11:03 DP0 TP0] Decode batch. #running-req: 1, #full token: 555, full token usage: 0.00, #swa token: 555, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:11:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 338, full token usage: 0.00, #swa token: 338, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.12, #queue-req: 0, 
[2025-07-28 00:11:04 DP0 TP0] Decode batch. #running-req: 1, #full token: 595, full token usage: 0.00, #swa token: 595, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.80, #queue-req: 0, 
[2025-07-28 00:11:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 378, full token usage: 0.00, #swa token: 378, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.16, #queue-req: 0, 
[2025-07-28 00:11:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 635, full token usage: 0.00, #swa token: 635, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:11:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 418, full token usage: 0.00, #swa token: 418, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.11, #queue-req: 0, 
[2025-07-28 00:11:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 675, full token usage: 0.00, #swa token: 675, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.72, #queue-req: 0, 
[2025-07-28 00:11:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 458, full token usage: 0.00, #swa token: 458, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.16, #queue-req: 0, 
[2025-07-28 00:11:06 DP0 TP0] Decode batch. #running-req: 1, #full token: 715, full token usage: 0.00, #swa token: 715, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.77, #queue-req: 0, 
[2025-07-28 00:11:07 DP1 TP0] Decode batch. #running-req: 1, #full token: 498, full token usage: 0.00, #swa token: 498, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.05, #queue-req: 0, 
[2025-07-28 00:11:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 755, full token usage: 0.00, #swa token: 755, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.60, #queue-req: 0, 
[2025-07-28 00:11:07 DP1 TP0] Decode batch. #running-req: 1, #full token: 538, full token usage: 0.00, #swa token: 538, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.02, #queue-req: 0, 
[2025-07-28 00:11:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 795, full token usage: 0.00, #swa token: 795, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.75, #queue-req: 0, 
[2025-07-28 00:11:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 578, full token usage: 0.00, #swa token: 578, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.03, #queue-req: 0, 
[2025-07-28 00:11:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 835, full token usage: 0.00, #swa token: 835, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.77, #queue-req: 0, 
[2025-07-28 00:11:09 DP1 TP0] Decode batch. #running-req: 1, #full token: 618, full token usage: 0.00, #swa token: 618, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.03, #queue-req: 0, 
[2025-07-28 00:11:09 DP0 TP0] Decode batch. #running-req: 1, #full token: 875, full token usage: 0.00, #swa token: 875, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.74, #queue-req: 0, 
[2025-07-28 00:11:09 DP1 TP0] Decode batch. #running-req: 1, #full token: 658, full token usage: 0.00, #swa token: 658, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.01, #queue-req: 0, 
[2025-07-28 00:11:09] INFO:     127.0.0.1:41694 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:11:09 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 149, #cached-token: 98, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:11:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 249, full token usage: 0.00, #swa token: 249, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.76, #queue-req: 0, 
[2025-07-28 00:11:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 698, full token usage: 0.00, #swa token: 698, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.99, #queue-req: 0, 
[2025-07-28 00:11:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 289, full token usage: 0.00, #swa token: 289, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.94, #queue-req: 0, 
[2025-07-28 00:11:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 738, full token usage: 0.00, #swa token: 738, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.00, #queue-req: 0, 
[2025-07-28 00:11:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 329, full token usage: 0.00, #swa token: 329, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.92, #queue-req: 0, 
[2025-07-28 00:11:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 778, full token usage: 0.00, #swa token: 778, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.01, #queue-req: 0, 
[2025-07-28 00:11:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 369, full token usage: 0.00, #swa token: 369, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.92, #queue-req: 0, 
[2025-07-28 00:11:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 818, full token usage: 0.00, #swa token: 818, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.00, #queue-req: 0, 
[2025-07-28 00:11:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 409, full token usage: 0.00, #swa token: 409, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.91, #queue-req: 0, 
[2025-07-28 00:11:13] INFO:     127.0.0.1:41700 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:11:13 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 83, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:11:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 182, full token usage: 0.00, #swa token: 182, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.91, #queue-req: 0, 
[2025-07-28 00:11:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 449, full token usage: 0.00, #swa token: 449, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.91, #queue-req: 0, 
[2025-07-28 00:11:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 222, full token usage: 0.00, #swa token: 222, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.66, #queue-req: 0, 
[2025-07-28 00:11:14 DP0 TP0] Decode batch. #running-req: 1, #full token: 489, full token usage: 0.00, #swa token: 489, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.92, #queue-req: 0, 
[2025-07-28 00:11:14 DP1 TP0] Decode batch. #running-req: 1, #full token: 262, full token usage: 0.00, #swa token: 262, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.35, #queue-req: 0, 
[2025-07-28 00:11:14 DP0 TP0] Decode batch. #running-req: 1, #full token: 529, full token usage: 0.00, #swa token: 529, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.87, #queue-req: 0, 
[2025-07-28 00:11:15 DP1 TP0] Decode batch. #running-req: 1, #full token: 302, full token usage: 0.00, #swa token: 302, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.17, #queue-req: 0, 
[2025-07-28 00:11:15 DP0 TP0] Decode batch. #running-req: 1, #full token: 569, full token usage: 0.00, #swa token: 569, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.80, #queue-req: 0, 
[2025-07-28 00:11:15 DP1 TP0] Decode batch. #running-req: 1, #full token: 342, full token usage: 0.00, #swa token: 342, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.17, #queue-req: 0, 
[2025-07-28 00:11:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 609, full token usage: 0.00, #swa token: 609, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:11:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 382, full token usage: 0.00, #swa token: 382, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.16, #queue-req: 0, 
[2025-07-28 00:11:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 649, full token usage: 0.00, #swa token: 649, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.77, #queue-req: 0, 
[2025-07-28 00:11:16] INFO:     127.0.0.1:35378 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:11:16 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 155, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:11:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 422, full token usage: 0.00, #swa token: 422, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.14, #queue-req: 0, 
[2025-07-28 00:11:17 DP0 TP0] Decode batch. #running-req: 1, #full token: 288, full token usage: 0.00, #swa token: 288, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.81, #queue-req: 0, 
[2025-07-28 00:11:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 462, full token usage: 0.00, #swa token: 462, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.17, #queue-req: 0, 
[2025-07-28 00:11:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 328, full token usage: 0.00, #swa token: 328, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.93, #queue-req: 0, 
[2025-07-28 00:11:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 502, full token usage: 0.00, #swa token: 502, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.17, #queue-req: 0, 
[2025-07-28 00:11:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 368, full token usage: 0.00, #swa token: 368, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.91, #queue-req: 0, 
[2025-07-28 00:11:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 542, full token usage: 0.00, #swa token: 542, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.07, #queue-req: 0, 
[2025-07-28 00:11:19 DP0 TP0] Decode batch. #running-req: 1, #full token: 408, full token usage: 0.00, #swa token: 408, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.90, #queue-req: 0, 
[2025-07-28 00:11:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 582, full token usage: 0.00, #swa token: 582, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.06, #queue-req: 0, 
[2025-07-28 00:11:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 448, full token usage: 0.00, #swa token: 448, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.93, #queue-req: 0, 
[2025-07-28 00:11:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 622, full token usage: 0.00, #swa token: 622, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.07, #queue-req: 0, 
[2025-07-28 00:11:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 488, full token usage: 0.00, #swa token: 488, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.90, #queue-req: 0, 
[2025-07-28 00:11:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 662, full token usage: 0.00, #swa token: 662, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.02, #queue-req: 0, 
[2025-07-28 00:11:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 528, full token usage: 0.00, #swa token: 528, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.88, #queue-req: 0, 
[2025-07-28 00:11:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 702, full token usage: 0.00, #swa token: 702, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.02, #queue-req: 0, 
[2025-07-28 00:11:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 568, full token usage: 0.00, #swa token: 568, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.76, #queue-req: 0, 
[2025-07-28 00:11:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 742, full token usage: 0.00, #swa token: 742, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.97, #queue-req: 0, 
[2025-07-28 00:11:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 608, full token usage: 0.00, #swa token: 608, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:11:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 782, full token usage: 0.00, #swa token: 782, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.03, #queue-req: 0, 
[2025-07-28 00:11:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 648, full token usage: 0.00, #swa token: 648, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.79, #queue-req: 0, 
[2025-07-28 00:11:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 822, full token usage: 0.00, #swa token: 822, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.01, #queue-req: 0, 
[2025-07-28 00:11:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 688, full token usage: 0.00, #swa token: 688, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.77, #queue-req: 0, 
[2025-07-28 00:11:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 862, full token usage: 0.00, #swa token: 862, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.03, #queue-req: 0, 
[2025-07-28 00:11:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 728, full token usage: 0.00, #swa token: 728, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.78, #queue-req: 0, 
[2025-07-28 00:11:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 902, full token usage: 0.00, #swa token: 902, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.04, #queue-req: 0, 
[2025-07-28 00:11:25] INFO:     127.0.0.1:55638 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:11:25 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 91, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:11:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 768, full token usage: 0.00, #swa token: 768, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 58.87, #queue-req: 0, 
[2025-07-28 00:11:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 222, full token usage: 0.00, #swa token: 222, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 53.26, #queue-req: 0, 
[2025-07-28 00:11:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 808, full token usage: 0.00, #swa token: 808, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.80, #queue-req: 0, 
[2025-07-28 00:11:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 262, full token usage: 0.00, #swa token: 262, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.32, #queue-req: 0, 
[2025-07-28 00:11:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 848, full token usage: 0.00, #swa token: 848, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.78, #queue-req: 0, 
[2025-07-28 00:11:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 302, full token usage: 0.00, #swa token: 302, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.16, #queue-req: 0, 
[2025-07-28 00:11:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 888, full token usage: 0.00, #swa token: 888, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.76, #queue-req: 0, 
[2025-07-28 00:11:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 342, full token usage: 0.00, #swa token: 342, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.13, #queue-req: 0, 
[2025-07-28 00:11:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 928, full token usage: 0.00, #swa token: 928, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:11:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 382, full token usage: 0.00, #swa token: 382, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.14, #queue-req: 0, 
[2025-07-28 00:11:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 968, full token usage: 0.00, #swa token: 968, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.78, #queue-req: 0, 
[2025-07-28 00:11:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 422, full token usage: 0.00, #swa token: 422, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.12, #queue-req: 0, 
[2025-07-28 00:11:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 1008, full token usage: 0.00, #swa token: 1008, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.80, #queue-req: 0, 
[2025-07-28 00:11:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 462, full token usage: 0.00, #swa token: 462, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.15, #queue-req: 0, 
[2025-07-28 00:11:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 1048, full token usage: 0.00, #swa token: 1048, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.80, #queue-req: 0, 
[2025-07-28 00:11:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 502, full token usage: 0.00, #swa token: 502, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.13, #queue-req: 0, 
[2025-07-28 00:11:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 1088, full token usage: 0.00, #swa token: 1088, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.78, #queue-req: 0, 
[2025-07-28 00:11:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 542, full token usage: 0.00, #swa token: 542, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.08, #queue-req: 0, 
[2025-07-28 00:11:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 1128, full token usage: 0.00, #swa token: 1128, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.78, #queue-req: 0, 
[2025-07-28 00:11:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 582, full token usage: 0.00, #swa token: 582, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.05, #queue-req: 0, 
[2025-07-28 00:11:32] INFO:     127.0.0.1:55654 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:11:32 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 179, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:11:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 289, full token usage: 0.00, #swa token: 289, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.68, #queue-req: 0, 
[2025-07-28 00:11:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 622, full token usage: 0.00, #swa token: 622, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.07, #queue-req: 0, 
[2025-07-28 00:11:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 329, full token usage: 0.00, #swa token: 329, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.91, #queue-req: 0, 
[2025-07-28 00:11:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 662, full token usage: 0.00, #swa token: 662, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.05, #queue-req: 0, 
[2025-07-28 00:11:33 DP0 TP0] Decode batch. #running-req: 1, #full token: 369, full token usage: 0.00, #swa token: 369, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.91, #queue-req: 0, 
[2025-07-28 00:11:33 DP1 TP0] Decode batch. #running-req: 1, #full token: 702, full token usage: 0.00, #swa token: 702, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.06, #queue-req: 0, 
[2025-07-28 00:11:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 409, full token usage: 0.00, #swa token: 409, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.92, #queue-req: 0, 
[2025-07-28 00:11:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 742, full token usage: 0.00, #swa token: 742, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.97, #queue-req: 0, 
[2025-07-28 00:11:34 DP0 TP0] Decode batch. #running-req: 1, #full token: 449, full token usage: 0.00, #swa token: 449, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.92, #queue-req: 0, 
[2025-07-28 00:11:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 782, full token usage: 0.00, #swa token: 782, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.03, #queue-req: 0, 
[2025-07-28 00:11:35 DP0 TP0] Decode batch. #running-req: 1, #full token: 489, full token usage: 0.00, #swa token: 489, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.90, #queue-req: 0, 
[2025-07-28 00:11:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 822, full token usage: 0.00, #swa token: 822, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.04, #queue-req: 0, 
[2025-07-28 00:11:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 529, full token usage: 0.00, #swa token: 529, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.88, #queue-req: 0, 
[2025-07-28 00:11:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 862, full token usage: 0.00, #swa token: 862, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.04, #queue-req: 0, 
[2025-07-28 00:11:36 DP0 TP0] Decode batch. #running-req: 1, #full token: 569, full token usage: 0.00, #swa token: 569, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:11:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 902, full token usage: 0.00, #swa token: 902, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.04, #queue-req: 0, 
[2025-07-28 00:11:37 DP0 TP0] Decode batch. #running-req: 1, #full token: 609, full token usage: 0.00, #swa token: 609, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:11:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 942, full token usage: 0.00, #swa token: 942, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.03, #queue-req: 0, 
[2025-07-28 00:11:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 649, full token usage: 0.00, #swa token: 649, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.80, #queue-req: 0, 
[2025-07-28 00:11:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 982, full token usage: 0.00, #swa token: 982, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.99, #queue-req: 0, 
[2025-07-28 00:11:38] INFO:     127.0.0.1:43640 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:11:38 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 183, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:11:38 DP0 TP0] Decode batch. #running-req: 1, #full token: 689, full token usage: 0.00, #swa token: 689, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.71, #queue-req: 0, 
[2025-07-28 00:11:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 318, full token usage: 0.00, #swa token: 318, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.62, #queue-req: 0, 
[2025-07-28 00:11:39 DP0 TP0] Decode batch. #running-req: 1, #full token: 729, full token usage: 0.00, #swa token: 729, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.76, #queue-req: 0, 
[2025-07-28 00:11:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 358, full token usage: 0.00, #swa token: 358, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.18, #queue-req: 0, 
[2025-07-28 00:11:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 769, full token usage: 0.00, #swa token: 769, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.75, #queue-req: 0, 
[2025-07-28 00:11:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 398, full token usage: 0.00, #swa token: 398, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.17, #queue-req: 0, 
[2025-07-28 00:11:40 DP0 TP0] Decode batch. #running-req: 1, #full token: 809, full token usage: 0.00, #swa token: 809, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.73, #queue-req: 0, 
[2025-07-28 00:11:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 438, full token usage: 0.00, #swa token: 438, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.20, #queue-req: 0, 
[2025-07-28 00:11:41 DP0 TP0] Decode batch. #running-req: 1, #full token: 849, full token usage: 0.00, #swa token: 849, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.76, #queue-req: 0, 
[2025-07-28 00:11:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 478, full token usage: 0.00, #swa token: 478, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.22, #queue-req: 0, 
[2025-07-28 00:11:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 889, full token usage: 0.00, #swa token: 889, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.76, #queue-req: 0, 
[2025-07-28 00:11:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 518, full token usage: 0.00, #swa token: 518, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.15, #queue-req: 0, 
[2025-07-28 00:11:42 DP0 TP0] Decode batch. #running-req: 1, #full token: 929, full token usage: 0.00, #swa token: 929, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.77, #queue-req: 0, 
[2025-07-28 00:11:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 558, full token usage: 0.00, #swa token: 558, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.10, #queue-req: 0, 
[2025-07-28 00:11:43 DP0 TP0] Decode batch. #running-req: 1, #full token: 969, full token usage: 0.00, #swa token: 969, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.76, #queue-req: 0, 
[2025-07-28 00:11:43 DP1 TP0] Decode batch. #running-req: 1, #full token: 598, full token usage: 0.00, #swa token: 598, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.06, #queue-req: 0, 
[2025-07-28 00:11:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 1009, full token usage: 0.00, #swa token: 1009, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.76, #queue-req: 0, 
[2025-07-28 00:11:44 DP1 TP0] Decode batch. #running-req: 1, #full token: 638, full token usage: 0.00, #swa token: 638, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.09, #queue-req: 0, 
[2025-07-28 00:11:44 DP0 TP0] Decode batch. #running-req: 1, #full token: 1049, full token usage: 0.00, #swa token: 1049, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.77, #queue-req: 0, 
[2025-07-28 00:11:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 678, full token usage: 0.00, #swa token: 678, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.07, #queue-req: 0, 
[2025-07-28 00:11:45 DP0 TP0] Decode batch. #running-req: 1, #full token: 1089, full token usage: 0.00, #swa token: 1089, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.77, #queue-req: 0, 
[2025-07-28 00:11:45 DP1 TP0] Decode batch. #running-req: 1, #full token: 718, full token usage: 0.00, #swa token: 718, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.05, #queue-req: 0, 
[2025-07-28 00:11:46] INFO:     127.0.0.1:43656 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:11:46 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 96, #cached-token: 97, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:11:46 DP0 TP0] Decode batch. #running-req: 1, #full token: 199, full token usage: 0.00, #swa token: 199, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.83, #queue-req: 0, 
[2025-07-28 00:11:46 DP1 TP0] Decode batch. #running-req: 1, #full token: 758, full token usage: 0.00, #swa token: 758, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.07, #queue-req: 0, 
[2025-07-28 00:11:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 239, full token usage: 0.00, #swa token: 239, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.31, #queue-req: 0, 
[2025-07-28 00:11:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 798, full token usage: 0.00, #swa token: 798, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.08, #queue-req: 0, 
[2025-07-28 00:11:47 DP0 TP0] Decode batch. #running-req: 1, #full token: 279, full token usage: 0.00, #swa token: 279, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.02, #queue-req: 0, 
[2025-07-28 00:11:47 DP1 TP0] Decode batch. #running-req: 1, #full token: 838, full token usage: 0.00, #swa token: 838, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.05, #queue-req: 0, 
[2025-07-28 00:11:48 DP0 TP0] Decode batch. #running-req: 1, #full token: 319, full token usage: 0.00, #swa token: 319, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.97, #queue-req: 0, 
[2025-07-28 00:11:48 DP1 TP0] Decode batch. #running-req: 1, #full token: 878, full token usage: 0.00, #swa token: 878, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.06, #queue-req: 0, 
[2025-07-28 00:11:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 359, full token usage: 0.00, #swa token: 359, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.96, #queue-req: 0, 
[2025-07-28 00:11:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 918, full token usage: 0.00, #swa token: 918, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.07, #queue-req: 0, 
[2025-07-28 00:11:49 DP0 TP0] Decode batch. #running-req: 1, #full token: 399, full token usage: 0.00, #swa token: 399, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.95, #queue-req: 0, 
[2025-07-28 00:11:49 DP1 TP0] Decode batch. #running-req: 1, #full token: 958, full token usage: 0.00, #swa token: 958, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.08, #queue-req: 0, 
[2025-07-28 00:11:50 DP0 TP0] Decode batch. #running-req: 1, #full token: 439, full token usage: 0.00, #swa token: 439, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.93, #queue-req: 0, 
[2025-07-28 00:11:50 DP1 TP0] Decode batch. #running-req: 1, #full token: 998, full token usage: 0.00, #swa token: 998, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.07, #queue-req: 0, 
[2025-07-28 00:11:51] INFO:     127.0.0.1:43928 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:11:51 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 156, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:11:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 479, full token usage: 0.00, #swa token: 479, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.94, #queue-req: 0, 
[2025-07-28 00:11:51 DP1 TP0] Decode batch. #running-req: 1, #full token: 267, full token usage: 0.00, #swa token: 267, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.93, #queue-req: 0, 
[2025-07-28 00:11:51 DP0 TP0] Decode batch. #running-req: 1, #full token: 519, full token usage: 0.00, #swa token: 519, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.94, #queue-req: 0, 
[2025-07-28 00:11:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 307, full token usage: 0.00, #swa token: 307, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.20, #queue-req: 0, 
[2025-07-28 00:11:52 DP0 TP0] Decode batch. #running-req: 1, #full token: 559, full token usage: 0.00, #swa token: 559, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 58.96, #queue-req: 0, 
[2025-07-28 00:11:52 DP1 TP0] Decode batch. #running-req: 1, #full token: 347, full token usage: 0.00, #swa token: 347, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.18, #queue-req: 0, 
[2025-07-28 00:11:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 599, full token usage: 0.00, #swa token: 599, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:11:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 387, full token usage: 0.00, #swa token: 387, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.22, #queue-req: 0, 
[2025-07-28 00:11:53 DP0 TP0] Decode batch. #running-req: 1, #full token: 639, full token usage: 0.00, #swa token: 639, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.86, #queue-req: 0, 
[2025-07-28 00:11:53 DP1 TP0] Decode batch. #running-req: 1, #full token: 427, full token usage: 0.00, #swa token: 427, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.21, #queue-req: 0, 
[2025-07-28 00:11:54 DP0 TP0] Decode batch. #running-req: 1, #full token: 679, full token usage: 0.00, #swa token: 679, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:11:54 DP1 TP0] Decode batch. #running-req: 1, #full token: 467, full token usage: 0.00, #swa token: 467, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.20, #queue-req: 0, 
[2025-07-28 00:11:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 719, full token usage: 0.00, #swa token: 719, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.76, #queue-req: 0, 
[2025-07-28 00:11:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 507, full token usage: 0.00, #swa token: 507, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.15, #queue-req: 0, 
[2025-07-28 00:11:55 DP0 TP0] Decode batch. #running-req: 1, #full token: 759, full token usage: 0.00, #swa token: 759, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:11:55 DP1 TP0] Decode batch. #running-req: 1, #full token: 547, full token usage: 0.00, #swa token: 547, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.08, #queue-req: 0, 
[2025-07-28 00:11:56 DP0 TP0] Decode batch. #running-req: 1, #full token: 799, full token usage: 0.00, #swa token: 799, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:11:56 DP1 TP0] Decode batch. #running-req: 1, #full token: 587, full token usage: 0.00, #swa token: 587, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.07, #queue-req: 0, 
[2025-07-28 00:11:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 839, full token usage: 0.00, #swa token: 839, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:11:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 627, full token usage: 0.00, #swa token: 627, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.03, #queue-req: 0, 
[2025-07-28 00:11:57 DP0 TP0] Decode batch. #running-req: 1, #full token: 879, full token usage: 0.00, #swa token: 879, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.82, #queue-req: 0, 
[2025-07-28 00:11:57 DP1 TP0] Decode batch. #running-req: 1, #full token: 667, full token usage: 0.00, #swa token: 667, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.01, #queue-req: 0, 
[2025-07-28 00:11:58] INFO:     127.0.0.1:47302 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:11:58 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 127, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:11:58 DP0 TP0] Decode batch. #running-req: 1, #full token: 249, full token usage: 0.00, #swa token: 249, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.87, #queue-req: 0, 
[2025-07-28 00:11:58 DP1 TP0] Decode batch. #running-req: 1, #full token: 707, full token usage: 0.00, #swa token: 707, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.99, #queue-req: 0, 
[2025-07-28 00:11:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 289, full token usage: 0.00, #swa token: 289, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.95, #queue-req: 0, 
[2025-07-28 00:11:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 747, full token usage: 0.00, #swa token: 747, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.99, #queue-req: 0, 
[2025-07-28 00:11:59 DP0 TP0] Decode batch. #running-req: 1, #full token: 329, full token usage: 0.00, #swa token: 329, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.97, #queue-req: 0, 
[2025-07-28 00:11:59 DP1 TP0] Decode batch. #running-req: 1, #full token: 787, full token usage: 0.00, #swa token: 787, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.00, #queue-req: 0, 
[2025-07-28 00:12:00 DP0 TP0] Decode batch. #running-req: 1, #full token: 369, full token usage: 0.00, #swa token: 369, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.95, #queue-req: 0, 
[2025-07-28 00:12:00 DP1 TP0] Decode batch. #running-req: 1, #full token: 827, full token usage: 0.00, #swa token: 827, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.99, #queue-req: 0, 
[2025-07-28 00:12:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 409, full token usage: 0.00, #swa token: 409, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.96, #queue-req: 0, 
[2025-07-28 00:12:01 DP1 TP0] Decode batch. #running-req: 1, #full token: 867, full token usage: 0.00, #swa token: 867, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.02, #queue-req: 0, 
[2025-07-28 00:12:01 DP0 TP0] Decode batch. #running-req: 1, #full token: 449, full token usage: 0.00, #swa token: 449, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.95, #queue-req: 0, 
[2025-07-28 00:12:01 DP1 TP0] Decode batch. #running-req: 1, #full token: 907, full token usage: 0.00, #swa token: 907, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.01, #queue-req: 0, 
[2025-07-28 00:12:02 DP0 TP0] Decode batch. #running-req: 1, #full token: 489, full token usage: 0.00, #swa token: 489, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.94, #queue-req: 0, 
[2025-07-28 00:12:02 DP1 TP0] Decode batch. #running-req: 1, #full token: 947, full token usage: 0.00, #swa token: 947, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.03, #queue-req: 0, 
[2025-07-28 00:12:03 DP0 TP0] Decode batch. #running-req: 1, #full token: 529, full token usage: 0.00, #swa token: 529, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.91, #queue-req: 0, 
[2025-07-28 00:12:03 DP1 TP0] Decode batch. #running-req: 1, #full token: 987, full token usage: 0.00, #swa token: 987, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.98, #queue-req: 0, 
[2025-07-28 00:12:03 DP0 TP0] Decode batch. #running-req: 1, #full token: 569, full token usage: 0.00, #swa token: 569, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.83, #queue-req: 0, 
[2025-07-28 00:12:03 DP1 TP0] Decode batch. #running-req: 1, #full token: 1027, full token usage: 0.00, #swa token: 1027, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.06, #queue-req: 0, 
[2025-07-28 00:12:04 DP0 TP0] Decode batch. #running-req: 1, #full token: 609, full token usage: 0.00, #swa token: 609, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.84, #queue-req: 0, 
[2025-07-28 00:12:04 DP1 TP0] Decode batch. #running-req: 1, #full token: 1067, full token usage: 0.00, #swa token: 1067, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.06, #queue-req: 0, 
[2025-07-28 00:12:04] INFO:     127.0.0.1:47304 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:12:04 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 135, #cached-token: 99, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:12:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 649, full token usage: 0.00, #swa token: 649, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.79, #queue-req: 0, 
[2025-07-28 00:12:05 DP1 TP0] Decode batch. #running-req: 1, #full token: 260, full token usage: 0.00, #swa token: 260, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 53.08, #queue-req: 0, 
[2025-07-28 00:12:05 DP0 TP0] Decode batch. #running-req: 1, #full token: 689, full token usage: 0.00, #swa token: 689, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.80, #queue-req: 0, 
[2025-07-28 00:12:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 300, full token usage: 0.00, #swa token: 300, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.17, #queue-req: 0, 
[2025-07-28 00:12:06 DP0 TP0] Decode batch. #running-req: 1, #full token: 729, full token usage: 0.00, #swa token: 729, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.79, #queue-req: 0, 
[2025-07-28 00:12:06 DP1 TP0] Decode batch. #running-req: 1, #full token: 340, full token usage: 0.00, #swa token: 340, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.17, #queue-req: 0, 
[2025-07-28 00:12:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 769, full token usage: 0.00, #swa token: 769, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.80, #queue-req: 0, 
[2025-07-28 00:12:07 DP1 TP0] Decode batch. #running-req: 1, #full token: 380, full token usage: 0.00, #swa token: 380, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.16, #queue-req: 0, 
[2025-07-28 00:12:07 DP0 TP0] Decode batch. #running-req: 1, #full token: 809, full token usage: 0.00, #swa token: 809, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.78, #queue-req: 0, 
[2025-07-28 00:12:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 420, full token usage: 0.00, #swa token: 420, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.17, #queue-req: 0, 
[2025-07-28 00:12:08 DP0 TP0] Decode batch. #running-req: 1, #full token: 849, full token usage: 0.00, #swa token: 849, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.78, #queue-req: 0, 
[2025-07-28 00:12:08 DP1 TP0] Decode batch. #running-req: 1, #full token: 460, full token usage: 0.00, #swa token: 460, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.19, #queue-req: 0, 
[2025-07-28 00:12:09 DP0 TP0] Decode batch. #running-req: 1, #full token: 889, full token usage: 0.00, #swa token: 889, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.78, #queue-req: 0, 
[2025-07-28 00:12:09 DP1 TP0] Decode batch. #running-req: 1, #full token: 500, full token usage: 0.00, #swa token: 500, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.18, #queue-req: 0, 
[2025-07-28 00:12:09 DP0 TP0] Decode batch. #running-req: 1, #full token: 929, full token usage: 0.00, #swa token: 929, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.80, #queue-req: 0, 
[2025-07-28 00:12:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 540, full token usage: 0.00, #swa token: 540, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.11, #queue-req: 0, 
[2025-07-28 00:12:10] INFO:     127.0.0.1:44528 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:12:10 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 131, #cached-token: 103, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:12:10 DP0 TP0] Decode batch. #running-req: 1, #full token: 247, full token usage: 0.00, #swa token: 247, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.95, #queue-req: 0, 
[2025-07-28 00:12:10 DP1 TP0] Decode batch. #running-req: 1, #full token: 580, full token usage: 0.00, #swa token: 580, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.07, #queue-req: 0, 
[2025-07-28 00:12:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 287, full token usage: 0.00, #swa token: 287, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.83, #queue-req: 0, 
[2025-07-28 00:12:11 DP1 TP0] Decode batch. #running-req: 1, #full token: 620, full token usage: 0.00, #swa token: 620, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.02, #queue-req: 0, 
[2025-07-28 00:12:11 DP0 TP0] Decode batch. #running-req: 1, #full token: 327, full token usage: 0.00, #swa token: 327, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.93, #queue-req: 0, 
[2025-07-28 00:12:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 660, full token usage: 0.00, #swa token: 660, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.06, #queue-req: 0, 
[2025-07-28 00:12:12 DP0 TP0] Decode batch. #running-req: 1, #full token: 367, full token usage: 0.00, #swa token: 367, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.94, #queue-req: 0, 
[2025-07-28 00:12:12 DP1 TP0] Decode batch. #running-req: 1, #full token: 700, full token usage: 0.00, #swa token: 700, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.08, #queue-req: 0, 
[2025-07-28 00:12:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 407, full token usage: 0.00, #swa token: 407, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.94, #queue-req: 0, 
[2025-07-28 00:12:13 DP1 TP0] Decode batch. #running-req: 1, #full token: 740, full token usage: 0.00, #swa token: 740, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.06, #queue-req: 0, 
[2025-07-28 00:12:13 DP0 TP0] Decode batch. #running-req: 1, #full token: 447, full token usage: 0.00, #swa token: 447, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.94, #queue-req: 0, 
[2025-07-28 00:12:14 DP1 TP0] Decode batch. #running-req: 1, #full token: 780, full token usage: 0.00, #swa token: 780, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.07, #queue-req: 0, 
[2025-07-28 00:12:14 DP0 TP0] Decode batch. #running-req: 1, #full token: 487, full token usage: 0.00, #swa token: 487, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.94, #queue-req: 0, 
[2025-07-28 00:12:14 DP1 TP0] Decode batch. #running-req: 1, #full token: 820, full token usage: 0.00, #swa token: 820, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.06, #queue-req: 0, 
[2025-07-28 00:12:15 DP0 TP0] Decode batch. #running-req: 1, #full token: 527, full token usage: 0.00, #swa token: 527, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.91, #queue-req: 0, 
[2025-07-28 00:12:15 DP1 TP0] Decode batch. #running-req: 1, #full token: 860, full token usage: 0.00, #swa token: 860, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.04, #queue-req: 0, 
[2025-07-28 00:12:15 DP0 TP0] Decode batch. #running-req: 1, #full token: 567, full token usage: 0.00, #swa token: 567, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.84, #queue-req: 0, 
[2025-07-28 00:12:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 900, full token usage: 0.00, #swa token: 900, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.06, #queue-req: 0, 
[2025-07-28 00:12:16 DP0 TP0] Decode batch. #running-req: 1, #full token: 607, full token usage: 0.00, #swa token: 607, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.83, #queue-req: 0, 
[2025-07-28 00:12:16 DP1 TP0] Decode batch. #running-req: 1, #full token: 940, full token usage: 0.00, #swa token: 940, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.07, #queue-req: 0, 
[2025-07-28 00:12:17 DP0 TP0] Decode batch. #running-req: 1, #full token: 647, full token usage: 0.00, #swa token: 647, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:12:17 DP1 TP0] Decode batch. #running-req: 1, #full token: 980, full token usage: 0.00, #swa token: 980, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.08, #queue-req: 0, 
[2025-07-28 00:12:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 687, full token usage: 0.00, #swa token: 687, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.81, #queue-req: 0, 
[2025-07-28 00:12:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 1020, full token usage: 0.00, #swa token: 1020, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.07, #queue-req: 0, 
[2025-07-28 00:12:18 DP0 TP0] Decode batch. #running-req: 1, #full token: 727, full token usage: 0.00, #swa token: 727, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.80, #queue-req: 0, 
[2025-07-28 00:12:18 DP1 TP0] Decode batch. #running-req: 1, #full token: 1060, full token usage: 0.00, #swa token: 1060, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.07, #queue-req: 0, 
[2025-07-28 00:12:19 DP0 TP0] Decode batch. #running-req: 1, #full token: 767, full token usage: 0.00, #swa token: 767, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.78, #queue-req: 0, 
[2025-07-28 00:12:19 DP1 TP0] Decode batch. #running-req: 1, #full token: 1100, full token usage: 0.00, #swa token: 1100, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.08, #queue-req: 0, 
[2025-07-28 00:12:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 807, full token usage: 0.00, #swa token: 807, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.78, #queue-req: 0, 
[2025-07-28 00:12:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 1140, full token usage: 0.00, #swa token: 1140, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.08, #queue-req: 0, 
[2025-07-28 00:12:20] INFO:     127.0.0.1:38908 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:12:20 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 152, #cached-token: 104, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:12:20 DP0 TP0] Decode batch. #running-req: 1, #full token: 847, full token usage: 0.00, #swa token: 847, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.80, #queue-req: 0, 
[2025-07-28 00:12:20 DP1 TP0] Decode batch. #running-req: 1, #full token: 284, full token usage: 0.00, #swa token: 284, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.93, #queue-req: 0, 
[2025-07-28 00:12:21 DP0 TP0] Decode batch. #running-req: 1, #full token: 887, full token usage: 0.00, #swa token: 887, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.74, #queue-req: 0, 
[2025-07-28 00:12:21 DP1 TP0] Decode batch. #running-req: 1, #full token: 324, full token usage: 0.00, #swa token: 324, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.18, #queue-req: 0, 
[2025-07-28 00:12:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 927, full token usage: 0.00, #swa token: 927, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.76, #queue-req: 0, 
[2025-07-28 00:12:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 364, full token usage: 0.00, #swa token: 364, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.20, #queue-req: 0, 
[2025-07-28 00:12:22 DP0 TP0] Decode batch. #running-req: 1, #full token: 967, full token usage: 0.00, #swa token: 967, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.78, #queue-req: 0, 
[2025-07-28 00:12:22 DP1 TP0] Decode batch. #running-req: 1, #full token: 404, full token usage: 0.00, #swa token: 404, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.21, #queue-req: 0, 
[2025-07-28 00:12:23 DP0 TP0] Decode batch. #running-req: 1, #full token: 1007, full token usage: 0.00, #swa token: 1007, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.78, #queue-req: 0, 
[2025-07-28 00:12:23 DP1 TP0] Decode batch. #running-req: 1, #full token: 444, full token usage: 0.00, #swa token: 444, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.21, #queue-req: 0, 
[2025-07-28 00:12:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 1047, full token usage: 0.00, #swa token: 1047, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.78, #queue-req: 0, 
[2025-07-28 00:12:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 484, full token usage: 0.00, #swa token: 484, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.18, #queue-req: 0, 
[2025-07-28 00:12:24 DP0 TP0] Decode batch. #running-req: 1, #full token: 1087, full token usage: 0.00, #swa token: 1087, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.75, #queue-req: 0, 
[2025-07-28 00:12:24 DP1 TP0] Decode batch. #running-req: 1, #full token: 524, full token usage: 0.00, #swa token: 524, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.14, #queue-req: 0, 
[2025-07-28 00:12:25] INFO:     127.0.0.1:38910 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:12:25 DP0 TP0] Prefill batch. #new-seq: 1, #new-token: 110, #cached-token: 103, full token usage: 0.00, swa token usage: 0.00, #running-req: 0, #queue-req: 0, 
[2025-07-28 00:12:25 DP0 TP0] Decode batch. #running-req: 1, #full token: 223, full token usage: 0.00, #swa token: 223, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 52.82, #queue-req: 0, 
[2025-07-28 00:12:25 DP1 TP0] Decode batch. #running-req: 1, #full token: 564, full token usage: 0.00, #swa token: 564, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.05, #queue-req: 0, 
[2025-07-28 00:12:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 263, full token usage: 0.00, #swa token: 263, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.11, #queue-req: 0, 
[2025-07-28 00:12:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 604, full token usage: 0.00, #swa token: 604, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.07, #queue-req: 0, 
[2025-07-28 00:12:26 DP0 TP0] Decode batch. #running-req: 1, #full token: 303, full token usage: 0.00, #swa token: 303, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.01, #queue-req: 0, 
[2025-07-28 00:12:26 DP1 TP0] Decode batch. #running-req: 1, #full token: 644, full token usage: 0.00, #swa token: 644, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.09, #queue-req: 0, 
[2025-07-28 00:12:27 DP0 TP0] Decode batch. #running-req: 1, #full token: 343, full token usage: 0.00, #swa token: 343, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.96, #queue-req: 0, 
[2025-07-28 00:12:27 DP1 TP0] Decode batch. #running-req: 1, #full token: 684, full token usage: 0.00, #swa token: 684, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.00, #queue-req: 0, 
[2025-07-28 00:12:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 383, full token usage: 0.00, #swa token: 383, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.95, #queue-req: 0, 
[2025-07-28 00:12:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 724, full token usage: 0.00, #swa token: 724, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.01, #queue-req: 0, 
[2025-07-28 00:12:28 DP0 TP0] Decode batch. #running-req: 1, #full token: 423, full token usage: 0.00, #swa token: 423, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.98, #queue-req: 0, 
[2025-07-28 00:12:28 DP1 TP0] Decode batch. #running-req: 1, #full token: 764, full token usage: 0.00, #swa token: 764, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.05, #queue-req: 0, 
[2025-07-28 00:12:29 DP1 TP0] Decode batch. #running-req: 1, #full token: 804, full token usage: 0.00, #swa token: 804, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.04, #queue-req: 0, 
[2025-07-28 00:12:29 DP0 TP0] Decode batch. #running-req: 1, #full token: 463, full token usage: 0.00, #swa token: 463, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.93, #queue-req: 0, 
[2025-07-28 00:12:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 844, full token usage: 0.00, #swa token: 844, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.06, #queue-req: 0, 
[2025-07-28 00:12:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 503, full token usage: 0.00, #swa token: 503, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.94, #queue-req: 0, 
[2025-07-28 00:12:30 DP1 TP0] Decode batch. #running-req: 1, #full token: 884, full token usage: 0.00, #swa token: 884, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.03, #queue-req: 0, 
[2025-07-28 00:12:30 DP0 TP0] Decode batch. #running-req: 1, #full token: 543, full token usage: 0.00, #swa token: 543, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.86, #queue-req: 0, 
[2025-07-28 00:12:31 DP1 TP0] Decode batch. #running-req: 1, #full token: 924, full token usage: 0.00, #swa token: 924, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.02, #queue-req: 0, 
[2025-07-28 00:12:31 DP0 TP0] Decode batch. #running-req: 1, #full token: 583, full token usage: 0.00, #swa token: 583, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.85, #queue-req: 0, 
[2025-07-28 00:12:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 964, full token usage: 0.00, #swa token: 964, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.07, #queue-req: 0, 
[2025-07-28 00:12:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 623, full token usage: 0.00, #swa token: 623, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.83, #queue-req: 0, 
[2025-07-28 00:12:32 DP1 TP0] Decode batch. #running-req: 1, #full token: 1004, full token usage: 0.00, #swa token: 1004, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.09, #queue-req: 0, 
[2025-07-28 00:12:32 DP0 TP0] Decode batch. #running-req: 1, #full token: 663, full token usage: 0.00, #swa token: 663, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 59.83, #queue-req: 0, 
[2025-07-28 00:12:33] INFO:     127.0.0.1:44710 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:12:33 DP1 TP0] Prefill batch. #new-seq: 1, #new-token: 96, #cached-token: 103, full token usage: 0.00, swa token usage: 0.00, #running-req: 1, #queue-req: 0, 
[2025-07-28 00:12:33 DP1 TP0] Decode batch. #running-req: 2, #full token: 1168, full token usage: 0.00, #swa token: 1168, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 82.57, #queue-req: 0, 
[2025-07-28 00:12:34 DP1 TP0] Decode batch. #running-req: 2, #full token: 1248, full token usage: 0.00, #swa token: 1248, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 115.94, #queue-req: 0, 
[2025-07-28 00:12:34] INFO:     127.0.0.1:41440 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-28 00:12:34 DP1 TP0] Decode batch. #running-req: 1, #full token: 303, full token usage: 0.00, #swa token: 303, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 81.60, #queue-req: 0, 
[2025-07-28 00:12:35 DP1 TP0] Decode batch. #running-req: 1, #full token: 343, full token usage: 0.00, #swa token: 343, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.18, #queue-req: 0, 
[2025-07-28 00:12:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 383, full token usage: 0.00, #swa token: 383, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.18, #queue-req: 0, 
[2025-07-28 00:12:36 DP1 TP0] Decode batch. #running-req: 1, #full token: 423, full token usage: 0.00, #swa token: 423, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.19, #queue-req: 0, 
[2025-07-28 00:12:37 DP1 TP0] Decode batch. #running-req: 1, #full token: 463, full token usage: 0.00, #swa token: 463, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.19, #queue-req: 0, 
[2025-07-28 00:12:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 503, full token usage: 0.00, #swa token: 503, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.22, #queue-req: 0, 
[2025-07-28 00:12:38 DP1 TP0] Decode batch. #running-req: 1, #full token: 543, full token usage: 0.00, #swa token: 543, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.09, #queue-req: 0, 
[2025-07-28 00:12:39 DP1 TP0] Decode batch. #running-req: 1, #full token: 583, full token usage: 0.00, #swa token: 583, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.08, #queue-req: 0, 
[2025-07-28 00:12:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 623, full token usage: 0.00, #swa token: 623, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.06, #queue-req: 0, 
[2025-07-28 00:12:40 DP1 TP0] Decode batch. #running-req: 1, #full token: 663, full token usage: 0.00, #swa token: 663, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.08, #queue-req: 0, 
[2025-07-28 00:12:41 DP1 TP0] Decode batch. #running-req: 1, #full token: 703, full token usage: 0.00, #swa token: 703, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.08, #queue-req: 0, 
[2025-07-28 00:12:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 743, full token usage: 0.00, #swa token: 743, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.07, #queue-req: 0, 
[2025-07-28 00:12:42 DP1 TP0] Decode batch. #running-req: 1, #full token: 783, full token usage: 0.00, #swa token: 783, swa token usage: 0.00, cuda graph: True, gen throughput (token/s): 60.06, #queue-req: 0, 
[2025-07-28 00:12:42] INFO:     127.0.0.1:58130 - "POST /v1/chat/completions HTTP/1.1" 200 OK

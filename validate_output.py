#!/usr/bin/env python3
"""
Script to validate the merged JSONL output file.
"""

import json
import sys
from collections import defaultdict

def validate_jsonl(filename):
    """Validate the JSONL file format and content."""
    print(f"Validating {filename}...")
    
    total_entries = 0
    model_counts = defaultdict(int)
    custom_id_set = set()
    missing_fields = defaultdict(int)
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                    
                try:
                    entry = json.loads(line)
                    total_entries += 1
                    
                    # Check required fields
                    required_fields = ['custom_id', 'id', 'index', 'prompt', 'target', 'response', 'model']
                    for field in required_fields:
                        if field not in entry:
                            missing_fields[field] += 1
                        elif field == 'custom_id':
                            if entry[field] in custom_id_set:
                                print(f"Warning: Duplicate custom_id {entry[field]} at line {line_num}")
                            custom_id_set.add(entry[field])
                        elif field == 'model':
                            model_counts[entry[field]] += 1
                            
                except json.JSONDecodeError as e:
                    print(f"Error: Invalid JSON at line {line_num}: {e}")
                    
    except FileNotFoundError:
        print(f"Error: File {filename} not found")
        return False
        
    # Print validation results
    print(f"\nValidation Results:")
    print(f"Total entries: {total_entries}")
    print(f"Unique custom_ids: {len(custom_id_set)}")
    
    if missing_fields:
        print(f"\nMissing fields:")
        for field, count in missing_fields.items():
            print(f"  {field}: {count} entries missing")
    else:
        print("✓ All required fields present")
        
    print(f"\nModel distribution:")
    for model, count in sorted(model_counts.items()):
        print(f"  {model}: {count} entries")
        
    # Check custom_id sequence
    expected_custom_ids = set(range(1, total_entries + 1))
    actual_custom_ids = custom_id_set
    
    if expected_custom_ids == actual_custom_ids:
        print("✓ Custom IDs are sequential from 1 to", total_entries)
    else:
        missing = expected_custom_ids - actual_custom_ids
        extra = actual_custom_ids - expected_custom_ids
        if missing:
            print(f"Missing custom_ids: {sorted(missing)[:10]}{'...' if len(missing) > 10 else ''}")
        if extra:
            print(f"Extra custom_ids: {sorted(extra)[:10]}{'...' if len(extra) > 10 else ''}")
            
    return len(missing_fields) == 0

if __name__ == "__main__":
    filename = sys.argv[1] if len(sys.argv) > 1 else "merged_output.jsonl"
    validate_jsonl(filename)

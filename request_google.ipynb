

from google import genai
from google.genai import types
import os

# os.environ[""] = "AIzaSyDKXQVpntZl8a-DTbW4YD80b8nfj2I7_jE"

client = genai.Client(api_key="AIzaSyDKXQVpntZl8a-DTbW4YD80b8nfj2I7_jE")


uploaded_file = client.files.upload(
    file='my-batch-requests.jsonl',
    config=types.UploadFileConfig(display_name='my-batch-requests', mime_type='jsonl')
)

print(f"Uploaded file: {uploaded_file.name}")
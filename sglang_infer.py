import os
import sys
import time
import signal
import shlex
import subprocess
import requests
from contextlib import contextmanager
import json

def _wait_for_ready(base_url: str, api_key: str | None = None, timeout: float = 600.0):
    """轮询 /health，直到就绪或超时"""
    headers = {"Authorization": f"Bearer {api_key}"} if api_key else {}
    deadline = time.time() + timeout
    url = f"{base_url.rstrip('/')}/health"
    last_err = None
    while time.time() < deadline:
        try:
            r = requests.get(url, headers=headers, timeout=5)
            if r.status_code == 200:
                return
        except Exception as e:
            last_err = e
        time.sleep(2)
    raise TimeoutError(f"SGLang server 未在 {timeout}s 内就绪；最后错误：{last_err}")

def _graceful_shutdown(proc: subprocess.Popen, grace: float = 20.0):
    """优雅关闭：SIGINT -> 等待 -> SIGTERM -> 等待 -> kill"""
    try:
        # 给进程组发信号（start_new_session=True 时，proc.pid 是该进程组 ID）
        os.killpg(proc.pid, signal.SIGKILL)
    except Exception:
        # 退化为 terminate（POSIX=SIGTERM）
        proc.terminate()
    try:
        proc.wait(timeout=grace)
        return
    except subprocess.TimeoutExpired:
        try:
            os.killpg(proc.pid, signal.SIGTERM)
            proc.wait(timeout=10)
            return
        except Exception:
            proc.kill()
            proc.wait(timeout=5)

@contextmanager
def sglang_server(
    model_path: str,
    host: str = "127.0.0.1",
    port: int = 8001,
    **kwargs
):
    """
    启动 SGLang server，并在 with 结束时自动关闭。
    返回 base_url，例如 http://127.0.0.1:30000
    """

    try:
        # 循环查找可用端口
        while True:
            port_check = subprocess.run(["lsof", "-i", f":{port}", "-t"], capture_output=True, text=True)
            if not port_check.stdout:
                # 找到可用端口
                break
            print(f"端口 {port} 已被占用,尝试下一个端口")
            port += 1
            if port > 65535:  # 端口号最大值
                raise RuntimeError("无法找到可用端口")
    except Exception as e:
        print(f"检查端口占用时出错: {e}")

    # 推荐避免 shell，直接跑 Python 模块
    cmd = [sys.executable, "-m", "sglang.launch_server",
           "--model-path", model_path,
           "--host", host, 
           "--port", str(port),
           "--dp-size", "2",
           "--tp-size", "4",
           "--mem-fraction-static", "0.85",
           "--context-length", "32768",
           "--show-time-cost",
           "--enable-metrics"]           

    log_file = kwargs.get("log_file", None)
    stdout = open(log_file, "wb") if log_file else subprocess.PIPE
    # start_new_session=True: 建新进程组，方便优雅关停
    proc = subprocess.Popen(
        cmd,
        stdout=stdout,
        stderr=subprocess.STDOUT,
        start_new_session=True,
        text=False,
        bufsize=1,
    )

    base_url = f"http://{host}:{port}"
    try:
        _wait_for_ready(base_url, api_key=None, timeout=600)  # 大模型首次加载可能较慢
        yield base_url
    except Exception as e:
        _graceful_shutdown(proc)
        if log_file:
            stdout.close()
        yield None
    finally:
        _graceful_shutdown(proc)
        if log_file:
            stdout.close()

# =============== 示例用法 ===============
if __name__ == "__main__":

    models = [
        # '/data/home/<USER>/repos/anjesbench/share/models/gemma-3-1b-it',
        # '/data/home/<USER>/repos/anesbench/share/models/gemma-3-4b-it',
        # '/data/home/<USER>/repos/anesbench/share/models/gemma-3-12b-it',
        # '/data/home/<USER>/repos/anesbench/share/models/gemma-3-27b-it',
        # '/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-0.5B-Instruct',
        # '/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-1.5B-Instruct',
        # '/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-3B-Instruct',
        # '/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-7B-Instruct',
        # '/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-14B-Instruct',
        # '/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-32B-Instruct',
        # '/data/home/<USER>/repos/anesbench/share/models/Qwen2.5-72B-Instruct',
        # '/data/home/<USER>/repos/anesbench/share/models/Qwen3-0.6B',
        # '/data/home/<USER>/repos/anesbench/share/models/Qwen3-1.7B',
        # '/data/home/<USER>/repos/anesbench/share/models/Qwen3-4B',
        # '/data/home/<USER>/repos/anesbench/share/models/Qwen3-8B',
        # '/data/home/<USER>/repos/anesbench/share/models/Qwen3-14B',
        # '/data/home/<USER>/repos/anesbench/share/models/Qwen3-30B-A3B',
        # '/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Llama-8B',
        # '/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Llama-70B',
        # '/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Qwen-1.5B',
        # '/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Qwen-7B',
        # '/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Qwen-14B',
        '/data/home/<USER>/repos/anesbench/share/models/DeepSeek-R1-Distill-Qwen-32B',
    ]

    with open('/data/home/<USER>/repos/anesbench/share/datasets/anesthesia/benchmark/fine/rebuttal/openqa/system2_en_refined_w_prompt.json', 'r') as f:
        data = json.load(f)

    for index, item in enumerate(data):
        data[index]['index'] = index

    for model in models:
        with sglang_server(model, log_file=f"./logs/server_{model.split('/')[-1]}.log") as endpoint:
            if endpoint is None:
                print(f"Failed to start server for {model}")
                continue
            model_res = []
            import concurrent.futures

            def fetch_response(item):
                payload = {
                    "model": "default",
                    "messages": [
                        {"role": "user", "content": item['prompt']},
                    ],
                    "max_tokens": 8192,
                    "temperature": 0,
                }
                r = requests.post(f"{endpoint}/v1/chat/completions", json=payload, timeout=120)
                content = r.json()["choices"][0]['message']['content']
                return {"response": content, "id": item['id'], "target": item['target'], "index": item['index'], "prompt": item['prompt']}

            with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
                futures = [executor.submit(fetch_response, item) for item in data]
                for future in concurrent.futures.as_completed(futures):
                    model_res.append(future.result())
            model_res = sorted(model_res, key=lambda x: x['index'])
            with open(f"./data/{model.split('/')[-1]}.json", 'w') as f:
                json.dump(model_res, f, ensure_ascii=False, indent=2)
            print(f"Finished {model}")

